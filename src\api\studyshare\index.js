import service from '@/utils/request.js'

// 新增案例
export function addstudyshare(data) {
    return service.request({
      method: 'post',
      url: '/crm/controller/case/save',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    });
  }
  // 编辑案例
export function updatestudyshare(data) {
    return service.request({
      method: 'post',
      url: '/crm/controller/case/update',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    });
  }


  export function updateStatus(data) {
    return service.request({
      method: 'post',
      url: '/crm/controller/case/updateStatus',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    });
  }
// 案例列表
export function studyshareList(params) {
    return service.request({
        method: 'get',
        url: '/crm/controller/case/list',
        params,
    });
}

// 案例详情
export function studyshareInfo(id) {
    return service.request({
        method: 'get',
        url: `/crm/controller/case/info/${id}`,
    });
}

//
// 删除案例
export function deletestudyshare(data) {
    return service.request({
        method: 'post',
        url: '/crm/controller/case/delete',
        data,
    });
}

// 点赞/取消

export function updateLike(params) {
    return service.request({
        method: 'get',
        url: '/crm/controller/caseliked/updateLike',
        params,
    });
}

// 学习记录
export function queryCaseLearningRanking(params) {
    return service.request({
        method: 'get',
        url: '/crm/controller/caselearningrecord/queryCaseLearningRanking',
        params,
    });
}
//保存案例学习记录
export function savecaselearningrecord(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/caselearningrecord/save',
    data,
});
}

// 评论列表
export function queryCaseCommentList(params) {
  return service.request({
      method: 'get',
      url: '/crm/controller/casecomment/queryList',
      params,
  });
}
// 保存评论回复
export function casecommentSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/casecomment/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
});
}

// 删除评论回复
export function deletecasecomment(data) {
  return service.request({
      method: 'post',
      url: '/crm/controller/casecomment/delete',
      data,
  });
}



export function caseAdminList(params) {
    return service.request({
        method: 'get',
        url: '/crm/controller/case/adminList',
        params,
    });
}

//
export function queryMyCaseInfo(data) {
  return service.request({
      method: 'post',
      url: '/crm/controller/case/queryMyCaseInfo',
      data,
  });
}



// 已读未读列表

export function queryViewCount(data) {
	return service.request({
		method: 'post',
		url: `/crm/controller/caseview/queryViewCount`,
	  data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
	});
}

//
export function queryLikeList(data) {
	return service.request({
		method: 'post',
		url: `/crm/controller/caseliked/queryLikeList`,
	  data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
	});
}

