<template>
  <div class="flex">
    <div>
        <back>新建任务</back>
    </div>
    <div class="mainbg">
        <el-form :rules="rules" :model="form" ref="addform" class="addfcss"  label-width="130px">
            <textBorder>基础信息</textBorder>
            <div class="pt20  bbline">
                <el-row :gutter="0" type="flex" justify="start" >
                    <el-col :span="8">
                        <el-form-item  label="任务名称：" prop="taskName">
                            <el-input maxlength="50" show-word-limit clearable="" v-model="form.taskName" class="definput" placeholder="请输入任务名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item  label="预估工时(人/天)：">
                            <el-input type="number" clearable="" v-model="form.workHours" class="definput" placeholder="请输入工时"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item  label="开始时间：">
                            <el-date-picker
                            format="yyyy-MM-dd HH:mm" 
                            value-format="yyyy-MM-dd HH:mm"
                            class="eldate"
                            v-model="form.beginTime"
                            type="datetime"
                            placeholder="选择时间">
                          </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="0" type="flex" justify="start" >
                    <el-col :span="8">
                        <el-form-item  label="结束时间：">
                            <el-date-picker
                            format="yyyy-MM-dd HH:mm" 
                            value-format="yyyy-MM-dd HH:mm"
                            class="eldate"
                            v-model="form.endTime"
                            type="datetime"
                            placeholder="选择时间">
                          </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="form.taskStatus == 2 || form.taskStatus == 5">
                        <el-form-item  label="任务状态：">
                            <el-select class="definput" v-model="taskStatusClone" placeholder="请选择状态">
                                <el-option v-for="item in statusTaskArr" :key="item.value" :label="item.name"
                                                :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="0" type="flex" justify="start" >
                  <el-col :span="16">
                        <el-form-item label="任务内容：" prop="taskContent">
                            <el-input v-model="form.taskContent" class="definput" maxlength="300" show-word-limit type="textarea" rows="4" placeholder="请输入任务内容"  ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
            <textBorder class="mt30">负责与协作</textBorder>
            <div class="pt20  bbline">
              <el-row :gutter="0" type="flex" justify="start" >
                    <el-col :span="8">
                        <el-form-item label="负责人：" prop="chargePerson">
                            <span  v-if="form.chargePerson" class="mr10">{{form.chargePersonName}}</span>
                             <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择负责人',1)">点击选择负责人</el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="协作人：" prop="collaborator">
                            <el-tag class="tagcss" size="small" v-for="item in xiezuolist" closable @close="handleTagClose(item)" :key="item.id">{{item.name}}</el-tag>
                            <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择协作人',5)">点击选择协作人</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
                
            </div>
            <textBorder class="mt30">补充信息</textBorder>
            <div class="pt20  bbline">
                        <el-form-item label="附件：">
                            <upload2 ref="upload2" @submitImg="submitPdf" accept=".pdf" :fileList="fileListPdf">
                            <span class="studiocss">
                              <img src="../../../assets/img/file_icon.png">
                              <span class="uploadtext  deffont">点击上传附件</span>
                            </span>
                             <template slot="ptip">
                                <p>只能上传pdf文件</p>
                              </template>
                          </upload2>
                        </el-form-item>
                        <el-row :gutter="0" type="flex" justify="start" >
                            <el-col :span="16">
                                <el-form-item label="备注：">
                                    <el-input v-model="form.notes" class="definput" maxlength="300" show-word-limit type="textarea" rows="4" placeholder="请输入备注"  ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
            </div>
            <div class="pt20 btncenter">
                <el-form-item >
                    <el-button @click="save" class="w98 btn_h42" type="primary" v-dbClick>保存</el-button>
                </el-form-item>
            </div>
        </el-form>
    </div>
    <!-- 负责人/协作人 -->
    <systemDialog 
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData">
    </systemDialog>


    
  </div>
</template>

<script>
import back from '../../common/back.vue';
import textBorder from '../../common/textBorder.vue';
import systemDialog from '../../common/systemDialog.vue';
import upload2 from '@/components/common/upload2.vue'
import {taskSave,taskInfo,taskUpdate} from '@/api/stage/index'
import {getFileTypeNum} from '@/utils/tools'
export default {
    components:{
        back,
        textBorder,
        systemDialog,
        upload2,
    },
    data(){
        return{
            dialogVisible:false,
            isSmall:window.screen.availWidth<1500?true:false,
            dialogName:'',
            form:{
                chargePerson:'',
                chargePersonName:'',
                collaborator:'',
                collaboratorName:'',
                fileInfoList:[],
                workHours:'',
                taskName:'',
                beginTime:'',
                endTime:'',
                taskContent:'',
                notes:'',
            },
            multipleNum:1,
            xiezuolist:[],
            fileListPdf:[],
            statusTaskArr:[],
            taskArr1:[
                {
                    name:'进行中',
                    value:'2'
                },
                {
                    name:'结束',
                    value:'4'
                },
            ],
            taskArr2:[
                {
                    name:'暂停',
                    value:'5'
                },
                {
                    name:'结束',
                    value:'4'
                },
            ],
            taskStatusClone:"",
            rules: {
                taskName: [
                    { required: true, message: '请输入任务名称', trigger: 'blur' },
                ],
                taskContent: [
                    { required: true, message: '请填写任务内容', trigger: 'blur' }
                ],
                chargePerson:[
                    { required: true, message: '请选择负责人', trigger: 'change' }
                ]
            },
        }
    },
    created(){
        if(this.$route.query.taskId){
            this.taskInfoApi()
        }
    },
    methods:{
        taskInfoApi(){
            taskInfo(this.$route.query.taskId).then(res=>{
                    if(res.status == 0){
                        this.form = res.data
                        this.form.workHours = res.data.workHours == 0?'':res.data.workHours
                            // 负责人/协作人
                        var ids = res.data.collaborator && res.data.collaborator.split(',');
                        let names = res.data.collaboratorName && res.data.collaboratorName.split(',');
                        ids && ids.forEach((item,index) => {
                            var data = {};
                            data.id = item;
                            data.name = names[index];
                            this.xiezuolist.push(data)
                        });
                        res.data.fileInfoList.forEach(item=>{
                            item.name = item.fileName
                        })
                        this.fileListPdf = res.data.fileInfoList
                        this.$refs.upload2.setFileList(this.fileListPdf)
                        if(res.data.taskStatus ==5){
                                this.statusTaskArr = this.taskArr1
                        }
                        if(res.data.taskStatus ==2){
                                this.statusTaskArr = this.taskArr2
                        }
                    }
            })
        },
        save(){
            this.$refs['addform'].validate((valid)=>{
                    if(valid){
                        if(this.form.chargePerson == ''){
                            this.msgError('请选择负责人')
                            return
                            }
                            this.form.fileInfoList = []
                            if(this.fileListPdf.length>0){
                                this.arrForEach(this.fileListPdf)
                            }
                            this.form.stageId = this.$route.query.id
                            if(this.taskStatusClone){
                                this.form.taskStatus = this.taskStatusClone
                            }
                            if(this.$route.query.taskId){
                                taskUpdate(this.form).then(res=>{
                                    if(res.status == 0){
                                            this.msgSuccess('修改成功')
                                            this.$router.back();
                                    }else{
                                        this.msgError(res.msg)
                                    }
                                })
                            }else{
                                taskSave(this.form).then(res=>{
                                    if(res.status == 0){
                                            this.msgSuccess('添加成功')
                                            this.$router.back();
                                    }else{
                                        this.msgError(res.msg)
                                    }
                                })
                            }
                    }
            })
        },
        arrForEach(arrFileList){
            arrFileList.forEach(item=>{
                        this.form.fileInfoList.push({
                            ...item,
                            fileType:getFileTypeNum(item.url)
                        })
            })
        },
        submitPdf(fileList){
            this.fileListPdf = fileList
        },
        handleTagClose(tag) {
            this.xiezuolist.splice(this.xiezuolist.indexOf(tag), 1);
            this.xiezuoData(this.xiezuolist)
        },
        xiezuoData(list){
            var ids = [];
            var names = [];
            list.forEach(item => {
                ids.push(item.id);
                names.push(item.name);
            });
            this.form.collaborator = ids.join(',');
            this.form.collaboratorName = names.join(',');
        },
        submitData(data,type,departmentId){
            if (type == '选择负责人') {
                this.form.chargePerson = data.length>0 ? data[0].id :'';
                this.form.chargePersonName = data.length>0 ? data[0].name :'';
                this.form.taskDepartmentId = departmentId;
                this.$refs.addform.validateField(['chargePerson'])

            }else if(type == '选择协作人'){
                this.xiezuoData(data)
                this.xiezuolist = data;
            }
            this.updateSystemVisible(false)
        },
          // 选择协作人
       clickXuan(name,multipleNum){
            this.dialogName = name;
            this.multipleNum = multipleNum;
            this.$refs.systemdialog.loadData();
            if (name == '选择负责人') {
                this.form.chargePerson ? this.$refs.systemdialog.updateWorksId([{id:this.form.chargePerson,name:this.form.chargePersonName}]):this.$refs.systemdialog.updateWorksId([]);
            }else if(name == '选择协作人'){
                this.form.collaborator ? this.$refs.systemdialog.updateWorksId(this.xiezuolist):this.$refs.systemdialog.updateWorksId([]);
            }
            this.dialogVisible = true;
        },
        updateSystemVisible(value){
            this.dialogVisible = value;
        },
    }
}
</script>
<style scoped lang="scss">
.tagcss /deep/ .el-icon-close {
    width: 12px;
    height: 12px;
    line-height: 12px;
    background-color: #4285F4;
    color: white;
}
.tagcss {
    margin-left: 8px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    background: #DFEAFD;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
}
.studiocss img{
    width: 16px;
    height: 16px;
    margin-right: 3px;
    margin-top: -3px;
}
.eldate{
    width: 100% !important;
    padding: 0;
       /deep/.el-input__inner{
            height: 34px;
            line-height:34px;
    }
    /deep/.el-input__icon{
        line-height:34px;
    }
}
.els50{
    width: 49%;
    margin-right: 1%;
}
.els50:last-child{
    margin-right: 0;
    margin-left: 1%;
    float: right;
}
.elflex{
    display: flex;
}
.w98{
    width: 98px;
}
.flex{
    display: flex;
    flex-direction: column;
}
.rcenter{
    position: absolute;
    right: 10px;
    line-height: 34px;
    font-size: 14px;
    color: #c0c4cc;
}

.btncenter{
    text-align: center;
}
.quanxiancss /deep/.el-form-item__label {
    margin-left: -7px;
    width: 125px !important;
}
.addresscss{
    max-height: 68px;
}

.input-new-tag{
    width: 200px;
    margin-left: -8px;
    
}
.input-new-tag /deep/.el-input__inner{
    border: none;
    background-color: rgba(0, 0, 0, 0);
}

.tagcol /deep/.el-form-item__label{
    height: 34px;
    line-height: 34px;
}

.tagcol /deep/.el-form-item__content{
    background: #F5F5F5;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    min-height: 34px;
    line-height: 34px;
}

.mainbg{
    margin: 16px 0px;
    padding: 20px 16px;
    background-color: white;
    border-radius: 8px;
}
.pd0{
    padding-right: 0px !important;
}
</style>
<style>

</style>