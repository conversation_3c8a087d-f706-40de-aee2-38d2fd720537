import service from '@/utils/request.js'



export function bookList(params) {
  return service.request({
    method: 'get',
    url: `/crm/business/teachingmaterial/list`,
    params
  });
}

export function giftList(params) {
  return service.request({
    method: 'get',
    url: `/crm/business/giftinfo/list`,
    params
  });
}


export function queryPress(params) {
  return service.request({
    method: 'get',
    url: `/crm/business/teachingmaterial/queryPress`,
    params
  });
}



export function addBook(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/teachingmaterial/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function updateBook(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/teachingmaterial/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function queryBookInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/business/teachingmaterial/info/` + id,
  });
}
// 删除教材
export function deleteBook(data) {
  // 
  return service.request({
    method: 'post',
    url: '/crm/business/teachingmaterial/delete',
    data,
  });
}

// 下载模版
export function pressImportTemplate(params) {
  return service.request({
    method: 'get',
    url: `/crm/business/teachingmaterial/pressImportTemplate`,
    params
  });
}

// crm/business/teachingmaterial/batchImportExcel

// /crm/business/giftinfo/save

export function addGift(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/giftinfo/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
export function updateGift(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/giftinfo/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
export function deleteGift(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/giftinfo/delete',
    data,
  });
}
export function giftinfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/business/giftinfo/info/${id}`,
  });
}

