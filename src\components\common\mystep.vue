<template>
  <el-steps class="stepscss" :active="setupData.activeIndex">
    <el-step class="mystep">
      <span slot="icon">
          <div class="typename">
              提交{{stypes == 'audit' ? "计划" :'总结'}}
          </div>
          <div class="pr">
              <headuser  :url="setupData.logo" width="40" borderRadius="10px" :username="setupData.name">
              </headuser>
              <i  class="fl el-icon-success scolor"></i>
          </div>
      </span>
      <span slot="title" class="unamecss">
      {{setupData.name}}<span class="stacss">(已提交)</span>
      <div class="ctimecss">
        {{setupData.auditTime}}
      </div>
      </span>
    </el-step>
    <el-step class="mystep" v-for=" item in setupData.examineVoList" :key="item.div">
      <span slot="icon">
          <div class="typename bbtn">
            {{stypes[stype]}}
          </div>
          <div class="pr">
              <headuser  :url="item.logo" width="40" borderRadius="10px" :username="item.name">
              </headuser>
              <i v-if="item.logsStatus == 2"  class="fl el-icon-success scolor"></i>
              <i v-if="item.logsStatus == 3"  class="fl el-icon-error ecolor"></i>
          </div>
      </span>
      <span slot="title" class="unamecss">
      {{item.name}}<span class="stacss">({{stype == 'look' ? staNames2[item.logsStatus]: staNames[item.logsStatus]}})</span>
      <div class="ctimecss">
        {{item.auditTime}}
      </div>
      <div class="ctimecss defont" v-if="item.url && item.logsStatus == 2 ">
        签名：<img class="esignimg" :src="item.url" alt="" srcset="">
      </div>
      </span>
    </el-step>
  </el-steps>
  
        
</template>

<script>
import headuser from '@/components/common/headuser.vue';
export default {
  components:{
    headuser
  },
    props:{
        setupData:{
            type:Object,
            default:()=>Object()
        },
        stype:{
          type:String,
          default:'audit'
        },
    },
    data(){
      return{
        stypes:{
          "audit":'审核人',
          "look":"查阅人"
        },
        staNames2:{
          1:"待查阅",
          2:'已查阅',
        },
        staNames:{
          1:"待审核",
          2:"通过",
          3:'驳回'
        },
      }
    },
}
</script>

<style lang="scss" scoped> 
.defont{
  color: #333333 !important;
}
.esignimg{
  width: 60px;
  height: 40px;
}
.ctimecss{
  font-family: Microsoft YaHei, Microsoft YaHei !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #999999 ;
  line-height: 16px !important;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.stacss{
  
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  padding-left: 10px;
}
.stepscss{
  width: 100%;
  margin: 20px 0 !important;
}
.mystep /deep/.el-step__icon.is-text{
  border: none;
  border-radius: 0;
}
.mystep /deep/.el-step__line{
  top: 60px !important;
  left: 0px !important;
  right: 0px !important;
}
.mystep /deep/.el-step__icon{
  background-color: white;
  width: 50px !important;
  max-width: 50px !important;
  height: 100%;
}
.typename{
  font-family: Microsoft YaHei, Microsoft YaHei !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #333333 !important;
  line-height: 40px !important;
  text-align: center !important;
  font-style: normal;
  text-transform: none;
}

  .scolor{
    color: #56C36E ;
  }
  .ecolor{
    color: #F45961;
  }
  .pr{
    position: relative;
    width: 40px;
    height: 40px;
  }
  .fl{
    position: absolute;
    bottom: -5px;
    right: -5px;
  }
  .unamecss{
    white-space: nowrap !important; 
    font-family: Microsoft YaHei, Microsoft YaHei !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    color: #333333 !important;
  }
</style>