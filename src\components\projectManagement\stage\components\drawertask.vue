<template>
  <div class="drawer">
    <el-drawer class="ed" center size="50%" title="任务详情" :visible.sync="drawer_" :direction="direction" @close="handleClose">
      <el-tabs  class="edt" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="任务信息" name="first">
          <taskInfo v-on="$listeners" :form="form"></taskInfo>
        </el-tab-pane>
        <el-tab-pane label="成本记录" name="second">
          <costrecord ref="costRef" :taskId="taskId">
            <template slot-scope="{dataobj}">
            <p class="cbtext clearfix">本流程累计成本(元)：<span class="yuan">{{ dataobj.yuan }}元</span>  <el-button :disabled="!form.isOperate" @click="addcb" class="defaultbtn mt right" icon="el-icon-plus" type="primary" >添加</el-button></p>
            </template>
          </costrecord>

        </el-tab-pane>
        <el-tab-pane label="工时与成果" name="third">
            <hourresult ref="houtRef" :taskId="taskId">
              <template slot-scope="scope">
              <p class="cbtext clearfix">
                本流程累计工时：<span class="yuan">{{scope.row.accumulatedWorkHours}}人/天</span>
                <span class="ml32"> 本流程累计成果(分钟)：</span>
                <span class="yuan">{{scope.row.accumulatedDuration}}</span>
              <el-button :disabled="!form.isOperate" class="defaultbtn mt right" icon="el-icon-plus" type="primary" @click="addHourRec">添加</el-button>
              </p>
            </template>
            </hourresult>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
    <cbadd 
        @btnOk="btnOk"
        :formDataList="fType ==1 ? formDataList : formDataListHour"
        :formParams="fType ==1 ? formParams : formParamsHour"  
        :formConfig="formConfig" 
        :title="title" 
        :visible="visible" 
        @updateVisible="updateVisible"></cbadd>
  </div>
</template>

<script>
import costrecord from '../costrecord'
import hourresult from '../hourresult'
import taskInfo from './taskInfo.vue'

import cbadd from './cbadd.vue'

import {taskInfoApi,taskHourSave,taskCostSave} from '@/api/stage/index'

export default {
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    direction: {
      type: String,
      default: 'rtl',
    },
  },
  components:{
    taskInfo,
    costrecord,
    hourresult,
    cbadd
  },
  data() {
    return {
      formDataList: [
        {
          label: "金额(元)",
          prop: "costAmount",
          type: "input",
          placeholder:'请输入本次成本的金额(元)',
          rules: [
            { required: true, message: "金额不能为空", trigger: "blur" },
          ],
        },
        {
          label: "原因",
          prop: "reason",
          type: "textarea",
          maxlength:'50',
          rules: [
            { required: true, message: "原因不能为空", trigger: "blur" },
          ],
        },
      ],
      activeName: 'first',
      visible:false,
      title:'添加成本记录',
      formConfig: {
        labelWidth: "120px",
        inline: false,
        ref: "formRef",
      },
      formParams: {
        costAmount: "",
        reason: "",
      },
      formParamsHour: {
        workHours: "",
        duration: "",
        id: "",
      },
      formDataListHour: [
        {
          label: "工时(人/天)",
          prop: "workHours",
          type: "input",
          placeholder:'请输入本次工',
          rules: [
            { required: true, message: "工时不能为空", trigger: "blur" },
          ],
        },
        {
          label: "成品时长(分钟)",
          prop: "duration",
          type: "input",
          placeholder:'请输入成品时长',
        },
      ],
      fType:1,
      taskId:'',
      form:{},
    }
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
        //   console.log(v, 'v')
        //   this.$emit('changeDrawer', v)
      },
    },
  },
  methods: {
    btnOk(){
        console.log(this.formParams)
        if(this.fType == 1){
          this.formParams.taskId = this.taskId
          taskCostSave(this.formParams).then(res=>{
              if(res.status == 0){
                this.formParams.costAmount = ''
                this.formParams.reason = ''
                this.visible = false
                this.$refs.costRef.costListData(2,this.taskId)
              }
          })
        }else{
          this.formParamsHour.taskId = this.taskId
          taskHourSave(this.formParamsHour).then(res=>{
              if(res.status == 0){
                this.formParamsHour.workHours = ''
                this.formParamsHour.duration = ''
                this.visible = false
                this.$refs.houtRef.hourListData(2,this.taskId)
              }
          })
        }
    },
    addHourRec(){
        this.title = '添加工时记录'
        this.visible = true
        this.fType =2
    },
    addcb(){
         this.title = '添加成本记录'
         this.visible = true
         this.fType = 1
    },
    updateVisible(visible){
      console.log(visible)
      this.visible = visible
    },
    handleClick(tab, event) {
        console.log(tab, event);
      },
    handleClose() {
      this.$emit('changeDrawer', false)
    },
    getTaskInfo(taskId){
        this.taskId = taskId
        this.$refs.costRef.costListData(2,this.taskId)
        this.$refs.houtRef.hourListData(2,this.taskId)
        taskInfoApi(this.taskId).then(res=>{
            if(res.status == 0){
                this.form  = res.data
            }
        })
    },
  },
}
</script>

<style lang="scss" scoped>
  .drawer{
    /deep/.el-drawer{
      padding: 20px;
      padding-top: 0;
    }
    /deep/ .el-drawer__header{
      padding-left: 0;
      text-align: center;
    }
  }
</style>>
