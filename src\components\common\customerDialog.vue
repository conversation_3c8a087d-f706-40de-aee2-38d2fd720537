<template>
    <el-dialog :before-close="handleClose" class="unitdialog" title="选择客户" top="5%" :visible.sync="dialogTableVisible" width="70%" center>
                <div>
                    <el-form class="unitfcss" :inline="true">
                        <el-form-item label="">
                            <el-input clearable="" class="definput" v-model="pageBean.customerName" placeholder="请输入客户名称"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-input clearable="" class="definput" v-model="pageBean.unitName" placeholder="请输入客户单位"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-button @click="search" class="defaultbtn ml20" icon="el-icon-search" type="primary">搜索</el-button>
                        </el-form-item>
                    </el-form>
                    <el-table class="mytable" :data="dataList" height="478px" v-loading="isLoading">
                        <el-table-column class-name="column_blue" property="customerName" label="客户名称" align="center" min-width="200px"></el-table-column>
                        <el-table-column property="unitName" label="客户单位" align="center" width="180px" show-overflow-tooltip></el-table-column>
                        <el-table-column property="unitDepartment" label="部门" align="center" width="150px"></el-table-column>
                        <el-table-column property="duties" label="职务" align="center"  width="150px"></el-table-column>
                        <el-table-column property="customerLevelName" label="客户级别" align="center"  width="150px">
                            <template slot-scope="scope">
                                <span :class="customerLevelColors[scope.row.customerLevelName]">{{ scope.row.customerLevelName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column property="phone" label="联系方式" align="center"  width="150px"></el-table-column>
                        <el-table-column  label="负责人" align="center" width="160px">
                            <template slot-scope="scope">
                                <div v-if="scope.row.chargePersonNames.length>0">
                                    <el-tag class="cuscss" v-for="(item,index) in scope.row.chargePersonNames" :key="index">{{item}}</el-tag>
                                </div>
                                <div v-else>
                                    —
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column property="edit" label="操作" align="center" fixed="right">
                            <template slot-scope="scope" >
                                <div class="rbtn deffont" v-show="scope.row.isSelect">取消选择</div>
                                <div class="bbtn deffont" v-show="scope.row.isSelect == 0" @click="chooseAction(scope.row)">选择</div>
                            </template>
                        </el-table-column>
                        <template slot="empty">
                            <nolist></nolist>
                        </template>
                    </el-table>
                    <div class="center">
                        <el-button class="defaultbtn" type="primary" @click="submitAction">提交</el-button>
                    </div> 
                    <page 
                    :currentPage="pageBean.pageNum" 
                    :pageSize="pageBean.pageSize" 
                    :total="total"
                    @updatePageNum="handleCurrentChange"
                    ></page>
                </div>
    </el-dialog>
</template>

<script>

import page  from './page.vue';

import {selectCustomer} from '@/api/wapi'
import nolist from './nolist.vue';
import { customerLevelColors } from "@/utils/dict";
export default {
    components:{
        page,
        nolist
    },
   
    data(){
        return{
            customerLevelColors:customerLevelColors,
            activeName:"first",
            isLoading:false,
            dataList:[],
            pageBean:{
                customerName:"",
                unitName:'',
                pageNum:1,
                pageSize:8,
                methodName:'list'
            },
            total:0,
            rdata:{},
            customerDataS:{},
            tempData:{}
        }
    },
    props:{
        visible:{
            type:Boolean,
            default:false
        },
    },
    computed:{
        dialogTableVisible:{
            get(){
                return this.visible;
            },
            set(val){
                this.$emit('updateVisible',val);
            },
        }
    },
    methods:{
        handleClose(){
            this.pageBean.pageNum = 1
            this.pageBean.unitName = ''
            this.pageBean.customerName = ''
            this.dialogTableVisible = false;
        },
        search(){
            this.selectCustomerData()
        },
        selectCustomerData(params){
            if(params){
              this.tempData = params
            }else{
                params = this.tempData
            }
            let sData = {...params,...this.pageBean}
            this.isLoading = true;
            selectCustomer(sData).then(res=>{
                this.isLoading = false;
                if(res.status == 0){
                    this.dataList = res.data
                    this.total = res.page.total
                    this.dataList.forEach(item=>{
                        this.$set(item,'isSelect',false)
                    })
                    if(this.customerDataS.id){
                        this.selected(this.dataList)
                    }
                    
                }else{
                    this.msgError(res.msg)
                }
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        selected(dataList){
            dataList.forEach(item=>{
                if(item.id === this.customerDataS.id){
                    this.$set(item,'isSelect',true)
                }
            })
        },
        submitAction(){
            if(!this.customerDataS.id){
                this.msgError('请先选择客户')
                return 
            }
            this.$emit('updateCustomer',this.customerDataS)
            this.dialogTableVisible = false;
        },
        handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.selectCustomerData()
        },
        chooseAction(row){
            this.dataList.forEach(item=>{
                item.isSelect = false
            })
            row.isSelect = true
            this.customerDataS = row
        }
    }
}
</script>
<style scoped>
.center{
    margin-top: 30px;
    text-align: center;
}
.bbtn{
    color: #4285F4;
    cursor: pointer;
}
.rbtn{
    color: #F45961;
    cursor: pointer;
}

.unitfcss{
    line-height: 34px;
}
.unitfcss /deep/.el-form-item__label{
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
}
.unitfcss /deep/.el-form-item{
    margin-bottom: 20px;
    /* padding-right: 60px; */
}
.ml20{
    margin-left: 20px;
}
.unitdialog /deep/.el-dialog__body{
    padding: 20px;
    padding-top: 0px;
}
.unitdialog /deep/.el-dialog__header{
    border: none;
}
</style>
<style scoped>
.tabscss /deep/.el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 40px;
  width: 240px !important;
  text-align: center;
  line-height: 40px;
  /* padding: 0 60px; */
}

</style>