<template>
  <el-drawer
    title="部门详情"
    :visible.sync="visible"
    direction="rtl"
    size="50%"
    class="departmentDrawer"
    :destroy-on-close="true"
    :modal-append-to-body="false"
  >
    <div class="departmentInfo">
        <el-table :data="departmentData" border stripe>
            <el-table-column prop="departmentName" label="部门名称"></el-table-column>
            <el-table-column prop="createName" label="创建人"></el-table-column>
            <el-table-column prop="createTime" label="创建时间"></el-table-column>
            <el-table-column prop="modifyTime" label="更新时间"></el-table-column>
            <el-table-column label="操作" width="150" fixed="right" align="center">
                <template slot-scope="scope">
                    <el-button type="text" size="mini" @click="showMigrateDialog">迁移</el-button>
                    <el-button type="text" size="mini" @click="showEditDialog">编辑</el-button>
                    <el-button type="text" size="mini" @click="handleDelete">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="部门资料" name="1">
                <departmentInfo :department-detail="departmentDetail" />
            </el-tab-pane>
            <el-tab-pane label="操作记录" name="2">
                <operatingRecord :department-id="departmentId" />
            </el-tab-pane>
        </el-tabs>
    </div>

    <el-dialog title="编辑部门" :visible.sync="editDialogVisible" width="30%" :append-to-body="true">
      <el-form :model="editForm" ref="editForm" :rules="rules" label-width="80px">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入部门名称"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sortNo">
          <el-input-number v-model="editForm.sortNo" :min="1" :max="999"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitEdit">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="部门迁移"
      :visible.sync="migrateDialogVisible"
      width="500px"
      center
      :append-to-body="true">
      <el-form :model="migrateForm" ref="migrateForm" :rules="migrateRules" label-width="80px">
        <el-form-item label="选择部门" prop="targetDepartmentId">
          <el-select 
            v-model="migrateForm.departmentNames" 
            placeholder="请选择" 
            @focus="chooseMigrateTarget" 
            clearable
            readonly>
            <el-option 
              v-if="migrateForm.departmentNames" 
              :value="migrateForm.departmentNames" 
              :label="migrateForm.departmentNames">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitMigrate">确 定</el-button>
      </div>
    </el-dialog>

    <MigrateDepartmentDialog 
      ref="migrateDepRef" 
      :visible.sync="migrateDepartmentDialogVisible" 
      :current-department-id="departmentId"
      :current-parent-id="departmentDetail.parentId"
      @updateVisible="updateMigrateDialogVisible"
      @submit="handleMigrateTargetSelected"
      @cancel="handleMigrateTargetCancel">
    </MigrateDepartmentDialog>

  </el-drawer>
</template>
<script>
import departmentInfo from './departmentInfo.vue'
import operatingRecord from './operatingRecord.vue'
import { findDepartmentInfo, updateDepartment, migrateDepartment, deleteDepartment } from '@/api/framework/user'
import MigrateDepartmentDialog from '@/components/common/MigrateDepartmentDialog.vue'


export default {
    components: {
        departmentInfo,
        operatingRecord,
        MigrateDepartmentDialog
    },
    data() {
        return {
            activeName: '1',
            departmentData: [],
            departmentDetail: {},
            editDialogVisible: false,
            migrateDialogVisible: false,
            migrateDepartmentDialogVisible: false,
            selDepartmentData: [],
            editForm: {
                id: '',
                name: '',
                sortNo: 1
            },
            migrateForm: {
                departmentId: '',
                targetDepartmentId: '',
                departmentNames: ''
            },
            rules: {
                name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
            },
            migrateRules: {
                targetDepartmentId: [{ required: true, message: '请选择目标部门', trigger: 'blur' }],
            }
        };
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        departmentId: {
            type: [String, Number],
            required: true
        }
    },
    computed: {
        visible: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        }
    },
    watch: {
        visible(val) {
            if (val && this.departmentId) {
                this.getDepartmentInfo();
            }
        }
    },
    methods: {
        handleClick() {
            console.log('==========');
        },
        getDepartmentInfo() {
            findDepartmentInfo({ id: this.departmentId })
                .then(res => {
                    const detail = res.data;
                    this.departmentData = [
                        {
                            departmentName: detail.name,
                            createName: detail.createName,
                            createTime: detail.createTime,
                            modifyTime: detail.modifyTime
                        }
                    ];
                    this.departmentDetail = detail;
                })
        },
        showEditDialog() {
            this.editForm.id = this.departmentDetail.id;
            this.editForm.name = this.departmentDetail.name;
            this.editForm.sortNo = this.departmentDetail.sortNo ;
            this.editDialogVisible = true;
        },
        submitEdit() {
            updateDepartment(this.editForm).then(res => {
                if (res.status == 0) {
                    this.$message.success('更新部门成功');
                    this.editDialogVisible = false;
                    this.getDepartmentInfo();
                    this.$emit('edit', this.editForm);
                } else {
                    this.$message.error(res.msg );
                }
            })
        },
        showMigrateDialog() {
            this.migrateForm.departmentId = this.departmentDetail.id;
            this.migrateForm.targetDepartmentId = '';
            this.migrateForm.departmentNames = '';
            this.selDepartmentData = [];
            this.migrateDialogVisible = true;
        },
        chooseMigrateTarget() {
            if (!this.departmentDetail || !this.departmentDetail.id) {
                this.$message.error('部门信息加载失败，请刷新后重试');
                return;
            }
            
            this.migrateDepartmentDialogVisible = true;
            this.$refs.migrateDepRef.loadData();
            this.$refs.migrateDepRef.reset();
        },
        updateMigrateDialogVisible(value) {
            this.migrateDepartmentDialogVisible = value;
        },
        handleMigrateTargetSelected(data) {
            this.migrateDepartmentDialogVisible = false;
            this.selDepartmentData = data;
            let departmentIds = data.map(item => item.id);
            let departmentNames = data.map(item => item.name);
            this.migrateForm.departmentNames = departmentNames.join(',');
            this.migrateForm.targetDepartmentId = departmentIds.join(',');
        },
        handleMigrateTargetCancel() {
            this.migrateDepartmentDialogVisible = false;
        },
        submitMigrate() {
            this.$refs['migrateForm'].validate((valid) => {
                if (valid) {
                    const targetId = this.migrateForm.targetDepartmentId.split(',')[0];
                    
                    const data = {
                        id: this.migrateForm.departmentId,
                        parentId: targetId
                    };
                    
                    migrateDepartment(data).then(res => {
                        if (res.status == 0) {
                            this.$message.success('部门迁移成功');
                            this.migrateDialogVisible = false;
                            this.selDepartmentData = [];
                            this.migrateForm.targetDepartmentId = '';
                            this.migrateForm.departmentNames = '';
                            this.getDepartmentInfo();
                            this.$emit('migrate', data);
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                } else {
                    return false;
                }
            });
        },
        closeDialog() {
            this.migrateDialogVisible = false;
            this.selDepartmentData = [];
            this.migrateForm.targetDepartmentId = '';
            this.migrateForm.departmentNames = '';
            if (this.$refs.migrateDepRef) {
                this.$refs.migrateDepRef.reset();
            }
        },
        resetDepartmentSelection() {
            if (this.$refs.migrateDepRef) {
                this.$refs.migrateDepRef.reset();
            }
        },
        handleDelete() {
            this.$confirm('确定要删除该部门吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const data = { id: this.departmentId };
                deleteDepartment(data).then(res => {
                    if (res.status == 0) {
                        this.$message.success('删除部门成功');
                        this.visible = false;
                        this.$emit('delete', this.departmentId);
                    } else {
                        this.$message.error(res.msg);
                    }
                })
            })
        }
    }
};
</script>

<style lang="scss" scoped>
.departmentDrawer{
}
/deep/.el-drawer__header{
    margin-bottom: 0px !important;
}
/deep/.el-drawer__body {
    padding:  20px;
}
.rcenter{
    display: flex;
    align-items: flex-end;
}
</style> 