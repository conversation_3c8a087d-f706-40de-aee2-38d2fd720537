<!--会员提现-->
<template>
    <div>
        <BaseOrderNumber :moneyList="moenyData"></BaseOrderNumber>
        <el-form :inline="true">
            <el-form-item>
                <el-select clearable placeholder="提现方式" v-model="dataForm.businessType">
                    <el-option
                        v-for="item in Dict.Money_STATUS"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
              <SelectDatePicker v-model="time"></SelectDatePicker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" @click="handleSearch">查询</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
            prop="id"
            label="订单号"
            width="140">
            </el-table-column>
            <el-table-column
            prop="payeeAccount"
            label="提现账户"
            width="200">
            </el-table-column>
            <el-table-column
            label="提现方式"
            width="140">
            <template slot-scope="scope">{{scope.row.paymentChannelType | dict('Money_STATUS')}}</template>
            </el-table-column>
            <el-table-column
            prop="amount"
            label="提现金额(元)"
            width="180">
            </el-table-column>
            <el-table-column
            label="申请时间"
            width="140">
              <template slot-scope="scope">
                <span>{{ruleTimeSuccess(scope.row.createTime)}}</span>
              </template>
            </el-table-column>
            <el-table-column
            label="到账时间"
            width="140">
              <template slot-scope="scope">
                <span>{{ruleTimeSuccess(scope.row.modifyTime)}}</span>
              </template>
            </el-table-column>
            <el-table-column
            prop="status"
            label="状态"
            width="140">
                <template slot-scope="scope">
                  <span>{{statusType(scope.row.status)}}</span>
                </template>
            </el-table-column>
            <el-table-column
            label="审核状态"
            width="140">
                <template slot-scope="scope">
                  <span>{{auditType(scope.row.auditStatus)}}</span>
                </template>
            </el-table-column>
            <el-table-column
            prop=""
            label="备注">
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div>
          <el-pagination
            v-if="pagingObj.totalSum"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagingObj.currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagingObj.totalSum">
          </el-pagination>
        </div>
    </div>
</template>

<script>
import BaseOrderNumber from '@/components/base/BaseOrderNumber.vue'
import { ruleTimeSuccess } from '../../../../utils/tools'
import SelectDatePicker from '@/components/select/SelectDatePicker.vue'

export default {
  props: [ 'last' ],
  data() {
    return {
      tableData: [],
      time: [],
      moenyData: [
        {
          label: '提现总额',
          value: ''
        }
      ],
      pagingObj: {
        totalSum: '',
        currentPage: 1
      },
      withdrawal: {},
      dataForm: {}
    }
  },
  components: {
    BaseOrderNumber,
    SelectDatePicker
  },
  watch: {
    lastSelectValue(val) {
      this.last = val
      this.dataForm.userId = this.last.id
      this.withdrawalStatistical(this.dataForm)
      this.getMemberRecharge(this.dataForm)
    }
  },
  computed: {
    lastSelectValue() {
      return this.last
    }
  },
  methods: {
    // 时间戳转化为时分秒
    ruleTimeSuccess (val) {
      return ruleTimeSuccess(val)
    },
    // 状态
    statusType(obj) {
      let str = ""
      switch (obj) {
        case 0:
          str = "提现成功"
          break;
        case 1:
          str = "提现失败"
          break;
      }
      return str
    },
    // 审核状态
    auditType(obj) {
      let str = ""
      switch (obj) {
        case 0:
          str = "未审核"
          break;
        case 1:
          str = "审核通过"
          break;
      }
      return str
    },
    // 分页
    handleSizeChange (val) {
      this.dataForm.pageSize = val
      this.getMemberRecharge(this.dataForm)
    },
    handleCurrentChange (obj) {
      this.dataForm.pageNum = obj
      this.getMemberRecharge(this.dataForm)
    },
    // 查询
    handleSearch() {
      this.handleTimeParams(this.time)
      this.getMemberRecharge(this.dataForm)
      this.withdrawalStatistical(this.dataForm)
    },
    // 获取时间
    handleTimeParams (obj) {
      if (obj && obj instanceof Array) {
        this.dataForm.startTime = obj[0];
        this.dataForm.endTime = obj[1]
      } else {
        this.dataForm.startTime = '';
        this.dataForm.endTime = ''
      }
    },
    // 会员提现列表
    async getMemberRecharge(obj) {
      let res = await this.$axios.get('sf/member/fundAccountFlow/userWithdraw', { params: obj })
      if (res.status === 0) {
        this.tableData = res.data
        this.pagingObj.totalSum = res.page.total
      } else {
        this.$message.error(res.msg)
      }
    },
    // 会员提现统计
    async withdrawalStatistical(obj) {
      obj.fundType = "4"
      let res = await this.$axios.get('sf/member/fundAccountFlow/totalFund', { params: obj })
      if (res.status === 0) {
        if (res.data === null) {
          this.moenyData[0].value = 0
        } else {
          this.moenyData[0].value = res.data.totalOrder
        }
      } else {
        this.$message.error(res.msg)
      }
    }
  },
  created() {
    this.dataForm.userId = this.last.id
    this.withdrawalStatistical(this.dataForm)
    this.getMemberRecharge(this.dataForm)
  }
}
</script>

<style lang="scss" scoped>
.enveloright {
  top: 0px !important;
  right: 0px !important;
  position: relative !important;
}
</style>
