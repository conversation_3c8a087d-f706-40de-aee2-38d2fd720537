<template>
  <div class="mainbg">
      <listtable :typeLabel="typeLabel"></listtable>
  </div>
</template>

<script>

import listtable from '../components/index.vue';
  export default {
      components: {
        listtable
      },
      data() {
          return {
            typeLabel:'6'
          }
      },
      created() {
      },
      mounted() {
         
      },
      methods: {
       
      }
  }
</script>

<style scoped>
  .mainbg {
      padding: 20px;
      background-color: white;
      transform: none;
  }

  .pcc {
      margin: 0 auto;
      text-align: center;
  }

  .smtext {
      zoom: 0.8;
  }

  .fxcenter {
      width: 50px;
      height: 52px;
      display: flex;
      justify-content: center;
      align-items: center;
  }

  .zhiding {
      width: 16px;
      height: 16px;
      margin-right: 4px;
  }

  .mt {
      margin-top: 4px;
  }

  .cusnamecss {
      display: flex;
  }

  .tagcss {
      font-size: .625em !important;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      height: 14px;
      min-width: 30px;
      line-height: 11px;
      border-radius: 2px;
      margin-right: 20px;
  }

  .genjin {
      color: #4285F4;
      background-color: #DFEAFD;
  }

  .fuze {
      color: #FEF2E7;
      background-color: #FF8D1A;
  }

  .xiezuo {
      color: #56C36E;
      background-color: #F3FEF6;
  }

  .tagcss:nth-child(2n+2) {
      margin-top: 4px;
  }

  .ltext {
      text-align: justify !important;
  }

  .mytable /deep/ .cell {
      height: auto !important;
      min-height: 52px !important;
      padding: 13px !important;
      line-height: 25px !important;
  }

  .mytable .el-button {
      padding: 2px;
  }

  .mr10 {
      margin-right: 10px;
  }

  .bbtn,
  .bbtn:hover,
  .bbtn:focus {
      color: #4285F4;
  }

  .rbtn,
  .rbtn:hover,
  .rbtn:focus {
      color: #F45961;
  }

  .right {
      text-align: right;
  }
</style>