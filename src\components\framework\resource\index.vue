<template>
  <div class="resource app-container">
    <el-button
      v-isShow="'crm:controller:resource:saveResource'"
      type="primary"
      class="defaultbtn"
      @click="addOrUpdateHandle('null', 1)"
      ><i class="fa fa-plus"></i> 新增目录
    </el-button>
    <el-button
      type="primary"
      v-isShow="'weihu:genjin'"
      class="defaultbtn"
      @click="addOrUpdateHandle('null', 1)"
      ><i class="fa fa-plus"></i> 测试权限按钮</el-button
    >
    <el-button
      type="primary"
      v-isShow="'weihu:genjin1'"
      class="defaultbtn"
      @click="addOrUpdateHandle('null', 1)"
      ><i class="fa fa-plus"></i> 按钮二</el-button
    >
    <el-table
      class="tableResouze"
      :data="dataList"
      border
      stripe
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
      :row-key="getRowKeys"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
    >
      <el-table-tree-column
        file-icon="icon icon-file"
        folder-icon="icon icon-folder"
        childKey="childrenObj"
        parentKey="parentId"
        levelKey="sType"
        prop="name"
        label="资源名称"
        fixed="left"
        width="230"
      >
      </el-table-tree-column>
      <el-table-column prop="sType" label="资源类型" width="100">
        <template slot-scope="scope">
          <el-tag type="danger" size="mini" v-if="scope.row.sType == 0"
            >目录</el-tag
          >
          <el-tag type="success" size="mini" v-if="scope.row.sType == 1"
            >菜单</el-tag
          >
          <el-tag type="warning" size="mini" v-if="scope.row.sType == 2"
            >按钮</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column prop="permissionKey" label="授权标识"> </el-table-column>
      <el-table-column prop="sUrl" label="访问URL"> </el-table-column>
      <el-table-column prop="sIcon" label="图标" width="100">
        <template slot-scope="scope">
          <!-- <i :class="scope.row.sIcon"></i> -->
          <img :src="imgToUrl[scope.row.sIcon]" alt="" />
        </template>
      </el-table-column>
      <el-table-column prop="sortNo" label="排序号" width="100">
      </el-table-column>
      <el-table-column prop="status" label="资源状态" width="100">
        <template slot-scope="scope">
          <el-tag type="success" size="mini" v-if="scope.row.status == 0"
            >正常</el-tag
          >
          <el-tag type="info" size="mini" v-if="scope.row.status == 1"
            >隐藏</el-tag
          >
          <el-tag type="warning" size="mini" v-if="scope.row.status == 2"
            >删除</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="scope.row.sType == 0 ? '添加菜单' : '添加按钮'"
            placement="left"
          >
            <el-button
              v-isShow="'crm:controller:resource:saveResource'"
              type="text"
              size="mini"
              @click="addOrUpdateHandle(scope.row, 1)"
              v-if="scope.row.sType != 2"
              icon="el-icon-plus"
              >新增</el-button
            >
          </el-tooltip>
          <el-button
            v-isShow="'crm:controller:resource:updateResource'"
            type="text"
            size="mini"
            @click="addOrUpdateHandle(scope.row, 2)"
            icon="el-icon-edit"
            >修改</el-button
          >
          <el-button
            v-isShow="'crm:controller:resource:deleteResource'"
            class="dBtn"
            type="text"
            size="mini"
            icon="el-icon-delete"
            @click="deleteHandle(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <AddOrUpdate
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></AddOrUpdate>
  </div>
</template>

<script>
import AddOrUpdate from "./add-or-update";
import { deleteResource, queryResourceList } from "@/api/framework/resource";
export default {
  data() {
    return {
      dataList: [],
      dataListLoading: false,
      addOrUpdateVisible: false,
      defaultProps: {
        children: "children",
        label: "name"
      },
      imgToUrl: {
        peoject: require("../../../assets/menu/peoject.png"),
        house: require("../../../assets/menu/house.png"),
        kehuweihu: require("../../../assets/menu/kehuweihu.png"),
        gongdan: require("../../../assets/menu/gongdan.png"),
        study: require("../../../assets/menu/study.png"),
        kehu: require("../../../assets/menu/kehu.png"),
        mubiao: require("../../../assets/menu/mubiao.png"),
        fahuo: require("../../../assets/menu/fahuo.png"),
        dict: require("../../../assets/menu/dict.png"),
        xitong: require("../../../assets/menu/xitong.png"),
        product: require("../../../assets/menu/product.png"),
        fafang: require("../../../assets/menu/fafang.png"),
        shuju: require("../../../assets/menu/shuju.png"),
        jiaoyongliang: require("../../../assets/menu/jiaoyongliang.png"),
        shangwu: require("../../../assets/menu/shangwu.png"),
        plan: require("../../../assets/menu/plan.png"),
        zongjie: require("../../../assets/menu/zongjie.png"),
        ribao: require("../../../assets/menu/ribao.png"),
        hetong: require("../../../assets/menu/hetong.png")
      }
    };
  },
  components: {
    AddOrUpdate
  },
  watch: {},
  created() {
    this.getDataList();
  },
  methods: {
    getRowKeys(row) {
      return row.id;
    },
    selectionChangeHandle(val) {
      this.dataListSelections = val;
    },
    addOrUpdateHandle(obj, type) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(obj, type);
      });
    },
    deleteHandle(obj) {
      this.$confirm(`确定进行删除操作?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        let res = await deleteResource({ id: obj.id });
        if (res.status === 0) {
          this.$message({
            message: "操作成功",
            type: "success"
          });
          this.getDataList();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    async getDataList() {
      this.dataListLoading = true;
      let res = await queryResourceList();
      if (res.status === 0) {
        let str = JSON.stringify(res.data);
        let targetstr = str.replace(/children/g, "childrenObj");
        this.dataList = JSON.parse(targetstr);
        this.dataListLoading = false;
      } else {
        this.dataListLoading = false;
      }
    }
  }
};
</script>
<style scoped lang="scss"></style>
