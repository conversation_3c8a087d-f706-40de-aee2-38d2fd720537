<!-- 根据公司查询：所属部门 -->
<template>
  <div>
    <el-form-item label="所属部门" prop="departmentId">
      <el-select clearable placeholder="请选择" v-model="myValue" @change="change">
        <el-option
          v-for="item in Dict.COMPANY_DEPARTMENT"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
export default {
  props: ['value', 'companyid'],
  data () {
    return {
      myValue: '',
      options: [],
      selectTree: []
    }
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    change (val) {
      this.$emit('input', this.myValue)
    },
    async getReqData () {
      let data = {
        companyId: this.companyid
      }
      let res = await this.$axios.get('/sf/business/companydepartment/findByCompanyId', { params: data })
      if (res.status === 0) {
        this.selectTree = res.data
      }
      // if (res.status_code === 0) {
      // let title, id
      // res.result.map(item => {
      //   title = item.o_g_name
      //   id = item.o_g_id
      //   this.options.push({ value: id, label: title })
      // })
      // }
    }
  },
  created () {
    this.myValue = this.value || ''
    this.getReqData()
  }
}
</script>
