import Global from '@/config/global.js'
import Vue from 'vue'
import store from '@/store'

Vue.directive('has', {
  inserted: (el, binding, vnode) => {
    let marker = binding.value;
    if (marker) {
      if (!Global.ACTION_MARKERS.includes(marker)) {
        if (el.parentNode) {
          el.parentNode.removeChild(el)
        }
      }
    } else {
      throw new Error('v-has must have a permission marker')
    }
  }
})

Vue.directive('isShow', {
  inserted: (el, binding, vnode) => {
    let marker = binding.value;
    if (marker) {
      let currentChildren = store.getters.authList
      if (!currentChildren.includes(marker)) {
        if (el.parentNode) {
          el.parentNode.removeChild(el)
        }
      }
    } else {
      return console.log('button must is array')
    }
  }
})

Vue.directive('dbClick', {
  inserted(el, binding) {
    el.addEventListener('click', e => {
      if (!el.disabled) {
        el.disabled = true
        el.style.cursor = 'not-allowed'
        setTimeout(() => {
          el.style.cursor = 'pointer'
          el.disabled = false
        }, 1500)
      }
    })
  }
})





Vue.directive(
  'el-select-loadmore', {
  bind(el, binding) {
    let self = this
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
    SELECTWRAP_DOM.addEventListener('scroll', function () {
      /**
      * scrollHeight 获取元素内容高度(只读)
      * scrollTop 获取或者设置元素的偏移值,常用于, 计算滚动条的位置, 当一个元素的容器没有产生垂直方向的滚动条, 那它的scrollTop的值默认为0.
      * clientHeight 读取元素的可见高度(只读)
      * 如果元素滚动到底, 下面等式返回true, 没有则返回false:
      * ele.scrollHeight - ele.scrollTop === ele.clientHeight;
      */
      const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
      if (condition) binding.value()
    });
  }
}
)
