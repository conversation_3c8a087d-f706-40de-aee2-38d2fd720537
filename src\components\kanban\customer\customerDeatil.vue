<template>
  <div>
    <back>业务经理客户情况</back>
    <el-card class="card">
      <el-form class="myform" label-width="85px" inline>
        <el-form-item label="业务经理：">
          <el-input
            class="definput"
            v-model="pageBean.userName"
            clearable
            placeholder="请输入业务经理姓名"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            class="defaultbtn mt5"
            icon="el-icon-search"
            @click="searchAction"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        v-loading="isLoading"
        :data="tableData"
        style="width: 100%"
        @sort-change="sortAction"
      >
        <el-table-column
          prop="chargePersonName"
          label="业务经理"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="relatedCustomerNum"
          label="关联客户数量"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.relatedCustomerNum + '位' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="threeStarSort"
          sort-by="threeStarCustomer"
          align="center"
          label="3星客户"
          sortable
        >
          <template slot-scope="scope">
            <span class="colors" @click="openDrawer(scope.row, 3)">{{
              scope.row.threeStarCustomer + '位'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="twoStarSort"
          sort-by="twoStarCustomer"
          align="center"
          label="2星客户"
          sortable
        >
          <template slot-scope="scope">
            <span class="colors" @click="openDrawer(scope.row, 2)">{{
              scope.row.twoStarCustomer + '位'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="oneStarSort"
          sort-by="oneStarCustomer"
          align="center"
          label="1星客户"
          sortable
        >
          <template slot-scope="scope">
            <span class="colors" @click="openDrawer(scope.row, 1)">{{
              scope.row.oneStarCustomer + '位'
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </el-card>
    <el-drawer
      :title="`${this.cusPageBean.star}星客户展示`"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%"
      class="custom-drawer"
    >
      <div class="drawer-content clearfix">
        <el-table class="theight" :data="drawerTableData" style="width: 100%">
          <el-table-column prop="customerName" label="客户名称" align="center">
            <template slot-scope="scope">
              <span class="colors">{{ scope.row.customerName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="unitName"
            label="客户单位"
            align="center"
          ></el-table-column>
          <el-table-column prop="unitDepartment" label="部门" align="center">
          </el-table-column>
          <el-table-column prop="duties" label="职务" align="center">
          </el-table-column>
          <el-table-column prop="specialtyName" label="专业" align="center">
            <template slot-scope="scope">
              <span>{{
                scope.row.specialtyName.length > 0
                  ? scope.row.specialtyName.join(',')
                  : '--'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="联系方式" align="center">
          </el-table-column>
        </el-table>
        <div class="pheight">
          <page
            :currentPage="cusPageBean.pageNum"
            :total="custotal"
            :pageSize="cusPageBean.pageSize"
            @updatePageNum="handleCurrentChange2"
          ></page>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import page from '@/components/common/page.vue'
import back from '@/components/common/back.vue'
import { querySituation, queryStarCustomerList } from '@/api/bigdata/index'
export default {
  components: {
    back,
    page,
  },
  data() {
    return {
      drawerVisible: false,
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        oneStarSort: '',
        twoStarSort: '',
        threeStarSort: '',
        userName: '',
        departmentId: [],
        userId: [],
        year: '',
      },
      cusPageBean: {
        pageNum: 1,
        pageSize: 10,
        userId: '',
        star: '',
      },
      custotal: 0,
      drawerTableData: [],
    }
  },
  created() {
    let paramsObj = JSON.parse(this.$route.query.paramsObj)
    this.pageBean.year = this.$route.query.year
    this.pageBean.departmentId = paramsObj.departmentIds
    this.pageBean.userId = paramsObj.userIds
    this.loadData()
  },
  methods: {
    searchAction() {
      this.pageBean.pageNum = 1
      this.clearSort()
      this.loadData()
    },
    loadData() {
      this.isLoading = true
      querySituation(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = true
        })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    sortAction(e) {
      if (e.order === 'ascending') {
        this.pageBean[e.prop] = 1
      } else if (e.order === 'descending') {
        this.pageBean[e.prop] = 2
      } else {
        this.pageBean[e.prop] = ''
      }
      this.clearSort(e.prop)
      this.pageBean.pageNum = 1
      this.loadData()
    },
    clearSort(prop) {
      var array = ['oneStarSort', 'twoStarSort', 'threeStarSort']
      array.forEach((element) => {
        if (element != prop) {
          this.pageBean[element] = ''
        }
      })
    },
    openDrawer(row, star) {
      this.drawerTableData = []
      this.custotal = 0
      if (
        (star == 1 && row.oneStarCustomer == 0) ||
        (star == 2 && row.twoStarCustomer == 0) ||
        (star == 3 && row.threeStarCustomer == 0)
      ) {
        return
      }
      this.cusPageBean.userId = [row.chargePerson]
      this.cusPageBean.star = star
      this.cusPageBean.pageNum = 1
      this.loadCustomer()
      this.drawerVisible = true
    },
    handleCurrentChange2(page) {
      this.cusPageBean.pageNum = page
      this.loadCustomer()
    },
    loadCustomer() {
      queryStarCustomerList(this.cusPageBean).then((result) => {
        this.drawerTableData = result.data
        this.custotal = result.page.total
      })
    },
  },
}
</script>

<style scoped lang="scss">
.custom-drawer /deep/.el-drawer__body {
  height: calc(100% - 80px);
  // background-color: red;
}
.pheight {
  height: 80px;
}
.theight {
  height: calc(100% - 100px);
  overflow-y: auto;
}
.mt5 {
  margin-top: 3px;
}
.card {
  margin-top: 20px;
}

.colors {
  color: #4285f4;
  cursor: pointer;
}

.drawer-content {
  height: 100%;
}
.custom-drawer /deep/.el-drawer__header {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 20px;
  color: #333333;
  text-align: center;
}
</style>