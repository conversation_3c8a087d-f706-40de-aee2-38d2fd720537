<template>
  <div class="mainbg fixpb">
    <el-form :inline="true" label-width="100px" class="myform">
      <el-row type="flex" justify="space-between">
        <el-col :span="18">
          <el-form-item label="所属类型：">
            <el-select
              clearable=""
              class="definput"
              popper-class="removescrollbar"
              v-model="pageBean.parentId"
              placeholder="请选择"
            >
              <el-option
                v-for="item in unitTypes"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-button
            class="defaultbtn mt"
            icon="el-icon-search"
            type="primary"
            @click="searchAction"
            >搜索
          </el-button>
        </el-col>
        <el-col :span="6" class="flexd">
          <el-button
            class="defaultbtn mt"
            icon="el-icon-plus"
            type="primary"
            @click="addInfo"
            >新增</el-button
          >
        </el-col>
      </el-row>
    </el-form>
    <el-table
      class="mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column prop="name" label="单位特点" align="center">
      </el-table-column>
      <el-table-column prop="parentName" label="所属类型" align="center">
      </el-table-column>
      <el-table-column
        prop="edit"
        width="140"
        align="center"
        label="操作"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-isShow="'crm:controller:workorder:update'"
            class="bbtn"
            type="text"
            @click="onEdit(scope.row)"
          >
            编辑</el-button
          >
          <el-button
            v-isShow="'crm:controller:workorder:delete'"
            class="rbtn"
            type="text"
            @click="deleteAction(scope.row)"
          >
            删除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogFormVisible"
      width="500px"
      center
      :before-close="handleClose"
    >
      <el-form
        ref="addform"
        label-width="140px"
        :model="form"
        :rules="rules"
        class="addform"
      >
        <el-form-item prop="name" label="单位特点描述：">
          <!-- <el-input   v-model="form.name" maxlength="20" show-word-limit placeholder="请输入单位特点描述"></el-input> -->
          <el-autocomplete
            v-model="form.name"
            :fetch-suggestions="querySearch"
            @select="handlename"
            placeholder="请输入单位特点描述"
            :trigger-on-focus="false"
            class="el-auto"
            :maxlength="20"
            show-word-limit
          >
          </el-autocomplete>
        </el-form-item>
        <el-form-item prop="parentId" label="所属类型：">
          <el-select
            popper-class="removescrollbar"
            v-model="form.parentId"
            placeholder="请选择"
          >
            <el-option
              v-for="item in unitTypes"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">取 消</el-button>
        <el-button type="primary" @click="submitAction()" :loading="isSubmit"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <dc-dialog
      iType="1"
      title="温馨提示"
      width="500px"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <p class="pcc">是否删除该特点？</p>
    </dc-dialog>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import {
  dictList,
  adddict,
  dictInfo,
  deleteDict,
  updateDict,
  queryList,
} from '@/api/dict/index'
import { getDict } from '@/utils/tools'
export default {
  components: {
    page,
    nolist,
  },
  data() {
    return {
      deleteData: {},
      detailVisible: false,
      dialogVisible: false,
      dialogFormVisible: false,
      dialogTitle: '',
      isSubmit: false,
      isLoading: false,
      unitTypes: [],
      tableData: [],
      total: 0,
      form: {
        name: '',
        parentId: '',
        dictTypeCode: 'UnitCharacter',
        code: '',
        sortNo: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入单位特点描述', trigger: 'blur' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
        ],
        parentId: [
          { required: true, message: '请选择所属类型', trigger: 'change' },
        ],
      },
      pageBean: {
        parentId: '',
        dictTypeCode: 'UnitCharacter',
        pageNum: 1,
        pageSize: 10,
      },
    }
  },
  created() {
    getDict('UnitType')
      .then((result) => {
        this.unitTypes = result
      })
      .catch((err) => {})
    this.loadData()
  },
  methods: {
    querySearch(queryString, callback) {
      var list = [{}]
      if (queryString && queryString.length > 0) {
        let params = {
          dictTypeCode: 'UnitCharacter',
          name: queryString,
        }
        queryList(params).then((res) => {
          list = res.data.map((item) => {
            return {
              id: `${item.id}`,
              value: `${item.name}`,
            }
          })
          callback(list)
        })
      }
    },
    handlename(item) {
      this.form.name = item.value
    },
    loadData() {
      this.isLoading = true
      dictList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    submitDialog() {
      deleteDict({ id: this.deleteData.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
      this.dialogVisible = false
    },
    exportInfo() {},
    addInfo() {
      this.form.dictTypeCode = 'UnitCharacter'
      this.form.parentId = ''
      this.dialogFormVisible = true
      this.dialogTitle = '新增'
    },
    deleteAction(data) {
      this.deleteData = data
      this.dialogVisible = true
    },
    onEdit(data) {
      this.loadinfo(data.id)
      this.dialogFormVisible = true
      this.dialogTitle = '编辑'
    },
    loadinfo(id) {
      dictInfo(id)
        .then((result) => {
          this.form = result.data
        })
        .catch((err) => {})
    },
    handleClose() {
      Object.keys(this.form).forEach((item) => {
        if (item != 'dictTypeCode' && this.form[item] != '') {
          this.form[item] = ''
        }
      })
      if (!this.form.id) {
        this.$refs.addform.clearValidate()
      }
      this.dialogFormVisible = false
    },
    submitAction() {
      console.log('this.form', this.form)
      this.$refs['addform'].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            this.updateData()
          } else {
            this.add()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    add() {
      this.isSubmit = true
      adddict(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功',
            })
            this.handleClose()
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    updateData() {
      this.isSubmit = true
      delete this.form.createTime
      delete this.form.modifyTime
      updateDict(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '更新成功',
            })
            this.handleClose()
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>




<style scoped>
.el-auto {
  width: 310px;
}
.flexd {
  text-align: right;
}
.definput {
  width: 200px;
}
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: 100%;
}
.pcc {
  margin: 0 auto;
  text-align: center;
}
.smtext {
  zoom: 0.8;
}
.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.mt {
  margin-top: 4px;
}
.cusnamecss {
  display: flex;
}
.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 20px;
}
.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}
.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}
.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}
.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}
.ltext {
  color: #4285f4;
  text-align: justify !important;
}
.mytable /deep/ .cell {
  height: auto !important;
  min-height: 52px !important;
  padding: 13px !important;
  line-height: 25px !important;
}
.mytable .el-button {
  padding: 2px;
}
.mr10 {
  margin-right: 10px;
}
.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.right {
  text-align: right;
}
</style>