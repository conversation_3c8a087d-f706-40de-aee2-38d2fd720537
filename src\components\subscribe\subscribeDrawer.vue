<template>
  <el-drawer
    :visible.sync="visible"
    :direction="direction"
    :size="size"
    :title="title"
  >
    <div class="drawer-content">
      <div class="right">折扣支持两位小数，折扣范围为0.01～1.00，如75折可输入0.75。</div>
      <el-table :data="tableData" style="width: 100%" class="unittable mytable">
        <el-table-column prop="materialName" label="教材名称" align="center" />
        <el-table-column prop="isbn" label="ISBN" align="center" />
        <el-table-column prop="platformName" label="出版社" align="center" />
        <el-table-column prop="unitName" label="用书单位" align="center" />
        <el-table-column prop="useBookSpecialty" label="用书专业" align="center" />
        <el-table-column prop="customerName" label="客户" align="center" />
        <el-table-column prop="useBookYear" label="用书时间" align="center" />
        <el-table-column prop="price" label="定价(元)" align="center" />
        <el-table-column prop="reserveNum" label="预定数量" align="center" />
        <el-table-column label="实报数量" align="center" width="100px">
          <template slot-scope="scope">
            <el-input-number
              :controls="false"
              class="numcss"
              :precision="0"
              :step="1"
              :min="0"
              size="mini"
              placeholder="实报数量"
              v-model="scope.row.actualNum"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="折扣" align="center" width="100px">
          <template slot-scope="scope">
            <el-input-number
              class="numcss"
              :controls="false"
              :precision="2"
              :step="0.01"
              :min="0.01"
              :max="1.00"
              size="mini"
              placeholder="设置折扣"
              v-model="scope.row.discount"
            ></el-input-number>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="drawer-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确认</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { subscriptionUpdate } from '@/api/baoding'
export default {
  name: 'ApprovalDrawer',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: '报批'
    }
  },
  data() {
    return {
      direction: 'rtl',
      size: '70%',
      tableData: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    data: {
      immediate: true,
      handler(val) {
        if (val && val.length) {
          this.tableData = JSON.parse(JSON.stringify(val)).map(item => {
            return {
              ...item,
              actualNum:item.actualNum>0 ? item.actualNum : undefined,
              discount:item.discount>0 ? item.discount : 1.00
            }
          })
        } else {
          this.tableData = []
        }
      }
    }
  },
  methods: {
    handleSubmit() {
      var list = [];
      this.tableData.forEach(item =>{
        list.push({
          id: item.id,
          createBy:item.createBy,
          actualNum:item.actualNum,
          discount:item.discount,
          departmentId:item.departmentId,
          status:item.status === 0 ? undefined : item.status
        })
      }
      )
      subscriptionUpdate(list).then(res =>{
        if(res.data){
          this.$emit('submit', this.tableData)
          this.visible = false
        }else{
          this.$message.error(res.msg)
        }
      }).catch(err =>{

      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mytable{
  // height:clac(100% - 200px);
}
.numcss{
  width:80px;
}
.right{
    text-align: right;
    margin-bottom: 10px !important;
    color: #999999;
}
.drawer-content {
  padding: 20px;
  padding-bottom:70px;
  max-height:calc(100vh - 200px)

  .mytable{
  }
}
.drawer-footer {
  height:70px;
  margin-top: 20px;
  padding:20px;
  text-align: right;
}
</style>
