/**
 * @Desc 业务字段 下拉框维护表
 * @Name 命名要大写，多个单词用下划线隔开
 */

export default {

  MEAL_TYPE: [
    {
      value: '0',
      label: '女'
    },
    {
      value: '1',
      label: '男'
    }
  ],
  RESOURCE_TYPE: [
    {
      value: '0',
      label: '目录'
    },
    {
      value: '1',
      label: '菜单'
    },
    {
      value: '2',
      label: '按钮'
    }
  ],


  // 配置管理-新增配置控件
  CONFIG_NEW: [
    {
      value: 'TEXT',
      label: '输入框'
    },
    {
      value: 'DATE',
      label: '日期'
    },
    {
      value: 'COMBOBOX',
      label: '下拉单选'
    },
    {
      value: 'COLOR',
      label: '颜色'
    },
    {
      value: 'CHECKBOXTREE',
      label: '下拉多选'
    }
  ],
  // 任务状态
  TASK_STATUS: [
    {
      value: '0',
      label: '成功'
    },
    {
      value: '1',
      label: '失败'
    }
  ],
  // 发行计划状态
  PLAN_STATUS: [
    {
      value: 'all',
      label: '全部'
    },
    {
      value: 'wait',
      label: '未审核'
    },
    {
      value: 'success',
      label: '审核通过'
    },
    {
      value: 'fail',
      label: '审核驳回'
    },
    {
      value: 'del',
      label: '已删除'
    }
  ],
  // 员工管理
  EMPLOYEE_STATUS: [
    {
      value: '1',
      label: '正常'
    },
    {
      value: '3',
      label: '已冻结'
    },
    {
      value: '4',
      label: '已注销'
    },
    {
      value: '5',
      label: '已禁用'
    },
    {
      value: '7',
      label: '已离职'
    }
  ],
  // 学历
  EDUCATIONAL_STATUS: [
    {
      value: '1',
      label: '初中'
    },
    {
      value: '2',
      label: '中专/职高/技校'
    },
    {
      value: '3',
      label: '高中'
    },
    {
      value: '4',
      label: '大专'
    },
    {
      value: '5',
      label: '本科'
    },
    {
      value: '6',
      label: '硕士'
    },
    {
      value: '7',
      label: '博士'
    },
    {
      value: '8',
      label: '博士后'
    }
  ],
  //  血型
  BLOOD_TYPE: [
    {
      value: '1',
      label: 'A型'
    },
    {
      value: '2',
      label: 'B型'
    },
    {
      value: '3',
      label: 'O型'
    },
    {
      value: '4',
      label: 'AB型'
    },
    {
      value: '5',
      label: '其他'
    }
  ],
  // 婚姻状态
  MARATITAL_STATUS: [
    {
      value: '1',
      label: '未婚'
    },
    {
      value: '2',
      label: '已婚'
    },
    {
      value: '3',
      label: '离婚'
    },
    {
      value: '4',
      label: '丧婚'
    }
  ],
  // 红包 指定平台
  RED_PLATFORM: [
    {
      value: '2',
      label: 'APP端'
    },
    {
      value: '3',
      label: 'PC端'
    },
    {
      value: '4',
      label: '微信H5，公众号'
    },
    {
      value: '5',
      label: '微信小程序'
    },
    {
      value: '6',
      label: '支付宝小程序'
    },
    {
      value: '7',
      label: '抖音小程序'
    },
    {
      value: '8',
      label: '百度小程序'
    }
  ],
  // 资源管理
  DATAFORM_STATUS: [
    {
      value: '0',
      label: '正常'
    },
    {
      value: '1',
      label: '隐藏'
    }
  ],
  // 订单统计列表 结算状态
  SETTLEMENT_STATUS: [
    {
      value: '0',
      label: '初始状态(订单刚创建)'
    },
    {
      value: '1',
      label: '未结算(已支付成功，待到账)'
    },
    {
      value: '2',
      label: '结算成功'
    },
    {
      value: '3',
      label: '结算失败'
    },
    {
      value: '4',
      label: '部分结算'
    }
  ],


  // 收银台 订单状态
  ORDERMONEY_STATU: [
    {
      value: '0',
      label: '未支付'
    },
    {
      value: '1',
      label: '已支付'
    },
    {
      value: '2',
      label: '取消支付'
    },
    {
      value: '3',
      label: '支付失败'
    }
  ],
  // 收银台 提现
  WITCHDRAW_MONEY: [
    {
      value: '0',
      label: '未审核'
    },
    {
      value: '1',
      label: '审核通过'
    },
    {
      value: '2',
      label: '审核驳回'
    }
  ],
  // 会员管理 充值类型
  Money_STATUS: [
    {
      value: '1',
      label: '微信'
    },
    {
      value: '2',
      label: '支付宝'
    },
    {
      value: '3',
      label: '云闪付'
    }
  ],
  //
  Money_TYPE: [
    {
      value: '0',
      label: '未支付'
    },
    {
      value: '1',
      label: '已支付'
    },
    {
      value: '2',
      label: '失败'
    }
  ],
  // 文章管理 文章和列表
  ARTICLE_TYPE: [
    {
      value: '1',
      label: '文章'
    },
    {
      value: '2',
      label: '列表'
    }
  ],
  // 登录日志 flatform
  FLATFORM_TYPE: [
    {
      value: 'APP',
      label: 'APP'
    },
    {
      value: 'PC',
      label: 'PC'
    },
    {
      value: 'OCS',
      label: 'OCS'
    }
  ],
  // 登录日志 操作项
  ACTION_TYPE: [
    {
      value: 'insert',
      label: 'insert'
    },
    {
      value: 'delete',
      label: 'delete'
    },
    {
      value: 'update',
      label: 'update'
    },
    {
      value: 'updateUser ',
      label: 'updateUser '
    }
  ],
  // 投诉举报类型
  COMPLAIN_TYPE: [
    {
      value: '0',
      label: '未处理'
    },
    {
      value: '1',
      label: '已处理'
    },
    {
      value: '2',
      label: '无效反馈'
    }
  ],

  // 活动主题风格
  THENE_DATA: [
    {
      name: "蓝色",
      type: 1
    },
    {
      name: "白色",
      type: 2
    },
    {
      name: "绿色",
      type: 3
    }
  ],


  // app 应用
  APPLICATION: [{
    value: "ios",
    label: "苹果"
  },
  {
    value: "android",
    label: "安卓"
  }
  ]

}
