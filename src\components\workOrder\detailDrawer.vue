<template>
    <el-drawer
    center
    class="mydrawer"
    title="工单详情"
    size="50%"
    :visible.sync="drawer"
    direction="rtl">
    <div class="concss">
        <el-form class="infoform" ref="form"  label-width="130px">
            <el-row :gutter="0" class=" mt10 pb20 mb30 bbline"  type="flex" justify="center">
                <el-col :span="24">
                    <el-form-item label="工单内容：" class="labeltext lh">
                        <span>{{form.content}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="0" class="width100 mt10" type="flex" justify="center">
                <el-col :span="12">
                    <el-form-item label="工单状态：" class="labeltext">
                        <span>{{form.workOrderStatus}}</span>
                    </el-form-item>
                    <el-form-item label="负责人：" class="labeltext">
                        <span>{{form.chargePersonName}}</span>
                    </el-form-item>
                    <el-form-item label="受理人：" class="labeltext">
                        <span>{{form.acceptedPersonName}}</span>
                    </el-form-item>
                    <el-form-item label="创建时间：" class="labeltext">
                        <span>{{form.createTime}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="12">  
                    <el-form-item label="优先级：" class="labeltext">
                        <span>{{form.workOrderPriority}}</span>
                    </el-form-item>
                    <el-form-item label="负责人所在部门：" class="labeltext">
                        <span>{{form.chargePersonDepartmentName}}</span>
                    </el-form-item>
                    <el-form-item label="受理人所在部门：" class="labeltext">
                        <span>{{form.acceptedPersonDepartmentName}}</span>
                    </el-form-item>
                    <el-form-item label="完成时间：" class="labeltext">
                        <span>{{form.completeTime ? form.completeTime : '--'}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="0" class="width100 pb12 mb20 bbline "  type="flex" justify="center">
                <el-col :span="24">
                    <el-form-item label="协作人：" class="labeltext lh">
                        <span>{{form.collaboratorName}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 "  type="flex" justify="center">
                <el-col :span="12">
                  <el-form-item label="关联合同：" class="labeltext lh">
                      <span>{{form.contractName}}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">  
                  <el-form-item label="关联项目：" class="labeltext lh">
                      <span>{{form.projectName}}</span>
                  </el-form-item>
                </el-col>
            </el-row>
        </el-form>

    </div>
    </el-drawer>
</template>

<script>
import { workorderInfo } from "@/api/workorder";
export default {
    props:{
        visible:{
            type:Boolean,
            default:false
        }
    },
    computed:{
        drawer:{
            get(){
                return this.visible
            },
            set(val){
                this.$emit('updateVisible',val)
            }
        }
    },
    data(){
        return{
            isLoading:'',
            form:{
                id:'',
                content:"",
                status:'',
                priority:"",
                chargePerson:"",
                chargePersonName:"",
                acceptedPerson:"",
                acceptedPersonName:'',
                contractId:"",
                projectId:"",
                chargePersonDepartmentId:"",
                acceptedPersonDepartmentId:"",
            },
        }
    },
    methods:{
        loadInfo(id){
            this.isLoading = true;
            workorderInfo(id).then((result) => {
                this.form = result.data;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
    }
}
</script>

<style scoped>
.lh24 /deep/.el-form-item__content{
    line-height: 24px !important;
    padding: 0;
    padding-top: 8px;
}
.filebg{
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  padding: 3px 10px;
  color: #4285F4;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #4285F4;
  cursor: pointer;

}
.pb12{
    padding-bottom: 12px;
}
.lh /deep/.el-form-item__content{
    line-height: 18px !important;
    padding: 0;
    padding-top: 12px;
}
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}
.concss{
    padding: 0px 20px;
    padding-top: 30px;
}
.mydrawer /deep/.el-drawer__header{
    text-align: center;
    color: #333333;
}
</style>