import request from '@/utils/request'
export function addResource(data) {
  return request({
    url: '/crm/controller/resource/saveResource',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}
export function updateResource(data) {
  return request({
    url: '/crm/controller/resource/updateResource',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}
export function deleteResource(query) {
  return request({
    url: '/crm/controller/resource/deleteResource',
    method: 'get',
    params: query
  })
}

export function queryResourceList(query) {
  return request({
    url: '/crm/controller/resource/queryResourceList',
    method: 'get',
    params: query
  })
}


export function resourceInfo(id) {
  return request({
    url: `/crm/controller/resource/info/${id}`,
    method: 'get',
  })
}





