<template>
  <div>
    <el-dialog
      class="dg"
      center=""
      :append-to-body="true"
      title="回款"
      :visible.sync="dialogVisible"
      width="50%"
      @close="$emit('update:show', false)">
      <el-form :rules="rules" class="infoform" ref="form" :model="form" label-width="140px">
          <el-row :gutter="0" class="width100 mt10" >
            <el-col :span="12">
              <el-form-item label="回款金额(万元)：" prop="returnAmount">
                <el-input class="definput" v-model="form.returnAmount"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="币种：" class="">
                <el-select class="definput" v-model="form.currency" placeholder="请选择币种">
                  <el-option v-for="item in currencyType" :key="item.id" :label="item.name"
                                :value="item.id">
                      </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            </el-row>
            <el-row :gutter="0" class="width100 mt10">
                <el-col :span="12">
                <el-form-item label="实际回款日期：" class="" prop="returnTime">
                  <el-date-picker
                  value-format="yyyy-MM-dd HH:mm:ss"
                  class="defdate"
                  v-model="form.returnTime"
                  type="datetime"
                  placeholder="选择日期">
                 </el-date-picker>
                </el-form-item>
                </el-col>
                <el-col :span="12">
                <el-form-item label="付款方式：" class="">
                  <el-select class="definput" v-model="form.paymentWay" placeholder="请选择付款方式">
                      <el-option v-for="item in payType" :key="item.value" :label="item.name"
                                :value="item.value">
                      </el-option>
                </el-select>
                </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                  <el-form-item prop="isAdvance" label="是否预收：" class="">
                    <el-select  clearable="" class="definput" v-model="form.isAdvance" placeholder="请选择是否预收">
                      <el-option v-for="item in isAdvanceArr" :key="item.value" :label="item.name"
                                :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
            </el-row>


            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
            <el-col :span="12">
              <el-form-item label="收款账户：" >
                <el-input class="definput" v-model="form.collectionAccount"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交易流水号：" class="" prop="transactionNumber">
                <el-input class="definput" v-model="form.transactionNumber"></el-input>
              </el-form-item>
            </el-col>
            </el-row>
                    <el-button class="w98 btn_h42 mauto" type="primary" @click="save">提交</el-button>
    </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getDict } from '@/utils/tools'
import {currencySave} from '@/api/stage/index'
  export default {
        data () {
            return {
              dialogVisible: this.show,
              form: {
                returnAmount: '',
                collectionAccount: '',
                transactionNumber: '',
                isAdvance: '',
                returnTime: '',
                currency:'',
                stageId:'',
                paymentWay:'',
              },
              rules:{
                returnAmount: [
                    { required: true, message: '请输入回款金额（万元）', trigger: 'blur' },
                ],
                returnTime: [
                    { required: true, message: '请输入实际回款日期', trigger: 'blur' }
                ],
                transactionNumber: [
                    { required: true, message: '请输入交易流水号', trigger: 'blur' },
                ],
                isAdvance: [
                    { required: true, message: '请选择是否预收', trigger: 'change' },
                ],
            },
              isAdvanceArr:[
                  {
                    name:'是',
                    value:'1'
                  },
                  {
                    name:'否',
                    value:'0'
                  },
                ],
                payType:[
                  {
                    name:'现金',
                    value:'1'
                  },
                  {
                    name:'银行转账',
                    value:'2'
                  },
                  {
                    name:'微信转账',
                    value:'3'
                  },
                  {
                    name:'支付宝转账',
                    value:'4'
                  },
                  {
                    name:'其他',
                    value:'5'
                  },
                ],
                currencyType:[],
            };
        },
        props: {
            show: {
                type: Boolean,
                default: false
            },
            stageId:{
              type:String,
              default:''
            }
        },
        created(){
          getDict('Currency').then((result) => {
            this.currencyType = result;
          }).catch((err) => {

          });
        },
        watch: {
            show () {
                this.dialogVisible = this.show;
            }
        },
        methods:{
          save(){
            this.form.stageId = this.stageId
            this.$refs['form'].validate((valid) => {
                if (!valid) {
                    return false;
                }
                  currencySave(this.form).then(res=>{
                      if(res.status == 0){
                              this.msgSuccess('添加成功')
                              this.$emit('loadData')
                              this.$emit('update:show', false)
                      }
                  })
            });

          }
        }
  }
</script>

<style lang="scss" scoped>
.dg{
  /deep/ .el-dialog{
    min-width: 720px;
  }
  /deep/.el-dialog__title{
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
  }
}
.pb12{
  padding-bottom: 10px;
}
.infoform /deep/.el-form-item{
    margin-bottom: 10px !important;
}
.mauto{
  margin: 0 auto;
  display: block
}
.defdate /deep/.el-input__inner{
    width: 100% !important;
    height: 100%;
    line-height: 34px;
}
.defdate{
  width: 100% !important;
  height: 34px;
  line-height: 34px;
}
.defdate /deep/.el-input__icon{
    line-height: 34px;
}

</style>
