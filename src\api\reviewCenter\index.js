import request from '@/utils/request'

export function reviewList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/processrecord/processList',
    params
  });
}
export function approve(data) {
  return request({
    method: 'post',
    url: '/crm/controller/processrecord/approve',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function ruleSave(data) {
  return request({
    method: 'post',
    url: '/crm/controller/processrule/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
export function ruleDelete(data) {
  return request({
    method: 'post',
    url: '/crm/controller/processrule/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function ruleUpdate(data) {
  return request({
    method: 'post',
    url: '/crm/controller/processrule/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}



// 获取规则详情
export function ruleInfo(id) {
  return request({
    method: 'get',
    url: `/crm/controller/processrule/info/${id}`
  });
}





// 删除公告
export function deleteGonggao(id) {
  return request({
    method: 'get',
    url: `/crm/controller/version/delete`,
    params: { id }
  });
}



// 更新公告
export function updateGonggao(data) {
  return request({
    method: 'post',
    url: '/crm/controller/version/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

//意见反馈列表
export function fetchFeedBackList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/feedback/list',
    params
  });
}

// 教材报批审核列表
export function querySubscriptionList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/processrecord/querySubscriptionList',
    params
  });
}
// 开票审核列表
export function queryContractInvoicingList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/processrecord/queryContractInvoicingList',
    params
  });
}
// 回款审核列表
export function queryContractReturnList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/processrecord/queryContractReturnList',
    params
  });
}
// 退款审核列表
export function queryContractRefundList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/processrecord/queryContractRefundList',
    params
  });
}
// 项目成本审核列表
export function queryProjectCostList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/processrecord/queryProjectProcessList',
    params
  });
}
// 项目成本详情
export function projectCostInfo(id) {
  return request({
    method: 'get',
    url: `/crm/controller/taskcost/info/${id}`
  });
}

