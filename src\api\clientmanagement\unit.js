import request from "@/utils/request";

// 单位列表
export function listUnit(params) {
  return request({
    url: "/crm/controller/unit/list",
    method: "get",
    params
  });
}


// 单位列表
export function customerbook(params) {
  return request({
    url: "/crm/business/customerbook/list",
    method: "get",
    params
  });
}






// 单位详情
export function unitInfo(id) {
  return request({
    url: `/crm/controller/unit/info/${id}`,
    method: "get"
  });
}
// 查询单位概览信息（合同金额、关联客户等信息）
export function queryUnitOverview(params) {
  // /crm/controller/unit/queryUnitOverview
  return request({
    url: "/crm/controller/unit/queryUnitOverview",
    method: "get",
    params
  });
}

export function addUnit(data) {
  return request({
    url: "/crm/controller/unit/save",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    },
    data
  });
}
export function update<PERSON>hao(data) {
  return request({
    url: "/crm/business/recruitstudentinfo/update",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    },
    data
  });
}

export function add<PERSON>hao(data) {
  return request({
    url: "/crm/business/recruitstudentinfo/save",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    },
    data
  });
}

// 新建单位
export function updateUnit(data) {
  return request({
    url: "/crm/controller/unit/update",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    },
    data
  });
}
export function deleteUnit(data) {
  return request({
    url: "/crm/controller/unit/delete",
    method: "post",
    data
  });
}
export function deleteZhao(data) {
  return request({
    url: "/crm/business/recruitstudentinfo/delete",
    method: "post",
    data
  });
}

// 添加客户、单位目标设置时选择单位
export function selectUnit(params) {
  return request({
    method: "get",
    url: "/crm/controller/unit/selectUnit",
    params
  });
}

// 单位类型
export function unitType(params) {
  return request({
    method: "get",
    url: "/crm/controller/unit/queryUnitType",
    params
  });
}

// 单位特点
export function queryUnitCharacter(params) {
  return request({
    method: "get",
    url: "/crm/controller/dicttype/queryItemAll",
    params
  });
}

// 单位结构详情
export function unitSD(id) {
  return request({
    method: "get",
    url: "/crm/controller/structure/info/" + id
  });
}
export function zhaoInfo(id) {
  return request({
    method: "get",
    url: "/crm/business/recruitstudentinfo/info/" + id
  });
}

export function zhaoList(params) {
  return request({
    url: "/crm/business/recruitstudentinfo/list",
    method: "get",
    params
  });
}

export function selectUintSpecialty(params) {
  return request({
    url: "/crm/business/unitspecialty/selectUintSpecialty",
    method: "get",
    params
  });
}

// 单位模版下载
export function unitDownLoad() {
  return request({
    url: `/crm/controller/unit/unitDownLoad`,
    method: 'get',
    responseType: "blob"
  })
}

//单位导出
export function unitExport(params) {
  return request({
    url: `/crm/controller/unit/exportExcel`,
    method: 'get',
    params
  })
}

// 批量分配负责人
export function insertBatchChargePerson(data) {
  return request({
    url: '/crm/controller/unit/insertBatchChargePerson',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}
//下载招生数据
export function unitZhaoDownLoad(data) {
  return request({
    url: `/crm/business/recruitstudentinfo/downloadRecruitStudentInfo`,
    method: 'post',
    responseType: "blob",
    data
  })
}