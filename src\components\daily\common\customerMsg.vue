<template>
    <el-drawer
    class="custord"
    title="客户信息"
    size="50%"
    :visible.sync="visible"
    :before-close="handleClose"
    direction="rtl"
    >
  <el-form class="infoform" ref="form" :model="formInfo" label-width="80px">
        <textBorder>基本信息</textBorder>
        <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline" >
            <el-col :span="12">
                <el-form-item label="客户名称:" class="labeltext mulitline">
                    <span>{{formInfo.customerName}}</span>
                </el-form-item>
                <el-form-item label="部门:" class="labeltext">
                <span>{{formInfo.unitDepartment}}</span>
                </el-form-item>
                <el-form-item label="客户级别:" class="labeltext mulitline">
                <span :class="customerLevelColors[formInfo.customerLevelName]">{{formInfo.customerLevelName}}</span>
                </el-form-item>
                <el-form-item label="负责课程:" class="labeltext">
                    <span>{{formInfo.responsibleCourse}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="12">  
                <el-form-item label="客户单位:" class="labeltext">
                    <span>{{formInfo.unitName}}</span>
                </el-form-item>
                <el-form-item label="职务:" class="labeltext">
                    <span class="colorcss">{{formInfo.duties}}</span>
                </el-form-item>
                <el-form-item label="负责专业:" class="labeltext">
                <span>{{formInfo.specialtyName?formInfo.specialtyName.join(','):''}}</span>
            </el-form-item>
            </el-col>
        </el-row>
        <textBorder>客户标签</textBorder>
        <el-row :gutter="20" class="width100 mtop20 bbline mb30" >
            <el-col :span="24" class="mb20 ml40">
                <span class="tagitemcss" :style="colors[index]" :key="index" v-for="(item,index) in formInfo.tags">{{item}}</span>
            </el-col>
        </el-row>
        <textBorder>联系信息</textBorder>
        <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
            <el-col :span="12" class="mb10">
                <el-form-item label="电话:" class="labeltext mulitline">
                    <span>{{formInfo.phone}}</span>
                </el-form-item>
                <el-form-item label="微信:" class="labeltext mulitline">
                <span>{{formInfo.wechat}}</span>
            </el-form-item>
            </el-col>
            <el-col :span="12" class="mb10">  
                <el-form-item label="邮箱:" class="labeltext mulitline">
                <span>{{formInfo.mailbox}}</span>
                </el-form-item>
            </el-col>
        </el-row>
        <textBorder>数据画像</textBorder>
        <el-row :gutter="20" class="width100 mt10 pb5">
            <el-col :span="12" class="">
                <el-form-item label="性别:" class="labeltext">
                    <span>{{gender[formInfo.sex]}}</span>
                </el-form-item>
                <el-form-item label="年龄:" class="labeltext">
                    <span>{{formInfo.age}}</span>
                </el-form-item>
                <el-form-item label="民族:" class="labeltext">
                    <span>{{formInfo.nationName}}</span>
                </el-form-item>
                <el-form-item label="职称:" class="labeltext">
                    <span>{{formInfo.professionalTitles}}</span>
                </el-form-item>
                <el-form-item label="工作地址:" class="labeltext mulitline">
                    <span>{{formInfo.address}}</span>
                </el-form-item>
                <el-form-item label="家庭地址:" class="labeltext mulitline">
                <span>{{formInfo.homeAddress}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="mb10">  
                <el-form-item label="身份证号:" class="labeltext mulitline">
                    <span>{{formInfo.cardNumber}}</span>
                </el-form-item>
                <el-form-item label="籍贯:" class="labeltext mulitline">
                    <span>{{formInfo.nativePlace}}</span>
                </el-form-item>
                <el-form-item label="生日:" class="labeltext">
                    <span>{{formInfo.birthday}}</span>
                </el-form-item>
                <el-form-item label="职称时间:" class="labeltext">
                    <span>{{formInfo.professionalTime}}</span>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="width100 mt10 mb30 bbline">
            <el-col :span="24">
                <el-form-item label="爱好情况:" class="labeltext mulitline">
                    <span>{{formInfo.hobby}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="论文课题:" class="labeltext mulitline">
                    <span>{{formInfo.thesisTopic}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="研究方向:" class="labeltext mulitline">
                    <span>{{formInfo.researchDirection}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="车辆信息:" class="labeltext mulitline">
                            <span>{{formInfo.carInfo}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="车牌号:" class="labeltext mulitline">
                            <span>{{formInfo.carNumber}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>
            <el-col :span="24" >
                 <el-form-item label="婚恋情况:" class="labeltext mulitline">
                    <span>{{formInfo.marriage}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="在意的人:" class="labeltext mulitline">
                    <p class='carep' v-for="(item,index) in inputTags1" :key="index">{{item}}</p>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="mb10">
                <el-form-item label="子女情况:" class="labeltext mulitline">
                    <span>{{formInfo.children}}</span>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</el-drawer>
    
</template>

<script>
import textBorder from '@/components/common/textBorder.vue'
import {customerInfo } from "@/api/daily/index";
import { customerLevelColors } from "@/utils/dict";
export default {
    name:"customerMsg",
    props:{
        visible:{
            type:Boolean,
            default:false
        }
    },
    components:{
        textBorder
    },
    data(){
        return{
            customerLevelColors:customerLevelColors,
            inputTags1:[],
            gender:{
                1:'男',
                2:'女'
            },
            formInfo:{

            },
            colors:{

            },
        }
    },
    methods:{
        loadDataInfo(customerId){
            customerInfo(customerId).then((result) => {
                this.formInfo = result.data;
                this.formInfo.tags = this.formInfo.label && this.formInfo.label.split(',')
                this.inputTags1 = result.data.importPeople&& result.data.importPeople.split(',');
                this.formInfo.sex = this.formInfo.sex == 0 ? '' :this.formInfo.sex;
                this.formInfo.age = this.formInfo.age == 0 ? '' :this.formInfo.age;
            }).catch((err) => {
                
            });
        },
        handleClose(){
            this.$emit('closeCus')
        },
    }
}
</script>

<style scoped>
      .custord /deep/.el-drawer__header span{
            font-weight: 400;
            font-size: 20px;
            color: #333333;
            text-align: center;
            margin-bottom: 10px;
            position: relative;
            top: 4px;
        }
        .custord /deep/.el-drawer__header{
        }
.mtop20{
    margin-top: 20px;
}
.infoform{
    padding: 20px;
    margin-bottom: 40px;
    height: calc(95vh - 40px);
    overflow-y: auto !important;
}
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}
.mulitline /deep/.el-form-item__content{
    padding-top: 10px;
    line-height: 20px;
}
.carep{
    line-height: 30px;
  }
  .carep:first-child{
    margin-top: 5px;
  }
  .labeltext {
    margin-bottom: 5px !important;
  }
  .tagitemcss{
    font-size: 12px;
    padding:4px 8px;
    margin: 0px 4px;
    background-color: #DFEAFD;
    border-radius: 2px;
    color: #4285F4;
}
.mt3{
  margin-top: 3px;
}
.ml{
  margin-left: -80px;
}
</style>