<template>
  <el-dialog
    title="驳回"
    :visible.sync="visible"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form ref="form" :model="form" label-width="100px" :rules="rules">
      <el-form-item label="驳回原因：" prop="reason">
        <el-input
          type="textarea"
          rows="6"
          maxlength="300"
          show-word-limit
          v-model="form.reason"
          placeholder="请输入驳回原因"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'RejectDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        reason: [
          { required: true, message: '请输入驳回原因', trigger: 'blur' }
        ]
      },
      form: {
        reason: ''
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.form.reason = ''
    },
    submit() {
      // 处理提交逻辑
      if (!this.form.reason) {
        this.$message.error('请输入驳回原因')
        return
      }
      this.$emit('submit', this.form.reason)
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
