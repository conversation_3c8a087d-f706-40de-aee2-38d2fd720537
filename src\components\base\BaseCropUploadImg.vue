<!-- 上传头像带裁剪组件 -->
<template>
  <div>
    <div class="el-upload el-upload--picture-card upload-box"
    :style="defaultNumber"
        @click="handleSelctePic">
      <img v-if="myValue" :src="myValue"/>
      <i v-else class="el-icon-plus"></i>
    </div>
    <vue-crop-upload
      field="file"
      @crop-success="handleBeforeUploadCrop"
      @crop-upload-success="cropUploadSuccess"
      v-model="showCropLogo"
      :noCircle="true"
      :width="widthPx"
      :height="heightPx"
      :url="$uploadUrl"
      :langExt="langZh"
      :headers="headersos"
      img-format="jpg/png"
      ></vue-crop-upload>
  </div>
</template>

<script>
import vueCropUpload from 'vue-image-crop-upload';
export default {
  components: {
    vueCropUpload
  },
  props: {
    value: {
      type: String
    },
    widthPx: {
      type: Number,
      default: 80
    },
    heightPx: {
      type: Number,
      default: 80
    }
  },
  data() {
    return {
      langZh: {
        hint: '点击，或拖动图片至此处',
        loading: '正在上传……',
        noSupported: '浏览器不支持该功能，请使用IE10以上或其他现在浏览器！',
        success: '上传成功',
        fail: '图片上传失败',
        preview: '图片预览',
        btn: {
          off: '取消',
          close: '关闭',
          back: '上一步',
          save: '保存'
        },
        error: {
          onlyImg: '仅限图片格式',
          outOfSize: '单文件大小不能超过 ',
          lowestPx: '图片最低像素为（宽*高）：'
        }
      },
      myValue: '',
      headersos: {Authorization: 'Bearer ' + sessionStorage.getItem('__token__')},
      showCropLogo: false// logo裁剪
    }
  },
  computed: {
    defaultNumber() {
      return {
        width: `${this.widthPx}px`,
        height: `${this.heightPx}px`,
        lineHeight: `${this.heightPx}px`
      }
    }
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    handleSelctePic() {
      this.showCropLogo = !this.showCropLogo
    },
    // 图片截取完成事件（上传前)
    handleBeforeUploadCrop(imgDataUrl, field) {
      this.myValue = imgDataUrl
    },
    // 上传成功
    cropUploadSuccess(jsonData, field) {
      if (jsonData.code === 0) {
        this.myValue = jsonData.data.full_url;
        this.$emit('input', this.myValue)
      } else {
        this.$message.error('抱歉，上传失败，请稍候重试~');
      }
    }
  }
}
</script>
