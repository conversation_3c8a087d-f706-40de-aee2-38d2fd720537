<!-- 操作日志 -->
<template>
  <div class="mod-config app-container">
    <el-form :inline="true">
      <el-form-item>
        <el-input class="input-230" v-model="search.uAccountName" clearable placeholder="用户账号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          clearable
          class="input-230"
          v-model="time"
          type="datetimerange"
          value-format="yyyy-MM-dd"
          start-placeholder="起始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.platform" clearable class="input-230" placeholder="请选择平台类型">
          <el-option
            v-for="item in Dict.FLATFORM_TYPE"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.uOperation" clearable class="input-230" placeholder="操作项">
          <el-option
            v-for="item in Dict.ACTION_TYPE"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" v-isShow="'sf:business:operationlog:list'" @click="handleSearch"><i class="fa fa-search"></i>查询</el-button>
      </el-form-item>
    </el-form>
    <!-- table -->
    <el-table
      :data="dataList"
      border
      stripe
      :empty-text="$emptyFont"
      v-loading="dataListLoading"
      style="width: 100%;"
      element-loading-text="加载中..." element-loading-spinner="el-icon-loading">
      <el-table-column
        prop="uAccountName"
        header-align="center"
        align="center"
        label="姓名">
      </el-table-column>
      <el-table-column
        prop="platform"
        header-align="center"
        align="center"
        label="操作来源">
      </el-table-column>
      <el-table-column
        prop="uOperation"
        header-align="center"
        align="center"
        label="操作项">
      </el-table-column>
      <el-table-column
        prop="createTime"
        header-align="center"
        align="center"
        label="操作日期">
        <template slot-scope="scope"><span v-if="scope.row.createTime">{{ruleTime(scope.row.createTime)}}</span></template>
      </el-table-column>
      <el-table-column
        prop="ip"
        header-align="center"
        align="center"
        label="操作IP">
      </el-table-column>
      <el-table-column
        prop="region"
        header-align="center"
        align="center"
        label="IP区域">
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        label="操作内容">
        <template slot-scope="scope">
          <span>{{(scope.row.content).substring(0, 40)}}  ...</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button size="small" type="text" icon="el-icon-view" @click="handleShowCentent(scope.row)">详细内容</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      style="padding: 32px 16px 12px;background:#fff"
      background
      v-if="pagingObj.totalSum"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagingObj.currentPage"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="10"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagingObj.totalSum">
    </el-pagination>
    <!-- 操作内容 -->
    <BaseScrollLeft :isShow.sync="drawer.confirm" placement='right' :title="drawer.title">
      {{content}}
    </BaseScrollLeft>
  </div>
</template>

<script>
import BaseScrollLeft from '@/components/base/BaseScrollLeft.vue'
import pagination from '@/mixin/pagination'
export default {
  mixins: [pagination],
  components: {
    BaseScrollLeft
  },
  data () {
    return {
      drawer: {
        title: '操作内容',
        confirm: false
      },
      content: '',
      time: [],
      search: {},
      dataList: [],
      dataListLoading: true
    }
  },
  methods: {
    handleShowCentent (obj) {
      this.drawer.confirm = true;
      this.content = obj.content
    },
    handleSearch () {
      this.search.pageNum = 1;
      this.pagingObj.currentPage = 1;
      this.handleTimeParams(this.time, 'startTime', 'endTime')
      this.getDataList(this.search)
    },
    async getDataList (obj) {
      let res = await this.$axios.get('/sf/business/operationlog/list', { params: obj })
      if (res.status === 0) {
        this.dataListLoading = false;
        this.dataList = res.data;
        this.pagingObj.totalSum = res.page.total;
      }
    }
  },
  created () {
    this.search.pageNum = '1';
    this.search.pageSize = '10';
    this.getDataList(this.search);
  }
}
</script>
