<template>
  <div>
    <el-table class="detailList mytable"  :data="tableData" style="width: 100%" height="590px" v-loading="isLoading">
      <el-table-column
        prop="opportunityName"
        label="销售机会"
        align="center"
        show-overflow-tooltip
        min-width="200px"
        >
      </el-table-column>
      <el-table-column
        prop="customerName"
        label="客户"
        align="center"
        min-width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="unitName"
        label="客户单位"
        align="center"
        min-width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="estimatedAmount"
        label="预计金额（万元）"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="stage"
        align="center"
        width="167px"
        label="机会阶段">
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page 
    :currentPage="pageBean.pageNum" 
    :total="total" 
    :pageSize="pageBean.pageSize" 
    @updatePageNum="handleCurrentChange"
    ></page>
  </div>
</template>

<script>
import page from '../../common/page.vue';
import nolist from '../../common/nolist.vue';
import { opportunityList } from "@/api/clientMaintenance/opportunity";
  export default {
    components:{
      page,
      nolist
    },
    data(){
      return{
        isLoading:false,
        tableData:[],
        total:0,
        pageBean:{
          unitId:this.$route.query.id,
          pageNum:1,
          pageSize:10,
        }
      }
    },
    created(){

    },
    methods:{
      loadData(){
        this.isLoading = true;
        opportunityList(this.pageBean).then((result) => {
          this.tableData = result.data;
          this.total = result.page.total;
          this.isLoading = false;
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      onDetail(data){
        console.log(data)
        this.$router.push({
          path:"/clientMaintenance/salesLead/detail",
          query:{
            id:data.name
          }
        })
      },
      handleCurrentChange(page){
        this.pageBean.pageNum = page;
        this.loadData();
      }
    }
  }
</script>

<style lang="scss" scoped>
.tabbtn{
  font-size: 14px;
}
.bbtn,.bbtn:hover,.bbtn:focus{
  color: #4285F4;
}
</style>