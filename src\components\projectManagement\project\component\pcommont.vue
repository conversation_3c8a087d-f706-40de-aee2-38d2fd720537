<template>
  <div class="commentp">
    <div class="cdiv">
      <div v-if="commentArr.length == 0 " class="nolist">
        <img class="noimg" src="../../../../assets/no.png" alt="">
        <p>暂无记录~</p>
      </div>
      <comment2 @reply="reply" v-for="(item,index) in commentArr" :key="index" :item="item" @deleteAction="deleteAction"></comment2>
    </div>
    <div class="fiex">
      <send ref="send" @save="save"></send>
    </div>
    <replyDialog ref="replyDialog" :visible.sync="replayDialogVisible" :title="replayTitle" @updateVisible="updateReplayVisible" @replyData="replyData"></replyDialog>
  </div>
</template>

<script>
import comment2 from '@/components/common/comment2'
import send from '@/components/common/send'
import replyDialog from '@/components/common/replyDialog'
import {messageList,messageSave,messageDelete} from '@/api/project/index'
  export default {
    props:{
      proId:{
        type:String,
        default:''
      }
    },
    components:{
      comment2,
      send,
      replyDialog
    },
    data(){
      return {
        replayDialogVisible:false,
        replayTitle:'',
        commentArr:[],
        parentId:''
      }
    },
    watch:{
      proId:{
        immediate:true,
        handler(newValue){
          if(newValue){
            this.commentListData()
          }
        }
      }
    },
    created(){
      
    },
    methods:{
      reply(data){
          this.parentId = data.id,
          this.updateReplayVisible(true);
          this.replayTitle = `回复@${data.name}`
      },
      
      updateReplayVisible(val){
        this.replayDialogVisible = val;
      },
      replyData(data){
        this.save(data);
        this.$refs.replyDialog.closeAction();
      },
      deleteAction(commentId){
        messageDelete({id:commentId}).then((result) => {
          if (result.data) {
            this.$message({
              type:'success',
              message:'删除成功'
            })
            this.commentListData()
          } else {
            this.$message({
              type:'error',
              message:'删除失败'
            })
          }
        }).catch((err) => {
          
        });
      },
      save(value){
          let params = {
            projectId:this.proId,
            message:value,
            parentId:this.parentId || 0
          }
          messageSave(params).then(res=>{
                if(res.status == 0){
                    this.commentListData()
                    this.$refs.send.clearValue()
                }
                this.parentId = 0;
          }).catch((err) => {
            this.parentId = 0;
          });
      },
      commentListData(){
        messageList({projectId:this.proId}).then(res=>{
                if(res.status == 0){
                      if(res.data){
                        this.commentArr = res.data
                      }
                }
            })
          },
    }
  }
</script>

<style lang="scss" scoped>
.nolist{
  height: calc(100vh - 283px);
  text-align: center;
  overflow: hidden;
}
.noimg{
  margin-top: 180px;
}
.cdiv{
}
.fiex{
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  background: #fff;
}
.commentp{
  padding-bottom: 44px;
  height: calc(100vh - 180px);
  overflow-x:hidden;
}

</style>