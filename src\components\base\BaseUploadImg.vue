<!-- 上传图片 -->
<template>
  <div class="base-upimg-button">
    <div @click="imgClick" class="baseimgbutton"></div>
  </div>
</template>

<script>
export default {
  data () {
    return {

    }
  },
  methods: {
    imgClick () {
      let input = document.createElement('input');
      input.type = 'file';
      input.name = '';
      input.accept = 'image/jpeg,image/png,image/jpg,image/gif,application/pdf';
      input.onchange = this.onFileChange;
      input.click();
    },
    // 选择上传图片
    onFileChange (e) {
      // let self = this;
      let fileInput = e.target;
      if (fileInput.files.length === 0) { return };
      if (fileInput.files[0].size > 1024 * 1024) {
        this.$alert('图片不能大于1M', '图片尺寸过大', {
          confirmButtonText: '确定',
          type: 'warning'
        })
        return
      }
      let data = new FormData();
      this.$message('图片上传中...')
      data.append('file', fileInput.files[0]);
      this.$axios.post(this.$uploadUrl, data)
        .then((res) => {
          if (res.status === 0) {
            this.$message({
              message: '上传成功',
              type: 'success'
            })
            this.$emit('handleGetImg', res.data)
          }
        }).catch((err) => {
          console.log(err)
        })
    }
  }
}
</script>

<style style="scss" scoped>
.baseimgbutton{
  width: 100%;
  height: 100%;
}
</style>
