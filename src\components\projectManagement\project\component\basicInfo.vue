<template>
  <div>
    <el-form class="infoform" ref="form" :model="form" label-width="120px">
          <img @click="collect(0)" v-if="form.top" class="zhiding cs" src="../../../../assets/img/zhiding_icon.png" alt=""> 
          <img @click="collect(1)" v-else class="zhiding cs" src="../../../../assets/img/zhidingd.png" alt=""> 
          <textBorder>基本信息</textBorder>
          <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
            <el-col :span="12">
              <el-form-item label="项目名称：" class="labeltext">
                 <span>{{ form.projectName }}</span>
              </el-form-item>
              <el-form-item label="客户：" class="labeltext">
                <span>{{ form.customerName }}</span>
              </el-form-item>
              <el-form-item label="开始时间：" class="labeltext">
                <span>{{ form.beginTime }}</span>
              </el-form-item>
              <el-form-item label="关联合同：" class="labeltext">
                <span>{{ form.contractName }}</span>
              </el-form-item>
              <el-form-item label="项目类型：" class="labeltext">
                <span>{{ form.projectTypeName }}--{{ form.projectTypeSubName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">  
              <el-form-item label="项目状态：" class="labeltext">
                 <span>{{ form.projectStatusName }}</span>
              </el-form-item>
              <el-form-item label="客户单位：" class="labeltext">
                 <span class="column_blue">{{ form.unitName }}</span>
              </el-form-item>
              <el-form-item label="结束时间：" class="labeltext">
                <span>{{ form.endTime }}</span>
              </el-form-item>
              <el-form-item label="项目金额(万元)：" class="labeltext">
                <span>{{ form.projectAmount }}</span>
              </el-form-item>
            </el-col>
          </el-row>


          <textBorder>补充信息</textBorder>
          <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
            <el-col :span="24">
              <el-form-item label="附件：" class="labeltext">
                <pdfview type="1" :pdfArr="form.fileInfoList" ></pdfview>
              </el-form-item>
               <el-form-item label="备注：" class="labeltext">
                <span>{{ form.notes }}</span>
              </el-form-item>
            </el-col>
          </el-row>

            <textBorder>负责与协作</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                <el-form-item label="负责人：" class="labeltext">
                    <span>{{form.chargePersonName}}</span>
                </el-form-item>
                </el-col>
                <el-col :span="12">  
                <el-form-item label="协作人：" class="labeltext">
                    <span>{{ form.collaboratorName }}</span>
                </el-form-item>
                </el-col>
            </el-row>

            <textBorder>系统信息</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                <el-form-item label="创建人：" class="labeltext">
                    <span>{{form.createByName  }}</span>
                </el-form-item>
                <el-form-item label="创建时间：" class="labeltext">
                    <span>{{ form.createTime }}</span>
                </el-form-item>
                </el-col>
                <el-col :span="12">  
                <el-form-item label="最后修改人：" class="labeltext">
                    <span>{{ form.modifyByName }}</span>
                </el-form-item>
                <el-form-item label="最后修改时间：" class="labeltext">
                    <span>{{ form.modifyTime }}</span>
                </el-form-item>
                </el-col>
            </el-row>
    </el-form>
  </div>
</template>

<script>
  import textBorder from '@/components/common/textBorder.vue'
  import {proInfo,projectUpdate} from '@/api/project/index'
  import pdfview from '@/components/common/pdfview.vue'
  export default {
    props:{
      proId:{
        type:String,
        default:''
      },
      form:{
        type:Object,
        default:()=>({})
      }
    },
    data(){
     return {
     }
    },
    components:{
      textBorder,
      pdfview
    },
    created(){

    },
    methods:{
      collect(boolean){
            projectUpdate({id:this.proId,top:boolean}).then(res=>{
                if(res.status==0){
                  boolean?this.msgSuccess('置顶成功'):this.msgSuccess('取消成功')
                  this.form.top = boolean
                  this.$emit('loadData')
                }
            })
      }
    }
  }
</script>

<style lang="scss" scoped>
/deep/ .guancontent {
    margin-left: -18px !important;
    margin: 0;
    margin-top: 10px;
}

.pb12{
    padding-bottom: 12px;
}

</style>
<style scoped>
.cs{
  width: 24px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10;
}
.pdfdiv{
  cursor: pointer;
  display: inline-block;
  height: 24px;
  border-radius: 4px 4px 4px 4px;
  border: 1px dashed #4285F4;
  line-height: 4px;
  padding: 10px;
  box-sizing: border-box;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
}
.lh /deep/.el-form-item__content{
    line-height: 18px !important;
    padding: 0;
    padding-top: 12px;
}
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}
.infoform{
  position: relative;
}

</style>