<template>
    <div>
        <div>
            <back>{{bTitle}}</back>
        </div>
        <div class="mainbg">
            <el-form :model="form" ref="addform" :rules="rules" class="addfcss" label-width="130px">
                <el-row :gutter="0" type="flex" justify="start">
                    <el-col :span="8">
                        <el-form-item prop="customerId" label="跟进客户：">
                            <div class="unitbtn" :class="{selectedCss:form.customerName}" @click="chooseCustomer">
                                {{form.customerName ? form.customerName :'请选择客户'}} <i
                                    class=" rcenter el-icon-arrow-down" /></div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="0">
                    <el-col :span="14">
                        <el-form-item prop="description" label="跟进与拜访说明:">
                            <el-input maxlength="300" show-word-limit v-model="form.description" class="definput"
                                type="textarea" rows="4" placeholder="请输入跟进内容"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="0">
                    <el-col :span="14">
                        <el-form-item prop="isAssist" label="关键信息：">
                            <span class="checktext deffont">
                                是否需要协助
                                <span class="radiocss">
                                    <el-radio v-model="form.isAssist" :label="1">是</el-radio>
                                    <el-radio v-model="form.isAssist" :label="0">否</el-radio>
                                </span>
                            </span>
                            <el-input v-if="form.isAssist == 1" v-model="form.assistContent" class="definput"
                                type="textarea" rows="4" placeholder="请输入关键信息或需要帮助的内容"></el-input>
                        </el-form-item>

                    </el-col>
                </el-row>
                <el-row :gutter="0" type="flex" justify="start">
                    <el-col :span="24">
                        <el-form-item width="800" class="mb10" label="图片或视频：">
                            <upload ref="imgRef" accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PBG,.GIF,.mp4"
                                :fileList="fileList" @submitImg="submitImg"></upload>
                        </el-form-item>
                        <el-form-item class="mb0" label="录音：">
                            <upload2 ref="mp3ref" @submitImg="submitMp3" accept=".mp3" :fileList="fileListF">
                                <span class="studiocss">
                                    <img src="../../../assets/img/studio_icon.png">
                                    <span class="uploadtext  deffont">点击选择录音文件</span>
                                </span>
                                <template slot="ptip">
                                    <p>只能上传mp3文件</p>
                                </template>
                            </upload2>
                        </el-form-item>
                        <el-form-item class="mb10" label="附件：">
                            <upload2 :fileMaxSize='100' ref="upload2" @submitImg="submitPdf" accept=".pdf,.doc,.docx,.xlsx"
                                :fileList="fileListPdf">
                                <span class="studiocss">
                                    <img src="../../../assets/img/file_icon.png">
                                    <span class="uploadtext  deffont">点击上传附件</span>
                                </span>
                                <template slot="ptip">
                                    <p>只能上传pdf,doc,docx,ppt,pptx,xlsx文件</p>
                                </template>
                            </upload2>
                        </el-form-item>
                        <el-form-item prop="assistType" label="跟进类型：">
                            <el-select clearable="" class="definput sw" popper-class="removescrollbar"
                                v-model="form.assistType" placeholder="请选择">
                                <el-option v-for="item in types" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="下次跟进提醒：">
                            <el-select clearable="" class="definput sw" popper-class="removescrollbar"
                                v-model="form.isRemind" @change="handleRemindChange" placeholder="请选择">
                                <el-option v-for="item in tixing" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="form.isRemind == 1" label="提醒时间：">
                            <el-date-picker   popper-class="datepickerPopperClass" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" v-model="form.remindTime" type="datetime" class=" definput wid100" placeholder="选择日期时间"
                            :picker-options="{ disabledDate: disabledDate }"
                                >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item v-if="form.isRemind == 1" label="提醒人：">
                            <el-tag
                                class="tagcss"
                                size="small"
                                v-for="item in remindUserList"
                                closable
                                @close="handleRemindUserClose(item)"
                                :key="item.id"
                            >{{ item.name }}</el-tag>
                            <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickSelectRemindUser">
                                点击选择提醒人</el-button>
                        </el-form-item>
                        <el-form-item label="关联机会：">
                            <div class="unitbtn sw" :class="{selectedCss:form.opportunityName}" @click="chooseChance">
                                {{form.opportunityName ? form.opportunityName :'请选择'}} <i @click.stop="closeOpp"
                                    v-show="form.opportunityName" class="rcenter el-icon-close"></i> <i
                                    v-show="!form.opportunityName" class="rcenter el-icon-arrow-down" /> </div>
                        </el-form-item>
                        <el-form-item label="关联合同：">
                            <div class="unitbtn sw" :class="{selectedCss:form.contractName}" @click="chooseContract">
                                {{form.contractName ? form.contractName :'请选择'}} <i @click.stop="closeCon"
                                    v-show="form.contractName" class="rcenter el-icon-close"></i> <i
                                    v-show="!form.contractName" class=" rcenter el-icon-arrow-down" /></div>
                        </el-form-item>
                        <el-form-item label="关联项目：">
                            <div class="unitbtn sw" :class="{selectedCss:form.projectName}" @click="chooseProject">
                                {{form.projectName ? form.projectName :'请选择'}} <i @click.stop="closePro"
                                    v-show="form.projectName" class="rcenter el-icon-close"></i> <i
                                    v-show="!form.projectName" class=" rcenter el-icon-arrow-down" /></div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div class="pt20 btncenter">
                    <el-form-item>
                        <el-button class="btn_h42 wid98" type="primary" @click="saveButton">保存</el-button>
                    </el-form-item>
                </div>
            </el-form>
        </div>


        <!-- 客户选择 -->
        <cuatomerDialog ref="customer" :visible.sync="customerDialogVisible" @updateVisible="updateVisible"
            @updateCustomer="updateCustomer">
        </cuatomerDialog>


        <!-- 机会选择 -->
        <chanceDialog ref="changeRef" :visible.sync="chanceVisible" @updateVisible="updateChanceVisible"
            @updateData="updateChanceData"></chanceDialog>



        <!-- 合同选择 -->
        <contractDialog ref="contractRef" :visible.sync="contractVisible" @updateVisible="updateContractVisible"
            @updateData="updateContractData"></contractDialog>



        <!-- 项目选择 -->
        <projectDialog ref="projectRef" :visible.sync="projectVisible" @updateVisible="updateProjectVisible"
            @updateData="updateProjectData"></projectDialog>
        <!-- 部门验证组件 -->
        <verifyDeparment
          ref="verifyDeparment"
          @submit="submitWithDepartment"
        ></verifyDeparment>

        <!-- 提醒人选择 -->
        <systemDialog ref="systemdialog" :name="dialogName" :multipleNum="multipleNum" :visible.sync="dialogVisible"
            @updateVisible="updateSystemVisible" @submitData="submitData">
        </systemDialog>
    </div>
</template>

<script>
    import back from '../../common/back.vue';
    import cuatomerDialog from '../../common/customerDialog.vue';
    import chanceDialog from '../../common/chanceDialog.vue';
    import contractDialog from '../../common/contractDialog.vue';
    import projectDialog from '../../common/projectDialog.vue';
    import systemDialog from '../../common/systemDialog.vue';


    import upload from '@/components/common/upload.vue'
    import upload2 from '@/components/common/upload2.vue'


    import { getFileTypeNum, getFileType, addSnop } from '@/utils/tools'

    import { visitSave, visitInfo, visitUpdate } from '@/api/visit/index'
    import { opportunityInfo } from '@/api/clientMaintenance/opportunity'
    import VerifyDeparment from '@/components/common/verifyDeparment.vue';

    export default {
        components: {
            back,
            cuatomerDialog,
            chanceDialog,
            contractDialog,
            projectDialog,
            upload,
            upload2,
            VerifyDeparment,
            systemDialog
        },
        data() {
            return {
                fileListF: [],
                fileList: [],
                fileListPdf: [],
                projectVisible: false,
                chanceVisible: false,
                contractVisible: false,
                customerDialogVisible: false,
                dialogVisible: false,
                remindUserList: [],
                dialogName: '',
                multipleNum: 30,
                isSmall: window.screen.availWidth < 1500 ? true : false,
                dialogName: '',
                inputValue: '',
                isDeleteTag: false,
                isChooseCustomer: false,
                form: {
                    projectId: '',
                    contractId: '',
                    chanceName: "",
                    chanceId: "",
                    customerName: '',
                    customerId: "",
                    description: "",
                    assistContent: "",
                    assistType: '',
                    isRemind: 0,
                    remindTime: '',
                    isAssist: 0,
                    fileInfoList: [],
                    opportunityId: '',
                    opportunityName: '',
                    contractName: '',
                    projectName: '',
                    remindUser: '',
                    remindUserName: '',
                },
                rules:{
                    description: [
                    { required: true, message: "请输入跟进与拜访内容", trigger: "blur" },
                    {
                        min: 1,
                        max: 300,
                        message: "长度在 1 到 300 个字符",
                        trigger: "blur",
                    },
                    ],
                    isAssist: [
                        { required: true, message: "请选择关键信息", trigger: "change" },
                    ],
                    assistType: [
                        { required: true, message: "请选择跟进类型", trigger: "change" },
                    ],
                    customerId: [
                        { required: true, message: "请选择跟进客户", trigger: "change" },
                    ],
                },
                types: [
                    {
                        label: "电话拜访",
                        value: 1,
                    },
                    {
                        label: "线上拜访",
                        value: 2,
                    },
                    {
                        label: "实地拜访",
                        value: 3,
                    },
                ],
                tixing: [
                    {
                        label: "提醒",
                        value: 1,
                    },
                    {
                        label: "不提醒",
                        value: 0,
                    },
                ],
                unitDialogVisible: false,
                bTitle: '新建跟进与拜访'
            }
        },
        created() {
            if (this.$route.query.customerId) {
                this.form.customerId = this.$route.query.customerId;
                this.form.customerName = this.$route.query.customerName;
                this.isChooseCustomer = true;
            }
            if (this.$route.query.id && !this.$route.query.opportunityId) {
                this.getvisitInfo()
                this.bTitle = '编辑跟进与拜访'
            }
            if (this.$route.query.opportunityId) {
                this.loadOpportunityInfo(this.$route.query.opportunityId)
            }
        },

        methods: {
            loadOpportunityInfo(opportunityId) {
                opportunityInfo(opportunityId)
                    .then((result) => {
                        const opportunity = result.data
                        this.form.opportunityId = opportunity.id
                        this.form.opportunityName = opportunity.opportunityName
                        this.form.customerId = opportunity.customerId
                        this.form.customerName = opportunity.customerName
                        this.isChooseCustomer = true
                    })

            },
            disabledDate(time) {
                var date = new Date()
                return time < new Date(date.getFullYear(),date.getMonth(),date.getDate(),0,0,0);
            },
            closeOpp() {
                this.form.opportunityId = ''
                this.form.opportunityName = ''
            },
            closeCon() {
                this.form.contractId = ''
                this.form.contractName = ''
            },
            closePro() {
                this.form.projectId = ''
                this.form.projectName = ''
            },
            getvisitInfo() {
                visitInfo(this.$route.query.id).then(res => {
                    if (res.status == 0) {
                        this.form = res.data
                        if (res.data.fileList.length > 0) {
                            this.fileListPdf = res.data.fileList
                            this.fileListPdf.forEach(item => item.name = item.fileName)
                            this.$refs.upload2.setFileList(this.fileListPdf)
                        }
                        if (res.data.soundList.length > 0) {
                            this.fileListF = res.data.soundList
                            this.fileListF.forEach(item => item.name = item.fileName)
                            this.$refs.mp3ref.setFileList(this.fileListF)
                        }
                        if (res.data.picList.length > 0 || res.data.videoList > 0) {
                            this.fileList = [...res.data.picList, ...res.data.videoList]
                            this.fileList.forEach(item => {
                                item.name = item.fileName
                                item.statsu = 'success',
                                    item._url = getFileType(item.url) == 'image' ? item.url : addSnop(item.url)
                            })
                            this.$refs.imgRef.setFileList(this.fileList)
                        }
                        // 处理提醒人数据
                        if (res.data.remindUser && res.data.remindUserName) {
                            var ids = res.data.remindUser.split(',')
                            var names = res.data.remindUserName.split(',')
                            ids.forEach((item, index) => {
                                this.remindUserList.push({ id: item, name: names[index] })
                            })
                        }
                        this.form.fileInfoList = []
                    }
                })
            },
            arrForEach(arrFileList) {
                arrFileList.forEach(item => {
                    this.form.fileInfoList.push({
                        ...item,
                        fileType: getFileTypeNum(item.url)
                    })
                })
            },
            saveButton() {
                this.$refs.addform.validate((valid) => {
                    if (valid) {
                      if (this.form.isAssist == 1) {
                          if (this.form.assistContent == '') {
                              this.msgError('请填写关键信息')
                              return
                          }
                      }
                      if (this.fileList.length > 0) {
                          this.arrForEach(this.fileList)
                      }
                      if (this.fileListF.length > 0) {
                          this.arrForEach(this.fileListF)
                      }
                      if (this.fileListPdf.length > 0) {
                          this.arrForEach(this.fileListPdf)
                      }
                      if (this.$route.query.id) {
                          visitUpdate(this.form).then(res => {
                              if (res.status == 0) {
                                  this.msgSuccess('修改成功')
                                  this.$router.back();
                              }
                          })
                      } else {
                        this.$refs.verifyDeparment.verify()
                      }


                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });


            },
            submitWithDepartment(departmentId){
              this.form.createDepartmentId = departmentId;
              visitSave(this.form).then(res => {
                  if (res.status == 0) {
                      this.msgSuccess('添加成功')
                      this.$router.back();
                  }
              })

            },
            submitImg(fileList) {
                this.fileList = fileList
            },
            submitMp3(fileList) {
                this.fileListF = fileList
            },
            submitPdf(fileList) {
                this.fileListPdf = fileList
            },
            chooseProject() {
                let params = {
                    id: this.form.projectId || '',
                    methodName: 'list',
                    className: 'ProjectController',
                    contractId: ''
                }
                this.$refs.projectRef.selectProjectData(params)
                this.projectVisible = true;
            },
            updateProjectVisible(val) {
                this.projectVisible = val;
            },
            updateProjectData(data) {
                this.form.projectId = data.id;
                this.form.projectName = data.projectName;
            },
            chooseChance() {
                let params = {
                    id: this.form.opportunityId || '',
                    methodName: 'list',
                    className: 'VisitController',
                }
                this.$refs.changeRef.selectChanceData(params)
                this.chanceVisible = true;
            },
            updateChanceVisible(val) {
                this.chanceVisible = val;
            },
            updateChanceData(data) {
                this.form.opportunityId = data.id;
                this.form.opportunityName = data.opportunityName;
            },
            chooseContract() {
                let params = {
                    id: this.form.contractId || '',
                    methodName: 'list',
                    className: 'VisitController',
                    opportunityId: ''
                }
                this.$refs.contractRef.selectContractData(params)
                this.contractVisible = true;
            },
            updateContractData(data) {
                this.form.contractName = data.contractTitle;
                this.form.contractId = data.id;
            },
            updateContractVisible(val) {
                this.contractVisible = val;
            },
            updateCustomer(data) {
                this.form.customerName = data.customerName;
                this.form.customerId = data.id;
                this.$refs.addform.validateField('customerId')
            },
            chooseCustomer() {
                if (this.isChooseCustomer) {
                    return
                }
                this.customerDialogVisible = true;
                this.$refs.customer.selectCustomerData({ visitId: '', id: this.form.customerId, className: 'VisitController' })
            },
            updateVisible(val) {
                this.customerDialogVisible = val;
            },
            // 选择提醒人
            clickSelectRemindUser() {
                this.dialogName = '选择提醒人';
                this.multipleNum = 30;
                this.$refs.systemdialog.loadData();
                this.$refs.systemdialog.updateWorksId(this.remindUserList);
                this.dialogVisible = true;
            },
            updateSystemVisible(value) {
                this.dialogVisible = value;
            },
            // 处理提醒人数据
            submitData(data, type) {
                if (type === '选择提醒人') {
                    this.remindUserData(data);
                    this.remindUserList = data;
                }
                this.updateSystemVisible(false);
            },
            remindUserData(list) {
                var ids = [];
                var names = [];
                list.forEach(item => {
                    ids.push(item.id);
                    names.push(item.name);
                });
                this.form.remindUser = ids.join(',');
                this.form.remindUserName = names.join(',');
            },
            // 删除提醒人标签
            handleRemindUserClose(tag) {
                this.remindUserList.splice(this.remindUserList.indexOf(tag), 1);
                this.remindUserData(this.remindUserList);
            },
            handleRemindChange(value) {
                if (value === 0) {
                    this.form.remindTime = '';
                    this.form.remindUser = '';
                    this.form.remindUserName = '';
                    this.remindUserList = [];
                }
            },
        }
    }
</script>
<style lang="scss" scoped>
    .unitbtn {
        position: relative;
    }

    .sw {
        width: 224px !important;
    }

    .radiocss {
        margin-left: 20px;
    }

    .mb0 {
        margin-bottom: 0px !important;
    }

    .mb10 {
        margin-bottom: 10px !important;
    }

    .mt10 {
        margin-top: 10px !important;
    }

    .studiocss img {
        width: 16px;
        height: 16px;
        margin-right: 3px;
        margin-top: -3px;
    }

    .checkcss /deep/.el-checkbox__inner {
        border-radius: 8px;
        width: 16px;
        height: 16px;
    }

    .checkcss /deep/.el-checkbox__inner::after {
        left: 5px;
        top: 2px;
    }

    .ml12 {
        margin-left: 12px;
    }

    .width100 {
        width: 100%;
    }

    .wid98 {
        width: 98px;
    }

    .uploadtext {
        color: #4285F4;
        cursor: pointer;
    }

    .uploadcss {
        width: 88px;
        height: 88px;
        line-height: 24px;
        background: #F5F5F5;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px dashed #CCCCCC;
    }

    .uploadcss i {
        margin-top: 20px;
        font-size: 24px;
        color: #4285F4;
    }

    .rcenter {
        position: absolute;
        right: 10px;
        line-height: 34px;
        font-size: 14px;
        color: #c0c4cc;
        top: 0;
        z-index: 10;
    }

    .btncenter {
        text-align: center;
    }

    .quanxiancss /deep/.el-form-item__label {
        margin-left: -7px;
        width: 125px !important;
    }

    .addresscss {
        max-height: 68px;
    }

    .addfcss /deep/ .el-upload .el-upload__list {
        line-height: 34px;
        min-height: 34px;
    }

    .mainbg {
        margin: 16px 0px;
        padding: 20px 16px;
        background-color: white;
        border-radius: 8px;
    }

    .pd0 {
        padding-right: 0px !important;
    }

    .tagcss {
        margin-left: 8px;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #4285f4;
        background-color: #F0F7FF;
        border: 1px solid #4285f4;
        margin-right: 8px;
        margin-bottom: 8px;
    }

    .tagcss /deep/ .el-icon-close {
        width: 12px;
        height: 12px;
        line-height: 12px;
        background-color: #4285f4;
        color: white;
    }

    .bBtn {
        color: #4285F4;
        font-size: 12px;
    }
</style>
<style>

</style>
