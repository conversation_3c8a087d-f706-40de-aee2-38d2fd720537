<template>
    <el-drawer
    :append-to-body="true"
    center
    class="mydrawer ed"
    title="回款详情"
    size="50%"
    :before-close="beforeClose"
    :visible.sync="drawer"
    direction="rtl">
    <div class="concss">
        <el-form class="infoform" ref="form" label-width="120px">
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" type="flex" justify="center">
                <el-col :span="11">
                    <el-form-item label="回款金额：" class="labeltext">
                        <span>{{form.returnAmount}}万元</span>
                    </el-form-item>
                    <el-form-item label="实际回款日期：" class="labeltext">
                        <span>{{form.returnTime}}</span>
                    </el-form-item>
                    <el-form-item label="是否预收：" class="labeltext">
                        <span>{{typeToName[form.isAdvance]}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="11">  
                    <el-form-item label="币种：" class="labeltext">
                        <span>{{form.currencyName}}</span>
                    </el-form-item>
                    <el-form-item label="付款方式：" class="labeltext">
                        <span>{{paymentWays[form.paymentWay]}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline"  type="flex" justify="center">
                <el-col :span="11">
                <el-form-item label="收款账户：" class="labeltext">
                    <span>{{form.collectionAccount}}</span>
                </el-form-item>
                </el-col>
                <el-col :span="11">  
                <el-form-item label="交易流水号：" class="labeltext">
                    <span>{{form.transactionNumber}}</span>
                </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 "  type="flex" justify="center">
                <el-col :span="11">
                <el-form-item label="操作人：" class="labeltext">
                    <span>{{form.createByName}}</span>
                </el-form-item>
                </el-col>
                <el-col :span="11">  
                <el-form-item label="业务时间：" class="labeltext">
                    <span>{{form.createTime}}</span>
                </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
    </el-drawer>
</template>

<script>
import { contractreturnInfo  } from "@/api/contract/index";
import {stageReturnInfo} from '@/api/stage/index'
export default {
    props:{
        visible:{
            type:Boolean,
            default:false
        }
    },
    data(){
        return{
            paymentWays:{
                1:"现金",
                2:"银行转账",
                3:'微信转账',
                4:'支付宝转账',
                5:'其他'
            },
            typeToName:{
                1:'是',
                0:'否'
            },
            form:{
                id:'',
                contractId:"",
                refundAmount:"",
                currency:"",
                paymentWay:'',
                actualReturnDay:"",
                refundWay:"",
                refundAccount:"",
                collectionAccount:"",
                transactionNumber:"",
                refundReason:"",
                createTime:"",
                isAdvance:''
            },
        }
    },
    computed:{
        drawer:{
            get(){
                return this.visible
            },
            set(val){
                this.$emit('updateVisible',val)
            }
        }
    },
    methods:{
        beforeClose(){
            this.drawer = false;
            var keys = Object.keys(this.form);
            keys.forEach(element => {
                this.form[element] = ''
            });
        },
        async loadData(id,type){
            let res
            if(type){
                    res =  await stageReturnInfo(id)
                    if(res.status == 0){
                        this.form = res.data;
                    }
            }else{
                    res =  await contractreturnInfo(id)
                    if(res.status == 0){
                        this.form = res.data;
                    }
            }
        },
    }
}
</script>

<style scoped>
.pb12{
    padding-bottom: 12px;
}
.lh /deep/.el-form-item__content{
    line-height: 18px !important;
    padding: 0;
    padding-top: 12px;
}
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}
.concss{
    padding: 0px 20px;
    padding-top: 30px;
}
.mydrawer /deep/.el-drawer__header{
    text-align: center;
    color: #333333;
}
</style>