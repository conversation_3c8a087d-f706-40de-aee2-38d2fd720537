<template>
  <div>
    <div>
      <back>用书信息</back>
    </div>
    <el-card v-loading="isLoading" class="mt10">
      <el-form label-width="85px" class="myform">
        <div class="flexc">
          <el-form-item label="" label-width="0px">
            <el-input
              class="definput icss"
              v-model="pageBean.materialName"
              clearable
              placeholder="请输入教材名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="用书专业：">
            <el-select
              class="definput umajor"
              popper-class="removescrollbar"
              clearable
              v-model="pageBean.useBookSpecialty"
              placeholder="请选择"
            >
              <el-option
                v-for="item in majorList"
                :key="item.id"
                :label="item.specialtyName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用书时间：">
            <el-input
              class="definput cpdding utime"
              v-model="pageBean.useBookYear"
              clearable
              placeholder="例:2023年春季"
            ></el-input>
          </el-form-item>

          <el-form-item label="创建人：">
            <el-input
              class="definput icss"
              v-model="pageBean.operationName"
              clearable
              placeholder="请输入"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="创建人部门：" label-width="120px">
            <div class="unitbtn mt definput" @click="chooseDepartment">
              <span v-if="pageBean.operationDepartmentIds" class="deffont">{{
                searchdepartmentNames
              }}</span>
              <span v-else>请选择</span>
              <i
                v-if="searchdepartmentNames"
                @click.stop="cleardata"
                class="rcenter el-icon-circle-close"
              />
              <i v-else class="rcenter el-icon-arrow-down" />
            </div>
          </el-form-item>
          <el-button
            class="defaultbtn1 mt"
            icon="el-icon-search"
            type="primary"
            @click="searchAction"
            >搜索</el-button
          >
          <div class="bucss">
            <el-button
              class="defaultbtn1 ml20"
              icon="el-icon-my-download"
              type="primary"
              :loading="isDownload"
              @click="downloadAction"
              >下载模版</el-button
            >
            <el-upload
              class="ml20"
              :action="getUrl"
              :show-file-list="false"
              :before-upload="beforeUpload"
              :on-success="handleAvatarSuccess"
              :on-error="handleError"
              :headers="headers"
              :data="fileData"
              accept=".xlsx"
            >
              <el-button
                class="defaultbtn"
                icon="el-icon-my-inport"
                type="primary"
                :loading="isImport"
                >导入用书信息</el-button
              >
            </el-upload>
            <el-button
              class="defaultbtn1 fr ml20"
              icon="el-icon-plus"
              type="primary"
              @click="addInfo"
              >添加</el-button
            >
          </div>
        </div>
      </el-form>
      <el-table
        class="customertable mytable tootiptable"
        height="590px"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          prop="materialName"
          label="教材名称"
          align="center"
          min-width="167px"
        >
          <template slot-scope="scope">
            <span class="column_blue">{{ scope.row.materialName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="isbn"
          label="ISBN"
          align="center"
          min-width="167px"
        >
        </el-table-column>
        <el-table-column
          prop="platformName"
          label="出版社"
          align="center"
          min-width="167px"
        >
        </el-table-column>
        <el-table-column
          prop="author"
          label="主编"
          align="center"
          min-width="167px"
        >
        </el-table-column>
        <el-table-column
          prop="price"
          label="价格"
          align="center"
          min-width="100px"
        >
        </el-table-column>
        <el-table-column
          prop="bookNumber"
          label="用书量"
          align="center"
          min-width="100px"
        >
        </el-table-column>
        <el-table-column
          prop="useBookYear"
          label="用书时间"
          align="center"
          min-width="167px"
        >
        </el-table-column>
        <el-table-column
          prop="bookSpecialtyName"
          align="center"
          label="用书专业"
          min-width="167px"
        >
        </el-table-column>
        <el-table-column
          prop="createByName"
          label="创建人"
          align="center"
          min-width="100px"
        >
        </el-table-column>
        <el-table-column
          prop="operationPersonDepartment"
          label="创建人部门"
          align="center"
          min-width="167px"
        >
        </el-table-column>

        <el-table-column
          prop="edit"
          width="200"
          align="center"
          fixed="right"
          label="更多操作"
        >
          <template slot-scope="scope">
            <el-button class="bbtn" type="text" @click="lookInfo(scope.row)">
              情况说明
            </el-button>
            <el-button
              v-isShow="'crm:controller:customer:update'"
              class="bbtn"
              type="text"
              @click="editInfo(scope.row)"
            >
              更新</el-button
            >
            <el-button
              v-isShow="'crm:controller:customer:delete'"
              class="rbtn"
              type="text"
              @click="deleteAction(scope.row)"
            >
              移除</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <nolist></nolist>
        </template>
      </el-table>
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
      <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogFormVisible"
        width="500px"
        :before-close="handleClose"
      >
        <el-form
          :rules="rules"
          :model="bookInfoForm"
          ref="addform"
          class="addform"
        >
          <el-form-item
            v-if="!bookInfoForm.id"
            label="选择教材："
            prop="materialId"
            label-width="100px"
          >
            <div class="unitbtn" @click="chooseBook">
              <span v-if="bookInfoForm.materialId" class="deffont">{{
                bookInfoForm.materialName
              }}</span>
              <span v-else class="pltcss">请选择</span>
              <i class="rcenter el-icon-arrow-down" />
            </div>
          </el-form-item>

          <el-form-item prop="bookNumber" label="用书量：" label-width="100px">
            <el-input
              class="wcss definput"
              v-model="bookInfoForm.bookNumber"
              placeholder="请输入教材用量（册）"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="!bookInfoForm.id"
            prop="useBookYear"
            label="用书时间："
            label-width="100px"
          >
            <el-date-picker
            class="definput"
              @change="changePicker"
              v-model="useBookYear"
              type="year"
              format="yyyy"
              value-format="yyyy年"
              placeholder="选择年"
            >
            </el-date-picker>
            <span class="pdl">
              <el-radio class="mr10" v-model="radio" label="春季"
                >春季</el-radio
              >
              <el-radio class="mr10" v-model="radio" label="秋季"
                >秋季</el-radio
              >
            </span>
          </el-form-item>
          <el-form-item
            prop="useBookSpecialty"
            label="用书专业："
            label-width="100px"
          >
            <el-select
              class="wcss definput"
              v-model="bookInfoForm.useBookSpecialty"
              popper-class="removescrollbar"
              placeholder="请选择用书专业"
            >
              <el-option
                v-for="item in majorList"
                :key="item.id"
                :label="item.specialtyName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item
            label="业务经理:"
            prop="operationId"
            label-width="100px"
          >
            <div class="unitbtn" @click="chooseEmployee">
              <span v-if="bookInfoForm.operationName" class="deffont">{{
                bookInfoForm.operationName
              }}</span>
              <span v-else class="pltcss">请选择业务经理</span>
              <i class="rcenter el-icon-arrow-down" />
            </div>
          </el-form-item> -->

          <el-form-item prop="note" label="情况说明：" label-width="100px">
            <el-input
              type="textarea"
              placeholder="请输入情况说明"
              v-model="bookInfoForm.note"
              :rows="5"
              maxlength="500"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitAction" :loading="isSubmit">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        title="查看情况说明"
        :visible.sync="dialogInfoVisible"
        width="500px"
      >
        <span>
          {{ notes }}
        </span>
      </el-dialog>
      <dc-dialog
        :iType="dialogType"
        title="温馨提示"
        width="500px"
        :showCancel="isShowCancel"
        :dialogVisible.sync="dialogVisible"
        @submit="submitDialog"
      >
        <template> </template>
        <p class="pcc">{{ deletemsg }}</p>
      </dc-dialog>
      <chooseBookDialog
        ref="book"
        :visible.sync="bookDialogVisible"
        @updateVisible="updateVisible"
        @updateData="updateData"
      >
      </chooseBookDialog>
    </el-card>

    <systemDialog
      ref="employeeDialog"
      :name="'选择业务经理'"
      :multipleNum="1"
      :visible.sync="employeeDialogVisible"
      @updateVisible="updateEmployeeVisible"
      @submitData="submitEmployeeData"
    >
    </systemDialog>
    <dc-dialog
      iType="2"
      title="导入信息提示"
      width="500px"
      :showCancel="false"
      :dialogVisible.sync="dialogMsgVisible"
      @submit="submitMsgDialog"
      :appendToBody="true"
    >
      <template>
        <p>导入成功：{{ importData.successCount }} 条</p>
        <p>导入失败：{{ importData.errorCount }} 条</p>
        <p
          class="pcc"
          v-for="(item, index) in importData.errorData"
          :key="index"
        >
          {{ item }}
        </p>
      </template>
    </dc-dialog>
    <!-- 选择部门 -->
    <departmentDialog
      ref="deRef"
      dType="1"
      :visible.sync="dialogDepartmentVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </departmentDialog>
    <verifyDeparment
    ref="verifyDeparment"
    @submit="submitDepartment"></verifyDeparment>
  </div>
</template>

<script>
import systemDialog from '../../common/systemDialog.vue'
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import back from '../../common/back.vue'
import verifyDeparment from '../../common/verifyDeparment.vue';
import {
  selectSpecialty,
  customerbookList,
  customerbookSave,
  customerbookUpdate,
  customerbookInfo,
  customerbookDelete,
  customerbookDownLoad,
} from '@/api/clientmanagement/customer'
import { getDict, fileLinkToStreamDownload, urlFile } from '@/utils/tools'
import chooseBookDialog from '../../common/chooseBookDialog.vue'
import departmentDialog from '../../common/departmentDialog.vue'
import { getToken } from '@/utils/auth'
export default {
  components: {
    back,
    page,
    nolist,
    chooseBookDialog,
    systemDialog,
    departmentDialog,
    verifyDeparment,
  },
  data() {
    return {
      dialogDepartmentVisible: false,
      searchdepartmentNames: '',
      selDepartmentData: [],
      fileData: {
        applicationId: sessionStorage.getItem('applicationId'),
        serviceName: 'crm/customer/excel',
        customerId: this.$route.query.id,
      },
      headers: { Authorization: getToken() },
      getUrl: `${process.env.VUE_APP_BASE_API}/crm/business/customerbook/customerbookImportExcel`,
      isImport: false,
      isDownload: false,
      typeObj: {
        1: '过往合作',
        2: '非合作',
      },
      dialogMsgVisible: false,
      employeeDialogVisible: false,
      customerId: this.$route.query.id,
      bookDialogVisible: false,
      dialogFormVisible: false,
      dialogInfoVisible: false,
      dialogTitle: '',
      dialogVisible: false,
      dialogType: '1',
      deleteCustomerId: '',
      isShowCancel: true,
      typename: '',
      notes: '',
      levels: [],
      majorList: [],
      isLoading: false,
      tableData: [],
      bookInfoForm: {
        customerId: this.$route.query.id,
        materialId: '',
        bookNumber: '',
        useBookYear: '',
        useBookSpecialty: '',
        note: '',
        isJoin:2,
        operationDepartmentId:''
      },
      importData: {
        successCount: 0,
        errorCount: 0,
        errorData: [],
      },
      useBookYear: '',
      radio: '春季',
      total: 0,
      deletemsg: '',
      pageBean: {
        materialName: '',
        useBookYear: null,
        userBookSpecialty: '',
        operationType:1,
        pageNum: 1,
        pageSize: 10,
        operationName: '',
        operationDepartmentIds: '',
      },
      isSubmit:false,
      rules: {
        materialId: [
          { required: true, message: '请选择教材', trigger: 'change' },
        ],
        bookNumber: [
          { required: true, message: '请输入教材用量', trigger: 'blur' },
        ],
        useBookSpecialty: [
          { required: true, message: '请选择用书专业', trigger: 'change' },
        ],
        useBookYear: [
          { required: true, message: '请选择用书时间', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.loadSepcialty()
    this.pageBean.customerId = this.$route.query.id
    this.loadData()
  },
  methods: {
    handleError(file, res) {
      this.isImport = false
    },
    handleAvatarSuccess(res, file) {
      if (res.status == 0 && res.data.errorCount == 0) {
        this.$message({
          type: 'success',
          message: res.msg,
        })
      } else {
        this.$message({
          type: 'error',
          message: res.msg,
        })
      }
      this.dialogMsgVisible = true
      this.importData = res.data
      this.isImport = false
      this.pageBean.pageNum = 1
      // 刷新数据
      this.loadData()
    },
    submitMsgDialog() {
      this.dialogMsgVisible = false
      this.importData = {
        successCount: 0,
        errorCount: 0,
        errorData: [],
      }
    },
    beforeUpload(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['xlsx']
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error('导入明细仅支持 .xlsx 格式!')
        return false
      }
      this.isImport = true
    },
    downloadAction() {
      this.isDownload = true
      customerbookDownLoad({ customerId: this.customerId })
        .then((result) => {
          let fileName = urlFile(result.data.fileName, 1)
          let fileExtension = urlFile(result.data.url, 2)
          fileLinkToStreamDownload(result.data.url, fileName, fileExtension)
          this.isDownload = false
        })
        .catch((err) => {})
        .finally(() => {
          this.isDownload = false
        })
    },
    chooseEmployee() {
      this.employeeDialogVisible = true
      this.$nextTick(() => {
        this.bookInfoForm.operationId
          ? this.$refs.employeeDialog.updateWorksId([
              {
                id: this.bookInfoForm.operationId,
                name: this.bookInfoForm.operationName,
                departmentId: this.bookInfoForm.operationDepartmentId,
              },
            ])
          : this.$refs.employeeDialog.updateWorksId([])
        this.$refs.employeeDialog.loadData()
      })
    },
    updateEmployeeVisible(val) {
      this.employeeDialogVisible = val
    },
    submitEmployeeData(data, type, departmentId) {
      if (data && data.length > 0) {
        this.bookInfoForm.operationId = data[0].id
        this.bookInfoForm.operationName = data[0].name
        this.bookInfoForm.operationDepartmentId = departmentId
        this.$refs.addform.validateField('operationId')
      } else {
        this.bookInfoForm.operationId = ''
        this.bookInfoForm.operationName = ''
      }
      this.employeeDialogVisible = false
    },
    loadSepcialty() {
      selectSpecialty({
        customerId: this.customerId,
      })
        .then((result) => {
          this.majorList = result.data
        })
        .catch((err) => {})
    },
    changePicker(val) {
      if (val) {
        this.bookInfoForm.useBookYear = val
      } else {
        this.bookInfoForm.useBookYear = ''
      }
    },
    loadData() {
      this.isLoading = true
      customerbookList(this.pageBean)
        .then((result) => {
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    changeOption(data) {
      if (data == '1') {
        this.pageBean.chargePerson = window.sessionStorage.getItem('userid')
        this.pageBean.collaborator = ''
      } else if (data == '2') {
        this.pageBean.chargePerson = ''
        this.pageBean.collaborator = window.sessionStorage.getItem('userid')
      } else {
        this.pageBean.chargePerson = ''
        this.pageBean.collaborator = ''
      }
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    submitDialog() {
      this.dialogVisible = false
      if (this.dialogType == 1) {
        this.deleteData()
      }
    },
    deleteData() {
      customerbookDelete({ id: this.deleteCustomerId })
        .then((result) => {
          if (result.data) {
            this.loadData()
            this.$message({
              type: 'success',
              message: '移除成功',
            })
          } else {
            this.dialogType = 2
            this.isShowCancel = false
            this.deletemsg = result.msg
            this.dialogVisible = true
          }
        })
        .catch((err) => {})
    },

    addInfo() {
      this.dialogFormVisible = true
      this.dialogTitle = '新增用书信息'
    },
    editInfo(data) {
      this.dialogFormVisible = true
      this.dialogTitle = '编辑用书信息'
      this.loadInfoById(data.id)
    },
    loadInfoById(id) {
      customerbookInfo(id)
        .then((result) => {
          this.bookInfoForm = result.data
          var useBookYear = this.bookInfoForm.useBookYear
          var index = useBookYear.indexOf('年')
          this.useBookYear = useBookYear.substring(0, index + 1)
          this.radio = useBookYear.substring(index + 1, useBookYear.length)
        })
        .catch((err) => {})
    },
    deleteAction(data) {
      this.dialogType = 1
      this.isShowCancel = true
      this.dialogVisible = true
      this.deletemsg = '是否删除该用书信息？'
      this.deleteCustomerId = data.id
    },
    toDetail(data) {
      this.$router.push({
        path: '/clientManagement/customer/detail',
        query: { id: data.id },
      })
    },
    lookInfo(data) {
      this.dialogInfoVisible = true
      this.notes = data.note
    },
    onEdit(data) {
      this.$router.push({
        path: '/clientManagement/customer/add',
        query: {
          id: data.id,
        },
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    handleClose() {
      this.dialogFormVisible = false
      this.dialogTitle = ''
      Object.keys(this.bookInfoForm).forEach((element) => {
        if (element != 'customerId') {
          this.bookInfoForm[element] = ''
        }
      })
      this.bookInfoForm.operationName = ''
      this.useBookYear = ''
      this.radio = '春季'
      this.$nextTick(() => {
        this.$refs.addform.clearValidate()
      })
    },
    submitAction() {
      if (this.useBookYear && this.radio) {
        this.bookInfoForm.useBookYear = `${this.useBookYear}${this.radio}`
      }
      this.$refs['addform'].validate((valid) => {
        if (valid) {
          delete this.bookInfoForm.operationName
          if (this.bookInfoForm.id) {
            this.update()
          } else {
            this.$refs.verifyDeparment.verify()
          }
        } else {
          return false
        }
      })
    },
    submitDepartment(departmentId){
      this.bookInfoForm.operationDepartmentId = departmentId;
      this.add()
    },
    add() {
      this.isSubmit = true;
      customerbookSave(this.bookInfoForm)
        .then((result) => {
          this.isSubmit = false;
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功',
            })
            this.loadData()
            this.handleClose()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    update() {
      this.isSubmit = true
      customerbookUpdate(this.bookInfoForm)
        .then((result) => {
          this.isSubmit = false
          if (result.data) {
            this.$message({
              type: 'success',
              message: '更新成功',
            })
            this.loadData()
            this.handleClose()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    chooseBook() {
      this.bookDialogVisible = true
      this.$nextTick(() => {
        this.$refs.book.loadBookData({
          customerId: this.customerId,
          type: 2,
        },'')
      })
    },
    updateVisible(val) {
      this.bookDialogVisible = val
    },
    updateData(data) {
      this.bookInfoForm.materialId = data.id
      this.bookInfoForm.materialName = data.name
      this.$refs.addform.validateField('materialId')
    },
    cleardata() {
      this.searchdepartmentNames = ''
      this.pageBean.operationDepartmentIds = ''
      this.selDepartmentData = []
    },
    chooseDepartment(e) {
      this.dialogDepartmentVisible = true
      this.$refs.deRef.loadData()
      this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
    },
    updateSystemVisible(val) {
      this.dialogDepartmentVisible = val
    },
    submitData(data) {
      this.dialogDepartmentVisible = false
      this.selDepartmentData = data
      let departmentIds = data.map((item) => item.id)
      let departmentNames = data.map((item) => item.name)
      this.pageBean.operationDepartmentIds = departmentIds.join(',')
      this.searchdepartmentNames = departmentNames.join(',')
    },
  },
}
</script>
<style>
.tootiptable .el-tooltip {
  text-align: left;
}

.cpdding .el-input__inner {
  padding-right: 0;
}
</style>

<style scoped lang='scss'>
.wcss{
  width: 100% !important;
}
.cbt {
  margin-bottom: 0px !important;
}
.ml20 {
  margin-left: 20px;
}
.bucss {
  display: flex;
  margin-left: auto;
}
.defr {
  float: right !important;
}
.flexc {
  display: flex;
  flex-wrap: wrap;
}
.down {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row-reverse;
  align-items: flex-end;
}
.linput {
  width: 120px;
}
.lei /deep/.el-form-item__label {
  width: 60px !important;
}
.lei /deep/.el-form-item__content {
  margin-left: 60px !important;
}
.utime {
  width: 140px;
}
.umajor {
  width: 180px;
}
.icss {
  width: 150px;
}
.mr10 {
  margin-right: 10px !important;
}

.pdl {
  padding-left: 10px;
}

.defaultbtn1 {
  padding: 9px 10px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 34px;
  padding: 0 12px !important;
}
.definput {
  width: 200px;
}
.rcenter {
  position: absolute;
  top: 5px;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}

.namecss {
  display: flex;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  min-width: 30px !important;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  margin-top: -4px;
  vertical-align: middle;
  position: relative;
  top: 21px;
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
}

.column_blue {
  color: #4285f4;
}

.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}

.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}

.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.tagcss:nth-child(3n) {
  margin-top: 4px;
}

.customertable /deep/.cell {
}

.customertable .el-button {
  padding: 2px;
}
.customertable {
  margin-top: 20px !important;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}
</style>
