import service from '@/utils/request.js'
//合同列表
export function contractList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/contract/list',
    params,
  });
}





// 合同详情
export function contractInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/contract/info/${id}`
  })
}
// 新增合同
export function contractSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contract/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 编辑合同
export function updateContract(data) {
  return service.request({
    method: 'post',
    url: 'crm/controller/contract/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 删除
export function deleteContract(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contract/delete',
    data,
  });
}

// 合同-发货

//  发货列表
export function contractinvoiceList(params) {
  return service.request({
    method: 'get',
    url: "/crm/controller/contractinvoice/list",
    params
  })
}

//  合同列表发货物品总数
export function contractinvoiceQueryOverview(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/contractinvoice/queryOverview',
    params
  })
}
// 新增发货记录
export function addContractinvoice(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractinvoice/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 删除发货
export function deleteContractinvoice(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractinvoice/delete',
    data,
  });
}

//
// 下载模版
export function contractInvoiceDownloadTemplate() {
  return service.request({
    method: 'get',
    url: '/crm/controller/contractinvoice/contractInvoiceDownloadTemplate',
    responseType: "blob"
  });
}

// 合同-退款
//  退款列表
export function contractrefundList(params) {
  return service.request({
    method: 'get',
    url: "/crm/controller/contractrefund/list",
    params
  })
}

//  退款详情
export function contractrefundInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/contractrefund/info/${id}`
  })
}
// 新增退款记录
export function addContractrefund(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractrefund/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 编辑退款记录
export function updateContractrefund(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractrefund/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 撤销退款记录
export function revokeContractrefund(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractrefund/revoke',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 删除退款记录
export function deleteContractrefund(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractrefund/delete',
    data,
  });
}

// 发货-回款
// 回款列表
export function contractreturnList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/contractreturn/list',
    params
  })
}
//  回款详情
export function contractreturnInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/contractreturn/info/${id}`
  })
}
// 新增回款记录
export function addContractreturn(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractreturn/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 编辑回款记录
export function updateContractreturn(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractreturn/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}







export function queryApproveInfo(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/processrecord/queryApproveInfo',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 撤销回款记录
export function revokeContractreturn(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractreturn/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 删除回款记录
export function deleteContractreturn(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractreturn/delete',
    data,
  });
}

// 发货-开票
// 开票列表
export function contractinvoicingList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/contractinvoicing/list',
    params
  })
}
//  开票详情
export function contractinvoicingInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/contractinvoicing/info/${id}`
  })
}
// 新增开票记录
export function addContractinvoicing(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractinvoicing/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 编辑开票记录
export function updateContractinvoicing(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractinvoicing/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 删除开票记录
export function deleteContractinvoicing(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/contractinvoicing/delete',
    data,
  });
}

// /crm/controller/contract/selectReturnOrRefund
export function selectReturnOrRefund(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/contract/selectReturnOrRefund',
    params
  })
}



export function addProject(contractId) {
  console.log(contractId);
  return service.request({
    url: `/crm/controller/contract/addProject/${contractId}`,
    method: 'get',
  })
}
