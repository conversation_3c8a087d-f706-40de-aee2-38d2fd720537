<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :visible.sync="centerDialogVisible"
    width="30%"
  >
    <el-form
      label-width="80px"
      :rules="Rule.NEW_MANAGEMENT"
      ref="dataForm"
      :model="dataForm"
    >
      <el-form-item label="角色名称" prop="name">
        <el-input
          v-model="dataForm.name"
          placeholder="请输入角色名称"
          clearable
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="centerDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm('dataForm')"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import {saveRole,updateRole} from '@/api/framework/roleposition'
export default {
  props: {
  },
  data() {
    return {
      centerDialogVisible: '',
      dataForm: {
        id: '',
        name: '',
      },
    }
  },
  methods: {
    // 初始化
    init(role) {
      this.dataForm.id = role?role.id:''
      role ? (this.dataForm.name = role.name) : (this.dataForm.name = '')
      this.centerDialogVisible = true
    },
    // 新增或者修改
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
            let res;
            if(!this.dataForm.id){
               res = await saveRole(this.dataForm) 
            }else{
              res = await updateRole(this.dataForm) 
            }
          if (res.status === 0) {
              this.centerDialogVisible = false
              this.$message.success('成功')
              this.$emit('refreshDataList')
              this.dataForm.name = ''
              this.dataForm.id = ''
          }
        } else {
          return false
        }
      })
    },
  },
}
</script>
