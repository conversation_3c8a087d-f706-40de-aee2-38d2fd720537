/**
 * @Desc 公共分页 转化时间戳方法
 */
import { ruleTime } from '../utils/tools'
export default {
  data() {
    return {
      pagingObj: {
        totalSum: '',
        currentPage: 1
      }
    }
  },
  methods: {
    // 时间戳转化为时分秒
    ruleTime(val) {
      return ruleTime(val)
    },
    /**
     * @param {*} val 总条数
     */
    handleSizeChange(val) {
      this.search.pageSize = val
      this.getDataList(this.search)
    },
    /**
     * @param {*} obj  页码
     */
    handleCurrentChange(obj) {
      this.pagingObj.currentPage = obj
      this.search.pageNum = this.pagingObj.currentPage
      this.getDataList(this.search)
    },
    /**
     * @param {*} obj 处理时间参数
     * @param {*} startTime 开始参数
     * @param {*} endTime 结束参数
     */
    handleTimeParams(obj, startTime, endTime) {
      if (obj && obj instanceof Array) {
        this.search[startTime] = obj[0];
        this.search[endTime] = obj[1]
      } else {
        this.search[startTime] = '';
        this.search[endTime] = ''
      }
    },
    /**
     * @param {*} obj 处理地区参数
     * @param {*} provinceId 省参数
     * @param {*} cityId 市参数
     * @param {*} countyId 区参数
     */
    handleGetArea(obj, provinceId, cityId, countyId) {
      if (obj[0]) {
        this.search[provinceId] = obj[0];
      } else {
        this.search[provinceId] = '';
      }
      if (obj[1]) {
        this.search[cityId] = obj[1];
      } else {
        this.search[cityId] = '';
      }
      if (obj[2]) {
        this.search[countyId] = obj[2];
      } else {
        this.search[countyId] = '';
      }
    }
  }
}
