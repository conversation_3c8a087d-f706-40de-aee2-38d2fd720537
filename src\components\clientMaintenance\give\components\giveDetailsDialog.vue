<template>
    <el-dialog class="givedialog" title="查看明细" top="80px" :visible.sync="dialogTableVisible" width="70%" center>
        <div>
            <span class="ttext deffont">发放物品数量：<span class="deffont mr10">{{ totaldata.distributionNumber }}</span></span>
            <span class="ttext deffont">累计成本：<span class="deffont">{{ totaldata.distributionTotalAmount }}元</span></span>
            <div class="right flex">
                <el-button class=" defaultbtn mb20" icon="el-icon-plus" type="primary" @click="addAction">添加明细</el-button>
                <el-upload class="m20" :action="getUrl" :show-file-list="false" :before-upload="beforeUpload"
                    :on-success="handleAvatarSuccess" :on-error="handleError" :headers="headers" :data="fileData"
                    accept=".xlsx">
                    <el-button class=" defaultbtn mb20" icon="el-icon-my-inport" type="primary"
                        :loading="isImport">导入明细</el-button>
                </el-upload>

                <el-button class=" defaultbtn mb20" icon="el-icon-my-download" type="primary" :loading="isDownload"
                    @click="downloadAction">下载模版</el-button>
            </div>
        </div>
        <el-table key="table1" v-if="currentData.type == 1" :height="477" class="mytable" :data="dataList"
            style="width:100%" :v-loading="isLoading">
            <el-table-column property="name" label="教材名称" align="center"></el-table-column>
            <el-table-column property="publish" label="出版社" align="center"></el-table-column>
            <el-table-column property="isbn" label="书号" align="center"></el-table-column>
            <el-table-column property="price" label="价格" align="center"></el-table-column>
            <el-table-column property="number" label="数量" align="center"></el-table-column>
            <el-table-column property="edit" width="150px" label="操作" align="center">
                <template slot-scope="scope">
                    <div class="rbtn deffont" @click="deleteAction(scope.row)">移除</div>
                </template>
            </el-table-column>
            <template slot="empty">
                <img class="nodataimgcss" src="@/assets/img/nodata_icon.png" alt="" srcset="">
                <div>
                    暂无明细～
                </div>
            </template>
        </el-table>

        <el-table key="table2" v-if="currentData.type == 2" :height="477" class="mytable" :data="dataList"
            style="width:100%" :v-loading="isLoading">
            <el-table-column property="name" label="物品" align="center"></el-table-column>
            <el-table-column property="number" label="数量" align="center"></el-table-column>
            <el-table-column property="price" label="价格" align="center"></el-table-column>
            <el-table-column property="edit" width="150px" label="操作" align="center">
                <template slot-scope="scope">
                    <div class="rbtn deffont" @click="deleteAction(scope.row)">移除</div>
                </template>
            </el-table-column>
            <template slot="empty">
                <img src="@/assets/img/nodata_icon.png" alt="" srcset="">
                <div>
                    暂无明细～
                </div>
            </template>
        </el-table>

        <page :currentPage="pageBean.pageNum" :pageSize="pageBean.pageSize" :total="total"
            @updatePageNum="handleCurrentChange"></page>
        <el-dialog title="添加明细" class="adddetailcss" width="40%" :visible.sync="addVisible" :before-close="beforeClose"
            append-to-body center>
            <el-form ref="addform" :model="form" :rules="rules" class="revisitcss myform" label-width="95px">
                <el-row :gutter="0" v-if="currentData.type == 1">
                    <el-col :span="24">
                        <el-form-item label='教材名称：' prop="name">
                            <el-input class="definput" v-model="form.name" placeholder="请输入教材名称"></el-input>
                        </el-form-item>
                        <el-form-item label='出版社：' prop="publish">
                            <el-input class="definput" v-model="form.publish" placeholder="请输入出版社名称"></el-input>
                        </el-form-item>
                        <el-form-item label='书号：' prop="isbn">
                            <el-input class="definput" v-model="form.isbn" placeholder="请输入书号"></el-input>
                        </el-form-item>
                        <el-form-item label='价格：' prop="price">
                            <el-input class="definput" type="number" v-model="form.price" placeholder="请输入价格"></el-input>
                        </el-form-item>
                        <el-form-item label='数量：' prop="number">
                            <el-input class="definput" type="number" v-model="form.number" placeholder="请输入数量"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="0" v-if="currentData.type == 2">
                    <el-col :span="24">
                        <el-form-item required label='礼品名称：' prop="name">
                            <el-input class="definput" v-model="form.name" placeholder="请输入礼品名称"></el-input>
                        </el-form-item>
                        <el-form-item label='价格：' prop="price">
                            <el-input class="definput" type="number" v-model="form.price" placeholder="请输入价格"></el-input>
                        </el-form-item>
                        <el-form-item label='数量：' prop="number">
                            <el-input class="definput" type="number" v-model="form.number" placeholder="请输入数量"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

           



            <div class="center">
                <el-button class="submitbtn defaultbtn" type="primary" :loading="isSubmit"
                    @click="submitForm">提交</el-button>
            </div>
        </el-dialog>
        <!-- 删除提示 -->
        <dc-dialog iType="1" title="确定删除吗？" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog"
            :appendToBody="true">
            <template>

            </template>
            <p class="pcc">是否删除该发放明细？</p>
        </dc-dialog>
        <!-- 导入提示 -->
        <dc-dialog iType="2" title="导入信息提示" width="500px" :showCancel="false" :dialogVisible.sync="dialogMsgVisible"
            @submit="submitMsgDialog" :appendToBody="true">
            <template>
            </template>
            <p class="pcc" v-for="(item, index) in errorData" :key="index">{{ item }}</p>
        </dc-dialog>
    </el-dialog>
</template>

<script>

import page from '@/components/common/page.vue';
import { downloadExcelFile } from "@/utils/tools";
import { getToken } from '@/utils/auth'
import { distributionDetailList, queryDetailOverview, deleteDistributionDetail, addDistributionDetail, batchImportExcel, downloadTemplate } from "@/api/clientMaintenance/give";

export default {
    components: {
        page
    },
    data() {
        return {
            dialogMsgVisible: false,
            errorData: [],
            isDownload: false,
            dialogVisible: false,
            addVisible: false,
            activeName: "first",
            getUrl: `${process.env.VUE_APP_BASE_API}/crm/controller/distributiondetail/batchImportExcel`,
            headers: { Authorization: getToken() },
            dataList: [],
            isLoading: false,
            isSubmit: false,
            totaldata: {
                distributionNumber: 0,
                distributionTotalAmount: 0
            },
            form: {
                distributionId: "",
                type: "",
                name: "",
                publish: "",
                isbn: "",
                number: '',
                price: ''
            },
            fileData: {
                type: "",
                distributionId: "",
                serviceName: "crm/distributiondetail",
            },
            rules: {
                name: [
                    { required: true, message: '请输入教材名称', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
                publish: [
                    { required: true, message: '请输入出版社名称', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
            },
            isImport: false,
            pageBean: {
                pageNum: 1,
                pageSize: 8,
            },
            total: 0,
            currentData: {},
            deleteData: {},
            typeName: {
                1: '样书',
                2: '礼品'
            }
        }
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
    },
    computed: {
        dialogTableVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('updateVisible', val);
            },
        }
    },
    methods: {
        init(data) {
            this.currentData = data;
            if(this.currentData.type == 2){
                this.rules.name[0].message = '请输入礼品名称'
            }else{
                this.rules.name[0].message = '请输入教材名称'
            }
            this.dataList = [];
            var par = {
                distributionId: data.id,
                type: data.type
            }
            this.fileData = Object.assign(this.fileData, par);
            this.form = Object.assign(this.form, par);
            this.pageBean = Object.assign(this.pageBean, par);
            this.loadList();
            this.loadData();
        },
        loadList() {
            this.isLoading = true;
            distributionDetailList(this.pageBean).then((result) => {
                this.dataList = result.data;
                this.total = result.page.total;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        loadData(par) {
            var par = {
                distributionId: this.currentData.id,
                type: this.currentData.type
            }
            queryDetailOverview(par).then((result) => {
                this.totaldata = result.data;
            }).catch((err) => {

            });
        },
        addAction() {
            this.addVisible = true;
        },
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.loadList();
        },
        deleteAction(val) {
            this.dialogVisible = true;
            this.deleteData = val;
        },
        beforeClose() {
            this.addVisible = false;
            var keys = Object.keys(this.form);

            this.$refs['addform'].resetFields();
            keys.forEach(element => {
                if (element != 'distributionId' && element != 'type') {
                    this.form[element] = ''
                }
            });
        },
        // 提交表单
        submitForm() {
            this.$refs['addform'].validate((valid) => {
                if (!valid) {
                    return false;
                }
                if (this.form.id) {
                    // 编辑
                    this.updateData();
                } else {
                    // 添加
                    this.addData();
                }
            });
        },
        addData() {
            this.isSubmit = true;
            addDistributionDetail(this.form).then((result) => {
                if (result.data) {
                    this.$message({
                        type: "success",
                        message: "添加成功！"
                    })
                    this.loadList();
                    this.loadData();
                    this.beforeClose();
                } else {
                    this.$message({
                        type: "error",
                        message: "保存失败！"
                    })
                }
                this.isSubmit = false;
            }).catch((err) => {
                this.isSubmit = false;
            });
        },
        updateData() {

        },
        submitDialog() {
            deleteDistributionDetail({ id: this.deleteData.id }).then((result) => {
                if (result.data) {
                    this.$message({
                        type: "success",
                        message: "移除成功"
                    });
                    this.loadList();
                    this.loadData();
                    this.dialogVisible = false;
                } else {
                    this.$message({
                        type: "error",
                        message: result.msg
                    });
                }
            }).catch((err) => {

            });

        },
        beforeUpload(file) {
            const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
            const whiteList = ['xlsx'];
            if (whiteList.indexOf(fileSuffix) === -1) {
                this.$message.error('导入明细仅支持 .xlsx 格式!');
                return false
            }
            this.isImport = true;
        },
        handleError(file, res) {
            this.isImport = false;
        },
        handleAvatarSuccess(res, file) {
            if (res.status == 0 && res.data.errorCount <= 0) {
                this.$message({
                    type: 'success',
                    message: res.msg
                })
            } else {
                this.$message({
                    type: 'error',
                    message: res.msg
                })
                // 显示错误提示的弹框
                this.dialogMsgVisible = true;
                this.errorData = res.data.errorData;
            }
            // 刷新数据
            this.loadData();
            this.loadList();
            this.isImport = false;
        },
        submitMsgDialog() {
            this.dialogMsgVisible = false;
            this.errorData = [];
        },
        downloadAction() {
            this.isDownload = true;
            downloadTemplate({ type: this.currentData.type }).then((result) => {
                downloadExcelFile(result, `${this.typeName[this.currentData.type]}明细模版`)
                this.isDownload = false;
            }).catch((err) => {
                this.isDownload = false;
            });
        },
    }
}
</script>
<style scoped>
.m20 {
    margin: 0 20px;
}

.nodataimgcss {
    margin-bottom: 12px;
    width: 180px;
    height: 92px;
}

.mytable /deep/.el-table__empty-text {
    line-height: 20px !important;
    min-height: 20px !important;
}

.width100 {
    width: 100%;
}

.submitbtn {
    margin-top: 50px;
}

.flex {
    display: flex;
}

.lh {
    width: 100%;
    line-height: 23px;
}

.ttext {
    color: #999999;
    line-height: 34px;
}

.center {
    text-align: center;
}

.bbtn {
    color: #4285F4;
    cursor: pointer;
}

.rbtn {
    color: #F45961;
    cursor: pointer;
}

.revisitcss {
    line-height: 34px;
    margin: 0px 80px;
}

.revisitcss /deep/.el-form-item {
    margin-bottom: 18px;
    /* padding-right: 60px; */
}

.ml20 {
    margin-left: 20px;
}

.givedialog /deep/.el-dialog {
    min-width: 700px !important;
}

.givedialog /deep/.el-dialog__body {
    padding: 20px;
    padding-top: 0px;
}

.givedialog /deep/.el-dialog__header {
    border: none;
}
</style>
<style scoped>
.tabscss /deep/.el-tabs__item {
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 40px;
    width: 240px !important;
    text-align: center;
    line-height: 40px;
    /* padding: 0 60px; */
}
</style>