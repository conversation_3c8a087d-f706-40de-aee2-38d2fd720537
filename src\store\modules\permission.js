import { getPermissions } from '@/api/user'
import Layout from '@/layout'
import { asyncRoutes, constantRoutes } from "@/router";
export const loadView = (view) => { // 路由懒加载
  console.log(view)
  return (resolve) => require([`@/components${view}`], resolve)
}
export function filterAsyncRoutes(routes, parent) {
  const res = []
  routes.forEach(route => {
    route.childMentList = []
    if (route.sType == 0) {
      if (route.parentId == 0) {
        route.component = Layout
        route.path = route.sUrl
        route.redirect = route.children.length > 0 ? route.children[0].sUrl : null
      } else {
        if (!route.component) {
          let ming = route.sUrl.split('/')[1]
          route.component = loadView("/" + ming + "/index")
          route.path = route.sUrl
        }
      }
    } else {
      if (route.parentId == 0) {
        route.component = Layout
        route.path = route.sUrl == '/home' ? '/' : route.sUrl
        route.children = [{
          component: loadView(route.sUrl == '/home' ? '/home' : route.sUrl),
          path: route.sUrl,
          name: route.name
        }]
        route.redirect = route.children[0].sUrl
      } else {
        if (!route.component) {
          route.component = loadView(route.sUrl)
          route.path = route.sUrl
        }
      }
    }
    route.meta = {
      title: route.name,
      icon: route.sIcon
    }

    if (route.children != null && route.children && route.children.length > 0) {
      if (route.sType == 0) {
        route.children = filterAsyncRoutes(route.children, route)
      } else {
        let menuList = filterAsyncRoutes(route.children, parent)
        parent.childMentList = parent.childMentList.concat(menuList)
      }
    }
    if (parent) {
      if (parent.sUrl != '/home') {
        route.children = []
      }
    } else {
      if (route.childMentList) {
        route.children = route.children.concat(route.childMentList)
      }
    }
    res.push(route)
  })
  return res
}

export function getPaths(routers){
  var paths = []
  routers.forEach(element => {
    if (element.sUrl) {
      paths.push(element.sUrl)
    }
    if (element.children && element.children.length>0) {
      let list = getPaths(element.children)
      paths = paths.concat(list)
    }
  });
  return paths
}
const state = {
  lookRouter: [], // 可访问的权限路由
  AllRouter: constantRoutes, // 公共路由与异步权限路由拼接
  authList: [], //操作权限
  permissionMenu: [], //权限
  paths:[],// 路由地址
}

const mutations = {
  SET_LOOKROUTERS: (state, routers) => {
    state.lookRouter = routers // 可访问的权限路由
    state.AllRouter = constantRoutes.concat(routers) // 公共路由与异步权限路由拼接
  },
  SET_AUTHLIST: (state, authList) => {
    state.authList = authList
  },
  SET_PATHS: (state, paths) => {
    state.paths = paths
  },
  SET_PERMISSIONMENU: (state, permissionMenu) => {
    state.permissionMenu = permissionMenu
  },
}

const actions = {
  getRouters({ commit }) {
    return new Promise((resolve, reject) => {
      getPermissions().then((res) => {
        let asyncRoutes = res.data.menuList
        let accessedRouters = filterAsyncRoutes(asyncRoutes)
        let paths = getPaths(accessedRouters)
        const object = { path: '*', redirect: '/404', hidden: true }
        accessedRouters.push(object)
        let authList = res.data.permissionKeys
        let permissionMenu = res.data.resourceList

        commit('SET_LOOKROUTERS', accessedRouters)
        commit('SET_AUTHLIST', authList)
        commit('SET_PERMISSIONMENU', permissionMenu)
        commit('SET_PATHS', paths)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  //清空路由
  resertLookRouters({ commit }) {
    return new Promise(resolve => {
      commit('SET_LOOKROUTERS', [])
      commit('SET_AUTHLIST', [])
      commit('SET_PERMISSIONMENU', [])
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
