<template>
  <section  class="app-main" id="section">
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key" />
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  created(){
   
  },
  computed: {
    key() {
      return this.$route.path
    }
  }
}
</script>

<style scoped>
.app-main {
  height:100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: #f7f7f7;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;

}
.fixed-header+.app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
