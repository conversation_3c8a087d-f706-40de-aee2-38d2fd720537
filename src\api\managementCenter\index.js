import request from '@/utils/request'

export function ruleList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/processrule/list',
    params
  });
}

export function ruleSave(data) {
  return request({
    method: 'post',
    url: '/crm/controller/processrule/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
export function ruleDelete(data) {
  return request({
    method: 'post',
    url: '/crm/controller/processrule/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function ruleUpdate(data) {
  return request({
    method: 'post',
    url: '/crm/controller/processrule/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}



// 获取规则详情
export function ruleInfo(id) {
  return request({
    method: 'get',
    url: `/crm/controller/processrule/info/${id}`
  });
}





// 删除公告
export function deleteGonggao(id) {
  return request({
    method: 'get',
    url: `/crm/controller/version/delete`,
    params: { id }
  });
}



// 更新公告
export function updateGonggao(data) {
  return request({
    method: 'post',
    url: '/crm/controller/version/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

//意见反馈列表  
export function fetchFeedBackList(params) {
  return request({
    method: 'get',
    url: '/crm/controller/feedback/list',
    params
  });
}