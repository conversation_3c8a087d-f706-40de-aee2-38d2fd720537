<template>
  <div class="mainbg" v-loading="isLoading">
    <div class="backdiv">
      <back>明细数据</back>
    </div>
    <div class="page-header">
      <el-form class="myform" ref="form" :model="pageBean" :inline="true">
        <el-form-item label="">
          <el-input clearable="" class="definput" v-model="pageBean.unitSpecialtyId" placeholder="请输入教材名称"></el-input>
        </el-form-item>
        <el-form-item label="用书专业">
          <el-select clearable="" class="definput" popper-class="removescrollbar" v-model="pageBean.unitSpecialtyId"
          placeholder="请选择专业">
            <el-option v-for="item in optionsMajor" :key="item.specialtyId" :label="item.specialtyName" :value="item.specialtyId">
            </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="数据来源">
          <el-select clearable="" class="definput" popper-class="removescrollbar" v-model="pageBean.unitSpecialtyId"
            placeholder="请选择">
              <el-option v-for="item in datasourceList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否合作">
          <el-select clearable="" class="definput" popper-class="removescrollbar" v-model="pageBean.unitSpecialtyId"
            placeholder="请选择">
              <el-option v-for="item in cooList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button class=" defaultbtn" type="primary" icon="el-icon-search" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table class=" mytable" :data="tableData" >
      <el-table-column prop="specialtyName" label="教材名称" align="center">
      </el-table-column>
      <el-table-column prop="planRecruitNumber" label="出版社" align="center">
      </el-table-column>
      <el-table-column prop="realityRecruitNumber" label="主编" align="center">
      </el-table-column>
      <el-table-column prop="recruitYear" label="价格" align="center">
      </el-table-column>
      <el-table-column prop="courseNumber" label="数量" align="center">
      </el-table-column>
      <el-table-column prop="materialNumber" label="用书专业" align="center">
      </el-table-column>
      <el-table-column prop="materialNumber" label="用书时间" align="center">
      </el-table-column>
      <el-table-column prop="materialNumber" label="数据来源" align="center">
      </el-table-column>
      <el-table-column prop="materialNumber" label="是否合作" align="center">
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
      @updatePageNum="handleCurrentChange"></page>
  </div>
</template>

<script>

  import page from '../../common/page.vue';
  import nolist from '../../common/nolist.vue';
  import { zhaoList,selectUintSpecialty,updateZhao,addZhao,deleteZhao,zhaoInfo} from '@/api/clientmanagement/unit'
  import back from '../../common/back.vue'
  export default {
    components: {
      page,
      nolist,
      back
    },
    data() {
      return {
        datasourceList:[
          {
            id:'',
            name:'全部'
          },
          {
            id:1,
            name:'用书信息'
          },
          {
            id:2,
            name:'销售机会'
          },
          {
            id:3,
            name:'教材发货'
          },
        ],
        cooList:[
          {
            id:'',
            name:'全部'
          },
          {
            id:1,
            name:'是'
          },
          {
            id:2,
            name:'否'
          },
        ],
        isLoading: false,
        tableData: [],
        total: 0,
        pageBean: {
          recruitYear: '',
          pageNum: 1,
          pageSize: 10,
          unitSpecialtyId:"",
        },
        optionsMajor:[],
      }
    },
    created() {
      this.loadData();
      this.selectUintSpecialtyApi()
    },
    methods: {
      updateButton(row){
        this.dialogTableVisible = true
        zhaoInfo(row.id).then(res=>{
            if(res.status == 0){
                this.ruleForm = res.data
                this.ruleForm.recruitYear = this.ruleForm.recruitYear+''
            }
        })
      },
      selectUintSpecialtyApi(){
        let params = {
          unitId:this.$route.query.id
        }
        selectUintSpecialty(params).then(res=>{
          if(res.status == 0){
              this.optionsMajor = res.data
          }
        })
      },
      submitButton(){
        this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.ruleForm.unitId = this.$route.query.id
        if (this.ruleForm.id) {
          updateZhao(this.ruleForm).then((result) => {
            if (result.data) {
              this.$message({
                type: "success",
                message: "更新成功！"
              })
              this.dialogTableVisible = false
              this.loadData()
            } else {
              this.$message({
                type: "error",
                message: "保存失败！"
              })
            }
          }).catch((err) => {

          });
        } else {
          addZhao(this.ruleForm).then((result) => {
            if (result.data) {
              this.$message({
                type: "success",
                message: "添加成功！"
              })
              this.dialogTableVisible = false
              this.loadData()
            } else {
              this.$message({
                type: "error",
                message: "保存失败！"
              })
            }
          }).catch((err) => {

          });
        }
      });
      },
      loadData() {
        this.isLoading = true;
        zhaoList(this.pageBean).then((result) => {
          this.tableData = result.data;
          this.total = result.page.total;
          this.isLoading = false;
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      onSearch() {
        this.pageBean.pageNum = 1;
        this.loadData();
      },
      addUnit() {
        this.dialogTableVisible = true
        Object.keys(this.ruleForm).forEach((key)=>{
          this.ruleForm[key] = undefined;
        })
        this.$nextTick(()=>{
          this.$refs["ruleForm" ].resetFields();
        })
      },
      deleteAction(data) {
        deleteZhao({ id: data.id }).then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '移除成功',
            })
            this.loadData();
          } else {
            this.$message({
              type: 'error',
              message: result.msg
            })
          }
        }).catch((err) => {

        });
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.loadData();
      },
    }
  }

</script>

<style scoped>
  .guang /deep/.el-input__inner{
    line-height: 1px !important;
  }
  .wcss{
      width: 200px;
  }
  .cDialog /deep/.el-dialog__body{
    display: flex;
    justify-content: center;
    align-content: center;
  }
  .cDialog /deep/.el-dialog{
    width: 580px !important;
  }
  .backdiv{
    margin-bottom: 12px;
  }
  .definput{
    width: 180px;
  }
  .page-header {
    display: flex;
    justify-content: space-between;
  }
  .tabbtn {
    font-size: 14px;
    cursor: pointer;
    padding: 2px;
  }

  .rbtn,
  .rbtn:hover,
  .rbtn:focus {
    color: #F45961;
  }
  .mainbg {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
  }
</style>