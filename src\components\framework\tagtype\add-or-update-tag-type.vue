<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="30%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="100px">
      <el-form-item label="所属类型" v-if="dataForm.parentId!=0">
        <el-input v-model="dataForm.parentName" disabled></el-input>
      </el-form-item>
      <el-form-item label="类型名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="类型编码" prop="code">
        <el-input v-model="dataForm.code" placeholder="类型编码"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        parentId: '',
        code: '',
        name: ''
      },
      dataRule: {
        code: [
          { required: true, message: '类型编码不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '类型名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init (id, parentId, parentName) {
      this.dataForm.id = id
      this.dataForm.parentId = parentId || 0
      this.dataForm.parentName = parentName
      this.visible = true
      this.$nextTick(async () => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          let res = await this.$axios.get(`/sf/business/tagtype/info/${this.dataForm.id}`);
          if (res.status === 0) {
            this.dataForm.code = res.data.code
            this.dataForm.name = res.data.name
          }
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          var params = {
            'id': this.dataForm.id || undefined,
            'parentId': this.dataForm.parentId,
            'code': this.dataForm.code,
            'name': this.dataForm.name
          }
          let url = '';
          if (!this.dataForm.id) {
            url = '/sf/business/tagtype/save'
          } else {
            url = '/sf/business/tagtype/update'
          }
          let res = await this.$axios.post(url, params);
          if (res.status === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshTagTypeTree')
              }
            })
          } else {
            this.$message.error(res.msg)
          }
        }
      })
    }
  }
}
</script>
