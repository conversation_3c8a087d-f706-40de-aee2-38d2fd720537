<!-- 带快捷方式的日期选择器 -->
<template>
  <div>
    <el-date-picker
      style="width: 260px;"
      v-model="value6"
      type="daterange"
      align="right"
      unlink-panels
      start-placeholder="起始时间"
      end-placeholder="结束时间"
      value-format="yyyy-MM-dd"
      :picker-options="pickerOptions"
    ></el-date-picker>
  </div>
</template>

<script>
export default {
  props: ['value'],
  data() {
    return {
      value6: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      }
    }
  },
  watch: {
    value (val) {
      this.value6 = val
    },
    value6 (val) {
      if(val) {
        val[0] = `${val[0]} 00:00:00`
        val[1] = `${val[1]} 23:59:59`
      }
      
      this.$emit('input', val)
    }
  }
};
</script>
