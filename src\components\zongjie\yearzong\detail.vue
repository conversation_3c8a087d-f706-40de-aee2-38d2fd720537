<template>
  <div>
    <back>返回</back>
    <div
      class="maindiv"
      :class="{ pb100: setupData.isUserExamine }"
      :style="{ zoom: zoom }"
    >
      <div class="owntop" :class="{ w60: isShow }">
        <div class="plantitle">
          <div>{{ titleStr.mainTitle }}</div>
          <div v-if="titleStr.subTitle" class="subtitle">
            ({{ titleStr.subTitle }})
          </div>
        </div>
        <div class="personalplan">
          <textBorder>个人{{ types[form.goalsType].label }}</textBorder>
          <addzongcom
            :layouttype="isShow ? '2' : '1'"
            :zjid="$route.query.id"
            :zjtype="$route.query.type"
          ></addzongcom>
        </div>
        <div>
          <textBorder>总结内容</textBorder>
          <div class="pt20">
            <showmodel
              :viewDataList="formLabelAlign.templateItemList"
            ></showmodel>
          </div>
        </div>
        <div>
          <textBorder>抄送人</textBorder>
          <div class="pt20">
            <ul class="uflex" v-if="formLabelAlign.copyPersons.length > 0">
              <li
                v-for="(item, index) in formLabelAlign.copyPersons"
                :key="index"
                class="pli"
              >
                <img v-if="item.logo" class="pimg" :src="item.logo" alt="" />
                <div class="noimg" v-else>
                  {{ item.name }}
                </div>
                <p class="pname">{{ item.name }}</p>
              </li>
            </ul>
          </div>
        </div>
        <div>
          <textBorder>审核记录</textBorder>
        </div>
        <div>
          <mystep :setupData="setupData" stype="look"></mystep>
        </div>

        <div class="mb20">
          <textBorder>评论</textBorder>
        </div>
        <div class="savediv">
          <el-input
            show-word-limit
            maxlength="400"
            clearable=""
            ref="elRef"
            class="inputcommont"
            v-model.trim="value"
            placeholder="请输入评论内容"
          ></el-input>
          <el-button @click="commentButton()" class="savebutton" type="primary"
            >发送</el-button
          >
        </div>

        <!-- 评论列表 -->
        <el-collapse-transition>
          <div v-show="expand">
            <comment
              @reply="reply"
              v-for="(item, index) in commentArr"
              :key="index"
              :item="item"
              @deleteAction="deleteAction"
            ></comment>
          </div>
        </el-collapse-transition>

        <div class="buttoncursor" @click="expandOrShou()">
          <img
            v-show="!expand && commentArr.length > 0"
            src="@/assets/xia.png"
            alt=""
          />
          <span v-show="!expand && commentArr.length > 0" class="expand"
            >展开</span
          >

          <img
            v-show="expand && commentArr.length > 0"
            src="@/assets/shang.png"
            alt=""
          />
          <span v-show="expand && commentArr.length > 0" class="expand"
            >收起</span
          >
        </div>
        <replyDialog
          ref="replyDialog"
          :visible.sync="replayDialogVisible"
          :title="replayTitle"
          @updateVisible="updateReplayVisible"
          @replyData="replyData"
        ></replyDialog>
      </div>

      <div class="rightdiv" :class="{ w40: isShow }">
        <div v-if="isShow" class="reviewmask">
          <planreview
            ref="planreview"
            v-if="reviewType == 'planreview'"
            :viewData="viewData"
            :date="planTime"
            :page="pageData"
            @closereview="closereview"
            :plantype="`${+form.goalsType - 3}`"
          ></planreview>
          <summaryreview
            v-if="reviewType == 'summaryreview'"
            :viewData="viewData"
            :date="planTime"
            :page="pageData"
            @closereview="closereview"
            :plantype="form.goalsType"
          ></summaryreview>
        </div>
        <div v-else class="tright">
          <div class="bbtn" id="planreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            计划回顾
          </div>
          <div class="bbtn" id="summaryreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            总结回顾
          </div>
        </div>
      </div>
    </div>

    <div class="bottomdiv" v-if="setupData.isUserExamine">
      <el-button
        class="clasbtn"
        type="primary"
        dblclick
        @click="changeLogsStatus(2)"
        >确认查看</el-button
      >
    </div>

    <el-dialog
      title="选择回顾时间"
      :visible.sync="dialogPlanVisible"
      width="340px"
      center
      :before-close="handlePlanClose"
    >
      <span>
        <el-form class="mt30" label-width="85px">
          <el-form-item
            :label="`${
              types[
                reviewType == 'planreview'
                  ? +form.goalsType - 3
                  : form.goalsType
              ].label
            }：`"
          >
            <el-date-picker
              class="definput"
              :picker-options="pickerOptions"
              @change="changeDatePicker"
              v-model="planTime"
              :type="types[form.goalsType].type"
              :format="types[form.goalsType].format"
              placeholder="请选择"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextAction">下一步</el-button>
      </span>
    </el-dialog>

    <esign
      :dialogVisible="dialogVisible"
      @updateVisible="updateVisible"
      @submitAction="submitAction"
      :hasLastSign="isHas"
    ></esign>
  </div>
</template>

    <script>
import { sendSave, commentList, commentDelete } from '@/api/visit/index'
import comment from '@/components/clientMaintenance/followVisit/components/comment.vue'
import replyDialog from '@/components/common/replyDialog.vue'
import textBorder from '@/components/common/textBorder.vue'
import back from '@/components/common/back.vue'
import mystep from '@/components/common/mystep.vue'
import planreview from '@/components/common/planreview.vue'
import summaryreview from '@/components/common/summaryreview.vue'
import { deepClone } from '@/utils/tools.js'
import { getWeek, getPlanTime } from '@/utils/index'
import { toCheckList, check, jiHuaInfo, queryReviewGoals } from '@/api/goal'
import showmodel from '@/components/zongjie/temmodel/showmodel.vue'
import addzongcom from '../components/addzongcom.vue'
import esign from '@/components/common/esign.vue'
export default {
  components: {
    back,
    textBorder,
    planreview,
    summaryreview,
    showmodel,
    mystep,
    addzongcom,
    esign,
    comment,
    replyDialog,
  },
  data() {
    return {
      value: '',
      replayDialogVisible: false,
      replayTitle: '',
      commentArr: [],
      expand: false,
      parentId: 0,
      titleStr: '',
      isChange: false,
      isHas: false,
      pickerOptions: {
        disabledDate: this.endPickerTime,
      },
      isShow: false,
      isLoading: false,
      dialogPlanVisible: false,
      getUrl: `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`,
      year: new Date(this.$route.query.time).getFullYear(),
      tableData: [],
      fileList: [],
      types: {
        1: {
          name: '年计划',
          label: '年度计划',
          type: 'year',
          format: 'yyyy',
        },
        2: {
          name: '月计划',
          label: '月度计划',
          type: 'month',
          format: 'yyyy-M',
        },
        3: {
          name: '周计划',
          label: '周计划',
          type: 'week',
          format: 'yyyy-W',
        },
        4: {
          name: '年总结',
          label: '年度总结',
          type: 'year',
          format: 'yyyy',
        },
        5: {
          name: '月总结',
          label: '月度总结',
          type: 'month',
          format: 'yyyy-M',
        },
        6: {
          name: '周总结',
          label: '周总结',
          type: 'week',
          format: 'yyyy 第 W 周',
        },
      },
      form: {
        goalsType: this.$route.query.type + '',
        year: '',
        month: '',
        week: '',
        pageNum: 1,
        pageSize: 1,
      },
      reviewType: '',
      goalDetailList: [
        {
          goalType: 1,
          goalTypeName: '新增客户',
          goalTypeKey: 'newCustomers',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 2,
          goalTypeName: '新增拜访',
          goalTypeKey: 'newVisit',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 3,
          goalTypeName: '新增机会',
          goalTypeKey: 'newOpportunity',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 4,
          goalTypeName: '新增合同',
          goalTypeKey: 'newContract',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 5,
          goalTypeName: '合同金额（万元）',
          goalTypeKey: 'contractAmount',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 6,
          goalTypeName: '回款金额（万元）',
          goalTypeKey: 'contractReturnAmount',
          sum: 0,
          year: this.year,
        },
      ],
      keyDict: {
        1: 'newCustomers',
        2: 'newVisit',
        3: 'newOpportunity',
        4: 'newContract',
        5: 'contractAmount',
        6: 'contractReturnAmount',
      },
      nameDict: {
        1: '新增客户',
        2: '新增拜访',
        3: '新增机会',
        4: '新增合同',
        5: '合同金额（万元）',
        6: '回款金额（万元）',
      },
      options: [],
      labelPosition: 'right',
      formLabelAlign: {
        newCustomers: null,
        newContract: null,
        newVisit: null,
        newOpportunity: null,
        contractAmount: null,
        contractReturnAmount: null,
        year: this.year,
        personalGoalsUnitList: [],
        personalGoalsDetailList: [],
        copyPersons: [],
      },
      unitGoalItem: [
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'informationAmount',
          unitgoalTypeName: '信息化业绩（万元）',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'teachingMaterialAmount',
          unitgoalTypeName: '教材业绩金额（万元）',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'totalContract',
          unitgoalTypeName: '合同数量',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'totalVisit',
          unitgoalTypeName: '拜访次数',
          goalNum: '',
          year: '',
        },
      ],
      keyNames: {
        informationAmount: '信息化业绩（万元）',
        teachingMaterialAmount: '教材业绩金额（万元）',
        totalContract: '合同数量',
        totalVisit: '拜访次数',
      },
      unitGoalItem2: [],
      unitGoalList: [],
      unitIds: [],
      unitDialogVisible: false,
      currentYear: '',
      planTime: '',
      setupData: {},
      viewData: {},
      pageData: {},
      dialogVisible: false,
      statusPar: {
        relateId: this.$route.query.id,
        logsStatus: '',
        url: '',
      },
      zoom: 1,
    }
  },
  mounted: function () {},
  created() {
    this.unitGoalItem2 = deepClone(this.unitGoalItem)
    this.loadInfo()
    this.commentListData()
    // var zoom = (window.innerWidth/1920.0) + 0.1
    // this.zoom = zoom>1? 1 : zoom
    // if (window.innerWidth<1350) {
    //   this.zoom = 0.8
    // }
  },

  methods: {
    commentButton() {
      if (this.value == '') {
        this.msgError('请输入评论内容')
        return
      }
      this.save(this.value)
    },

    save(comment) {
      let params = {
        visitId: this.$route.query.id,
        comment: comment,
        commentType: 4,
        parentId: this.parentId || 0,
      }
      sendSave(params)
        .then((res) => {
          if (res.status == 0) {
            this.commentListData()
            this.expand = true
            this.value = ''
          }
          this.parentId = 0
        })
        .catch((err) => {
          this.parentId = 0
        })
    },

    commentListData() {
      commentList({ visitId: this.$route.query.id }).then((res) => {
        if (res.status == 0) {
          this.commentArr = res.data
        }
      })
    },

    deleteAction(commentId) {
      commentDelete({ id: commentId })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.commentListData()
          } else {
            this.$message({
              type: 'error',
              message: '删除失败',
            })
          }
        })
        .catch((err) => {})
    },

    expandOrShou() {
      this.expand = !this.expand
      if (this.expand && !this.commentArr.length) {
        this.commentListData()
      }
    },

    reply(data) {
      this.parentId = data.id
      this.updateReplayVisible(true)
      this.replayTitle = `回复@${data.name}`
    },

    updateReplayVisible(val) {
      this.replayDialogVisible = val
    },
    replyData(data) {
      this.save(data)
      this.$refs.replyDialog.closeAction()
    },

    loadOldData(data) {
      data.createBy = this.formLabelAlign.createBy
      queryReviewGoals(data)
        .then((result) => {
          if (result.data && result.data.length > 0) {
            this.viewData = result.data
            this.pageData = result.page
            this.isShow = true
            this.dialogPlanVisible = false
          } else {
            this.$message({
              type: 'error',
              message: '暂无相关回顾可查看',
            })
          }
        })
        .catch((err) => {})
    },
    loadInfo() {
      jiHuaInfo({
        id: this.$route.query.id,
        type: 1,
      })
        .then((result) => {
          this.formLabelAlign = result.data
          this.loadAuditList()
          this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList)
          this.titleStr = this.generateTitleStr()
        })
        .catch((err) => {})
    },
    generateTitleStr() {
      const createByName = this.$route.query.createByName || ''
      const planTime = getPlanTime(this.formLabelAlign, this.form.goalsType)
      const planType = this.types[this.form.goalsType].name
      const timeRange = this.$route.query.timeRange || ''

      let mainTitle = `${createByName}${planTime}${planType}`
      let subTitle = timeRange

      return {
        mainTitle,
        subTitle,
      }
    },
    // 计划审核
    updateVisible(val) {
      this.dialogVisible = val
    },
    submitAction(fileData) {
      if (fileData) {
        this.statusPar.url = fileData.url
      }
      check(this.statusPar)
        .then((result) => {
          if (result.data) {
            this.updateVisible(false)
            this.loadAuditList()
            this.$message({
              type: 'success',
              message: result.msg,
            })
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    changeLogsStatus(data) {
      this.statusPar.logsStatus = data
      this.dialogVisible = true
    },
    loadAuditList() {
      toCheckList({ id: this.$route.query.id })
        .then((result) => {
          this.setupData = result.data[0]
          let checkList = result.data.slice(1, result.data.length)
          this.setupData.examineVoList = checkList
          this.setupData.examineVoList.forEach((element) => {
            if (element.isStatus) {
              this.setupData.isUserExamine = true
            }
            if (element.logsStatus == 1) {
              this.setupData.activeIndex = element.logsSort
              this.isHas = element.url.length > 0 ? true : false
              this.statusPar.url = element.url
              return
            }
          })
          if (!this.setupData.activeIndex && this.formLabelAlign.status == 3) {
            this.setupData.activeIndex = this.setupData.examineVoList.length + 1
          }
        })
        .catch((err) => {})
    },

    // 总结限制未来时间选中
    endPickerTime(time) {
      if (this.reviewType == 'planreview') {
        return false
      }
      const today = new Date()
      return time > today
    },

    changeDatePicker(date) {
      var newDate = new Date(date)
      this.form.year = newDate.getFullYear()
      if (this.form.goalsType == '2' || this.form.goalsType == '5') {
        this.form.month = newDate.getMonth() + 1
      } else if (this.form.goalsType == '3' || this.form.goalsType == '6') {
        var time = getWeek(newDate)
        var times = time.split('-')
        this.form.year = times[0]
        this.form.week = times[1]
      }
    },
    handlePlanClose() {
      this.dialogPlanVisible = false
      this.planTime = ''
    },
    nextAction() {
      if (this.planTime) {
        if (this.reviewType == 'planreview') {
          var fdata = Object.assign({}, this.form)
          fdata.goalsType = +fdata.goalsType - 3
          this.loadOldData(fdata)
        } else {
          this.loadOldData(this.form)
        }
      } else {
        this.$message({
          type: 'info',
          message: `请选择${this.types[this.form.goalsType].label}`,
        })
      }
    },
    //  获取到回顾信息后，再打开回顾页面
    openreview() {
      this.isShow = true
      this.dialogPlanVisible = false
    },
    closereview() {
      this.isShow = false
      this.reviewType = ''
    },
    getUnitGoalList(array) {
      this.unitGoalList = []
      this.unitIds = []
      var keys = [
        'informationAmount',
        'teachingMaterialAmount',
        'totalContract',
        'totalVisit',
      ]
      array.forEach((element, idx) => {
        var list = []
        this.unitIds.push(element.unitId)
        keys.forEach((key) => {
          var item = {
            id: element.id,
            index: idx,
            unitId: element.unitId,
            unitName: element.unitName,
            year: element.year,
            goalNum: element[key],
            unitgoalType: key,
            unitgoalTypeName: this.keyNames[key],
          }
          list.push(item)
        })
        this.unitGoalList.push(list)
      })
    },
    showView(e) {
      this.planTime = ''
      this.dialogPlanVisible = true
      this.reviewType = e.target.id
    },
    // 单位目标合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (columnIndex === 0 && rowIndex === 0) {
          return {
            rowspan: 4,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
  },
}
</script>
    <style lang="scss" scoped>
.mb20 {
  margin-bottom: 20px;
}
.inputcommont {
  width: calc(100% - 88px);
  margin-right: 16px;
  margin-bottom: 10px;
}
.inputcommont /deep/ .el-input__inner {
  height: 34px !important;
  line-height: 34px !important;
}
.savebutton {
  width: 72px;
  padding: 9px 5px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
}
.buttoncursor {
  cursor: pointer;
  display: inline-block;
}

.expand {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
}
.plantitle {
  height: 30px;
  width: 100%;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
  line-height: 23px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.pname {
  text-align: center;
}
.noimg {
  width: 48px;
  height: 48px;
  line-height: 48px;
  border-radius: 8px;
  background-color: #4285f4;
  text-align: center;
  color: #fff;
  font-size: 12px;
}
.uflex {
  display: flex;
  flex-wrap: wrap;
}
.closeimg {
  position: absolute;
  right: -8px;
  top: -8px;
  cursor: pointer;
}
.pli {
  margin-right: 24px;
  margin-bottom: 24px;
  position: relative;
}
.pimg {
  width: 48px;
  height: 48px;
  border-radius: 8px 8px 8px 8px;
}
.pflex {
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
}
.adddi {
  width: 44px;
  height: 44px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d6d6d6;
  text-align: center;
  line-height: 44px;
  font-size: 18px;
  color: #d6d6d6;
  cursor: pointer;
}
.bottomdiv {
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  height: 100px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.16);
  border-radius: 12px 12px 0px 0px;
  z-index: 2;
}
.clasbtn {
  margin-right: 20px;
  padding: 10px;
  width: 100px;
}
.mystep /deep/.el-step__title {
  font-size: 12px !important;
  padding-left: 4px;
  line-height: 20px !important;
}
.headdiv {
  margin-top: 10px;
  display: flex;
}
.tablebox {
  height: 337px;
  overflow-x: hidden;
  padding-right: 10px;
  margin: 16px 0;
}
.myform /deep/.el-form-item {
  margin-bottom: 0px !important;
}
.myform {
  margin: 16px 0;
}
.mr {
  margin-left: 20px;
}
.upload-demo {
  margin-top: 20px;
}
.pd20 {
  padding: 20px 0;
}
.tright {
  text-align: center;
}

.maindiv {
  display: flex;
  padding-top: 20px;
}
.pb100 {
  padding-bottom: 100px;
}
.rightdiv {
  width: 20%;
  background-color: rgba($color: #000000, $alpha: 0);
  margin-left: 20px;
  border-radius: 10px;
}
.w40 {
  width: 50%;
  background-color: white;
}
.submitmask {
  width: 100%;
  text-align: center;
  margin-top: 20px;
}
.textAlign /deep/.el-input__inner {
  text-align: center !important;
}
.flexcss {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.textAlignRight {
  text-align: right;
}
.width200 {
  width: 200px;
  margin-bottom: 16px;
}
.definput /deep/.el-input-group__append {
  background-color: #f6f7fb !important;
  color: #333333 !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.rtextcss {
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  color: #999999;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.owntable /deep/ td,
th {
  padding: 8px 0 !important;
}
.owntable /deep/ th {
  background-color: #f6f7fb !important;
}
.owntable /deep/ .cell {
  font-size: 14px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400 !important;
  color: #333333 !important;
}
.mubiao {
  background: #fff;
  margin-top: 10px;
  padding: 24px 20px;
  min-height: 500px;
  box-sizing: border-box;
}
.inputcss {
  height: 36px !important;
}
/deep/ .el-input__inner {
  line-height: 36px !important;
  height: 36px;
}
.deleteimg {
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.tablebox {
  height: 337px;
  overflow-x: hidden;
  padding-right: 10px;
}
.asbu {
  position: absolute;
  right: 0;
  top: 0;
}
.rela {
  position: relative;
  margin-bottom: 24px;
}
/deep/.el-input-group__append {
  padding: 0 12px !important;
}
.mt {
  margin-top: 20px;
}
.elform {
  width: 100%;
  .el-form-item {
    margin-bottom: 20px !important;
  }
}
.bbtn {
  height: 48px;
  line-height: 48px;
  background-color: #dfeafd;
  font-size: 16px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  letter-spacing: 1px;
  border-radius: 4px;
  cursor: pointer;
}
.bbtn:hover {
  background-color: #4285f4;
  color: white !important;
}
.bbtn + .bbtn {
  margin-top: 10px;
}
.owntop {
  width: 80%;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
}
.w60 {
  width: 50%;
}
.personalplan {
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  box-sizing: border-box;
  margin-top: 48px !important;
}
.unitplan {
  height: 260px;
  overflow: auto;
  flex: 1;
  background: #fff;
  box-sizing: border-box;
  margin: 20px 0;
}

.progress /deep/.el-progress-bar__outer {
  height: 4px !important;
  background-color: #dfeafd;
}
.progress /deep/.el-progress-bar__inner {
  background-color: #5a98ff;
}
.progress /deep/.el-progress-bar {
  margin-right: 0px;
}
</style>
