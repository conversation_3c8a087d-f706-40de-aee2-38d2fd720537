<!-- 适用于员工管理 地区级联 -->
<template>
  <el-form-item>
    <el-cascader
      clearable
      ref="cascaderAddr"
      class="input-230"
      placeholder="请选择地区"
      change-on-select
      v-model="myValue"
      :options="options"
      filterable
      @change="handleChange"
      :props="handleprops">
      </el-cascader>
  </el-form-item>
</template>

<script>
export default {
  props: ['value'],
  data() {
    return {
      myValue: [],
      handleprops: {
        value: 'id',
        label: 'name'
      },
      options: [],
      getid: []
    };
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    handleChange (val) {
      this.$emit('input', val)
    },
    async getDataList () {
      let res = await this.$axios.get('/sf/business/area/areaTree')
      if (res.status === 0) {
        this.options = res.data;
      }
    }
  },
  created () {
    this.myValue = this.value || ''
    this.getDataList()
  }
};
</script>
