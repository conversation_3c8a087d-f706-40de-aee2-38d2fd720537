export const formConf = {
  formRef: 'elForm',
  formModel: 'formData',
  size: 'medium',
  labelPosition: 'right',
  labelWidth: 100,
  formRules: 'rules',
  disabled: false,
  formBtns: true
}

export const inputComponents = [
  {
    __config__: {
      label: '单行文本',
      tag: 'el-input',
      required: false,
      layout: 'colFormItem',
      iconUrl: require('../../assets/danhang.png'),
      itemType: 1,
      itemDescribe: '',
      itemLimitNumber: '',
    },
  },
  {
    __config__: {
      label: '多行文本',
      tag: 'el-input',
      required: false,
      layout: 'colFormItem',
      iconUrl: require('../../assets/duohang.png'),
      itemType: 2,
      itemDescribe: '',
      itemLimitNumber: '',
    },
    type: 'textarea',
  },
  {
    __config__: {
      label: '附件',
      tag: 'el-upload',
      layout: 'colFormItem',
      required: false,
      buttonText: '点击上传',
      iconUrl: require('../../assets/fujian.png'),
      itemType: 3,
      itemLimitNumber: '',
      itemDescribe: ''
    },
    __slot__: {
      'list-type': true
    },
    action: 'https://jsonplaceholder.typicode.com/posts/',
  },
  // {
  //   __config__: {
  //     label: '视频',
  //     tag: 'el-upload',
  //     layout: 'colFormItem',
  //     required: false,
  //     buttonText: '点击上传',
  //     iconUrl: require('../../assets/fujian.png'),
  //     itemType: 4,
  //     itemLimitNumber: '',
  //     itemDescribe: ""
  //   },
  //   __slot__: {
  //     'list-type': true
  //   },
  //   action: 'https://jsonplaceholder.typicode.com/posts/',
  // }
]


