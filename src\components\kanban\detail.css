
.riqi {
    margin-bottom: 20px;
}

.xuanze {
    width: 210px;
    height: 34px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    margin-right: 16px;
}
.title{ 
    display: flex;
    margin-bottom: 20px;
}
.shugang1 {
    width: 3px;
    height: 16px;
    background: #4285F4;
    border-radius: 7px 7px 7px 7px;
    margin-right: 8px;
    margin-top: 5px;
}
.bi<PERSON><PERSON>{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold !important;
    font-size: 20px !important;
    color: #333333 !important;
}
.shugang {
    width: 3px;
    height: 12px;
    background: #4285F4;
    border-radius: 7px 7px 7px 7px;
}
.niandu {
    height: 299px;
    border-radius: 8px;
    margin-bottom: 12px;
}
.user {
    height: 217px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
    text-align: center;
}
.name{
    font-family: <PERSON> YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    margin-top: 12px;
}
.bumen {
    max-height: 56px;
    background: #F6F7FB;
    border-radius: 4px 4px 4px 4px;
    margin: 16px 16px 24px 17px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #4285F4;
    padding: 10px 12px 10px 12px;
}

.kuai {
    height: 217px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
    padding: 16px;
}
.toptitle {
    display: flex;
}
.toptop {
    justify-content: space-between; 
}
.shugang {
    width: 3px;
    height: 12px;
    background: #4285F4;
    border-radius: 7px 7px 7px 7px;
    margin-right: 8px;
    margin-top: 4px;
}
.wenzi {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    flex-grow: 1;
}
.icon {
    text-align: center;
    margin-top: 32px;
}
.number{
    color: #999999;
    margin-top: 16px;
    text-align: center;
}

.qiandan{
    color: #999999;
    margin-top: 10px;
}
.number-style {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
}
.number-style1 {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
    padding-top: 8px;
}
.dan {
    display: flex;
    justify-content: center;
    align-items: center;
}
.nowyear {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 21px;
}
.nowyear1 {
    font-family: Roboto, Roboto;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
}
.number-style2{
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
    margin-right: 2px;
}
.lastyear {
    margin: 0;
}
.niandu1 {
    height: 311px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    margin-bottom: 12px;
}
.kuai1 {
    height: 229px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
    padding: 16px;
    display: flex; 
    justify-content: space-between;
    
}


.number-styles {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
}
.lastyears {
    display: flex;
    color: #999999;
    justify-content: center;
    margin-top: 24px;
}

.number-style3 {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
}
.zeng {
    display: flex;
    justify-content: center;
    margin-top: 12px;
}
@media (max-width: 1384px) {
    .lastyear,.zengzhang,.wenzi,.bumen,.nowyear1 {
        font-size: 12px !important; /* 减小字体大小 */
    }
    .baifenbi {
        font-size: 22px;
    }
    .number-style3,.number-style1 {
        font-size: 14px;
    }
    .number-style,.number-style2{
        font-size: 14px;
    }

}
@media (max-width: 1585px) {
    .lastyear,.zengzhang,.wenzi,.bumen,.nowyear1 {
        font-size: 14px !important; /* 减小字体大小 */
    }
    .number-style,.number-style2{
        font-size: 14px;
    }

}
@media (max-width: 1466px) {
    .lastyear,.zengzhang,.wenzi,.bumen,.nowyear1 {
        font-size: 13px !important; /* 减小字体大小 */
    }
    .number-style,.number-style2{
        font-size: 13px;
    }

}
@media (max-width: 1410px) {
    .lastyear,.zengzhang,.wenzi,.bumen,.nowyear1 {
        font-size: 12px !important; /* 减小字体大小 */
    }
    .number-style,.number-style2{
        font-size: 12px;
    }

}
@media (max-width: 1350px) {
    .lastyear,.zengzhang,.wenzi,.bumen,.nowyear1 {
        font-size: 11px !important; /* 减小字体大小 */
    }
    .number-style,.number-style2{
        font-size: 11px;
    }

}
.zengzhang {
    font-family: Roboto, Roboto;
    font-weight: 400;
    font-size: 16px;
    margin-top: 5px;
}
.xiajiang {
    font-family: Roboto, Roboto;
    font-weight: 400;
    font-size: 16px;
    color: #56C36E;
    margin-top: 5px;
}
.baifenbi {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 24px;
}
.baifenbi2 {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 24px;
    color: #56C36E;
}
.jiantou {
    width: 16px;
    height: 16px;
    margin-left: 2px;
}
.juzhong {
    text-align: center;
    margin-top: 50px;
}

.echarts-pie {
    width: 160px;
    height: 160px;
    margin-top: -30px; 
}

.lastyearss{
    display: flex;
    color: #999999;
    justify-content: center;
    margin-top: 24px;
}