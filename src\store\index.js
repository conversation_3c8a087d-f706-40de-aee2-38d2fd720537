import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import app from './modules/app'
import settings from './modules/settings'
import user from './modules/user'
import permission from './modules/permission'
import cus from './modules/cus'
import bookusage from './modules/bookusage'
import task from './modules/task'
Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    app,
    settings,
    user,
    permission,
    cus,
    bookusage,
    task,
  },
  getters
})
