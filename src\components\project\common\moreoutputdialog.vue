<template>
    <div>
      <el-dialog
      title="工时和产出详情"
      class="mytable"
      :visible.sync="dialogVisible"
      fullscreen
      @close="handleClose">
      <el-form class="myform" inline >
          <el-form-item>
            <el-input class="definput" v-model="pageBean.taskName" placeholder="请输入任务名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-input class="definput" v-model="pageBean.createByName" placeholder="请输入人员名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="产出类型：">
            <el-select v-model="pageBean.workType" placeholder="请选择" clearable>
              <el-option 
              v-for="item in workTypeOptions" 
              :key="item.id" 
              :label="item.name"
              :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="部门名称：">
            <div class="unitbtn mt definput w1" @click="chooseDepartment">
              <span v-if="pageBean.departmentIds" class="deffont">{{ searchdepartmentNames }}</span>
              <span v-else>请选择</span>
              <i
                v-if="searchdepartmentNames"
                @click.stop="cleardata"
                class="rcenter el-icon-circle-close"
              />
              <i v-else class="rcenter el-icon-arrow-down" />
            </div>
          </el-form-item>
          <el-form-item label="产出时间：">
            <datepicker @submitAction="submitAction"></datepicker>
          </el-form-item>
          <el-form-item>
            <el-button class="defaultbtn" type="primary" icon="el-icon-search"  @click="handleSearch">搜索</el-button>
          </el-form-item>
          <el-form-item class="right">
            <el-button class="defaultbtn" type="primary" icon="el-icon-download" :loading="isDownload" @click="downloadAction">导出筛选数据</el-button>
          </el-form-item>
      </el-form>
      <el-table
          class="mtable mt12"
          size="small"
          :height="`calc(100vh - 250px)`"
          :data="dataList"
          v-loading="isLoading"
          default-expand-all
          :tree-props="{children: 'childrenList'}"
          border>
          <el-table-column
            prop="taskName"
            label="任务名称"
            width="180px"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            prop="workTime"
            label="日期"
            width="120">
          </el-table-column>
          <el-table-column
            prop="content"
            label="内容"
            show-overflow-tooltip>
          </el-table-column>
          
          
          <el-table-column
            prop="workHours"
            label="消耗工时"
            width="150">
          </el-table-column>
          <el-table-column
            prop="workType"
            label="产出类型"
            width="100">
            <template slot-scope="scope">
              {{workTypeOptionMap[scope.row.workType]}}
            </template>
          </el-table-column>
          <el-table-column
            prop="duration"
            label="产出时长"
            width="100">
          </el-table-column>
          <el-table-column
            prop="createByName"
            label="创建人"
            width="100">
          </el-table-column>
          <el-table-column
            prop="departmentName"
            label="部门"
            width="150">
          </el-table-column>
        </el-table>
        <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                  @updatePageNum="handleCurrentChange"></page>
      </el-dialog>

      <departmentDialog
        ref="deRef"
        dType="1"
        :visible.sync="dialogDepartmentVisible"
        @updateVisible="updateSystemVisible"
        @submitData="submitData"
      >
      </departmentDialog>
    </div>
</template>
  
<script>
import page from '@/components/common/page.vue';
import { queryProjectWorkHoursList,exportProjectWorkHoursList } from '@/api/project/index'
import departmentDialog from '@/components/common/departmentDialog.vue'
import datepicker from '@/components/common/datepicker'
import { workTypeOptionMap,workTypeOptions } from "@/utils/dict";
import {  downloadExcelFile } from '@/utils/tools'
  export default {
      components: {
          page,
          departmentDialog,
          datepicker
      },
      props: {
          projectId: {
              type: String,
              default: ''
          }
      },
      data() {
          return {
              workTypeOptions,
              workTypeOptionMap,
              dataList: [],
              isLoading:false,
              dialogVisible:false,
              pageBean: {
                  workType:undefined,
                  projectId: '',
                  pageNum: 1,
                  pageSize: 10,
                  taskName: '',
                  createByName: '',
                  startTime: '',
                  endTime: '',
                  time: '',
              },
              total: 0,
              dialogDepartmentVisible: false,
              searchdepartmentNames: '',
              selDepartmentData: [],
              isDownload:false,
          }
      },
      created() {
          this.pageBean.projectId = this.projectId;
      },
      methods: {
          show(){
            this.dialogVisible = true;
            this.loadData()
          },
          chooseDepartment() {
            this.dialogDepartmentVisible = true
            this.$refs.deRef.loadData()
            this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
          },
          updateSystemVisible(val) {
            this.dialogDepartmentVisible = val
          },
          submitData(data) {
            this.dialogDepartmentVisible = false
            this.selDepartmentData = data
            let departmentIds = data.map((item) => item.id)
            let departmentNames = data.map((item) => item.name)
            this.pageBean.departmentIds = departmentIds.join(',')
            this.searchdepartmentNames = departmentNames.join(',')
            this.pageBean.departmentName = this.searchdepartmentNames
          },
          cleardata() {
            this.searchdepartmentNames = ''
            this.pageBean.departmentIds = ''
            this.pageBean.departmentName = ''
            this.selDepartmentData = []
          },
          submitAction(type, dateRange) {
            if (type == 11) {
              if (dateRange) {
                this.pageBean.startTime = dateRange[0]
                this.pageBean.endTime = dateRange[1]
              } else {
                this.pageBean.startTime = ''
                this.pageBean.endTime = ''
              }
              this.pageBean.time = ''
            } else {
              this.pageBean.time = type
              this.pageBean.startTime = ''
              this.pageBean.endTime = ''
            }
          },
          loadData() {
            this.isLoading = true;
              queryProjectWorkHoursList(this.pageBean).then((result) => {
                  this.dataList = result.data;
                  this.total = result.page.total;
                  this.isLoading = false;
              }).catch((err) => {
                  this.isLoading = false;
              });
          },
          handleClose() {
              this.dialogVisible = false;
              this.pageBean = {
                  projectId: this.projectId,
                  pageNum: 1,
                  pageSize: 10,
                  taskName: '',
                  createByName: '',
                  workType:undefined,
                  startTime: '',
                  endTime: '',
                  time: '',
              }
          },
          handleSearch() {
            this.pageBean.pageNum = 1;
            this.loadData();
          },
          handleCurrentChange(page) {
              this.pageBean.pageNum = page;
              this.loadData();
          },
          downloadAction(){
            this.isDownload = true;
            exportProjectWorkHoursList(this.pageBean).then((result) => {
              this.isDownload = false;
              downloadExcelFile(result,`${this.$route.query.name}工时产出.xlsx`)
            }).catch((err) => {
              this.isDownload = false
            });
          },
      }
  }
</script>
  
<style scoped>
.overdue {
color: #F45961;
}
.bbtn {
color: #55C36E;
}
.unitbtn {
  cursor: pointer;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 15px;
  height: 32px;
  line-height: 32px;
  position: relative;
}

.w1 {
  width: 200px;
}
.mt{
  margin-top: 5px;
}
.rcenter {
  position: absolute;
  top: 0;
  right: 10px;
  line-height: 32px;
  font-size: 14px;
  color: #c0c4cc;
}
</style>