<template>
  <div class="mod-config">
    <back>返回</back>
    <div class="el-rowone">
      <div class="wcolor clrp">
        <div class="grid-content bg-purple">
          <div class="clearfix tdiv">
            <span class="zutext">组织架构</span>
            <!-- <el-button
              class="right defaultbtn"
              type="primary"
              icon="el-icon-help"
              @click="showUnitImg()"
              >
              架构图预览
            </el-button> -->
            <el-button
              class="right defaultbtn"
              type="primary"
              icon="el-icon-share"
              @click="showTreeChart()"
            >
              树形架构图
            </el-button>
          </div>
          <div>
            <el-button
              class="right defaultbtn"
              size="mini"
              type="text"
              @click="addFirst()"
              ><i class="fa fa-plus"></i> 添加一级结构
            </el-button>
          </div>
          <el-tree
            node-key="id"
            class="filter-tree"
            :data="tagTypeData"
            default-expand-all
            :props="defaultProps"
            ref="tagTypeTree"
            accordion
            :highlight-current="true"
            :expand-on-click-node="false"
            :render-content="renderContent"
            @node-click="getTreeNode"
          >
          </el-tree>
        </div>
      </div>
      <div class="cpding">
        <div class="tablecss wcolor" ref="configtypetable">
          <div class="page-header">
            <el-form
              size="small"
              class="clearfix"
              ref="form"
              :model="pageBean"
              :inline="true"
            >
              <el-form-item label="客户级别：">
                <el-select
                  clearable=""
                  class="des"
                  popper-class="removescrollbar"
                  v-model="pageBean.customerLevel"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="客户名称：">
                <el-input
                  v-model="pageBean.customerName"
                  clearable
                  placeholder="请输入客户名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="">
                <el-button
                  class="search btn defaultbtn"
                  type="primary"
                  icon="el-icon-search"
                  @click="onSearch"
                  >搜索
                </el-button>
              </el-form-item>
              <el-form-item class="right mr0">
                <el-button
                  icon=" el-icon-thumb"
                  class="addunit btn defaultbtn"
                  size="small"
                  type="primary"
                  @click="addUnit"
                  >选择客户</el-button
                >
              </el-form-item>
              <el-form-item class="right">
                <el-button
                  icon="el-icon-plus"
                  v-isShow="'crm:controller:customer:save'"
                  class="addunit btn defaultbtn"
                  size="small"
                  type="primary"
                  @click="addCustomer"
                  >新建客户</el-button
                >
              </el-form-item>
            </el-form>
          </div>
          <el-table
            class="mytable"
            :data="dataList"
            style="width: 100%"
            v-loading="isLoading"
          >
            <el-table-column
              prop="customerName"
              label="客户名称"
              align="center"
            >
            </el-table-column>
            <el-table-column prop="unitDepartment" label="部门" align="center">
            </el-table-column>
            <el-table-column prop="duties" align="center" label="职务">
            </el-table-column>
            <el-table-column
              prop="customerLevelName"
              align="center"
              label="客户级别"
            >
              <template slot-scope="scope">
                <span
                  :class="customerLevelColors[scope.row.customerLevelName]"
                  >{{ scope.row.customerLevelName }}</span
                >
              </template>
            </el-table-column>
            <el-table-column prop="phone" align="center" label="联系方式">
            </el-table-column>
            <el-table-column label="负责人" align="center" width="160px">
              <template slot-scope="scope">
                <div v-if="scope.row.chargePersonNames.length > 0">
                  <el-tag
                    class="cuscss"
                    v-for="(item, index) in scope.row.chargePersonNames"
                    :key="index"
                    >{{ item }}</el-tag
                  >
                </div>
                <div v-else>—</div>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="chargePersonDepartment" align="center" label="负责人所在部门">
            </el-table-column> -->
            <el-table-column
              prop="edit"
              width="100"
              align="center"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button class="bBtn" type="text" @click="onEdit(scope.row)">
                  详情</el-button
                >
                <el-button
                  class="ml16 dBtn"
                  type="text"
                  @click="deleteButton(scope.row)"
                >
                  移除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <page
            :currentPage="pageBean.pageNum"
            :total="total"
            :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"
          ></page>
        </div>
      </div>
    </div>
    <AddOrUpdateTagType
      v-if="dialogVisibleOr"
      ref="addOrUpdateTagType"
      @refreshTagTypeTree="getTagTypeData"
    >
    </AddOrUpdateTagType>
    <dc-dialog
      iType="1"
      title="温馨提示"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <p class="pcc">确定要删除单位下的客户么?</p>
    </dc-dialog>
    <customSelect
      :coId="coId"
      ref="custom"
      :customVisible.sync="customVisible"
      @getCusList="getCusList"
    ></customSelect>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import AddOrUpdateTagType from './components/add-or-update-tag-type'
import page from '../../common/page.vue'
import customSelect from './components/customselect.vue'
import {
  queryStructureTreeList,
  orDelete,
  cusList,
  unitCusDele,
} from '@/api/unit'

import { getDict } from '@/utils/tools'
import { customerLevelColors } from '@/utils/dict'
export default {
  data() {
    return {
      customerLevelColors: customerLevelColors,
      customVisible: false,
      dialogVisible: false,
      dialogVisibleOr: false,
      isLoading: false,
      pageBean: {
        structureId: '',
        customerName: '',
        pageNum: 1,
        pageSize: 10,
        customerLevel: '',
      },
      options: [],
      dataForm: {
        tagTypeCode: '',
      },
      tagTypeData: [],
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateTagTypeVisible: false,
      addOrUpdateTagItemVisible: false,
      defaultProps: {
        children: 'childList',
        label: 'name',
      },
      coId: '',
    }
  },
  components: {
    AddOrUpdateTagType,
    page,
    customSelect,
    back,
  },
  methods: {
    getCusList() {
      this.getTagItemData()
    },
    deleteButton(row) {
      this.deId = row.id
      this.dialogVisible = true
    },
    async submitDialog() {
      let res = await unitCusDele({
        customerId: this.deId,
        structureId: this.coId,
      })
      if (res.status === 0) {
        this.$message({
          message: '删除成功',
          type: 'success',
        })
        this.getTagItemData()
      } else {
        this.$message.error(res.msg)
      }
      this.dialogVisible = false
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.getTagItemData()
    },
    onEdit(item) {
      this.$router.push({
        path: '/clientManagement/unit/unitsysdetail',
        query: {
          id: item.id,
          sId: this.pageBean.structureId,
        },
      })
    },
    addCustomer() {
      if (this.tagTypeData.length == 0) {
        this.$message({
          message: '请先添加组织架构',
          type: 'error',
        })
        return
      }
      this.$router.push({
        path: '/clientManagement/customer/add',
        query: {
          unitId: this.$route.query.id,
          unitName: this.$route.query.unitName,
          structureId: this.coId,
        },
      })
    },
    addUnit() {
      if (this.tagTypeData.length == 0) {
        this.$message({
          message: '请先添加组织架构',
          type: 'error',
        })
        return
      }
      let params = {
        unitId: this.$route.query.id,
        methodName: 'list',
        className: 'UnitController',
        structureId: this.coId,
      }
      this.$refs.custom.selectCustomerData(params)
      this.customVisible = true
    },
    onSearch() {
      this.pageBean.pageNum = 1
      this.getTagItemData()
    },
    async getTagTypeData() {
      let res = await queryStructureTreeList({ unitId: this.$route.query.id })
      if (res.status === 0) {
        this.tagTypeData = res.data
        if (res.data.length > 0) {
          this.$nextTick(() => {
            this.$refs.tagTypeTree.setCurrentKey(res.data[0].id)
            this.pageBean.structureId = res.data[0].id
            this.coId = res.data[0].id
            this.getTagItemData()
          })
        }
      }
    },
    getTreeNode(data) {
      this.coId = data.id
      this.pageBean.structureId = this.coId
      this.getTagItemData()
    },
    addOrUpdateTagTypeHandle(params) {
      this.dialogVisibleOr = true
      this.$nextTick(() => {
        this.$refs.addOrUpdateTagType.init(params)
      })
    },
    addFirst() {
      let params = {
        unitId: this.$route.query.id,
      }
      this.addOrUpdateTagTypeHandle(params)
    },
    deleteTagType(data) {
      this.$confirm(`确定要对[${data.name}]进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        let res = await orDelete({ id: data.id })
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
          })
          this.getTagTypeData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    renderContent(h, { node, data, store }) {
      console.log(node)
      return (
        <span class="custom-tree-node">
          <span class="sblock">{data.name}</span>
          <el-popover
            class="elP"
            placement="bottom-end"
            width="100"
            trigger="click"
          >
            {node.level < 3 ? (
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() =>
                    this.addOrUpdateTagTypeHandle({
                      parentId: data.id,
                      parentName: data.name,
                      unitId: this.$route.query.id,
                    })
                  }
                >
                  添加下级
                </el-button>
              </div>
            ) : (
              ''
            )}

            <div>
              <el-button
                size="mini"
                type="text"
                on-click={() =>
                  this.addOrUpdateTagTypeHandle({
                    id: data.id,
                    name: data.name,
                    parentId: data.parentId,
                    parentName: node.parent.data.name,
                    unitId: this.$route.query.id,
                  })
                }
              >
                编辑
              </el-button>
            </div>
            <div>
              <el-button
                size="mini"
                type="text"
                on-click={() => this.deleteTagType(data)}
              >
                删除
              </el-button>
            </div>

            <el-button
              slot="reference"
              type="text"
              size="mini"
              class="tree-icon1"
            >
              <div>
                <i class="fa fa-cog"></i>
              </div>
            </el-button>
          </el-popover>
        </span>
      )
    },
    async getTagItemData() {
      this.dataListLoading = true
      let res = await cusList(this.pageBean)
      if (res.status === 0) {
        this.dataList = res.data
        this.dataListLoading = false
        this.total = res.page.total
      } else {
        this.$message.error(`${res.msg}`)
      }
    },
    showUnitImg() {
      this.$router.push({
        path: '/clientManagement/unit/components/unitEchart',
        query: {
          id: this.$route.query.id,
          unitName: this.$route.query.unitName,
        },
      })
    },
    showTreeChart() {
      this.$router.push({
        path: '/clientManagement/unit/components/unitTreeChart',
        query: {
          id: this.$route.query.id,
          unitName: this.$route.query.unitName,
        },
      })
    },
  },

  mounted() {
    this.$refs.tagTypeTree.$el.style.height =
      Number(window.innerHeight) - 174 + 'px'
  },
  created() {
    this.getTagTypeData()
    getDict('CustomerLevel').then((res) => {
      this.options = res
    })
  },
}
</script>
<style>
.mr0 {
  margin-right: 0px !important;
}
.custom-tree-node {

  height: 26px;
  line-height: 26px;
  position: relative;
  display: block;
}

.el-popover {
  width: auto !important;
  min-width: auto;
}
.fa-gear:before, .fa-cog:before{
  position: absolute;
  top: 8px;
}
.sblock {
  display: inline-block;
  /* min-width: 160px; */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 160px;
  width: 160px;
}
</style>

<style scoped lang="scss">
.addunit + .addunit {
  margin-right: 16px;
}
/deep/.el-tree > .el-tree-node {
  position: relative;
  width: fit-content;
}

/deep/.el-tree {
  min-width: 100%;
  position: relative;
}
/deep/.el-tree-node{
  width: 100% !important;
}
/deep/ .el-tree-node__content {
  width: fit-content;
}

.search {
  margin-left: 10px;
}

.tablecss {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
}

.clrp {
  width: 260px;
  margin-right: 20px;
  min-width: 260px;
}

.cpding {
  flex: 1;
}

.el-rowone {
  display: flex;
  margin-top: 5px;
}

.wcolor {
  background: #fff;
}

.tdiv {
  line-height: 40px;
  height: 74px;
  padding: 17px 24px;
  box-sizing: border-box;
  border-bottom: 1px solid #f0f0f0;
}

.zutext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.pcc {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #666666;
  text-align: center;
  line-height: 24px;
}

.ml16 {
  margin-left: 16px;
}

.mr16 {
  margin-right: 16px;
}

.filter-tree {
  font-size: 14px;
}

.filter-tree,
.grid-contenttable {
  overflow-y: scroll;
}
</style>
