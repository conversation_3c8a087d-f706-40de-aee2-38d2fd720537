<template>
      <div class="pdfMask" v-if="visible" :class="{'fullHeight':type == 1}">
        <p class="close" @click="close"><span  class="viewer__btn viewer__close"><i class="el-icon-close"></i></span></p>
        <iframe class="pdfcss" :src="`https://wthedu.51suiyixue.com/PDF/web/viewer.html?file=${url}`" frameborder="0"></iframe>
      </div>
</template>

<script>
  export default {
    data(){
      return {
        url :"",
        visible:false,
        type:'',
      }
    },
    created(){
      console.log(this.type)
    },
    methods:{
      close(){
        this.visible = false
      }
    }
  }
</script>

<style lang="scss" scoped>
.pdfMask{
  text-align: center;
  width: calc(100% - 170px);
  height:calc(100% - 56px);
  position: fixed;
  right: 0;
  bottom:0;
  z-index: 3000;

}
.fullHeight{
  height: 100%;
}
.pdfcss{
  height: 100%;
  width: 100%;
}
.close{

}
.viewer__close {
    top: -2px;
    right: 40px;
    width: 40px;
    height: 40px;
    color: #fff;
    font-weight: bold;
    // background-color: #606266;
}
.el-icon-close{
  font-size: 20px;
  color:rgba(251,251,251,1);
    font-weight: 800;
}
.viewer__btn {
    position: absolute;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    opacity: .8;
    cursor: pointer;
    box-sizing: border-box;
    user-select: none;
}
</style>