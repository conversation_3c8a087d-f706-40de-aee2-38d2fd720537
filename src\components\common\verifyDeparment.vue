<template>
  <el-dialog
    title="温馨提示"
    width="500px"
    top="30vh"
    :modal-append-to-body="true"
    :visible.sync="dialogVisible"
    @close="handleClose"
    center
    custom-class="el-dialog-s"
    append-to-body
  >
    <template>
      <el-form class="myform" label-width="80px">
        <p class="pcc">
          由于您存在于多个部门中，请选择该数据的关联部门，该数据将归属在该部门中。
        </p>
        <el-form-item label="选择部门:">
          <el-select class="definput" v-model="form.departmentId">
            <el-option
              v-for="item in list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </template>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="submitAction">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { verifyDepartmentList } from '@/api/index'
export default {
  data() {
    return {
      list: [],
      isShowCancel: true,
      dialogVisible: false,
      form: {
        departmentId: '',
      },
    }
  },
  methods: {
    async verify() {
      var res = await verifyDepartmentList()
      if (res.data.length === 1) {
        this.$emit('submit', res.data[0].id)
        return
      }
      this.list = res.data
      this.dialogVisible = true
      return false
    },
    handleClose() {
      this.form.departmentId = ''
      this.dialogVisible = false
    },
    submitAction() {
      if (this.form.departmentId) {
        this.$emit('submit', this.form.departmentId)
        this.handleClose()
      }
    },
  },
}
</script>
<style>
.el-dialog-s {
  z-index: 3000;
}
</style>

<style lang="scss" scoped>
.myform {
  margin-bottom: 0px;
  width: 100%;
  /deep/.el-form-item {
    margin-top: 10px;
    margin-bottom: 0px;
  }
  .definput {
    width: 100% !important;
  }
}
</style>
