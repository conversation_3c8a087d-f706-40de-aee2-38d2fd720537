<template>
  <el-dialog
    :title="isEdit ? '编辑退款' : '退款'"
    class="adddetailcss"
    width="60%"
    :visible.sync="addVisible"
    append-to-body
    center
    @close="beforeClose"
  >
    <el-form
      ref="addform"
      class="refundcss myform"
      :model="form"
      :rules="rules"
      label-width="130px"
    >
      <el-row type="flex" justify="space-around" :gutter="0">
        <el-col :span="12">
          <el-form-item label="退款金额(万元)：" prop="refundAmount">
            <el-input
              class="definput"
              type="number"
              v-model="form.refundAmount"
              placeholder="请输入退款金额"
            ></el-input>
          </el-form-item>
          <el-form-item label="实际退款日期：" prop="actualRefundDate">
            <el-date-picker
              class="definput w100"
              v-model="form.actualRefundDate"
              type="datetime"
              placeholder="请选择实际退款日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="退款收款账户：" prop="refundAccount">
            <el-input
              class="definput"
              v-model="form.refundAccount"
              placeholder="请输入退款收款账户"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="币种：" prop="currency">
            <el-select
              class="definput"
              popper-class="removescrollbar"
              v-model="form.currency"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="退款方式：" prop="refundWay">
            <el-select
              class="definput"
              popper-class="removescrollbar"
              v-model="form.refundWay"
              placeholder="请选择"
            >
              <el-option
                v-for="item in refundTypes"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="退款原因：" prop="refundReason">
            <el-input
              class="definput"
              v-model="form.refundReason"
              placeholder="请输入退款原因"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item  label='交易流水号：' prop="transactionNumber">
                        <el-input  class="definput" v-model="form.transactionNumber" placeholder="请输入交易流水号"></el-input>
                    </el-form-item> -->
        </el-col>
      </el-row>
      <el-row :gutter="0">
        <el-col :span="24">
          <el-form-item label="备注：" prop="notes">
            <el-input
              maxlength="300"
              show-word-limit
              class="definput"
              type="textarea"
              :rows="5"
              v-model="form.notes"
              placeholder="请输入备注内容"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="center">
      <el-button
        class="submitbtn defaultbtn"
        type="primary"
        @click="submitAction"
        :loading="isSubmit"
        >提交</el-button
      >
    </div>
    <verifyDeparment ref="verifyDeparment" @submit="addData"></verifyDeparment>
  </el-dialog>
</template>

<script>
import {
  addContractrefund,
  contractrefundInfo,
  updateContractrefund,
} from '@/api/contract/index'
import { getDict } from '@/utils/tools'
import verifyDeparment from '@/components/common/verifyDeparment.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    contractId: {
      type: String,
      default: '',
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    verifyDeparment,
  },
  data() {
    return {
      isSubmit: false,
      rules: {
        refundAmount: [
          {
            required: true,
            message: '请输入退款金额（万元）',
            trigger: 'blur',
          },
        ],
        actualRefundDate: [
          { required: true, message: '请输入实际退款日期', trigger: 'blur' },
        ],
        // transactionNumber: [
        //     { required: true, message: '请输入交易流水号', trigger: 'blur' },
        // ],
        refundReason: [
          { required: true, message: '请输入退款原因', trigger: 'blur' },
        ],
      },
      form: {
        contractId: this.contractId,
        refundAmount: '',
        currency: '',
        actualRefundDate: '',
        refundWay: '',
        refundAccount: '',
        // transactionNumber:"",
        refundReason: '',
        notes: '',
        refundDepartmentId: '',
      },
      options: [
        {
          id: '1',
          name: '人民币',
        },
      ],
      refundTypes: [
        {
          id: 1,
          name: '现金',
        },
        {
          id: 2,
          name: '银行转账',
        },
        {
          id: 3,
          name: '微信转账',
        },
        {
          id: 4,
          name: '支付宝转账',
        },
        {
          id: 5,
          name: '其他',
        },
      ],
    }
  },
  computed: {
    addVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('updateVisible', val)
      },
    },
  },
  created() {
    getDict('Currency')
      .then((result) => {
        this.options = result
      })
      .catch((err) => {})
  },
  methods: {
    loadDetailData(id) {
      contractrefundInfo(id).then((res) => {
        if (res.status == 0) {
          res.data.currency = res.data.currency === 0 ? undefined : res.data.currency
          res.data.refundWay = res.data.refundWay === 0 ? undefined : res.data.refundWay
          this.form = { ...res.data }
        }
      })
    },
    close(){

    },
    submitAction() {
      this.$refs['addform'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.isEdit) {
          this.updateData()
        } else {
          this.$refs.verifyDeparment.verify()
        }
      })
    },
    beforeClose() {
      this.addVisible = false
      var keys = Object.keys(this.form)
      this.$refs['addform'].resetFields()
      keys.forEach((element) => {
        if (element != 'contractId') {
          this.form[element] = ''
        }
      })
    },
    addData(departmentId) {
      this.form.refundDepartmentId = departmentId
      this.isSubmit = true
      addContractrefund(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功',
            })
            this.beforeClose()
            this.$emit('submitSuccess')
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    updateData() {
      this.isSubmit = true
      updateContractrefund(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '修改成功',
            })
            this.beforeClose()
            this.$emit('submitSuccess')
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
  },
}
</script>

<style scoped>
.w100 {
  width: 100% !important;
}
.center {
  text-align: center;
}
.submitbtn {
  margin-top: 30px;
}
.refundcss /deep/.el-form-item {
  margin-bottom: 18px;
  margin-right: 0px;
}
.refundcss {
  padding-right: 4vw;
  padding-left: 2vw;
}
</style>
