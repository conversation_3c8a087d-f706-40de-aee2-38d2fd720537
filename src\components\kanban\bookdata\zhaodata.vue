<template>
  <div class="container">
    <back>返回</back>
    <div class="box">
      <el-row :gutter="20" class="rowcss">
        <el-col :span="12"
          ><div class="boxleft boxcommon">
            <el-table
              v-loading="isLoading"
              class="unittable mytable"
              :data="tableData"
              style="width: 100%"
            >
              <el-table-column prop="recruitYear" label="招生时间">
              </el-table-column>
              <el-table-column prop="planRecruitNumber" label="计划招生(人)">
              </el-table-column>
              <el-table-column prop="realityRecruitNumber" label="实际招生(人)">
              </el-table-column>
              <template slot="empty">
                <nolist></nolist>
              </template>
            </el-table>
            <div>
              <page
                :currentPage="pageBean.pageNum"
                :total="total"
                :pageSize="pageBean.pageSize"
                @updatePageNum="handleCurrentChange"
              ></page>
            </div></div
        ></el-col>
        <el-col :span="12"
          ><div class="boxright boxcommon">
            <div id="main" style="width: 100%; height: 592px"></div></div
        ></el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import * as echarts from 'echarts'
import page from '../../common/page.vue'
import { zhaoList } from '@/api/clientmanagement/unit'
import nolist from '../../common/nolist.vue'
import { queryLastFiveYearslist } from '@/api/kanban/index'
export default {
  components: {
    back,
    nolist,
    page,
  },
  data() {
    return {
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        unitSpecialtyId: this.$route.query.unitSpecialtyId,
        unitId: this.$route.query.unitId,
      },
      tableData: [],
      chartsInstance: '',
      xdata: [],
      planRecruitNumberdata: [],
      realityRecruitNumberdata: [],
      isLoading: false,
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    queryLastFiveYearslistApi() {
      queryLastFiveYearslist({
        unitSpecialtyId: this.$route.query.unitSpecialtyId,
        unitId: this.$route.query.unitId,
      }).then((res) => {
        let data = res.data
        data.forEach((item) => {
          this.xdata.push(item.recruitYear)
          this.planRecruitNumberdata.push(item.planRecruitNumber)
          this.realityRecruitNumberdata.push(item.realityRecruitNumber)
        })
        this.drawLine('main')
      })
    },
    loadData() {
      this.isLoading = true
      zhaoList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    drawLine(id) {
      this.charts = echarts.init(document.getElementById(id))
      this.charts.setOption({
        title: {
          text: '招生情况',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          itemWidth: 12,
          itemHeight: 12,
          data: ['计划招生', '实际招生'],
          icon: 'roundRect', //图例文字块的样式
          show: true,
          top: '0',
          right: '0',
          textStyle: {
            color: '#878D99', //图例文字的颜色
          },
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '8%',
          top: 70,
          containLabel: true,
        },

        xAxis: {
          name: '',
          type: 'category',
          boundaryGap: false,
          data: this.xdata,
          axisLabel: {
            show: true,
            margin: 28, // X轴文字与轴线的间距
            textStyle: {
              color: '#878D99',
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              type: 'dashed', // 设置X轴为虚线
              color: '#EBEEF5',
            },
          },
        },
        yAxis: {
          name: '',
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#EBEEF5'],
              width: 1, // 粗细
              type: 'dashed', // solid实线 dashed虚线 dotted点状
            },
          },
        },
        series: [
          {
            name: '计划招生',
            type: 'line',
            data: this.planRecruitNumberdata,
            symbolSize: 8, //折线点的大小
            itemStyle: {
              normal: {
                color: '#387FF5',
                lineStyle: {
                  color: '#387FF5',
                },
              },
            },
          },
          {
            name: '实际招生',
            type: 'line',
            data: this.realityRecruitNumberdata,
            symbolSize: 8, //折线点的大小
            itemStyle: {
              normal: {
                color: '#4BD173',
                lineStyle: {
                  color: '#4BD173',
                },
              },
            },
          },
        ],
      })
    },
  },
  mounted() {
    this.queryLastFiveYearslistApi()
  },
}
</script>

<style lang="scss" scoped>
.boxright {
  padding: 30px;
  height: 100%;
}
.boxleft {
  padding: 20px;
  height: 100%;
}
.mytable {
  margin: 0;
  height: calc(100% - 60px);
}
.rowcss {
  height: 100%;
  .el-col {
    height: 100%;
  }
}
.boxcommon {
  border-radius: 8px;
  background: #fff;
}
.container {
  height: 100%;
}
.box {
  height: calc(100% - 38px);
  margin-top: 20px;
}
</style>