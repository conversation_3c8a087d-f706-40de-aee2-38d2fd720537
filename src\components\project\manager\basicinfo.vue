<template>
    <div class="infobg">
        <div class="leftdiv">
            <div class="dlistcss">
                <ditem class="ditemcss" v-for="item in datalist" :key="item.key" :label="item.name" :value="item.value" :valueColor="item.color"></ditem>
            </div>
            <el-form class="infoform" ref="form" :model="form" label-width="120px">
                <textBorder>基本信息</textBorder>
                <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
                <el-col :span="12">
                    <el-form-item label="项目名称：" class="labeltext mulitline">
                    <span>{{ form.projectName }}</span>
                    </el-form-item>
                    <el-form-item label="客户：" class="labeltext">
                    <span>{{ form.customerName }}</span>
                    </el-form-item>
                    <el-form-item label="开始时间：" class="labeltext">
                    <span>{{ form.beginTime }}</span>
                    </el-form-item>
                    <el-form-item label="关联合同：" class="labeltext">
                    <span>{{ form.contractName }}</span>
                    </el-form-item>
                    <el-form-item label="项目类型：" class="labeltext">
                    <span>{{ form.projectTypeName }}--{{ form.projectTypeSubName }}</span>
                    </el-form-item>
                    <el-form-item label="中标公司：" class="labeltext">
                    <span>{{ form.bidwinnerName }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="项目状态：" class="labeltext">
                    <span>{{ form.projectStatusName }}</span>
                    </el-form-item>
                    <el-form-item label="客户单位：" class="labeltext">
                    <span class="column_blue">{{ form.unitName }}</span>
                    </el-form-item>
                    <el-form-item label="结束时间：" class="labeltext">
                    <span>{{ form.endTime }}</span>
                    </el-form-item>
                    <el-form-item label="项目金额(万元)：" class="labeltext">
                    <span>{{ form.projectAmount }}</span>
                    </el-form-item>
                    <el-form-item label="是否提成：" class="labeltext">
                    <span>{{form.isCommission ? '已提成':"未提成"}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="项目描述：" class="labeltext mulitline">
                    <span>{{form.description}}</span>
                    </el-form-item>
                </el-col>
                </el-row>
                <textBorder>补充信息</textBorder>
                <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
                <el-col :span="24">
                    <el-form-item label="附件：" class="labeltext">
                      <pdfview v-if="form.fileInfoList.length>0" type="1" :pdfArr="form.fileInfoList" ></pdfview>
                      <span v-else>暂无</span>
                    </el-form-item>
                    <el-form-item label="备注：" class="labeltext mulitline">
                    <span>{{ form.notes }}</span>
                    </el-form-item>
                </el-col>
                </el-row>

                <textBorder>负责与协作</textBorder>
                <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                    <el-col :span="12">
                    <el-form-item label="业务负责人：" class="labeltext">
                        <span>{{form.businessOwnerName}}</span>
                    </el-form-item>
                    </el-col>
                    <el-col :span="12">
                    <el-form-item label="项目负责人：" class="labeltext">
                        <span>{{ form.chargePersonName }}</span>
                    </el-form-item>
                    </el-col>
                </el-row>

                <textBorder>系统信息</textBorder>
                <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                    <el-col :span="12">
                        <el-form-item label="创建人：" class="labeltext">
                            <span>{{form.createByName  }}</span>
                        </el-form-item>
                        <el-form-item label="创建时间：" class="labeltext">
                            <span>{{ form.createTime }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="最后修改人：" class="labeltext">
                            <span>{{ form.modifyByName }}</span>
                        </el-form-item>
                        <el-form-item label="最后修改时间：" class="labeltext">
                            <span>{{ form.modifyTime }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="rightdiv">
            <pcommont></pcommont>
        </div>
    </div>

  </template>

  <script>
    import textBorder from '@/components/common/textBorder.vue'
    import ditem from '../common/ditem.vue';
    import pdfview from '@/components/common/pdfview.vue'
    import pcommont from './pcommont.vue';
    import { proInfo,queryTaskStatusCount } from '@/api/project/index'
    export default {
      components:{
        textBorder,
        pdfview,
        ditem,
        pcommont

      },
      data(){
       return {
         datalist:[
            {
                name:"总任务",
                key:'totalNum',
                value:"0",
                color:"#4285F4"
            },
            {
                name:"未开始",
                key:'notStartedNum',
                value:"0",
                color:"#FF8D1A"
            },
            {
                name:"进行中",
                key:'inprogressNum',
                value:"0",
                color:"#4285F4"
            },
            {
                name:"已完成",
                key:'completedNum',
                value:"0",
                color:"#55C36E"
            },
            {
                name:"暂停",
                key:'pauseNum',
                value:"0",
                color:"#A25EFA"
            },
            {
                name:"停止",
                key:'stopNum',
                value:"0",
                color:'#F45961'
            },

         ],
         vdata:{},
         form:{
          fileInfoList:[]
         }
       }
      },

      created(){
        this.load()
      },
      methods:{
        load(){
          this.loadInfo()
          this.loadData()
        },
        loadInfo(){
          proInfo(this.$route.query.id).then((result) => {
            this.form = result.data;
            var fileData = result.data.contractFileList.length>0 ? result.data.contractFileList[0] : {};
            this.$store.commit('cus/CHANGE_KEY',{key:"eContractUrl",value:fileData.url})
          }).catch((err) => {

          });
        },
        loadData(){
          // 获取项目信息
          queryTaskStatusCount(this.$route.query.id).then((result) => {
            this.vdata = result.data;
            this.datalist.map(item =>{
              item.value = this.vdata[item.key].toString()
            })
          }).catch((err) => {

          });

        }
      }
    }
  </script>

  <style lang="scss" scoped>
  .infobg{
    width: 100%;
    padding: 0;
    display: flex;
  }
  .leftdiv{
    width: 70%;
    margin-right: 10px;
    background-color: white;
  }
  .rightdiv{
    width: 30%;
    min-width: 350px;
    background-color: white;
  }
  .ditemcss{
    width: 16.6% !important;
  }
  .dlistcss{
    display: flex;
    margin: 40px 0px;
  }
  /deep/ .guancontent {
      margin-left: -18px !important;
      margin: 0;
      margin-top: 10px;
  }

  .pb12{
      padding-bottom: 12px;
  }

  </style>
  <style scoped>
  .cs{
    width: 24px;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 10;
  }
  .pdfdiv{
    cursor: pointer;
    display: inline-block;
    height: 24px;
    border-radius: 4px 4px 4px 4px;
    border: 1px dashed #4285F4;
    line-height: 4px;
    padding: 10px;
    box-sizing: border-box;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
  }
  .lh /deep/.el-form-item__content{
      line-height: 18px !important;
      padding: 0;
      padding-top: 12px;
  }
  .mulitline /deep/.el-form-item__content{
        padding-top: 10px;
        line-height: 24px;
        overflow-wrap: break-word;
    }
  .infoform /deep/.el-form-item{
    margin-bottom: 0px;
  }
  .infoform{
    padding: 0 20px;
    position: relative;
  }

  </style>
