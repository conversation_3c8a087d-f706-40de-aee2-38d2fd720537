<template>
  <div class="flex">
    <div class="backcss">
      <back class="backc">返回</back>
      <span class="namec">{{ $route.query.projectName }}</span>
    </div>
    <p>{{ titleName }}</p>
    <div class="mainbg">
      <div class="theadcss" v-if="idAndName.taskNum">
        父任务--ID：
        {{ idAndName.taskNum }}
        <span class="sml">{{ idAndName.taskName }}</span>
      </div>
      <el-form
        :rules="rules"
        :model="form"
        ref="addform"
        class="addfcss"
        label-width="130px"
      >
        <textBorder>基础信息</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="任务名称：" prop="taskName">
                <el-input
                  maxlength="50"
                  show-word-limit
                  clearable=""
                  v-model="form.taskName"
                  class="definput"
                  placeholder="请输入任务名称"
                ></el-input>
              </el-form-item>
              <el-form-item prop="beginTime" label="开始时间：">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  class="eldate"
                  v-model="form.beginTime"
                  type="date"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任务优先级：" prop="priority">
                <el-select
                  clearable=""
                  class="definput ellipsis"
                  popper-class="removescrollbar"
                  v-model="form.priority"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in grades"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="任务状态：" prop="taskStatus">
                                <el-select clearable class="definput" popper-class="removescrollbar"
                                    v-model="form.taskStatus" placeholder="请选择">
                                    <el-option v-for="item in optionsStatus" :key="item.value" :label="item.name"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item> -->
              <el-form-item prop="endTime" label="结束时间：">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  class="eldate"
                  v-model="form.endTime"
                  type="date"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="20">
              <el-form-item
                label="任务描述："
                prop="projectAmount"
                class="mb20"
              >
                <el-input
                  clearable=""
                  type="textarea"
                  :rows="5"
                  maxlength="500"
                  show-word-limit
                  v-model="form.taskContent"
                  class="definput"
                  placeholder="请在这里对任务进行简单描述"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">负责与协作</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="任务负责人：" prop="chargePerson">
                <span class="mr10">{{ form.chargePersonName }}</span>
                <el-button
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择任务负责人', 1)"
                >
                  点击选择任务负责人</el-button
                >
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任务参与人：">
                <el-tag
                  class="tagcss"
                  size="small"
                  v-for="item in xiezuolist"
                  closable
                  @close="handleTagClose(item)"
                  :key="item.id"
                  >{{ item.name }}</el-tag
                >
                <el-button
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择任务参与人', 30)"
                >
                  点击选择任务参与人</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">补充信息</textBorder>
        <div class="pt20 bbline">
          <el-form-item label="附件：">
            <upload2
              ref="upload2"
              @submitImg="submitPdf"
              accept=".pdf"
              :fileList="fileListPdf"
            >
              <span class="studiocss">
                <img src="../../../../assets/img/file_icon.png" />
                <span class="uploadtext deffont">点击上传附件</span>
              </span>
              <template slot="ptip">
                <p>只能上传pdf文件</p>
              </template>
            </upload2>
          </el-form-item>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="20">
              <el-form-item label="备注：" class="mb20">
                <el-input
                  maxlength="300"
                  show-word-limit
                  v-model="form.notes"
                  class="definput"
                  type="textarea"
                  rows="4"
                  placeholder="请输入备注"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="pt20 btncenter">
          <el-form-item>
            <el-button
              :disabled="disabled"
              class="w98 btn_h42"
              type="primary"
              @click="saveButton"
              v-dbClick
              >保存
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :filtrationList="filtrationList"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
  </div>
</template>
  
  <script>
import back from '@/components/common/back.vue'
import textBorder from '@/components/common/textBorder.vue'
import systemDialog from '@/components/common/systemDialog.vue'
import cuatomerDialog from '@/components/common/customerDialog.vue'
import contractDialog from '@/components/common/contractDialog.vue'
import { getDict } from '@/utils/tools.js'
import { projectSave, projectUpdate, proInfo } from '@/api/project/index'
import { contractInfo } from '@/api/contract'
import upload2 from '@/components/common/upload2.vue'
import { getFileTypeNum } from '@/utils/tools'

import {
  selectTemplate,
  taskSave,
  taskInfo,
  taskUpdate,
} from '@/api/project/index.js'
export default {
  components: {
    back,
    textBorder,
    systemDialog,
    upload2,
  },
  data() {
    return {
      idAndName: {
        taskNum: '',
        taskName: '',
      },
      grades: [
        {
          value: 1,
          name: '低',
        },
        {
          value: 2,
          name: '一般',
        },
        {
          value: 3,
          name: '紧急',
        },
        {
          value: 4,
          name: '非常紧急',
        },
      ],
      rules: {
        taskName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
        ],
        priority: [
          { required: true, message: '请选择任务优先级', trigger: 'change' },
        ],
        beginTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' },
        ],
        chargePerson: [
          { required: true, message: '请选择任务负责人', trigger: 'change' },
        ],
      },
      optionsStatus: [
        {
          value: 1,
          name: '未开始',
        },
        {
          value: 2,
          name: '进行中',
        },
        {
          value: 3,
          name: '已完成',
        },
      ],
      dialogVisible: false,
      dialogName: '',
      form: {
        beginTime: '',
        endTime: '',
        notes: '',
        fileInfoList: [],
        projectId: '',
        chargePerson: '',
        chargePersonName: '',
        collaborator: '',
        collaboratorName: '',
        taskName: '',
        taskContent: '',
        parentId: '',
      },
      multipleNum: 1,
      fileListPdf: [],
      xiezuolist: [],
      titleName: '新建任务',
      disabled: false,
      filtrationList: [],
    }
  },
  created() {
    if (this.$route.query.editId) {
      this.form.id = this.$route.query.editId
      this.taskInfoApi()
      this.titleName = '修改任务'
    }
    if (this.$route.query.projectId) {
      this.form.projectId = this.$route.query.projectId
    }
    if (this.$route.query.parentId) {
      this.form.parentId = this.$route.query.parentId
    }
    if (this.$route.query.taskNum) {
      this.idAndName.taskNum = this.$route.query.taskNum
      this.idAndName.taskName = this.$route.query.taskName
    }
  },
  methods: {
    taskInfoApi() {
      taskInfo(this.$route.query.editId).then((res) => {
        if (res.status == 0) {
          this.form = res.data
          var ids = res.data.collaborator && res.data.collaborator.split(',')
          let names =
            res.data.collaboratorName && res.data.collaboratorName.split(',')
          ids &&
            ids.forEach((item, index) => {
              var data = {}
              data.id = item
              data.name = names[index]
              this.xiezuolist.push(data)
            })
          res.data.fileInfoList.forEach((item) => {
            item.name = item.fileName
          })
          this.fileListPdf = res.data.fileInfoList
          this.$refs.upload2.setFileList(this.fileListPdf)
          if (res.data.taskStatus == 5) {
            this.statusTaskArr = this.taskArr1
          }
          if (res.data.taskStatus == 2) {
            this.statusTaskArr = this.taskArr2
          }
        }
      })
    },
    submitPdf(fileList) {
      this.fileListPdf = fileList
    },
    arrForEach(arrFileList) {
      arrFileList.forEach((item) => {
        this.form.fileInfoList.push({
          ...item,
          fileType: getFileTypeNum(item.url),
        })
      })
    },
    saveButton() {
      this.$refs['addform'].validate((valid) => {
        if (valid) {
          if (this.form.chargePerson == '') {
            this.msgError('请选择负责人')
            return
          }
          this.form.fileInfoList = []
          if (this.fileListPdf.length > 0) {
            this.arrForEach(this.fileListPdf)
          }
          if (this.$route.query.editId) {
            taskUpdate(this.form).then((res) => {
              if (res.status == 0) {
                this.msgSuccess('修改成功')
                this.$router.back()
              } else {
                this.msgError(res.msg)
              }
            })
          } else {
            taskSave(this.form).then((res) => {
              if (res.status == 0) {
                this.msgSuccess('添加成功')
                this.$router.back()
              } else {
                this.msgError(res.msg)
              }
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 选择协作人
    clickXuan(name, multipleNum) {
      this.dialogName = name
      this.multipleNum = multipleNum
      this.$refs.systemdialog.loadData()
      if (name == '选择任务负责人') {
        this.filtrationList = this.xiezuolist.map((item) => item.id)
        this.form.chargePerson
          ? this.$refs.systemdialog.updateWorksId([
              {
                id: this.form.chargePerson,
                name: this.form.chargePersonName,
                departmentId: this.form.taskDepartmentId,
              },
            ])
          : this.$refs.systemdialog.updateWorksId([])
      } else if (name == '选择任务参与人') {
        this.filtrationList = this.form.chargePerson
          ? [this.form.chargePerson]
          : []
        this.$refs.systemdialog.updateWorksId(this.xiezuolist)
      }
      this.dialogVisible = true
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    handleTagClose(tag) {
      this.xiezuolist.splice(this.xiezuolist.indexOf(tag), 1)
      this.xiezuoData(this.xiezuolist)
    },
    xiezuoData(list) {
      var ids = []
      var names = []
      list.forEach((item) => {
        ids.push(item.id)
        names.push(item.name)
      })
      this.form.collaborator = ids.join(',')
      this.form.collaboratorName = names.join(',')
    },
    submitData(data, type, departmentId) {
      if (type === '选择任务负责人') {
        this.form.chargePerson = data.length > 0 ? data[0].id : ''
        this.form.chargePersonName = data.length > 0 ? data[0].name : ''
        this.form.taskDepartmentId = departmentId
        this.$refs.addform.validateField(['chargePerson'])
      } else if (type === '选择任务参与人') {
        this.xiezuoData(data)
        this.xiezuolist = data
      }
      this.updateSystemVisible(false)
    },
  },
}
</script>
  <style scoped lang="scss">
.sml {
  margin-left: 8px;
}
.theadcss {
  padding: 0 30px;
  height: 45px;
  line-height: 45px;
  background: #4285f4;
  border-radius: 8px;

  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 16px;
  color: #ffffff;
  margin-bottom: 16px;
}
.mb20 {
  margin-bottom: 40px !important;
}
.widm {
  width: calc(100% - 130px);
  margin-right: 20px;
}
.discss {
  background-color: #f5f7fa;
}

.tagcss /deep/ .el-icon-close {
  width: 12px;
  height: 12px;
  line-height: 12px;
  background-color: #4285f4;
  color: white;
}

.tagcss {
  margin-left: 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  background: #dfeafd;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}
.tagcss:last-child {
  margin-right: 10px !important;
}

.ellipsis /deep/.el-input__inner {
  padding: 0 8px !important;
}

/deep/ .el-input__suffix {
  right: 0 !important;
}

.studiocss img {
  width: 16px;
  height: 16px;
  margin-right: 3px;
  margin-top: -3px;
}

.eldate {
  width: 100% !important;
  padding: 0;

  /deep/.el-input__inner {
    height: 34px;
    line-height: 34px;
  }

  /deep/.el-input__icon {
    line-height: 34px;
  }
}

.els50 {
  width: 58%;
}

.els40 {
  width: 40%;
  margin-right: 1%;
}

.els50:last-child {
  margin-right: 0;
  margin-left: 1%;
  float: right;
}

.elflex {
  display: flex;
}

.w98 {
  width: 98px;
}

.flex {
  display: flex;
  flex-direction: column;
}

.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}

.btncenter {
  text-align: center;
}

.quanxiancss /deep/.el-form-item__label {
  margin-left: -7px;
  width: 125px !important;
}

.addresscss {
  max-height: 68px;
}

.input-new-tag {
  width: 200px;
  margin-left: -8px;
}

.input-new-tag /deep/.el-input__inner {
  border: none;
  background-color: rgba(0, 0, 0, 0);
}

.tagcol /deep/.el-form-item__label {
  height: 34px;
  line-height: 34px;
}

.tagcol /deep/.el-form-item__content {
  background: #f5f5f5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  min-height: 34px;
  line-height: 34px;
}

.mainbg {
  margin: 16px 0px;
  padding: 20px 16px;
  background-color: white;
  border-radius: 8px;
}

.pd0 {
  padding-right: 0px !important;
}
.backcss {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  background-color: #f7f7f7;
  padding-bottom: 20px;
  position: relative;
  text-align: center;
}
.backc {
  display: inline-block;
  position: absolute;
  left: 0;
}
</style>
