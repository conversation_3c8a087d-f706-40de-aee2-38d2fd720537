<template>
  <el-dialog
    :title="dType == 1 ? '选择部门' : '选择员工'"
    :visible.sync="dialogVisible"
    :width="dType == 1 ? '500px' : '768px'"
    center
    class="unittree"
    @close="closeAction"
  >
    <div class="condiv">
      <div class="ldiv" :class="{ fixwidth: dType == 1 }">
        <span class="spantext">部门</span>
        <el-tree
          ref="treeRef"
          :data="data"
          :expand-on-click-node="false"
          :show-checkbox="dType == 1"
          class="ptop"
          node-key="id"
          updateKeyChildren="updateKeyChildren"
          default-expand-all
          :props="defaultProps"
          @node-click="nodeClick"
        >
        </el-tree>
      </div>
      <div class="line" v-if="dType == 2"></div>
      <div class="rdiv" v-if="dType == 2">
        <span class="spantext"> 员工 </span>
        <el-checkbox-group class="workercheckbox" v-model="checkedWorkers">
          <el-checkbox
            class="boxitem"
            v-for="item in workers"
            :label="item.id"
            :key="item.id"
            @change="(e) => handleCheckedWorkersChange(e, item)"
            >{{ item.name }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="saveAction">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { listDept, listSatff } from '@/api/framework/dept'
import { queryTreeByUserId } from '@/api/index'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dType: {
      type: String,
      default: '1',
    },
    multiple: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('updateVisible', value)
      },
    },
  },
  data() {
    return {
      workers: [],
      checkedWorkers: [],
      selList: [],
      selDepartmentId: '',
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },

  methods: {
    loadData() {
      var par = {
        methodName: 'list',
        className: 'CaseController',
      }
      queryTreeByUserId(par).then((result) => {
        this.data = result.data
      })
    },
    updateWorksId(data) {
      this.checkedWorkers = []
      this.selList = data
      this.selList.forEach((element) => {
        this.checkedWorkers.push(element.id)
      })
    },
    nodeClick(data) {
      if (this.dType == 2) {
        this.selDepartmentId = data.id
        listSatff({ departmentId: data.id }).then((result) => {
          this.workers = result.data
        })
      }
    },
    updateKeyChildren(key, data) {},
    handleCheckedWorkersChange(checked, data) {
      if (this.multiple) {
        if (checked) {
          this.selList.push(data)
        } else {
          var index = -1
          this.selList.forEach((item, idx) => {
            if (item.id == data.id) {
              index = idx
              return
            }
          })
          if (index >= 0) {
            this.selList.splice(index, 1)
          }
        }
      } else {
        if (checked) {
          this.checkedWorkers = [
            this.checkedWorkers[this.checkedWorkers.length - 1],
          ]
          this.selList = [{ ...data }]
        } else {
          this.selList = []
        }
      }
    },
    closeAction() {
      this.workers = []
      this.checkedWorkers = []
      this.selList = []
    },
    saveAction() {
      if (this.dType == 1) {
        if (this.$refs.treeRef.getCheckedNodes().length == 0) {
          this.msgError('请选择部门')
          return
        }
        let dIdList = this.$refs.treeRef.getCheckedNodes().map((item) => {
          return {
            id: item.id,
            name: item.name,
          }
        })
        this.$emit('submitData', dIdList)
      } else {
        if (this.selList.length == 0) {
          this.msgError('请选择员工')
          return
        }
        this.$emit('submitData', this.selList)
      }
    },
  },
}
</script>

<style scoped lang="scss">
/deep/ .el-tree {
  display: inline-block;
  min-width: 100%;
  padding-right: 10px;
}
.ptop {
  padding-top: 10px;
}
.workercheckbox {
  padding-top: 10px;
}
.workercheckbox /deep/.el-checkbox__label {
  line-height: 32px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  min-width: 65px;
}
.unittree /deep/.el-dialog__body {
  height: 600px;
  padding: 0 !important;
}
.unittree /deep/.el-tree-node__label {
  font-size: 12px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 14px;
}
.unittree /deep/.el-tree-node__expand-icon {
  color: #333333;
}
.unittree /deep/.el-tree-node__expand-icon.is-leaf {
  color: transparent;
}
.condiv {
  display: flex;
  height: 100%;
}
.ldiv {
  padding-top: 30px;
  padding-left: 26px;
  padding-right: 26px;
  width: 35%;
  overflow-y: scroll;
  overflow-x: scroll;
}
.fixwidth {
  width: 100%;
  box-sizing: border-box;
}
.line {
  margin-right: 30px;
  width: 1px;
  height: 100%;
  background-color: #f0f0f0;
}
.spantext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.rdiv {
  width: 60%;
  padding-top: 30px;
  padding-left: 0px;
  overflow-y: scroll;
}
</style>