<template>
  <div :class="{ 'has-logo': showLogo }">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" :background-color="variables.menuBg"
        :text-color="variables.menuText" :unique-opened="false" :active-text-color="variables.menuActiveText"
        :collapse-transition="false" mode="vertical">
        <!-- <sidebar-item
          v-for="(route, index) in AllRouter"
          :key="index"
          :item="route"
          :base-path="route.path"
        /> -->
        <sidebar-item v-for="(route, index) in AllRouter" :key="index" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import SidebarItem from './SidebarItem'
  import variables from '@/styles/variables.scss'

  let whiteMenu = ['/projectManagement/stage/taskadd', '/projectManagement/stage/index']

  export default {
    components: { SidebarItem, },
    computed: {
      ...mapGetters(['sidebar', 'AllRouter']),
      routes() {
        return this.$router.options.routes
      },
      activeMenu() {
        const route = this.$route
        if (whiteMenu.includes(route.path)) {
          return '/projectManagement/project/index'
        }
        if(route.path=='/give/add' || route.path=='/give/detail'){
            if(route.query.type ==1){
               return '/give/samplebook/index'
            }else if(route.query.type ==2){
               return '/give/giftlist/index'
            }else if (route.query.type == 3) {
              return '/give/busactivity/index'
            }
        }
        const { path } = route
        let patharr = path.split('/')
        if (patharr.length == 2) {
          return path
        }
        if (patharr.length == 3) {
          return '/' + patharr[1] + '/index'
        }
        if (patharr.length == 4) {
          // let result = this.findNameById(path,this.$router.options.routes)
          let result = this.findNameById(path, this.AllRouter)
          if (result) {
            if (result.status == 1) {
              return '/' + patharr[1] + '/' + patharr[2] + '/index'
            } else {
              return path
            }
          }
          return '/' + patharr[1] + '/' + patharr[2] + '/index'
        }
        return path
      },
      showLogo() {
        return this.$store.state.settings.sidebarLogo
      },
      variables() {
        return variables
      },
      isCollapse() {
        return !this.sidebar.opened
      },

    },
    created() {
      console.log(this.AllRouter)
    },
    methods: {
      findNameById(path, data) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].path === path) {
            return data[i];
          } else if (data[i].children) {
            const result = this.findNameById(path, data[i].children);
            if (result) {
              return result;
            }
          }
        }
        return null;
      }
    }
  }
</script>
<style>
   .el-menu-item.is-active  .svg-icon {
      fill: #4285F4;
      color: #4285F4;
    }
</style>
<style scoped lang="scss">
  /deep/.el-submenu.is-active>.el-submenu__title {

    span {
      color: #4285F4;
    }

    .svg-icon {
      fill: #4285F4;
      color: #4285F4;
    }
  }

  /deep/ .el-menu-item.is-active {
    .svg-icon {
      fill: #4285F4;
      color: #4285F4;
    }
  }

  /* icon图标也跟着变 */
  ::v-deep .el-submenu.is-active>.el-submenu__title i {
    color: #409eff !important;
  }
</style>