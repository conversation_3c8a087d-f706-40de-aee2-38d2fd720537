<!-- 收入类型 -->
<template>
  <div>
    <el-select clearable placeholder="收入类型" v-model="myValue" @change="change" class="input-230">
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.name"
        :value="item.name"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  data () {
    return {
      myValue: '',
      options: []
    }
  },
  methods: {
    change (val) {
      this.$emit('input', val)
    },
    async getReqData () {
      let res = await this.$axios.get('/sf/member/fundAccountFlow/typeFundList', {params: {fundType: '1'}})
      if (res.status === 0) {
        this.options = res.data
      }
    }
  },
  created () {
    this.myValue = this.value || ''
    this.getReqData()
  }
}
</script>
