<template>
  <el-drawer
  class="readd"
  :append-to-body='true'
  title="阅读记录"
  :visible.sync="drawer"
  :direction="direction"
  :before-close="beforeClose">
  <div>
    <el-tabs class="eltab" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="已读" name="first">
        <div class="dailylist">
          <p v-if="readLogs.length==0">暂无人阅读</p>
          <ul>
            <li class="rili activecss" v-for="(item,index) in readLogs" :key="index">
              <headuser :url="item.logo" class="usercss" borderRadius="8px" width="28" :username="item.name">
              </headuser>
              <span>
                {{item.name}}的日报
              </span>
              <span class='readb noread'>
                {{item.createTime}}
              </span>
            </li>
          </ul>
        </div>
      </el-tab-pane>
      <el-tab-pane label="未读" name="second">
        <div class="dailylist">
          <p v-if="notReadLogs.length==0">暂无</p>
          <ul>
            <li class="rili activecss" v-for="(item,index) in notReadLogs" :key="index">
              <headuser :url="item.logo" class="usercss" borderRadius="8px" width="28" :username="item.name">
              </headuser>
              <span>
                {{item.name}}
              </span>
              <span class='readb noread'>
                {{item.createTime}}
              </span>
            </li>
          </ul>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</el-drawer>
       
</template>

<script>
 
  import headuser from '@/components/common/headuser.vue'
  export default {
    components: {
      headuser,
    },
      props:{
          visible:{
              type:Boolean,
              default:false
          },
          readLogs: {
            type: Array,
            default: () => ([])
          },
          notReadLogs:{
            type: Array,
            default: () => ([])
          }
      },
    data() {
      return {
        direction: 'rtl',
        daliyUser: [
          {
            userName: '张三',
            isRead: true,
            url: 'https://static-legacy.dingtalk.com/media/lADPDiQ3NIHTXyzNCurNCuo_2794_2794.jpg',
          },
          {
            userName: '张三1',
            isRead: false,
            url: '',
          },
          {
            userName: '张三',
            isRead: true,
            url: 'https://static-legacy.dingtalk.com/media/lADPDiQ3NIHTXyzNCurNCuo_2794_2794.jpg',
          },
        ],
        activeName: 'first',
      };
    },
    computed:{
        drawer:{
            get(){
                return this.visible
            },
            set(val){
                this.$emit('closeRead',val)
            }
        }
    },
    mounted: function () { },
    created() {
    },
    methods: {
        beforeClose(){
            this.drawer =false
        },
      closeImg(){
          this.isShow = false
      },
      handleClick() {

      },
    }
  };
</script>

<style lang="scss" scoped>
  .usercss{
    margin-top: 4px;
  }
  .readd /deep/.el-drawer__header span{
    font-weight: 400;
    font-size: 20px;
    color: #333333;
    text-align: center;
    margin-bottom: 10px;
  }
  .readd /deep/.el-drawer__header {
     margin-bottom: 10px;
  }
  

  .dailylist {
    padding-left: 20px;
  }

  .eltab /deep/.el-tabs__item {
    width: 120px;
    text-align: center;
  }

  .rili {
    height: 36px;
    line-height: 36px;
    margin: 10px 0;
    position: relative;
    padding: 0 4px;
    cursor: pointer;
  }
</style>