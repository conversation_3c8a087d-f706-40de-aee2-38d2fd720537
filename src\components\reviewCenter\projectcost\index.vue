<template>
  <div>
    <div class="mainbg" v-loading="isLoading">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="待处理的" name="waitlist">
          <list ref="waitlist" :handleType="1"/>
        </el-tab-pane>
        <el-tab-pane label="已处理的" name="alreadylist">
          <list ref="alreadylist" :handleType="2"/>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { nextTick } from 'vue'
import list from './components/list.vue'

export default {
  components: {
    list
  },
  data() {
    return {
      isLoading: false,
      activeName: 'waitlist',

    }
  },
  created() {
    nextTick(()=>{
      this.$refs.waitlist.loadData()
    })
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
      this.$nextTick(()=>{
        this.$refs[tab.name].reload()
      })

    },

  }
}
</script>

<style lang="scss" scoped>
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: 100%;
  border-radius: 10px;
}
</style>