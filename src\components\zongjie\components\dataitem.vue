<template>
    <div class="dataitem">
        <div class="flex">
          <img class="imgcss" :src="item.icon" alt="">
          <div class="typename">{{item.name}}({{item.unit}})</div>
        </div>
        <div class="dataviewcss">
            <div class="goalnumcss">
              <div class="curnumcss">
                {{item.goal}}
              </div>
              <div>
                目标
              </div>
            </div>
            <div class="goalnumcss">
              <div class="curnumcss" >{{item.finishGoal}}</div>
              <div>
                达成
              </div>
            </div>
            <div class="goalnumcss">
              <div class="curnumcss" >{{item.residueGoal>0?item.residueGoal:0}}</div>
              <div>
                剩余
              </div>
            </div>
        </div>
        <el-progress class="procss" :stroke-width="8" :percentage="getPercentage(item)" :format="format(item)" :color="customColor" text-color="#333333"></el-progress>
    </div>
</template>

<script>
export default {
    props:{
        item:{
            type:Object,
            default:()=>{}
        }
    },
    data(){
      return{
        customColor:'#5A98FF'
      }
    },
    methods:{
      format(percentage) {
            return () => {
              if(percentage.goal == 0){
                  return '**.**%'
              }else{
                 return (percentage.finishRate*100).toFixed(2) + '%'
              }
            }
        },
      getPercentage(item){
        if (Object.keys(item).length<=0) {
            return 0
        }
        if (item.goal == 0) {
            return 0;
        }else if(item.finishRate *100>=100){
            return 100
        } else {
            return item.finishRate *100;
        }
      },
    }
}
</script>

<style scoped>
.procss /deep/.el-progress-bar{
  padding-right: 90px !important;
  margin-right: -95px !important;
}
.procss /deep/.el-progress__text{
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 0.875em;
  color: #333333;
  line-height: 16px;
  text-align: right;
  font-style: normal;
  text-transform: none;
  /* width: 80px; */
}
.procss{
  margin-top: 20px;
}
.dataviewcss{
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.ml{
  margin-left: 3px;
}
.goalnumcss{
  width: 100%;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 1em;
  color: #999999;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  text-align: center;
  border-right: 1px solid #F0F0F0;
}
.goalnumcss:last-child{
  border-right: none;
}
.curnumcss{
font-family: Roboto, Roboto;
font-weight: bold;
font-size: 1.5em;
color: #333333;
line-height: 28px;
text-align: center;
font-style: normal;
text-transform: none;

}
.pl12{
  padding-left: 8px;
}
.flex{
  display: flex;
  align-items: center;
}
.imgcss{
  width: 2.5em;
  height: 2.5em;
}
.typename{
  max-height: 40px;
  line-height: 20px;
  margin-left: 10px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 1em;
  color: #999999;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.dataitem{
  padding: 16px 20px;
  height: 190px !important;
  background: #FFFFFF;
  box-shadow: 0px 2px 16px 0px rgba(15,27,50,0.08);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  margin-top: 0px !important;
  margin-bottom: 10px;
}
</style>