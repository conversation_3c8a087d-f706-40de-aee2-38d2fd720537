<template>
    <el-dialog title="新增阶段" center width="40%" :visible.sync="dialogFormVisible" >
        <el-form class="mainform" ref="addform" :model="form" :rules="rules" @before-close="beforeClose">
            <el-form-item  label="阶段名称：" :label-width="formLabelWidth" prop="name">
                <el-input class="definput" v-model="form.name" placeholder="请输入阶段名称"  autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item  label="阶段类型：" :label-width="formLabelWidth" prop="stageType">
                <el-select class="width100 definput" popper-class="removescrollbar" v-model="form.stageType">
                    <el-option 
                    :key="item.id" 
                    v-for="item in types"
                    :label="item.name"
                    :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitAction">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        dialogFormVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('updateVisible', value)
            }
        }
    },
    data() {
        return {
            rules:{
                name: [
                    { required: true, message: '请输入阶段名称', trigger: 'blur' },
                    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
                ],
                stageType: [
                    { required: true, message: '请选择阶段类型', trigger: 'change' }
                ],
            },
            types:[
                {
                    id:"1",
                    name:"一般阶段"
                },
                {
                    id:"2",
                    name:"回款阶段"
                }
            ],
            formLabelWidth: '100px',
            form: {
                name: "",
                stageType: ''
            }
        }
    },
    methods:{
        beforeClose(){
            console.log('ssssssssss');
            var keys = Object.keys(this.form);
            this.$refs['addform'].resetFields();
            keys.forEach(element => {
                this.form[element] = ''
            });
        },
        submitAction(){
            this.$refs['addform'].validate((valid) => {
                if (!valid) {
                    return false;
                } 
                if (this.form.id) {
                    
                }else{
                    this.$emit('submitAction',this.form);
                    this.beforeClose()
                }
            });
            
        },
    }
}
</script>



<style lang="scss" scoped>
@import "@/styles/mixin.scss";

 .mainform {
    width: 80%;
    margin: 0 auto;
}

.timediv {
    display: flex;
    justify-content: flex-start;
}

.datepicker /deep/.el-input__prefix {
    left: calc(100% - 30px);
}

.datepicker /deep/.el-input__inner {
    @include formatFont13;
    padding-left: 15px !important;
}

.datepicker /deep/.el-input__icon {
    line-height: 34px;
}

.mainform /deep/.el-form-item {
    margin-bottom: 28px;
}

.mainform /deep/.el-form-item__label {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400 !important;
    color: #333333;
    line-height: 34px;
}

.mainform /deep/.el-form-item__content {
    line-height: 34px;
}

.mainform /deep/.el-textarea__inner {
    @include formatFont13;
    line-height: 1.5;
}

.mainform /deep/.el-input__inner {
    @include formatFont13;
}

.canclebtn {
    margin-top: 7px;
    margin-left: 10px;
    width: 20px;
    height: 20px;
}
</style>
