<template>
    <el-dialog
        :title="name"
        :visible.sync="dialogVisible"
        width="768px"
        top="5%"
        center
        class="unittree"
        @close="closeAction">
         >
         <div class="condiv">
           <div class="ldiv">
             <span class="spantext">部门</span>
             <el-tree
               :data="data"
               class="ptop"
               node-key="id"
               updateKeyChildren="updateKeyChildren"
               default-expand-all
                :highlight-current="true" 
                :expand-on-click-node="false" 
               :props="defaultProps"
               @node-click="nodeClick">
             </el-tree>
           </div>
           <div class="line"></div>
           <div class="rdiv">
             <span class="spantext">
               员工
             </span>
             <el-checkbox-group class="workercheckbox"  v-model="checkedWorkers" >
               <el-checkbox class="boxitem" v-for="item in workers" :label="item.id" :key="item.id" :disabled="getDisable(item.id)" @change=" e =>handleCheckedWorkersChange(e,item)">{{item.name}}</el-checkbox>
             </el-checkbox-group>
           </div>
         </div>
         <span slot="footer" class="dialog-footer">
           <el-button type="primary" @click="saveAction">确 定</el-button>
         </span>
       </el-dialog>
 </template>
 
 <script>
 import { listDept,listSatff } from '@/api/framework/dept'
 export default {
        props:{
            multipleNum:{
                type:Number,
                default:1
            },
            visible:{
                type:Boolean,
                default:false,
            },
            filtrationList:{
                type:Array,
                default:()=>{
                return new Array()
                }
            },
        },
     computed:{
         dialogVisible:{
             get(){
                 return this.visible;
             },
             set(value){
                 this.$emit('updateVisible',value)
                 
             }
         },
     },
     data(){
         return{
             workers:[],
             checkedWorkers:[],
             selList:[],
             selDepartmentId:'',
             data: [],
             defaultProps: {
               children: 'children',
               label: 'name'
             }
         }
     },
     methods:{
         loadData(){
           listDept({}).then((result) => {
             this.data = result.data;
           }).catch((err) => {
             
           });
         },
         updateWorksId(data){
           this.checkedWorkers = [];
           this.selList = data;
           this.selList.forEach(element => {
             this.checkedWorkers.push(element.id);
           });
         },
         nodeClick(data){
              console.log(data)
              this.selDepartmentId = data.id;
              listSatff({departmentId:data.id}).then((result) => {
               this.workers = result.data
              }).catch((err) => {
               
              });
         },
         updateKeyChildren(key,data){
         },
         getDisable(id){
           if (this.filtrationList.indexOf(id)>-1) {
             return true
           }
           return false
         },
         handleCheckedWorkersChange(checked,data){
           if (checked) {
             if (this.selList.length<this.multipleNum || this.multipleNum == 0 ) {
               this.selList.push(data)
             }else if(this.multipleNum == 1){
               this.selList = [data];
               this.checkedWorkers = [data.id]
             } else {
               this.$message.info(`最多选择${this.multipleNum}个`)
               var index = this.checkedWorkers.indexOf(data.id);
               this.checkedWorkers.splice(index,1)
             }
           } else {
               var index = -1;
               this.selList.forEach((item,idx) => {
                 if (item.id == data.id) {
                   index = idx;
                   return;
                 }
               });
               if (index>=0) {
                 this.selList.splice(index,1);
               }
           }
         },
         closeAction(){
           this.workers  = [];
           this.checkedWorkers = [];
           this.selList = [];
         },
     // 确定
         saveAction(){
           if (this.multipleNum == 1) {
             console.log(this.selDepartmentId)
             this.$emit('submitData',this.selList,this.name,this.selDepartmentId);
           }else{
             this.$emit('submitData',this.selList,this.name);
           }
             
         }
     }
 }
 </script>
 
 <style scoped>
 .ptop{
   padding-top: 10px;
 }
 .workercheckbox{
   padding-top: 10px;
 }
 .workercheckbox /deep/.el-checkbox__label{
   line-height: 32px;
   font-size: 12px;
   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
   font-weight: 400;
   color: #333333;
   min-width: 65px;
 }
 .unittree /deep/.el-dialog__body{
   height: 500px;
   padding: 0 !important;
 }
 .unittree /deep/.el-tree-node__label{
   font-size: 12px !important;
   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
   font-weight: 400;
   color: #333333;
   line-height: 14px;
 }
 .unittree /deep/.el-tree-node__expand-icon{
   color: #333333;
 }
 .unittree /deep/.el-tree-node__expand-icon.is-leaf{
   color: transparent;
 }
 .condiv{
   display: flex;
   height: 100%;
 }
 .ldiv{
   padding-top: 30px;
   padding-left: 26px;
   width: 35%;
   overflow-y: scroll;
 }
 .line{
   margin-right:30px;
   width: 1px;
   height: 100%;
   background-color: #F0F0F0;
 }
 .spantext{
   font-size: 14px;
   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
   font-weight: 400;
   color: #333333;
 }
 .rdiv{
   width: 60%;
   padding-top: 30px;
   padding-left: 0px;
   overflow-y: scroll;
 }
 
 </style>