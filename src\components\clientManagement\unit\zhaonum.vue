<template>
  <div class="mainbg" v-loading="isLoading">
    <div class="backdiv">
      <back>招生数据</back>
    </div>
    <div class="page-header">
      <el-form class="myform" ref="form" :model="pageBean" :inline="true">
        <el-form-item label="专业">
          <el-select
            filterable
            clearable=""
            class="definput"
            popper-class="removescrollbar"
            v-model="pageBean.unitSpecialtyId"
            placeholder="请选择专业"
          >
            <el-option
              v-for="item in optionsMajor"
              :key="item.id"
              :label="item.parentSpecialtyName + '- ' + item.specialtyName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年份：">
          <el-date-picker
            format="yyyy"
            value-format="yyyy"
            v-model="pageBean.recruitYear"
            type="year"
            placeholder="选择年"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="">
          <el-button
            class="defaultbtn"
            type="primary"
            icon="el-icon-search"
            @click="onSearch"
            >查询</el-button
          >
        </el-form-item>
      </el-form>

      <div>
        <el-button
          class="defaultbtn"
          icon="el-icon-my-download"
          type="primary"
          :loading="isDownload"
          @click="downloadAction"
          >导出筛选招生数据</el-button
        >
        <el-button
          class="ml20 defaultbtn"
          icon="el-icon-my-inport"
          type="primary"
          @click="showImportDialog"
          >导入招生数据</el-button
        >
        <el-button
          v-isShow="'crm:controller:unit:save'"
          class="defaultbtn"
          type="primary"
          icon="el-icon-plus"
          @click="addUnit"
          >补充数据</el-button
        >
      </div>
    </div>
    <el-table class="mytable" :data="tableData">
      <el-table-column prop="specialtyName" label="专业名称" align="center">
      </el-table-column>
      <el-table-column
        prop="parentSpecialtyName"
        label="专业类型"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="planRecruitNumber"
        label="计划招生数量"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="realityRecruitNumber"
        label="实际招生数量"
        align="center"
      >
      </el-table-column>
      <el-table-column prop="recruitYear" label="招生年份" align="center">
      </el-table-column>
      <el-table-column prop="courseNumber" label="开课数量" align="center">
      </el-table-column>
      <el-table-column prop="materialNumber" label="教材用量" align="center">
      </el-table-column>
      <el-table-column prop="edit" width="140" align="center" label="操作">
        <template slot-scope="scope">
          <el-button
            class="tabbtn"
            type="text"
            @click="updateButton(scope.row)"
          >
            更新</el-button
          >
          <el-button
            class="rbtn tabbtn"
            type="text"
            @click="deleteAction(scope.row)"
          >
            移除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page
      :currentPage="pageBean.pageNum"
      :total="total"
      :pageSize="pageBean.pageSize"
      @updatePageNum="handleCurrentChange"
    ></page>
    <el-dialog
      title="招生数据"
      :visible.sync="dialogTableVisible"
      class="cDialog"
      width="40%"
      center
      @close="closeDia"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="130px"
        class="demo-ruleForm"
      >
        <el-form-item
          v-if="!ruleForm.id"
          label="请选择专业"
          prop="unitSpecialtyId"
        >
          <el-select
            filterable
            class="wcss"
            v-model="ruleForm.unitSpecialtyId"
            placeholder="请选择专业"
          >
            <el-option
              v-for="item in optionsMajor"
              :key="item.id"
              :label="item.parentSpecialtyName + '- ' + item.specialtyName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划招生数量" prop="planRecruitNumber">
          <el-input
            type="number"
            placeholder="请输入"
            class="wcss guang"
            v-model="ruleForm.planRecruitNumber"
          ></el-input>
        </el-form-item>
        <el-form-item label="实际招生数量" prop="realityRecruitNumber">
          <el-input
            type="number"
            class="wcss"
            v-model="ruleForm.realityRecruitNumber"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="!ruleForm.id"
          label="请选择招生年份"
          prop="recruitYear"
        >
          <el-date-picker
            format="yyyy"
            value-format="yyyy"
            class="wcss"
            v-model="ruleForm.recruitYear"
            type="year"
            placeholder="选择年"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="开课数量" prop="courseNumber">
          <el-input
            type="number"
            class="wcss"
            v-model="ruleForm.courseNumber"
          ></el-input>
        </el-form-item>
        <el-form-item label="教材用量" prop="materialNumber">
          <el-input
            type="number"
            class="wcss"
            v-model="ruleForm.materialNumber"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitButton">确 定</el-button>
      </span>
    </el-dialog>

    <dc-dialog
      iType="2"
      title="导入信息提示"
      width="500px"
      :showCancel="false"
      :dialogVisible.sync="dialogMsgVisible"
      @submit="submitMsgDialog"
      :appendToBody="true"
    >
      <template>
        <p>导入成功：{{ importData.successCount }} 条</p>
        <p>导入失败：{{ importData.errorCount }} 条</p>
        <p class="" v-for="(item, index) in importData.errorData" :key="index">
          {{ item }}
        </p>
      </template>
    </dc-dialog>

    <el-dialog
      title="导入招生数据"
      :visible.sync="importDialogVisible"
      width="600px"
      center
    >
      <div class="import-dialog-content">
        <div class="warning-message">
          <p>
            请注意，当导入数据存在同专业同年份的数据时，导入失败！若您已确认导入数据无误，可选择文件并导入。
          </p>
        </div>
        <div class="file-upload-area">
          <el-upload
            class="upload-demo"
            ref="upload"
            :action="getUrl"
            :show-file-list="true"
            :before-upload="beforeUpload"
            :on-success="handleAvatarSuccess"
            :on-error="handleError"
            :headers="headers"
            :data="fileData"
            :auto-upload="false"
            :limit="1"
            accept=".xlsx"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选择文件</el-button
            >
            <div slot="tip" class="el-upload__tip">只能上传xlsx文件</div>
          </el-upload>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="isImport" @click="submitUpload"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import dcDialog from '../../common/dcDialog.vue'
import {
  zhaoList,
  selectUintSpecialty,
  updateZhao,
  addZhao,
  deleteZhao,
  zhaoInfo,
  unitZhaoDownLoad,
} from '@/api/clientmanagement/unit'
import back from '../../common/back.vue'
import { getParStr, downloadExcelFile } from '@/utils/tools'
import { getToken } from '@/utils/auth'
export default {
  components: {
    page,
    nolist,
    back,
    dcDialog,
  },
  data() {
    return {
      importData: {
        successCount: 0,
        errorCount: 0,
        errorData: [],
      },
      dialogMsgVisible: false,
      headers: { Authorization: getToken() },
      isImport: false,
      importDialogVisible: false,
      fileData: {
        applicationId: sessionStorage.getItem('applicationId'),
        serviceName: 'crm/unit/excel',
      },
      getUrl: `${process.env.VUE_APP_BASE_API}/crm/business/recruitstudentinfo/batchImportExcel`,
      isDownload: false,
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        recruitYear: '',
        pageNum: 1,
        pageSize: 10,
        unitSpecialtyId: '',
        unitId: '',
      },
      dialogTableVisible: false,
      optionsMajor: [],
      ruleForm: {
        unitSpecialtyId: '',
        planRecruitNumber: undefined,
        realityRecruitNumber: undefined,
        recruitYear: '',
        courseNumber: undefined,
        materialNumber: undefined,
        unitId: '',
      },
      rules: {
        planRecruitNumber: [
          { required: true, message: '请输入计划招生数量', trigger: 'blur' },
        ],
        unitSpecialtyId: [
          { required: true, message: '请选择专业', trigger: 'change' },
        ],
        recruitYear: [
          { required: true, message: '请选择年份', trigger: 'change' },
        ],
        materialNumber: [
          { required: true, message: '请输入教材用量', trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    this.pageBean.unitId = this.$route.query.id
    this.fileData.unitId = this.$route.query.id
    this.loadData()
    this.selectUintSpecialtyApi()
  },
  methods: {
    submitMsgDialog() {
      this.dialogMsgVisible = false
      this.importData = {}
    },
    handleError(file, res) {
      this.isImport = false
      this.importDialogVisible = false
    },
    handleAvatarSuccess(res, file) {
      this.importDialogVisible = false
      if (res.status == 0 && res.data.errorCount == 0) {
        this.$message({
          type: 'success',
          message: res.msg,
        })
      } else {
        this.$message({
          type: 'error',
          message: res.msg,
        })
      }
      this.dialogMsgVisible = true
      this.importData = res.data
      this.isImport = false
      this.pageBean.pageNum = 1
      this.loadData()
    },
    beforeUpload(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['xlsx']
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error('导入明细仅支持 .xlsx 格式!')
        return false
      }
      return true
    },
    showImportDialog() {
      this.importDialogVisible = true
    },
    submitUpload() {
      this.isImport = true
      this.$refs.upload.submit()
    },
    downloadAction() {
      this.isDownload = true
      let params = {
        unitSpecialtyId: this.pageBean.unitSpecialtyId,
        recruitYear: this.pageBean.recruitYear,
        unitId: this.$route.query.id,
      }
      unitZhaoDownLoad(params)
        .then((result) => {
          console.log(result.type, '00000000000')
          if (result.type == 'application/json') {
            this.$message({
              type: 'error',
              message: '暂无可下载的数据',
            })
            this.isDownload = false
          } else {
            downloadExcelFile(result, `招生数据`)
            this.isDownload = false
          }
        })
        .catch((err) => {
          this.isDownload = false
        })
    },
    closeDia() {
      this.$refs['ruleForm'].resetFields()
    },
    updateButton(row) {
      this.dialogTableVisible = true
      zhaoInfo(row.id).then((res) => {
        if (res.status == 0) {
          this.ruleForm = res.data
          this.ruleForm.recruitYear = this.ruleForm.recruitYear + ''
        }
      })
    },
    selectUintSpecialtyApi() {
      let params = {
        unitId: this.$route.query.id,
      }
      selectUintSpecialty(params).then((res) => {
        if (res.status == 0) {
          this.optionsMajor = res.data
        }
      })
    },
    submitButton() {
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.ruleForm.unitId = this.$route.query.id
        if (this.ruleForm.id) {
          updateZhao(this.ruleForm)
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '更新成功！',
                })
                this.dialogTableVisible = false
                this.loadData()
              } else {
                this.$message({
                  type: 'error',
                  message: '保存失败！',
                })
              }
            })
            .catch((err) => {})
        } else {
          addZhao(this.ruleForm)
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '添加成功！',
                })
                this.dialogTableVisible = false
                this.loadData()
              } else {
                this.$message({
                  type: 'error',
                  message: '保存失败！',
                })
              }
            })
            .catch((err) => {})
        }
      })
    },
    loadData() {
      this.isLoading = true
      zhaoList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    onSearch() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    addUnit() {
      this.dialogTableVisible = true
      Object.keys(this.ruleForm).forEach((key) => {
        this.ruleForm[key] = undefined
      })
      this.$nextTick(() => {
        this.$refs['ruleForm'].resetFields()
      })
    },
    deleteAction(data) {
      deleteZhao({ id: data.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '移除成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>

<style scoped>
.elupload {
  display: inline-block;
  margin-left: 12px;
  margin-right: 12px;
}
.guang /deep/.el-input__inner {
  line-height: 1px !important;
}
.wcss {
  width: 200px;
}
.cDialog /deep/.el-dialog__body {
  display: flex;
  justify-content: center;
  align-content: center;
}
.cDialog /deep/.el-dialog {
  width: 580px !important;
}
.backdiv {
  margin-bottom: 12px;
}
.definput {
  width: 180px;
}
.page-header {
  display: flex;
  justify-content: space-between;
}
.tabbtn {
  font-size: 14px;
  cursor: pointer;
  padding: 2px;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.mainbg {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
}
.warning-message {
  background-color: #fff6f7;
  border-radius: 4px;
  padding: 10px 15px;
  color: #f56c6c;
  margin-bottom: 15px;
}
.file-upload-area {
  margin: 20px 0;
}
.import-dialog-content {
  padding: 0 10px;
}
</style>