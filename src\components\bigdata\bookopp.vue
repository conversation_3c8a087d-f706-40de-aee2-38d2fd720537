<template>
  <div class="mainbg">
    <el-form :inline="true" class="myform">
      <el-form-item label="客户：" class="flex">
        <el-input
          class="definput wid"
          v-model="pageBean.opportunityName"
          clearable
          placeholder="请输入客户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="单位：" class="flex">
        <el-input
          class="definput wid yong"
          v-model="pageBean.materialName"
          clearable
          placeholder="请输入单位名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="用书时间：" class="flex">
        <el-input
          class="definput wid dan"
          v-model="pageBean.customerName"
          clearable
          placeholder=""
        ></el-input>
      </el-form-item>
      <el-form-item label="用书专业：" label-width="85px">
        <el-select
          class="definput w100"
          popper-class="removescrollbar"
          v-model="typename"
          @change="changeOption"
          clearable
          placeholder="请选择"
        >
          <el-option key="" label="全部" value=""> </el-option>
          <el-option key="1" label="我负责的" value="1"> </el-option>
          <el-option key="2" label="我协作的" value="2"> </el-option>
        </el-select>
      </el-form-item>
      <el-button
        class="defaultbtn mt"
        icon="el-icon-search"
        type="primary"
        @click="searchAction"
        >搜索</el-button
      >
    </el-form>
    <el-table
      class="jhtable mytable1"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        prop="opportunityName"
        label="客户单位"
        align="center"
        min-width="200px"
      >
      </el-table-column>
      <el-table-column prop="customerName" label="客户" align="center">
      </el-table-column>
      <el-table-column prop="opportunityName" label="销售机会" align="center">
      </el-table-column>
      <el-table-column prop="estimatedAmount" label="预计金额" align="center">
        <template slot-scope="scope">
          <span>
            {{
              scope.row.estimatedAmount > 0
                ? scope.row.estimatedAmount + '万元'
                : '—'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="stage" label="册数" align="center">
      </el-table-column>
      <el-table-column prop="stage" label="机会阶段" align="center">
      </el-table-column>
      <el-table-column prop="specialtyName" label="用书专业" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.specialtyName || '—' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="specialtyName" label="用书时间" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.specialtyName || '—' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="chargePersonName" label="负责人" align="center">
      </el-table-column>
      <el-table-column
        prop="specialtyName"
        label="跟进拜访次数"
        align="center"
        width="110px"
      >
        <template slot-scope="scope">
          <span class="numtime" @click="gotoDetail(scope.row)">
            {{ scope.row.visitCount + '次' }}
          </span>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page
      :currentPage="pageBean.pageNum"
      :total="total"
      :pageSize="pageBean.pageSize"
      @updatePageNum="handleCurrentChange"
    ></page>
    <el-drawer
      size="50%"
      title="跟进列表"
      :visible.sync="drawer"
      :direction="direction"
      :before-close="handleClose"
    >
      <div class="pwidth">
        <listitem
          @loadData="loadData"
          @audioPlay="audioPlay"
          :key="index"
          v-for="(item, index) in dataList"
          :item="item"
        ></listitem>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import page from '@/components/common/page.vue'
import nolist from '@/components/common/nolist.vue'
import { getDict, valide } from '@/utils/tools'
import {
  opportunityList,
  deleteOpportunity,
} from '@/api/clientMaintenance/opportunity'
import listitem from './listitem.vue'
import { visitList } from '@/api/visit/index'
export default {
  components: {
    page,
    nolist,
    listitem,
  },
  data() {
    return {
      dataList: [],
      drawer: false,
      direction: 'rtl',
      typename: '',
      dialogType: 1,
      dialogMessage: '确认删除该机会？',
      dialogVisible: false,
      stageVisible: false,
      options: [],
      isLoading: false,
      deleteData: {},
      tableData: [],
      total: 0,
      pageBean: {
        opportunityStage: '',
        opportunityName: '',
        customerName: '',
        chargePersonName: '',
        chargePersonDepartment: '',
        chargePerson: '',
        collaborator: '',
        pageNum: 1,
        pageSize: 10,
      },
      pageBeanV: {
        opportunityId: '',
        pageNum: 1,
        pageSize: 4,
      },
      types: [],
      type: 'opportunityName',
      loading: false,
      onOff: false,
      loadBoo: false,
      totalV: 0,
    }
  },
  created() {
    if (this.$route.query.unitId) {
      this.pageBean.unitId = this.$route.query.unitId
    }
    this.getTypeList()
    this.loadData()
  },
  methods: {
    loadDataVisit() {
      this.getData(1)
    },
    getData(type) {
      visitList(this.pageBeanV).then((res) => {
        if (res.status == 0) {
          if (type) {
            this.dataList = []
          }
          this.dataList = [...this.dataList, ...res.data]
          this.totalV = res.page.total
          this.dataList.forEach((item) => {
            if (item.soundList.length > 0) {
              this.$set(item, 'isPlay', false)
            }
          })
          this.onOff = false
          this.loading = false
          this.loadBoo = true
        }
      })
    },
    audioPlay(item, boolean) {
      this.dataList.forEach((item1) => {
        if (item1.soundList.length > 0) {
          item1.isPlay = false
        }
      })
      item.isPlay = boolean
    },
    handleClose() {
      this.drawer = false
    },
    gotoDetail(row) {
      this.drawer = true
      this.pageBeanV.opportunityId = row.id
      this.getData()
    },
    loadData() {
      this.isLoading = true
      opportunityList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    getTypeList() {
      getDict('OpportunityStage')
        .then((result) => {
          this.options = result
        })
        .catch((err) => {})
      getDict('OpportunityType')
        .then((result) => {
          this.types = result
        })
        .catch((err) => {})
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    changeOption(data) {
      if (data == '1') {
        this.pageBean.chargePerson = window.sessionStorage.getItem('userid')
        this.pageBean.collaborator = ''
      } else if (data == '2') {
        this.pageBean.chargePerson = ''
        this.pageBean.collaborator = window.sessionStorage.getItem('userid')
      } else {
        this.pageBean.chargePerson = ''
        this.pageBean.collaborator = ''
      }
    },
    updateStageVisible(val) {
      this.stageVisible = val
    },
    successBlock() {
      this.stageVisible = false
      this.loadData()
    },
    submitDialog() {
      this.dialogVisible = false
      if (this.dialogType == 1) {
        deleteOpportunity({ id: this.deleteData.id })
          .then((result) => {
            if (result.data) {
              this.$message({
                type: 'success',
                message: '删除成功',
              })
              this.loadData()
            } else {
              this.dialogMessage = result.msg
              this.dialogType = 2
              this.dialogVisible = true
            }
          })
          .catch((err) => {})
      } else {
      }
    },
    addChance() {
      this.$router.push({
        path: '/clientMaintenance/salesLead/add',
      })
    },
    deleteAction(data) {
      this.dialogType = 1
      this.dialogMessage = '确认删除该销售机会？'
      this.dialogVisible = true
      this.deleteData = data
    },
    onEdit(data) {
      this.$router.push({
        path: '/clientMaintenance/salesLead/add',
        query: {
          id: data.id,
        },
      })
    },
    handleCommand(index, data) {
      switch (index) {
        case 0: // 变更
          this.stageVisible = true
          this.$refs.stage.getTypeList()
          this.$refs.stage.updateData(data)
          break
        case 1:
          {
            this.$router.push({
              path: '/clientMaintenance/salesLead/detail',
              query: { id: data.id }, // 机会id
            })
          }
          break
        default:
          break
      }
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>
<style>
.mytable1 td,
th {
  padding: 14px 0;
}
.el-drawer__body {
  overflow: auto;
}
.el-drawer {
}
</style>
<style lang="scss" scoped>
.pwidth {
  padding: 0 20px;
  overflow-y: auto;
  height: 100%;
}
.numtime {
  color: #4285f4;
  cursor: pointer;
}
.yong {
  width: 240px !important;
}
.dan {
  width: 220px !important;
}
.w100 {
  width: 110px;
}
.wid {
  width: 200px;
}
.mr40 {
  margin-right: 40px;
}
.jhtable .el-button + .el-button {
  margin-left: 40px;
}
.mainbg {
  background-color: white;
  padding: 20px;
}
.pcc {
  margin: 0 auto;
  text-align: center;
}
.smtext {
  zoom: 0.8;
}
.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.mt {
  margin-top: 3px;
}
.cusnamecss {
  display: flex;
}
.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 20px;
}
.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}
.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}
.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}
.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}
.jhtable .el-button {
  padding: 2px;
}
.mr10 {
  margin-right: 10px;
}
.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}
.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: justify;
}
</style>