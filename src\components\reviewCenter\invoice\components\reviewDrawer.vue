<template>
  <el-drawer
    class="mydrawer"
    :visible.sync="visible"
    direction="rtl"
    size="45%"
    title="审核"
    @close="handleClose"
    center
  >
    <div class="drawer-content">
      <el-form class="infoform" ref="form" label-width="120px">
        <el-row
          :gutter="0"
          class="width100 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="12">
            <el-form-item label="合同金额：" class="labeltext">
              <span>{{ form.contractAmount || '--' }}万元</span>
            </el-form-item>
            <el-form-item label="未开票金额：" class="labeltext">
              <span>{{ form.noInvoicingTotalAmount }}万元</span>
            </el-form-item>
            <el-form-item label="币种：" class="labeltext">
              <span>{{ form.currencyName || '--' }}</span>
            </el-form-item>
            <el-form-item label="开票日期：" class="labeltext">
              <span>{{ form.invoicingDate }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开票金额：" class="labeltext">
              <span>{{ form.invoicingAmount || '--' }}万元</span>
            </el-form-item>
            <el-form-item label="开票类型：" class="labeltext">
              <span>{{ invoicingTypes[form.invoicingType] || '--' }}</span>
            </el-form-item>
            <el-form-item label="单位税号：" class="labeltext">
              <span>{{ form.taxIdentificationNumber || '--' }}</span>
            </el-form-item>
            <el-form-item label="抬头名称：" class="labeltext">
              <span>{{ form.invoiceHeader || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="0"
          class="width100 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="12">
            <el-form-item label="开户银行：" class="labeltext">
              <span>{{ form.openingBank || '--' }}</span>
            </el-form-item>
            <el-form-item label="单位地址：" class="labeltext">
              <span>{{ form.unitAddress || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户地址：" class="labeltext">
              <span>{{ form.openingAddress || '--' }}</span>
            </el-form-item>
            <el-form-item label="联系电话：" class="labeltext">
              <span>{{ form.contactsPhone || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="12">
            <el-form-item label="申请人：" class="labeltext">
              <span>{{ form.createByName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请时间：" class="labeltext">
              <span>{{ form.createTime }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="checkpeople">
          <span class="spanname">审核意见：</span>
          <eltimeline :businessId="data.businessId" :status="data.status" v-if="visible"></eltimeline>
        </div>
      </el-form>
      <div class="btns" v-if="handleType == 1">
        <el-button @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="handleApprove">通过</el-button>
      </div>
    </div>

    <!-- 通过对话框 -->
    <approve-dialog
      :visible.sync="approveDialogVisible"
      @submit="onApproveSubmit"
    ></approve-dialog>

    <!-- 驳回对话框 -->
    <reject-dialog
      :visible.sync="rejectDialogVisible"
      @submit="onRejectSubmit"
    ></reject-dialog>
  </el-drawer>
</template>

<script>
import ApproveDialog from '../../common/approveDialog.vue'
import RejectDialog from '../../common/rejectDialog.vue'
import { approve } from '@/api/reviewCenter'
import eltimeline from '@/components/common/eltimeline.vue'
import { contractinvoicingInfo } from '@/api/contract/index'
export default {
  name: 'ReviewDrawer',
  components: {
    ApproveDialog,
    RejectDialog,
    eltimeline,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    handleType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: () => {
        return new Object()
      },
    },
  },
  data() {
    return {
      approveDialogVisible: false,
      rejectDialogVisible: false,
      form: {},
      invoiceHeaderTypes: {
        1: '企业',
      },
      invoicingTypes: {
        1: '增值税专用发票',
        2: '增值税普通发票',
        3: '电子普通发票',
        4: '收据',
      },
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  methods: {
    loadinfo() {
      contractinvoicingInfo(this.data.businessId)
        .then((result) => {
          console.log('ddddffffgggg=====', result)
          this.form = result.data
        })
        .catch((err) => {})
    },
    handleClose() {
      this.visible = false
    },
    handleApprove() {
      this.approveDialogVisible = true
    },
    handleReject() {
      this.rejectDialogVisible = true
    },
    onApproveSubmit() {
      this.submit({
        id: this.data.id,
        status: 3,
      })
    },
    onRejectSubmit(data) {
      console.log('驳回提交的数据', data)
      this.submit({
        id: this.data.id,
        status: 4,
        remark: data,
      })
    },
    submit(par) {
      approve(par)
        .then((result) => {
          if (result.data) {
            this.visible = false
            if (par.status == 3) {
              this.msgSuccess('审核通过')
            } else {
              this.msgSuccess('已驳回')
            }
            this.$emit('reload')
          } else {
            this.$message.error(result.msg)
          }
        })
        .catch((err) => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
}
.boxItem {
  display: flex;
  align-items: center;
  line-height: 18px;
}
.title {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
}
/deep/ .el-drawer {
  .el-drawer__header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
}
.mydrawer /deep/.el-drawer__header {
  text-align: center;
  color: #333333;
}
.btns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.mb20 {
  margin-bottom: 20px;
}

.spanname {
  width: 110px;
  padding-right: 8px;
  text-align: right;
  flex-shrink: 0;
}
.checkpeople {
  display: flex;
  margin-left: 6px;
}
</style>
