<template>
  <div>
    <div>
      <back>{{ title }}</back>
    </div>
    <div class="mainbg" v-loading="isLoading">
      <el-form
        :rules="rules"
        :model="form"
        ref="addform"
        class="addfcss addfcss1"
        label-width="130px"
      >
        <textBorder>基础信息</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="8">
              <el-form-item prop="contractTitle" label="合同标题：">
                <el-input
                  clearable=""
                  v-model="form.contractTitle"
                  class="definput"
                  maxlength="100"
                  show-word-limit
                  placeholder="请输入合同标题"
                ></el-input>
              </el-form-item>
              <el-form-item label="客户单位：">
                <div class="unitbtn selectedCss">{{ form.unitName }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="canclepadding" required label="合同类型：">
                <el-form-item
                  class="els40"
                  prop="contractType"
                  label-width="0px"
                >
                  <el-select
                    @change="changeValue"
                    class="definput kuan ellipsis"
                    popper-class="removescrollbar"
                    v-model="form.contractType"
                    placeholder="请选择"
                    :disabled="!!$route.query.opportunityId"
                  >
                    <el-option
                      v-for="item in optionsContract"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  class="els50"
                  prop="contractTypeSub"
                  label-width="0px"
                >
                  <el-select
                    class="definput kuan ellipsis"
                    popper-class="removescrollbar"
                    v-model="form.contractTypeSub"
                    placeholder="请选择"
                    :disabled="!!$route.query.opportunityId"
                  >
                    <el-option
                      v-for="item in optionsContractDetail"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form-item>
              <el-form-item label="关联机会：">
                <div
                  class="unitbtn"
                  :class="{ selectedCss: form.opportunityName }"
                  @click="chooseChance"
                >
                  {{ form.opportunityId ? form.opportunityName : '请选择' }}
                  <i
                    @click.stop="closeCon"
                    v-if="form.opportunityName"
                    class="rcenter el-icon-close"
                  />
                  <i v-else class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="customerId" label="关联客户：">
                <div
                  class="unitbtn"
                  :class="{ selectedCss: form.customerName }"
                  @click="chooseCustomer"
                >
                  {{ form.customerName ? form.customerName : '请选择' }}
                  <i class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
              <el-form-item prop="expireTime" label="到期时间：">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  class="definput width100"
                  v-model="form.expireTime"
                  placeholder="选择到期时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                required
                label="合同编号："
                v-if="form.id"
                prop="contractNumber"
              >
                <el-input
                  class="definput"
                  disabled
                  v-model="form.contractNumber"
                  placeholder=""
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">合同信息</textBorder>
        <div class="pt20 bbline">
          <el-row type="flex" justify="start">
            <el-col :span="8">
              <el-form-item label="合同金额(万元)：" prop="contractAmount">
                <el-input
                  v-model="form.contractAmount"
                  class="definput"
                  type="number"
                  :placeholder="contractPlText"
                ></el-input>
              </el-form-item>
              <el-form-item label="收货人：" prop="receiver">
                <el-input
                  class="definput"
                  placeholder="请输入姓名及联系方式"
                  v-model="form.receiver"
                ></el-input>
              </el-form-item>
              <el-form-item prop="beginTime" label="开始时间：">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  class="definput width100"
                  v-model="form.beginTime"
                  placeholder="选择开始时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="折扣：" prop="discount">
                <el-input
                  v-model="form.discount"
                  :min="0"
                  :max="1"
                  class="definput"
                  type="number"
                  placeholder="如8折输入0.8）"
                ></el-input>
              </el-form-item>
              <el-form-item label="收货人地址：" prop="deliveryAddress">
                <el-input
                  v-model="form.deliveryAddress"
                  class="definput"
                  placeholder="请输入收货人地址"
                ></el-input>
              </el-form-item>
              <el-form-item prop="endTime" label="结束时间：">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  class="definput width100"
                  v-model="form.endTime"
                  placeholder="选择结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="质保金年限：" prop="warrantyPeriod">
                <el-input
                  class="definput"
                  type="number"
                  v-model="form.warrantyPeriod"
                  placeholder="请输入质保金年限"
                ></el-input>
              </el-form-item>
              <el-form-item label="仓库：" prop="warehouseId">
                <el-select
                  v-model="form.warehouseId"
                  class="definput"
                  placeholder="请选择仓库"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">电子合同</textBorder>
        <div class="pt20 bbline">
          <el-row>
            <el-col :span="16">
              <el-form-item class="mb0" label="电子合同：" prop="fileInfoList">
                <upload2
                  ref="uploadFile"
                  :limit="1"
                  @submitImg="submitFile"
                  accept=".pdf"
                  :fileList="fileListF"
                >
                  <span class="studiocss">
                    <img src="../../../assets/addhe.png" />
                    <span class="uploadtext deffont">点击添加</span>
                  </span>
                  <template slot="ptip">
                    <p>只能上传pdf文件</p>
                  </template>
                </upload2>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">负责与协作</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <!-- v-if="form.id" -->
            <el-col :span="8">
              <el-form-item required label="负责人：">
                <span v-if="form.chargePerson" class="mr10">{{
                  form.chargePersonName
                }}</span>
                <el-button
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择负责人', 1)"
                  >点击选择负责人</el-button
                >
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="协作人：" prop="collaborator">
                <el-tag
                  class="tagcss"
                  size="small"
                  v-for="item in xiezuolist"
                  closable
                  @close="handleTagClose(item)"
                  :key="item.id"
                  >{{ item.name }}</el-tag
                >
                <el-button
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择协作人', 5)"
                  >点击选择协作人</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">补充信息</textBorder>
        <div class="pt20">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="16">
              <el-form-item label="备注：">
                <el-input
                  v-model="form.notes"
                  maxlength="300"
                  show-word-limit
                  class="definput"
                  type="textarea"
                  rows="4"
                  placeholder="请输入备注信息"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="pt20 btncenter">
          <el-form-item>
            <el-button class="btn_h42 wid98" type="primary" @click="saveAction"
              >保存</el-button
            >
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 负责人/协作人 -->
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>

    <!-- 客户选择 -->
    <cuatomerDialog
      ref="customer"
      :visible.sync="customerDialogVisible"
      @updateVisible="updateVisible"
      @updateCustomer="updateCustomer"
    ></cuatomerDialog>

    <!-- 机会选择 -->
    <chanceDialog
      ref="changeRef"
      :visible.sync="chanceVisible"
      @updateVisible="updateChanceVisible"
      @updateData="updateChanceData"
    ></chanceDialog>
    <!-- 部门验证组件 -->
    <verifyDeparment
      ref="verifyDeparment"
      @submit="addData"
    ></verifyDeparment>
  </div>
</template>

<script>
import back from '@/components/common/back.vue'
import textBorder from '@/components/common/textBorder.vue'
import systemDialog from '@/components/common/systemDialog.vue'
import cuatomerDialog from '@/components/common/customerDialog.vue'
import chanceDialog from '@/components/common/chanceDialog.vue'
import upload2 from '@/components/common/upload2.vue'
import { getDict } from '@/utils/tools.js'
import { opportunityInfo } from '@/api/clientMaintenance/opportunity'
import { contractSave, contractInfo, updateContract } from '@/api/contract'
import { queryVoListByCode } from '@/api/wapi.js'
import VerifyDeparment from '@/components/common/verifyDeparment.vue'
export default {
  components: {
    back,
    textBorder,
    systemDialog,
    cuatomerDialog,
    chanceDialog,
    upload2,
    VerifyDeparment
  },
  data() {
    return {
      title: this.$route.query.id ? '编辑合同' : '新建合同',
      isLoading: false,
      rules: {
        contractTitle: [
          { required: true, message: '请输入合同标题', trigger: 'blur' },
        ],
        customerId: [
          { required: true, message: '请选择客户', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' },
        ],
        beginTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' },
        ],
        contractType: [
          { required: true, message: '请选择合同类型', trigger: 'change' },
        ],
        contractTypeSub: [
          { required: true, message: '请选择合同子类型', trigger: 'change' },
        ],
        fileInfoList: [
          { required: true, message: '请上传电子合同', trigger: 'change' },
        ],
        contractAmount: [
          { required: true, message: '请输入合同金额', trigger: 'blur' },
        ],
      },
      fileListF: [],
      optionsContract: [],
      optionsContractDetail: [],
      chanceVisible: false,
      customerDialogVisible: false,
      dialogVisible: false,
      isSmall: window.screen.availWidth < 1500 ? true : false,
      dialogName: '',
      inputValue: '',
      isDeleteTag: false,
      form: {
        id: '',
        contractTitle: '',
        contractNumber: '',
        customerId: '',
        customerName: '',
        opportunityId: '',
        opportunityName: '',
        unitName: '',
        expireTime: '',
        contractType: '',
        contractTypeSub: '',
        contractAmount: '',
        discount: '',
        receiver: '',
        deliveryAddress: '',
        warehouseId: '',
        beginTime: '',
        endTime: '',
        warrantyPeriod: '',
        chargePerson: '',
        collaborator: '',
        notes: '',
        fileInfoList: [],
        contractDepartmentId: '',
      },
      contractPlText: '请输入金额',
      xiezuolist: [],
      options: [],
      unitDialogVisible: false,
      multipleNum: 1,
      editId: '',
      isChooseCustomer: false,
    }
  },
  created() {
    if (this.$route.query.customerId) {
      this.form.customerId = this.$route.query.customerId
      this.form.customerName = this.$route.query.customerName
      this.form.unitId = this.$route.query.unitId
      this.form.unitName = this.$route.query.unitName
      this.isChooseCustomer = true
    }
    if (this.$route.query.id) {
      this.loadInfo()
    } else {
      this.getDictApi()
    }
    if (this.$route.query.opportunityId) {
      this.loadOpportunityInfo(this.$route.query.opportunityId)
    }
    getDict('Storage').then((res) => {
      this.options = res
    })
  },
  methods: {
    loadOpportunityInfo(opportunityId) {
      this.isLoading = true
      opportunityInfo(opportunityId)
        .then((result) => {
          const opportunity = result.data
          this.form.opportunityId = opportunity.id
          this.form.opportunityName = opportunity.opportunityName
          this.form.contractTitle = opportunity.opportunityName
          this.form.contractAmount = opportunity.estimatedAmount
          this.form.contractType = opportunity.opportunityType
          this.form.contractTypeSub = result.data.opportunityTypeSub
          this.form.customerId = opportunity.customerId
          this.form.customerName = opportunity.customerName
          this.form.unitId = opportunity.unitId
          this.form.unitName = opportunity.unitName
          this.isLoading = false
          this.getDictApi()
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    getDictApi() {
      queryVoListByCode({ code: 'ContractType' }).then((res) => {
        if (res.status == 0 && res.data.length > 0) {
          this.optionsContract = res.data
          if (this.form.contractType) {
            this.setValue(this.form.contractType)
          }
        }
      })
    },
    loadInfo() {
      this.isLoading = true
      contractInfo(this.$route.query.id)
        .then((result) => {
          this.form = result.data
          this.form.warehouseId =
            this.form.warehouseId == 0 ? '' : this.form.warehouseId
          this.form.discount = this.form.discount == 0 ? '' : this.form.discount
          this.form.warrantyPeriod =
            this.form.warrantyPeriod == 0 ? '' : this.form.warrantyPeriod
          this.form.contractAmount =
            this.form.contractAmount == 0 ? '' : this.form.contractAmount
          // 负责人/协作人
          var ids =
            result.data.collaborator && result.data.collaborator.split(',')
          let names =
            result.data.collaboratorName &&
            result.data.collaboratorName.split(',')
          ids &&
            ids.forEach((item, index) => {
              var data = {}
              data.id = item
              data.name = names[index]
              this.xiezuolist.push(data)
            })
          this.fileListF = result.data.fileInfoList
          this.getDictApi()
          // 电子合同
          this.$nextTick(() => {
            this.$refs.uploadFile.changeFileList(result.data.fileInfoList)
          })
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    changeValue(val) {
      if (val == '') {
        this.form.contractTypeSub = ''
        this.optionsContractDetail = []
      } else {
        this.form.contractTypeSub = ''
        let item = this.optionsContract.filter((item) => item.id === val)
        this.optionsContractDetail = item[0].children
        if (val === '1215413373253166238') {
          this.contractPlText = '请填写实洋'
        } else {
          this.contractPlText = '请输入金额'
        }
      }
    },
    setValue(val) {
      let item = this.optionsContract.filter((item) => item.id === val)
      this.optionsContractDetail = item[0].children
    },
    submitFile(fileList) {
      this.fileListF = fileList
      this.fileListF.forEach((item) => (item.fileType = 4))
      this.form.fileInfoList = this.fileListF
      this.$refs.addform.validateField(['fileInfoList'])
    },
    saveAction() {
      this.$refs['addform'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.form.beginTime > this.form.endTime) {
          this.$message({
            type: 'error',
            message: '开始时间不能大于结束时间',
          })
          return false
        }
        if (this.form.id) {
          this.updateData()
        } else {
          // 多部门验证
          this.$refs.verifyDeparment.verify()
        }
      })
    },
    addData(departmentId) {
      this.form.createDepartmentId = departmentId;
      var cdata = Object.assign({}, this.form)
      contractSave(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功！',
            })
            this.$route.params.data = {
              id: result.data,
              contractTitle: cdata.contractTitle,
            }
            this.$route.params.isSaveSuccess = true
            this.$router.back()
          } else {
            this.$message({
              type: 'error',
              message: '保存失败！',
            })
          }
        })
        .catch((err) => {})
    },
    updateData() {
      updateContract(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '更新成功！',
            })
            this.$router.back()
          } else {
            this.$message({
              type: 'error',
              message: '保存失败！',
            })
          }
        })
        .catch((err) => {})
    },
    chooseChance() {
      if (this.$route.query.opportunityId) {
        return
      }
      this.form.opportunityId =
        this.form.opportunityId == 0 ? '' : this.form.opportunityId
      let params = {
        id: this.form.opportunityId,
        methodName: 'list',
        className: 'ContractController',
      }
      this.$refs.changeRef.selectChanceData(params)
      this.chanceVisible = true
    },
    updateChanceVisible(val) {
      this.chanceVisible = val
    },
    updateChanceData(data) {
      this.form.opportunityId = data.id
      this.form.opportunityName = data.opportunityName
    },
    updateUnit(data) {
      this.form.unit = data.name
      this.form.unitId = data.id
    },
    updateUnitVisible(val) {
      this.unitDialogVisible = val
    },
    updateCustomer(data) {
      this.form.customerName = data.customerName
      this.form.customerId = data.id
      this.form.unitId = data.unitId
      this.form.unitName = data.unitName
      this.$refs.addform.validateField('customerId')
    },
    handleTagClose(tag) {
      this.xiezuolist.splice(this.xiezuolist.indexOf(tag), 1)
      this.xiezuoData(this.xiezuolist)
    },
    xiezuoData(list) {
      var ids = []
      var names = []
      list.forEach((item) => {
        ids.push(item.id)
        names.push(item.name)
      })
      this.form.collaborator = ids.join(',')
      this.form.collaboratorName = names.join(',')
    },
    chooseCustomer() {
      if (this.$route.query.opportunityId) {
        return // 如果已经有客户，则不执行选择操作
      }
      this.customerDialogVisible = true
      this.$refs.customer.selectCustomerData({
        id: this.form.customerId,
        className: 'ContractController',
      })
    },
    chooseunit() {
      this.unitDialogVisible = true
    },
    updateVisible(val) {
      this.customerDialogVisible = val
    },
    // 选择协作人
    clickXuan(name, multipleNum) {
      this.dialogName = name
      this.multipleNum = multipleNum
      this.$refs.systemdialog.loadData()
      if (name == '选择负责人') {
        this.form.chargePerson
          ? this.$refs.systemdialog.updateWorksId([
              {
                id: this.form.chargePerson,
                name: this.form.chargePersonName,
                departmentId: this.form.contractDepartmentId,
              },
            ])
          : this.$refs.systemdialog.updateWorksId([])
      } else if (name == '选择协作人') {
        this.form.collaborator
          ? this.$refs.systemdialog.updateWorksId(this.xiezuolist)
          : this.$refs.systemdialog.updateWorksId([])
      }
      this.dialogVisible = true
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    submitData(data, type, departmentId) {
      console.log(departmentId)
      if (type == '选择负责人') {
        this.form.chargePerson = data.length > 0 ? data[0].id : ''
        this.form.chargePersonName = data.length > 0 ? data[0].name : ''
        this.form.contractDepartmentId = departmentId
      } else if (type == '选择协作人') {
        this.xiezuoData(data)
        this.xiezuolist = data
      }
      this.updateSystemVisible(false)
    },
    closeCon() {
      this.form.opportunityId = ''
      this.form.opportunityName = ''
    },
  },
}
</script>
<style lang="scss" scoped>
.addfcss1 /deep/ .el-form-item {
  margin-bottom: 18px;
  min-height: 34px;
  line-height: 34px;
  padding-right: 0 !important;
}
.canclepadding /deep/ .el-form-item {
  margin-bottom: 0px !important;
}
// .kuan {
//   width: 170px;
// }
.tagcss /deep/ .el-icon-close {
  width: 12px;
  height: 12px;
  line-height: 12px;
  background-color: #4285f4;
  color: white;
}
.tagcss {
  margin-left: 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  background: #dfeafd;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}

.selectedCss {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.ellipsis /deep/.el-input__inner {
  padding: 0 8px !important;
}
.els50 {
  width: 58%;
}
.els40 {
  width: 40%;
  margin-right: 1%;
  float: left;
}
.els30 {
  width: 44%;
}
.els50:last-child {
  margin-right: 0;
  margin-left: 1%;
  float: right;
}
.studiocss img {
  width: 16px;
  height: 16px;
  margin-right: 3px;
  margin-top: -3px;
}
.width100 {
  width: 100%;
}
.wid98 {
  width: 98px;
}
.uploadtext {
  color: #4285f4;
  cursor: pointer;
}
.uploadcss {
  width: 88px;
  height: 88px;
  line-height: 24px;
  background: #f5f5f5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #cccccc;
}
.uploadcss i {
  margin-top: 20px;
  font-size: 24px;
  color: #4285f4;
}
.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
  top: 0;
  z-index: 10;
}

.btncenter {
  text-align: center;
}
.mainbg {
  margin: 16px 0px;
  padding: 20px 16px;
  background-color: white;
  border-radius: 8px;
}
.pd0 {
  padding-right: 0px !important;
}
</style>
<style>
</style>
