<template>
  <div>
    <div>
      <back>{{ title }}</back>
    </div>
    <div class="card">
      <el-form
        ref="form"
        class="myform"
        :rules="rules"
        :model="form"
        label-width="110px"
        v-loading="isLoading"
      >
        <textBorder>基本信息</textBorder>
        <el-row :gutter="20" class="mt20">
          <el-col :span="8">
            <el-form-item label="单位名称:" prop="unitName">
              <el-input
                class="definput"
                v-model="form.unitName"
                placeholder="请输入单位名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="联系电话:" prop="phone">
              <el-input
                class="definput"
                v-model="form.phone"
                placeholder="请输入联系电话"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="来源:" prop="source">
              <el-select
                class="definput"
                v-model="form.source"
                placeholder="请选择"
                popper-class="removescrollbar"
              >
                <el-option label="公司资源" value="1"></el-option>
                <el-option label="自主开拓" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="传真:" prop="fax">
              <el-input
                class="definput"
                v-model="form.fax"
                placeholder="请输入传真"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="网址:" prop="website">
              <el-input
                class="definput"
                v-model="form.website"
                placeholder="请输入网址"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <textBorder>单位画像</textBorder>
        <el-row :gutter="20" class="mt20">
          <el-col :span="8">
            <el-form-item label="单位类型:" prop="unitType">
              <el-select
                @change="changeType"
                class="definput"
                v-model="form.unitType"
                placeholder="请选择"
                popper-class="removescrollbar"
              >
                <el-option
                  v-for="item in unitTypeData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="单位地址:" prop="addressData">
              <el-cascader
                v-model="form.addressData"
                class="definput unitf"
                :options="options"
                :props="{
                  value: 'id',
                  label: 'name',
                  children: 'areaList',
                  checkStrictly: true,
                }"
                @change="changeAddress"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位特点:" prop="unitCharacter">
              <el-select
                class="definput"
                v-model="form.unitCharacter"
                placeholder="请先选择类型"
                popper-class="removescrollbar"
              >
                <el-option
                  v-for="item in characterData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="详细地址:" prop="address">
              <el-input
                rows="4"
                type="textarea"
                maxlength="300"
                show-word-limit
                v-model="form.address"
                placeholder="请输入详细地址"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="curUnitTypeName != '其他'" :span="16" class="mt10">
            <el-form-item label="教材供应商:" prop="notes">
              <el-input
                rows="4"
                type="textarea"
                maxlength="50"
                show-word-limit
                v-model="form.supplier"
                placeholder="请输入教材供应商"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="curUnitTypeName != '其他'" :span="16" class="mt10">
            <el-form-item label="信息化合作商:" prop="notes">
              <el-input
                rows="4"
                type="textarea"
                maxlength="50"
                show-word-limit
                v-model="form.informatizationPartner"
                placeholder="请输入信息化合作商"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <textBorder v-if="isShowChargePerson">单位负责人</textBorder>
        <div class="pt20 bbline" v-if="isShowChargePerson">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="20">
              <el-form-item label="负责人：" prop="chargePersonList">
                <el-tag
                  class="tagcss"
                  size="small"
                  v-for="(item, index) in form.chargePersonList"
                  :closable="!getDisable()"
                  @close="handleTagClose(index, item)"
                  :key="item.chargePerson"
                  >{{ item.chargePersonName }}</el-tag
                >
                <el-button
                  v-if="!getDisable()"
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择负责人', 5)"
                  >点击选择负责人</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder>补充信息</textBorder>
        <el-row :gutter="20" class="mt20">
          <el-col :span="16">
            <el-form-item label="备注:" prop="notes">
              <el-input
                rows="4"
                type="textarea"
                maxlength="1000"
                show-word-limit
                v-model="form.notes"
                placeholder="请输入备注"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="单位结构图:">
          <el-upload class="avatar-uploader" :action="getUrl" :show-file-list="false" :before-upload="beforeUpload"
            :on-success="handleAvatarSuccess" :on-error="handleError" :on-remove="handleRemove" :headers="headers"
            :data="fileData" accept=".jpg, .png, .jpeg, .JPG, .PNG, .JPEG">
            <img v-if="form.unitStructureUrl" :src="form.unitStructureUrl" class="avatar">
            <div v-else class="avatardiv">
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div class="uptext">点击上传</div>
            </div>
          </el-upload>
          <div class="msgcss">
            <p>温馨提示：</p>
            <p>1.图片格式为jpg、png、jpeg；</p>
            <p>2.图片大小不能超过10M；</p>
          </div>
        </el-form-item> -->
        <el-button
          type="primary"
          class="savecss btn_h42 w98"
          @click="submitForm"
          >保存</el-button
        >
      </el-form>
    </div>
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
    <!-- 多部门验证 -->
    <verifyDeparment
    ref="verifyDeparment"
    @submit="submitWithDepartment"></verifyDeparment>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import textBorder from '../../common/textBorder.vue'
import {
  addUnit,
  updateUnit,
  unitInfo,
  unitType,
  queryUnitCharacter,
} from '@/api/clientmanagement/unit'
import { deleteFile, queryAreaVoList } from '@/api/index'
import { getToken } from '@/utils/auth'
import { validateWWw, ValidatePhone, ValidateFaxNumber } from '@/utils/tools.js'
import { getDict } from '@/utils/tools'
import systemDialog from '../../common/systemDialog.vue'
import VerifyDeparment from '@/components/common/verifyDeparment.vue'
export default {
  components: {
    back,
    textBorder,
    systemDialog,
    VerifyDeparment
  },
  data() {
    return {
      title: this.$route.query.id ? '编辑单位' : '新建单位',
      isLoading: false,
      rules: {
        unitName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
        ],
        phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        address: [
          { required: true, message: '请输入单位详细地址', trigger: 'blur' },
        ],
        addressData: [
          { required: true, message: '请选择单位地址', trigger: 'blur' },
        ],
        source: [{ required: true, message: '请选择来源', trigger: 'change' }],
        unitType: [
          { required: true, message: '请选择单位类型', trigger: 'change' },
        ],
        unitCharacter: [
          { required: true, message: '请选择单位特点', trigger: 'change' },
        ],
        chargePersonList: [
          { required: true, message: '请选择负责人', trigger: 'change' },
        ],
      },
      form: {
        id: this.$route.query.id,
        unitName: '',
        website: '',
        source: '',
        fax: '',
        phone: '',
        address: '',
        notes: '',
        unitStructureUrl: '',
        unitStructure: '',
        unitType: '',
        unitCharacter: '',
        chargePersonName: '',
        chargePerson: '',
        supplier: '',
        informatizationPartner: '',
        addressData: [],
        provinceId: '',
        cityId: '',
        chargePersonList: [],
      },
      getUrl: `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`,
      headers: { Authorization: getToken() },
      fileData: {
        applicationId: sessionStorage.getItem('applicationId'),
        serviceName: 'crm/unit',
      },
      curUnitTypeName:'',
      unitTypeData: [],
      characterData: [],
      isShowChargePerson: this.$route.query.id ? true : false,
      dialogVisible: false,
      multipleNum: 0,
      dialogName: '',
      chargePersonList: [],
      options: [],
    }
  },
  created() {
    if (this.$route.query.id) {
      unitInfo(this.form.id).then((result) => {
        let characterId = result.data.unitCharacter
        this.form = result.data
        this.form.unitCharacter = ''
        if (!this.form.chargePersonList) {
          this.form.chargePersonList = []
        }
        if (result.data.unitType) {
          this.changeType(result.data.unitType, () => {
            this.form.unitCharacter = characterId
          })
        }
        this.queryAreaVoListApi(true)
      })
    } else {
      this.queryAreaVoListApi()
    }
    getDict('UnitType').then((result) => {
      this.unitTypeData = result
      if (!this.curUnitTypeName && this.form.unitType) {
        this.getUnitTypeName(this.form.unitType)
      }
    })
  },
  methods: {
    getUnitTypeName(val){
      var list =  this.unitTypeData.filter(item => item.id === val)
      if (list.length>0) {
        this.curUnitTypeName  = list[0].name
      }
    },
    handleTreeList(list) {
      // 删除第三级children
      for (var i = 0; i < list.length; i++) {
        if (list[i].areaList.length < 1) {
          // 判断children的数组长度
          list[i].areaList = undefined
        } else {
          this.handleTreeList(list[i].areaList)
        }
      }
      return list
    },
    queryAreaVoListApi(boolean) {
      queryAreaVoList({ level: 0 }).then((res) => {
        if (res.status == 0) {
          this.options = this.handleTreeList(res.data)
          if (boolean) {
            if (this.form.provinceId) {
              this.$set(this.form, 'addressData', [])
              if (this.form.cityId != 0 && this.form.cityId != '0') {
                this.form.addressData[1] = this.form.cityId
              }
              this.form.addressData[0] = this.form.provinceId
            }
            console.log(this.form)
          }
        }
      })
    },
    getDisable() {
      var datascope = sessionStorage.getItem('dataScope')
      if (datascope == 4) {
        return false
      }
      return true
    },
    changeAddress(val) {},
    changeType(val, callback) {

      this.getUnitTypeName(val)
      if (!val) {
        this.characterData = res.data
        this.form.unitCharacter = ''
      } else {
        queryUnitCharacter({ parentId: val }).then((res) => {
          if (res.status == 0) {
            this.characterData = res.data
            this.form.unitCharacter = ''
            if (callback) {
              callback()
            }
          }
        })
      }
    },
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.form.website) {
          if (!validateWWw(this.form.website)) {
            this.$message({
              type: 'error',
              message: ' 请输入正确的网址',
            })
            return
          }
        }
        if (this.form.phone) {
          if (!ValidatePhone(this.form.phone)) {
            this.$message({
              type: 'error',
              message: ' 请输入正确的联系方式',
            })
            return
          }
        }
        if (this.form.addressData) {
          if (this.form.addressData.length == 1) {
            this.form.provinceId = this.form.addressData[0]
            this.form.cityId = ''
          } else {
            this.form.provinceId = this.form.addressData[0]
            this.form.cityId = this.form.addressData[1]
          }
        }
        this.form.unitName = this.form.unitName.replace(/\s*/g, '')
        if (this.form.id) {
          // 编辑
          updateUnit(this.form)
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '更新成功！',
                })
                this.$router.back()
              } else {
                this.$message({
                  type: 'error',
                  message: '保存失败！',
                })
              }
            })
            .catch((err) => {})
        } else {
          // 多部门验证
          this.$refs.verifyDeparment.verify()
        }

      })
    },
    submitWithDepartment(departmentId) {
      this.form.createDepartmentId = departmentId;
      addUnit(this.form)
      .then((result) => {
        if (result.data) {
          this.$message({
            type: 'success',
            message: '添加成功！',
          })
          this.$router.back()
        } else {
          this.$message({
            type: 'error',
            message: '保存失败！',
          })
        }
      })
      .catch((err) => {})
    },
    beforeUpload(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['jpg', 'png', 'jpeg', 'JPG', 'PNG', 'JPEG']
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error(
          '单位结构图片只能是 .jpg, .png, .jpeg, .JPG, .PNG, .JPEG 格式!'
        )
        return false
      }
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('单位结构图片图片大小不能超过10MB!')
        return false
      }
      this.isLoading = true
    },
    handleError() {
      this.isLoading = false
    },
    handleRemove(file, fileList) {
      this.form.unitStructureUrl = ''
      this.form.unitStructure = ''
    },
    handleAvatarSuccess(res, file) {
      this.isLoading = false
      this.form.unitStructureUrl = res.data.url
      this.form.unitStructure = res.data.fileName
    },
    // 选择协作人
    clickXuan(name, multipleNum) {
      this.dialogName = name
      this.multipleNum = multipleNum
      this.$refs.systemdialog.loadData()
      if (name == '选择负责人') {
        var list = []
        this.form.chargePersonList.forEach((element) => {
          var item = {}
          item.id = element.chargePerson
          item.name = element.chargePersonName
          item.departmentId = element.departmentId
          list.push(item)
        })
        this.form.chargePersonList
          ? this.$refs.systemdialog.updateWorksId(list)
          : this.$refs.systemdialog.updateWorksId([])
      }
      this.dialogVisible = true
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    //  负责人/协作人
    submitData(data, type, departmentId) {
      if (type == '选择负责人') {
        if (data.length > 5) {
          this.$message.warning('最多只能选择5个负责人')
          return
        }
        this.xiezuoData(data)
        var list = []
        data.forEach((element) => {
          var item = {
            chargePerson: element.id,
            chargePersonName: element.name,
            departmentId: element.departmentId,
          }
          list.push(item)
        })
        this.form.chargePersonList = list
        this.$refs.form.validateField('chargePerson')
      }
      this.updateSystemVisible(false)
    },
    handleTagClose(index, data) {
      this.form.chargePersonList.splice(index, 1)
    },
    xiezuoData(list) {
      var ids = []
      var names = []
      list.forEach((item) => {
        ids.push(item.id)
        names.push(item.name)
      })
      this.form.chargePerson = ids.join(',')
      this.form.chargePersonName = names.join(',')
    },
  },
}
</script>

<style lang="scss" scoped>
.unitf {
  width: 100%;
}
.myform .el-form-item {
  margin-right: 0px !important;
}
.mt10 {
  margin-top: 10px;
}
.msgcss {
  padding-top: 10px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
}

.w98 {
  width: 98px;
}

.savecss {
  display: block;
  margin: 0 auto;
}

.mt20 {
  margin-top: 20px;
  width: 100%;
}

.card {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #4285f4;
}

.avatar-uploader-icon {
  font-size: 24px;
  color: #4285f4;
  margin-top: 16px;
}

.avatar {
  width: 88px;
  height: 88px;
  background: #f5f5f5;
  border-radius: 4px 4px 4px 4px;
  border: 1px dashed #cccccc;
  object-fit: cover;
  // display: block;
}

.uptext {
  height: 18px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  line-height: 16px;
}

.avatardiv {
  width: 88px;
  height: 88px;
  background: #f5f5f5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #cccccc;
}
.tagcss /deep/ .el-icon-close {
  width: 12px;
  height: 12px;
  line-height: 12px;
  background-color: #4285f4;
  color: white;
}
.tagcss {
  margin-left: 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  background: #dfeafd;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}
.definput /deep/.el-input .el-input__inner {
  width: 100% !important;
}
</style>
