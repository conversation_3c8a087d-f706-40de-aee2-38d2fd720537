<template>
  <el-dialog :title="'选择图标'" width="68%" :close-on-click-modal="false" :visible.sync="visible" append-to-body>
    <div class="iconStyle">
      <div class="fa-icons">
        <ul class="fa-icons-list">
          <li @click="getIconData($event)">
            <img class="house" src="../../../assets/menu/house.png" alt="">
            首页
          </li>
          <li @click="getIconData($event)">
            <img class="kehu" src="../../../assets/menu/kehu.png" alt="">
            客户管理
          </li>
          <li @click="getIconData($event)">
            <img class="kehuweihu" src="../../../assets/menu/kehuweihu.png" alt="">
            客户维护
          </li>
          <li @click="getIconData($event)">
            <img class="peoject" src="../../../assets/menu/peoject.png" alt="">
            项目管理
          </li>

          <li @click="getIconData($event)">
            <img class="gongdan" src="../../../assets/menu/gongdan.png" alt="">
            工单管理
          </li>
          <li @click="getIconData($event)">
            <img class="shuju" src="../../../assets/menu/shuju.png" alt="">
            数据中心
          </li>


          <li @click="getIconData($event)">
            <img class="study" src="../../../assets/menu/study.png" alt="">
            学习与分享
          </li>

          <li @click="getIconData($event)">
            <img class="shuju" src="../../../assets/menu/shuju.png" alt="">
            数据分析
          </li>

          <li @click="getIconData($event)">
            <img class="mubiao" src="../../../assets/menu/mubiao.png" alt="">
            目标管理
          </li>
          <li @click="getIconData($event)">
            <img class="fahuo" src="../../../assets/menu/fahuo.png" alt="">
            教材发货
          </li>
          <li @click="getIconData($event)">
            <img class="dict" src="../../../assets/menu/dict.png" alt="">
            字典管理
          </li>
          <li @click="getIconData($event)">
            <img class="product" src="../../../assets/menu/product.png" alt="">
            产品管理
          </li>
          <li @click="getIconData($event)">
            <img class="fafang" src="../../../assets/menu/fafang.png" alt="">
            发放管理
          </li>
          <li @click="getIconData($event)">
            <img class="xitong" src="../../../assets/menu/xitong.png" alt="">
            系统管理
          </li>
          <li @click="getIconData($event)">
            <img class="jiaoyongliang" src="../../../assets/menu/jiaoyongliang.png" alt="">
            教材用量数据
          </li>
          <li @click="getIconData($event)">
            <img class="shangwu" src="../../../assets/menu/shangwu.png" alt="">
            商务数据
          </li>
          <li @click="getIconData($event)">
            <img class="plan" src="../../../assets/menu/plan.png" alt="">
            计划管理
          </li>
          <li @click="getIconData($event)">
            <img class="zongjie" src="../../../assets/menu/zongjie.png" alt="">
            总结管理
          </li>
          <li @click="getIconData($event)">
            <img class="ribao" src="../../../assets/menu/ribao.png" alt="">
            日报管理
          </li>
          <li @click="getIconData($event)">
            <img class="hetong" src="../../../assets/menu/hetong.png" alt="">
            合同管理
          </li>






        </ul>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      message: ''
    }
  },
  methods: {
    init() {
      this.visible = true
    },
    getIconData(event) {
      let currentElement = event.currentTarget
      let className = currentElement.firstElementChild.className
      this.visible = false
      this.$emit('setIconData', className)
    }
  }
}
</script>
<style>
.fa-icons {
  float: left;
}

.fa-icons li {
  float: left;
  width: 100px;
  margin-bottom: 12px;
  list-style: none;
  cursor: pointer;
  text-align: center;
}

.fa-icons li:hover {
  background-color: #f0f0f0;
}

.iconStyle {
  height: 500px;
  overflow-y: auto;
}
</style>
