<!-- 富文本编辑 -->
<template>
  <el-dialog
    title="添加"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="60%"
    class="articleDialog"
  >
    <el-form
      :model="ruleForm"
      :rules="Rule.ARTICLE_ADD"
      ref="ruleForm"
      label-width="100px"
    >
      <el-form-item label="文章标题：" prop="title">
        <el-input v-model="ruleForm.title"></el-input>
      </el-form-item>
      <el-form-item label="作者：" prop="author">
        <el-input v-model="ruleForm.author"></el-input>
      </el-form-item>
      <el-form-item label="简介：" prop="summany">
        <el-input v-model="ruleForm.summany"></el-input>
      </el-form-item>
      <el-form-item label="发布：" prop="isPublication">
        <el-radio-group v-model="ruleForm.isPublication">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序号：" prop="sortNo">
        <el-input-number v-model="ruleForm.sortNo" :min="0"></el-input-number>
      </el-form-item>
      <el-form-item label="文章内容：" prop="content" class="item-conent-edit">
        <quill-editor
          ref="myTextEditor"
          class="conent-edit"
          v-model="ruleForm.content"
          :options="editorOption"
        ></quill-editor>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="handleSaveEdit('ruleForm')"
          >保 存</el-button
        >
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
export default {
  components: {
    quillEditor,
  },
  data() {
    return {
      ruleForm: {
        isPublication: 1,
        title: '',
        author: '',
        summany: '',
        sortNo: '',
        content: '',
        categoryType: '',
      },
      editorOption: {},
      visible: false,
      editorid: '',
    }
  },
  methods: {
    init(type, id, currentObj) {
      this.ruleForm.categoryType = type
      this.ruleForm.categoryId = id || ''
      if (currentObj) {
        this.ruleForm.id = currentObj.id || ''
      } else {
        this.ruleForm.id = ''
      }
      this.visible = true
      if (this.ruleForm.id) {
        this.ruleForm.title = currentObj.title
        this.ruleForm.author = currentObj.author
        this.ruleForm.summany = currentObj.summany
        this.ruleForm.isPublication = currentObj.isPublication
        this.ruleForm.sortNo = currentObj.sortNo
        this.ruleForm.content = currentObj.content
      } else {
        this.clearForm()
      }
    },
    clearForm() {
      this.ruleForm.title = ''
      this.ruleForm.author =
        this.$store.state.user.username || localStorage.getItem('username')
      this.ruleForm.summany = ''
      this.ruleForm.sortNo = ''
      this.ruleForm.content = ''
    },
    // 弹框保存
    handleSaveEdit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let url = this.ruleForm.id
            ? '/sf/business/article/update'
            : '/sf/business/article/save'
          let res = await this.$axios.post(url, this.ruleForm)
          if (res.status === 0) {
            this.$message.success('操作成功')
            setTimeout(() => {
              this.visible = false
              this.$emit('refreshTagEditorList')
            }, 300)
          } else {
            this.$message.error(res.msg)
          }
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.articleDialog /deep/ .el-dialog__body {
  height: 600px !important;
}
.item-conent-edit {
  height: 400px;
}
.conent-edit {
  height: 250px;
}
</style>
