<template>
  <div>
    <el-dialog
    title="项目参与人员"
    class="mytable"
    :visible.sync="dialogVisible"
    fullscreen
    :before-close="handleClose">
      <el-table
        :data="proMemberList"
        style="width: 100%">
        <el-table-column
          prop="name"
          label="项目人员"
          align="center"
          >
        </el-table-column>
        <el-table-column
          prop="workHours"
          label="累计工时"
          align="center"
          >
        </el-table-column>
        <el-table-column
          prop="chargeTask"
          label="负责任务"
          align="center">
        </el-table-column>
      </el-table>
      <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                @updatePageNum="handleCurrentChange"></page>
    </el-dialog>
  </div>
</template>

<script>
import page from '@/components/common/page.vue';
import { queryProjectMembers } from '@/api/project/index'
export default {
    components:{
        page
    },
    props:{
        dialogVisible:{
            type:Boolean,
            default:false,
        },
        projectId:{
            type:String,
            default:''
        }
    },
    data(){
        return{
            proMemberList:[],
            pageBean:{
                id:'',
                isAll:1,
                pageNum:1,
                pageSize:10
            },
            total:0,
        }
    },
    created(){
        this.pageBean.id = this.projectId;
    },
    methods:{
        loadProjectMembers(){
            queryProjectMembers(this.pageBean).then((result) => {
                this.proMemberList = result.data.projectMemberVoList;
                this.total = result.page.total;
            }).catch((err) => {
                
            });
        },
        handleClose(){
            this.$emit('handleClose')
        },
        handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.loadProjectMembers();
        },
    }
}
</script>

<style>

</style>