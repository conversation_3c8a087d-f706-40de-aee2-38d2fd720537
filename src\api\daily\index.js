import service from "@/utils/request.js";

export function templatePermissionInfo(params) {
  return service.request({
    method: "get",
    url: "/crm/business/templatepermission/templatePermissionInfo",
    params
  });
}

export function queryDailyList(params) {
  return service.request({
    method: "get",
    url: "/crm/controller/dailyrecord/queryDailyList",
    params
  });
}

export function listDailyPaper(params) {
  return service.request({
    method: "get",
    url: "/crm/controller/dailyrecord/listDailyPaper",
    params
  });
}

export function saveTemplatePermission(data) {
  return service.request({
    method: "post",
    url: "/crm/business/templatepermission/saveTemplatePermission",
    data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    }
  });
}

export function saveDailyRecord(data) {
  return service.request({
    method: "post",
    url: "/crm/controller/dailyrecord/saveDailyRecord",
    data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    }
  });
}

export function isCheckPermission(params) {
  return service.request({
    method: "get",
    url: "/crm/business/templatepermission/isCheckPermission",
    params
  });
}

export function querPersonalGoalsVoInfo(params) {
  return service.request({
    method: "get",
    url: "/crm/controller/dailyrecord/querPersonalGoalsVoInfo",
    params
  });
}



export function queryDailyReview(params={}) {
  return service.request({
    method: "get",
    url: "/crm/controller/dailyrecord/queryDailyReview",
    params
  });
}


export function dailyDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/dailyrecord/delete',
    data,
  });
}


// 客户详情
export function customerInfo(id) {
  return service.request({
    url: `/crm/controller/dailyrecord/customerInfo/${id}`,
    method: 'get',
  })
}

export function readDailyRecordVo(data) {
  return service.request({
    method: "post",
    url: "/crm/controller/dailyrecord/readDailyRecordVo",
    data
  });
}

export function dailyRecordDownload(params){
  return service.request({
    method: "get",
    url: "/crm/controller/dailyrecord/dailyRecordDownload",
    params
  });
}