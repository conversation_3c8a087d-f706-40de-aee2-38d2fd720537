<template>
  <div>
      <div>
          <back>{{'预览'}}</back>
          <div class="rightdiv " >
            <div  class="tright">
             <div class="bbtn" id="planreview"> 
               <i class="el-icon-notebook-2" alt=""/>
               计划回顾</div>
               <div class="bbtn" id="summaryreview"  > 
               <i class="el-icon-notebook-2" alt=""/>
               总结回顾</div>
            </div>
         </div> 
      </div>
      <div class="mainbg">
          <div class="fleft">
            <div class="titlecss geren">{{temText[templateType]}}</div>
            <el-form label-width="100px" class="mt elform myform" :model="formLabel">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="跟进拜访：" >
                    <el-input placeholder="请输入拜访次数" class="definput" v-model="formLabel.newVisit" type="number"><template slot="append">次</template></el-input>
                  </el-form-item>
                  <el-form-item label="业绩：" >
                    <el-input placeholder="请输入金额" class="definput"  v-model="formLabel.newCustomers" type="number"> <template slot="append">万元</template></el-input>
                  </el-form-item>
                  <el-form-item label="合同新增：" >
                    <el-input placeholder="请输入新增合同个数" class="definput" v-model="formLabel.newContract" type="number"><template slot="append">个</template></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客户新增：" >
                    <el-input placeholder="请输入新增客户数量" class="definput"  v-model="formLabel.newCustomers" type="number"> <template slot="append">个</template></el-input>
                  </el-form-item>
                  <el-form-item label="回款：" >
                    <el-input placeholder="请输入金额" class="definput" v-model="formLabel.contractReturnAmount" type="number"><template slot="append">万元</template></el-input>
                  </el-form-item>
                  <el-form-item label="样书发放：" >
                    <el-input placeholder="请输入样书发放次数" class="definput" v-model="formLabel.contractAmount" type="number"><template slot="append">次</template></el-input>
                  </el-form-item>
              
                </el-col>
              </el-row>
            </el-form>
            <template v-if="templateType==1">
              <div class="titlecss">单位年度计划</div>
              <div class="clearfix mb10">
                <el-button  class="defaultbtn  asbu"  icon="el-icon-plus" type="primary" >选择单位</el-button>
              </div>
              <div class="tablebox">
                  <el-table class=" mytable" v-for="(item,index) in unitGoalList" :key="index"  :data="item" :span-method="objectSpanMethod" border >
                    <el-table-column prop="unitName" label="学校" width="200">
                      <template slot-scope="scope">
                        <div>{{ scope.row.unitName }} </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="unitgoalTypeName" label="目标维度" ></el-table-column>
                    <el-table-column prop="goalNum" label="目标">
                      <template v-slot="{ row }">
                          {{row.goalNum}}
                      </template>
                    </el-table-column>
                </el-table>
              </div>
            </template>
        <viewmodelcom></viewmodelcom>
      </div>
      </div>
  </div>
</template>

<script>
import back from '../../common/back.vue';
import  viewmodelcom from '../../zongjie/temmodel/viewmodelcom.vue'
  export default {
      components: {
        back,
        viewmodelcom
      },
      data() {
          return {
            temText:{
              1:'个人年度计划',
              2:'个人月度计划',
              3:'个人周计划',
            },
            formLabel: {
              newCustomers: null,
              newContract: null,
              newVisit: null,
              newOpportunity:null,
              contractAmount:null,
              contractReturnAmount:null,
              year:this.year,
              personalGoalsUnitList:[],
              personalGoalsDetailList:[],
            },
            formLabelAlign: {
              personalGoalsUnitList:[
                {
                  createBy:"1176711646287180320",
                  createTime: 1708670844000,
                  id: "1386914140391810841",
                  informationAmount: 1,
                  personalGoalsId: "1386914140391810834",
                  teachingMaterialAmount: 1,
                  totalContract: 1,
                  totalVisit: 1,
                  unitId: "1366872946815345826",
                  unitName: "苏州大学",
                  year: 2024,
                }
              ],
            },
            unitGoalList: [],
            keyNames:{
              informationAmount:"信息化业绩（万元）",
              teachingMaterialAmount:'教材业绩金额（万元）',
              totalContract:'合同数量',
              totalVisit:'拜访次数'
            },
            templateType:'',
          }
      },
      created() {
        this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList)
        this.templateType = this.$route.query.templateType
      },
      methods: {
        getUnitGoalList(array){
              this.unitGoalList = [];
              this.unitIds = [];
              var keys = ['informationAmount','teachingMaterialAmount','totalContract','totalVisit'];
              array.forEach((element,idx) => {
                  var list = [];
                  this.unitIds.push(element.unitId)
                  keys.forEach(key => {
                    var item = {
                      id:element.id,
                      index:idx,
                      unitId:element.unitId,
                      unitName:element.unitName,
                      year:element.year,
                      goalNum:element[key],
                      unitgoalType:key,
                      unitgoalTypeName:this.keyNames[key],
                      reachNum:1
                    };
                    list.push(item);
                  });
                this.unitGoalList.push(list);
              });
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
          if (columnIndex === 0 && rowIndex === 0) {
            return {
              rowspan: 4,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      },
      }
  }
</script>
<style scoped>
   .bbtn{
    height: 36px;
    line-height: 36px;
    background-color: #DFEAFD;
    font-size: 16px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    letter-spacing: 1px;
    border-radius: 4px;
    cursor: pointer;
    width: 120px;
    text-align: center;
  }
  
  .bbtn + .bbtn{
    margin-top:10px;
  }
  .geren{
    margin-bottom: 20px;
  }
  .mb10{
    margin: 10px 0;
  }
  .defaultbtn{
    display: block;
    float: right;
  }
  .save{
      display: block;
      margin: 0 auto;
  } 
    .rightdiv{
        border-radius: 10px;
        display: flex;
        justify-content: flex-end;
      }

  .tright{
      position: relative;
      width: 120px;
  }
  .fleft{
    background-color: white;
    padding: 20px 16px;
    border-radius: 8px;
  }
    .mainbg{
            height: 100%;
            overflow-y: auto;
            margin: 16px 0px;
    }
.mt{
  margin-top: 5px;
}
.titlecss{
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  line-height: 23px;
}

</style>
