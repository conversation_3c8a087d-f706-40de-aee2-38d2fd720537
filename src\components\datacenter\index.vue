<template>
  <div>
      <el-card v-loading="isLoading" class="mt10">
          <el-form label-width="85px" class="myform ">
              <el-row :gutter="10" type="flex" justify="start">
                  <el-col :span="5">
                      <el-form-item label="" label-width="0px">
                          <el-input class="definput " v-model="pageBean.materialName" clearable placeholder="请输入教材名称"></el-input>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item label="用书专业：">
                          <el-select class="definput" popper-class="removescrollbar" clearable v-model="pageBean.useBookSpecialty"
                              placeholder="请选择">
                              <el-option v-for="item in majorList" :key="item.id" :label="item.specialtyName" :value="item.id">
                              </el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="6">
                      <el-form-item  label="用书时间：" >
                          <el-input class="definput cpdding" v-model="pageBean.useBookYear" clearable placeholder="例:2023年春季"></el-input>
                      </el-form-item>
                  </el-col>
                  <el-col :span="5">
                      <el-form-item label="类型：">
                          <el-select class="definput cwidth" popper-class="removescrollbar" clearable v-model="pageBean.isJoin"
                              @change="changeOption" placeholder="请选择">
                              <el-option key="1" label="过往合作" value="1"> </el-option>
                              <el-option key="2" label="非合作" value="2"> </el-option>
                              <el-option key="3" label="历史发货" value="3"> </el-option>
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="8">
                  <el-button class="defaultbtn1 mt" icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
                  <el-button  class="defaultbtn1 mt fr" icon="el-icon-plus"
                          type="primary" @click="addInfo">添加</el-button>
                  </el-col>
              </el-row>
          </el-form>
          <el-table class="customertable mytable tootiptable" height="590px" :data="tableData" style="width: 100%">
              <el-table-column prop="materialName" label="教材名称" align="center" width="250px">
                  <template slot-scope="scope">
                      <span class="column_blue">{{ scope.row.materialName}}</span>
                  </template>
              </el-table-column>
              <el-table-column prop="isbn" label="ISBN" align="center" min-width="167px">
              </el-table-column>
              <el-table-column prop="platformName" label="出版社" align="center" min-width="167px">
              </el-table-column>
              <el-table-column prop="author" label="主编" align="center" >
              </el-table-column>
              <el-table-column prop="price" label="价格" align="center" >
              </el-table-column>
              <el-table-column prop="bookNumber" label="用书量" align="center" >
              </el-table-column>
              <el-table-column prop="useBookYear" label="用书时间" align="center" width="120px">
              </el-table-column>
              <el-table-column prop="bookSpecialtyName" align="center" label="用书专业" width="120px">
              </el-table-column>
              <el-table-column prop="isJoin" label="类型" align="center" >
              <template slot-scope="scope">
                  <span>{{typeObj[scope.row.isJoin]}}</span>
              </template>
              </el-table-column>
              <el-table-column prop="edit" width="200" align="center" fixed="right" label="更多操作">
                  <template slot-scope="scope">
                      <el-button class="bbtn" type="text"
                          @click="lookInfo(scope.row)"> 情况说明 </el-button> 
                      <el-button v-isShow="'crm:controller:customer:update'" class="bbtn" type="text"
                          @click="editInfo(scope.row)"> 更新</el-button>
                      <el-button v-isShow="'crm:controller:customer:delete'" class="rbtn" type="text"
                          @click="deleteAction(scope.row)"> 移除</el-button>
                  </template>
              </el-table-column>
              <template slot="empty">
                  <nolist></nolist>
              </template>
          </el-table>
          <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
              @updatePageNum="handleCurrentChange"></page>
          <el-dialog
          :title="dialogTitle"
          :visible.sync="dialogFormVisible"
          width="500px"
          :before-close="handleClose">
          <el-form :rules="rules" :model="bookInfoForm" ref="addform"  class="addform">
              <el-form-item  v-if="!bookInfoForm.id" label="选择教材：" prop="materialId" label-width="100px">
                  <div class="unitbtn" @click="chooseBook">
                      <span v-if="bookInfoForm.materialId" class="deffont">{{bookInfoForm.materialName}}</span>
                      <span v-else class="pltcss">请选择</span>
                      <i class=" rcenter el-icon-arrow-down"/>
                  </div>
              </el-form-item>
              <el-form-item  label="类型" prop="isJoin" label-width="100px">
                  <el-select class="wcss"  v-model="bookInfoForm.isJoin" placeholder="请选择">
                    <el-option v-for="item in isCoo" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              <el-form-item prop="bookNumber" label="用书量：" label-width="100px">
                  <el-input class="definput" v-model="bookInfoForm.bookNumber" placeholder="请输入教材用量（册）"></el-input>
              </el-form-item>
              <el-form-item prop="useBookYear" label="用书时间：" label-width="100px">
                  <el-date-picker
                  @change="changePicker"
                  v-model="useBookYear"
                  type="year"
                  format="yyyy"
                  value-format="yyyy年"
                  placeholder="选择年">
                  </el-date-picker>
                  <span class="pdl">
                      <el-radio class="mr10" v-model="radio" label="春季">春季</el-radio>
                      <el-radio class="mr10" v-model="radio" label="秋季">秋季</el-radio>
                  </span>
              </el-form-item>
              <el-form-item prop="useBookSpecialty" label="用书专业：" label-width="100px">
                  <el-select class="definput" v-model="bookInfoForm.useBookSpecialty"  popper-class="removescrollbar" placeholder="请选择用书专业">
                      <el-option v-for="item in majorList" :key="item.id" :label="item.specialtyName" :value="item.id"></el-option>
                  </el-select>
              </el-form-item>
       
              <el-form-item  prop="note" label="情况说明：" label-width="100px">
                  <el-input type="textarea" placeholder="请输入情况说明" v-model="bookInfoForm.note" :rows="5" maxlength="500" show-word-limit></el-input>
              </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitAction">确 定</el-button>
          </span>
      </el-dialog>
          <el-dialog
          title="查看情况说明"
          :visible.sync="dialogInfoVisible"
          width="500px">
          <span>
          {{notes}}
          </span>
      </el-dialog>
          <dc-dialog :iType="dialogType" title="温馨提示" width="500px" :showCancel="isShowCancel"
              :dialogVisible.sync="dialogVisible" @submit="submitDialog">
              <p class="pcc">{{deletemsg}}</p>
          </dc-dialog>
          <chooseBookDialog ref="book" :visible.sync="bookDialogVisible" @updateVisible="updateVisible"
              @updateData="updateData">
          </chooseBookDialog>
      </el-card>
  </div>
  
</template>

<script>
  import page from '../common/page.vue';
  import nolist from '../common/nolist.vue';
  import back from '../common/back.vue';
  import { selectSpecialty, customerbookList, customerbookSave,customerbookUpdate,customerbookInfo,customerbookDelete } from "@/api/clientmanagement/customer";
  import { getDict } from '@/utils/tools'
  import chooseBookDialog from "../common/chooseBookDialog.vue";
  export default {
      components: {
          back,
          page,
          nolist,
          chooseBookDialog
      },
      data() {
          return {
              typeObj:{
                  1:'过往合作',
                  3:'历史发货',
                  2:'非合作'
              },
              isCoo:[
                  {
                      id:1,
                      name:'过往合作'
                  },
                  {
                      id:2,
                      name:'非合作'
                  },
              ],
              customerId:this.$route.query.id,
              bookDialogVisible:false,
              dialogFormVisible:false,
              dialogInfoVisible:false,
              dialogTitle:'',
              dialogVisible: false,
              dialogType: '1',
              deleteCustomerId: "",
              isShowCancel: true,
              typename: "",
              notes:'',
              levels: [],
              majorList:[],
              isLoading: false,
              tableData: [],
              bookInfoForm:{
                  customerId:this.$route.query.id,
                  materialId:'',
                  bookNumber:'',
                  useBookYear:'',
                  useBookSpecialty:'',
                  note:'',
                  isJoin:undefined,
              },
              useBookYear:'',
              radio:'春季',
              total: 0,
              deletemsg: '',
              pageBean: {
                  materialName:'',
                  useBookYear:null,
                  userBookSpecialty:'',
                  isJoin:undefined,
                  pageNum: 1,
                  pageSize: 10,
              },
              rules: {
                  materialId: [
                      { required: true, message: '请选择教材', trigger: 'change' },
                  ],
                  isJoin: [
                      { required: true, message: '请选择类型', trigger: 'change' }
                  ],
                  bookNumber: [
                     { required: true, message: '请输入教材用量', trigger: 'blur' }
                  ],
                  userBookSpecialty: [
                      {  required: true, message: '请选择用书专业', trigger: 'change' }
                  ],
                  useBookYear: [
                      { required: true, message: '请选择用书时间', trigger: 'change' }
                  ],
              }
          }
      },
      created() {
          this.loadSepcialty();
          this.pageBean.customerId = this.$route.query.id
          this.loadData();
      },
      methods: {
          loadSepcialty(){
              selectSpecialty({
                  customerId:this.customerId,
              }).then((result) => {
                  this.majorList = result.data;
              }).catch((err) => {
                  
              });
          },
          changePicker(val){
              if(val){
                      this.bookInfoForm.useBookYear = val
              }else{
                  this.bookInfoForm.useBookYear = ''
              }
          },
          loadData() {
              this.isLoading = true;
              customerbookList(this.pageBean).then((result) => {
                  this.tableData = result.data ? result.data : [];
                  this.total = result.data ? result.page.total : 0
                  this.isLoading = false;
              }).catch((err) => {
                  this.isLoading = false;
              });
          },
          changeOption(data) {
              if (data == '1') {
                  this.pageBean.chargePerson = window.sessionStorage.getItem('userid');
                  this.pageBean.collaborator = '';
              } else if (data == '2') {
                  this.pageBean.chargePerson = '';
                  this.pageBean.collaborator = window.sessionStorage.getItem('userid');
              } else {
                  this.pageBean.chargePerson = '';
                  this.pageBean.collaborator = '';
              }
          },
          searchAction() {
              this.pageBean.pageNum = 1;
              this.loadData();
          },
          submitDialog() {
              this.dialogVisible = false;
              if (this.dialogType == 1) {
                  this.deleteData();
              }

          },
          deleteData() {
              customerbookDelete({ id: this.deleteCustomerId }).then((result) => {
                  if (result.data) {
                      this.loadData()
                      this.$message({
                          type:'success',
                          message:'移除成功'
                      })
                  } else {
                      this.dialogType = 2;
                      this.isShowCancel = false;
                      this.deletemsg = result.msg;
                      this.dialogVisible = true;
                  }
              }).catch((err) => {

              });
          },

          addInfo() {
              this.dialogFormVisible = true;
              this.dialogTitle = "新增用书信息";
          },
          editInfo(data){
            this.dialogFormVisible = true;
            this.dialogTitle = "编辑用书信息";
            this.loadInfoById(data.id)
          },
          loadInfoById(id){
              customerbookInfo(id).then((result) => {
                  this.bookInfoForm = result.data;
                  var useBookYear = this.bookInfoForm.useBookYear;
                  var index = useBookYear.indexOf('年');
                  this.useBookYear = useBookYear.substring(0,index+1)
                  this.radio = useBookYear.substring(index+1,useBookYear.length);
              }).catch((err) => {
                  
              });
          },
          deleteAction(data) {
              this.dialogType = 1;
              this.isShowCancel = true;
              this.dialogVisible = true;
              this.deletemsg = "是否删除该用书信息？";
              this.deleteCustomerId = data.id;
          },
          toDetail(data) {
              this.$router.push({
                  path: '/clientManagement/customer/detail',
                  query: { id: data.id }
              })
          },
          lookInfo(data){
              this.dialogInfoVisible = true;
              this.notes = data.note;
          },
          onEdit(data) {
              this.$router.push({
                  path: '/clientManagement/customer/add',
                  query: {
                      id: data.id,
                  }
              })
          },
          handleCurrentChange(page) {
              this.pageBean.pageNum = page;
              this.loadData();
          },
          handleClose(){
            this.dialogFormVisible = false;
            this.dialogTitle = ''
            Object.keys(this.bookInfoForm).forEach(element => {
              if (element != 'customerId') {
                  this.bookInfoForm[element] = ''
              }
            });
            this.useBookYear = '';
            this.radio = '春季';
            this.$nextTick(()=>{
              this.$refs.addform.clearValidate();
            })
          },
          submitAction(){
              if (this.useBookYear&&this.radio) {
                  this.bookInfoForm.useBookYear = `${this.useBookYear}${this.radio}`
              }
              this.$refs['addform'].validate((valid) => {
                  if (valid) {
                      if (this.bookInfoForm.id) {
                          this.update()
                      }else{
                          this.add()
                      }
                  } else {
                      console.log('error submit!!');
                      return false;
                  }
                  });
          },
          add(){
              customerbookSave(this.bookInfoForm).then((result) => {
                  if (result.data) {
                      this.$message({
                          type:"success",
                          message:'添加成功'
                      })
                      this.loadData();
                      this.handleClose();
                  } else {
                      this.$message({
                          type:"error",
                          message:result.msg
                      })
                  }
              }).catch((err) => {
                  
              });
          },
          update(){
              customerbookUpdate(this.bookInfoForm).then((result) => {
                  if (result.data) {
                      this.$message({
                          type:"success",
                          message:'更新成功'
                      })
                      this.loadData();
                      this.handleClose();
                  } else {
                      this.$message({
                          type:"error",
                          message:result.msg
                      })
                  }
              }).catch((err) => {
                  
              });
          },
          chooseBook(){
              this.bookDialogVisible = true;
              this.$nextTick(()=>{
                  this.$refs.book.loadBookData({
                      customerId:this.customerId,
                      type:1,
                  });
              })
              
          },
          updateVisible(val){
              this.bookDialogVisible = val;
          },
          updateData(data){
              this.bookInfoForm.materialId = data.id,
              this.bookInfoForm.materialName = data.name;
              this.$refs.addform.validateField('materialId');
          },
      }
  }
</script>
<style>
  .tootiptable .el-tooltip {
      text-align: left;
  }
  .cpdding .el-input__inner{
          padding-right: 0;
  }       
</style>

<style scoped>
.mr10{
  margin-right: 10px !important;
}
.pdl{
  padding-left: 10px;
}
.defaultbtn1 {
  padding: 9px 10px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
  .rcenter{
      position: absolute;
      right: 10px;
      line-height: 34px;
      font-size: 14px;
      color: #c0c4cc;
  }
  .namecss {
      display: flex;
  }

  .pcc {
      margin: 0 auto;
      text-align: center;
  }

  .smtext {
      zoom: 0.8;
  }

  .fxcenter {
      min-width: 30px !important;
      height: 52px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
  }

  .zhiding {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      margin-top: -4px;
      vertical-align: middle;
      position: relative;
      top: 21px;
  }

  .mt {
      margin-top: 4px;
  }

  .cusnamecss {
      display: flex;
  }

  .tagcss {
      font-size: .625em !important;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      height: 14px;
      min-width: 30px;
      line-height: 11px;
      border-radius: 2px;
  }

  .column_blue {
      color: #4285F4;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
  }

  .genjin {
      color: #4285F4;
      background-color: #DFEAFD;
  }

  .fuze {
      color: #FEF2E7;
      background-color: #FF8D1A;
  }

  .xiezuo {
      color: #56C36E;
      background-color: #F3FEF6;
  }

  .tagcss:nth-child(2n+2) {
      margin-top: 4px;
  }

  .tagcss:nth-child(3n) {
      margin-top: 4px;
  }


  .customertable .el-button {
      padding: 2px;
  }

  .mr10 {
      margin-right: 10px;
  }

  .bbtn,
  .bbtn:hover,
  .bbtn:focus {
      color: #4285F4;
  }

  .rbtn,
  .rbtn:hover,
  .rbtn:focus {
      color: #F45961;
  }

  .right {
      text-align: right;
  }
</style>