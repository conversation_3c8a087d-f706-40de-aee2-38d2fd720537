<template>
  <div class="box" ref="scrollContainer" @scroll="handleScroll">
    <div class="boxItem" v-for="(item,index) in boxList" :key="index">
      <div class="title">{{ getGoalsTypeTitle(item.goalsType) }}-{{ getPlanTime(item) }}</div>
      <div class="container">
        <el-row>
          <el-col :span="12">
            <div class="content">提交情况：{{ getTimeoutStatus(item.timeout) }}</div>
          </el-col>
          <el-col :span="12">
            <div class="content">提交人：{{ item.createByName }}</div>
          </el-col>
        </el-row>
      </div>
      <div class="justBetween">
        <div class="time">提醒时间：{{ item.auditTime }}</div>
        <div class="btn" @click="goDetail(item)">
          <div class="btnText">详情</div>
          <img src="@/assets/right.png" alt="" class="btnImg">
        </div>
      </div>
    </div>
    <div v-if="loading" class="loading-text">加载中...</div>
    <div v-if="noMore && boxList.length > 0" class="no-more-text">没有更多数据了</div>
    <el-empty v-if="boxList.length == 0 && !loading" description="暂无计划待办"></el-empty>
  </div>
</template>

<script>
import { planList } from '@/api/index'
import { getPlanTime, getGoalsTypeTitle } from '@/utils/index'
import {myMinxin} from '../common/handleScroll.js'
export default {
  mixins:[myMinxin],
  data() {
    return {
      pageBean:{
        pageNum: 1,
        pageSize: 10,
        kind:1,
        type:1
      },
      boxList:[],
      getGoalsTypeTitle,
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    getList(isLoadMore = false){
      planList(this.pageBean).then(res=>{
        if (isLoadMore) {
          this.boxList = [...this.boxList, ...res.data]
        } else {
          this.boxList = res.data
        }

        this.noMore = !res.page.hasNextPage
      }).finally(() => {
        this.loading = false
        this.isScrollLoading = false
      })
    },

    getTimeoutStatus(timeout) {
      if (timeout === 1) {
        return '未超时'
      } else if (timeout === 2) {
        return '已超时'
      }
      return '未知'
    },
    getPlanTime(data) {
      return getPlanTime(data, data.goalsType)
    },
    goDetail(item){
      this.$router.push({
        path: '/reviewCenter/plan/index',
        query: {
          goalType: item.goalsType
         }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.boxItem{
  height: 137px;
  padding: 12px 20px;
  background: #F6F7FB;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 16px;
}
.boxItem:last-child{
  margin-bottom: 0;
}
.title{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #333333;
}
.container{
  border-top: 1px solid #E6E9F5;
  border-bottom: 1px solid #E6E9F5;
  padding: 12px 0;
  margin-top: 12px;
  margin-bottom: 12px;
}
.content{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}
.content:last-child{
  margin-bottom: 0;
}
.justBetween{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.btn{
  display: flex;
  align-items: center;
  cursor: pointer;
}
.btnText{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #4285F4;
  margin-right: 2px;
}
.btnImg{
  width: 16px;
  height: 17px;
}
.time{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}

.box {
  max-height: 100%;
  overflow-y: auto;
}

.loading-text, .no-more-text {
  text-align: center;
  padding: 20px;
  font-size: 14px;
  color: #999;
}
</style>
