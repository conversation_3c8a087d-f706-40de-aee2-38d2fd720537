<template>
 <p class="ptext"><slot></slot></p>
</template>

<script>
  export default {
    
  }
</script>

<style lang="scss" scoped>
.ptext{
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  position: relative;
  padding-left: 8px;
}
.ptext::before{
      position: absolute;
      top: 0.3em;
      bottom: 0;
      left: 0;
      content: '';
      width: 3px;
      height: calc(100% - 0.5em);
      background: #4285F4;
      border-radius: 7px 7px 7px 7px;
}

</style>