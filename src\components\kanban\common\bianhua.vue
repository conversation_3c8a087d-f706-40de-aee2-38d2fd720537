<template>
    <div>
        <el-card class="mokuai">
            <div class="title">
                <textBorder class="biaoti">客户变化</textBorder>
                <div class="danwei">单位：人</div>
            </div>
            <div class="tubiao" ref="chartContainer"></div>
        </el-card>
    </div>
</template>

<script>
import '../detail.css';
import * as echarts from 'echarts';
import textBorder from '@/components/common/textBorder.vue';
export default {
    components:{
        textBorder
    },
    data() {
        return {

            

        }
    },
    mounted() {
        
    },
    methods: {
        initChart(data) {
            this.chart = echarts.init(this.$refs.chartContainer);
            const option = {
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            result += item.marker + '客户总数 : ' + '<span style="font-weight: bold; margin-left: 20px;">' + item.value + '</span><span style="color: #999999; ">人</span><br/>';
                        });
                        return result;
                    }
                },
                xAxis: {
                    type: 'category',
                    axisTick: { // 坐标轴刻度
                    show: false
                    },
                    splitLine: { // 坐标轴在 grid 区域中的分隔线。
                    show: false
                    },

                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月','9月','10月','11月','12月'],
                    axisPointer: {
                        type: 'shadow', // 使用阴影效果
                        shadowStyle: {
                            color: 'rgba(237, 238, 252,0.3)', // 阴影颜色，这里使用半透明的灰色
                            width: 'auto' // 自动匹配数据点的宽度
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    splitLine: {  // 坐标轴在 grid 区域中的分隔线
                    lineStyle: { // 分隔线
                        type: 'dashed', // 线的类型
                        color: '#e8e8e8' // 分隔线颜色
                    }
                    },

                },
                series: [{
                    data: data,
                    type: 'line',
                    smooth: true,
                    color:'#fa8544', 
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1, // 从上到下的渐变
                            [
                                {offset: 0, color: '#FA8544'}, // 0% 处的颜色
                                {offset: 1, color: 'rgba(255,255,255,0)'} // 100% 处的颜色
                            ]
                        )
                    },
                    // emphasis: { 
                    //     areaStyle: {
                    //         color: new echarts.graphic.LinearGradient(
                    //         1, 0, 0, 0, // 从上到下的渐变
                    //         [
                    //             {offset: 0, color: '#FA8544'}, // 0% 处的颜色
                    //             {offset: 1, color: 'rgba(255,255,255,0)'} // 100% 处的颜色
                    //         ]
                    //     )
                    //     }
                    // },
                    symbol: 'none' // 取消每个数据顶点的小圆
                }]
            };
            this.chart.setOption(option);
            
            window.addEventListener('resize',function(){
                this.chart.resize();
            }.bind(this)) 
        }
    }
}
</script>
<style lang="scss" scoped>

.mokuai {
    height: 469px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    margin-bottom: 12px;
}
.title {
    display: flex; 
    justify-content: space-between; 
    align-items: flex-start; 
    margin-bottom: 20px;
}
.left-container {
    display: flex;
}
.danwei {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #333333;
}
.tubiao {
    width: 100%;
    height: 400px;
}
.shugang1 {
    width: 3px;
    height: 16px;
    background: #4285F4;
    border-radius: 7px 7px 7px 7px;
    margin-right: 8px;
    margin-top: 5px;
}
.biaoti{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold !important;
    font-size: 20px !important;
    color: #333333 !important;
}
.shugang {
    width: 3px;
    height: 12px;
    background: #4285F4;
    border-radius: 7px 7px 7px 7px;
}
.niandu {
    height: 299px;
    border-radius: 8px;
    margin-bottom: 12px;
}
.user {
    height: 217px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
    text-align: center;
}
.name{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    margin-top: 12px;
}
.bumen {
    max-height: 56px;
    background: #F6F7FB;
    border-radius: 4px 4px 4px 4px;
    margin: 16px 16px 24px 17px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #4285F4;
    padding: 10px 12px 10px 12px;
}

.kuai {
    height: 217px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
    padding: 16px;
}
.toptitle {
    display: flex;
}
.toptop {
    justify-content: space-between; 
}
.shugang {
    width: 3px;
    height: 12px;
    background: #4285F4;
    border-radius: 7px 7px 7px 7px;
    margin-right: 8px;
    margin-top: 4px;
}
.wenzi {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    flex-grow: 1;
}
.icon {
    text-align: center;
    margin-top: 32px;
}
.number{
    color: #999999;
    margin-top: 16px;
    text-align: center;
}

.qiandan{
    color: #999999;
    margin-top: 10px;
}
.number-style {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
}
.number-style1 {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
    padding-top: 8px;
}
.dan {
    display: flex;
    justify-content: center;
    align-items: center;
}
.nowyear {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 21px;
}
.nowyear1 {
    font-family: Roboto, Roboto;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
}
.number-style2{
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
    margin-right: 2px;
}
.lastyear {
    margin: 0;
}
.zeng {
    display: flex;
    justify-content: center;
    margin-top: 12px;
}
@media (max-width: 1384px) {
    .lastyear,.wenzi,.bumen,.nowyear1 {
        font-size: 12px !important; /* 减小字体大小 */
    }
    .baifenbi {
        font-size: 22px;
    }
    .number-style1 {
        font-size: 14px;
    }
    .number-style,.number-style2{
        font-size: 14px;
    }

}
@media (max-width: 1585px) {
    .lastyear,.wenzi,.bumen,.nowyear1 {
        font-size: 14px !important; /* 减小字体大小 */
    }
    .number-style,.number-style2{
        font-size: 14px;
    }

}
@media (max-width: 1466px) {
    .lastyear,.wenzi,.bumen,.nowyear1 {
        font-size: 13px !important; /* 减小字体大小 */
    }
    .number-style,.number-style2{
        font-size: 13px;
    }

}
@media (max-width: 1410px) {
    .lastyear,.wenzi,.bumen,.nowyear1 {
        font-size: 12px !important; /* 减小字体大小 */
    }
    .number-style,.number-style2{
        font-size: 12px;
    }

}
@media (max-width: 1350px) {
    .lastyear,.wenzi,.bumen,.nowyear1 {
        font-size: 11px !important; /* 减小字体大小 */
    }
    .number-style,.number-style2{
        font-size: 11px;
    }

}

.baifenbi {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 24px;
}
</style>