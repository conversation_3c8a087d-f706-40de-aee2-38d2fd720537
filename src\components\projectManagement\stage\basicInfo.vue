<template>
  <div>
    <el-form class="infoform" ref="form" :model="form" label-width="140px">
           <div class="bp">
            <el-button :disabled="!form.isOperate" @click="deleteHandle" class="defaultbtn" icon="el-icon-delete" type="danger">删除</el-button>
          </div>
          <textBorder>基本信息</textBorder>
          <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
            <el-col :span="12">
              <el-form-item label="阶段名称：" class="labeltext">
                 <span>{{ form.name }}</span>
              </el-form-item>
            </el-col>
          </el-row>



            <textBorder>负责与协作</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                <el-form-item label="负责人：" class="labeltext">
                    <span>{{form.chargePersonName}}</span>
                </el-form-item>
                </el-col>
                <el-col :span="12">  
                <el-form-item label="协作人：" class="labeltext">
                    <span>{{form.collaboratorName}}</span>
                </el-form-item>
                </el-col>
            </el-row>

            
          <textBorder>特殊</textBorder>
          <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
            <el-col :span="24">
              <el-form-item label="是否为回款阶段：" class="labeltext">
                <span>{{ typeToName[form.stageType] }}</span>
              </el-form-item>
            </el-col>
          </el-row>

            <textBorder>系统信息</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                <el-form-item label="创建人：" class="labeltext">
                    <span>{{ form.createByName }}</span>
                </el-form-item>
                <el-form-item label="创建时间：" class="labeltext">
                    <span>{{ form.createTime }}</span>
                </el-form-item>
                </el-col>
                <el-col :span="12">  
                <el-form-item label="最后修改人：" class="labeltext">
                    <span>{{ form.modifyByName }}</span>
                </el-form-item>
                <el-form-item label="最后修改时间：" class="labeltext">
                    <span>{{ form.modifyTime }}</span>
                </el-form-item>
                </el-col>
            </el-row>
    </el-form>
  </div>
</template>

<script>
  import textBorder from '@/components/common/textBorder.vue'
  import {stageDelete} from '@/api/stage/index'
  export default {
    props:{
      form:{
        type:Object,
        default:()=>({})
      }
    },
    data(){
     return {
      typeToName:{
        1:'一般阶段',
        2:'回款阶段'
      }
     }
    },
    methods:{
      deleteHandle(){
        stageDelete({id:this.form.id}).then(res=>{
            if(res.status == 0){
                this.msgSuccess('删除成功')
                this.$emit('changeDrawer',false)
                this.$emit('loadData')
            }
        })
      }
    },
    components:{
      textBorder,
    }
  }
</script>

<style lang="scss" scoped>

.pb12{
    padding-bottom: 12px;
}

</style>
<style scoped>
.bp{
  position: absolute;
  right: 0;
  top: 0;
  z-index:10;
}
.pdfdiv{
  cursor: pointer;
  display: inline-block;
  height: 24px;
  border-radius: 4px 4px 4px 4px;
  border: 1px dashed #4285F4;
  line-height: 4px;
  padding: 10px;
  box-sizing: border-box;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
}
.lh /deep/.el-form-item__content{
    line-height: 18px !important;
    padding: 0;
    padding-top: 12px;
}
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}

</style>