import service from "@/utils/request.js";





export function queryUnitBookStatistics(data) {
  return service.request({
    method: "post",
    url: "/crm/business/customerbook/queryUnitBookStatistics",
    data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    }
  });
}





export function queryUnitBookStatisticsDetail(data) {
  return service.request({
    method: "post",
    url: "/crm/business/customerbook/queryUnitBookStatisticsDetail",
    data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    }
  });
}


export function queryLastFiveYearslist(data) {
  return service.request({
    method: "post",
    url: "/crm/business/recruitstudentinfo/queryLastFiveYearslist",
    data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    }
  });
}





export function queryMaterialStatisticsDetail(data) {
  return service.request({
    method: "post",
    url: "/crm/business/customerbook/queryMaterialStatistics",
    data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    }
  });
}





export function queryUnitStatistics(data) {
  return service.request({
    method: "post",
    url: "/crm/business/customerbook/queryUnitStatistics",
    data,
    headers: {
      "Content-Type": "application/json;charset=UTF-8"
    }
  });
}



export function isCheckPermission(params) {
  return service.request({
    method: "get",
    url: "/crm/business/templatepermission/isCheckPermission",
    params
  });
}








export function dailyDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/dailyrecord/delete',
    data,
  });
}


// 客户详情
export function customerInfo(id) {
  return service.request({
    url: `/crm/controller/dailyrecord/customerInfo/${id}`,
    method: 'get',
  })
}



