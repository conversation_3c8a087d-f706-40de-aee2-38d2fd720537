
export const getTimeStr = (time) =>{
    var date = new Date(time);
    var Y = date.getFullYear() + '-';
    var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
    var D = date.getDate() + '';

    Y = Y.length===2 ? '0' + Y : Y
    M = M.length===2 ? '0' + M : M
    D = D.length===1 ? '0' + D : D

    return Y + M + D + ' '
}
// 获取审核状态
export const getAuditStatus = (status) =>{
    var str = "";
    switch (status) {
        case 0:
             str = '待审核'
            break;
        case 1:
            str = '审核通过'
            break;
        case 2:
            str = '审核未通过'
            break;   
        default:
            break;
    }
    return str
}