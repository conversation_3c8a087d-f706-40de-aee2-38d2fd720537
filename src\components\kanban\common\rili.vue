<template>
    <div>
        <el-card class="mokuai">
            <div class="title">
                <div class="left-container"> 
                    <textBorder class="biaoti">业绩日历</textBorder>
                </div>
                <div class="danwei">单位：万元</div>
            </div>
            <div class="tubiao" ref="chartContainer"></div>
        </el-card>
    </div>
</template>

<script>
import '../detail.css';
import * as echarts from 'echarts';
import textBorder from '@/components/common/textBorder.vue';
export default {
    components:{
        textBorder
    },
    data() {
        return {

            

        }
    },
    mounted() {

    },
    methods: {
        initChart(data) {
            this.chart = echarts.init(this.$refs.chartContainer);
            const option = {
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            result += item.marker + '业绩 : ' + '<span style="font-weight: bold; margin-left: 20px;">' + item.value + '</span><span style="color: #999999; ">万元</span><br/>';
                        });
                        return result;
                    }
                },
                xAxis: {
                    type: 'category',
                    axisTick: { // 坐标轴刻度
                    show: false
                    },
                    splitLine: { // 坐标轴在 grid 区域中的分隔线。
                    show: false
                    },

                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月','9月','10月','11月','12月'],
                    axisPointer: {
                        type: 'shadow', // 使用阴影效果
                        shadowStyle: {
                            color: 'rgba(237, 238, 252,0.3)', // 阴影颜色，这里使用半透明的灰色
                            width: 'auto' // 自动匹配数据点的宽度
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    splitLine: {  // 坐标轴在 grid 区域中的分隔线
                    lineStyle: { // 分隔线
                        type: 'dashed', // 线的类型
                        color: '#e8e8e8' // 分隔线颜色
                    }
                    },

                },
                series: [{
                    data: data,
                    type: 'line',
                    color:'#5e97f5',
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1, // 从上到下的渐变
                            [
                                {offset: 0, color: '#4285F4'}, // 0% 处的颜色
                                {offset: 1, color: 'rgba(255,255,255,0)'} // 100% 处的颜色
                            ]
                        )
                    },
                    // emphasis: { 
                    //     areaStyle: {
                    //         color: new echarts.graphic.LinearGradient(
                    //         1, 0, 0, 0, // 从上到下的渐变
                    //         [
                    //             {offset: 0, color: '#4285F4'}, // 0% 处的颜色
                    //             {offset: 1, color: 'rgba(255,255,255,0)'} // 100% 处的颜色
                    //         ]
                    //     )
                    //     }
                    // },
                }]
            };
            this.chart.setOption(option);
            
            window.addEventListener('resize',function(){
                this.chart.resize();
            }.bind(this)) 
        }
    }
}
</script>
<style lang="scss" scoped>

.mokuai {
    height: 462px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
}
.title {
    display: flex; 
    justify-content: space-between; 
    align-items: flex-start; 
}
.left-container {
    display: flex;
}
.danwei {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #333333;
}
.tubiao {
    width: 100%;
    height: 400px;
}
</style>