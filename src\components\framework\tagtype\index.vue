<!-- 标签管理 -->
<template>
  <div class="mod-config app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="grid-content bg-purple">
          <el-form :inline="true">
            <el-form-item>
              <el-input
                placeholder="输入标签类型过滤"
                v-model="filterText"
                prefix-icon="fa fa-search"
                style="width: 70%"
              ></el-input>
              <el-tooltip
                class="item"
                effect="dark"
                :content="'添加一级标签类型'"
                placement="right"
              >
                <el-button
                  type="primary"
                  v-isShow="'sf:business:tagtype:save'"
                  @click="addOrUpdateTagTypeHandle()"
                  ><i class="fa fa-plus"></i> 添加</el-button
                >
              </el-tooltip>
            </el-form-item>
          </el-form>
          <el-tree
            class="filter-tree"
            :data="tagTypeData"
            :props="defaultProps"
            ref="tagTypeTree"
            accordion
            :highlight-current="true"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :render-content="renderContent"
            @node-click="getTreeNode"
          >
          </el-tree>
        </div>
      </el-col>
      <!-- <el-col :span="18">
        <div
          class="grid-content bg-purple grid-contenttable"
          ref="configtypetable"
        >
          <el-table
            :data="dataList"
            border
            stripe
            v-loading="dataListLoading"
            style="width: 100%"
            element-loading-text="加载中..."
            element-loading-spinner="el-icon-loading"
          >
            <el-table-column
              prop="id"
              header-align="center"
              align="center"
              label="标签项ID"
            >
            </el-table-column>
            <el-table-column
              prop="name"
              header-align="center"
              align="center"
              label="标签项名称"
            >
            </el-table-column>
            <el-table-column
              prop="code"
              header-align="center"
              align="center"
              label="标签项编码"
            >
            </el-table-column>
            <el-table-column
              prop="tagtypeName"
              header-align="center"
              align="center"
              label="标签类型"
            >
            </el-table-column>
            <el-table-column
              prop="tagtypeCode"
              header-align="center"
              align="center"
              label="类型编码"
            >
            </el-table-column>
            <el-table-column
              prop="sortNo"
              header-align="center"
              align="center"
              label="排序号"
            >
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              width="200"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  v-isShow="'sf:business:tagtype:update'"
                  type="text"
                  icon="el-icon-edit"
                  @click="
                    addOrUpdateTagItemHandle(
                      scope.row.id,
                      scope.row.tagtypeId,
                      scope.row.tagtypeCode,
                      scope.row.tagtypeName
                    )
                  "
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  v-isShow="'sf:business:tagtype:delete'"
                  icon="el-icon-delete"
                  type="text"
                  @click="
                    deleteTagItem(scope.row.id, scope.row.name, scope.row)
                  "
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col> -->
    </el-row>
    <!-- 弹窗, 新增 / 修改 -->
    <AddOrUpdateTagType
      v-if="addOrUpdateTagTypeVisible"
      ref="addOrUpdateTagType"
      @refreshTagTypeTree="getTagTypeData"
    ></AddOrUpdateTagType>
    <AddOrUpdateTagItem
      v-if="addOrUpdateTagItemVisible"
      ref="addOrUpdateTagItem"
      @refreshTagItemData="getTagItemData"
    ></AddOrUpdateTagItem>
  </div>
</template>

<script>
import AddOrUpdateTagType from './add-or-update-tag-type'
import AddOrUpdateTagItem from './add-or-update-tag-item'
export default {
  data() {
    return {
      dataForm: {
        tagTypeCode: '',
      },
      tagTypeData: [],
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateTagTypeVisible: false,
      addOrUpdateTagItemVisible: false,
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tagTypeTree.filter(val)
    },
  },
  components: {
    AddOrUpdateTagType,
    AddOrUpdateTagItem,
  },
  methods: {
    // 标签类型的方法
    // 获取标签类型数据
    async getTagTypeData() {
      let res = await this.$axios.get(
        '/sf/business/dicttype/dictTypeTree?parentId=0'
      )
      if (res.status === 0) {
        this.tagTypeData = res.data
      }
    },
    // 获取点击的树节点
    getTreeNode(data) {
      this.dataForm.tagTypeCode = data.code
      // this.getTagItemData()
    },
    // 新增 / 修改
    addOrUpdateTagTypeHandle(id, parentId, parentName) {
      this.addOrUpdateTagTypeVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdateTagType.init(id, parentId, parentName)
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 删除
    deleteTagType(node, data) {
      let id = data.id
      this.$confirm(`确定要对[${data.name}]进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        let res = await this.$axios.post('/sf/business/tagtype/delete', {
          ids: id,
          tagtypeCode: data.code,
        })
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getTagTypeData()
            },
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    renderContent(h, { node, data, store }) {
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
          <span>
            <el-popover placement="bottom" width="60" trigger="click">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() =>
                    this.addOrUpdateTagItemHandle(
                      '',
                      data.id,
                      data.code,
                      data.name
                    )
                  }
                >
                  添加标签项
                </el-button>
              </div>
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() =>
                    this.addOrUpdateTagTypeHandle(
                      data.id,
                      data.parentId,
                      node.parent.data.name
                    )
                  }
                >
                  编辑
                </el-button>
              </div>
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() => this.deleteTagType(node, data)}
                >
                  删除
                </el-button>
              </div>
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() =>
                    this.addOrUpdateTagTypeHandle('', data.id, data.name)
                  }
                >
                  添加下级
                </el-button>
              </div>
              <el-button
                slot="reference"
                style="float:right"
                type="text"
                size="mini"
              >
                <div class="tree-icon">
                  <i class="fa fa-cog"></i>
                </div>
              </el-button>
            </el-popover>
          </span>
        </span>
      )
    },
    // 标签类型项的方法
    // 获取数据列表
    async getTagItemData() {
      this.dataListLoading = true
      let data = {
        tagtypeCode: this.dataForm.tagTypeCode,
      }
      let res = await this.$axios.get('/sf/business/dicttype/listAll', {
        params: data,
      })
      if (res.status === 0) {
        this.dataList = res.data
        this.dataListLoading = false
      } else {
        this.$message.error(`${res.msg}`)
      }
    },
    // 新增 / 修改
    addOrUpdateTagItemHandle(id, tagTypeId, tagTypeCode, tagTypeName) {
      this.addOrUpdateTagItemVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdateTagItem.init(
          id,
          tagTypeId,
          tagTypeCode,
          tagTypeName
        )
      })
    },
    // 删除
    deleteTagItem(id, name, obj) {
      let ids = id
        ? [id]
        : this.dataListSelections.map((item) => {
            return item.id
          })
      var idNames = name
        ? [name]
        : this.dataListSelections.map((item) => {
            return item.name
          })
      let dataid = ids.join(',')
      this.$confirm(
        `确定对[${idNames.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        let res = await this.$axios.post('/sf/business/tagitem/delete', {
          ids: dataid,
          tagtypeCode: obj.tagtypeCode,
        })
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getTagItemData()
            },
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  },

  mounted() {
    this.$refs.tagTypeTree.$el.style.height =
      Number(window.innerHeight) - 300 + 'px'
    // this.$refs.configtypetable.style.height =
    //   Number(window.innerHeight) - 200 + 'px'
  },
  created() {
    this.getTagTypeData()
  },
}
</script>

<style scoped lang="scss">
.filter-tree {
  font-size: 14px;
}
.filter-tree,
.grid-contenttable {
  overflow-y: scroll;
}
</style>
