import request from '@/utils/request'

// 查询部门列表
export function listDept(params) {
  return request({
    url: '/crm/controller/companydepartment/tree',
    method: 'get',
    params
  })
}
// 根据部门id查询部门下员工列表
export function listSatff(params) {
  return request({
    url: '/crm/controller/companydepartment/queryDepartmentStaff',
    method: 'get',
    params
  })
}

// 查询部门详细
export function getDept(id) {
  return request({
    url: '/sf/business/companydepartment/info/' + id,
    method: 'get'
  })
}

// 查询部门下拉树结构
export function treeselect() {
  return request({
    url: '/system/dept/treeselect',
    method: 'get'
  })
}

// 根据角色ID查询部门树结构
export function roleDeptTreeselect(roleId) {
  return request({
    url: '/system/dept/roleDeptTreeselect/' + roleId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/sf/business/companydepartment/save',
    method: 'post',
    data,
    type: 'json'
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/sf/business/companydepartment/update',
    method: 'POST',
    data,
    type: 'json'
  })
}

// 删除部门
export function delDept(id) {
  return request({
    url: '/sf/business/companydepartment/delete/' + id,
    method: 'POST'
  })
}