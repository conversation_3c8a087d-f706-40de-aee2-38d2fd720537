<template>
  <div class="bg">
    <div class="header">
      <el-form inline class="myform">
            <el-form-item>
              <el-input class="definput" v-model="pageBean.caseName" clearable placeholder="请输入案例名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input class="definput" v-model="pageBean.createByName" clearable placeholder="请输入分享人"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input class="definput" v-model="pageBean.departmentName" clearable placeholder="请输入分享人部门"></el-input>
            </el-form-item>
            <el-form-item>
              <el-input class="definput" v-model="pageBean.unitName" clearable placeholder="请输入单位"></el-input>
            </el-form-item>
            <el-form-item label="案例类型：">
              <el-select class="definput" popper-class="removescrollbar" v-model="pageBean.caseClassify" clearable
                placeholder="请选择案例类型">
                <el-option key="" label="全部" value=""></el-option>
                <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="分享类型：">
              <el-select class="definput" popper-class="removescrollbar" v-model="pageBean.caseType" clearable
                placeholder="请选择分享类型">
                <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="创建时间：">
              <datepicker @submitAction="submitAction"></datepicker>
            </el-form-item> -->
            <el-form-item>
              <el-button class="defaultbtn " icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
            </el-form-item>
      </el-form>
    </div>
    <el-table
        class="mytable"
        :data="dataList"
        style="width: 100%">
        <el-table-column
          prop="caseName"
          label="案例"
          min-width="200">
        </el-table-column>
        <el-table-column
          prop="createByName"
          label="分享人"
          align="center"
          width="167">
        </el-table-column>
        <el-table-column
          prop="departmentName"
          label="分享人部门"
          align="center"
          min-width="167">
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="上传时间"
          align="center"
          width="167">
        </el-table-column>
        <el-table-column
          prop="unitName"
          label="关联单位"
          align="center"
          min-width="167">
          <template slot-scope="scope">
            {{
              scope.row.unitName || '--'
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="caseClassifyName"
          label="案例类型"
          align="center"
          width="167">
        </el-table-column>
        <el-table-column
          prop="caseType"
          label="分享类型"
          align="center"
          width="167">
          <template slot-scope="scope">{{caseMap[scope.row.caseType]}}</template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="状态"
          width="100"
          align="center"
          fixed="right">
          <template slot-scope="scope">
            <span class="a_color" v-if="scope.row.status == 1">上架</span>
            <span class="c_color" v-else>下架</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="edit"
          label="操作"
          align="center"
          fixed="right"
          width="167">
          <template slot-scope="scope">
            <el-button
            class="bbtn"
            type="text"
            v-isShow="'crm:controller:case:info'"
            @click="onDetail(scope.row)">
            详情</el-button>
          <el-button
          class="bbtn"
          type="text"
          v-isShow="'crm:controller:case:update'"
          @click="onChangeStatus(scope.row)">
            {{ scope.row.status == 1 ?'下架':'上架' }}
          </el-button>
          <el-button
          class="rbtn"
          type="text"
          v-isShow="'crm:controller:case:delete'"
          @click="onDelete(scope.row)">
            删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    <page
      :currentPage="pageBean.pageNum"
      :pageSize="pageBean.pageSize"
      :total="total"
      @updatePageNum="handleCurrentChange">
    </page>
    <dc-dialog iType="1" title="温馨提示" width="500px" :showCancel="true" :dialogVisible.sync="deleteVisible"
      @submit="submitDialog">
      <template>
      </template>
      <p class="pcc">是否删除该案例？删除后该案例的相关评论信息也将删除。</p>
    </dc-dialog>
  </div>
</template>

<script>
  import page from '@/components/common/page.vue'
  import headuser from "@/components/common/headuser.vue";
  import datepicker from '@/components/common/datepicker.vue'
  import dcDialog from '@/components/common/dcDialog.vue';
  import { caseAdminList,updateStatus, deletestudyshare } from "@/api/studyshare/index";
  import { getDict,getParStr } from "@/utils/tools";
  import noList from '@/components/common/nolist.vue'
  import { checkPermission } from '@/utils/permission'
  export default {
    components: {
      page,
      datepicker
    },
    data() {
      return {
        activeName:'first',
        loadBoo: false,
        caseMap:{
          '1':'视频类',
          '2':'文章类',
          '3':'文档类'
        },
        types: [
          {
            id: '',
            name: '全部'
          },
          {
            id: '1',
            name: '视频类'
          },
          {
            id: '2',
            name: '文章类'
          },
          {
            id: '3',
            name: '文档类'
          }
        ],
        defZan: require('@/assets/img/zan_def.png'),
        selZan: require('@/assets/img/zan_sel.png'),
        tagMaxWid: 0,
        spanNum: 6,
        dataList: [],
        sortList: [],
        deleteVisible: false,
        deleteData: {},
        level: sessionStorage.getItem('dataScope'),
        userId: sessionStorage.getItem('userid'),
        isLoading: false,
        total: 0,
        pageBean: {
          caseName: "",
          caseType: "",
          caseClassify: "",
          createByName: "",
          departmentName:'',
          pageNum: 1,
          pageSize: 10,
          unitName:'',
          time:'',
          startTime:'',
          endTime:''
        },
        options: [],
      }
    },
    created() {
      if (Object.keys(this.$route.query).length>0) {
          this.pageBean = Object.assign(this.pageBean,this.$route.query)
          this.pageBean.pageNum = Number(this.pageBean.pageNum)
          this.pageBean.pageSize = Number(this.pageBean.pageSize)
      }
      this.getTypeList();
      this.loadData();
    },
    methods: {
      getTypeList() {
        getDict('CaseType').then((result) => {
          this.options = result;
        }).catch((err) => {

        });
      },
      loadData() {
        history.replaceState(null,null,`#${this.$route.path}?${getParStr(this.pageBean)}`)
        this.isLoading = true;
        caseAdminList(this.pageBean).then((result) => {
          this.dataList = result.data;
          this.total = result.page.total;
          this.isLoading = false;
        }).catch((err) => {
          this.isLoading = false;
        });
      },

      searchAction() {
        this.pageBean.pageNum = 1;
        this.loadData();
      },
      zanAction(data) {
      },
      onDetail(data) {
        this.$router.push({
          path: '/managementCenter/study/detail',
          query: { id: data.id }
        })
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.loadData();
      },
      onChangeStatus(data){
        var par = {
          id:data.id,
          status:data.status == 1 ? 0 :1
        }
         this.$confirm(`是否${data.status == 1 ? '下架':"上架"}该学习分享`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.update(par)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });
        });
      },
      update(par){
        updateStatus(par).then((result) => {
          if(result.data){
            this.$message({
              type: 'success',
              message: `${par.status == 1 ? '上架':"下架"}成功！`
            });
            this.loadData()
          }else{
            this.$message({
              type:'error',
              message:result.msg
            })
          }
        }).catch((err) => {

        });
      },
      onDelete(data) {
        this.deleteData = data;
        this.deleteVisible = true;
      },
      submitDialog() {
        // 删除数据
        deletestudyshare({ id: this.deleteData.id }).then((result) => {
          if (result.data) {
            this.$message({
              type: "success",
              message: result.msg
            })
            this.deleteVisible = false;
            this.loadData();
          } else {
            this.$message({
              type: "error",
              message: result.msg
            })
          }
        }).catch((err) => {

        });
      },
      submitAction(type, dateRange) {
        if (type == 11) {
          if (dateRange) {
            this.pageBean.startTime = dateRange[0]
            this.pageBean.endTime = dateRange[1]
          } else {
            this.pageBean.startTime = ''
            this.pageBean.endTime = ''
          }
          this.pageBean.time = ''
        } else {
          this.pageBean.time = type
          this.pageBean.startTime = ''
          this.pageBean.endTime = ''
        }
      },
    }
  }
</script>
<style scoped>
  .lookdiv{
    position: absolute;
    left: 0;
    bottom: 0;
    height: 40px;
    background: linear-gradient( 180deg, rgba(51,51,51,0) 0%, rgba(51,51,51,0.6) 100%);
    border-radius: 0px 0px 0px 0px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    line-height: 40px;
    text-align: right;
    color: #FFFFFF;
    width: 100%;
    padding-right: 5px;
  }
  .posi{
    position: relative;
  }
  .tabscss /deep/.el-tabs__item{
     width: 50%;
     text-align: center;
  }
  .tabscss /deep/.el-tabs__nav{
     width: 100%;
  }

  .mtrow{
    margin-top: 10px;
  }
  .deletebtn {
    position: absolute;
    width: 24px;
    height: 24px;
    top: 10px;
    right: 12px;
    cursor: pointer;
  }
  .sharebtn {
    margin-left: 10px;
  }
  .mw67 {
    max-width: 67px;
  }

  .ml10 {
    margin-left: 5px;
  }

  .itemcss {
    position: relative;
    cursor: pointer;
  }

  .text24 {
    width: 24px;
    padding-top: 3px;
    text-align: center;
  }

  .flex {
    display: flex;
    align-items: center;
  }

  .img24 {
    width: 24px;
    height: 24px;
  }

  .rankhead {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-left: 8px;
    margin-right: 12px;
  }

  .rankcss {
    height: 56px;
    padding: 0px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .tc {

    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    line-height: 15px;
    cursor: pointer;
  }

  .phtitle {

    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 19px;

  }

  .paihang_t {
    background: url(../../../assets/liebg.png) no-repeat;
    background-size: 100% 100%;
    height: 20px;
    padding: 0px 16px;
  }

  .paihang_t img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }

  .mr0 {
    margin-right: 0px !important;
  }

  .datacss1 {
    margin-top: 5px;
    padding-left: 12px;
    padding-right: 4px;
    padding-bottom: 20px;
    height: 42px;
    overflow: hidden;
    white-space: nowrap;
    overflow: hidden;
  }

  .tagitem {
    margin-right: 8px;
    padding: 4px 7px;
    padding-bottom: 4px;
    background: #DFEAFD;
    border-radius: 2px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    line-height: 14px;
  }

  .datacss {
    margin-top: 7px;
    height: 24px;
    background: rgba(255,164,76,0.12);
    border-radius: 2px 2px 2px 2px;
    max-width: calc(100% - 24px);
    width: auto;
    display: inline-block;
    margin: 0 12px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #FFA44C;
    box-sizing: border-box;
    padding: 0 8px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .rtext {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 14px;
    margin-top: 2px;
    display: block;
    width: 64px;
    height: 24px;
    background: #FD7A41;
    border-radius: 2px 2px 2px 2px;
    position: absolute;
    left: 10px;
    top: 10px;
    text-align: center;
    line-height: 24px;
  }

  .sptext {
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    line-height: 14px;
    padding-right: 8px;
  }

  .jianjiecss {
    margin: 0px 12px;
    margin-top: 7px;
    height: 46px;
    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #444444;
    line-height: 23px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .zantext {
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    line-height: 16px;
    color: #333333;
    position: absolute;
    top: 10px;
    right: 12px
  }

  .zan_sel {
    color: #FFA44C;
  }

  .iconimg {
    width: 16px;
    height: 16px;
    opacity: 1;
    margin-top: -3px;
  }

  .zanimg {
    width: 16px;
    height: 16px;
    opacity: 1;
    margin-top: -3px;
    margin-right: 4px;
  }

  .namecss {
    padding-left: 66px;
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 32px;
  }

  .condiv {
    position: relative;
    height: 160px;
  }

  .headimg {
    position: absolute;
    top: -18px;
    left: 10px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    opacity: 1;
    border: 2px solid #FFFFFF;
  }

  .mb12 {
    margin-bottom: 12px;
  }

  .fengmian {
    width: 100%;
    border-radius: 8px 8px 0px 0px;
    aspect-ratio: 4 / 3;
  }

  .card {
    width: 100%;
    background-color: white;
    border-radius: 8px;
  }

  .rlist {
    width: 20%;
    min-width: 260px;
    background-color: white;
    border-radius: 8px;
  }

  .leftlist {
    margin-right: 12px;
    width: 80%;
    height: 100%;
    min-height: calc(100vh - 300px);
  }

  .concss {
    display: flex;
    padding: 20px;
    min-height: calc(100% - 74px);
  }

  .mt {
    margin-top: 0px;
  }

  .ml10 {
    margin-left: 20px;
  }

  .rheadcss {
    height: 38px;
    line-height: 38px;
    width: 15vw;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .header {
    width: 100%;
    padding-top: 17px;
    background-color: white;
    display: flex;
  }

  .myform /deep/.el-form-item {
    margin-bottom: 10px !important;
    padding-right: 1vw;
  }

  .myform /deep/.el-form-item .el-input__inner {
    width: 100% !important;
  }

  .bg {
    min-height: calc(100%);
    padding: 0px 20px;
    background-color: #FFFFFF;
  }

  .llist {
    /* height:  */
  }
</style>
<style>

</style>
