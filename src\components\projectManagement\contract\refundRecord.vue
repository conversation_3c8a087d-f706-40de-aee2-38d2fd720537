<template>
  <div>
    <div class="mb20">
      <span class="ttext deffont"><span class="deffont mr10"></span></span>
      <div class="right flex">
        <el-button
          class="defaultbtn"
          icon="el-icon-my-refund"
          type="primary"
          @click="addAction"
          >退款</el-button
        >
      </div>
    </div>
    <el-table
      :height="600"
      class="mytable"
      :data="dataList"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        property="refundAmount"
        label="退款金额（万元）"
        align="center"
      ></el-table-column>
      <el-table-column prop="refundStatus" label="退款状态" align="center">
        <template slot-scope="scope">
          <span
            class="circle"
            :class="{
              'c-five': scope.row.status == 5,
              'c-four': scope.row.status == 4,
              'c-three': scope.row.status == 3,
              'c-two': scope.row.status == 2,
              'c-one': scope.row.status == 1,
            }"
          ></span>
          {{ commonStatusMap[scope.row.status] }}
        </template>
      </el-table-column>
      <el-table-column
        property="createByName"
        label="申请人"
        align="center"
      ></el-table-column>
      <el-table-column
        property="refundDepartmentName"
        label="申请人部门"
        align="center"
      ></el-table-column>
      <el-table-column
        property="examTime"
        label="退款日期"
        align="center"
      ></el-table-column>
      <el-table-column
        property="createTime"
        label="申请时间"
        align="center"
      ></el-table-column>
      <el-table-column property="edit" label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            class="deffont bbtn"
            type="text"
            @click="lookAction(scope.row)"
            >详情</el-button
          >
          <el-button
            class="deffont bbtn"
            type="text"
            @click="editAction(scope.row)"
            v-if="
              (scope.row.status == 4 || scope.row.status == 5) &&
              userId == scope.row.createBy
            "
            >编辑</el-button
          >
          <el-button
            class="deffont bbtn"
            type="text"
            v-if="scope.row.status == 1 && userId == scope.row.createBy"
            @click="revokeAction(scope.row)"
            >撤销</el-button
          >
          <el-button
            v-isShow="'crm:controller:contractrefund:delete'"
            class="deffont rbtn"
            type="text"
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <img
          class="nodataimgcss"
          src="../../../assets/img/nodata_icon.png"
          alt=""
          srcset=""
        />
        <p>暂无数据～</p>
      </template>
    </el-table>
    <page
      :currentPage="pageBean.pageNum"
      :pageSize="pageBean.pageSize"
      :total="total"
      @updatePageNum="handleCurrentChange"
    ></page>
    <refundDialog
      ref="refundDialog"
      :visible.sync="dialogVisible"
      :contractId="pageBean.contractId"
      :isEdit="isEdit"
      :editData="currentEditData"
      @updateVisible="updateVisible"
      @submitSuccess="submitSuccess"
    >
    </refundDialog>
    <refundDetailDrawer
      ref="refundDetail"
      :visible.sync="detailVisible"
      @updateVisible="updateDetailVisible"
    >
    </refundDetailDrawer>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import refundDialog from './components/refundDialog.vue'
import refundDetailDrawer from './components/refundDetailDrawer.vue'
import {
  contractrefundList,
  deleteContractrefund,
  updateContractrefund,
} from '@/api/contract'
import { commonStatusMap } from '@/utils/status-tool'

export default {
  components: {
    page,
    refundDialog,
    refundDetailDrawer,
  },
  data() {
    return {
      detailVisible: false,
      dialogVisible: false,
      dataList: [],
      isLoading: false,
      pageBean: {
        contractId: this.$route.query.id,
        pageNum: 1,
        pageSize: 10,
      },
      total: 19,
      commonStatusMap,
      isEdit: false,
      currentEditData: {},
      userId: sessionStorage.getItem('userid'),
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.isLoading = true
      contractrefundList(this.pageBean)
        .then((result) => {
          this.dataList = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    lookAction(data) {
      this.detailVisible = true
      this.$refs.refundDetail.loadData(data.id,data.status)
    },
    updateDetailVisible(data) {
      this.detailVisible = data
    },
    // 编辑
    editAction(data) {
      this.isEdit = true
      this.currentEditData = data
      this.dialogVisible = true
      setTimeout(() => {
        this.$refs.refundDialog.loadDetailData(data.id)
      }, 100)
    },
    // 撤销
    revokeAction(data) {
      this.$confirm('是否撤销退款申请？撤销后该申请可以再次提交', '提示', {
        confirmButtonText: '撤销',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.isLoading = true
        updateContractrefund({ id: data.id,status:5 }).then((result) => {
          this.isLoading = false
          if (result.data) {
            this.$message({
              type: 'success',
              message: '撤销成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.message,
            })
          }
        }).catch(()=>{
          this.isLoading = false
        })
      })
    },
    deleteAction(data) {
      this.isLoading = true
      deleteContractrefund({ id: data.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
            this.isLoading = false
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    addAction() {
      this.isEdit = false
      this.currentEditData = {}
      this.dialogVisible = true
    },
    updateVisible(val) {
      this.dialogVisible = val
    },
    submitSuccess() {
      this.updateVisible(false)
      this.pageBean.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>

<style scoped>
.nodataimgcss {
  margin-bottom: 12px;
  width: 180px;
  height: 92px;
}
.mytable /deep/.el-table__empty-text {
  line-height: 20px !important;
  min-height: 20px !important;
}

.submitbtn {
  margin-top: 50px;
}

.flex {
  display: flex;
  align-items: center;
}

.lh {
  width: 100%;
  line-height: 23px;
}
.ttext {
  color: #999999;
  line-height: 34px;
}
.center {
  text-align: center;
}

.ml20 {
  margin-left: 20px;
}
</style>
<style scoped>
</style>
