<template>
  <div class="mainbg fixpb">
    <div class="backcss">
      <back class="backc">返回</back>
      <span class="namec">{{ $route.query.name }}</span>
    </div>

    <div class="bgwhite">
      <el-form class="myform" inline>
        <el-form-item label="">
          <el-input
            clearable
            v-model="pageBean.keyword"
            class="definput w300"
            placeholder="请输入任务ID/任务名称/任务负责人"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="状态：">
          <el-select
            clearable
            class="definput w110"
            popper-class="removescrollbar"
            v-model="pageBean.taskStatus"
            placeholder="请选择"
          >
            <el-option
              v-for="item in optionsStatus"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级：" class="mr20">
          <el-select
            clearable
            class="definput w110 mr10"
            popper-class="removescrollbar"
            v-model="pageBean.priority"
            placeholder="请选择"
          >
            <el-option
              v-for="item in grades"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" class="mr20">
          <el-checkbox v-model="pageBean.involved">我参与的</el-checkbox>
        </el-form-item>
        <el-form-item label="" class="mr20">
          <el-checkbox v-model="pageBean.overdue">只看逾期</el-checkbox>
        </el-form-item>
        <el-form-item label="">
          <el-button
            @click="search"
            class="defaultbtn mt"
            icon="el-icon-search"
            type="primary"
            >搜索</el-button
          >
        </el-form-item>
        <el-form-item label="" class="fr">
          <el-button
            @click="showAdd"
            class="defaultbtn mt"
            icon="el-icon-search"
            type="primary"
            v-isShow="'crm:controller:task:save'"
          >
            新建任务</el-button
          >
        </el-form-item>
      </el-form>
      <questlist
        @flushData="flushData"
        :tableData="tableData"
        :isControl="true"
        :isLoading="isLoading"
        :isAutoHeight="false"
      ></questlist>
      <div class="fixpage">
        <page
          :currentPage="pageBean.pageNum"
          :total="total"
          :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"
        ></page>
      </div>
    </div>
  </div>
</template>

  <script>
import page from '../../../common/page.vue'
import back from '@/components/common/back.vue'
import questlist from '../../common/questlist.vue'

import { projectDelete, taskList } from '@/api/project'
import { getParStr } from '@/utils/tools'

export default {
  components: {
    page,
    back,
    questlist,
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      grades: [
        {
          value: 1,
          name: '低',
        },
        {
          value: 2,
          name: '一般',
        },
        {
          value: 3,
          name: '紧急',
        },
        {
          value: 4,
          name: '非常紧急',
        },
      ],
      typeToValue: {
        1: 'collaborator',
        2: 'chargePerson',
      },
      dialogVisible: false,
      optionsResponsible: [
        {
          value: '0',
          name: '全部',
        },
        {
          value: '1',
          name: '我协作的',
        },
        {
          value: '2',
          name: '我负责的',
        },
      ],
      types: [],
      subTypes: [],
      optionsStatus: [
        {
          value: '',
          name: '全部',
        },
        {
          value: '1',
          name: '未开始',
        },
        {
          value: '2',
          name: '进行中',
        },
        {
          value: '3',
          name: '已完成',
        },
        {
          value: '4',
          name: '暂停',
        },
        {
          value: '5',
          name: '停止',
        },
      ],
      isLoading: false,
      tableData: [],
      total: 100,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        taskStatus: '',
        priority: '',
        involved: '',
        overdue: '',
        projectId: this.$route.query.projectId,
      },
      selectValue: '',
      userId: '',
      statusToCss: {
        1: 'nobegin',
        2: 'doing',
        3: 'end',
      },
      dId: '',
      projectStatus: this.$route.query.sta,
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
      this.pageBean.involved = Boolean(this.pageBean.involved)
      this.pageBean.overdue = Boolean(this.pageBean.overdue)
    }
    this.projectListData()
    this.userId = sessionStorage.getItem('userid') || ''
  },
  computed: {
    computedCss() {
      return function (row) {
        let topWidth = row.top ? 35 : 10
        let tagWidth =
          row.chargePerson == this.userId ||
          row.collaborator.split(',').includes(this.userId)
            ? 42
            : 10
        let width = 227 - topWidth - tagWidth
        return {
          width: width + 'px',
        }
      }
    },
    statusCss() {
      return (row) => {
        return [this.statusToCss[row.projectStatus]]
      }
    },
  },
  methods: {
    flushData() {
      this.projectListData()
    },
    getTypesData() {
      getDict('ContractType').then((res) => {
        this.types = res
      })
    },
    search() {
      this.pageBean.pageNum = 1
      this.projectListData()
    },
    showAdd() {
      if (this.visibleProjectStatus()) {
        return
      }
      this.$router.push({
        path: '/project/manager/quest/add',
        query: {
          projectId: this.$route.query.projectId,
          projectName: this.$route.query.name,
        },
      })
    },
    visibleProjectStatus() {
      if (this.projectStatus != 2) {
        this.$message.info('该项目未启动，无法进行操作')
        return true
      }
      return false
    },
    projectListData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      taskList(this.pageBean)
        .then((res) => {
          this.isLoading = false
          if (res.status == 0) {
            this.tableData = res.data
            this.total = res.page.total
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    changeDrawer(v) {
      this.drawer = v
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.projectListData()
    },
  },
}
</script>
  <style scoped>
.bgwhite {
  padding-bottom: 10px !important;
}
.backcss {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  background-color: #f7f7f7;
  padding-bottom: 20px;
  position: relative;
  text-align: center;
}
.backc {
  display: inline-block;
  position: absolute;
  left: 0;
}
.mt12 {
  margin-top: 12px;
}
.w110 {
  width: 110px;
}
.wid {
  width: calc(100% - 130px);
}
.mr20 {
  margin-right: 20px;
}
.w300 {
  width: 300px;
}
</style>
  <style scoped lang="scss">
.mainbg {
  min-height: 100%;
  background-color: white;
}
.fr {
  float: right;
}
.smt {
  margin-top: 19px;
}

.protext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #409eff;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  display: inline-block;
  margin-top: 16px;
  vertical-align: top;
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 5px;
  text-align: center;
}

.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}

.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
  margin-bottom: 5px;
}

.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}
.customertable .el-button {
  padding: 2px;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}
</style>
