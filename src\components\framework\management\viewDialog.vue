<template>
    <div>
        <el-dialog
        title="会员详情"
        :visible.sync="dialogVisible"
        width="60%">
            <el-tabs type="card" v-model="activeName">
                <el-tab-pane label="基本资料" name="first">
                    <first :first="parameters"></first>
                </el-tab-pane>
                <el-tab-pane label="更多资料" name="second">
                    <second :second="parameters"></second>
                </el-tab-pane>
                <el-tab-pane label="用户收入" name="fourth">
                    <fourth :fourth="parameters"></fourth>
                </el-tab-pane>
                <el-tab-pane label="用户支出" name="fifth">
                    <fifth :fifth="parameters"></fifth>
                </el-tab-pane>
                <el-tab-pane label="用户充值" name="sixth">
                    <sixth :sixth="parameters"></sixth>
                </el-tab-pane>
                <el-tab-pane label="用户提现" name="last">
                    <last :last="parameters"></last>
                </el-tab-pane>
                <el-tab-pane label="审核记录" name="third">
                  <third :third="parameters"></third>
                </el-tab-pane>
            </el-tabs>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="closePopup">确 定</el-button>
        </span>
        </el-dialog>
    </div>
</template>

<script>
import first from './components/first'
import second from './components/second'
import third from './components/third'
import fourth from './components/fourth'
import fifth from './components/fifth'
import sixth from './components/sixth'
import last from './components/last'

export default {
  data() {
    return {
      dialogVisible: false,
      activeName: "first",
      parameters: {},
      firstNumber: ''
    }
  },
  methods: {
    init (obj) {
      this.dialogVisible = true
      this.parameters = obj
      this.activeName = 'first'
      this.$nextTick(() => {
        this.parameters = obj
      })
    },
    closePopup() {
      setTimeout(() => {
        this.dialogVisible = false
      }, 300)
    }
  },
  components: {
    first,
    second,
    third,
    fourth,
    fifth,
    sixth,
    last
  }
}
</script>
