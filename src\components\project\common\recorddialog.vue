<template>
  <el-dialog
  title="工作记录"
  class="mydialog"
  top="50px"
  :visible.sync="dialogVisible"
  width="80%"
  center
  :before-close="handleClose">
    <div class="theadcss" v-if="idAndName">ID：{{idAndName.taskNum}} {{idAndName.taskName}}</div>
    <el-tabs class="mtabscss" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="工作记录" name="first">
            <div class="ddiv">
                <div class="formdiv" v-if="taskIdObj.isEnable != 1" >
                    <copyaddrecord ref="resetRef" @submitData="submitData" :recordType="'work'"></copyaddrecord>
                </div>
                <div class="listdiv" :class="{w100 : taskIdObj.isEnable == 1}">
                    <copyworkrecord :isControl="taskIdObj.isEnable != 1" :isCreateBy="true"  :type="type" @deleteSubmit="deleteSubmit"></copyworkrecord>  
                </div>
            </div>
        </el-tab-pane>
        <el-tab-pane label="成本记录" name="third">
            <div class="ddiv">
                <div class="formdiv" v-if="taskIdObj.isEnable != 1">
                    <copyaddrecord   ref="constRef" @submitData="submitData" :recordType="'cost'"></copyaddrecord>
                </div>
                <div class="listdiv" :class="{w100 : taskIdObj.isEnable == 1}">
                    <costrecord :isControl="taskIdObj.isEnable != 1 " :isCreateBy="true"  :type="type"></costrecord>
                </div>
            </div>
        </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import costrecord from './costrecord.vue';
import copyworkrecord from './copyworkrecord.vue';
import copyaddrecord from './copyaddrecord.vue';
import { taskworkhoursSave,taskcostSave } from '@/api/project'
import Bus from '@/utils/EventBus'
export default {
    components:{
        costrecord,
        copyworkrecord,
        copyaddrecord
    },
    props:{
        dialogVisible:{
            type:Boolean,
            default:false
        },
        type:{
            type:String,
            default:'1'
        },
        idAndName:{
            type:Object,
            default:()=>{
                return new Object()
            }
        }
    },
    // inject: ['taskIdObj'],
    inject: {
        taskIdObj: {
            default:()=>{
                return new Object()
            }
        }
    },
    data(){
        return{
            activeName:'first',
            strToObj:{
                'work':1,
            },
            isUpdate:false
        }
    },
    methods:{
        deleteSubmit(){
            this.isUpdate = true
        },
        submitData(data){
            if(data.recordType == 'work'){
                data.form.taskId = this.taskIdObj.taskId
                data.form.type = this.strToObj[data.recordType]
                data.form.taskType = this.type
                taskworkhoursSave(data.form).then(res=>{
                    if(res.status == 0){
                        this.$message({
                            type: "success",
                            message: "保存成功"
                        });
                        this.isUpdate = true
                        Bus.$emit('loadData')
                        this.$refs.resetRef.resetForm()
                    }
                })
            }else{
                data.form.taskId = this.taskIdObj.taskId
                data.form.taskType = this.type;
                taskcostSave(data.form).then(res=>{
                    if(res.status == 0){
                        this.$message({
                            type: "success",
                            message: "保存成功"
                        });
                        Bus.$emit('loadDataConst')
                        this.$refs.constRef.resetForm()
                    }
                })
            }
        },
        handleClose(){
            if(this.activeName == 'third'){
                this.$refs.constRef && this.$refs.constRef.resetForm()
            }else if(this.activeName == 'first'){
                this.$refs.resetRef && this.$refs.resetRef.resetForm()
            }
            this.$emit('closeDialog',this.isUpdate)
            this.activeName = 'first'
        },
        handleClick(){
            if(this.activeName == 'third'){
                Bus.$emit('loadDataConst')
            } else if(this.activeName == 'first'){
                Bus.$emit('loadData')
            }
        },
    }
}
</script>

<style lang="scss" scoped>

.formdiv{
    width: 40%;
    border-right: 1px solid #F0F0F0;
    padding-top: 30px;
    padding-right: 30px;
}
.w100{
    width: 100% !important;
}
.listdiv{
    width: 60%;
    padding: 30px 0;
    padding-left: 30px;
}
.ddiv{
   display: flex;
}
.mtabscss{
    margin-top: 20px ;
    border-bottom: 1px solid #F0F0F0;
}
.mtabscss /deep/.el-tabs__header{
    margin-bottom: 0px !important;
}
.mtabscss /deep/.el-tabs__item{
    padding: 0 47px !important;
}
.mydialog /deep/.el-dialog__header{
    border-bottom: none;
    margin-bottom: 0px !important;
}
.mydialog /deep/.el-dialog__body{
    padding: 20px 20px 0px;
    border-bottom: none !important;
}
.theadcss{
    padding: 0 30px;
    height: 45px;
    line-height: 45px;
    background: #4285F4;
    border-radius: 8px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #FFFFFF;
}
.mydialog /deep/.el-dialog__body{
    padding-top: 0px !important;
}
.mydialog /deep/ .el-dialog__headerbtn{
    right: 20px !important;
}
.mydialog{
    min-width: 1000px;
}
</style>