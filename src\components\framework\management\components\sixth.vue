<!--会员充值-->
<template>
    <div>
        <BaseOrderNumber :moneyList="moenyData"></BaseOrderNumber>
        <el-form :inline="true">
            <el-form-item>
                <el-select clearable placeholder="选择充值类型" v-model="dataForm.businessType">
                    <el-option
                        v-for="item in Dict.Money_STATUS"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
              <SelectDatePicker v-model="time"></SelectDatePicker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" @click="handleSearch">查询</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
            prop="orderId"
            label="订单号"
            width="140">
            </el-table-column>
            <el-table-column
            label="充值方式"
            width="200">
                <template slot-scope="scope">
                  <span>{{scope.row.paymentChannelType | dict('Money_STATUS')}}</span>
                </template>
            </el-table-column>
            <el-table-column
            prop="amount"
            label="充值金额（元）"
            width="140">
            </el-table-column>
            <el-table-column
            label="充值时间"
            width="180">
              <template slot-scope="scope">
                <span>{{ruleTimeSuccess(scope.row.paymentTime)}}</span>
              </template>
            </el-table-column>
            <el-table-column
            prop="redpacketAmount"
            label="状态"
            width="140">
            </el-table-column>
            <el-table-column
            prop="closeTime"
            label="备注">
              <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small"></el-button>
              </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div>
          <el-pagination
            v-if="pagingObj.totalSum"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagingObj.currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagingObj.totalSum">
          </el-pagination>
        </div>
    </div>
</template>

<script>
import BaseOrderNumber from '@/components/base/BaseOrderNumber.vue'
import { ruleTimeSuccess } from '../../../../utils/tools'
import SelectDatePicker from '@/components/select/SelectDatePicker.vue'

export default {
  props: [ 'sixth' ],
  data() {
    return {
      dataValue: '',
      tableData: [],
      area: '',
      time: [],
      moenyData: [
        {
          label: '充值总额',
          value: ''
        }
      ],
      pagingObj: {
        totalSum: '',
        currentPage: 1
      },
      rechargeStatistics: {},
      // 会员充值列表参数
      dataForm: {}
    }
  },
  components: {
    BaseOrderNumber,
    SelectDatePicker
  },
  watch: {
    sixthSelectValue(val) {
      this.sixth = val
      this.dataForm.userId = this.sixth.id
      this.getMemberRecharge(this.dataForm)
      this.memberRechargeStatistics(this.dataForm)
    }
  },
  computed: {
    sixthSelectValue() {
      return this.sixth
    }
  },
  methods: {
    // 时间戳转化为时分秒
    ruleTimeSuccess (val) {
      return ruleTimeSuccess(val)
    },
    // 分页
    handleSizeChange (val) {
      this.dataForm.pageSize = val
      this.getMemberIncome(this.dataForm)
    },
    handleCurrentChange (obj) {
      this.dataForm.pageNum = obj
      this.getMemberIncome(this.dataForm)
    },
    // 查询
    handleSearch() {
      this.handleTimeParams(this.time)
      this.getMemberRecharge(this.dataForm)
    },
    // 获取时间
    handleTimeParams (obj) {
      if (obj && obj instanceof Array) {
        this.dataForm.startTime = obj[0];
        this.dataForm.endTime = obj[1]
      } else {
        this.dataForm.startTime = '';
        this.dataForm.endTime = ''
      }
    },
    // 会员充值列表
    async getMemberRecharge(obj) {
      let res = await this.$axios.get('sf/member/fundAccountFlow/userRecharge', { params: obj })
      if (res.status === 0) {
        this.tableData = res.data
        this.pagingObj.totalSum = res.page.total
        console.log(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 会员充值统计
    async memberRechargeStatistics(obj) {
      obj.fundType = "3"
      let res = await this.$axios.get('sf/member/fundAccountFlow/totalFund', { params: this.rechargeStatistics })
      if (res.status === 0) {
        this.RechargeType = res.data
        if (res.data === null) {
          this.moenyData[0].value = 0
        } else {
          this.moenyData[0].value = res.data.totalOrder
        }
      } else {
        this.$message.error(res.msg)
      }
    }
  },
  created() {
    this.dataForm.userId = this.sixth.id
    this.getMemberRecharge(this.dataForm)
    this.memberRechargeStatistics(this.dataForm)
  }
}
</script>

<style lang="scss" scoped>
.enveloright {
  top: 0px !important;
  right: 0px !important;
  position: relative !important;
}
</style>
