<!--会员支出-->
<template>
    <div>
        <BaseOrderNumber :moneyList="moenyData"></BaseOrderNumber>
        <el-form :inline="true">
            <el-form-item>
                <el-select clearable placeholder="消费类型" v-model="dataForm.businessType">
                    <el-option
                        v-for="item in IncomeType"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
              <SelectDatePicker v-model="time"></SelectDatePicker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" @click="handleSearch">查询</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
            prop="orderId"
            label="订单号"
            width="140">
            </el-table-column>
            <el-table-column
            prop="description"
            label="支付类型"
            width="200">
            </el-table-column>
            <el-table-column
            prop=""
            label="支付方式"
            width="140">
            </el-table-column>
            <el-table-column
            prop="payTotalAmount"
            label="订单金额（元）"
            width="180">
            </el-table-column>
            <el-table-column
            prop="redpacketAmount"
            label="红包低值"
            width="140">
            </el-table-column>
            <el-table-column
            prop="realityAmount"
            label="实际支付（元）"
            width="120">
            </el-table-column>
            <el-table-column
            label="交易时间"
            width="120">
                <template slot-scope="scope">
                  <span>{{ruleTimeSuccess(scope.row.createTime)}}</span>
                </template>
            </el-table-column>
            <el-table-column
            prop="closeTime"
            label="操作">
              <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small">详情</el-button>
              </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div>
          <el-pagination
            v-if="pagingObj.totalSum"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagingObj.currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagingObj.totalSum">
          </el-pagination>
        </div>
    </div>
</template>

<script>
import SelectDatePicker from '@/components/select/SelectDatePicker.vue'
import { ruleTimeSuccess } from '../../../../utils/tools'
import BaseOrderNumber from '@/components/base/BaseOrderNumber.vue'

export default {
  props: [ 'fifth' ],
  data() {
    return {
      dataValue: '',
      tableData: [],
      IncomeType: [],
      time: [],
      moenyData: [
        {
          label: '订单总额',
          value: ''
        },
        {
          label: '红包总额',
          value: ''
        },
        {
          label: '支付总额',
          value: ''
        }
      ],
      pagingObj: {
        totalSum: '',
        currentPage: 1
      },
      dataForm: {},
      spendingStatistics: {}
    }
  },
  components: {
    BaseOrderNumber,
    SelectDatePicker
  },
  watch: {
    fifthSelectValue(val) {
      this.fifth = val
      this.dataForm.userId = this.fifth.id
      this.getSpendingStatistics(this.dataForm)
      this.getMemberIncome(this.dataForm)
      this.init()
    }
  },
  computed: {
    fifthSelectValue() {
      return this.fifth
    }
  },
  methods: {
    // 获取消费类型下拉框
    async init() {
      let typeSelect = {}
      typeSelect.fundType = '2'
      let res = await this.$axios.get('sf/member/fundAccountFlow/typeFundList', { params: typeSelect })
      if (res.status === 0) {
        this.IncomeType = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 时间戳转化为时分秒
    ruleTimeSuccess (val) {
      return ruleTimeSuccess(val)
    },
    // 获取时间
    handleTimeParams (obj) {
      if (obj && obj instanceof Array) {
        this.dataForm.startTime = obj[0];
        this.dataForm.endTime = obj[1]
      } else {
        this.dataForm.startTime = '';
        this.dataForm.endTime = ''
      }
    },
    // 分页
    handleSizeChange (val) {
      this.dataForm.pageSize = val
      this.getMemberIncome(this.dataForm)
    },
    handleCurrentChange (obj) {
      this.dataForm.pageNum = obj
      this.getMemberIncome(this.dataForm)
    },
    // 查询
    handleSearch () {
      this.handleTimeParams(this.time);
      this.getMemberIncome(this.dataForm);
      this.getSpendingStatistics(this.dataForm)
    },
    // 会员支出列表
    async getMemberIncome(obj) {
      let res = await this.$axios.get('sf/member/fundAccountFlow/fundList', { params: obj })
      if (res.status === 0) {
        this.tableData = res.data
        this.pagingObj.totalSum = res.page.total
      } else {
        this.$message.error(res.msg)
      }
    },
    // 会员支出统计
    async getSpendingStatistics(obj) {
      let res = await this.$axios.get('sf/member/fundAccountFlow/totalFund', { params: obj })
      if (res.status === 0) {
        if (res.data === null) {
          this.moenyData[0].value = 0
          this.moenyData[1].value = 0
          this.moenyData[2].value = 0
        } else {
          this.moenyData[0].value = res.data.totalOrder
          this.moenyData[1].value = res.data.totalRedpacket
          this.moenyData[2].value = res.data.totalPayment
        }
      } else {
        this.$message.error(res.msg)
      }
    }
  },
  created() {
    this.dataForm.userId = this.fifth.id
    this.dataForm.fundType = "2"
    this.dataForm.pageNum = '1'
    this.dataForm.pageSize = '10'
    this.getSpendingStatistics(this.dataForm)
    this.getMemberIncome(this.dataForm)
    this.init()
  }
}
</script>

<style lang="scss" scoped>
.enveloright {
  top: 0px !important;
  right: 0px !important;
  position: relative !important;
}
</style>
