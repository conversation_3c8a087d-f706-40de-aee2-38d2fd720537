<!-- 冻结原因 -->
<template>
  <div>
    <el-select clearable placeholder="请选择" v-model="myValue" @change="change">
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  props: ['value'],
  data () {
    return {
      myValue: '',
      options: []
    }
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    change (val) {
      this.$emit('input', this.myValue)
    },
    async getReqData () {
      let res = await this.$axios.get('/sf/business/tagitem/listAll', {params: {tagtypeCode: 'FREEZE_REASON'}})
      if (res.status === 0) {
        this.options = res.data
        console.log(this.value)
      }
    }
  },
  created () {
    this.myValue = this.value || ''
    this.getReqData()
  }
}
</script>
