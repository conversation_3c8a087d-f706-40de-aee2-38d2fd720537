<template>
  <div class="bg">
    <div class="titlefl" v-loading="isLoading">
      <Back>返回</Back>
      <div class="titlecss">个人目标详情</div>
    </div>
    <div class="timecss">{{ nowTime }}</div>
    <el-row :gutter="12">
      <el-col :span="4" v-for="(item, index) in list" :key="index">
        <dataitem :item="item"></dataitem>
      </el-col>
    </el-row>
    <div class="goallist mt50">
      <div class="goalitem flex" v-for="(item, index) in list" :key="index">
        <img class="imgcss" :src="item.icon" alt="" />
        <div class="width100 pl12">
          <div class="textcss">
            距离目标达成还差{{ item.residueGoal }}{{ item.unit
            }}{{ type[item.id] }}
          </div>
          <el-progress
            class="progress"
            :percentage="getPercentage(item)"
            :format="() => ''"
          ></el-progress>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Back from '@/components/common/back.vue'
import dataitem from '../../goal/show/components/dataitem.vue'
import { personalgoalsInfo } from '@/api/goal'

export default {
  components: {
    dataitem,
    Back,
  },
  data() {
    return {
      list: [
        {
          id: 2,
          name: '新增拜访与跟进',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img2.png'),
          unit: '次',
          color: '#4A8BF6',
          finishRate: 0,
        },
        {
          id: 1,
          name: '新增客户',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img5.png'),
          unit: '个',
          color: '#F46D40',
          finishRate: 0,
        },
        {
          id: 5,
          name: '业绩',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img3.png'),
          unit: '万元',
          color: '#E85D5D',
          finishRate: 0,
        },
        {
          id: 6,
          name: '回款金额',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img4.png'),
          unit: '万元',
          color: '#FF8D1A',
          finishRate: 0,
        },
        {
          id: 4,
          name: '新增合同',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img/hetong_icon.png'),
          unit: '个',
          color: '#4A8BF6',
          finishRate: 0,
        },
        {
          id: 7,
          name: '样书发放',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/yangshuicon.png'),
          unit: '次',
          color: '#56C36E',
          finishRate: 0,
        },
      ],
      nowTime: '',
      isLoading: false,
      goalId: '',
      type: {
        1: '客户',
        2: '拜访与跟进',
        5: '业绩',
        6: '回款金额',
        7: '样书发放',
        4: '合同',
      },
    }
  },
  created() {
    this.goalId = this.$route.query.id
    this.loadData()
  },
  methods: {
    loadData() {
      if (!this.goalId) {
        this.$message.error('缺少目标ID参数')
        return
      }

      this.isLoading = true
      personalgoalsInfo({ personalGoalsId: this.goalId })
        .then((result) => {
          this.isLoading = false
          this.nowTime = `${this.$route.query.year}年度目标 - ${this.$route.query.userName}`
          var list = result.data.goalSummaryVoList
          var data = {}
          list.forEach((element) => {
            data[element.goalType] = element
          })
          this.list.forEach((item) => {
            var dataItem = data[item.id]
            item.goal = dataItem.goal
            item.finishGoal = dataItem.finishGoal
            item.residueGoal = dataItem.residueGoal
            item.finishRate = dataItem.finishRate
          })
        })
        .catch((err) => {
          this.isLoading = false
          this.$message.error('获取详情失败')
        })
    },
    getPercentage(item) {
      if (Object.keys(item).length <= 0) {
        return 0
      }
      if (item.goal == 0 || item.finishGoal == 0) {
        return 0
      } else if (item.finishGoal / item.goal > 1) {
        return 100
      } else {
        return item.finishRate * 100
      }
    },
  },
}
</script>
<style scoped>
.timecss {
  margin-top: 8px;
  text-align: right;

  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #cccccc;
}
.titlefl {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.width100 {
  width: 80%;
}
.textcss {
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
  margin: 15px 0px;
}
.progress /deep/.el-progress-bar__outer {
  height: 4px !important;
  background-color: #dfeafd;
}
.progress /deep/.el-progress-bar__inner {
  background-color: #5a98ff;
}
.progress /deep/.el-progress-bar {
  margin-right: 0px;
}
.inblock {
  display: inline-block;
}
.mt50 {
  margin-top: 26px;
}
.goalitem {
  height: 72px;
  line-height: 72px;
  margin-bottom: 20px;
}
.ml {
  margin-left: 3px;
}
.mt {
  margin-top: 5px;
}
.goalcss {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 16px;
}
.finishGoalcss {
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
  color: #f46d40;
  line-height: 28px;
}
.pl12 {
  padding-left: 12px;
}
.flex {
  display: flex;
  align-items: center;
}
.imgcss {
  width: 3em;
  height: 3em;
  margin-left: 1.5vw;
}
.typename {
  padding-top: 20px;
  margin-left: 20px;
  margin-bottom: 1.25em;
  font-size: 1em;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.dataitem {
  margin-top: 16px;
  height: 9.375em;
  background: #ffffff;
  box-shadow: 0px 2px 16px 0px rgba(15, 27, 50, 0.08);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}

.titlecss {
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 23px;
}
.bg {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  background-color: white;
  border-radius: 8px;
}
</style>
