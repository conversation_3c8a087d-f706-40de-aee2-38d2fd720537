<template>
  <div class="mainbg">
    <el-form label-width="85px"  class="myform " >
        <el-row :gutter="30" type="flex" justify="start">
            <el-col :span="8">
                <el-form-item label="模板名称：" prop="name">
                    <el-input class="definput" v-model="pageBean.name" clearable placeholder="请输入模板名称"></el-input>
                </el-form-item>
                
            </el-col>
            <el-col :span="4">
                <el-button class="defaultbtn mt" icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
            </el-col>
            <el-col :span="12">
                <el-button class="defaultbtn mt right" v-isShow="'crm:controller:projectstagetemplate:save'" icon="el-icon-plus" type="primary" @click="addAction">新建模版</el-button>
            </el-col>
        </el-row>
    </el-form>
    <el-table  class="jhtable mytable"  :data="tableData" height="590px" style="width: 100%" v-loading="isLoading">
      <el-table-column
        prop="name"
        label="模板名称 "
        align="center"
        >
      </el-table-column>
      <el-table-column
        prop="createByName"
        label="创建人"
        align="center"
        >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        >
      </el-table-column>
      <el-table-column
        prop="isEnable"
        label="状态"
        align="center"
        >
        <template slot-scope="scope">
            <span v-if="scope.row.isEnable" class="deffont yesc">启用</span>
            <span v-else class="noc">停用</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="edit"
        width="200px"
        align="center"
        label="更多操作"
        fixed="right">
        <template slot-scope="scope">
          <el-button class="bbtn " :disabled="getDisabled(scope.row)" type="text" @click="changeEnable(scope.row)"> {{scope.row.isEnable == true ? '停用' :'启用'}}</el-button>
          <el-button class="bbtn " :disabled="getDisabled(scope.row)"  type="text" @click="onEdit(scope.row)"> 编辑</el-button>
          <el-button class="rbtn " :disabled="getDisabled(scope.row)"  type="text" @click="deleteAction(scope.row)"> 删除</el-button>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page 
    :currentPage="pageBean.pageNum" 
    :total="total" 
    :pageSize="pageBean.pageSize" 
    @updatePageNum="handleCurrentChange"
    ></page>    
    <dc-dialog iType="1" title="确定删除吗？" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
      <template>

      </template>
      <p class="pcc">是否确认删除该模版？</p>
    </dc-dialog>
    <!-- <revisit :visible.sync="revisitVisible" @updateVisible="updateVisible"></revisit> -->
  </div>
</template>

<script>
import page from '../../common/page.vue';
import nolist from '../../common/nolist.vue';
import { projectstagetemplateList,deleteProjectstagetemplate,updateProjectstagetemplate } from '@/api/project/template';
export default {
    components:{
        page,
        nolist,
        // stageDialog
    },
    data(){
        return{
            level:sessionStorage.getItem('dataScope'),
            userId:sessionStorage.getItem('userid'),
            dialogVisible:false,
            deleteData:{},
            isLoading:false,
            tableData:[],
            total:0,
            pageBean:{
                name:"",
                pageNum:1,
                pageSize:10,
            }
        }
    },
    created(){
        this.loadData();
    },
    methods:{
        loadData(){
            this.isLoading = true;
            projectstagetemplateList(this.pageBean).then((result) => {
                this.tableData = result.data;
                this.total = result.page.total;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        getDisabled(data){
            if (this.level == 4 || this.userId == data.createdBy) {
                return false
            }
            return true;
        },
        searchAction(){
            this.pageBean.pageNum = 1;
            this.loadData();
        },
        updateGiveVisible(val){
            this.givedialogVisible = val;
        },
        updateVisible(val){
            this.revisitVisible = val;
        },
        submitDialog(){
            // this.isLoading = true;
            deleteProjectstagetemplate({id:this.deleteData.id}).then((result) => {
                if (result.data) {
                    this.$message({
                        type:"success",
                        message:"删除成功"
                    })
                    this.loadData();
                } else {
                    this.$message({
                        type:"error",
                        message:result.msg
                    })
                    this.isLoading = false;
                }
            }).catch((err) => {
                this.isLoading = false;
            });
            this.dialogVisible = false;
        },
        addAction(){
            this.$router.push({
                path:'/projectManagement/template/add'
            })
        },
        deleteAction(data){
            this.dialogVisible = true;
            this.deleteData = data;
        },
        onEdit(data){
            this.$router.push({
                path:'/projectManagement/template/add',
                query:{
                    id:data.id,
                }
            })
        },
        handleCommand(index,data){
            switch (index) {
                case 0:// 查看明细
                    this.givedialogVisible = true;
                    this.$nextTick(()=>{
                        this.$refs.giveDialog.init(data);
                    })
                    break;
                case 1:// 回访
                    this.revisitVisible = true;
                    break;
                case 2:
                    {
                      this.$router.push({
                          path:'/clientMaintenance/give/detail',
                          query:{id:'123456'} // 机会id
                      })
                    }
                    break;
                default:
                    break;
            }

        },
        handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.loadData();
        },
        changeEnable(data){
            var par =  {
                projectStageTemplateEntity:{
                    id:data.id,
                    isEnable:data.isEnable == 1 ? false : true,
                    applicationId: sessionStorage.getItem('applicationId')
                },
            };
            this.isLoading = true;
            updateProjectstagetemplate(par).then((result) => {
                if (result.data) {
                    this.$message({
                    type:"success",
                    message:"更新成功！"
                    })
                    this.loadData();
                }else{
                    this.$message({
                    type:"error",
                    message:"保存失败！"
                    })
                    this.isLoading = false;
                }
            }).catch((err) => {
                this.isLoading = false;
            });
      },
    }
}
</script>

<style scoped>
.yesc{
    color: #333333;
}
.noc{
    color: #999999;
}
.mr20{
  margin-right: 20px;
}
.jhtable .el-button+.el-button{
  margin-left: 20px;
}
.mainbg{
  background-color: white;
  padding: 20px;
}
.pcc{
    margin: 0 auto;
    text-align: center;
}
.mt{
    margin-top: 4px;
}
.jhtable .el-button{
    padding: 2px ;
}
.mr10{
    margin-right: 10px;
}
.bbtn,.bbtn:hover,.bbtn:focus{
  color: #4285F4;
}
.rbtn,.rbtn:hover,.rbtn:focus{
  color: #F45961;
}
.right{
    text-align: right;
}

</style>