<template>
  <div>
    <div>
      <el-row class="mgtop" :gutter="8">
        <el-col :span="8" v-for="(item, index) in list" :key="index">
          <dataitem :item="item"></dataitem>
        </el-col>
      </el-row>
      <textBorder v-if="zjtype == '4'">单位总结</textBorder>
      <div class="listcss" v-if="zjtype == '4'">
        <el-row :gutter="8">
          <el-col :span="6" v-for="(item, index) in list1" :key="index">
            <dataitem2 :item="item"></dataitem2>
          </el-col>
        </el-row>
      </div>
      <div class="tablebox" v-if="zjtype == '4' && unitGoalList.length > 0">
        <div class="unitplan">
          <el-table
            class="mytable"
            v-for="(item, index) in unitGoalList"
            :key="index"
            :data="item"
            :span-method="objectSpanMethod"
            border
          >
            <el-table-column prop="unitName" label="学校">
              <template slot-scope="scope">
                <span>{{ scope.row.unitName }} </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="unitgoalTypeName"
              label="目标维度"
            ></el-table-column>
            <el-table-column prop="goalNum" label="目标" width="100">
              <template v-slot="{ row }">
                <span>{{ row.goalNum }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="finishNum" label="达成" width="100">
              <template v-slot="{ row }">
                <span>{{ row.finishNum }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dataitem from '../components/dataitem.vue'
import textBorder from '@/components/common/textBorder.vue'
import dataitem2 from '../components/dataitem2.vue'
import { personSummary } from '@/api/goal'
import { firstToUpper1, getPlanTime } from '@/utils/index.js'
export default {
  components: {
    dataitem,
    dataitem2,
    textBorder,
  },
  props: {
    layouttype: {
      type: String,
      default: '1',
    },
    zjtype: {
      type: String,
      default: '4',
    },
    zjid: {
      type: String,
      default: null,
    },
    year: {
      type: Number,
      default: null,
    },
    month: {
      type: Number,
      default: null,
    },
    week: {
      type: Number,
      default: null,
    },
  },

  data() {
    return {
      isShow: false,
      titleStr: '',
      list: [
        {
          id: 1,
          name: '新增拜访与跟进',
          goal: 0,
          finishGoal: 0,
          residueGoal: 0,
          icon: require('../../../assets/img2.png'),
          unit: '次',
          color: '#4A8BF6',
          finishRate: 0,
        },
        {
          id: 2,
          name: '新增客户',
          goal: 0,
          finishGoal: 0,
          residueGoal: 0,
          icon: require('../../../assets/img5.png'),
          unit: '个',
          color: '#F46D40',
          finishRate: 0,
        },
        {
          id: 3,
          name: '业绩',
          goal: 0,
          finishGoal: 0,
          residueGoal: 0,
          icon: require('../../../assets/img3.png'),
          unit: '万元',
          color: '#56C36E',
          finishRate: 0,
        },
        {
          id: 4,
          name: '回款金额',
          goal: 0,
          finishGoal: 0,
          residueGoal: 0,
          icon: require('../../../assets/img4.png'),
          unit: '万元',
          color: '#FF8D1A',
          finishRate: 0,
        },
        {
          id: 5,
          name: '新增合同',
          goal: 0,
          finishGoal: 0,
          residueGoal: 0,
          icon: require('../../../assets/img/hetong_icon.png'),
          unit: '个',
          color: '#4A8BF6',
          finishRate: 0,
        },
        {
          id: 6,
          name: '样书发放',
          goal: 0,
          finishGoal: 0,
          residueGoal: 0,
          icon: require('../../../assets/yang.png'),
          unit: '次',
          color: '#E85D5D',
          finishRate: 0,
        },
      ],
      isLoading: false,
      list1: [
        {
          id: 1,
          name: '信息化业绩',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img3.png'),
          unit: '万元',
          color: '#F46D40',
        },
        {
          id: 2,
          name: '教材业绩',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img4.png'),
          unit: '万元',
          color: '#E85D5D',
        },
        {
          id: 3,
          name: '合同数量',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img/hetong_icon.png'),
          unit: '个',
          color: '#4A8BF6',
        },
        {
          id: 4,
          name: '新增拜访与跟进',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img2.png'),
          unit: '次',
          color: '#4A8BF6',
        },
      ],
      unitGoalList: [],
      keyNames: {
        informationAmount: '信息化业绩（万元）',
        teachingMaterialAmount: '教材业绩金额（万元）',
        totalContract: '合同数量',
        totalVisit: '拜访次数',
      },
      defunitList: [
        [
          {
            unitName: '暂未选择学校',
            unitgoalType: 'informationAmount',
            unitgoalTypeName: '信息化业绩（万元）',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'teachingMaterialAmount',
            unitgoalTypeName: '教材业绩金额（万元）',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'totalContract',
            unitgoalTypeName: '合同数量',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'totalVisit',
            unitgoalTypeName: '拜访次数',
            goalNum: '--',
          },
        ],
      ],
      unitObj: {
        0: {
          goal: 'goalInformationAmount',
          finishGoal: 'achieveInformationAmount',
        },
        1: {
          goal: 'goalTeachingMaterialAmount',
          finishGoal: 'achieveTeachingMaterialAmount',
        },
        2: {
          goal: 'goalTotalContract',
          finishGoal: 'achieveTotalContract',
        },
        3: {
          goal: 'goalTotalVisit',
          finishGoal: 'achieveTotalVisit',
        },
      },
      tranObj: {
        0: {
          goal: 'newVisit',
          finishGoal: 'finishVisit',
          residueGoal: 'residueVisit',
          finishRate: 'rateVisit',
        },
        1: {
          goal: 'newCustomers',
          finishGoal: 'finishCustomers',
          residueGoal: 'residueCustomers',
          finishRate: 'rateCustomers',
        },
        2: {
          goal: 'newContractAmount',
          finishGoal: 'finishContractAmount',
          residueGoal: 'residueContractAmount',
          finishRate: 'rateContractAmount',
        },
        3: {
          goal: 'newContractReturnAmount',
          finishGoal: 'finishReturnAmount',
          residueGoal: 'residueReturnAmount',
          finishRate: 'rateReturnAmount',
        },
        4: {
          goal: 'newContract',
          finishGoal: 'finishContract',
          residueGoal: 'residueContract',
          finishRate: 'rateContract',
        },
        5: {
          goal: 'newMaterialDeliverNum',
          finishGoal: 'finishDeliverNum',
          residueGoal: 'residueDeliverNum',
          finishRate: 'rateDeliverNum',
        },
      },
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    format(percentage) {
      return () => {
        if (percentage.goal == 0) {
          return '完成度：**.**%'
        } else {
          return (percentage.finishRate * 100).toFixed(2) + '%'
        }
      }
    },
    getUnitGoalList(array) {
      this.unitGoalList = []
      this.unitIds = []
      var keys = [
        'informationAmount',
        'teachingMaterialAmount',
        'totalContract',
        'totalVisit',
      ]
      array.forEach((element, idx) => {
        var list = []
        this.unitIds.push(element.unitId)
        keys.forEach((key) => {
          var item = {
            id: element.id,
            index: idx,
            unitId: element.unitId,
            unitName: element.unitName,
            year: element.year,
            goalNum: element[key],
            unitgoalType: key,
            unitgoalTypeName: this.keyNames[key],
            finishNum: element['finish' + firstToUpper1(key)],
          }
          list.push(item)
        })
        this.unitGoalList.push(list)
      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (columnIndex === 0 && rowIndex === 0) {
          return {
            rowspan: 4,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    loadData() {
      let params = {
        id: this.zjid,
        goalsType: this.zjtype,
        year: this.year,
        month: this.month,
        week: this.week,
        departmentId: this.$route.query.departmentId,
      }
      this.isLoading = false
      personSummary(params)
        .then((result) => {
          this.isLoading = false
          let resultObj = result.data.goalsAchieveVo
          this.list.forEach((item, index) => {
            item.goal = resultObj[this.tranObj[index]['goal']]
            item.finishGoal = resultObj[this.tranObj[index]['finishGoal']]
            item.residueGoal = resultObj[this.tranObj[index]['residueGoal']]
            item.finishRate = resultObj[this.tranObj[index]['finishRate']]
          })
          if (this.zjtype == 4) {
            if (result.data.personalGoalsUnitList.length > 0) {
              this.getUnitGoalList(result.data.personalGoalsUnitList)
            } else {
              this.unitGoalList = this.defunitList
            }

            let goalUnitVo = result.data.goalUnitVo
            this.list1.forEach((item, index) => {
              item.goal = goalUnitVo[this.unitObj[index]['goal']]
              item.finishGoal = goalUnitVo[this.unitObj[index]['finishGoal']]
            })
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    getPercentage(item) {
      if (Object.keys(item).length <= 0) {
        return 0
      }
      if (item.goal == 0) {
        return 0
      } else if (item.finishRate * 100 >= 100) {
        return 100
      } else {
        return item.finishRate * 100
      }
    },
  },
}
</script>

<style scoped>
.mgtop {
  margin: 16px 0;
}
.plantitle {
  height: 30px;
  width: 100%;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
  line-height: 23px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.tablebox {
  height: 290px;
  overflow-y: auto;
  margin-bottom: 20px;
}
.mainbg {
  display: flex;
  background-color: #333333;
}
.ldiv {
  width: 80%;
  background-color: white;
}
.namecss {
  color: #999;
}
.progress /deep/.el-progress-bar__outer {
  height: 4px !important;
  background-color: #dfeafd;
}
.progress /deep/.el-progress-bar__inner {
  background-color: #5a98ff;
}
.progress /deep/.el-progress-bar {
  margin-right: 0px;
  padding-right: 0;
}
.progress /deep/.el-progress__text {
  position: absolute;
  right: 0;
  top: -16px;
}
.itemcss {
  width: 200px;
  margin-right: 8px;
}
.zm {
  zoom: 0.8;
}
.listcss {
  /* display: flex;
        align-items: center; */
  width: 100%;
  margin-bottom: 20px;
}
.mainbg {
  height: 100%;
  overflow-y: auto;
  border-radius: 8px;
  margin: 16px 0px;
  padding: 20px 16px;
  background-color: white;
}

.timecss {
  margin-top: 8px;
  text-align: right;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #cccccc;
}
.titlefl {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.w210 {
  width: 210px;
}
.width100 {
  width: 80%;
}
.textcss {
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
  margin: 4px 0px;
}
.progress /deep/.el-progress-bar__outer {
  height: 4px !important;
  background-color: #dfeafd;
}
.progress /deep/.el-progress-bar__inner {
  background-color: #5a98ff;
}
.progress /deep/.el-progress-bar {
  margin-right: 0px;
}
.inblock {
  display: inline-block;
}
.mt50 {
  margin-top: 50px;
}
.goalitem {
  height: 72px;
  line-height: 72px;
  margin-bottom: 20px;
}
.ml {
  margin-left: 3px;
}
.mt {
  margin-top: 5px;
}
.goalcss {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 16px;
}
.finishGoalcss {
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
  color: #f46d40;
  line-height: 28px;
}
.pl12 {
  padding-left: 12px;
}
.flex {
  display: flex;
  align-items: center;
}
.imgcss {
  width: 3em;
  height: 3em;
  margin-left: 1.5vw;
}
.typename {
  padding-top: 20px;
  margin-left: 20px;
  margin-bottom: 1.25em;
  font-size: 1em;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.dataitem {
  background: #ffffff;
  box-shadow: 0px 2px 16px 0px rgba(15, 27, 50, 0.08);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}

.titlecss {
  margin-top: 20px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  line-height: 23px;
}
.bg {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  background-color: white;
  border-radius: 8px;
}
</style>
