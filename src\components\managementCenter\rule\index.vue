<template>
  <div>
    <div class="mainbg" v-loading="isLoading">
      <el-form :inline="true" label-width="85px" class="myform clearfix">
        <el-form-item label="">
          <el-input
            v-model="pageBean.departmentName"
            class="definput inputWid150"
            clearable
            placeholder="部门名称"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="规则类型：">
          <el-select
            v-model="pageBean.ruleType"
            class="definput inputWid150"
            popper-class="removescrollbar"
            clearable
            placeholder="请选择规则类型"
          >
            <el-option
              v-for="item in ruleTypeStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            @click="search"
            class="defaultbtn mt"
            icon="el-icon-search"
            type="primary"
            >搜索</el-button
          >
        </el-form-item>
        <el-form-item class="fr">
          <el-dropdown @command="commandClick">
            <el-button v-isShow="'crm:business:processrule:save'" class="defaultbtn mt" type="primary"
              >新增规则<i class="el-icon-arrow-down el-icon--right"></i
            ></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">合同开票</el-dropdown-item>
              <el-dropdown-item :command="2">合同回款</el-dropdown-item>
              <el-dropdown-item :command="3">合同退款</el-dropdown-item>
              <el-dropdown-item :command="4">教材报订</el-dropdown-item>
              <el-dropdown-item :command="5">成本审核</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
      </el-form>
      <el-table class="mytable" :data="tableData" style="width: 100%">
        <el-table-column prop="ruleType" label="规则类型" align="center" width="167px">
          <template slot-scope="scope">
            <span type="success">{{
              ruleTypeStatusMap[scope.row.ruleType]
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="departments" label="适用部门" align="center" min-width="167" />
        <el-table-column
          prop="oneLevelUserName"
          label="一级审核人"
          width="167"
          align="center"
        />
        <el-table-column
          prop="twoLevelUserName"
          label="二级审核人"
          align="center"
          width="167"
          :formatter="formatter"
        />
        <el-table-column
          prop="threeLevelUserName"
          label="三级审核人"
          align="center"
          width="167"
          :formatter="formatter"
        />
        <el-table-column prop="createTime" label="创建时间" align="center" width="167" />
        <el-table-column prop="createByName" label="创建人" align="center" width="167"/>

        <el-table-column
          prop="edit"
          width="120"
          align="center"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              class="bbtn tabbtn"
              v-isShow="'crm:business:processrule:update'"
              :disabled="getDisable(scope.row)"
              type="text"
              @click="editHandle(scope.row)"
            >
              编辑</el-button
            >
            <el-button
              class="bbtn tabbtn rbtn"
              type="text"
              :disabled="getDisable(scope.row)"
              v-isShow="'crm:business:processrule:delete'"
              @click="deleteHandle(scope.row)"
            >
              删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="fixpage">
        <page
          :currentPage="pageBean.pageNum"
          :total="total"
          :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"
        ></page>
      </div>
    </div>
  </div>
</template>

<script>
import page from '@/components/common/page'
import { ruleTypeStatus, ruleTypeStatusMap } from '@/utils/status-tool'
import { ruleList, ruleDelete } from '@/api/managementCenter/index'
export default {
  components: {
    page,
  },
  data() {
    return {
      isLoading: false,
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        departmentName: '',
        ruleType: '',
      },
      tableData: [],
      ruleTypeStatus,
      ruleTypeStatusMap,
    }
  },
  created() {
    this.loadData()
  },

  methods: {
    formatter(row, column, cellValue, index){
      return cellValue ? cellValue :'--'
    },
    getDisable(row){
      const dataScope = sessionStorage.getItem('dataScope')
      const userId = sessionStorage.getItem('userid')
      if (dataScope !=4 && userId != row.createBy) {
        return true
      }

      return false
    },
    deleteHandle(row) {
      ruleDelete({ id: row.id }).then((result) => {
        if (result.data) {
          this.$message({
            type: 'success',
            message: '删除成功',
          })
          this.loadData()
        } else {
          this.$message({
            type: 'error',
            message: result.msg,
          })
        }
      })
    },
    editHandle(row) {
      this.$router.push({
        path: '/managementCenter/rule/add',
        query: {
          ruleType: row.ruleType,
          id: row.id,
        },
      })
    },
    commandClick(value) {
      this.$router.push({
        path: '/managementCenter/rule/add',
        query: {
          ruleType: value,
        },
      })
    },
    search() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(val) {
      this.pageBean.pageNum = val
      this.loadData()
    },
    loadData() {
      this.isLoading = true
      ruleList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    addRule() {
      this.$router.push('/managementCenter/rule/add')
    },
  },
}
</script>
<style lang="scss" scoped>
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: 100%;
  border-radius: 10px;
}
.bbtn {
  cursor: pointer;
}
</style>
