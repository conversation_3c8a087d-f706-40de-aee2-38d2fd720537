<template>
  <div>
    <div class="mb20">
      <span class="ttext deffont">物品数量：<span class="deffont mr10">{{distributionNumber}}</span></span>
      <div class="right flex">
        <el-button class="defaultbtn" icon="el-icon-plus" type="primary" @click="addAction">添加发货</el-button>
        <el-upload class="m20" :action="getUrl" :show-file-list="false" :before-upload="beforeUpload"
          :on-success="handleAvatarSuccess" :on-error="handleError" :headers="headers" :data="fileData" accept=".xlsx">
          <el-button class=" defaultbtn " icon="el-icon-my-inport" type="primary" :loading="isImport">导入发货单</el-button>
        </el-upload>
        <el-button class="defaultbtn" icon="el-icon-my-download" type="primary" @click="downloadAction"
          :loading="isDownload">下载模版</el-button>
      </div>
    </div>
    <el-table :height="600" class="mytable" :data="dataList" style="width:100%" v-loading="isLoading">
      <el-table-column property="teachingMaterial" label="教材名称" align="center" min-width="200px"></el-table-column>
      <el-table-column property="publish" label="出版社" align="center" min-width="200px"></el-table-column>
      <el-table-column property="isbn" label="书号" align="center" width="167px"></el-table-column>
      <el-table-column property="trackingNumber" label="物流单号" align="center" width="167px"></el-table-column>
      <el-table-column property="price" label="价格" align="center" width="100px"></el-table-column>
      <el-table-column property="number" label="数量" align="center" width="100px"></el-table-column>
      <el-table-column property="discount" label="折扣" align="center" width="100px"></el-table-column>
      <el-table-column property="deliveryTime" label="发货时间" align="center" width="167px"></el-table-column>
      <el-table-column property="edit" width="150px" label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <div class="rbtn deffont" @click="deleteAction(scope.row)">移除</div>
        </template>
      </el-table-column>
      <template slot="empty">
        <img class="nodataimgcss" src="../../../assets/img/nodata_icon.png" alt="" srcset="">
        <p>
          暂无数据～
        </p>
      </template>
    </el-table>
    <page :currentPage="pageBean.pageNum" :pageSize="pageBean.pageSize" :total="total"
      @updatePageNum="handleCurrentChange"></page>
    <addInvoiceDialog :visible.sync="dialogVisible" :contractId="pageBean.contractId" @updateVisible="updateVisible"
      @submitSuccess="submitSuccess"></addInvoiceDialog>
    <!-- 导入提示 -->
    <dc-dialog iType="2" title="导入信息提示" width="500px" :showCancel="false" :dialogVisible.sync="dialogMsgVisible"
      @submit="submitMsgDialog" :appendToBody="true">
      <template>

      </template>
      <p class="pcc" v-for="(item,index) in errorData" :key="index">{{item}}</p>
    </dc-dialog>
  </div>
</template>

<script>
  import page from '../../common/page.vue';
  import addInvoiceDialog from './components/addInvoiceDialog.vue';
  import { contractinvoiceList, contractinvoiceQueryOverview, deleteContractinvoice, contractInvoiceDownloadTemplate } from '@/api/contract'
  import { downloadExcelFile } from "@/utils/tools";
  import { getToken } from '@/utils/auth'
  export default {
    components: {
      page,
      addInvoiceDialog
    },
    data() {
      return {
        dialogMsgVisible: false,
        errorData: [],
        distributionNumber: 0,
        dialogVisible: false,
        isImport: false,
        isDownload: false,
        getUrl: `${process.env.VUE_APP_BASE_API}/crm/controller/contractinvoice/contractInvoicebatchImportExcel`,
        headers: { Authorization: getToken() },
        fileData: {
          applicationId: sessionStorage.getItem('applicationId'),
          contractId: this.$route.query.id,
          serviceName: "crm/contractinvoice",
        },
        dataList: [],
        isLoading: false,
        pageBean: {
          contractId: this.$route.query.id,
          pageNum: 1,
          pageSize: 10,
        },
        total: 0
      }
    },
    created() {
      this.loadTotalData();
      this.loadData();
    },
    methods: {
      loadData() {
        this.isLoading = true;
        contractinvoiceList(this.pageBean).then((result) => {
          this.isLoading = false;
          this.dataList = result.data;
          this.total = result.page.total;
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      loadTotalData() {
        contractinvoiceQueryOverview({ contractId: this.$route.query.id }).then((result) => {
          this.distributionNumber = result.data.distributionNumber;
        }).catch((err) => {

        });
      },
      deleteAction(data) {
        this.isLoading = true;
        deleteContractinvoice({ id: data.id }).then((result) => {
          this.isLoading = false;
          if (result.data) {
            this.$message({
              type: "success",
              message: '删除成功',
            })
            this.loadData();
            this.loadTotalData();
          } else {
            this.$message({
              type: "error",
              message: result.msg,
            })
          }
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      //  上传发货记录
      beforeUpload(file) {
        const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
        const whiteList = ['xlsx'];
        if (whiteList.indexOf(fileSuffix) === -1) {
          this.$message.error('导入明细仅支持 .xlsx 格式!');
          return false
        }
        this.isImport = true;
      },
      handleError(file, res) {
        this.isImport = false;
      },
      handleAvatarSuccess(res, file) {
        if (res.status == 0 && res.data.errorCount <= 0) {
          // 成功
          this.$message({
            type: 'success',
            message: res.msg
          })
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          })
          // 显示错误提示的弹框
          this.dialogMsgVisible = true;
          this.errorData = res.data.errorData;
        }
        // 刷新数据
        this.loadData();
        this.loadTotalData();
        this.isImport = false;
      },
      submitMsgDialog() {
        this.dialogMsgVisible = false;
        this.errorData = [];
      },
      addAction() {
        this.dialogVisible = true;
      },
      updateVisible(val) {
        this.dialogVisible = val;
      },
      submitSuccess() {
        // 注入合同id
        this.updateVisible(false);
        this.pageBean.pageNum = 1;
        this.loadData();
        this.loadTotalData();

      },
      downloadAction() {
        this.isDownload = true;
        contractInvoiceDownloadTemplate().then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: result.msg
            })
          } else {
            downloadExcelFile(result, '发货记录模版.xlsx')
          }

          this.isDownload = false;
        }).catch((err) => {
          this.isDownload = false;
        });
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.loadData();
      }
    }
  }
</script>

<style scoped>
  .m20 {
    margin: 0 20px;
  }

  .nodataimgcss {
    margin-bottom: 12px;
    width: 180px;
    height: 92px;
  }

  .mytable /deep/.el-table__empty-text {
    line-height: 20px !important;
    min-height: 20px !important;
  }

  .width100 {
    width: 100%;
  }

  .submitbtn {
    margin-top: 50px;
  }

  .flex {
    display: flex;
    align-items: center;
  }

  .lh {
    width: 100%;
    line-height: 23px;
  }

  .ttext {
    color: #999999;
    line-height: 34px;
  }

  .center {
    text-align: center;
  }

  .bbtn {
    color: #4285F4;
    cursor: pointer;
  }

  .rbtn {
    color: #F45961;
    cursor: pointer;
  }

  .revisitcss {
    line-height: 34px;
    margin: 0px 80px;
  }

  .revisitcss /deep/.el-form-item {
    margin-bottom: 18px;
    /* padding-right: 60px; */
  }

  .ml20 {
    margin-left: 20px;
  }

  .givedialog /deep/.el-dialog__body {
    padding: 20px;
    padding-top: 0px;
  }

  .givedialog /deep/.el-dialog__header {
    border: none;
  }
</style>
<style scoped>
  .tabscss /deep/.el-tabs__item {
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 40px;
    width: 240px !important;
    text-align: center;
    line-height: 40px;
    /* padding: 0 60px; */
  }
</style>