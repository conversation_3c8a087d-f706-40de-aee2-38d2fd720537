<!-- 权限管理 -->
<template>
  <div>
    <el-dialog title="权限分配" :close-on-click-modal="false" :visible.sync="visible" width="60%">
      <!-- 树型表格 -->
      <section>
        <el-table
          :data="copyOriginData"
          border
          style="width: 100%;"
          ref="multipleTable"
          @selection-change="handleSelectValue"
          @select="selectionChange"
          :row-key="getRowKeys"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
        >
          <el-table-column
            prop="id"
            type="selection"
            header-align="center"
            align="center"
            width="50"
          ></el-table-column>
          <el-table-column
          el-table-tree-column
          label="资源名称"
          fixed="left"
          width="200"
          header-align="center"
          prop="name"></el-table-column>
          <el-table-column prop="sType" label="资源类型" header-align="center" align="center">
            <template slot-scope="scope">
              <el-tag type="danger" size="mini" v-if="scope.row.sType==0">目录</el-tag>
              <el-tag type="success" size="mini" v-if="scope.row.sType==1">菜单</el-tag>
              <el-tag type="warning" size="mini" v-if="scope.row.sType==2">按钮</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="资源状态" header-align="center" align="center">
            <template slot-scope="scope">
              <el-tag type="success" size="mini" v-if="scope.row.status==0">正常</el-tag>
              <el-tag type="info" size="mini" v-if="scope.row.status==1">隐藏</el-tag>
              <el-tag type="warning" size="mini" v-if="scope.row.status==2">删除</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </section>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit()">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      copyOriginData: [],
      selectArray: [],
      target_permission: [],
      visible: true,
      positionId: ""
    };
  },
  methods: {
    // element table如果是树形结构 需要加row-key
    getRowKeys(row) {
      return row.id;
    },
    // 判断checkbox是否选中
    selectionChange(selection, row) {
      let selectd = selection.length && selection.indexOf(row) !== -1;
      if (selectd) {
        if (row.sType === 0) {
          // 一级
          this.level1Fun(row, true);
        } else if (row.sType === 1) {
          this.level2Fun(row, true);
        } else if (row.sType === 2) {
          this.level3Fun(row, true);
        }
      } else {
        if (row.sType === 0) {
          // 一级
          this.level1Fun(row, false);
        } else if (row.sType === 1) {
          this.deleteLevel2Fun(row, false);
        } else if (row.sType === 2) {
          this.level3Fun(row, false);
        }
      }
    },

    // 一级
    level1Fun(ary, flag) {
      this.copyOriginData.forEach((item, key) => {
        if (item.id === ary.id) {
          if (item.children && item.children.length > 0) {
            for (let j = 0; j < item.children.length; j++) {
              this.$refs.multipleTable.toggleRowSelection(
                this.copyOriginData[key].children[j],
                flag
              );
              let level2ary = item.children[j];
              if (level2ary.children && level2ary.children.length > 0) {
                for (let k = 0; k < level2ary.children.length; k++) {
                  this.$refs.multipleTable.toggleRowSelection(
                    this.copyOriginData[key].children[j].children[k],
                    flag
                  );
                }
              }
            }
          }
        }
      });
    },

    // 二级取消
    deleteLevel2Fun(ary, flag) {
      // let select = this.selectArray;
      // let saveId = [];
      // this.copyOriginData.forEach((item, key) => {
      //   if (ary.parentId === item.id) {
      //     saveId.push(item.id)
      //     if (item.children && item.children.length > 0) {
      //       for (let i = 0; i < item.children.length; i++) {
      //         if (!saveId.includes(item.children[i])) { saveId.push(item.children[i].id) };
      //         }
      //         for (let j=0; j<select.length; j++) {
      //           if (saveId.includes(select[j].id)) {
      //             this.$refs.multipleTable.toggleRowSelection(
      //               this.copyOriginData[key],
      //               true
      //             );
      //           } else {
      //             this.$refs.multipleTable.toggleRowSelection(
      //               this.copyOriginData[key],
      //               false
      //             );
      //           }
      //         }
      //     }
      //   }
      // })
    },
    // 二级
    level2Fun(ary, flag) {
      this.copyOriginData.forEach((item, key) => {
        if (ary.parentId === item.id) {
          if (item.children && item.children.length > 0) {
            for (let j = 0; j < item.children.length; j++) {
              if (ary.id === item.children[j].id) {
                this.$refs.multipleTable.toggleRowSelection(this.copyOriginData[key], flag);
                let level2ary = this.copyOriginData[key].children[j];
                if (level2ary.children && level2ary.children.length > 0) {
                  for (let k = 0; k < level2ary.children.length; k++) {
                    this.$refs.multipleTable.toggleRowSelection(this.copyOriginData[key].children[j].children[k], flag);
                  }
                }
              }
            }
          }
        }
      });
    },

    // 取以上按钮
    selectType(ary, flag) {
      this.copyOriginData.forEach((item, key) => {
        if (ary.parentId === item.id) {
          if (item.children && item.children.length > 0) {
            this.$refs.multipleTable.toggleRowSelection(this.copyOriginData[key], flag);
            for (let j = 0; j < item.children.length; j++) {
              if (item.children[j].id === ary.id) {
                this.$refs.multipleTable.toggleRowSelection(this.copyOriginData[key].children[j], flag);
              }
            }
          }
        }
      });
    },

    // 三级
    level3Fun(ary, flag) {
      this.copyOriginData.forEach((item, key) => {
        if (item.children && item.children.length > 0) {
          for (let i = 0; i < item.children.length; i++) {
            if (ary.parentId === item.children[i].id) {
              this.selectType(item.children[i], true);
            }
          }
        }
      });
    },

    // 选中每个按钮
    handleSelectValue(val) {
      this.selectArray = val;
    },

    // 遍历传入 返回id
    mapSelectIdFun(ary) {
      let resource = "";
      if (ary instanceof Array) {
        ary.filter(item => {
          resource += "," + item.id;
        });
      }
      return resource.substring(1);
    },

    // 提交
    async handleSubmit() {
      if (this.selectArray.length <= 0) {
        this.$message.error("请分配权限！");
        return false;
      }
      let parames = {
        positionId: this.positionId,
        resourceIds: ""
      };
      parames.resourceIds = this.mapSelectIdFun(this.selectArray);
      // let parames = {
      //   positionId: this.positionId,
      //   resourceIds: '42798086384266628,42798086384266629,42798086384266632,42798086384266633,42798086384266634,42798086384266635,42798086384266632,42798086384266633,42798086384266634,104490996624167616,104509860120532032,104509860120532033'
      // }
      let res = await this.$axios.post('/sf/business/companypositionresource/update', parames)
      if (res.status === 0) {
        setTimeout(() => {
          this.$message.success('操作成功');
          this.visible = false
        }, 300);
      }
    },
    // 处理回填数据
    // async splitFun (id) {
    //   let data = { positionId: id }
    //   let res = await this.$axios.get('/sf/business/companypositionresource/listAll', { params: data })
    //   if (res.status === 0) {
    //     return res.data
    //   }
    // },
    // 回填权限数据
    async backfillData(id) {
      let data = { positionId: id }
      let res = await this.$axios.get('/sf/business/companypositionresource/listAll', { params: data })
      if (res.status === 0) {
        let ary = res.data;
        let originary = [];
        for (let p = 0; p < ary.length; p++) {
          originary.push(ary[p].resourceId)
        }
        // 模拟回填数据
        // let originobj = '104509860120532032,104490996624167616,42798086384266631,42798086384266628,42798086384266629';
        // let originary = originobj.split(",");
        this.$nextTick(() => {
          this.copyOriginData.forEach((item, key) => {
            for (let i = 0; i < originary.length; i++) {
              if (item.id === originary[i]) {
                this.$refs.multipleTable.toggleRowSelection(this.copyOriginData[key], true);
              }
              if (item.children && item.children.length > 0) {
                for (let j = 0; j < item.children.length; j++) {
                  if (item.children[j].id === originary[i]) {
                    this.$refs.multipleTable.toggleRowSelection(this.copyOriginData[key].children[j], true);
                  }
                  let child = item.children[j]
                  if (child.children && child.children.length > 0) {
                    for (let m = 0; m < child.children.length; m++) {
                      if (child.children[m].id === originary[i]) {
                        this.$refs.multipleTable.toggleRowSelection(this.copyOriginData[key].children[j].children[m], true);
                      }
                    }
                  }
                }
              }
            }
          });
        });
      }
    },
    init(id) {
      this.visible = true;
      this.positionId = id;
      this.copyOriginData = JSON.parse(
        JSON.stringify(this.$store.state.sys.menus)
      );
      this.backfillData(this.positionId);
    }
  }
};
</script>
