<template>
  <div>
    <div class="formdiv">
        <back>返回</back>
        <span>
            <el-select class="definput right ml16 w210" popper-class="removescrollbar" v-model="time" @change="changeTime"  placeholder="请选择时间维度">
                <el-option 
                v-for="item in options1" 
                :key="item.id" 
                :label="item.name" 
                :value="item.id">
                </el-option>
            </el-select> 
            <el-select class="definput right ml16 w210"  popper-class="removescrollbar" v-model="isDep"  placeholder="请选择查看范围">
                <el-option 
                v-if="userType == 4"
                :key="''" 
                :label="'全公司'" 
                @click.native="changeRange"
                :value="''">
                </el-option>
                <el-option 
                @click.native="chooseDepartment"
                :key="'1'" 
                :label="'选部门'" 
                :value="'1'">
                </el-option>
            </el-select>
        </span>
        
    </div>
    <div class="mainbg" v-loading="isLoading">
        <el-row :gutter="12">
            <el-col :span="8" v-for="(item,index) in list" :key="index">
                <panel-item :ref="'panel'+ item.goalType"></panel-item>
            </el-col> 
        </el-row>
    </div>
    <!-- 选部门 -->
    <deDialog 
    ref="deRef"
    :dType="dType"
    :visible.sync="dialogDepVisible"
    @updateVisible="updateSystemVisible"
    @submitData="submitData">
    </deDialog>
  </div>
  
</template>

<script>
import back from '../../common/back.vue';
import PanelItem from './components/panelItem.vue';
import deDialog from '@/components/common/deDialog.vue';
import { achieveGoalsSummary } from "@/api/goal";
export default {
    components:{
        back,
        PanelItem,
        deDialog
    },
    data(){
        return{
            isLoading:false,
            dialogDepVisible:false,
            dType:'1',
            time:3,
            isDep:"",
            departmentIds:[],
            userType:sessionStorage.getItem('dataScope'),
            list:[],
             options1:[
                {id:1,name:'本月'},
                {id:2,name:'本季度'},
                {id:3,name:'本年度'},
                {id:4,name:'上月'},
                {id:5,name:'上季度'},
                {id:6,name:'上年度'}
            ],
            options2:[
                {id:'',name:'全部'},
                {id:"1",name:'已完成'},
                {id:"2",name:'未完成'}
            ],
            options3:[
                {id:1,name:'全公司'},
                {id:2,name:'看自己'},
                {id:3,name:'看部门'},
                {id:4,name:'选员工'}
            ],
        }
    },
    created(){
        this.loadData();
    },
    methods:{
        loadData(){
            var par = {
                time:this.time,
                departmentIds:this.departmentIds
            }
            this.isLoading = true;
            achieveGoalsSummary(par).then((result) => {
                this.isLoading = false;
                this.list = result.data;
                this.$nextTick(()=>{
                    this.list.forEach(element => {
                        const refname = 'panel' + element.goalType;
                        this.$refs[refname][0].setPanelData(element);
                        
                    });
                })
                
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        changeRange(){
            this.departmentIds = [];
            this.loadData();
        },
        chooseDepartment(){
            this.updateSystemVisible(true)
            this.$refs.deRef.loadData()
        },
        submitData(data){
            this.updateSystemVisible(false);
            this.departmentIds = data;
            this.loadData();
        },
        updateSystemVisible(val){
            this.dialogDepVisible = val;
        },
        changeTime(data){
            this.time = data;
            this.loadData();
        },
    }
}
</script>
<style scoped>
.ml16{
    margin-left: 16px !important;
}
.formdiv{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}
.w210{
    width: 210px;
}
.mainbg{
    height: calc(100vh - 135px);
    border-radius: 8px;
}
</style>
<style>

</style>