<template>
    <div class="bgwhite">
        <el-form label-width="85px" class="myform ">
            <el-row :gutter="60" type="flex" justify="space-between">
                <el-col :span="6">
                    <el-form-item label="协作负责：">
                        <el-select clearable="" class="definput" popper-class="removescrollbar" v-model="selectValue"
                            placeholder="请选择">
                            <el-option v-for="item in optionsResponsible" :key="item.value" :label="item.name"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="项目状态：">
                        <el-select clearable class="definput" popper-class="removescrollbar"
                            v-model="pageBean.projectStatus" placeholder="请选择">
                            <el-option v-for="item in optionsStatus" :key="item.value" :label="item.name"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="项目名称：">
                        <el-input clearable v-model="pageBean.projectName" class="definput" placeholder="请输入">
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="客户名称：">
                        <el-input clearable v-model="pageBean.customerName" class="definput" placeholder="请输入">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="60" type="flex" justify="space-between">
                <el-col :span="6">
                    <el-form-item label="客户单位：">
                        <el-input clearable v-model="pageBean.unitName" class="definput" placeholder="请输入"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="负责人：">
                        <el-input clearable v-model="pageBean.chargePersonName" class="definput" placeholder="请输入">
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-button @click="search" class="defaultbtn mt" icon="el-icon-search" type="primary">搜索</el-button>
                </el-col>
                <el-col :span="6" class="right">
                    <el-dropdown placement="bottom-end" @command="e => handleCommandAdd(e)" trigger="click">
                        <el-button class="defaultbtn mt" v-isShow="'crm:controller:project:save'" icon="el-icon-plus"
                            type="primary">新建项目</el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item :command="0">新建</el-dropdown-item>
                            <el-dropdown-item :command="1" v-isShow="'crm:controller:projectstagetemplate:list'">模板新建
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </el-col>
            </el-row>
        </el-form>
        <el-table class="customertable mytable teshutable" height="590px" :data="tableData" style="width: 100%"
            v-loading="isLoading">
            <el-table-column label="项目名称" width="227">
                <template slot-scope="scope">
                    <span class="cusnamecss">
                        <span class="smt">
                            <div v-if="scope.row.chargePerson == userId" class="tagcss fuze"><span
                                    class="smtext">负责</span>
                            </div>
                            <div v-if="scope.row.collaborator.split(',').includes(userId) &&  scope.row.chargePerson != userId "
                                class="tagcss xiezuo"> <span class="smtext">协作</span></div>
                        </span>
                        <span>
                            <img v-if="scope.row.top" class="zhiding" src="../../../assets/img/zhiding_icon.png" alt="">
                            <span class="protext" v-if="scope.row.projectName.length<14">{{ scope.row.projectName
                                }}</span>
                            <el-tooltip v-else class="item" effect="dark" :content="scope.row.projectName"
                                placement="top-start">
                                <span :style="computedCss(scope.row)" class="protext">{{ scope.row.projectName }}</span>
                            </el-tooltip>
                        </span>
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="customerName" label="客户名称" width="167px" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="unitName" label="客户单位" width="167px" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="projectStatusName" label="项目状态" width="120px">
                <template slot-scope="scope">
                    <span :class="statusCss(scope.row)"> {{ scope.row.projectStatusName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="beginTime" label="开始日期" width="120px">
            </el-table-column>
            <el-table-column prop="endTime" label="结束日期" width="120px">
            </el-table-column>
            <el-table-column prop="projectAmount" label="项目金额(万元)" width="120px">
            </el-table-column>
            <el-table-column prop="chargePersonName" label="负责人" width="120px">
            </el-table-column>
            <el-table-column prop="collaboratorName" label="协作人" width="167px" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="167px">
            </el-table-column>
            <el-table-column prop="edit" width="167px" fixed="right" label="更多操作">
                <template slot-scope="scope">
                    <el-dropdown placement="bottom-end" @command="e => handleCommand(e, scope.row)" trigger="click">
                        <el-button class="bbtn mr10" type="text">
                            更多
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item :command="0">阶段任务管理</el-dropdown-item>
                            <el-dropdown-item :command="1" v-isShow="'crm:controller:project:info'">详情
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-button :disabled="!scope.row.isOperate" class="bbtn " v-isShow="'crm:controller:project:update'"
                        type="text" @click="onEdit(scope.row)"> 编辑</el-button>
                    <el-button :disabled="!scope.row.isOperate" class="rbtn " v-isShow="'crm:controller:project:delete'"
                        type="text" @click="deleteAction(scope.row)"> 删除</el-button>
                </template>

            </el-table-column>
            <template slot="empty">
                <nolist></nolist>
            </template>
        </el-table>
        <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"></page>
        <dc-dialog iType="1" title="温馨提示" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
            <p class="pcc">确定要删除项目么?</p>
        </dc-dialog>
        <drawer @loadData="loadData" ref="prodetailRef" @changeDrawer="changeDrawer" :drawer="drawer"></drawer>
    </div>
</template>

<script>
    import page from '../../common/page.vue';
    import drawer from './component/drawer.vue'
    import nolist from '../../common/nolist.vue';
    import { projectList, projectDelete } from '@/api/project'

    export default {
        components: {
            page,
            drawer,
            nolist
        },
        data() {
            return {
                drawer: false,
                direction: 'rtl',
                unitArr: [
                    {
                        value: "0",
                        name: '全部',
                    },
                    {
                        value: "1",
                        name: '我协作的',
                    },
                    {
                        value: "2",
                        name: '我负责的',
                    }],
                typeToValue: {
                    1: "collaborator",
                    2: 'chargePerson'
                },
                dialogVisible: false,
                optionsResponsible: [
                    {
                        value: "0",
                        name: '全部',
                    },
                    {
                        value: "1",
                        name: '我协作的',
                    },
                    {
                        value: "2",
                        name: '我负责的',
                    }
                ],
                optionsStatus: [
                    {
                        value: "0",
                        name: '全部',
                    },
                    {
                        value: "1",
                        name: '未开始',
                    },
                    {
                        value: "2",
                        name: '进行中',
                    },
                    {
                        value: "3",
                        name: '已完成',
                    },
                ],
                isLoading: false,
                tableData: [

                ],
                total: 100,
                pageBean: {
                    pageNum: 1,
                    pageSize: 10,
                    projectName: '',
                    projectStatus: '',
                    customerName: '',
                    unitName: '',
                    chargePersonName: ''
                },
                selectValue: '',
                userId: '',
                statusToCss: {
                    1: 'nobegin',
                    2: 'doing',
                    3: 'end'
                },
                dId: ''
            }
        },
        created() {
            this.projectListData()
            this.userId = sessionStorage.getItem("userid") || ''
        },
        computed: {
            computedCss() {
                return function (row) {
                    let topWidth = row.top ? 35 : 10
                    let tagWidth = (row.chargePerson == this.userId || row.collaborator.split(',').includes(this.userId)) ? 42 : 10
                    let width = 227 - topWidth - tagWidth
                    return {
                        'width': width + 'px'
                    }
                }
            },
            statusCss() {
                return (row) => {
                    return [this.statusToCss[row.projectStatus]]
                }
            }
        },
        methods: {
            loadData() {
                this.projectListData()
            },
            search() {
                this.projectListData()
            },
            projectListData() {
                if (this.selectValue == 0) {
                    this.pageBean.chargePerson && delete this.pageBean.chargePerson
                    this.pageBean.collaborator && delete this.pageBean.collaborator
                }
                if (this.selectValue == 1) {
                    this.pageBean.chargePerson && delete this.pageBean.chargePerson
                    this.pageBean[this.typeToValue[this.selectValue]] = sessionStorage.getItem("userid") || ''
                }
                if (this.selectValue == 2) {
                    this.pageBean.collaborator && delete this.pageBean.collaborator
                    this.pageBean[this.typeToValue[this.selectValue]] = sessionStorage.getItem("userid") || ''
                }
                this.isLoading = true;
                projectList(this.pageBean).then(res => {
                    this.isLoading = false;
                    if (res.status == 0) {
                        this.tableData = res.data
                        this.total = res.page.total
                    }
                }).catch((err) => {
                    this.isLoading = false;
                });
            },
            changeDrawer(v) {
                this.drawer = v
            },
            submitDialog() {
                this.dialogVisible = false;
                projectDelete({ id: this.dId }).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: "删除成功"
                        })
                        this.projectListData();
                    } else {
                        this.$message({
                            type: "error",
                            message: result.msg
                        })
                    }
                }).catch((err) => {

                });
            },
            deleteAction(data) {
                this.dId = data.id
                this.dialogVisible = true;
            },
            toDetail(data) {
                this.$router.push({
                    path: '/clientManagement/customerDetail',
                    query: { id: data.name }
                })
            },
            onEdit(data) {
                this.$router.push({
                    path: '/projectManagement/project/add',
                    query: {
                        id: data.id,
                    }
                })
            },
            handleCommandAdd(index) {
                var rpath = ""
                switch (index) {
                    case 0:
                        rpath = "/projectManagement/project/add"
                        break;
                    case 1:
                        rpath = "/projectManagement/template/index"
                        break;
                    default:
                        break;
                }
                this.$router.push({
                    path: rpath,
                })
            },
            handleCommand(index, data) {
                var rpath = ""
                switch (index) {
                    case 0:
                        rpath = "/projectManagement/stage/index"
                        break;
                    case 1:
                        // this.drawer = true
                        this.$refs.prodetailRef.setProId(data.id)
                        break;
                    default:
                        break;
                }
                if (index == 0) {
                    this.$router.push({
                        path: rpath,
                        query: { id: data.id }
                    })
                }

            },
            handleCurrentChange(page) {
                this.pageBean.pageNum = page;
                this.projectListData()
            }
        }
    }
</script>

<style scoped lang="scss">
    .smt {
        margin-top: 19px;
    }

    .protext {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #4285F4;
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #409eff;
    }


    .pcc {
        margin: 0 auto;
        text-align: center;
    }

    .smtext {
        zoom: 0.8;
    }

    .fxcenter {
        width: 50px;
        height: 52px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .zhiding {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        /* background-color: red; */
        display: inline-block;
        margin-top: 16px;
        vertical-align: top;
    }

    .mt {
        margin-top: 4px;
    }

    .cusnamecss {
        display: flex;
    }

    .tagcss {
        font-size: .625em !important;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        height: 14px;
        min-width: 30px;
        line-height: 11px;
        border-radius: 2px;
        margin-right: 5px;
        text-align: center;
    }

    .genjin {
        color: #4285F4;
        background-color: #DFEAFD;
    }

    .fuze {
        color: #FEF2E7;
        background-color: #FF8D1A;
        margin-bottom: 5px;
    }

    .xiezuo {
        color: #56C36E;
        background-color: #F3FEF6;
    }

    .tagcss:nth-child(2n+2) {
        margin-top: 4px;
    }

    .customertable /deep/.cell {}

    .customertable .el-button {
        padding: 2px;
    }

    .mr10 {
        margin-right: 10px;
    }

    .bbtn,
    .bbtn:hover,
    .bbtn:focus {
        color: #4285F4;
    }

    .rbtn,
    .rbtn:hover,
    .rbtn:focus {
        color: #F45961;
    }

    .right {
        text-align: right;
    }
</style>