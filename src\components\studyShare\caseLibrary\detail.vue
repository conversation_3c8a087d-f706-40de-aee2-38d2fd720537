<template>
  <div class="mainbg">
    <div class="leftdiv" :class="{'leftWdith':type == 'manager'}">
      <back class="t20"> 返回</back>
      <div class="maskbg">
        <div class="titlecss">{{item.caseName}}</div>

        <div class="messagecss bbline">

           <div class="fl">
            <span class="sptext">
              作者：{{item.createByName}}
            </span>
            <span class="sptext maxwidth">
              单位：{{item.unitName}}
            </span>
            <span class="sptext">案例类型：{{item.caseClassifyName}}</span>
           </div>
           <div class="fl">
            <span class="sptext"><img class="iconimg" src="@/assets/img/time.png" alt="">{{item.createTime}}</span>
            <span class="sptext"><img class="iconimg" src="@/assets/img/look.png" alt=""> {{item.caseViewNum}}</span>
            <span class="sptext" :class="{'zan_sel':item.isLike}" @click="zanAction(item)"><img class="iconimg mr4" :src="item.isLike ? selZan : defZan" alt="">{{item.caseLikeNum}}</span>
            <span class="sptext" @click="showView"><img class="iconimg mr4" :src="item.caseViewCount>0 ? viewicon : noviewicon" alt="">{{item.caseViewCount}}</span>
           </div>
        </div>
        <div class="sharecss" :class="{pdfH:item.caseType == 3,'scollY':item.caseType == 2}">
            <video-player
              v-if="item.caseType == 1"
              @ended="onPlayerEnded($event)"
              class="video-player vjs-custom-skin videocss"
              ref="videoPlayer"
              :playsinline="true"
              :options="playerOptions"
            ></video-player>
            <div class="w-e-text-container" v-if="item.caseType == 2">
              <div
              class="sharecon"
              data-slate-editor
              v-html="item.caseContents">
              </div>
            </div>


          <iframe
          class="pdfcss"
          v-if="item.fileUrl && item.caseType == 3"
          :src="`https://wthedu.51suiyixue.com/PDF/web/viewer.html?file=${item.fileUrl}`"
          frameborder="0">
          </iframe>
        </div>
        <div class="tagbg">
          <span class="tagitem" :key="index" v-for="(tag,index) in item.tags "> {{tag}}</span>
        </div>
        <div class="concss" v-if="item.projectIntroduction.length>0">{{item.projectIntroduction}}</div>
      </div>
    </div>
    <div class="rightdiv" v-if="type != 'manager'" v-loading="isLoading">
      <div class="bgimg">
        <span class="phtitle">评论（{{dataList.length}}）</span>
      </div>
      <div class="commenlist">
        <commentShare @reply="reply" v-for="(item,index) in dataList" :key="index" :item="item" @deleteAction="deleteAction"></commentShare>
      </div>
      <div class="footer">
        <el-input class="definput " ref="elRef" show-word-limit  maxlength="400" placeholder="说点什么，鼓励一下你的队友吧~" v-model="commonPar.comment"></el-input>
        <el-button class="defaultbtn ml16" type="primary" @click="submitAction" :loading="isSubmit">提交</el-button>
      </div>
    </div>
    <replyDialog ref="replyDialog" :visible.sync="replayDialogVisible" :title="replayTitle" @updateVisible="updateReplayVisible" @replyData="replyData"></replyDialog>
    <ViewDialog ref="viewdialog"></ViewDialog>
  </div>

</template>

<script>
import back from "../../common/back.vue";
import commentShare from '../../common/commentShare.vue';
import replyDialog from '../../common/replyDialog.vue';
import VideoPlayer from 'vue-video-player'
import Vue from 'vue'
require('video.js/dist/video-js.css')
require('vue-video-player/src/custom-theme.css')
Vue.use(VideoPlayer)
import { addSnop } from '@/utils/tools'
import {
  updateLike,
  studyshareInfo,
  savecaselearningrecord,
  queryCaseCommentList,
  casecommentSave,
  deletecasecomment, } from "@/api/studyshare";
import ViewDialog from "./components/viewDialog.vue";

export default {
  props:{
    type:{
      type:String,
      default:'view'
    }
  },
  components:{
    back,
    commentShare,
    replyDialog,
    ViewDialog
  },
  data(){
    return{
      deleteVisible:false,
      replayTitle:'',
      replayDialogVisible:false,
      isLoading:false,
      isSubmit:false,
      dataList:[],
      item:{
        tags:[],
        caseClassify:"",
        caseClassifyName:"",
        projectIntroduction:'',
        caseType:"",
        label:"",
        caseContents:"",
        caseCover:"",
        caseCoverUrl:"",
        caseViewNum:"",
        caseLikeNum:"",
        isLike:false,
        fileUrl:''
      },
      departmentId:"",
      defZan:require('@/assets/img/zan_def.png'),
      selZan:require('@/assets/img/zan_sel.png'),
      viewicon:require('@/assets/viewicon.png'),
      noviewicon:require('@/assets/noviewicon.png'),
      playerOptions : {
          playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
          autoplay: false, //如果true,浏览器准备好时开始回放。
          muted: false, // 默认情况下将会消除任何音频。
          loop: false, // 导致视频一结束就重新开始。
          preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
          language: 'zh-CN',
          aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
          fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
          sources: [{
            type: "",//这里的种类支持很多种：基本视频格式、直播、流媒体等，具体可以参看git网址项目
            src: ''//url地址
          }],
          poster: "../../static/images/test.jpg", //你的封面地址
          notSupportedMessage: '此视频暂无法播放，请稍后再试', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
          controlBar: {
            timeDivider: true,
            durationDisplay: true,
            remainingTimeDisplay: false,
            fullscreenToggle: true  //全屏按钮
          }
      },
      isDelShow:false,
      startTime:0,
      deletecasecommentId:'',
      commonPar:{
        caseId:this.$route.query.id,
        comment:"",
        parentId:0,
      },
      rateType:2,
    }
  },
  created(){
    this.loadCommonList();
    this.loadInfo();
  },
  beforeDestroy(){
    this.saveRecord();
  },
  methods:{
    onPlayerEnded($event) {
       this.rateType = 1
       this.saveRecord()
    },
    loadInfo(){
        studyshareInfo(this.$route.query.id).then((result) => {
            this.item = result.data;
            this.item.tags = result.data.label.split(',');
            this.startTime = new Date().getTime();
            if (this.item.caseType == 1) {
              var videoData = this.item.fileInfoList[0];
              this.playerOptions.sources[0].src = videoData.url;
              this.playerOptions.poster = addSnop(videoData.url);
            }else if (this.item.caseType == 3) {
              var pdf = this.item.fileInfoList[0];
              this.item.fileUrl = pdf.url;
            }
        }).catch((err) => {

        });
    },
    loadCommonList(){
      this.isLoading = true;
      queryCaseCommentList({caseId:this.$route.query.id}).then((result) => {
        this.dataList = result.data;
        this.isLoading = false;
      }).catch((err) => {
        this.isLoading = false;
      });
    },
    saveRecord(){
      var endTime = new Date().getTime();
      var  time = (endTime - this.startTime)/1000
       if (time>60) {
          var par = {
            caseId:this.item.id,
            learningTime:parseInt(time),
            rateType:this.rateType
          };
          savecaselearningrecord(par).then((result) => {

          }).catch((err) => {

          });
       }
    },
    showView(){
      this.$refs.viewdialog.show(this.$route.query.id)
    },
    zanAction(item){
      updateLike({caseId:item.id}).then((result) => {
        if (result.data) {
          this.item.isLike = this.item.isLike == true ? false :true;
          if (this.item.isLike == true) {
            this.item.caseLikeNum = Number(this.item.caseLikeNum) + 1
          }else{
            this.item.caseLikeNum = Number(this.item.caseLikeNum) - 1
          }
        }
      }).catch((err) => {

      });
    },
    reply(data){
        this.commonPar.parentId = data.id,
        this.updateReplayVisible(true);
        this.replayTitle = `回复@${data.name}`
    },

    updateReplayVisible(val){
      this.replayDialogVisible = val;
    },
    replyData(data){
      this.commonPar.comment = data;

      this.save();
      this.$refs.replyDialog.closeAction();

    },
    submitAction(){
      this.commonPar.parentId = 0;
      this.save();
    },
    save(){
        this.commonPar.parentId == 0 ? this.isSubmit = true : null
        casecommentSave(this.commonPar).then((result) => {
          if (result.data) {
            this.$message({
              type:"success",
              message:"提交成功"
            });
            this.commonPar.comment = ''
            this.loadCommonList();
          }else{
            this.$message({
              type:"error",
              message:result.msg
            });
          }
          this.commonPar.parentId == 0 ? this.isSubmit = false : null
        }).catch((err) => {
          this.commonPar.parentId == 0 ? this.isSubmit = false : null
        });
    },

    deleteAction(commentId){
      console.log("ssssssss",commentId);
      deletecasecomment({id:commentId}).then((result) => {
        if (result.data) {
          this.$message({
            type:"success",
            message:"删除成功"
          })
          this.loadCommonList();
        }else{
          this.$message({
            type:"success",
            message:"删除失败"
          })
        }
      }).catch((err) => {

      });
    },
  }
}
</script>

<style scoped>
.fl{
  display: flex;
  align-items: center;
}
.videocss{
  max-height: calc(100% - 40px) !important;
}
.pdtop{
  padding-top: 16px;
}
.t20{
  position: absolute;
  top: 16px;
  left: 20px;
}
.dbtn{
  position:absolute;
  top: 28px;
  right: 100px;
  font-size: 14px;
  color: #F45961;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  cursor: pointer;
}
.commenlist{
  height: calc(100% - 130px);
  overflow-y: auto;
}
.ml16{
  margin-left: 16px !important;
}
.footer{
  position: absolute;
  left: 0px;
  right: 0px;
  bottom: 20px;
  height: 34px;
  display: flex;
  justify-content: space-between;
  padding: 0px 12px;
}
.bgimg{
  background: url(../../../assets/img/paihang_bg.png) no-repeat;
  background-size: 100%;
  height: 48px;
  margin-top: 12px;
  padding:0px 16px;
}
.pdfcss{
  width: 100%;
  height: 100%;
}
.sharecon{
  line-height: 30px;
  text-align: justify;
}
.sharecss{
  width:100%;
  margin: 0 auto;
  padding:16px;
  background: #F6F7FB;
  border-radius: 8px;
}
.pdfH{
  height: calc(100% - 170px);
}
.scollY{
  max-height: calc(100% - 170px);
  overflow-y: auto !important;
}
.tagbg{
  margin:0;
  margin-top: 12px;
}
.tagitem{
  background-color: #DFEAFD;
  border-radius: 2px;
  padding: 4px 8px;
  /* 文本样式 */
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  line-height: 14px;
  margin-right: 8px;

}
.concss{
  margin-top: 12px;
  padding-bottom: 20px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
  text-align: justify;
}
.pb30{
  padding-bottom: 30px;
}
.c1{
  color: #FFA44C !important;
}
.messagecss{
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  margin-top: 26px;
  margin-bottom: 17px;
}
.messagecss span{
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 14px;
  padding-right: 8px;

}
.sptext{
  height: 21px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.sptext + .sptext{
  margin-left: 30px;
}
.zantext{
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  line-height: 16px;
  color: #333333;
  cursor: pointer;
}
.zan_sel{
  color: #FFA44C;
}
.iconimg{
  width: 16px !important;
  height: 16px !important;
  opacity: 1;
  margin-top: -3px;
}
.zanimg{
  width: 16px;
  height: 16px;
  opacity: 1;
  margin-top: -3px;

}
.mr4{
  margin-right: 4px;
}
.titlecss{
  margin-top: 20px;
  text-align: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.mainbg{
  display: flex;
  justify-content: center;
  width: 100%;
  height: calc(100% + 40px);
  margin-top: -20px;
  margin-bottom: -20px;
}
.maskbg{
  height: calc(100% - 30px);
  width: calc(100% - 12%);
  margin: 0 auto;
}
.leftdiv{
  width: 80%;
  padding: 0 auto;
  background-color: white;
  border-radius: 8px;
  margin-right: 12px;
  position: relative;
  height: 100%;
  overflow-y: auto;
}
.leftWdith{
  width: 100% !important;
  .maskbg{
    max-width: 1140px;
  }
}

.rightdiv{
  width: 20%;
  min-width: 360px;
  background-color: white;
  border-radius: 8px;
  height: 100%;
  position: relative;
}

.maxwidth{
  width: 200px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

</style>
