import service from '@/utils/request.js'



export function dictList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/dicttype/queryVoListForPage`,
    params
  });
}


export function queryDictTypeVoList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/dicttype/queryDictTypeVoList`,
    params
  });
}

export function queryDictItemVoListForPage(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/dicttype/queryDictItemVoListForPage`,
    params
  });
}

export function queryList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/dicttype/queryList`,
    params
  });
}


// /crm/business/giftinfo/save

export function adddict(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/dicttype/saveDictItem',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function saveDictItem(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/dicttype/saveDictItem',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function updateDictItem(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/dicttype/updateDictItem',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function updateDict(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/dicttype/updateDictItem',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
export function deleteDict(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/dicttype/delete',
    data,
  });
}
export function dictInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/dicttype/findItemInfo/${id}`,
  });
}

// 下载模版
// /crm/controller/dicttype/dictItemDownloadTemplate

export function dictItemDownloadTemplate(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/dicttype/dictItemDownloadTemplate`,
    params
  });
}


// /sf/business/dictitem/queryDictItemList

export function queryDictItemList(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/dictItem/queryDictItemList',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

