
import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import qs from 'qs'
import router from '../router'

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  withCredentials: true,
  timeout: 30000,
})

service.interceptors.request.use(
  config => {
    if (config.headers['Content-Type'] == 'application/json;charset=UTF-8') {

    }
    else {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';
    }
    if (store.getters.token) {
      config.headers.Authorization = getToken()
      //config.headers.Authorization = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************************************************************************************************************************.6YDG7RrVagrroHKLHklN0GI9Qq-qSNnGRXeesXkyWLWMnef4ghW_3G7zOoHaGyBYZ4Tiv9BVbc59K6GH_NoWgQ"
    } else {
      config.headers.Authorization = getToken()
    }
    if (config.data instanceof FormData) {
      config.params = {
        ...config.params
      }
      return config
    }
    if (config.method === 'get') {
      if (!config.params) {
        config.params = {
          applicationId: sessionStorage.getItem('applicationId')
        }
      } else {
        config.params.applicationId = sessionStorage.getItem('applicationId')
      }
    } else if (config.method === 'post') {
      if (!config.data) {
        config.data = {
          applicationId: sessionStorage.getItem('applicationId')
        }
      } else {
        config.data.applicationId = sessionStorage.getItem('applicationId')
      }
    }
    if (config.method === 'post' && config.headers['Content-Type'] != 'application/json;charset=UTF-8') {
      let data = qs.parse(config.data)
      const finalData = {
        ...data
      }
      config.data = qs.stringify(finalData)
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data
    if (response.status === 200) {
      if (response.data.status === 100401) {//未登录
        MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定退出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      if (response.data.status === 100403) { // 没权限
        // Message.error(response.data.msg)
        return Promise.reject(new Error(res.message || 'Error'))
      }
      if (response.data.status === 100100) { // 数据迁移异常
        return response.data
      }
      if (response.data.status === 501) { //规则新增异常 // 成本撤销异常 //成本申报异常 //工时提交异常
        return response.data
      }
      if (response.data.status && response.data.status !== 0) {
        Message.error(response.data.msg)
        return Promise.reject(new Error(res.message || 'Error'))
      }
      return response.data
    } else {
      Message.error(response.msg)
      return Promise.reject(new Error(res.message || 'Error'))
    }
  },
  error => {
    Message({
      message: '请求服务器失败，请检查服务是否正常',
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service

