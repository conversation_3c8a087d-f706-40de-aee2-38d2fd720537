export const asyncRoutes = [
  {
    path: '/home',
    component: Layout,
    meta: { title: '首页', icon: 'el-icon-s-help' },
    children: [
      {
        path: '',
        component: () => HomeIndex,
        meta: { title: '首页' },
      },
    ]
  },
  {
    path: '/basesystem',
    meta: { title: '基本管理', icon: 'system' },
    component: Layout,
    redirect: '/basesystem/policy',
    children: [
      {
        path: '/basesystem/policy',
        name: 'policy',
        meta: { title: '政策管理' },
        component: () => import('@/components/basesystem/policy'),
      },
      {
        path: '/basesystem/standard',
        name: 'standard',
        meta: { title: '标准管理' },
        component: () => import('@/components/basesystem/standard'),
      },
      {
        path: '/basesystem/banner',
        name: 'banner',
        meta: { title: 'banner管理' },
        component: () => import('@/components/basesystem/banner'),
      },
    ]
  },
  {
    path: '/framework',
    component: Layout,
    meta: { title: '系统管理', icon: 'el-icon-setting' },
    redirect: '/framework/appManage',
    children: [
      {
        path: '/framework/appManage',
        component: () => import('@/components/framework/appManage'),
        meta: { title: 'APP版本管理' },

      },
      {
        path: '/framework/article',
        component: () => import('@/components/framework/article'),
        meta: { title: '文章管理' }
      },
      {
        path: '/framework/application',
        component: () => import('@/components/framework/application'),
        meta: { title: '应用平台管理' }
      },
      {
        path: '/framework/resource',
        component: () => import('@/components/framework/resource'),
        meta: { title: '资源管理' }
      },
      {
        path: '/framework/tagtype',
        component: () => import('@/components/framework/tagtype'),
        meta: { title: '标签管理' }
      },
      {
        path: '/framework/configtype',
        component: () => import('@/components/framework/configtype'),
        meta: { title: '配置管理' }
      },

      {
        path: '/framework/roleposition',
        component: () => import('@/components/framework/roleposition'),
        meta: { title: '角色管理' }
      },
      {
        path: '/framework/user',
        name: 'User',
        component: () => import('@/components/framework/user'),
        meta: { title: '用户管理' }   //用户管理
      },
      {
        path: '/framework/identity',
        name: 'identity',
        component: () => import('@/components/framework/identity'),
        meta: { title: '身份管理' }   //用户管理
      },
      {
        path: '/framework/log',
        component: () => import('@/components/framework/index'),
        meta: { title: '日志管理' },
        redirect: '/framework/operationlog',
        children: [
          {
            path: '/framework/operationlog',
            component: () => import('@/components/framework/operationlog'),
            meta: { title: '操作日志' },
          },
          {
            path: '/framework/loginlog',
            component: () => import('@/components/framework/loginlog'),
            meta: { title: '登录日志' },
          }
        ]
      },
    ]
  },
]