
<template>
  <div class="maindiv fixpb">
    <el-form :inline="true" class="myform">
      <el-form-item label="">
        <el-input
          v-model="pageBean.createByName"
          class="definput"
          clearable
          placeholder="请输入提交人姓名"
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="`${types[typeLabel].label}：`">
        <el-date-picker
          class="definput"
          @change="changeDatePicker"
          v-model="searchValue"
          :type="types[typeLabel].type"
          :format="types[typeLabel].format"
          placeholder="请选择"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="部门：">
        <div class="unitbtn mt definput" @click="chooseDepartment">
          <span v-if="pageBean.departmentIds" class="deffont">{{
            searchdepartmentNames
          }}</span>
          <span v-else>请选择</span>
          <i
            v-if="searchdepartmentNames"
            @click.stop="cleardata"
            class="rcenter el-icon-circle-close"
          />
          <i v-else class="rcenter el-icon-arrow-down" />
        </div>
      </el-form-item>
      <el-form-item label="查阅状态：">
        <el-select
          class="definput w1"
          popper-class="removescrollbar"
          clearable
          v-model="pageBean.status"
          placeholder="请选择"
        >
          <el-option
            v-for="item in statusList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          @click="searchAction"
          class="defaultbtn mt"
          icon="el-icon-search"
          type="primary"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item class="fr">
        <el-button
          class="defaultbtn mt"
          v-isShow="'crm:controller:personalgoals:save'"
          icon="el-icon-plus"
          type="primary"
          @click="addPlan"
          >发起总结</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        :width="typeLabel == 6 ? 260 : 100"
        prop="year"
        :label="types[typeLabel].label"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ getPlanTime(scope.row) }}</span>
          <span v-if="typeLabel == 6"
            >({{
              scope.row.timeRange.start + '-' + scope.row.timeRange.end
            }})</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="createByName" label="提交人" align="center">
        <template slot-scope="scope">
          <span
            >{{ scope.row.createByName }}
            <span class="noset">{{
              scope.row.logsStatus == 1 ? '(未处理)' : ''
            }}</span></span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="departmentName"
        label="部门"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column prop="year" label="查阅状态" align="center">
        <template slot-scope="scope">
          <span :class="statusColor[scope.row.status]">{{
            statusNames[scope.row.status]
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="提交时间" align="center">
      </el-table-column>
      <el-table-column prop="createTime" label="提交情况" align="center">
        <template slot-scope="scope">
          <span>{{ numberToName[scope.row.timeout] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="edit"
        width="160"
        align="center"
        label="操作"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-isShow="'crm:controller:personalgoals:info'"
            class="bbtn"
            type="text"
            @click="todetail(scope.row)"
          >
            详情</el-button
          >
          <el-button
            v-isShow="'crm:controller:personalgoals:updateSummarize'"
            class="bbtn"
            type="text"
            @click="toEdit(scope.row)"
            :disabled="isEditable(scope.row)"
          >
            编辑</el-button
          >
          <el-button
            v-isShow="'crm:controller:personalgoals:delete'"
            class="rbtn"
            type="text"
            :disabled="isDelete(scope.row)"
            @click="deleteAction(scope.row)"
          >
            删除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>

    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <!-- 选择部门 -->
    <departmentDialog
      ref="deRef"
      dType="1"
      :visible.sync="dialogDepartmentVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </departmentDialog>
    <!-- 删除 -->
    <dc-dialog
      iType="1"
      title="温馨提示"
      width="400px"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <template> </template>
      <p class="pcc">是否删除该总结内容？</p>
    </dc-dialog>
    <!-- 发起总结 -->
    <el-dialog
      title="发起总结"
      :visible.sync="dialogPlanVisible"
      width="500px"
      center
      :before-close="handlePlanClose"
    >
      <span>
        <el-form
          :model="addForm"
          :rules="rules"
          class="mt30 formw"
          label-width="85px"
          ref="formModel"
        >
          <el-form-item :label="`${types[typeLabel].label}：`" prop="planTime">
            <el-date-picker
              class="definput"
              :picker-options="pickerOptions"
              v-model="addForm.planTime"
              :type="types[typeLabel].type"
              :format="types[typeLabel].format"
              placeholder="请选择"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item
            label="部门："
            prop="departmentId"
            v-if="departmentArr.length > 1"
          >
            <el-select
              @change="cheangeDe"
              class="definput"
              popper-class="removescrollbar"
              v-model="addForm.departmentId"
              placeholder="请选择"
            >
              <el-option
                v-for="item in departmentArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="模版："
            prop="template"
            v-if="addForm.departmentId"
          >
            <el-select
              class="definput"
              popper-class="removescrollbar"
              clearable
              v-model="addForm.template"
              placeholder="请选择"
            >
              <el-option
                v-for="item in templateList"
                :key="item.id"
                :label="item.templateName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextAction">下一步</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="温馨提示"
      :visible.sync="dialogDraftVisible"
      width="400px"
      center
      :close-on-click-modal="false"
    >
      <div class="pcc">
        <p>存在未提交草稿，是否继续编辑草稿？</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rewriteSummary">重新填写</el-button>
        <el-button type="primary" @click="continueEditDraft">继续编辑</el-button>
      </span>
    </el-dialog>
  </div>
</template>
  <script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import { findTemplate } from '@/api/common'
import {
  personalgoalsList,
  personalgoalsdelete,
  queryCompanyDepartment,
} from '@/api/goal/index'
import {
  getDict,
  downloadFileByUrl,
  getWeekDates,
  getParStr,
} from '@/utils/tools'
import { getToken } from '@/utils/auth'
import { getWeek, getPlanTime } from '@/utils/index'
import departmentDialog from '@/components/common/departmentDialog.vue'
import { isExist } from '@/api/index'
export default {
  name: 'listtable',
  props: {
    typeLabel: {
      type: String,
      default: '4',
    },
  },
  components: {
    page,
    nolist,
    departmentDialog,
  },
  data() {
    return {
      numberToName: {
        1: '准时提交',
        2: '迟交',
        0: '--',
      },
      pickerOptions: {
        disabledDate: this.endPickerTime,
      },
      types: {
        4: {
          label: '年份',
          type: 'year',
          format: 'yyyy',
        },
        5: {
          label: '年-月',
          type: 'month',
          format: 'yyyy-M',
        },
        6: {
          label: '年-周',
          type: 'week',
          format: 'yyyy 第 WW 周',
        },
      },
      searchValue: '',
      addForm: {
        template: '',
        planTime: '',
        departmentId: '',
      },
      templateList: '',
      deleteData: {},
      draftId: '',
      detailVisible: false,
      dialogVisible: false,
      dialogPlanVisible: false,
      dialogDepartmentVisible: false,
      dialogDraftVisible: false,
      dialogFormVisible: false,
      dialogTitle: '',
      isSubmit: false,
      isLoading: false,
      searchdepartmentNames: '',
      returnStatusArr: [],
      options: [],
      tableData: [],
      total: 0,
      form: {
        name: '',
        dictTypeCode: 'Press',
        sortNo: '',
      },
      makeplanTime: {
        year: '',
        month: '',
        week: '',
      },
      rules: {
        planTime: [{ required: true, message: '请选择时间', trigger: 'blur' }],
        template: [
          { required: true, message: '请选择模板', trigger: 'change' },
        ],
        departmentId: [
          { required: true, message: '请选择部门', trigger: 'change' },
        ],
      },
      statusList: [
        { id: '1', name: '未查阅' },
        { id: '2', name: '部分查阅' },
        { id: '3', name: '已查阅' },
      ],
      statusColor: {
        1: 'bcolor',
        2: 'icolor',
        3: 'scolor',
      },
      statusNames: {
        1: '未查阅',
        2: '部分查阅',
        3: '已查阅',
      },
      pageBean: {
        createByName: '',
        status: '',
        departmentIds: '',
        pageNum: 1,
        pageSize: 10,
        year: '',
        month: '',
        week: '',
        goalsType: '',
        isChange: '',
        goalStatus: 1,
      },
      addpath: {
        4: '/zongjie/yearzong/addzong',
        5: '/zongjie/monthzong/monthzadd',
        6: '/zongjie/weekzong/weekzadd',
      },
      detailpath: {
        4: '/zongjie/yearzong/detail',
        5: '/zongjie/monthzong/monthzdetail',
        6: '/zongjie/weekzong/weekzdetail',
      },
      selDepartments: [],
      departmentArr: [],
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
    }
    this.pageBean.goalsType = this.typeLabel
    this.loadData()
    this.listDeptApi()
  },
  methods: {
    isEditable(data) {
      if (
        data.status == 1 &&
        data.createBy == sessionStorage.getItem('userid')
      ) {
        return false
      }
      return true
    },
    listDeptApi() {
      queryCompanyDepartment({}).then((result) => {
        if (result.status == 0) {
          this.departmentArr = result.data
          if (this.departmentArr.length == 1) {
            this.addForm.departmentId = this.departmentArr[0].id
          }
        }
      })
    },
    endPickerTime(time) {
      const today = new Date()
      return time > today
    },
    getPlanTime(data) {
      return getPlanTime(data, this.typeLabel)
    },
    cheangeDe(value) {
      this.addForm.template = ''
      this.loadTemplate()
    },
    loadTemplate() {
      findTemplate({
        methodName: 'save',
        className: 'PersonalGoalsController',
        templateType: this.typeLabel,
        departmentId: this.addForm.departmentId,
      })
        .then((result) => {
          this.templateList = result.data
        })
        .catch((err) => {})
    },
    querySearch(queryString, callback) {
      var list = [{}]
      if (queryString && queryString.length > 0) {
        let params = {
          dictTypeCode: 'Press',
          name: queryString,
        }
        queryList(params).then((res) => {
          list = res.data.map((item) => {
            return {
              id: `${item.id}`,
              value: `${item.name}`,
            }
          })
          callback(list)
        })
      }
    },
    handlename(item) {
      this.form.name = item.value
    },
    isDelete(data) {
      if (sessionStorage.getItem('dataScope') == 4) {
        return false
      }
      if (data.status == 1 || data.status == 4) {
        return false
      }
      return true
    },
    isChangeable(data) {
      if (data.status == 1 || data.status == 2 || data.status == 4) {
        return true
      }
      return false
    },
    // isEditable(data) {
    //   if (data.status == 1 || data.status == 4) {
    //     return false
    //   }
    //   return true
    // },
    loadData(params) {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      personalgoalsList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          if (this.typeLabel == 6) {
            this.tableData.forEach((item) => {
              item.timeRange = getWeekDates(item.year, item.week)
            })
          }
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    chooseDepartment(e) {
      this.dialogDepartmentVisible = true
      this.$refs.deRef.loadData()
      this.$refs.deRef.updateWorksIdTree(this.selDepartments)
    },
    updateSystemVisible(val) {
      this.dialogDepartmentVisible = val
    },
    submitData(data) {
      this.dialogDepartmentVisible = false
      this.selDepartments = data
      let departmentIds = data.map((item) => item.id)
      let departmentNames = data.map((item) => item.name)
      this.pageBean.departmentIds = departmentIds.join(',')
      this.searchdepartmentNames = departmentNames.join(',')
    },
    cleardata() {
      ;(this.searchdepartmentNames = ''), (this.pageBean.departmentIds = '')
      this.selDepartments = []
    },
    addPlan() {
      this.dialogPlanVisible = true
      this.loadTemplate()
    },
    handlePlanClose() {
      this.dialogPlanVisible = false
      this.planTime = ''
      this.$refs['formModel'].resetFields()
    },
    nextAction() {
      this.$refs['formModel'].validate((valid) => {
        if (valid) {
          let params = {
            goalsType: this.typeLabel,
            departmentId: this.addForm.departmentId,
            year: '',
            month: '',
            week: '',
          }
          params.year = new Date(this.addForm.planTime).getFullYear()
          if (this.typeLabel == 5) {
            params.month = new Date(this.addForm.planTime).getMonth() + 1
          } else if (this.typeLabel == 6) {
            // let startTime = new Date(this.addForm.planTime.getTime()); //开始时间
            // let endTime = new Date(this.addForm.planTime.getTime() + (24 * 60 * 60 * 1000) * 6); //结束时间
            // let timeArr = [startTime.toISOString().slice(0, 10), endTime.toISOString().slice(0, 10)];
            // console.log(timeArr)
            var time = getWeek(this.addForm.planTime)
            var times = time.split('-')
            params.week = times[1]
          }
          isExist(params).then((res) => {
            if (res.status == 0) {
              if (res.data.status == 0) {
                this.showDraftConfirmDialog(res.data.id)
              } else if (res.data.status == 1) {
                this.$message({
                  type: 'warning',
                  message: '已存在记录，无法新增总结',
                })
              } else {
                this.navigateToAddPage()
              }
            } else {
              this.$message({
                type: 'error',
                message: res.msg,
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    showDraftConfirmDialog(draftId) {
      this.draftId = draftId
      this.dialogDraftVisible = true
    },
    navigateToAddPage() {
      if (this.addForm.planTime && this.addForm.template) {
        this.$router.push({
          path: `${this.addpath[this.typeLabel]}?type=${
            this.typeLabel
          }&time=${this.addForm.planTime.toDateString()}&templateId=${
            this.addForm.template
          }&departmentId=${this.addForm.departmentId}`,
        })
      }
    },
    continueEditDraft() {
      // 继续编辑草稿，带上草稿ID
      if (this.addForm.planTime && this.addForm.template && this.draftId) {
        this.$router.push({
          path: `${this.addpath[this.typeLabel]}?type=${
            this.typeLabel
          }&time=${this.addForm.planTime.toDateString()}&templateId=${
            this.addForm.template
          }&departmentId=${this.addForm.departmentId}&id=${this.draftId}`,
        })
      }
      this.dialogDraftVisible = false
    },
    rewriteSummary() {
      if (this.addForm.planTime && this.addForm.template && this.draftId) {
        this.$router.push({
          path: `${this.addpath[this.typeLabel]}?type=${
            this.typeLabel
          }&time=${this.addForm.planTime.toDateString()}&templateId=${
            this.addForm.template
          }&departmentId=${this.addForm.departmentId}&draftId=${this.draftId}`,
        })
      }
      this.dialogDraftVisible = false
    },
    submitDialog() {
      personalgoalsdelete({ id: this.deleteData.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
      this.dialogVisible = false
    },
    addInfo() {
      this.dialogFormVisible = true
      this.dialogTitle = '新增出版社'
    },
    deleteAction(data) {
      this.deleteData = data
      this.dialogVisible = true
    },
    toEdit(row) {
      this.$router.push({
        path: `${this.addpath[this.typeLabel]}?type=${this.typeLabel}`,
        query: {
          id: row.id,
          createByName: row.createByName,
          timeRange: row.timeRange
            ? `${row.timeRange.start}-${row.timeRange.end}`
            : '',
        },
      })
    },
    todetail(data) {
      this.$router.push({
        path: this.detailpath[this.typeLabel],
        query: {
          id: data.id,
          type: this.typeLabel,
          createByName: data.createByName,
          timeRange: data.timeRange
            ? `${data.timeRange.start}-${data.timeRange.end}`
            : '',
        },
      })
    },
    changeDatePicker(date) {
      if (!date) {
        this.pageBean.year = ''
        this.pageBean.month = ''
        this.pageBean.week = ''
        return
      }
      var newDate = new Date(date)
      this.pageBean.year = newDate.getFullYear()
      if (this.typeLabel == '2' || this.typeLabel == '5') {
        this.pageBean.month = newDate.getMonth() + 1
      } else if (this.typeLabel == '3' || this.typeLabel == '6') {
        var time = getWeek(newDate)
        var times = time.split('-')
        this.pageBean.year = times[0]
        this.pageBean.week = times[1]
      }
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>
  
  <style scoped>
.noset {
  color: #e6a23c;
  margin-left: 8px;
}
.formw {
  width: 340px;
  margin: auto;
}
.mt30 {
  margin-top: 30px;
}
.el-auto {
  width: 280px;
}
.w1 {
  width: 110px !important;
}
.flexd {
  text-align: right;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}
.definput {
  width: 200px;
}
.maindiv {
  background-color: white;
  min-height: calc(100vh - 136px);
}
.pcc {
  margin: 0 auto;
  text-align: center;
}
.smtext {
  zoom: 0.8;
}
.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.mt {
  margin-top: 4px;
}
.ml {
  margin-right: 10px;
}
.cusnamecss {
  display: flex;
}
.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 20px;
}
.bcolor {
  color: #4285f4;
}
.icolor {
  color: #ff8d1a;
}
.scolor {
  color: #56c36e;
}
.ecolor {
  color: #f45961;
}
.fcolor {
  color: #333333;
}
.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}
.ltext {
  color: #4285f4;
  text-align: justify !important;
}
.mytable /deep/ .cell {
  height: auto !important;
  min-height: 52px !important;
  padding: 13px !important;
  line-height: 25px !important;
}
.mytable .el-button {
  padding: 2px;
}
.mr10 {
  margin-right: 10px;
}
.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.right {
  text-align: right;
}
.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}
</style>