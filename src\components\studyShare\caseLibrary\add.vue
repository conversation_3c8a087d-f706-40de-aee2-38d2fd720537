<template>
    <div class="flex">
        <div>
            <back>{{title}}</back>
        </div>
        <div class="mainbg">
            <el-form ref="addform" class="addfcss" :model="form" :rules="rules" label-width="118px">
                <textBorder>基础信息</textBorder>
                <div class="pt20  bbline">
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item label="案例名称：" prop="caseName">
                                <el-input class="definput" placeholder="请输入分享的案例名称" v-model="form.caseName"
                                    maxlength="50" show-word-limit></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="单位：" prop="unitId">
                                <div class="unitbtn" @click="chooseunit">
                                    <span v-if="form.unitId" class="deffont">{{ form.unitName }}</span>
                                    <span v-else>请选择</span>
                                    <i @click.stop="closeOpp"
                                    v-show="form.unitName" class="rcenter el-icon-close"></i> <i
                                    v-show="!form.unitName" class="rcenter el-icon-arrow-down" />
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="案例类型：" prop="caseClassify">
                                <el-select class="definput" popper-class="removescrollbar" v-model="form.caseClassify"
                                    placeholder="请选择分类">
                                    <el-option v-for="item in options" :key="item.id" :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="16">
                            <el-form-item class="tagcol" label="标签：" prop="label">
                                <div @click="clickInput">
                                    <el-tag :key="index" v-for="(tag,index) in dynamicTags" closable class="tagcss"
                                        size="small" :disable-transitions="false" @close="handleClose(tag)">
                                        {{tag}}
                                    </el-tag>
                                    <el-input class="input-new-tag" placeholder="按回车键Enter创建标签" v-model="inputValue"
                                        v-if="dynamicTags && dynamicTags.length != 5" ref="saveTagInput" size="small"
                                        @keyup.enter.native="handleInputConfirm" @blur="handleInputConfirm">
                                    </el-input>
                                    <span class="right pr10" v-if="dynamicTags && dynamicTags.length != 5">
                                        还能添加{{ 5-dynamicTags.length}}个标签
                                    </span>
                                </div>

                            </el-form-item>
                            <el-form-item class="mb10" label="封面：" prop="caseCoverUrl">
                                <img  @click="showUpload" v-if="form.caseCoverUrl" :src="form.caseCoverUrl" class="avatar" />
                                <div v-else  class="bordediv" @click="showUpload">
                                    <i  class="el-icon-plus"></i>
                                    <div  class="uploadtext deffont">点击上传</div>
                                </div>
                                <!-- <upload :multiple="false" ref="coverupload" accept=".jpg, .png, .jpeg, .JPG, .PNG, .JPEG" :limit="1"
                                    :data="{serviceName:'crm/case/cover'}" @submitImg="submitCoverSuccess"></upload> -->
                                    <!-- <el-upload ref="elUpload" accept=".jpg, .png, .jpeg, .JPG, .PNG, .JPEG,.gif,.pjpeg,.GIF" :limit="1" :multiple="false"  :data="{serviceName:'crm/case/cover'}" :action="''" :show-file-list="false"  :before-upload="beforeAvatarUpload" >
                                     <img v-if="form.caseCoverUrl" :src="form.caseCoverUrl" class="avatar" />
                                     <div v-else class="uploadcss">
                                        <i class="el-icon-plus"></i>
                                        <div class="uploadtext deffont">点击上传</div>
                                      </div>
                                    </el-upload> -->
                                        <XCropper ref="iscropper" :iscropperData="iscropperData" :isXCropper="isXCropper"  @getXCropper="getXCropper"></XCropper>
                            </el-form-item>
                            <el-form-item label="内容描述：" prop="projectIntroduction">
                                <el-input class="definput" type="textarea" maxlength="300" show-word-limit
                                    v-model="form.projectIntroduction" rows="4" placeholder="请输入内容描述"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <textBorder class="mt30">分享内容</textBorder>
                <div class="pt20  ">
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="16">
                            <el-form-item v-if="type == 1" class="mb0" label="视频：" prop="fileInfoList">
                                <upload :multiple="false" ref="videoupload" accept=".mp4" :limit="1"
                                    :data="{serviceName:'crm/case/video'}" @submitImg="submitVideoSuccess" :fileList="fileList"></upload>
                            </el-form-item>
                            <el-form-item v-if="type == 2" label="分享内容：" prop="caseContents">
                                <div class="wangeditor">
                                    <div ref="toolbar" class="toolbar"></div>
                                    <div ref="wangeditor" class="text"></div>
                                </div>
                            </el-form-item>
                            <el-form-item v-if="type == 3" class="mb0" label="文档：" prop="fileInfoList">
                                <upload2 :limit="1" :multiple="false" :data="{serviceName:'crm/case/pdf'}" :fileMaxSize='100' ref="upload2"
                                    @submitImg="submitPdf" accept=".pdf" :fileList="fileListPdf">
                                    <span class="studiocss">
                                        <img src="../../../assets/img/file_icon.png">
                                        <span class="uploadtext  deffont">点击上传附件</span>
                                    </span>
                                    <template slot="ptip">
                                        <p>只能上传pdf文件</p>
                                    </template>
                                </upload2>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="pt20 btncenter">
                    <el-form-item>
                        <el-button class="w98 btn_h42" type="primary" @click="submitAction" :loading="isSubmit">保存
                        </el-button>
                    </el-form-item>
                </div>
            </el-form>
        </div>
            <unitDialog ref="unitdialog" :visible.sync="unitDialogVisible" className="CustomerController" @updateVisible="updateVisible"
            @updateUnit="updateUnit"></unitDialog>

            <el-dialog
            class="dlcdialog"
            :visible.sync="dialogVisibleUpload"
            :before-close="handleCloseUpload">

            <div class="dbutton">
                <div @click="handleC(1)" class="dbcss">
                    选择模板
                    <img v-show="showImg == 1" class="checkss" src="../../../assets/check.png" alt="">
                </div>
                <div @click="handleC(2)" class="dbcss ml20">
                    上传本地照片
                    <img v-show="showImg == 2" class="checkss" src="../../../assets/check.png" alt="">
                </div>
            </div>

            <div v-show="showImg == 1">
                <div class="flexul">
                    <div class="lidiv" v-for="(item,index) in imgUrls" @click="checkImg(item,index)">
                        <img :src="item.imgNameActive?item.imgNameActive:item.imgName" alt="">
                        <img v-if="index == showIndex" class="quan" src="../../../assets/mocheck.png" alt="">
                        <img v-else class="quan" src="../../../assets/quan.png" alt="">
                    </div>
                </div>
                <span slot="footer" class="dialog-footer  footerdiv">
                    <div class="left ">
                        <p class="left spanl">
                            <span class="required-star">*</span>封面显示文案：
                        </p>
                        <el-input @clear="resetButton" maxlength="30" @keyup.native="onkeyup($event)" class='elInput' v-model.trim="inputValueMo" placeholder="请输入内容" clearable required></el-input>

                        <p class="resetcss" @click="resetButton">恢复默认</p>
                    </div>
                    <div class="bright">
                        <el-button @click="moCancleClick">取 消</el-button>
                        <el-button type="primary" @click="moHandleClick">确 定</el-button>
                    </div>
                </span>
            </div>

            <div v-show="showImg == 2" >
                <div class="uploadel">
                    <el-upload ref="elUpload" accept=".jpg, .png, .jpeg, .JPG, .PNG, .JPEG,.gif,.pjpeg,.GIF" :limit="1" :multiple="false"  :data="{serviceName:'crm/case/cover'}" :action="''" :show-file-list="false"  :before-upload="beforeAvatarUpload" >
                        <img v-if="formDia.caseCoverUrl" :src="formDia.caseCoverUrl" class="avatar" />
                        <div v-else class="uploadcss">
                           <i class="el-icon-plus"></i>
                           <div class="uploadtext deffont">点击上传</div>
                         </div>
                       </el-upload>
                </div>

                <span slot="footer" class="dialog-footer ccss">
                    <el-button @click="uploadCancle">取 消</el-button>
                    <el-button type="primary" @click="uploadHandle">确 定</el-button>
                </span>
            </div>
            </el-dialog>
            <msg-dialog ref="msgdialog"/>
    </div>
</template>

<script>
    import E from "wangeditor";
    import back from '../../common/back.vue';
    import textBorder from '../../common/textBorder.vue';
    import contractDialog from '../../common/contractDialog.vue'
    import upload from '../../common/upload.vue';
    import { getDict } from "@/utils/tools";
    import { addstudyshare, updatestudyshare, studyshareInfo } from "@/api/studyshare";
    import {uploadFile} from '@/api/wapi'
    import upload2 from '../../common/upload2.vue';
    import unitDialog from '../../common/unitDialog.vue';
    import XCropper from "../../common/XCropper"
    import { getFileTypeNum, getFileType, addSnop } from '@/utils/tools'
    export default {
        components: {
            back,
            textBorder,
            contractDialog,
            upload,
            upload2,
            unitDialog,
            XCropper,
        },
        data() {
            return {
                inputValueMo:'',
                showIndex:0,
                imgUrls:[
                    {
                        imgName:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/crm-develop/crm_platform/modal/modal_bg2.png',
                        imgNameActive:'',
                    },
                    {
                        imgName:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/crm-develop/crm_platform/modal/modal_bg1.png',
                        imgNameActive:''
                    },
                    {
                        imgName:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/crm-develop/crm_platform/modal/modal_bg3.png',
                        imgNameActive:''
                    },
                    {
                        imgName:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/crm-develop/crm_platform/modal/modal_bg4.png',
                        imgNameActive:''
                    },
                    {
                        imgName:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/crm-develop/crm_platform/modal/modal_bg5.png',
                        imgNameActive:''
                    },
                    {
                        imgName:'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/crm-develop/crm_platform/modal/modal_bg6.png',
                        imgNameActive:''
                    },
                ],
                showImg:1,
                dialogVisibleUpload:false,
                getUrl: `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`,
                title: this.$route.query.id ? '编辑分享' : '新建分享',
                type: this.$route.query.type,
                dialogVisible: false,
                isSmall: window.screen.availWidth < 1500 ? true : false,
                dialogName: '',
                inputValue: '',
                isDeleteTag: false,
                dynamicTags: [],
                rules: {
                    caseName: [
                        { required: true, message: '请输入案例名称', trigger: 'blur' },
                        { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
                    ],
                    caseClassify: [
                        { required: true, message: '请选择案例类型', trigger: 'change' }
                    ],
                    label: [
                        { required: true, message: '请设置标签', trigger: 'blur' }
                    ],
                    caseCoverUrl: [
                        { required: true, message: '请设置封面', trigger: 'change' }
                    ],
                    caseContents: [
                        { required: true, message: '请输入分享内容', trigger: 'blur' },
                        { min: 3, max: 5000, message: '长度在 3 到 5000个字符', trigger: 'blur' }
                    ],
                    fileInfoList: [
                        { required: true, message: '请选择分享资源', trigger: 'change' }
                    ],
                    inputValueMo: [
                        { required: true, message: '请输入封面显示文案', trigger: 'blur' }
                    ],
                },
                formDia:{
                    caseCover: "",
                    caseCoverUrl: "",
                },
                form: {
                    caseName: '',
                    contractId: '',
                    caseType: this.$route.query.type,
                    label: '',
                    caseClassify: "",
                    projectIntroduction: '',
                    caseContents: "",
                    caseCover: "",
                    caseCoverUrl: "",
                    fileInfoList: [],
                    unitId:'',
                    unitName:'',
                },
                isSubmit: false,
                options: [],
                contractVisible: false,
                wangEditor: null,
                fileListPdf: [],
                unitDialogVisible:false,
                iscropperData:{

                },
                isXCropper:false,
                lastTimeStamp:0,
                fileList:[],
            }
        },
        created() {
            this.getTypeList();
            if (this.type == 2) {
                this.$nextTick(() => {
                    this.initEditor();
                    this.wangEditor.txt.html(this.value);
                })
            }

            if (this.$route.query.id) {
                this.loadInfo();
            }

        },
        methods: {
            uploadHandle(){
                if(this.formDia.caseCoverUrl){
                    this.form.caseCover =  this.formDia.caseCover
                    this.form.caseCoverUrl =  this.formDia.caseCoverUrl
                    this.$refs.addform.validateField('caseCoverUrl');
                    this.uploadCancle()
                }else{
                    this.$message.error('请上传图片')
                }
            },
            uploadCancle(){
                this.formDia.caseCover = ''
                this.formDia.caseCoverUrl = ''
                this.dialogVisibleUpload = false
            },
            moCancleClick(){
                    if(this.inputValueMo){
                        this.resetButton()
                    }
                    this.showIndex = 0
                    this.dialogVisibleUpload = false
            },
            moHandleClick(){
                this.formDia.caseCover = ''
                this.formDia.caseCoverUrl = ''
                if (!this.inputValueMo) {
                    this.$message.error('请输入封面显示文案');
                    return;
                }
                if(this.inputValueMo){
                    let cloneFile = this.dataURLtoFile(this.imgUrls[this.showIndex].imgNameActive)
                    const formData = new FormData()
                    formData.append('file', cloneFile)
                    uploadFile(formData)
                        .then(response => {
                            this.getsuccess(response.data)
                            this.resetButton()
                            this.dialogVisibleUpload = false
                        })
                }else{
                    this.form.caseCoverUrl = this.imgUrls[this.showIndex].imgName
                    this.dialogVisibleUpload = false
                }
            },
            onkeyup(event){
                if(event.keyCode != 13){
                    this.lastTimeStamp = event.timeStamp;
                    setTimeout(() => {
                        if(this.lastTimeStamp == event.timeStamp){
                            this.handleImg();
                        }
                    }, 1000);
                }
            },
            addTextEvent(item) {
                    var _this = this
                    let canvas = document.createElement("canvas");
                    canvas.width = 325;
                    canvas.height = 244;
                    let myImage = new Image();
                    myImage.src =item.imgName;
                    myImage.crossOrigin = "Anonymous";
                    let context = canvas.getContext("2d");
                        myImage.onload = function() {
                            context.drawImage(myImage, 0, 0, canvas.width, canvas.height);
                            const text = _this.inputValueMo
                            const font = '24px Arial';
                            context.font = font;
                            context.fillStyle = 'white';
                            context.textAlign = 'left';
                            context.textBaseline = 'top';
                            let x = 12;
                            let y = 80;
                            let lineHeight = parseInt(font, 10) * 1.2;
                            let words = text.split('');
                            let temp = "";
                            for (let i = 0; i < words.length; i++) {
                                let width = context.measureText(temp).width;
                                if (context.measureText(temp).width > canvas.width - x - 16) {
                                    context.fillText(temp, x, y);
                                    temp = "";
                                    y += lineHeight;
                                }
                                temp += words[i] + " ";
                            }
                            context.fillText(temp, x, y);
                            let base64 = canvas.toDataURL("image/png");
                            item.imgNameActive = base64
                        }
            },
           dataURLtoFile(dataurl, filename = 'file') {
                let arr = dataurl.split(',');
                let mime = arr[0].match(/:(.*?);/)[1];
                let suffix = mime.split('/')[1];
                let bstr = atob(arr[1]);
                let n = bstr.length;
                let u8arr = new Uint8Array(n);
                while (n--) {
                    u8arr[n] = bstr.charCodeAt(n)
                }
                return new File([u8arr], `${filename}.${suffix}`, {
                    type: mime
                })
            },
            handleImg(){
                    let imgUrls = this.imgUrls;
                    for (var i=0;i<imgUrls.length;i++)
                    {
                        this.addTextEvent(imgUrls[i])
                    }
            },
            resetButton(){
                    this.inputValueMo = ''
                    let imgUrls = this.imgUrls;
                    for (var i=0;i<imgUrls.length;i++)
                    {
                        imgUrls[i].imgNameActive = ''
                    }
            },
            checkImg(item,index){
                this.showIndex = index
            },
            handleC(type){
                this.showImg = type
            },
            showUpload(){
                        this.dialogVisibleUpload = true
            },
            handleCloseUpload(){
                if(this.inputValueMo){
                    this.resetButton()
                }
                if(this.formDia.caseCoverUrl){
                    this.uploadCancle()
                }
                this.showIndex = 0
                this.dialogVisibleUpload = false
            },
            beforeAvatarUpload(file) {
                let _this = this
                return new Promise((resolve, reject) => {
                    let types = ['image/gif','image/jpeg','image/jpg','image/pjpeg','image/x-png','image/png']
                    const isJPG = types.includes(file.type)
                    let imgType = file.type.split("/")[1]
                    const isLt2M = file.size / 1024 / 1024 < 20
                    if (!isJPG) {
                        _this.$message.error('请上传图片格式')
                        reject()
                    }
                    if (!isLt2M) {
                        _this.$message.error('上传图片大小不能超过 20MB!')
                        reject()
                    }
                    let _URL = window.URL || window.webkitURL;
                    _this.iscropperData = {
                        img: _URL.createObjectURL(file), // 裁剪图片的地址
                        outputType: imgType,//图片格式
                        dialogVisiblex: true,
                        name: file.name,
                        fileType: '2',
                        file: file
                    }
                    this.isXCropper = true
                })
      },
     // 裁剪弹窗关闭后调用
     getXCropper(istype, file) {
        var that = this
        if (istype) {
          const cloneFile = new File([file.img], file.name);
          const formData = new FormData()
          formData.append('file', cloneFile)
          uploadFile(formData)
            .then(response => {
              if(file.fileType=='2'){
                that.getsuccess(response.data,2)
              }
              that.isXCropper = false
            })
        } else {
          this.$refs.elUpload.clearFiles()
          that.isXCropper = false
        }
      },
       getsuccess(data,type=1) {
            if(type ==1){
                    this.form.caseCover = data.fileName;
                    this.form.caseCoverUrl = data.url;
                    this.$refs.addform.validateField('caseCoverUrl');
            }else{
                    this.formDia.caseCover = data.fileName;
                    this.formDia.caseCoverUrl = data.url;
            }
        },
            closeOpp() {
                this.form.unitId = ''
                this.form.unitName = ''
            },
            updateVisible(val) {
                this.unitDialogVisible = val;
            },
            updateUnit(data) {
                this.form.unitName = data.unitName;
                this.form.unitId = data.id;
            },
            chooseunit() {
                    this.unitDialogVisible = true;
                    this.$refs.unitdialog.loadData(this.form.unitId);
            },
            submitPdf(fileList) {
                this.fileListPdf = fileList
                fileList.forEach(item => item.fileType = 4)
                this.form.fileInfoList = fileList
                this.$refs.addform.validateField('fileInfoList')
            },
            getTypeList() {
                getDict('CaseType').then((result) => {
                    this.options = result;
                }).catch((err) => {

                });
            },
            loadInfo() {
                studyshareInfo(this.$route.query.id).then((result) => {
                    this.form = result.data;
                    if (this.$route.query.type == 1) {
                       result.data.fileInfoList.forEach(item =>{
                        item.name = item.fileName
                        item.statsu = 'success'
                        item._url =  addSnop(item.url)
                      })
                      this.fileList = result.data.fileInfoList
                      this.$refs.videoupload.setFileList(this.fileList)
                    }else if(this.$route.query.type == 3){
                      result.data.fileInfoList.forEach(item =>{
                        item.name = item.fileName
                      })
                      this.fileListPdf = result.data.fileInfoList
                      this.$refs.upload2.setFileList(this.fileListPdf)
                    }

                    this.dynamicTags = result.data.label.split(',');
                    this.wangEditor.txt.html(this.form.caseContents);
                }).catch((err) => {

                });
            },
            // 初始化编辑器
            initEditor() {
                this.wangEditor = new E(this.$refs.toolbar, this.$refs.wangeditor);
                this.wangEditor.customConfig = this.wangEditor.customConfig ? this.wangEditor.customConfig : this.wangEditor.config
                this.wangEditor.customConfig.uploadImgShowBase64 = false; // base64存储图片（推荐）
                this.wangEditor.customConfig.uploadImgServer = `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`; // 配置服务器端地址（不推荐）
                this.wangEditor.customConfig.uploadImgHeaders = { }; // 自定义header
                this.wangEditor.customConfig.uploadFileName = "file"; // 后端接受上传文件的参数名
                this.wangEditor.customConfig.uploadImgMaxSize = 2 * 1024 * 1024; // 将图片大小限制为（默认最大支持2M）
                this.wangEditor.customConfig.uploadImgMaxLength = 6; // 限制一次最多上传6张图片
                this.wangEditor.customConfig.uploadImgTimeout = 1 * 60 * 1000; // 设置超时时间（默认1分钟）

                // 配置菜单
                this.wangEditor.customConfig.menus = [
                    "head", // 标题
                    "bold", // 粗体
                    "fontSize", // 字号
                    "fontName", // 字体
                    "italic", // 斜体
                    "underline", // 下划线
                    "strikeThrough", // 删除线
                    "foreColor", // 文字颜色
                    "backColor", // 背景颜色
                    "link", // 插入链接
                    // "list", // 列表
                    "justify", // 对齐方式
                    "quote", // 引用
                    // "emoticon", // 表情
                    "image", // 插入图片
                    // "table", // 表格
                    // "video", // 插入视频
                    // "code", // 插入代码
                    "undo", // 撤销
                    "redo", // 重复
                    "fullscreen" // 全屏
                ];
                this.wangEditor.customConfig.uploadImgHooks = {
                    fail: (xhr, editor, result) => {
                        // 插入图片失败回调
                    },
                    success: (xhr, editor, result) => {
                        // 图片上传成功回调
                    },
                    timeout: (xhr, editor) => {
                        // 网络超时的回调
                    },
                    error: (xhr, editor) => {
                        // 图片上传错误的回调
                    },
                    customInsert: (insertImg, result, editor) => {
                      console.log("url===>",result.data);
                        // 图片上传成功，插入图片的回调（不推荐）
                        insertImg(result.data.url);
                    }
                };
                this.wangEditor.customConfig.onchange = html => {
                    this.form.caseContents = html;
                    this.$refs.addform.validateField('caseContents');
                };
                // 创建富文本编辑器
                this.wangEditor.create();
            },
            // 合同
            chooseContract() {
                if (this.$route.query.contractId) {
                    return
                }
                let params = {
                    id: this.form.contractId,
                    methodName: 'list',
                    className: 'VisitController',
                    opportunityId: ''
                }
                this.$refs.contractRef.selectContractData(params)
                this.contractVisible = true;
            },
            updateContractData(data) {
                this.form.contractName = data.contractTitle;
                this.form.contractId = data.id;
            },
            updateContractVisible(val) {
                this.contractVisible = val;
            },
            submitVideoSuccess(datas) {
                datas.forEach(item => item.fileType = 2)
                this.form.fileInfoList = datas
            },
            submitPdfSuccess(datas) {
                datas.forEach(item => item.fileType = 4)
                this.form.fileInfoList = datas
                this.$refs.addform.validateField('fileInfoList')
            },
            submitCoverSuccess(datas) {
                if (datas.length > 0) {
                    var file = datas[0];
                    this.form.caseCover = file.name;
                    this.form.caseCoverUrl = file.url;
                } else {
                    this.form.caseCover = '';
                    this.form.caseCoverUrl = '';
                }
                this.$refs.addform.validateField('caseCoverUrl');
            },
            handleInputConfirm() {
                if (this.inputValue.length > 0) {
                    this.dynamicTags.push(this.inputValue)
                    this.form.label = this.dynamicTags.length > 0 ? this.dynamicTags.join(',') : ''
                    this.inputValue = ''
                    this.$refs.addform.validateField('label')
                }
            },
            clickInput() {
                if (this.$refs.saveTagInput) {
                    this.$refs.saveTagInput.focus();
                }
            },
            deleteTag() {
                console.log(this.inputValue.length);
            },
            handleClose(tag) {
                var index = this.dynamicTags.indexOf(tag);
                this.dynamicTags.splice(index);
                this.form.label = this.dynamicTags.length > 0 ? this.dynamicTags.join(',') : ''
                this.$refs.addform.validateField('label')
            },

            // 保存
            submitAction() {
                this.$refs['addform'].validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                    if (this.form.id) {
                        this.uploadData();
                    } else {
                        this.addData();
                    }
                });
            },
            uploadData() {
                this.isSubmit = true;
                updatestudyshare(this.form).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '更新成功'
                        })
                        this.$router.back();
                    } else {
                        this.$message({
                            type: "error",
                            message: result.msg
                        })
                    }
                    this.isSubmit = false;
                }).catch((err) => {
                    this.isSubmit = false;
                });
            },
            addData() {
                this.isSubmit = true;
                addstudyshare(this.form).then((result) => {
                    if (result.data) {
                        sessionStorage.setItem('isAddStudy','true')
                        this.$router.back();
                    } else {
                        this.$refs.msgdialog.show({
                          type:'error',
                          title:"提交失败",
                          msg:result.msg,
                        })
                    }
                    this.isSubmit = false;
                }).catch((err) => {
                    this.isSubmit = false;
                });
            },
        }
    }
</script>
<style scoped>
    .required-star {
        color: #F56C6C;
        margin-right: 4px;
    }
    .bright{
        float: right;
    }
    .ccss{
        text-align: center;
        display: block;
    }
    .uploadel{
        height: 540px;
        padding-top: 20px;
    }
    .resetcss{
        height: 18px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #4285F4;
        float: left;
        margin-left: 12px;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
    }
    .spanl{
        vertical-align: middle;
        display: inline-block;
        height: 40px;
        line-height: 40px;
    }
    .elInput{
        float: left;
        width: 589px;
    }
    .quan{
        position: absolute;
        right: 10px;
        top: 10px;
    }
    .lidiv{
        width: 325px;
        height: 244px;
        border-radius: 0px 0px 0px 0px;
        margin-bottom: 20px;
        cursor: pointer;
        position: relative;
    }
    .flexul{
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-top: 20px;
    }
    .checkss{
        position: absolute;
        right: 0;
        top: 0;
    }
    .ml20{
        margin-left: 20px;
    }
    .dbcss{
        padding: 11px 24px;
        height: 42px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #D6D6D6;
        text-align: center;
        cursor: pointer;
        position: relative;
    }
    .dbutton{
        display: flex;
    }
    .dlcdialog /deep/.el-dialog{
        padding: 20px;
        width: 1060px;
        height: 680px;
        background: #FFFFFF;
        border-radius: 8px 8px 8px 8px;
    }
    .dlcdialog /deep/.el-dialog__footer{
        border-top: 0;
        padding: 0;
    }
    .dlcdialog /deep/.el-dialog__body{
        padding: 0;
        height: 640px;
    }
    .dlcdialog /deep/.el-dialog__header{
        border-bottom: 0;
        padding: 0;
    }
    .bordediv{
            display: inline-block;
            text-align: center;
            cursor: pointer;
            outline: 0;
            width: 88px;
            height: 88px;
            line-height: 24px;
            background: #F5F5F5;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            border: 1px dashed #CCCCCC;
            text-align: center;

    }
    .bordediv i {
        margin-top: 20px;
        font-size: 24px;
        color: #4285F4;
    }
    .avatar{
        width: 100px;
        height: 100px;
        cursor: pointer;
    }
    .uploadcss {
  width: 88px;
  height: 88px;
  line-height: 24px;
  background: #F5F5F5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #CCCCCC;
  display: inline-block;
}

.uploadcss i {
  margin-top: 20px;
  font-size: 24px;
  color: #4285F4;
}

.el-icon-plus {
  font-size: 30px !important;
}
    .studiocss img {
        width: 16px;
        height: 16px;
        margin-right: 3px;
        margin-top: -3px;
    }

    .filecss img {
        width: 16px;
        height: 16px;
        margin-right: 3px;
        margin-top: -3px;
    }

    .wangeditor {
        border: 1px solid #e6e6e6;
        box-sizing: border-box;

    }

    .wangeditor /deep/.w-e-toolbar {
        z-index: 1 !important;
    }

    .wangeditor /deep/.w-e-text-container {
        z-index: 0 !important
    }

    .wangeditor .toolbar {
        border-bottom: 1px solid #e6e6e6;
        box-sizing: border-box;
    }

    .wangeditor .text {
        min-height: 300px;
    }

    .uploadtext {
        color: #4285F4;
        cursor: pointer;
    }

    .uploadcss {
        width: 88px;
        height: 88px;
        line-height: 24px;
        background: #F5F5F5;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px dashed #CCCCCC;
        text-align: center;
    }

    .uploadcss i {
        margin-top: 20px;
        font-size: 24px;
        color: #4285F4;
    }

    .pr10 {
        padding-right: 10px;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #999999;
        line-height: 34px;
    }

    .w98 {
        width: 98px;
    }

    .flex {
        display: flex;
        flex-direction: column;
    }

    .rcenter {
        position: absolute;
        right: 10px;
        line-height: 34px;
        font-size: 14px;
        color: #c0c4cc;
    }

    .btncenter {
        text-align: center;
    }

    .quanxiancss /deep/.el-form-item__label {
        margin-left: -7px;
        width: 125px !important;
    }

    .addresscss {
        max-height: 68px;
    }

    .input-new-tag {
        width: 200px;
        margin-left: -8px;

    }

    .input-new-tag /deep/.el-input__inner {
        border: none;
        background-color: rgba(0, 0, 0, 0);
    }

    .tagcss /deep/ .el-icon-close {
        width: 12px;
        height: 12px;
        line-height: 12px;
        color: #4285F4;
    }

    .tagcol /deep/.el-form-item__label {
        height: 34px;
        line-height: 34px;
    }

    .tagcol /deep/.el-form-item__content {
        /* background: #F5F5F5; */
        border: 1px solid #DCDFE6;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        min-height: 34px;
        line-height: 34px;
    }

    .tagcss {
        margin-left: 8px;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #4285F4;
        background: #DFEAFD;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
    }

    .mainbg {
        margin: 16px 0px;
        padding: 20px 16px;
        background-color: white;
        border-radius: 8px;
    }

    .pd0 {
        padding-right: 0px !important;
    }
</style>
<style>

</style>
