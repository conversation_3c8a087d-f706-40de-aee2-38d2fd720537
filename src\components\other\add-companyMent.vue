<!-- 复制 添加岗位弹框 -->
<template>
  <el-dialog
    :title="subtitle"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="50%">
    <el-form :model="dataForm" :rules="Rule.COMPANY_POSITION" ref="dataForm" label-width="120px">
      <!-- <el-form-item label="所属公司：" prop="companyName" >
        <el-input v-model="companyName" disabled></el-input>
      </el-form-item> -->
      <el-form-item label="所属部门：" prop="departmentName" >
        <el-input v-model="departmentName" disabled></el-input>
      </el-form-item>
      <el-form-item label="岗位：" prop="name">
        <el-input v-model="dataForm.name" placeholder="岗位名称"></el-input>
      </el-form-item>
      <el-row class="area-box">
        <el-col :span="12">
          <el-form-item label="所属地区权限：">
            <el-tree
              v-model="areaList"
              :data="dataArea"
              show-checkbox
              node-key="id"
              ref="treeArea"
              highlight-current
              :props="defaultProps">
            </el-tree>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属权限：">
            <div class="company">
              <span style="font-size: 12px; color: #C0C4CC;">点击按钮单选，点击文字多选</span>
              <el-tree
                :data="permissionList"
                show-checkbox
                node-key="id"
                check-on-click-node
                :default-expand-all="isDialog"
                :check-strictly="true"
                :expand-on-click-node="false"
                @node-click="nodeClick"
                ref="treePermission"
                :props="defaultProps">
              </el-tree>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleTree">展开收缩所属权限</el-button>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>

export default {
  data () {
    return {
      isDialog: true,
      areaList: [],
      visible: false,
      companyType: '',
      companyName: '',
      departmentName: '',
      permissionList: [],
      dataArea: [],
      dataForm: {
        companyId: '',
        departmentId: '',
        name: '',
        resourceIds: '',
        areas: ''
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      subtitle: '',
      paramsCompanyId: ''
    }
  },
  methods: {
    handleTree () {
      this.isDialog = !this.isDialog;
      for (let i = 0; i < this.$refs.treePermission.store._getAllNodes().length; i++) {
        this.$refs.treePermission.store._getAllNodes()[i].expanded = this.isDialog
      }
    },
    nodeClick (data, node) {
      this.childNodesChange(node);
      this.parentNodesChange(node);
    },
    childNodesChange (node) {
      let len = node.childNodes.length;
      for (let i = 0; i < len; i++) {
        node.childNodes[i].checked = node.checked;
        this.childNodesChange(node.childNodes[i])
      }
    },
    parentNodesChange (node) {
      if (node.parent) {
        for (let key in node) {
          if (key === 'parent') {
            node[key].checked = true;
            this.parentNodesChange(node[key])
          }
        }
      }
    },
    // 地区树原数据
    async getDataRequest () {
      this.permissionList = this.$store.state.resourList.permissionMenu // 权限数据
      let res = await this.$axios.get('/sf/business/companyposition/areaListByCompany', { params: {companyId: this.paramsCompanyId} });
      if (res.status === 0) {
        this.dataArea = res.data
      }
    },
    async init (flag, obj, parent) {
      this.paramsCompanyId = obj.companyId;
      this.dataForm.companyId = obj.companyId
      this.getDataRequest()
      this.visible = true
      if (flag === 'add') {
        // this.dataForm.companyId = parent.id || '';
        // this.companyName = parent.name || '';
        this.companyType = obj.dataType; // 区别部门 和 岗位
        this.dataForm.departmentId = obj.id; // 部门id
        this.departmentName = obj.name; // 部门姓名
        this.subtitle = '新增';
        this.dataForm.name = '';
        this.$nextTick(() => {
          this.$refs.treeArea.setCheckedKeys([]);
          this.$refs.treePermission.setCheckedKeys([]);
        })
      } else if (flag === 'update') {
        this.subtitle = '修改';
        this.companyType = obj.dataType;
        let id = obj.id;
        this.dataForm.id = id;
        let res = await this.$axios.get(`/sf/business/companyposition/info/${id}`)
        if (res.status === 0) {
          this.dataForm.name = res.data.positionName; // 回填岗位名称
          this.dataForm.departmentId = res.data.departmentId; // 回填 部门id
          this.departmentName = res.data.departmentName; // 回填部门 名称
          this.dataForm.companyId = res.data.companyId; // 回填公司id
          this.companyName = res.data.companyName; // 回填公司名称
          let ary = [];
          let resourceAry = [];
          res.data.areaVoList.map(item => {
            ary.push(item.areaId)
          })
          res.data.resourceVoList.map(item => {
            resourceAry.push(item.resourceId)
          })
          this.$refs.treeArea.setCheckedKeys(ary);
          this.$refs.treePermission.setCheckedKeys(resourceAry);
        }
      }
    },
    // 处理数据
    getAreaList (num) {
      let arytype = '';
      let ary = [];
      if (num === 1) {
        ary = this.$refs.treeArea.getCheckedKeys();
      } else {
        ary = this.$refs.treePermission.getCheckedKeys();
      }
      if (ary instanceof Array) {
        arytype = ary.join(',')
      }
      return arytype
    },
    // 表单提交
    dataFormSubmit () {
      let that = this;
      this.dataForm.areas = this.getAreaList(1);
      this.dataForm.resourceIds = this.getAreaList(2);
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          let url = this.companyType === 2 ? '/sf/business/companyposition/save' : '/sf/business/companyposition/update';
          let res = await this.$axios.post(`${url}`, this.dataForm)
          if (res.status === 0) {
            this.$message({ message: '操作成功', type: 'success' });
            setTimeout(() => {
              this.visible = false;
              that.$emit('refreshDataList')
            }, 500);
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.area-box{
  height: 500px;
  overflow-y: scroll;
}
</style>
