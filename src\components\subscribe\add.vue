<template>
    <div>
      <div>
        <back>{{ form.id ? '编辑教材报订' : '新建教材报订' }}</back>
      </div>
      <div class="mainbg" v-loading="isLoading">
        <el-form
          ref="addform"
          :model="form"
          :rules="rules"
          class="addfcss"
          label-width="118px"
        >
          <textBorder>基础信息</textBorder>

          <div class="pt20 bbline">
            <el-row :gutter="0" type="flex" justify="start">
              <el-col :span="10">
                <el-form-item label="客户:" prop="customerId">
                  <div class="unitbtn" @click="chooseCustomer">
                    <span v-if="form.customerId" class="deffont">{{
                      form.customerName
                    }}</span>
                    <span v-else class="pltcss">请选择客户</span>
                    <i class="rcenter el-icon-arrow-down" />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="用书单位：" prop="receiveUnit">
                  <div class="unitbtn">
                    <span v-if="form.unitId" class="deffont">{{
                      form.unitName
                    }}</span>
                    <span v-else class="pltcss">请选择用书单位</span>
                    <i class="rcenter el-icon-arrow-down" />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="0" type="flex" justify="start">
              <el-col :span="10">
                <el-form-item label="中标公司：" prop="bidwinnerId">
                  <el-select
                    clearable=""
                    class="definput ellipsis"
                    popper-class="removescrollbar"
                    v-model="form.bidwinnerId"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in companyList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="useBookYear" label="用书时间：">
                  <el-date-picker
                    class="definput wid100"
                    v-model="useBookYear"
                    type="year"
                    format="yyyy"
                    value-format="yyyy年"
                    placeholder="选择年"
                    @change="changeUseBookYear"
                  >
                  </el-date-picker>
                  <span class="pdl">
                    <el-radio class="mr10" v-model="radio" label="春季"
                      >春季</el-radio
                    >
                    <el-radio class="mr10" v-model="radio" label="秋季"
                      >秋季</el-radio
                    >
                  </span>
                </el-form-item>

              </el-col>
            </el-row>
            <el-row :gutter="0" type="flex" justify="start">
              <el-col :span="10">
                <el-form-item label="货源:" prop="source">
                  <el-input
                    class="definput"
                    v-model="form.sourceGoods"
                    maxlength="30"
                    show-word-limit
                    clearable
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <textBorder class="mt30">教材</textBorder>
          <div class="right">折扣支持两位小数，折扣范围为0.01～1.00，如75折可输入0.75。</div>
          <div class="pt20 bbline">
            <el-table class="mytable" :data="bookList">
              <el-table-column
                width="200"
                label="教材名称"
                prop="name"
                align="center"
              >
                <template slot-scope="scope">
                  <div class="flex">
                    <div class="unitbtn2" :class="{disabled:$route.query.id}">
                      <span v-if="scope.row.goodsId" class="deffont">{{
                        scope.row.name
                      }}</span>
                    </div>
                    <span
                      class="pltcss2"
                      v-if="!$route.query.id"
                      @click="chooseBook(scope.$index, scope.row)"
                      >选择</span
                    >
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="ISBN"
                prop="isbn"
                align="center"
                min-width="167px"
              ></el-table-column>
              <el-table-column
                label="出版社"
                prop="platformName"
                align="center"
                min-width="167px"
              ></el-table-column>
                            <el-table-column
                width="170"
                label="用书专业"
                prop=""
                align="center"
              >
                <template slot-scope="scope">
                  <el-select
                    size="mini"
                    popper-class="removescrollbar"
                    v-model="scope.row.bookSpecialty"
                    :placeholder="
                      form.customerId ? '请选择' : '请先选择用书专业'
                    "
                  >
                    <el-option
                      v-for="item in majorList"
                      :key="item.id"
                      :label="item.specialtyName"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                label="价格"
                fixed="right"
                prop="price"
                align="center"
                min-width="100px"
              ></el-table-column>
              <el-table-column
                width="140"
                label="预定数量"
                fixed="right"
                prop="number"
                align="center"
              >
                <template slot-scope="scope">
                  <el-input-number
                    :controls="false"
                    :disabled="$route.query.id?true:false"
                    :precision="0"
                    :step="1"
                    :min="1"
                    size="mini"
                    placeholder="设置预定数量"
                    v-model="scope.row.number"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column
                v-if="$route.query.id"
                width="140"
                label="折扣"
                fixed="right"
                prop="number"
                align="center"
              >
                <template>
                  <el-input-number
                    :controls="false"
                    :precision="2"
                    :step="0.01"
                    :min="0.01"
                    :max="1.00"
                    size="mini"
                    placeholder="设置折扣"
                    v-model="form.discount"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column v-if="!$route.query.id" label="操作" fixed="right" align="center">
                <template slot-scope="scope">
                  <span class="rbtn"  @click="delBook(scope.$index,scope.row)"
                    >删除</span
                  >
                </template>
              </el-table-column>
            </el-table>
            <div class="bottomView" v-if="!$route.query.id">
              <el-button type="text" icon="el-icon-plus" @click="addBook"
                >添加教材</el-button
              >
            </div>
          </div>
          <textBorder class="mt30">补充信息</textBorder>
          <div class="pt20">
            <el-row :gutter="0" type="flex" justify="start">
              <el-col :span="20">
                <el-form-item label="备注：" prop="notes">
                  <el-input
                    class="definput"
                    v-model="form.notes"
                    maxlength="300"
                    show-word-limit
                    type="textarea"
                    rows="4"
                    placeholder="请输入备注信息"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="pt20 btncenter">
            <el-form-item>
              <el-button
                class="btn_h42 wid98"
                type="primary"
                @click="submitForm"
                v-dbClick
                >保存</el-button
              >
            </el-form-item>
          </div>
        </el-form>
      </div>

      <!-- 客户 -->
      <cuatomerDialog
        ref="customer"
        :visible.sync="customerDialogVisible"
        @updateVisible="updateVisible"
        @updateCustomer="updateCustomer"
      >
      </cuatomerDialog>
      <!-- 选择教材 -->
      <chooseBookDialog
        ref="bookdialog"
        :visible.sync="bookDialogVisible"
        @updateVisible="updateBookVisible"
        @updateData="updateBookData"
        :disableIds="disableIds"
      >
      </chooseBookDialog>

      <verifyDeparment
      ref="verifyDeparment"
      @submit="add"></verifyDeparment>

    </div>
  </template>

  <script>
  import back from '@/components/common/back.vue'
  import textBorder from '@/components/common/textBorder.vue'
  import cuatomerDialog from '@/components/common/customerDialog.vue'
  import chooseBookDialog from '@/components/common/chooseBookDialog.vue'
  import {
    baodingsave,
    baodingupdate,
    baodinginfo
  } from '@/api/baoding/index'
  import { selectUintSpecialty } from '@/api/clientmanagement/customer'
  import { getDict } from '@/utils/tools.js'
  import verifyDeparment from '@/components/common/verifyDeparment.vue';
  import { queryDictTypeList } from "@/api/wapi";
  export default {
    components: {
      back,
      textBorder,
      cuatomerDialog,
      chooseBookDialog,
      verifyDeparment
    },
    data() {
      return {
        majorList: [],
        bookList: [],
        bookDialogVisible: false,
        customerDialogVisible: false,
        isLoading: false,
        companyList: [],
        rules: {
          sendNumber: [
            { required: true, message: '请输入发货量', trigger: 'blur' },
          ],
          materialId: [
            { required: true, message: '请选择教材', trigger: 'change' },
          ],
          useBookYear: [
            { required: true, message: '请选择用书时间', trigger: 'change' },
          ],

          useBookSpecialty: [
            { required: true, message: '请选择用书专业', trigger: 'change' },
          ],
          unitId: [
            { required: true, message: '请选择用书单位', trigger: 'change' },
          ],
          customerId: [
            { required: true, message: '请选择客户', trigger: 'change' },
          ],
          bidwinnerId: [
            { required: true, message: '请选择中标公司', trigger: 'change' },
          ],
        },
        form: {
          id: '',
          useBookYear: '',
          customerId: '',
          unitId: '',
          bidwinnerId: '',
          sourceGoods:'',
          notes:'',
          departmentId:'',
          materialInfoReqList:[],
        },
        isChooseCustomer: false,
        useBookYear: '',
        radio: '春季',
        disableIds:[],
      }
    },
    created() {
      if (this.$route.query.customerId) {
        this.isChooseCustomer = true
        this.form.customerId = this.$route.query.customerId
        this.form.customerName = this.$route.query.customerName
        this.form.unitId = this.$route.query.unitId
        this.form.unitName = this.$route.query.unitName
        this.loadMajor()
      } else {
        this.isChooseCustomer = false
      }
      this.getCompany()
      if(this.$route.query.id){
        this.loadInfo()
      }

    },
    methods: {
      delBook(index, data) {
        this.bookList.splice(index, 1)
      },
      addBook() {
        if (this.bookList.length>=100) {
          this.$message.error('最多选择100种教材')
          return
        }
        this.bookList.push({
            "goodsId":'',
            "name":'',
            "price":'',
            "isbn":'',
            "reserveNum":undefined,
            "bookSpecialty":''
        })
      },
      chooseBook(index, row) {
        this.curSelId = row.goodsId || ''
        this.disableIds = []
        this.bookList.forEach(element => {
          if ( element.goodsId && element.goodsId != this.curSelId) {
            this.disableIds.push(element.goodsId)
          }
        });
        this.curBookIndex = index;
        this.curBookRow = row;
        this.bookDialogVisible = true;
        this.$nextTick(() => {
          this.$refs.bookdialog.loadBookData({
            // opportunityId: this.form.id ? this.form.id : "",
            type: 2,
          },this.curSelId);
        });
      },
      updateBookVisible(val) {
        this.bookDialogVisible = val;
      },
      updateBookData(data) {
        if (data) {
          data.goodsId = data.id;
          this.curBookRow = Object.assign(this.curBookRow,data);
          this.bookList.splice(this.curBookIndex, 1, this.curBookRow);
        }else{
          this.curBookRow = {}
          this.bookList.splice(this.curBookIndex, 1, this.curBookRow);
        }
      },
      getCompany() {
        queryDictTypeList({code:'Company'}).then((result) => {
          this.companyList = result.data['Company'][0].dictItemVoList || []
        }).catch((err) => {

        });
      },
      loadMajor() {
        selectUintSpecialty({
          unitId: this.form.unitId,
        })
          .then((result) => {
            this.majorList = result.data
          })
          .catch((err) => {})
      },
      loadInfo() {
        var id = this.$route.query.id
        if (id) {
          this.form.id = id
          this.isLoading = true
          baodinginfo(id)
            .then((result) => {
              this.form = result.data
              this.form.discount = result.data.discount > 0 ? result.data.discount : 1;
              var useBookYear = this.form.useBookYear
              if (useBookYear) {
                var index = useBookYear.indexOf('年')
                this.useBookYear = useBookYear.substring(0, index + 1)
                this.radio = useBookYear.substring(index + 1, useBookYear.length)
              }
                this.bookList = [{
                  "id":this.form.materialId,
                  "goodsId":this.form.materialId,
                  "name":this.form.materialName,
                  "price":this.form.price,
                  "isbn":this.form.isbn,
                  "number":this.form.reserveNum,
                  "bookSpecialty":this.form.useBookSpecialty,
                  discount:this.form.discount > 0 ? this.form.discount : 1,
                  platformName:this.form.platformName
                }]
                console.log('dddddddd',this.bookList);
              if (this.form.unitId) {
                this.loadMajor()
              }
              this.isLoading = false
            })
            .catch((err) => {
              this.isLoading = false
            })
        }
      },
      updateCustomer(data) {
        if (data.unitId != this.form.unitId ) {
          this.bookList && this.bookList.forEach(item =>{
            item.bookSpecialty = ''
          })
        }
        this.form.customerName = data.customerName
        this.form.customerId = data.id
        this.form.unitId = data.unitId
        this.form.unitName = data.unitName
        this.$refs.addform.validateField('customerId')
        if (this.form.unitId) {
          this.form.useBookSpecialty = ''
          this.loadMajor()
        }
      },
      chooseCustomer() {
        if (this.isChooseCustomer) {
          return
        }
        this.customerDialogVisible = true
        this.$refs.customer.selectCustomerData({
          id: this.form.customerId,
          unitId: '',
          className: 'MaterialSubscriptionController',
        })
      },
      updateVisible(val) {
        this.customerDialogVisible = val
      },



      submitForm() {
        this.$refs['addform'].validate((valid) => {
          if (!valid) {
            return false
          }

          if (this.radio && this.useBookYear) {
            this.form.useBookYear = `${this.useBookYear}${this.radio}`
          }
          if ( !this.validateBookList()) {
            return
          }
          if (this.form.id) {
            // 编辑
            this.update()
          } else {
            // 添加
            this.$refs.verifyDeparment.verify()
          }
        })
      },
      update(){
        this.form.useBookSpecialty = this.bookList[0].bookSpecialty
        this.form.dictItemId = this.getspecialtyId(this.bookList[0].bookSpecialty)
        delete this.form.createTime
          baodingupdate(this.form)
          .then((result) => {
            if (result.data) {
              this.$message({
                type: 'success',
                message: '更新成功！',
              })
              this.$router.back()
            } else {
              this.$message({
                type: 'error',
                message: '保存失败！',
              })
            }
          })
          .catch((err) => {})
      },
      add(departmentId){
        this.form.departmentId = departmentId;
        this.form.materialInfoReqList = this.getBookList()
        baodingsave(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功！',
            })
            this.$router.back()
          } else {
            this.$message({
              type: 'error',
              message: '保存失败！',
            })
          }
        })
        .catch((err) => {})
      },
      getBookList(){
        var list = []
        this.bookList.forEach(item => {
          list.push({
            "materialId":item.goodsId,
            "name":item.name,
            "isbn":item.isbn,
            "price":item.price,
            "reserveNum":item.number,
            "useBookSpecialty":item.bookSpecialty,
            'dictItemId':this.getspecialtyId(item.bookSpecialty)
          })
        });
        return list
      },
      getspecialtyId(id){
        const data = this.majorList.filter(item => item.id === id)
        return data[0].specialtyId
      },
      validateBookList(){
        var isValidate = true
        if (this.bookList.length==0) {
          isValidate = false
          this.$message.error(`请完善教材信息`)
        }else{
          for (let index = 0; index < this.bookList.length; index++) {
            const element = this.bookList[index];
            if (!element.id){
              this.$message.error(`请完善第${index+1}行的教材信息`)
              isValidate = false
              return
            }
            if (!element.bookSpecialty) {
              this.$message.error(`${element.name}的用书专业为空，请设置`)
              isValidate = false
              return
            }
            if (!element.number) {
              this.$message.error(`${element.name}的数量为空，请设置`)
              isValidate = false
              return
            }

          }
        }

        return isValidate;
      },


      changeUseBookYear(data) {
        if (this.radio && this.useBookYear) {
          this.form.useBookYear = `${this.useBookYear}${this.radio}`
        }
      },
      updateUnit(data) {
        this.form.unitName = data.unitName
        this.form.unitId = data.id
        this.$refs.addform.validateField('unitId')
        if (this.form.unitId) {
          this.loadMajor()
        }
      },
    },
  }
  </script>
  <style>
  </style>
  <style scoped>
  .rbtn{
    cursor: pointer;
  }
  .pltcss2 {
    color: #4285f4;
    font-size: 13px;
    line-height: 28px;
    cursor: pointer;
  }
  .unitbtn2 {
    width: calc(100% - 50px);
    background-color: white;
    color: #333;
    position: relative;
    border: 1px solid #dcdfed;
    border-radius: 4px;
    line-height: 26px;
    height: 28px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .disabled{
    border: none !important;
  }
  .bottomView {
    text-align: center;
    padding-bottom: 10px;
  }
  .wi {
    width: 100% !important;
  }
  .wi /deep/.el-input__inner {
    text-align: left !important;
  }
  .matdetail p {
    line-height: 30px;
  }
  .matdetail {
    position: relative;
  }
  .tr {
    position: absolute;
    top: -30px;
    right: 0;
  }
  .wid100 {
    width: calc(100% - 130px);
    margin-right: 10px;
  }
  .wid200{
    width: 400px;
  }
  .wid2 {
    width: calc(100% - 100px);
  }
  .wid {
    width: 100px;
  }
  .tagcss {
    margin-right: 8px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285f4;
    background: #dfeafd;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
  }

  .studiocss img {
    width: 16px;
    height: 16px;
    margin-right: 3px;
    margin-top: -3px;
  }

  .uploadtext {
    color: #4285f4;
    cursor: pointer;
  }

  .pltcss {
    color: #cdcdcd;
  }

  .unitbtn {
    color: #333333;
    position: relative;
  }

  .width100 {
    width: 100%;
  }

  .wid98 {
    width: 98px;
  }

  .rcenter {
    position: absolute;
    right: 10px;
    line-height: 34px;
    font-size: 14px;
    color: #c0c4cc;
  }

  .btncenter {
    text-align: center;
  }

  .quanxiancss /deep/.el-form-item__label {
    margin-left: -7px;
    width: 125px !important;
  }

  .mainbg {
    margin: 16px 0px;
    padding: 20px 16px;
    background-color: white;
    border-radius: 8px;
  }

  .pd0 {
    padding-right: 0px !important;
  }
  .flex {
    display: flex;
  }
  .flex span {
    margin-left: 10px;
  }
  .ellipsis /deep/.el-input__inner {
    padding: 0 8px !important;
  }
  .right{
    margin-bottom: 10px;
    color: #999999;
  }
  </style>
