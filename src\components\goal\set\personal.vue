<template>
  <div>
    <div class="flexcss" v-loading="isLoading">
      <div>
        <el-button class="defaultbtn" v-if="currentYear <= year" type="primary" @click="saveGoalAction" v-dbClick>保存设置</el-button>
      </div>
      
      <div class="textAlignRight">
        选择年份：<el-select class="definput width200" popper-class="removescrollbar" @change="changeYear"  v-model="year" placeholder="请选择年度"> 
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </div>
    </div>
    
    <div class="owntop">
        <div class="ownleft">
          <textBorder>个人年度目标设置</textBorder>
          <el-form class="mt elform myform" :label-position="labelPosition" :disabled="currentYear > year" label-width="100px" :model="formLabelAlign">
            <el-form-item label="新增客户：">
              <el-input class="definput"  v-model="formLabelAlign.newCustomers" type="number"> <template slot="append">个</template></el-input>
            </el-form-item>
            <el-form-item label="新增拜访：">
              <el-input class="definput" v-model="formLabelAlign.newVisit" type="number"><template slot="append">个</template></el-input>
            </el-form-item>
            <el-form-item  label="新增机会：">
              <el-input class="definput" v-model="formLabelAlign.newOpportunity" type="number"><template slot="append">次</template></el-input>
            </el-form-item>
            <el-form-item label="新增合同：">
              <el-input class="definput" v-model="formLabelAlign.newContract" type="number"><template slot="append">个</template></el-input>
            </el-form-item>
            <el-form-item label="合同金额：">
              <el-input class="definput" v-model="formLabelAlign.contractAmount" type="number"><template slot="append">万元</template></el-input>
            </el-form-item>
            <el-form-item label="回款金额：">
              <el-input class="definput" v-model="formLabelAlign.contractReturnAmount" type="number"><template slot="append">万元</template></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="ownright">
          <div class="rela">
            <textBorder>单位年度目标设置</textBorder>
            <el-button  class="defaultbtn  asbu" v-if="currentYear <= year" icon="el-icon-plus" type="primary" @click="chooseunit">选择单位</el-button>
          </div>
          <div class="tablebox">
            <el-table class=" mytable" v-for="(item,index) in unitGoalList" :key="index"  :data="item" :span-method="objectSpanMethod" border >
              <el-table-column prop="unitName" label="学校" >
                <template slot-scope="scope">
                  <div>{{ scope.row.unitName }} <img class="deleteimg" src="../../../assets/delete.png" alt="" @click="deleteUnitGoal(scope.row)"></div>
                </template>
              </el-table-column>
              <el-table-column prop="unitgoalTypeName" label="目标维度" ></el-table-column>
              <el-table-column prop="goalNum" label="目标">
                <template v-slot="{ row }">
                  <el-input size="normal"  v-model="row.goalNum" type="number" :disabled="currentYear > year"   clearable></el-input>
                </template>
              </el-table-column>
          </el-table>
          </div>
      
        </div>

        
    </div>
    <div class="mubiao">
          <div class="">
            <textBorder>目标细分</textBorder>
            <span class="rtextcss">提示：请保证提交时，细分目标数据合计与个人年度目标中的数值一致。</span>
          </div>
          <div class="">
            <el-table
             class="owntable "
             :data="tableData"
             :min-height="385"
             border
            >
              <el-table-column
                prop="goalType"
                label="目标"
                width="167px"
                align="center"
                >
                <template slot-scope="scope">
                  <div>
                    {{scope.row.goalTypeName}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="sum"
                label="合计"
                align="center"
                >
              </el-table-column>

              <el-table-column
                prop="january"
                :label="`${this.year}-01`"
                align="center"
                width="100px"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number"  v-model="row.january" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.january}}</div>
                </template>
              </el-table-column>
              
              <el-table-column
                prop="february"
                :label="`${this.year}-02`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number"  v-model="row.february" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.february}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="march"
                :label="`${this.year}-03`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number"  v-model="row.march" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.march}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="april"
                :label="`${this.year}-04`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear <= year" type="number"  v-model="row.april" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.april}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="may"
                :label="`${this.year}-05`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number"  v-model="row.may" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.may}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="june"
                :label="`${this.year}-06`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number"  v-model="row.june" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.june}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="july"
                :label="`${this.year}-07`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number"  v-model="row.july" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.july}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="august"
                :label="`${this.year}-08`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number" v-model="row.august" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.august}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="september"
                :label="`${this.year}-09`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number"  v-model="row.september" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.september}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="october"
                :label="`${this.year}-10`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal" v-if="currentYear<=year" type="number"  v-model="row.october" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.october}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="november"
                :label="`${this.year}-11`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input size="normal"  v-if="currentYear<=year" type="number"  v-model="row.november" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.november}}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="december"
                :label="`${this.year}-12`"
                align="center"
                >
                <template v-slot="{ row }">
                  <el-input  v-if="currentYear <= year" size="normal" type="number" v-model="row.december" @input="changeNum(row)" clearable></el-input>
                  <div v-else>{{row.december}}</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <unitDialog 
          ref="unitdialog"
          :visible.sync="unitDialogVisible" 
          className="PersonalGoalsController"
          @updateVisible="updateVisible" 
          @updateUnit="updateUnit"></unitDialog>
  </div>
</template>

<script>
import textBorder from '../../common/textBorder.vue';
import unitDialog from '../../common/unitDialog.vue';
import {deepClone} from '@/utils/tools.js'
import { addpersonalgoals,updatepersonalgoals,personalgoalsInfo,getYearList,personalgoalsunitDelete } from "@/api/goal";
export default {
  data(){
    return{
      isLoading:false,
      year:2023,
      tableData:[],
      goalDetailList: [{
          goalType: 1,
          goalTypeName: '新增客户',
          goalTypeKey:'newCustomers',
          sum:0,
          january:'',
          february:'',
          march:"",
          april:"",
          may:"",
          june:"",
          july:"",
          august:"",
          september:"",
          october:"",
          november:"",
          december:"",
          year:this.year,
        }, {
          goalType: 2,
          goalTypeName: '新增拜访',
          goalTypeKey:'newVisit',
          sum:0,
          january:'',
          february:'',
          march:"",
          april:"",
          may:"",
          june:"",
          july:"",
          august:"",
          september:"",
          october:"",
          november:"",
          december:"",
          year:this.year,
        }, {
          goalType: 3,
          goalTypeName: '新增机会',
          goalTypeKey:'newOpportunity',
          sum:0,
          january:'',
          february:'',
          march:"",
          april:"",
          may:"",
          june:"",
          july:"",
          august:"",
          september:"",
          october:"",
          november:"",
          december:"",
          year:this.year,
        }, {
          goalType: 4,
          goalTypeName: '新增合同',
          goalTypeKey:'newContract',
          sum:0,
          january:'',
          february:'',
          march:"",
          april:"",
          may:"",
          june:"",
          july:"",
          august:"",
          september:"",
          october:"",
          november:"",
          december:"",
          year:this.year,
        }, {
          goalType: 5,
          goalTypeName: '合同金额（万元）',
          goalTypeKey:'contractAmount',
          sum:0,
          january:'',
          february:'',
          march:"",
          april:"",
          may:"",
          june:"",
          july:"",
          august:"",
          september:"",
          october:"",
          november:"",
          december:"",
          year:this.year,
        },
      {   
        goalType: 6,
        goalTypeName: '回款金额（万元）',
        goalTypeKey:'contractReturnAmount',
        sum:0,
        january:'',
        february:'',
        march:"",
        april:"",
        may:"",
        june:"",
        july:"",
        august:"",
        september:"",
        october:"",
        november:"",
        december:"",
        year:this.year,

      }],
      keyDict:{
        1:'newCustomers',
        2:'newVisit',
        3:'newOpportunity',
        4:'newContract',
        5:'contractAmount',
        6:'contractReturnAmount',
      },
      nameDict:{
        1:'新增客户',
        2:'新增拜访',
        3:'新增机会',
        4:'新增合同',
        5:'合同金额（万元）',
        6:'回款金额（万元）'
      },
      options:[],
      labelPosition: 'right',
      formLabelAlign: {
        newCustomers: null,
        newContract: null,
        newVisit: null,
        newOpportunity:null,
        contractAmount:null,
        contractReturnAmount:null,
        year:this.year,
        personalGoalsUnitList:[],
        personalGoalsDetailList:[],
      },
      unitGoalItem:[
        {
          unitId:'',
          unitName:'',
          unitgoalType:'informationAmount',
          unitgoalTypeName:'信息化业绩（万元）',
          goalNum:'',
          year:'',
        },{
          unitId:'',
          unitName:'',
          unitgoalType:'teachingMaterialAmount',
          unitgoalTypeName:'教材业绩金额（万元）',
          goalNum:'',
          year:'',
        },{
          unitId:'',
          unitName:'',
          unitgoalType:'totalContract',
          unitgoalTypeName:'合同数量',
          goalNum:'',
          year:'',
        },{
          unitId:'',
          unitName:'',
          unitgoalType:'totalVisit',
          unitgoalTypeName:'拜访次数',
          goalNum:'',
          year:'',
        },
      ],
      keyNames:{
        informationAmount:"信息化业绩（万元）",
        teachingMaterialAmount:'教材业绩金额（万元）',
        totalContract:'合同数量',
        totalVisit:'拜访次数'
      },
      unitGoalItem2:[],
      unitGoalList: [],
      unitIds:[],
      unitDialogVisible:false,
      currentYear :''
    }
  },
  mounted: function() {
  },
  created(){
    this.unitGoalItem2 = deepClone(this.unitGoalItem)
    
    getYearList().then((result) => {
      this.options = result.data.years;
      this.year = result.data.currentYear;
      this.currentYear =  result.data.currentYear;
      this.formLabelAlign.year = this.year;
      this.loadInfo()
    }).catch((err) => {
      
    });
  },
  components:{
        textBorder,
        unitDialog,
    },
    methods:{
      getUnitGoalList(array){
        this.unitGoalList = [];
        this.unitIds = [];
        var keys = ['informationAmount','teachingMaterialAmount','totalContract','totalVisit'];
        array.forEach((element,idx) => {
            var list = [];
            this.unitIds.push(element.unitId)
            keys.forEach(key => {
              var item = {
                id:element.id,
                index:idx,
                unitId:element.unitId,
                unitName:element.unitName,
                year:element.year,
                goalNum:element[key],
                unitgoalType:key,
                unitgoalTypeName:this.keyNames[key],
              };
              list.push(item);
            });
          this.unitGoalList.push(list);
        });
      },
      // 获取目标信息
      loadInfo(){
        personalgoalsInfo({year:this.year}).then((result) => {
          this.formLabelAlign = result.data;
            if (this.formLabelAlign && this.formLabelAlign.personalGoalsDetailList.length>0) {
              this.formLabelAlign.personalGoalsDetailList.forEach(item => {
              item.goalTypeKey = this.keyDict[item.goalType];
              item.goalTypeName = this.nameDict[item.goalType];
              item = this.changeNum(item);
            });
            this.tableData = this.formLabelAlign.personalGoalsDetailList;
          }else{
            this.tableData = deepClone(this.goalDetailList);
            this.tableData.forEach(item =>item.year = this.year);
          }
          this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList)
        }).catch((err) => {
          
        });
      },
      changeYear(year){
        this.loadInfo();
      },
      // 更新
      update(){
        this.isLoading = true;
        updatepersonalgoals(this.formLabelAlign).then((result) => {
          this.isLoading = false;
          if (result.data) {
            this.$message({
              type:"success",
              message:'保存成功'
            })
            this.loadInfo();
          }else{
            this.$message({
              type:"error",
              message:result.msg
            })
          }
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      // 保存
      save(){
        this.isLoading = true;
        addpersonalgoals(this.formLabelAlign).then((result) => {
          this.isLoading = false;
          if (result.data) {
            this.$message({
              type:"success",
              message:'保存成功'
            })
            this.loadInfo();
          }else{
            this.$message({
              type:"error",
              message:result.msg
            })
          }
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      // 保存
      saveGoalAction(){
        // 2.个人年度目标数据校验
        var isVisible = this.goalVisible();
        if (isVisible ) {
          this.formLabelAlign.personalGoalsDetailList = this.tableData;
            // 数据结构转换
          this.formLabelAlign.personalGoalsUnitList = this.getUnitData();
          // 保存
          if (this.formLabelAlign.id) {
            this.update();
          }else{
            this.save();
          }
          
        }else{
          this.$message({
            type:'error',
            message:`个人年度目标设置与相关目标明细合计数量不一致，请检查设置信息。`
          })
        }
         
      },
      // 校验明细
      goalVisible(){
        var list = ["newCustomers","newContract","newVisit","newOpportunity","contractAmount","contractReturnAmount"];
        var isVisible = true;
        this.tableData.forEach(item => {
            if(list.indexOf(item.goalTypeKey) >=0){
              if (Number(this.formLabelAlign[item.goalTypeKey]) != item.sum) {
                isVisible = false;
              } 
            }
        });
        return isVisible;
        
      },

      getUnitData(){
        var list = [];
        var keys = ['informationAmount','teachingMaterialAmount','totalContract','totalVisit'];
        this.unitGoalList.forEach(item => {
            var obj = Object();
           item.forEach(unitItem => {
              obj.unitId = unitItem.unitId;
              obj.year = unitItem.year;
              if (keys.indexOf(unitItem.unitgoalType)>=0) {
                obj[unitItem.unitgoalType] = unitItem.goalNum;
              }
           });
           list.push(obj);
        });
        return list;
      },
      chooseunit(){
          this.unitDialogVisible = true;
          var idsStr = this.unitIds.join(',');
          this.$refs.unitdialog.loadData(idsStr);
      },
      updateUnit(data){
        this.unitIds.push(data.id);
        var list = deepClone(this.unitGoalItem2)
        list.forEach(element => {
          element.unitId = data.id;
          element.unitName = data.unitName;
          element.year = this.year;
          element.goalNum = ''
        });
        this.unitGoalList.push(list);
      },
      updateVisible(val){
            this.unitDialogVisible = val;
        },
      changeNum(row){
        var list = ['january','february','march','april','may','june','july','august','september','october','november','december'];
        var sum = 0;
        list.forEach(element => {
          if(row[element]){
            var num = Number(row[element])
            sum  = sum + num;
          }
        });
        
        row.sum = sum;
        return row;
      },
      deleteUnitGoal(data){
        if (data.id) {
          personalgoalsunitDelete({id:data.id}).then((result) => {
            if (result.data) {
              this.unitGoalList.splice(data.index,1);
              this.unitIds = this.unitIds.filter(item=>item!==data.unitId)
              this.$message({
                type:'success',
                message:result.msg
              })
            }else{
              this.$message({
                type:'error',
                message:result.msg
              })
            }
          }).catch((err) => {
            
          });
        }else{
          this.unitGoalList.splice(data.index,1);
          this.unitIds = this.unitIds.filter(item=>item!==data.unitId)
        }
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
          if (columnIndex === 0 && rowIndex === 0) {
            return {
              rowspan: 4,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      }
    }
}
</script>

<style lang="scss" scoped> 

.textAlign /deep/.el-input__inner{
  text-align: center !important;
}
.flexcss{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.textAlignRight{
  text-align: right;
}
.width200{
  width: 200px ;
  margin-bottom: 16px;
}
.definput /deep/.el-input-group__append{
  background-color: #F6F7FB !important;
  color: #333333 !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.rtextcss{
  height: 24px;;
  line-height: 24px;
  font-size: 12px;
  color: #999999;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.owntable /deep/ td,  th {
  padding: 8px 0 !important;
}
.owntable /deep/ th{
  background-color: #F6F7FB !important;
}
.owntable /deep/ .cell{
  font-size: 14px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400 !important;
  color: #333333 !important;
}
.mubiao{
  background: #fff;
  margin-top: 10px;
  padding: 24px 20px;
  min-height: 500px;
  box-sizing: border-box;
}
.inputcss{
  height: 36px !important;
}
/deep/ .el-input__inner{
  line-height: 36px !important;
  height: 36px;
}
.deleteimg{
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.tablebox{
  height: 337px;
  overflow-x: hidden;
  padding-right: 10px;
}
.asbu{
  position: absolute;
  right: 0;
  top: 0;
}
.rela{
  position: relative;
  margin-bottom: 24px;
}
/deep/.el-input-group__append{
    padding: 0 12px !important;
}
.mt{
  margin-top: 20px;
}
.elform{
  width: 100%;
  .el-form-item{
    margin-bottom: 20px !important;
  }
}
.owntop{
  display: flex;
}
    .ownleft{
      height:424px ;
      width: 40%;
      min-width: 400px;
      margin-right: 10px;
      background: #fff;
      border-radius: 8px 8px 8px 8px;
      box-sizing: border-box;
      padding: 20px 40px 30px 20px;
    }
    .ownright{
      height: 424px;
      flex:1;
      background: #fff;
      border-radius: 8px 8px 8px 8px;
      padding: 20px 24px;
      box-sizing: border-box;
    }
</style>