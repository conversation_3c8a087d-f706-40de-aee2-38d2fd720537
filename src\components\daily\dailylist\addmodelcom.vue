<template>
  <div class='mobanlist'>
     <el-form
      label-position="top"
      :model="ruleForm"
      ref="ruleForm"
      label-width="120px"
      class="ruleFormmo"
    >
      <template v-for="(item, index) in viewDataList">
        <el-form-item
          :prop="item.__vModel__"
          :show-message="true"
          :rules="[
            {
              required: item.isRequired == 1 ? true : false,
              message: `${item.itemName}不能为空`,
              trigger: 'blur',
            },
          ]"
          :label="item.itemName"
          :key="index"
          v-if="item.itemType == 1"
        >
          <el-input  :maxlength="item.itemLimitNumber"
          show-word-limit  v-model="ruleForm[item.__vModel__]"></el-input>
          {{ item.itemDescribe }}
        </el-form-item>
        <el-form-item
          :prop="item.__vModel__"
          :show-message="true"
          :rules="[
            {
              required: item.isRequired == 1 ? true : false,
              message: `${item.itemName}不能为空`,
              trigger: 'blur',
            },
          ]"
          :label="item.itemName"
          :key="index"
          v-if="item.itemType == 2"
        >
          <el-input
          :maxlength="item.itemLimitNumber"
          show-word-limit 
            placeholder="请输入"
            class="textinput"
            type="textarea"
            v-model="ruleForm[item.__vModel__]"
          ></el-input>
          {{ item.itemDescribe }}
        </el-form-item>
        <el-form-item
        :prop="item.__vModel__"
        :show-message="true"
        :rules="[
          {
            required: item.isRequired == 1 ? true : false,
            message: `${item.itemName}不能为空`,
            trigger: 'blur',
          },
        ]"
        v-if="item.itemType == 3"
        :label="item.itemName"
        :key="index"
      >
        <UploadFile
          accept=".pdf,.mp4,.jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PNG,.GIF,.PDF,.doc,.docx,.xlsx"
          :itemDescribe="item.itemDescribe"
          :multiple="item.itemLimitNumber > 1 ? true : false"
          :itemType="item.itemType"
          :itemLimitNumber="item.itemLimitNumber"
          @fatherMethod="fatherMethod"
          :fileList.sync="ruleForm[item.__vModel__]"
        />
      </el-form-item>
      </template>
     
    </el-form>
    <fileview ref="fileview"></fileview>
  </div>
</template>

<script>
import {
  TemplateDetailRiZhi,
} from '../../../api/index'
import UploadFile from '@/components/zongjie/temmodel/upload/index'
import {getUrlType} from '@/utils/tools.js'
import fileview from '../../common/fileview.vue';
export default {
data() {
    return {
      viewDataList: [],
      ruleForm:{}
    }
},
components: {
    UploadFile,
    fileview
  },
created() {
  if(this.$route.query.templateId){
    this.getisUp()
  }
},
methods: {
  setViewList(templateItemVos){
          let originData =  templateItemVos
          originData.forEach((item) => {
            item.__vModel__ = 'model_' + item.id
            let itemDetailVos = item.itemDetailVos
            if (item.itemType == 1 || item.itemType == 2) {
              if (itemDetailVos.length > 0) {
                this.$set(
                  this.ruleForm,
                  'model_' + item.id,
                  itemDetailVos[0].templateItemValue
                )
              }
            } else if (item.itemType == 3) {
              let fileListData = []
              itemDetailVos.length > 0 &&
              itemDetailVos.forEach((itemchildren) => {
                  fileListData.push({
                    name: itemchildren.templateItemFileName,
                    url: itemchildren.templateItemUrl,
                    size: itemchildren.templateItemSize,
                    status: 'success',
                    id: itemchildren.id,
                    itemType: itemchildren.itemType,
                  })
                })
              this.$set(this.ruleForm, 'model_' + item.id, fileListData)
            }
          })
          this.viewDataList = originData
  },
  fatherMethod(file) {
        let viewUrl = ''
        if (file.hasOwnProperty('response')) {
          viewUrl = file.response.data.url
        } else {
          viewUrl = file.url
        }
    let mediaType = getUrlType(viewUrl)
    if(mediaType ==1){  //图片
      this.$maskV.show({
        type: 1,
        url: viewUrl,
        onClose: () => {
        }
      })
      return 
    }
    if(mediaType ==2){  //视频
      this.$maskV.show({
        type: 2,
        url: viewUrl,
        onClose: () => {
        }
      })
      return 
    }
    if(mediaType ==3){  //pdf
          this.$refs.fileview.show({
            url:viewUrl,
            fileName:file.fileName || file.response.data.fileName
          })
      return 
    }
    },
    getViewData(){
        let reData = this.processData(this.viewDataList)
        return reData
    },
    processData(dataArray){
          let formdataSubmit = []
          let originViewDataList = dataArray
          originViewDataList.forEach((item, index) => {
            if (item.itemType == 1 || item.itemType == 2) {
              let itemDetailVos = []
              if (this.ruleForm['model_' + item.id]) {
                let dataobj = {
                  templateItemValue: this.ruleForm['model_' + item.id],
                  templateItemUrl: '',
                  templateItemSize: '',
                  templateItemFileName: '',
                }
                if (this.editOnOff) {
                  if (item.itemWorksVos.length > 0) {
                    itemWorksVos = [
                      {
                        id: item.itemWorksVos[0].id,
                        ...dataobj,
                      },
                    ]
                  } else {
                    itemWorksVos = [
                      {
                        ...dataobj,
                      },
                    ]
                  }
                  formdataSubmit.push({
                    itemWorksVos: itemWorksVos,
                    itemType: item.itemType,
                    id: item.id,
                  })
                } else {
                  itemDetailVos = [
                    {
                      ...dataobj,
                    },
                  ]
                  formdataSubmit.push({
                    itemDetailVos: itemDetailVos,
                    id: item.id,
                  })
                }
              } else {
                if (this.editOnOff) {
                  if (item.itemDetailVos.length > 0) {
                    itemDetailVos = [
                      {
                        id: item.itemDetailVos[0].id,
                        modelValue: '',
                        modelItemUrl: '',
                        modelItemSize: '',
                        modelItemFileName: '',
                      },
                    ]
                  }
                  formdataSubmit.push({
                    itemDetailVos: itemDetailVos,
                    itemType: item.itemType,
                    id: item.id,
                  })
                } else {
                  formdataSubmit.push({
                    itemDetailVos: itemDetailVos,
                    id: item.id,
                  })
                }
              }
            } else if (item.itemType == 3 || item.itemType == 4) {
              let itemDetailVos = []
              if (this.ruleForm['model_' + item.id]) {
                try {
                  this.ruleForm['model_' + item.id].forEach((itemFile) => {
                    if (itemFile.status != 'success') {
                      throw new Error(item.competItem)
                    }
                    let dataobj = {
                      templateItemValue: '',
                      templateItemUrl: itemFile.response
                        ? itemFile.response.data.url
                        : itemFile.url,
                        templateItemSize: itemFile.response
                        ? itemFile.response.data.size
                        : itemFile.size,
                        templateItemFileName: itemFile.response
                        ? itemFile.response.data.fileName
                        : itemFile.name,
                    }
                    if (this.editOnOff) {
                      itemDetailVos.push({
                        ...dataobj,
                        id: itemFile.id ? itemFile.id : '',
                      })
                    } else {
                      itemDetailVos.push({
                        ...dataobj,
                      })
                    }
                  })
                } catch (e) {
                  this.$message({
                    type: 'error',
                    message: e.message + '正在上传',
                  })
                  throw e
                }
              } else {
                itemDetailVos = []
              }
              if (this.editOnOff && itemDetailVos.length > 0) {
                formdataSubmit.push({
                  itemDetailVos: itemDetailVos,
                  itemType: item.itemType,
                  id: item.id,
                })
              } else {
                formdataSubmit.push({
                  itemDetailVos: itemDetailVos,
                  id: item.id,
                })
              }
            }
          })
          return formdataSubmit
      },
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          if(this.$route.query.wordsNum == 1){
              let sumNum = 0;
              this.viewDataList.forEach(item=>{
                  if (item.itemType == 1 || item.itemType == 2) {
                          if (this.ruleForm['model_' + item.id]) {
                            sumNum+=this.ruleForm['model_' + item.id].length
                          }
                  }
              })
              if(sumNum==0){
                  this.$message({
                    type: 'error',
                    message:`请至少填写一项内容后提交`,
                  })
                  return 
              }
              this.$emit("headdenForm");
          }else if(this.$route.query.wordsNum == 2){
              let sumNum = 0;
              this.viewDataList.forEach(item=>{
                  if (item.itemType == 1 || item.itemType == 2) {
                          if (this.ruleForm['model_' + item.id]) {
                            sumNum+=this.ruleForm['model_' + item.id].length
                          }
                  }
              })
              if(sumNum<this.$route.query.minNum){
                  this.$message({
                    type: 'error',
                    message:`日报内容最少字数是${this.$route.query.minNum}`,
                  })
                  return 
              }
              if(sumNum>this.$route.query.maxNum){
                  this.$message({
                    type: 'error',
                    message:`日报内容最大字数限制是${this.$route.query.maxNum}`,
                  })
                  return 
              }
              this.$emit("headdenForm");
          }
        }
      });
    },
    getList(){
        return this.viewDataList || []
    },
    async getisUp() {
      let res = await TemplateDetailRiZhi(
        {
          id: this.$route.query.templateId,
          methodName:'saveDailyRecord',
          controllerName:'DailyRecordController',
        }
       )
      if (res.status != 0) return this.$message.error(res.msg)
      if (res.data) {
          let originData = res.data.templateItemVoList
          originData.forEach((item) => {
            item.__vModel__ = 'model_' + item.id
          })
          this.viewDataList = originData
          this.$emit('setPeople',res.data.templateDisposeVoList)
      }
    },
}
}
</script>
<style>
  .ruleFormmo .el-upload-list__item{
    line-height: 26px;
    height: 26px;
  }
  .ruleFormmo .el-icon-close{
    top: 4px;
  }
  .textinput .el-textarea__inner{
    min-height: 160px !important;
  }
</style>
<style scoped>
.ruleFormmo .el-form-item {
     margin-bottom: 22px;
  }
.uploadtext {
color: #4285F4;
cursor: pointer;
}
.el-icon-plus {
font-size: 30px !important;
}

.uploadcss {
width: 88px;
height: 88px;
line-height: 24px;
background: #F5F5F5;
border-radius: 4px 4px 4px 4px;
opacity: 1;
border: 1px dashed #CCCCCC;
display: inline-block;
}

.uploadcss i {
margin-top: 20px;
font-size: 24px;
color: #4285F4;
}
.mobanlist{
  width: 100%;
}
.blueline {
border-left: 4px solid #2490ff;
padding-left: 20px;
margin-top: 0;
margin-bottom: 20px;
}
.mobantitlecss {
height: 40px;
background: #ffffff;
border-radius: 10px;
margin-bottom: 20px;
}
.save{
display: block;
margin: 0 auto;
} 
</style>
