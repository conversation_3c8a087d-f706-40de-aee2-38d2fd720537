<template>
  <div class="mainbg">
    <modelCom :typeModel="typeModel"></modelCom>
  </div>
</template>
<script>
import modelCom from './modellistcom.vue'
export default {
  components: {
    modelCom,
  },
  data() {
    return {
      typeModel: '3',
    }
  },
  created() {},
  mounted() {},
  methods: {},
}
</script>
<style scoped>
.mainbg {
  min-height: calc(100vh - 106px);
  transform: none;
}
</style>