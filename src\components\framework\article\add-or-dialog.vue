<!-- 弹框 -->
<template>
  <el-dialog
    :title="!changetitle ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="25%"
  >
    <el-form
      :model="dataForm"
      :rules="Rule.ARTICLE_DIALOG"
      ref="dataForm"
      label-width="100px"
    >
      <el-form-item label="标志ID：" v-if="isDisbled">
        <el-input
          v-model="signid"
          placeholder="请输入名称"
          :disabled="true"
        ></el-input>
      </el-form-item>
      <el-form-item label="名称：" prop="name">
        <el-input v-model="dataForm.name" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="类型：" prop="type">
        <el-select
          v-model="dataForm.type"
          placeholder="请选择类型"
          :disabled="isDisbled"
        >
          <el-option
            v-for="item in Dict.ARTICLE_TYPE"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序号：" prop="sortNo">
        <el-input-number
          v-model="dataForm.sortNo"
          :min="0"
          placeholder="请输入排序号"
        ></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit('dataForm')"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      signid: '',
      visible: false,
      articleType: '',
      changetitle: {},
      dataForm: {
        name: '',
        type: '',
        sortNo: '',
      },
    }
  },
  computed: {
    isDisbled() {
      return !!this.changetitle
    },
  },
  methods: {
    init(id, backdata) {
      this.visible = true
      this.articleType = id
      this.changetitle = backdata // backdata:有就是修改 没有就是新增
      if (backdata) {
        // 修改
        this.signid = backdata.id
        this.dataForm.name = backdata.name
        this.dataForm.type = backdata.type.toString()
        this.dataForm.sortNo = backdata.sortNo
        this.dataForm.id = id // 修改需要当前id
        this.dataForm.parentId = backdata.parentId // 修改需要父级id
      } else {
        // 新增
        this.dataForm.name = ''
        this.dataForm.type = ''
        this.dataForm.sortNo = ''
        if (!this.articleType) {
          this.dataForm.parentId = 0
        } else {
          this.dataForm.parentId = id
        }
      }
    },
    // 弹框 确定
    dataFormSubmit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let url = !this.changetitle
            ? '/sf/business/articlecategory/save'
            : '/sf/business/articlecategory/update'
          let res = await this.$axios.post(url, this.dataForm)
          if (res.status === 0) {
            setTimeout(() => {
              this.visible = false
              this.$message.success('操作成功')
              this.$emit('refreshTagTypeTree')
            }, 300)
          }
        } else {
          return false
        }
      })
    },
  },
}
</script>
