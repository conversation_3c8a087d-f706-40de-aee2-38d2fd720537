<template>
    <div>
        <back class="mb20">返回</back>
        <div class="riqi date-picker-container">
            <div class="selectors">
                <el-date-picker
                    v-model="par.year"
                    type="year"
                    placeholder="请选择年份"
                    format="yyyy年"
                    value-format="yyyy"
                    :picker-options="{disabledDate(time) { return time.getTime() > Date.now(); }}" 
                    @change="changeYear"
                    class="xuanze"> 
                </el-date-picker>
            </div>
            <nowtime class="nowtime-right"></nowtime>
        </div>

        <!-- 年度概况 -->
        <niandu ref="niandu"></niandu>

        <!-- 业绩变化 -->
        <yeji ref="yeji"></yeji>

        <!-- 产出投入 -->
        <chanchu ref="chanchu"></chanchu>
         
        <!-- 销售情况 -->
        <xiaoshou ref="xiaoshou"></xiaoshou>

        <loudou ref="loudou"></loudou>

        <rili ref="rili"></rili>
    </div>
</template>    

<script>
import '../detail.css';
import niandu from '@/components/kanban/common/niandu.vue';
import yeji from '@/components/kanban/common/yeji.vue';
import chanchu from '@/components/kanban/common/chanchu.vue';
import xiaoshou from '@/components/kanban/common/xiaoshou.vue';
import loudou from '@/components/kanban/common/loudou.vue';
import rili from '@/components/kanban/common/rili.vue';
import nowtime from '@/components/kanban/common/nowtime.vue';
import back from "@/components/common/back.vue";
import { getParStr } from "@/utils/tools";

import { 
    businessmanagerOverView,
    performanceInvestment,
    performanceCalendar,
    querySalesFunnel,
    queryAnnualSales,
    queryUnitPerformancePage
} from '@/api/bigdata';
export default {
    components:{
        niandu,
        yeji,
        chanchu,
        xiaoshou,
        loudou,
        rili,
        nowtime,
        back
    },
    data() {
        return {
            par:{
                userId:this.$route.query.userId,
                departmentId:this.$route.query.departmentId ? this.$route.query.departmentId.split(',') : [],
                year:this.$route.query.year
            },

        }
    },
    mounted() {

    },
    created(){
        this.loadData()
    },
    methods: {
        loadData(){
            this.loadBusOverView()
            this.loadPerformanceCalendar()
            this.loadQuerySalesFunnel()
            this.loadQueryAnnualSales()
            this.loadMatData();
            this.loadInfomationData();
        },
        async loadBusOverView(){
            var result = await  businessmanagerOverView(this.par)
            this.$nextTick(()=>{
                this.$refs.niandu.loadData(result.data)
                this.$refs.yeji.loadData(result.data)
                this.$refs.chanchu.getYearData(result.data)
                this.loadPerformanceInvestment()
            })
        },
        loadPerformanceInvestment(){
            performanceInvestment(this.par).then((result) => {
                this.$nextTick(()=>{
                    this.$refs.chanchu.loadData(result.data)
                })
            }).catch((err) => {
                
            });
        },
        loadPerformanceCalendar(){
            performanceCalendar(this.par).then((result) => {
                this.$nextTick(()=>{
                    this.$refs.rili.initChart(result.data)
                })
            }).catch((err) => {
                
            });
        },
        loadQuerySalesFunnel(){
            querySalesFunnel(this.par).then((result) => {
                console.log('55555555',result)
                this.$nextTick(()=>{
                    this.$refs.loudou.loadData(result.data);
                })
            }).catch((err) => {
                
            });
        },
        loadQueryAnnualSales(){
            queryAnnualSales(this.par).then((result) => {
                this.$nextTick(()=>{
                    this.$refs.xiaoshou.loadData(result.data);
                })
            }).catch((err) => {
                
            });
        },
        loadMatData(){
            // 教材
            var pardata = Object.assign({
                type:1,
                pageNum:1,
                pageSize:5,
            },this.par)
            queryUnitPerformancePage(pardata).then((result) => {
                this.$refs.xiaoshou.loadMatData(result.data)
            }).catch((err) => {
                
            });
        },
        loadInfomationData(){
            // 教材
            var pardata = Object.assign({
                type:2,
                pageNum:1,
                pageSize:5,
            },this.par)
            queryUnitPerformancePage(pardata).then((result) => {
                this.$refs.xiaoshou.loadInfoData(result.data)
            }).catch((err) => {
                
            });
        },
        changeYear(){
            history.replaceState(null,null,`#${this.$route.path}?${getParStr(this.par)}`) 
            this.$router.replace({
                path:this.$route.path,
                query:this.par
            })
            this.loadData()
        },
    }
}
</script>

<style>
.date-picker-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selectors {
    display: flex;
    align-items: center;
}


</style>