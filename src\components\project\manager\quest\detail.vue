<template>
  <div>
    <div class="backcss">
      <back class="backc">返回</back>
      <span class="namec">
        {{ $route.query.pname }}
        <i class="el-icon-arrow-right"></i>
        {{ $route.query.name }}
      </span>
    </div>
    <div class="mainbg">
      <div class="theadcss">ID：{{ form.taskNum }} {{ form.taskName }}</div>
      <div class="fl">
        <div class="condiv">
          <div class="dercss br">
            <textBorder>产出内容</textBorder>
            <div class="content">
              {{ form.taskContent }}
            </div>
          </div>
          <questlist
            v-if="form.childrenList.length > 0"
            class="mtop20"
            :tableData="form.childrenList"
            :isControl="false"
            :isLoading="isLoading"
            :isAutoHeight="true"
            :isBorder="true"
            :isHighlight="true"
            @currentChange="handleCurrentChange"
          ></questlist>
          <div class="mtop20 recorddiv br">
            <el-tabs
              class="mtabscss"
              v-model="activeName"
              @tab-click="handleClick"
            >
              <el-tab-pane label="工作记录" name="first">
                <!-- <workrecord ref="workrecord" :isControl="false"></workrecord> -->
                <copyworkrecord
                  ref="workrecord"
                  :isControl="false"
                  :isCreateBy="false"
                ></copyworkrecord>
              </el-tab-pane>
              <el-tab-pane label="成本信息" name="third">
                <costrecord ref="costrecord" :isControl="false"></costrecord>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <div class="infodivcss">
          <el-form
            class="infoform"
            ref="form"
            :model="form"
            label-width="120px"
          >
            <textBorder>基本信息</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
              <el-col :span="12">
                <el-form-item label="优先级：" class="labeltext mulitline">
                  <span
                    v-if="form.priority"
                    :style="{ color: status1[form.priority].color }"
                    >{{ status1[form.priority].name }}</span
                  >
                </el-form-item>
                <el-form-item label="开始时间：" class="labeltext mulitline">
                  <span>{{ form.beginTime || '--' }}</span>
                </el-form-item>
                <el-form-item
                  label="实际开始时间："
                  class="labeltext mulitline"
                >
                  <span>{{ form.actualBeginTime || '--' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目状态：" class="labeltext">
                  <span>{{ form.taskStatusName }}</span>
                </el-form-item>
                <el-form-item label="结束时间：" class="labeltext mulitline">
                  <span>{{ form.endTime || '--' }}</span>
                </el-form-item>
                <el-form-item
                  label="实际结束时间："
                  class="labeltext mulitline"
                >
                  <span>{{ form.actualEndTime || '--' }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <textBorder>负责与协作</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
              <el-col :span="12">
                <el-form-item label="任务负责人：" class="labeltext">
                  <span>{{ form.chargePersonName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参与人：" class="labeltext mulitline">
                  <span>{{ form.collaboratorName }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <textBorder>补充信息</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
              <el-col :span="24">
                <el-form-item label="附件：" class="labeltext">
                  <pdfview
                    v-if="form.fileInfoList.length > 0"
                    type="1"
                    :pdfArr="form.fileInfoList"
                  ></pdfview>
                  <span v-else>暂无</span>
                </el-form-item>
                <el-form-item label="备注：" class="labeltext mulitline">
                  <span>{{ form.notes || '暂无' }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <textBorder>系统信息</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
              <el-col :span="12">
                <el-form-item label="创建人：" class="labeltext">
                  <span>{{ form.createByName }}</span>
                </el-form-item>
                <el-form-item label="创建时间：" class="labeltext mulitline">
                  <span>{{ form.createTime }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最后修改人：" class="labeltext">
                  <span>{{ form.modifyByName }}</span>
                </el-form-item>
                <el-form-item
                  label="最后修改时间："
                  class="labeltext mulitline"
                >
                  <span>{{ form.modifyTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import back from '@/components/common/back.vue'
import textBorder from '@/components/common/textBorder.vue'
import questlist from '../../common/questlist.vue'
import copyworkrecord from '../../common/copyworkrecord.vue'
import costrecord from '../../common/costrecord.vue'
import pdfview from '@/components/common/pdfview.vue'
import { taskInfo } from '@/api/project/index'
export default {
  components: {
    back,
    textBorder,
    questlist,
    costrecord,
    copyworkrecord,
    pdfview,
  },
  data() {
    return {
      status1: {
        1: {
          name: '低',
          color: '#56C36E',
        },
        2: {
          name: '一般',
          color: '#4285F4',
        },
        3: {
          name: '紧急',
          color: '#FF8D1A',
        },
        4: {
          name: '非常紧急',
          color: '#F45961',
        },
      },
      tableData: [],
      isLoading: false,
      activeName: 'first',
      form: {
        childrenList: [],
        fileInfoList: [],
      },
      taskIdObj: {
        taskId: '',
      },
    }
  },
  created() {
    this.loadInfo()
  },
  methods: {
    handleClick(item) {
      if (item.name == 'first') {
        this.$refs.workrecord.loadworks(this.$route.query.id)
      }
      if (item.name == 'third') {
        this.$refs.costrecord.loadcost(this.$route.query.id)
      }
    },
    loadInfo() {
      taskInfo(this.$route.query.id)
        .then((result) => {
          this.form = result.data
          this.$nextTick(() => {
            this.$refs.workrecord.loadworks(this.$route.query.id)
            this.$refs.workrecord.getTaskInfo(this.form)
          })
        })
        .catch((err) => {})
    },
    handleCurrentChange(data) {
      if (!data) {
        data = this.form
      }
      if (this.activeName == 'first') {
        this.$refs.workrecord.loadworks(data.id)
        this.$refs.costrecord.updateTaskId(data.id)
      } else if (this.activeName == 'third') {
        this.$refs.costrecord.loadcost(data.id)
        this.$refs.workrecord.updateTaskId(data.id)
      }
    },
  },
}
</script>

<style  scoped lang="scss">
.backcss {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  background-color: #f7f7f7;
  position: relative;
  text-align: center;
}
.backc {
  display: inline-block;
  position: absolute;
  left: 0;
}
.stacolor {
  line-height: 24px;
  background: #fff5f6;
  border-radius: 12px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #f45961;
  padding: 4px 8px;
}
.fl {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.pb12 {
  padding-bottom: 12px;
}
.mb30 {
  margin-bottom: 30px !important;
}
.infoform /deep/.el-form-item {
  margin-bottom: 0px;
}
.infoform {
  // padding: 0 20px;
  position: relative;
}
.mulitline /deep/.el-form-item__content {
  padding-top: 10px;
  line-height: 24px;
}
.mtabscss /deep/ .el-tabs__item {
  padding: 0 47px !important;
}
.recorddiv {
  padding: 20px;
}
.mtop20 {
  margin-top: 20px;
}
.mtable {
  border-radius: 8px;
}
.br {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #e6e6e6;
}
.pd {
  padding: 20px;
}
.content {
  margin-top: 10px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 24px;
  text-align: justify;
}
.condiv {
  width: 60%;
  // min-width: 740px;
  margin-right: 12px;
  background-color: white;
  padding: 20px;
  border-radius: 0 0 8px 8px;
}
.dercss {
  padding: 16px;
}
.infodivcss {
  width: 40%;
  background-color: white;
  border-radius: 0 0 8px 8px;
  padding: 20px;
}
.theadcss {
  padding: 0 20px;
  height: 62px;
  line-height: 62px;
  background: #4285f4;
  border-radius: 8px 8px 0px 0px;

  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 16px;
  color: #ffffff;
}
.flex {
  display: flex;
}
.mainbg {
  margin-top: 20px;
  min-height: calc(100vh - 140px);
  border-radius: 8px;
  overflow: hidden;
}
</style>