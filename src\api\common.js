import service from '@/utils/request.js'
// export function repositoryList(data) {
// 	return service.request({
// 		method: 'post',
// 		url: '/resource/selectAll',
// 		data
// 	});
// }
// export function indexHe() {
// 	return service.request({
// 		method: 'post',
// 		url: '/crm/controller/homepage/index',
// 		headers: {
// 			'Content-Type': 'application/json;charset=UTF-8'
// 		},
// 	})
// }

/**
 * 
 * @param {
 * methodName:
 * className:
 * templateType:模板类型（1，年计划；2，月计划；3,周计划；4，年总结；5，月总结；6，周总结）} params 
 * @returns 
 */

export function findTemplate(params) {
    	return service.request({
		method: 'get',
		url: '/crm/business/template/selectTemplate',
		params
	})
}
