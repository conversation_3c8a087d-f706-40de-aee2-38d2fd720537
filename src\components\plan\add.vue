<template>
  <div>
    <back :type="2" @goback="back">{{ operateName }}{{ types[form.goalsType].name }}</back>
    <div class="maindiv">
      <div class="owntop" :class="{ w60: isShow }">
        <div class="personalplan">
          <template v-if="form.goalsType != '1'">
            <textBorder>个人{{ types[form.goalsType].label }}设置</textBorder>
            <el-form
              class="mt elform myform"
              :label-position="labelPosition"
              :model="formLabelAlign"
            >
              <el-row>
                <el-col :span="12">
                  <el-form-item label="跟进拜访：" label-width="100px">
                    <el-input
                      placeholder="请输入拜访次数"
                      class="definput"
                      v-model="formLabelAlign.newVisit"
                      type="number"
                      ><template slot="append">次</template></el-input
                    >
                  </el-form-item>
                  <el-form-item label="业绩：" label-width="100px">
                    <el-input
                      placeholder="请输入金额"
                      class="definput"
                      v-model="formLabelAlign.contractAmount"
                      type="number"
                    >
                      <template slot="append">万元</template></el-input
                    >
                  </el-form-item>
                  <el-form-item label="合同新增：" label-width="100px">
                    <el-input
                      placeholder="请输入新增合同个数"
                      class="definput"
                      v-model="formLabelAlign.newContract"
                      type="number"
                      :max="5000"
                      ><template slot="append">个</template></el-input
                    >
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客户新增：" label-width="120px">
                    <el-input
                      placeholder="请输入新增客户数量"
                      class="definput"
                      v-model="formLabelAlign.newCustomers"
                      type="number"
                    >
                      <template slot="append">个</template></el-input
                    >
                  </el-form-item>
                  <el-form-item label="回款：" label-width="120px">
                    <el-input
                      placeholder="请输入金额"
                      class="definput"
                      v-model="formLabelAlign.contractReturnAmount"
                      type="number"
                      ><template slot="append">万元</template></el-input
                    >
                  </el-form-item>
                  <el-form-item label="样书发放：" label-width="120px">
                    <el-input
                      placeholder="请输入样书发放次数"
                      class="definput"
                      v-model="formLabelAlign.materialDeliverNum"
                      type="number"
                      :max="5000"
                      ><template slot="append">次</template></el-input
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </template>
        </div>
        <div class="unitplan" v-if="form.goalsType == '1'">
          <div class="rela">
            <textBorder>单位年度目标设置</textBorder>
          </div>
          <div class="borderdi">
            <div class="bposi">
              <p class="zhu">
                注：此处目标为针对某院校或单位设置的年度业绩与拜访目标
              </p>
              <el-button
                class="defaultbtn asbu"
                icon="el-icon-plus"
                type="primary"
                @click="chooseunit"
                >选择单位</el-button
              >
            </div>
            <div class="tablebox">
              <el-table
                class="mytable"
                v-for="(item, index) in unitGoalList"
                :key="index"
                :data="item"
                :span-method="objectSpanMethod"
                border
              >
                <el-table-column prop="unitName" label="学校">
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.unitName }}
                      <img
                        class="deleteimg"
                        src="../../assets/delete.png"
                        alt=""
                        @click="deleteUnitGoal(scope.row)"
                      />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="unitgoalTypeName"
                  label="目标维度"
                ></el-table-column>
                <el-table-column prop="goalNum" label="目标">
                  <template v-slot="{ row }">
                    <el-input
                      size="normal"
                      v-model="row.goalNum"
                      type="number"
                      clearable
                    ></el-input>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <addmodelcom ref="modelCom" @headdenForm="headdenForm"></addmodelcom>
        <div>
          <div class="pflex">
            <div class="pd10">抄送人：</div>
            <ul class="uflex">
              <li v-for="(item, index) in peopleList" :key="index" class="pli">
                <img
                  @click="closeTag(item.id)"
                  src="@/assets/close.png"
                  alt=""
                  class="closeimg"
                />
                <img v-if="item.logo" class="pimg" :src="item.logo" alt="" />
                <div class="noimg" v-else>
                  {{ item.name }}
                </div>
                <p class="pname">{{ item.name }}</p>
              </li>
              <li class="adddi" @click="clickXuan">
                <i class="el-icon-plus iadd"></i>
              </li>
            </ul>
          </div>
        </div>
        <div class="submitmask">
          <el-button
            class="defaultbtn"
            style="margin-left: 10px"
            @click="saveGoalAction(true)"
            v-dbClick
            v-if="formLabelAlign.goalStatus === 0 || !formLabelAlign.id"
            >保存草稿</el-button
          >
          <el-button
            class="defaultbtn"
            type="primary"
            @click="saveGoalAction(false)"
            v-dbClick
            >提交</el-button
          >

        </div>
      </div>
      <div class="rightdiv" :class="{ w40: isShow }">
        <div v-if="isShow" class="reviewmask">
          <planreview
            ref="planreview"
            v-if="reviewType == 'planreview'"
            :date="planTime"
            :page="pageData"
            :viewData="viewData"
            @closereview="closereview"
            :plantype="form.goalsType"
          ></planreview>
          <summaryreview
            v-if="reviewType == 'summaryreview'"
            @closereview="closereview"
            :date="planTime"
            :page="pageData"
            :viewData="viewData"
            :plantype="`${+form.goalsType + 3}`"
          ></summaryreview>
        </div>
        <div v-else class="tright">
          <div class="bbtn" id="planreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            计划回顾
          </div>
          <div class="bbtn" id="summaryreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            总结回顾
          </div>
        </div>
      </div>
    </div>

    <unitDialog
      ref="unitdialog"
      :visible.sync="unitDialogVisible"
      className="PersonalGoalsController"
      @updateVisible="updateVisible"
      @updateUnit="updateUnit"
    >
    </unitDialog>
    <el-dialog
      title="选择回顾时间"
      :visible.sync="dialogPlanVisible"
      width="340px"
      center
      :before-close="handlePlanClose"
    >
      <span>
        <el-form class="mt30" label-width="85px">
          <el-form-item
            :label="`${
              types[
                reviewType == 'summaryreview'
                  ? +form.goalsType + 3
                  : form.goalsType
              ].label
            }：`"
          >
            <el-date-picker
              class="definput"
              :picker-options="pickerOptions"
              @change="changeDatePicker"
              v-model="planTime"
              :type="types[form.goalsType].type"
              :format="types[form.goalsType].format"
              placeholder="请选择"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextAction">下一步</el-button>
      </span>
    </el-dialog>
    <systemDialog
      ref="systemdialog"
      name="选择抄送人"
      :multipleNum="0"
      :visible.sync="dialogSystemVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
  </div>
</template>

<script>
import textBorder from '@/components/common/textBorder.vue'
import unitDialog from '@/components/common/unitDialog.vue'
import back from '@/components/common/back.vue'
import planreview from '@/components/common/planreview.vue'
import summaryreview from '@/components/common/summaryreview.vue'
import { deepClone, removeById } from '@/utils/tools.js'
import { getWeek } from '@/utils/index'
import {
  addpersonalgoals,
  updatepersonalgoals,
  queryReviewGoals,
  personalgoalsunitDelete,
  jiHuaInfo,
} from '@/api/goal'

import addmodelcom from '@/components/zongjie/temmodel/addmodelcom.vue'
import systemDialog from '@/components/common/systemDialog.vue'
export default {
  components: {
    back,
    textBorder,
    unitDialog,
    planreview,
    summaryreview,
    addmodelcom,
    systemDialog,
  },
  data() {
    return {
      pickerOptions: {
        disabledDate: this.endPickerTime,
      },
      isShow: false,
      isLoading: false,
      dialogPlanVisible: false,
      year: new Date(this.$route.query.time).getFullYear(),
      tableData: [],
      planTime: '',
      types: {
        1: {
          name: '年计划',
          label: '年度计划',
          type: 'year',
          format: 'yyyy',
        },
        2: {
          name: '月计划',
          label: '月度计划',
          type: 'month',
          format: 'yyyy-M',
        },
        3: {
          name: '周计划',
          label: '周计划',
          type: 'week',
          format: 'yyyy 第 W 周',
        },
        4: {
          name: '年总结',
          label: '年度总结',
          type: 'year',
          format: 'yyyy',
        },
        5: {
          name: '月总结',
          label: '月度总结',
          type: 'month',
          format: 'yyyy-M',
        },
        6: {
          name: '周总结',
          label: '周总结',
          type: 'week',
          format: 'yyyy 第 W 周',
        },
      },
      form: {
        goalsType: this.$route.query.type,
        createBy: sessionStorage.getItem('userid'),
        year: '',
        month: '',
        week: '',
        pageNum: 1,
        pageSize: 1,
      },
      reviewType: '',
      goalDetailList: [
        {
          goalType: 1,
          goalTypeName: '新增客户',
          goalTypeKey: 'newCustomers',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 2,
          goalTypeName: '新增拜访',
          goalTypeKey: 'newVisit',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 3,
          goalTypeName: '新增机会',
          goalTypeKey: 'newOpportunity',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 4,
          goalTypeName: '新增合同',
          goalTypeKey: 'newContract',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 5,
          goalTypeName: '合同金额（万元）',
          goalTypeKey: 'contractAmount',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 6,
          goalTypeName: '回款金额（万元）',
          goalTypeKey: 'contractReturnAmount',
          sum: 0,
          year: this.year,
        },
      ],
      keyDict: {
        1: 'newCustomers',
        2: 'newVisit',
        3: 'newOpportunity',
        4: 'newContract',
        5: 'contractAmount',
        6: 'contractReturnAmount',
      },
      nameDict: {
        1: '新增客户',
        2: '新增拜访',
        3: '新增机会',
        4: '新增合同',
        5: '合同金额（万元）',
        6: '回款金额（万元）',
      },
      labelPosition: 'right',
      formLabelAlign: {
        draftId: null,
        newCustomers: null,
        newContract: null,
        newVisit: null,
        materialDeliverNum: null,
        contractAmount: null,
        contractReturnAmount: null,
        year: '',
        personalGoalsUnitList: [],
        week: '',
        month: '',
        departmentId: '',
        copyPersons: [],
        goalStatus: 0,
      },
      unitGoalItem: [
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'informationAmount',
          unitgoalTypeName: '信息化业绩（万元）',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'teachingMaterialAmount',
          unitgoalTypeName: '教材业绩金额（万元）',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'totalContract',
          unitgoalTypeName: '合同数量',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'totalVisit',
          unitgoalTypeName: '拜访次数',
          goalNum: '',
          year: '',
        },
      ],
      keyNames: {
        informationAmount: '信息化业绩（万元）',
        teachingMaterialAmount: '教材业绩金额（万元）',
        totalContract: '合同数量',
        totalVisit: '拜访次数',
      },
      unitGoalItem2: [],
      unitGoalList: [],
      unitIds: [],
      unitDialogVisible: false,
      editOnOff: false,
      templateItemList: [],
      viewData: {},
      pageData: {},
      operateName: '',
      peopleList: [],
      dialogSystemVisible: false,
    }
  },
  mounted: function () {},
  created() {
    this.formLabelAlign.year = new Date(this.$route.query.time).getFullYear()
    if (this.$route.query.templateId) {
      this.formLabelAlign.templateId = this.$route.query.templateId
    }
    if (this.$route.query.departmentId) {
      this.formLabelAlign.departmentId = this.$route.query.departmentId
    }
    this.formLabelAlign.goalsType = this.$route.query.type
    this.unitGoalItem2 = deepClone(this.unitGoalItem)
    if (this.form.goalsType == 2) {
      this.formLabelAlign.month =
        new Date(this.$route.query.time).getMonth() + 1
    } else if (this.form.goalsType == 3) {
      var time = getWeek(this.$route.query.time)
      var times = time.split('-')
      this.formLabelAlign.week = times[1]
    }
    if (this.$route.query.id) {
      // 编辑已有记录或继续编辑草稿
      this.formLabelAlign.id = this.$route.query.id
      this.loadInfo()
      this.operateName = '编辑'
    } else if (this.$route.query.draftId) {
      this.formLabelAlign.draftId = this.$route.query.draftId
      this.operateName = '新增'
    } else {
      this.operateName = '新增'
    }
  },

  methods: {
    loadData(data) {
      queryReviewGoals(data)
        .then((result) => {
          if (result.data && result.data.length > 0) {
            this.viewData = result.data
            this.pageData = result.page
            this.isShow = true
            this.dialogPlanVisible = false
          } else {
            this.$message({
              type: 'error',
              message: '暂无相关回顾可查看',
            })
          }
        })
        .catch((err) => {})
    },
    endPickerTime(time) {
      if (this.reviewType == 'planreview') {
        return false
      }
      const today = new Date()
      return time > today
    },
    changeDatePicker(date) {
      var newDate = new Date(date)
      this.form.year = newDate.getFullYear()
      if (this.form.goalsType == 2 || this.form.goalsType == 5) {
        this.form.month = newDate.getMonth() + 1
      } else if (this.form.goalsType == 3 || this.form.goalsType == 6) {
        var time = getWeek(newDate)
        var times = time.split('-')
        this.form.year = times[0]
        this.form.week = times[1]
      }
    },
    handlePlanClose() {
      this.dialogPlanVisible = false
      this.planTime = ''
    },
    nextAction() {
      if (this.planTime) {
        if (this.reviewType == 'summaryreview') {
          var fdata = Object.assign({}, this.form)
          fdata.goalsType = +fdata.goalsType + 3
          this.loadData(fdata)
        } else {
          this.loadData(this.form)
        }
      } else {
        this.$message({
          type: 'info',
          message: `请选择${this.types[this.form.goalsType].label}`,
        })
      }
    },
    closereview() {
      this.isShow = false
      this.reviewType = ''
    },
    getUnitGoalList(array) {
      this.unitGoalList = []
      this.unitIds = []
      var keys = [
        'informationAmount',
        'teachingMaterialAmount',
        'totalContract',
        'totalVisit',
      ]
      array.forEach((element, idx) => {
        var list = []
        this.unitIds.push(element.unitId)
        keys.forEach((key) => {
          var item = {
            id: element.id,
            index: idx,
            unitId: element.unitId,
            unitName: element.unitName,
            year: element.year,
            goalNum: element[key],
            unitgoalType: key,
            unitgoalTypeName: this.keyNames[key],
          }
          list.push(item)
        })
        this.unitGoalList.push(list)
      })
    },
    loadInfo() {
      jiHuaInfo({ id: this.$route.query.id, type: 2 })
        .then((result) => {
          this.formLabelAlign = result.data
          // 如果是草稿状态，设置draftId；如果是正式记录，保持原有id
          if (result.data.goalStatus === 0) {
            this.formLabelAlign.draftId = this.$route.query.id
          }
          this.templateItemList = this.formLabelAlign.templateItemList
          this.$refs.modelCom.setViewList(this.templateItemList)
          this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList)
          result.data.copyPersons.forEach((item) => {
            var data = {
              id: item.id,
              name: item.name,
              logo: item.logo,
            }
            this.peopleList.push(data)
          })
          this.formLabelAlign.copyPersons = result.data.copyPersons.map(
            (item) => item.userId
          )
        })
        .catch((err) => {})
    },
    showView(e) {
      this.planTime = ''
      this.dialogPlanVisible = true
      this.reviewType = e.target.id
    },
    update() {
      this.isLoading = true
      updatepersonalgoals(this.formLabelAlign)
        .then((result) => {
          this.isLoading = false
          if (result.data) {
            this.$message({
              type: 'success',
              message: this.formLabelAlign.goalStatus === 0 ? '草稿已保存' :'提交成功',
            })
            this.$router.go(-1)
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    // 保存
    save() {
      this.isLoading = true
      addpersonalgoals(this.formLabelAlign)
        .then((result) => {
          this.isLoading = false
          if (result.data) {
            this.$message({
              type: 'success',
              message: this.formLabelAlign.goalStatus === 0 ? '草稿已保存' :'提交成功',
            })
            this.$router.go(-1)
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    headdenForm() {
      let formData = this.$refs.modelCom.getViewData()
      this.formLabelAlign.templateItemList = formData
      if (this.formLabelAlign.id) {
        delete this.formLabelAlign.createTime
        delete this.formLabelAlign.isChange
        this.update()
      } else {
        this.save()
      }
    },
    saveGoalAction(isDraft = false) {
      if (this.$route.query.templateId) {
        let viewList = this.$refs.modelCom.getList()
        if (viewList.length == 0) {
          this.$message({
            type: 'error',
            message: '暂时没有模板,不能保存',
          })
          return
        }
      }
      if (this.$route.query.id) {
        if (this.formLabelAlign.templateItemList.length == 0) {
            this.$message({
              type: 'error',
              message: '暂时没有模板,不能保存',
            })
            return
        }
      }
      this.formLabelAlign.copyPersons =
        this.peopleList.length > 0 ? this.peopleList.map((item) => item.id) : []
      this.formLabelAlign.personalGoalsUnitList = this.getUnitData()

      // 设置目标状态
      if (isDraft) {
        this.formLabelAlign.goalStatus = 0 // 保存草稿
      } else {
        this.formLabelAlign.goalStatus = 1 // 正式保存
      }

      this.$refs.modelCom.submitForm()
    },
    getUnitData() {
      var list = []
      var keys = [
        'informationAmount',
        'teachingMaterialAmount',
        'totalContract',
        'totalVisit',
      ]
      this.unitGoalList.forEach((item) => {
        var obj = Object()
        item.forEach((unitItem) => {
          obj.unitId = unitItem.unitId
          obj.year = this.formLabelAlign.year
          if (keys.indexOf(unitItem.unitgoalType) >= 0) {
            obj[unitItem.unitgoalType] = unitItem.goalNum
          }
        })
        list.push(obj)
      })
      return list
    },
    chooseunit() {
      this.unitDialogVisible = true
      var idsStr = this.unitIds.join(',')
      this.$refs.unitdialog.loadData(idsStr)
    },
    updateUnit(data) {
      this.unitIds.push(data.id)
      var list = deepClone(this.unitGoalItem2)
      list.forEach((element) => {
        element.unitId = data.id
        element.unitName = data.unitName
        element.year = this.year
        element.goalNum = ''
      })
      this.unitGoalList.push(list)
    },
    updateVisible(val) {
      this.unitDialogVisible = val
    },
    deleteUnitGoal(data) {
      if (data.id) {
        personalgoalsunitDelete({ id: data.id })
          .then((result) => {
            if (result.data) {
              this.unitGoalList.splice(data.index, 1)
              this.unitIds = this.unitIds.filter((item) => item !== data.unitId)
              this.$message({
                type: 'success',
                message: result.msg,
              })
            } else {
              this.$message({
                type: 'error',
                message: result.msg,
              })
            }
          })
          .catch((err) => {})
      } else {
        this.unitGoalList.splice(data.index, 1)
        this.unitIds = this.unitIds.filter((item) => item !== data.unitId)
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (columnIndex === 0 && rowIndex === 0) {
          return {
            rowspan: 4,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    closeTag(idtemId) {
      this.peopleList = removeById(this.peopleList, idtemId)
    },
    clickXuan() {
      this.$refs.systemdialog.loadData()
      this.peopleList
        ? this.$refs.systemdialog.updateWorksId(this.peopleList)
        : this.$refs.systemdialog.updateWorksId([])
      this.dialogSystemVisible = true
    },
    updateSystemVisible(val) {
      this.dialogSystemVisible = val
    },
    resArr(arr1, arr2) {
      const result = []
      const idSet = new Set()
      arr1.forEach((item) => {
        result.push(item)
        idSet.add(item.id)
      })
      arr2.forEach((item) => {
        if (!idSet.has(item.id)) {
          result.push(item)
        }
      })
      return result
    },
    submitData(data) {
      let newArr = this.resArr(this.peopleList, data)
      if (this.$route.query.id) {
        this.peopleList = newArr
      } else {
        this.peopleList = data
      }
      this.dialogSystemVisible = false
      // this.form.copyPersons = data.map(item => item.id)
    },
    back(){
      if (this.formLabelAlign.goalStatus == 1){
        this.$router.go(-1)
      }else{
        this.saveGoalAction(true)
      }

    },
  },
}
</script>

<style lang="scss" scoped>
.pd10 {
  padding-top: 10px;
  color: #606266;
  font-weight: 700;
  width: 120px;
  padding-right: 10px;
  text-align: right;
}
.pname {
  text-align: center;
}
.noimg {
  width: 48px;
  height: 48px;
  line-height: 48px;
  border-radius: 8px;
  background-color: #4285f4;
  text-align: center;
  color: #fff;
  font-size: 12px;
}
.uflex {
  display: flex;
  flex-wrap: wrap;
  width: calc(100% - 130px);
}
.closeimg {
  position: absolute;
  right: -8px;
  top: -8px;
  cursor: pointer;
}
.pli {
  margin-right: 24px;
  margin-bottom: 24px;
  position: relative;
}
.pimg {
  width: 48px;
  height: 48px;
  border-radius: 8px 8px 8px 8px;
}
.pflex {
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
}
.adddi {
  width: 44px;
  height: 44px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d6d6d6;
  text-align: center;
  line-height: 44px;
  font-size: 18px;
  color: #d6d6d6;
  cursor: pointer;
}
.zhu {
  font-family: SourceHanSansSC;
  font-weight: 300;
  font-size: 14px;
  color: rgb(173, 173, 173);
}
.bposi {
  position: relative;
  height: 34px;
  margin-bottom: 10px;
}
.borderdi {
  border: 1px solid #dcdfe6;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}
.mr {
  margin-left: 20px;
}
.upload-demo {
  margin-top: 20px;
}
.pd20 {
  padding: 20px 0;
}
.tright {
  text-align: center;
}

.maindiv {
  display: flex;
  padding-top: 20px;
}
.rightdiv {
  width: 20%;
  margin-left: 20px;
  border-radius: 10px;
}
.w40 {
  width: 40%;
  background-color: white;
}
.submitmask {
  width: 100%;
  text-align: center;
  margin-top: 20px;
}
.textAlign /deep/.el-input__inner {
  text-align: center !important;
}
.flexcss {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.textAlignRight {
  text-align: right;
}
.width200 {
  width: 200px;
  margin-bottom: 16px;
}
.definput /deep/.el-input-group__append {
  background-color: #f6f7fb !important;
  color: #333333 !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.rtextcss {
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  color: #999999;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.owntable /deep/ td,
th {
  padding: 8px 0 !important;
}
.owntable /deep/ th {
  background-color: #f6f7fb !important;
}
.owntable /deep/ .cell {
  font-size: 14px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400 !important;
  color: #333333 !important;
}
.mubiao {
  background: #fff;
  margin-top: 10px;
  padding: 24px 20px;
  min-height: 500px;
  box-sizing: border-box;
}
.inputcss {
  height: 36px !important;
}
.elform /deep/ .el-input__inner {
  line-height: 36px !important;
  height: 36px;
}
.deleteimg {
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.tablebox {
  height: 337px;
  overflow-x: hidden;
}
.asbu {
  position: absolute;
  right: 0;
  top: 0;
}
.rela {
  position: relative;
  margin-bottom: 24px;
}
.elform /deep/.el-input-group__append {
  padding: 0 12px !important;
}
.mt {
  margin-top: 20px;
}
.elform {
  width: 100%;
}
.elform .el-form-item {
  margin-bottom: 20px !important;
  margin-right: 0px !important;
}
.bbtn {
  height: 48px;
  line-height: 48px;
  background-color: #dfeafd;
  font-size: 16px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  letter-spacing: 1px;
  border-radius: 4px;
  cursor: pointer;
}
.bbtn:hover {
  background-color: #4285f4;
  color: white !important;
}
.bbtn + .bbtn {
  margin-top: 10px;
}
.owntop {
  width: 80%;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
}
.w60 {
  width: 60%;
}
.personalplan {
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  box-sizing: border-box;
}
.unitplan {
  flex: 1;
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  box-sizing: border-box;
}
</style>
