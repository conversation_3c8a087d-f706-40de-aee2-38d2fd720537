<template>
  <div class="mod-config">
    <div class="flexdiv">
      <div class="leftbu">
        <p class="rizhitext">日志</p>
        <el-button v-isShow="'crm:controller:dailyrecord:saveDailyRecord'" @click="writeDaily" icon="el-icon-plus" type="primary" class="bw">写日报</el-button>
         <div class="dbutton" :class="{ 'riactive': item.activeBoo }" v-for="(item,index) in riList" :key="index" @click="changeItem(item)">
            <img v-if="!item.activeBoo" class="icss" :src="item.dimg" alt="">
            <img v-if="item.activeBoo" class="icss" :src="item.aimg" alt="">
            <span>{{item.name}}</span>
         </div>
      </div>
      <div class="flex1">
        <div class="df">
          <div class="checkbox">
             <span class="shoutext">我收到的</span>
             <span class="shuxian">|</span>
             <el-checkbox v-model="pageBean.readStatus" false-label="0"  true-label="1" @change="changeCheckBox">只看未读</el-checkbox>
          </div>
          <el-form class="inlineform" size="small" ref="form" :model="pageBean" :inline="true">
            <el-form-item label="">
              <el-input v-model="pageBean.content" clearable placeholder="请输入关键词" @clear="clearInput"></el-input>
            </el-form-item>
            <el-form-item label="">
              <el-button class="search btn defaultbtn" type="primary" icon="el-icon-search" @click="onSearch">搜索
              </el-button>
              <el-button v-if="dataScope == 4" class="defaultbtn" type="primary" @click="exportData" icon="el-icon-download
" :loading="isDownload">导出日报</el-button>
              <span ref="projectButton">
                <el-popover
                ref="elPopover"
                placement="bottom-end"
                width="380"
                trigger="manual"
  v-model="visible_2">
                <div>
                    <p class="titlep">时间筛选</p>
                    <el-date-picker
                    value-format="yyyy-MM-dd"
                    class="datepicker"
                    v-model="timeDate"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                  </el-date-picker>
                  <p class="titlep">发送人:</p>
                  <div class="senddiv" @click="chooseDepartment">
                      {{peopleNames}}
                  </div>
                  <div class="bri">
                    <el-button @click="resetButton">重置</el-button>
                    <el-button type="primary" @click="dialogButton">确定</el-button>
                  </div>
                </div>
                <el-button @click="visible_2 = !visible_2" slot="reference" class="search btn defaultbtn" type="primary" >
                  <i class="el-icon-devops" />		
                </el-button>
              </el-popover>
              </span>
           
            </el-form-item>
          </el-form>
        </div>
        <el-row  class="el-rowone">
          <el-col :span="5" class="wcolor clrp letree">
              <div v-if="daliyUser.length==0" class="nolist">
                    <nolist></nolist>
              </div>
              <div v-if="daliyUser.length>0" class="cflex cfd leall" id="scrollLeft" @scroll="scrollDivLeft">
                <div v-for="(item,index) in daliyUser" :key="index">
                  <p>{{item.date}}</p>
                  <ul>  
                   <li @click="pchange(itemChild)" :class="itemChild.id == selectedId ? 'activecss' : ''"  class="rili" v-for="(itemChild,index) in item.list" :key="index">
                      <headuser :url="itemChild.logo"   class="usercss" borderRadius="8px"  width="28" :username="itemChild.createByName"></headuser>
                      <span>
                         {{itemChild.createByName}}的日报
                      </span>
                      <span class='readb noread' v-if="itemChild.status==1">
                        未读
                      </span>
                      <span class="readb yesread" v-if="itemChild.status == 2">
                       已读
                     </span>
                   </li>
                  </ul>
                </div>
              </div>
              <div class="loading"  v-loading="loadingLeft">
              </div>
          </el-col>
          <el-col :span="19" class="cpding" >
            <div v-if="dataList.length==0" class="nolist">
                 <nolist></nolist>
            </div>
            <div v-if="dataList.length>0" class="tablecss wcolor  scrollDiv" ref="configtypetable" @scroll="scrollDiv" id="scrollDiv">
              <div class="w800">
                <listitem    @updateTopBottom="updateTopBottom"  @dataListDelete="dataListDelete" :index="index"  @showRead="showRead" :isShowButton="true" @viewDetail="viewDetail" @showCustomer="showCustomer" ref="ListRef"    :key="index" v-for="(item,index) in dataList" :item="item"></listitem>
              </div>
            </div>
            <div class="loading"  v-loading="loading">
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <el-drawer
      class="drawidth"
      :modal="false"
      title=""
      :visible.sync="drawerBoo"
      :with-header="false">
      <div class="pdra">
        <dailydetail ref="dailydetail" @closeBack="closeBack"></dailydetail>
      </div>
    </el-drawer>
    <el-dialog
    title="写日志"
    :visible.sync="dialogRiVisible"
    width="60%"
    center
    :before-close="handlePlanClose"
    class='logDialog'
    >
    <span>
      <template>
        <div class="maskview">
          <div class="conntent">
            <el-row :gutter="20" style="width: 100%" justify="start" align="center">
              <el-col :span="6" v-for="(item,index) in templateList" :key="item.id">
                <div @click="changeClass(index)" class="modelitem" :class="{ 'active-class': nowIndex === index }">
                  <div class="modeltitle">
                    {{ item.templateName }}
                    <div class="rt" v-if="nowIndex === index "></div>
                  </div>
                  <div class="tagview">
                    <el-tag
                      class="tagitem"
                      v-for="itemtag in item.templateItemVoList"
                      :key="itemtag.id"
                      >{{ itemtag.itemName }}</el-tag
                    >
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="nextAction">确定</el-button>
    </span>
  </el-dialog>
  <departmentDialogDaily :isDaily="true" ref="deRef" :dType="diaType" v-if="dialogDepartmentVisible" :dialogDepartmentVisible="dialogDepartmentVisible" @updateVisible="updateSystemVisible"
    @submitData="submitData">
    </departmentDialogDaily>

    <customerMsg ref="customerMsgRef" :visible.sync="drawerVisible" @closeCus="closeCus"></customerMsg>
    
    <readRecord :notReadLogs="notReadLogs" :readLogs="readLogs"  :visible.sync="drawerVisibleRead" @closeRead="closeRead"></readRecord>
 
  </div>
</template>

<script>
  import headuser from '@/components/common/headuser.vue'
  import { Loading } from 'element-ui';
  import listitem from './listitem.vue';
  import {
  saveDailyRecord,
  listDailyPaper,
  queryDailyList,
  readDailyRecordVo,
} from "@/api/daily";
  import dailydetail from './dailydetail.vue'
  import { findTemplate } from '@/api/common';
  import { isCheckPermission,dailyRecordDownload } from '@/api/daily/index';
  import departmentDialogDaily from '@/components/common/departmentDialogDaily.vue';
  import customerMsg  from "../common/customerMsg.vue";
  import readRecord  from "../common/readRecord.vue";
  import nolist from "@/components/common/nolist.vue";
  import { downloadFileByUrl } from "@/utils/tools";
  export default {
    data() {
      return {
        isDownload:false,
        readLogs:[],
        notReadLogs:[],
        totalLeft:0,
        onOffLeft:false,
        loadingLeft:false,
        drawerVisibleRead:false,
        visible_2:false,
        dialogDepartmentVisible:false,
        diaType:'1',
        nowIndex:0,
        templateList:[],
        rules:{
          template: [
            { required: true, message: '请选择模板', trigger: 'change' }
          ],
        },
        addForm:{
          template:"",
        },
        dataScope:sessionStorage.getItem('dataScope'),
        dialogRiVisible:false,
        drawerBoo:false,
        riList:[
          {
            id:2,
            name:'我收到的',
            dimg:require('../../../assets/shoudao.png'),
            aimg:require('../../../assets/ashoudao.png'),
            activeBoo:true,
          },
          {
            id:1,
            name:'我发出的',
            dimg:require('../../../assets/fachu.png'),
            aimg:require('../../../assets/afachu.png'),
            activeBoo:false,
          },
          {
            id:3,
            name:'抄送给我的',
            dimg:require('../../../assets/chao.png'),
            aimg:require('../../../assets/achao.png'),
            activeBoo:false,
          },
        ],
        daliyUser:[],
        timeDate:[],
        isLoading: false,
        pageBean: {
          dailyType: '2',
          userIds: '',
          pageNum: 1,
          pageSize: 20,
          content: '',
          startTime:'',
          endTime:'',
          goalsType:'7',
          dailyRecordId:'',
          readStatus:'',
        },
        pageBeanLeft: {
          dailyType: '2',
          userIds: '',
          pageNum: 1,
          pageSize: 20,
          content: '',
          startTime:'',
          endTime:'',
          goalsType:'7',
          readStatus:"",
        },
        onOff:false,
        dataList: [],
        total: 0,
        dimg: require('@/assets/touxiang.png'),
        isActiveIndex:0,
        peopleList:[],
        drawerVisible:false,
        loading:false,
        isLeft:false,
        offsetTopArr:[],
        selectedId:'',
        oldScrollTop:0,
        selectedRightId:'',
        scrollIndex:0,
        userIds:[],
        peopleNames:'',
        statusPar:{
          relateId:'',
        },
        isCheck:false,
        isType:'2',
      }
    },
    components: {
      headuser,
      listitem,
      dailydetail,
      departmentDialogDaily,
      customerMsg,
      readRecord,
      nolist
    },
    watch: {
      scrollIndex(newVal, oldVal) {
                if(newVal>oldVal){
                    this.scrollLeft(true)
                }else{
                    this.scrollLeft(false)
                }
      },
      timeDate(newVal, oldVal){
          if(newVal == null){
            this.timeDate = []
          }
      }
    },
    methods: {
    
      changeCheckBox(value){
          this.pageBeanLeft.pageNum = 1
          this.pageBean.pageNum = 1
          this.pageBeanLeft.readStatus = this.pageBean.readStatus
          this.getData(true)
          this.getLeftData(true)
      },
      clearInput(){
          this.pageBean.content = ''
          this.pageBeanLeft.content = ''
          this.pageBeanLeft.pageNum = 1
          this.pageBean.pageNum = 1
          this.getData(true)
          this.getLeftData(true)
      },
      dataListDelete(index){
        this.findIdIn2DArrayDelete(this.dataList[index].id)
        this.dataList.splice(index, 1);
      },
      findIdIn2DArrayDelete(id) {
        let arr = this.daliyUser;
        for (let i = 0; i < arr.length; i++) {
          for (let j = 0; j < arr[i].list.length; j++) {
            if (arr[i].list[j].id === id) {
              arr[i].list.splice(j, 1);
              break;
            }
          }
          if(arr[i].list.length == 0){
            arr.splice(i,1)
            break;
          }
        }
      },
      pchange(item){
          this.selectedId = item.id
          this.pageBean.dailyRecordId = item.id
          this.pageBean.pageNum = 1
          this.getData(true)
      },
      scrollLeft(boo){
                    let activecss = document.querySelectorAll(".activecss");
                    let element = document.getElementById('scrollLeft')
                    var  intElemScrollTop = element.scrollTop;
                    if(boo){
                      this.$nextTick(()=>{
                        if(activecss[0].offsetTop > 382){
                              element.scrollTop = intElemScrollTop += 36;
                        }
                      })
                    }else{
                      this.$nextTick(()=>{
                        if(activecss[0].offsetTop > 382){
                              element.scrollTop = intElemScrollTop -= 36;
                        }
                      })
                    }
                   
      },
      showRead(item){
        this.readLogs = item.readLogs
        this.notReadLogs = item.notReadLogs
        this.drawerVisibleRead = true
      },
      closeRead(){
          this.drawerVisibleRead = false
      },
      dialogButton(){
        if((this.timeDate==null || this.timeDate.length==0)  && this.userIds.length==0){
              this.$message({
                type: "error",
                message: "请选择时间或者发送人"
              })
              return 
        }
          if(this.timeDate && this.timeDate.length>0){
            if(this.timeDate.length ==2){
                  this.pageBean.startTime = this.timeDate[0]
                  this.pageBean.endTime = this.timeDate[1]
                  this.pageBeanLeft.startTime = this.timeDate[0]
                  this.pageBeanLeft.endTime = this.timeDate[1]
            }
          }
          if(this.userIds.length>0){
              this.pageBean.userIds = this.userIds.map(item=>item.id).join(',')
              this.pageBeanLeft.userIds = this.pageBean.userIds
          }
          this.pageBeanLeft.pageNum = 1
          this.pageBean.pageNum = 1
          this.getData(true)
          this.getLeftData(true)
      },
      resetButton(){
                this.userIds = []
                this.timeDate =[]
                this.pageBean.startTime = ''
                this.pageBean.endTime = ''
                this.pageBean.userIds = ''
                this.pageBeanLeft.startTime = ''
                this.pageBeanLeft.endTime = ''
                this.pageBeanLeft.userIds = ''
                this.peopleNames = ''
                this.pageBeanLeft.pageNum = 1
                this.pageBean.pageNum = 1
                this.getData(true)
                this.getLeftData(true)
      },
      chooseDepartment(){
        this.dialogDepartmentVisible = true
        this.$nextTick(()=>{
          this.$refs.deRef.loadData()
          this.$refs.deRef.updateWorksId(this.peopleList)
        })
    },
      submitData(data) {
        this.userIds = data
        this.peopleNames = this.userIds.map(item=>item.name).join(',')
        this.dialogDepartmentVisible = false
      },
      updateSystemVisible(val){
        this.dialogDepartmentVisible = val
      },
      changeClass (index) {
        this.nowIndex = index
      },
      loadTemplate(){
        findTemplate({
          methodName:'save',
          className:'PersonalGoalsController',
          templateType:7
        }).then((result) => {
          this.templateList = result.data;
        }).catch((err) => {
          
        });
      },
      handlePlanClose(){
        this.dialogRiVisible = false;
      },
      nextAction(){
        isCheckPermission({
          templateId:this.templateList[this.nowIndex].id,
        }).then((result) => {
          if(result.status == 0){
              this.$router.push({
                path:"/daily/dailylist/adddaily",
                query:{
                  templateId:this.templateList[this.nowIndex].id,
                  allowCopy:this.templateList[this.nowIndex].allowCopy,
                  minNum:this.templateList[this.nowIndex].minNum,
                  maxNum:this.templateList[this.nowIndex].maxNum,
                  wordsNum:this.templateList[this.nowIndex].wordsNum,
                }
              })
          }else{
              this.$message({
                type: "error",
                message: "该模板暂时没有没有设置权限,不能使用"
              })
          }
        }).catch((err) => {
          
        });
      },
      closeBack(){
        this.drawerBoo = false
      },
      viewDetail(item){
          this.drawerBoo = true
          this.$nextTick(()=>{
            this.$refs.dailydetail.loadInfo(item)
          })
      },
     writeDaily(){
        this.dialogRiVisible =true
        this.loadTemplate();
      },
      changeItem(item){
            this.isType = item.id
            this.riList.forEach(item=>{
              item.activeBoo = false
            })
            item.activeBoo = true
            this.pageBean.dailyType = item.id
            this.pageBeanLeft.dailyType = item.id
            this.pageBeanLeft.pageNum = 1
            this.pageBean.pageNum = 1
            this.getData(true)
            this.getLeftData(true,true)
      },
      findIdIn2DArray(array, id) {
        for (let i = 0; i < array.length; i++) {
          for (let j = 0; j < array[i].list.length; j++) {
            if (array[i].list[j].id === id) {
              this.$set(array[i].list[j],'status',2)
            }
          }
        }
      },
      checkApi(callback){
        readDailyRecordVo(this.statusPar).then((result) => {
              if (result.data) {
                callback()
              }else{
                this.$message({
                  type:"error",
                  message:result.msg
                })
              }
            }).catch((err) => {
              
            })
      },
      executeAudit(selectedId,dataItem){
                if(!this.isCheck){
                  this.isCheck = true
                  this.statusPar.relateId = selectedId
                  if(this.isType == 2){
                    this.statusPar.goalsType = 7
                  }else if(this.isType == 3){
                    this.statusPar.goalsType = 8
                  }
                  this.checkApi(()=>{
                    dataItem.status = 2
                    dataItem.readNum +=1 
                    this.$set(dataItem, "readNum", dataItem.readNum);
                    if(dataItem.readNum == dataItem.summaryLogsNum){
                      dataItem.allReadFlag=1
                      this.$set(dataItem, "allReadFlag", 1);
                    }
                    this.isCheck = false
                    this.findIdIn2DArray(this.daliyUser,selectedId)
                  })
                }
      },
      handleBottom(){
          console.log(this.scrollIndex)
          console.log(this.dataList.length)
      },
      scrollDiv(e){
        const scrollTop =  document.getElementById('scrollDiv').scrollTop;
        for (let i = 0; i < this.offsetTopArr.length; i++) {
          if (this.offsetTopArr[i].itemTop<=scrollTop && scrollTop<this.offsetTopArr[i].itemBottom) {
            this.selectedId = this.dataList[i].id;
            this.scrollIndex = i
            if(this.dataList[i].status == 1){
                this.executeAudit(this.selectedId,this.dataList[i])
            }
          }
          if((this.offsetTopArr[i].itemTop< scrollTop)  && (scrollTop<this.offsetTopArr[i].itemBottom-220)){
            this.dataList[i].isFixed = true
          }else{
            this.dataList[i].isFixed = false
          }   
        }
        if(!this.onOff){
          const scrollTop = e.target.scrollTop // 滚动条滚动时，距离顶部的距离
          const windowHeight = e.target.clientHeight // 可视区的高度
          const scrollHeight = e.target.scrollHeight // 滚动条的总高度
          // 滚动条到底部
          if (scrollTop + windowHeight === scrollHeight) {
              this.onOff = true
              this.loading = true
              this.pageBean.pageNum++
              if (this.pageBean.pageNum > Math.ceil(this.total / this.pageBean.pageSize)) {
                this.loading = false
                this.handleBottom()
                return
              }
              this.getData()
              this.pageBeanLeft.pageNum++
              this.getLeftData()
          }
        }
      },
      scrollDivLeft(e){
        const scrollTop =  document.getElementById('scrollLeft').scrollTop;
        if(!this.onOffLeft){
          const scrollTop = e.target.scrollTop // 滚动条滚动时，距离顶部的距离
          const windowHeight = e.target.clientHeight // 可视区的高度
          const scrollHeight = e.target.scrollHeight // 滚动条的总高度
          // 滚动条到底部
          if (scrollTop + windowHeight === scrollHeight) {
              this.onOffLeft = true
              this.loadingLeft = true
              this.pageBeanLeft.pageNum++
              if (this.pageBeanLeft.pageNum > Math.ceil(this.totalLeft / this.pageBeanLeft.pageSize)) {
                this.loadingLeft = false
                return
              }
              this.getLeftData()
          }
        }
      },
      getData(isSearch){
        listDailyPaper(this.pageBean).then(res=>{
          if(res.status == 0){
            if(res.data){
                  let rData = res.data
                  rData.forEach(item => {
                    this.$set(item, 'isFixed', false )
                  });
                  if(isSearch){
                    this.dataList = []
                  }
                  this.dataList = [...this.dataList,...res.data]
                  this.total = res.page.total
                  this.onOff = false
                  this.loading = false
                  this.getOffsetTopArr();
                  if(isSearch){
                    if(this.dataList.length>0){
                      if(this.dataList[0].status == 1){
                          this.executeAudit(this.dataList[0].id,this.dataList[0])
                      }
                    }
                  }
                  
            }
          }
        })
      },
      getLeftData(isSearch,isFirst){
        queryDailyList(this.pageBeanLeft).then(res=>{
          if(res.status == 0 && res.data){
            if(isSearch){
              this.daliyUser = []
            }
            this.daliyUser = [...this.daliyUser,...res.data]
            this.totalLeft = res.page.total
            this.onOffLeft = false
            this.loadingLeft = false
            if(isFirst){
              if(this.daliyUser.length>0 && this.daliyUser[0].list.length>0 ){
                this.selectedId = this.daliyUser[0].list[0].id
              }
            }
          }else{
            this.daliyUser = []
          }
        })
      },
      onSearch() {
        if(!this.timeDate){
                this.pageBean.startTime = ''
                this.pageBean.endTime = ''
                this.pageBeanLeft.startTime = ''
                this.pageBeanLeft.endTime = ''
        }
        this.pageBeanLeft.pageNum = 1
        this.pageBean.pageNum = 1
        this.pageBeanLeft.content = this.pageBean.content
        this.getData(true)
        this.getLeftData(true)
      },
      closePover(e){
          let self = this
          if(this.visible_2 && !this.$refs.projectButton.contains(e.target)){
              this.visible_2 = false
          }
      },
      showCustomer(customerId){
        this.drawerVisible = true;
        this.$nextTick(()=>{
          this.$refs.customerMsgRef.loadDataInfo(customerId);
        })
      },
      closeCus(){
        this.drawerVisible = false;
      },
      updateTopBottom(){
          // setTimeout(()=>{
          //   this.getOffsetTopArr()
          // },300)
      },
      getOffsetTopArr() {
        this.$nextTick(() => {
          this.offsetTopArr = []
          let rightListItems = document.querySelectorAll(".right_item");
          console.log(rightListItems)
          if (rightListItems && rightListItems.length > 0) {
            rightListItems.forEach((item) => {
              this.offsetTopArr.push(
                {
                  itemTop:item.offsetTop,
                  itemBottom:item.offsetTop + item.offsetHeight
                }
              );
            });
          }
        });
      },
      exportData(){
        var exportPar = Object.assign({},this.pageBean)
        delete exportPar.pageNum
        delete exportPar.pageSize
        exportPar.dailyRecordId = ''
        exportPar.dailyType = ''
        this.isDownload = true;
        console.log('conlose.log',exportPar);
        dailyRecordDownload(exportPar).then((result) => {
          if (result.data.url){
            downloadFileByUrl(result.data.url,result.data.fileName)
          }else{
            this.$message({
              type:'error',
              message:'下载失败'
            })
          }
            this.isDownload = false;
          }).catch((err) => {
            this.isDownload = false;
          });
      },
    },
    created() {
      this.getData()
      this.getLeftData(false,true)
    },
    mounted(){
      document.addEventListener('click',this.closePover)
    },
    
  }
</script>

<style scoped lang="scss">
  .leftAll{
      position: relative;
  }
  .nolist{
    height: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    flex-direction:column;
    width: 100%;
  }
  .loading{
    width: 100px;
    height: 60px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
  }
  .w800{
    width: 740px;
    margin: 0 auto;
  }
  .bri{
    display: flex;
    justify-content:flex-end;
  }
  .datepicker{
    margin-bottom: 20px;
  }
  .titlep{
    margin-bottom: 10px;
  }
  .senddiv{
    height: 36px;
    border: 1px solid #dcdfe6;
    margin-bottom: 20px;
    border-radius: 4px;
    cursor: pointer;
    padding: 6px 12px;
    box-sizing: border-box;
  }
  .senddiv:hover{
    border: 1px solid #4285F4;
    
  }
  .logDialog /deep/.el-dialog {
      min-width: 1000px !important;
  }
  .pdra{
    padding: 20px;
    background: #f7f7f7;
  }
  .drawidth{
    left: 170px;
    top: 56px;
    right: 0px;
    bottom:0px;
  }
  .drawidth /deep/.el-drawer{
      width: 100% !important;
  }
  .yesread{
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #BAC9E3;
    font-weight: 400;
    color: #BAC9E3;
  }
  .active-class{
    border: 1px solid #4285F4;
  }
  .icss{
    margin-right: 6px;
  }
  .dbutton{
    height: 34px;
    line-height: 34px;
    padding-left: 8px;
    cursor: pointer;
    border-radius: 4px 4px 4px 4px;
    margin: 10px 0;
  }
  .riactive{
    background: #DFEAFD;
  }
  .activecss{
    background: #EFF5FF;
  }

  .shuxian{
    color: #D6D6D6;
    margin: 0 12px;
  }
  .shoutext{
    font-weight: 400;
    font-size: 14px;
    color: #666666;
  }
  .rizhitext{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #999999;
    margin-bottom: 16px;
  }
  .df{
    background: #fff;
    padding: 12px 16px;
    border-bottom: 1px solid #EBEBEB;
  }
  ::v-deep .el-icon-devops {
  background: url('../../../assets/shai.png') center no-repeat;		
}

::v-deep .el-icon-devops:before {
  content: "替";
  visibility: hidden;
}
.scrollDiv{
  overflow-y: auto;
}

  .usercss{
    margin-top: 4px;
  }
  .checkbox{
    background: #fff;
    width: 240px;
    height: 32px;
    line-height: 30px;
  }
  .noread{
    color: #4285F4;
    border: 1px solid #4285F4;
  }
  .rili{
    height: 36px;
    line-height: 36px;
    margin: 10px 0;
    position: relative;
    padding:0 4px;
    cursor: pointer;
  }
  .readb{
    width: 40px;
    float: right;
    margin-top: 8px;
    font-size: 12px;
    height: 20px;
    line-height: 18px;
    text-align: center;
    border-radius: 4px 4px 4px 4px;
  }
  .df{
    display: flex;
  }
  .inlineform{
    display: inline-block;
    flex: 1;
    text-align: right;
  }
  .inlineform .el-form-item{
    margin-right: 0px !important;
    margin-bottom: 0px;
  }
  .bw{
    width: 105px;
    padding: 8px 10px !important;
    margin-bottom: 10px;
  }
  .cml{
    margin-left: 0px;
  }
  .flex1{
    flex:1;
  }
  .leftbu{
    width: 130px;
    background: #fff;
    padding: 12px;
    border-right: 1px solid #EBEBEB;
  }
  .flexdiv{
    display: flex;
    height: 100%;
  }


  .username {
    clear: both;
    position: relative;
    top: 10px;
  }


  .avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .leall {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .letree {
    display: flex;
    margin-left: 0 !important;
    position: relative;
    border-right: 1px solid #EBEBEB;
  }

  .mod-config {
    height: 100%;
  }

  .search {
    margin-left: 10px;
  }

  .tablecss {
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  .clrp {
    padding-left: 0 !important;
    padding-right: 0 !important;
    min-width: 240px;
  }

  .cpding {
    padding-right: 0 !important;
    display: flex;
    flex-direction: column;
    position: relative;
    background: #fff;
    position: relative;
  }

  .el-rowone {
    margin-left: 0 !important;
    margin-right: 0 !important;
    display: flex;
    height: calc(100% - 59px);
  }

  .wcolor {
    background: #fff;
  }

  .tagitem {
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 18px;
  border: 0;
}
.tagview {
  padding: 20px 20px;
  overflow: hidden;
}
.bottomview {
  position: absolute;
  bottom: 15px;
  right: 15px;
}
.modeltitle {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #e1e1e1;
  padding: 0px 15px;
  position: relative;
  text-align: left;
  font-size: 18px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.rt {
  width: 30px;
  height: 30px;
  border-top: 15px solid #e9523f;
  border-right: 15px solid #e9523f;
  border-left: 15px solid rgba(0, 0, 0, 0);
  border-bottom: 15px solid rgba(0, 0, 0, 0);
  box-sizing: border-box;
  position: absolute;
  top: 0;
  right: 0;
}
.addbtn {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #f8b84a;
  font-size: 18px;
  line-height: 250px;
}
.additem {
  height: 240px;
  border: 1px solid #f8b84a;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
}
.modelitem {
  cursor: pointer;
  height: 240px;
  background: #ffffff;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  position: relative;
  margin-bottom: 20px;
}
.addimage {
  width: 33px;
  height: 33px;
  margin-right: 5px;
}
.conntent {
  background-color: white;
}
.maskview::-webkit-scrollbar {
  display: none;
}
.el-col:nth-child(4n + 1) {
  margin-left: 10px;
}
.el-col:nth-child(4n) {
  margin-right: -10px;
}
.maskview {
  position: relative;
  height: 100%;
}
.headertitle {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 16px;
  color: #2490ff;
  font-weight: bold;
  height: 20px;
  line-height: 20px;
  margin-bottom: 10px;
}
.headertitle .dian {
  width: 10px;
  height: 10px;
  border-radius: 4px;
  background-color: #2490ff;
  margin-right: 5px;
}
</style>