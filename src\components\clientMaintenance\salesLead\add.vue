<template>
  <div>
    <div>
      <back>{{ form.id ? '编辑机会' : '新建机会' }}</back>
    </div>
    <div class="mainbg" v-loading="isLoading">
      <el-form
        ref="addform"
        :model="form"
        :rules="rules"
        class="addfcss"
        label-width="118px"
      >
        <textBorder>基础信息</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="10">
              <el-form-item label="机会名称：" prop="opportunityName">
                <el-input
                  class="definput"
                  v-model="form.opportunityName"
                  placeholder="请输入机会名称"
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item label="关联客户：" prop="customerId">
                <div class="unitbtn" @click="chooseCustomer">
                  <span v-if="form.customerId" class="deffont">{{
                    form.customerName
                  }}</span>
                  <span v-else class="pltcss">请选择</span>
                  <i class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
              <el-form-item label="机会阶段：" prop="opportunityStage">
                <el-select
                  class="definput"
                  popper-class="removescrollbar"
                  v-model="form.opportunityStage"
                  placeholder="请选择"
                  :disabled="$route.query.id?true:false"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="类型：" prop="opportunityTypeSub">
                <el-select
                  @change="changeValue"
                  class="definput w100 ellipsis"
                  popper-class="removescrollbar"
                  v-model="form.opportunityType"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in optionsContract"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>

                <el-select
                  class="definput width2 ml10"
                  popper-class="removescrollbar"
                  v-model="form.opportunityTypeSub"
                  clearable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in optionsContractDetail"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="预计金额：" prop="estimatedAmount">
                <el-input
                  class="definput"
                  v-model="form.estimatedAmount"
                  type="number"
                  placeholder="请输入预计金额（万元）"
                ></el-input>
              </el-form-item>
              <el-form-item label="预计时间：" prop="estimatedTime">
                <el-date-picker
                  class="definput width100 datepicker"
                  v-model="form.estimatedTime"
                  type="month"
                  placeholder="选择预计时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <textBorder
          class="mt30"
          v-if="form.opportunityType == '1215413373253166238'"
          >教材</textBorder
        >
        <div
          class="pt20"
          v-if="form.opportunityType == '1215413373253166238'"
        >
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="10">
              <el-form-item label="用书专业：" prop="bookSpecialty">
                <el-select
                  class="definput"
                  popper-class="removescrollbar"
                  v-model="form.bookSpecialty"
                  placeholder="请先选择关联客户后再选择用书专业"
                >
                  <el-option
                    v-for="item in majorList"
                    :key="item.id"
                    :label="item.specialtyName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="选择教材：" prop="materialId">
                <div class="flex">
                  <div class="chooseBookBtn">
                    <el-button
                      class="bBtn"
                      icon="el-icon-plus"
                      type="text"
                      @click="chooseBook"
                    >
                      点击选择教材
                    </el-button>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item prop="useBookYear" label="用书时间：">
                <el-date-picker
                  class="definput wid100"
                  v-model="useBookYear"
                  type="year"
                  format="yyyy"
                  value-format="yyyy年"
                  placeholder="选择年"
                >
                </el-date-picker>
                <span class="pdl">
                  <el-radio class="mr10" v-model="radio" label="春季"
                    >春季</el-radio
                  >
                  <el-radio class="mr10" v-model="radio" label="秋季"
                    >秋季</el-radio
                  >
                </span>
              </el-form-item>
            </el-col>
          </el-row>

        </div>
        <!-- 选中教材表格 -->
        <textBorder v-if="form.opportunityType == '1215413373253166238'">已选教材</textBorder>
        <el-table class="mytable" v-if="form.opportunityType == '1215413373253166238'" :data="bookList" border style="width: 100%">
          <el-table-column prop="name" label="教材名称" min-width="167px"></el-table-column>
          <el-table-column prop="isbn" label="ISBN" min-width="167px"></el-table-column>
          <el-table-column
            prop="platformName"
            label="出版社"
            min-width="167px"
          ></el-table-column>
          <el-table-column prop="publicationRevisionTime" label="出版时间"  min-width="167px" ></el-table-column>
          <el-table-column prop="author" label="作者" min-width="167px"></el-table-column>
          <el-table-column
            prop="price"
            label="价格（元）"
            fixed="right"
            align="center"
            min-width="100px"
          ></el-table-column>
          <el-table-column label="用书册数" fixed="right" align="center" min-width="167px">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.number"
                :min="1"
                :controls="false"
                placeholder="请输入册数"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="总码洋" width="150px" fixed="right" align="center" >
          <template slot-scope="scope">
            <span>{{scope.row.number && (scope.row.price * scope.row.number).toFixed(2)}}</span>
          </template>
        </el-table-column>
          <el-table-column label="操作" width="100" fixed="right" align="center">
            <template slot-scope="scope">
              <span
                class="rbtn"
                @click="removeBook(scope.$index)"
                >移除</span>
            </template>
          </el-table-column>
        </el-table>
        <textBorder class="mt30">负责与协作</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="10" v-if="isShowChargePerson">
              <el-form-item required label="负责人：" prop="chargePerson">
                <span v-if="form.chargePerson" class="mr10">{{
                  form.chargePersonName
                }}</span>
                <el-button
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择负责人', 1)"
                >
                  点击选择负责人</el-button
                >
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="协作人：" prop="collaborator">
                <el-tag
                  class="tagcss"
                  size="small"
                  v-for="item in xiezuolist"
                  closable
                  @close="handleTagClose(item)"
                  :key="item.id"
                  >{{ item.name }}</el-tag
                >
                <el-button
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择协作人', 5)"
                >
                  点击选择协作人</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">补充信息</textBorder>
        <div class="pt20">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="20">
              <el-form-item label="备注：" prop="notes">
                <el-input
                  class="definput"
                  v-model="form.notes"
                  maxlength="300"
                  show-word-limit
                  type="textarea"
                  rows="4"
                  placeholder="请输入备注信息"
                ></el-input>
              </el-form-item>
              <el-form-item label="附件：" prop="fileInfoList">
                <upload2
                  ref="upload"
                  :data="{ serviceName: 'crm/opportunity' }"
                  :limit="1"
                  :accept="'.pdf,.doc,.docx,.xlsx'"
                  @submitImg="submitSuccess"
                >
                  <span class="studiocss">
                    <img src="../../../assets/img/file_icon.png" />
                    <span class="uploadtext deffont">点击上传附件</span>
                  </span>
                  <template slot="ptip">
                    <p>只能上传pdf,doc,docx,xlsx文件</p>
                  </template>
                </upload2>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="pt20 btncenter">
          <el-form-item>
            <el-button
              class="btn_h42 wid98"
              type="primary"
              @click="submitForm"
              v-dbClick
              >保存</el-button
            >
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 负责人/协作人 -->
    <!-- 负责人/协作人 -->
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
    <!-- 关联客户 -->
    <cuatomerDialog
      ref="customer"
      :visible.sync="customerDialogVisible"
      @updateVisible="updateVisible"
      @updateCustomer="updateCustomer"
    >
    </cuatomerDialog>
    <!-- 选择教材 -->
    <multiplechooseBookDialog
      ref="book"
      :visible.sync="bookDialogVisible"
      @updateVisible="updateBookVisible"
      @updateData="updateData"
    >
    </multiplechooseBookDialog>
     <!-- 部门验证组件 -->
    <verifyDeparment
      ref="verifyDeparment"
      @submit="submitWithDepartment"
    ></verifyDeparment>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import { getToken } from '@/utils/auth'
import textBorder from '../../common/textBorder.vue'
import systemDialog from '../../common/systemDialog.vue'
import cuatomerDialog from '../../common/customerDialog.vue'
import multiplechooseBookDialog from '../../common/multiplechooseBookDialog.vue'
import upload2 from '../../common/upload2.vue'
import { getDict, getFileType, addSnop } from '@/utils/tools'
import {
  addOpportunity,
  opportunityInfo,
  updateOpportunity,
} from '@/api/clientMaintenance/opportunity'
import { selectUintSpecialty } from '@/api/clientmanagement/customer'
import { queryBookInfo } from '@/api/product/index'
import { queryVoListByCode } from '@/api/wapi.js'
import VerifyDeparment from '@/components/common/verifyDeparment.vue'
export default {
  components: {
    back,
    textBorder,
    systemDialog,
    cuatomerDialog,
    multiplechooseBookDialog,
    upload2,
    VerifyDeparment
  },
  data() {
    return {
      bookList: [],
      bookDialogVisible: false,
      customerDialogVisible: false,
      popoverVisibble: false,
      dialogVisible: false,
      isSmall: window.screen.availWidth < 1500 ? true : false,
      isShowChargePerson: this.$route.query.id ? true : false,
      dialogName: '',
      multipleNum: 1,
      isLoading: false,
      rules: {
        opportunityName: [
          { required: true, message: '请输入销售机会名称', trigger: 'blur' },
          {
            min: 3,
            max: 100,
            message: '长度在 3 到 100 个字符',
            trigger: 'blur',
          },
        ],
        opportunityStage: [
          { required: true, message: '请选择机会阶段', trigger: 'change' },
        ],
        opportunityTypeSub: [{ required: true, message: '请选择机会类型' }],
        customerId: [
          { required: true, message: '请选择关联客户', trigger: 'change' },
        ],
        chargePerson: [
          { required: true, message: '请选择负责人', trigger: 'change' },
        ],
      },
      form: {
        id: '',
        opportunityName: '',
        opportunityStage: '',
        customerId: '',
        estimatedAmount: undefined,
        estimatedTime: '',
        useYookYear: '',
        chargePerson: window.sessionStorage.getItem('userid'),
        collaborator: '',
        opportunityDepartmentId: '',
        notes: '',
        opportunityTypeSub: '',
        opportunityType: '',
        fileInfoList: [],
        distributionDetailList:[],
      },
      getUrl: `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`,
      headers: { Authorization: getToken() },
      fileData: {
        serviceName: 'crm/opportunityName',
      },
      xiezuolist: [],
      isChooseCustomer: false,
      options: [],
      types: [],
      majorList: [],
      unitDialogVisible: false,
      fileType: {
        image: 1,
        video: 2,
        redio: 3,
        file: 4,
      },
      useBookYear: '',
      radio: '春季',
      bookInfo: {
        name: '',
        isbn: '',
      },
      optionsContract: [],
      optionsContractDetail: [],
    }
  },
  created() {
    if (this.$route.query.customerId) {
      this.isChooseCustomer = true
      this.form.customerId = this.$route.query.customerId
      this.form.customerName = this.$route.query.customerName
      this.form.unitId = this.$route.query.unitId
      this.form.unitName = this.$route.query.unitName
    } else {
      this.isChooseCustomer = false
    }
    this.getTypeList()
    this.getDictApi()
    this.loadInfo()
  },
  methods: {
    getStageDisable(name) {
      if (name == '赢单' || name == '输单') {
        return true
      }
      return false
    },
    setValue(val) {
      let item = this.optionsContract.filter((item) => item.id === val)
      this.optionsContractDetail = item[0].children
    },
    getDictApi() {
      queryVoListByCode({ code: 'ContractType' }).then((res) => {
        if (res.status == 0) {
          this.optionsContract = res.data
        }
      })
    },
    changeValue(val) {
      if (val == '') {
        this.form.opportunityTypeSub = ''
        this.optionsContractDetail = []
      } else {
        this.form.opportunityTypeSub = ''
        let item = this.optionsContract.filter((item) => item.id === val)
        this.optionsContractDetail = item[0].children
      }
    },
    getTypeList() {
      getDict('OpportunityStage')
        .then((result) => {
          this.options = result
        })
        .catch((err) => {})
    },
    loadMajor() {
      selectUintSpecialty({
        unitId: this.form.unitId,
      })
        .then((result) => {
          this.majorList = result.data
        })
        .catch((err) => {})
    },
    loadInfo() {
      var id = this.$route.query.id

      if (id) {
        this.form.id = id
        this.isLoading = true
        opportunityInfo(id)
          .then((result) => {
            var fileList = result.data.fileInfoList
            fileList.forEach((element) => {
              element.status = 'success'
              element.name = element.fileName
              element._url =
                element.fileType == 2 ? addSnop(element.url) : element.url
            })
            this.$nextTick(() => {
              this.$refs.upload.setFileList(fileList)
            })
            var ids =
              result.data.collaborator && result.data.collaborator.split(',')
            var names =
              result.data.collaboratorName &&
              result.data.collaboratorName.split(',')
            ids &&
              ids.forEach((item, index) => {
                this.xiezuolist.push({ id: item, name: names[index] })
              })
            this.form = result.data
            var useBookYear = this.form.useBookYear
            if (useBookYear) {
              var index = useBookYear.indexOf('年')
              this.useBookYear = useBookYear.substring(0, index + 1)
              this.radio = useBookYear.substring(index + 1, useBookYear.length)
            }
            this.form.bookSpecialty = this.form.bookSpecialty || ''
            if (this.form.unitId) {
              this.loadMajor()
            }
            this.bookList = this.form.distributionDetailList
            if (this.form.opportunityType) {
              this.setValue(this.form.opportunityType)
            }
            this.isLoading = false
          })
          .catch((err) => {
            this.isLoading = false
          })
      }
    },
    updateCustomer(data) {
      this.form.customerName = data.customerName
      this.form.customerId = data.id
      this.form.unitId = data.unitId
      this.form.unitName = data.unitName
      this.$refs.addform.validateField('customerId')
      this.loadMajor()
    },
    chooseCustomer() {
      if (this.isChooseCustomer) {
        return
      }
      this.customerDialogVisible = true
      this.$refs.customer.selectCustomerData({
        id: this.form.customerId,
        className: 'OpportunityController',
      })
    },
    updateVisible(val) {
      this.customerDialogVisible = val
    },
    // 选择协作人
    clickXuan(name, multipleNum) {
      this.dialogName = name
      this.multipleNum = multipleNum
      this.$refs.systemdialog.loadData()
      if (name == '选择负责人') {
        this.form.chargePerson
          ? this.$refs.systemdialog.updateWorksId([
              {
                id: this.form.chargePerson,
                name: this.form.chargePersonName,
                departmentId: this.form.opportunityDepartmentId,
              },
            ])
          : this.$refs.systemdialog.updateWorksId([])
      } else if (name == '选择协作人') {
        this.form.collaborator
          ? this.$refs.systemdialog.updateWorksId(this.xiezuolist)
          : this.$refs.systemdialog.updateWorksId([])
      }
      this.dialogVisible = true
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    handleTagClose(tag) {
      this.xiezuolist.splice(this.xiezuolist.indexOf(tag), 1)
      this.ziezuoData(this.xiezuolist)
    },
    submitData(data, type, departmentId) {
      if (type == '选择负责人') {
        this.form.chargePerson = data.length > 0 ? data[0].id : ''
        this.form.chargePersonName = data.length > 0 ? data[0].name : ''
        this.form.opportunityDepartmentId = departmentId
        this.$refs.addform.validateField('chargePerson')
      } else if (type == '选择协作人') {
        this.ziezuoData(data)
        this.xiezuolist = data
      }
      this.updateSystemVisible(false)
    },
    ziezuoData(list) {
      var ids = []
      var names = []
      list.forEach((item) => {
        ids.push(item.id)
        names.push(item.name)
      })
      this.form.collaborator = ids.join(',')
      this.form.collaboratorName = names.join(',')
    },
    submitForm() {
      this.$refs['addform'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.bookList.length > 0) {
          const allBooksHaveCount = this.bookList.every(
            (book) => book.number !== undefined && book.number > 0
          )
          if (!allBooksHaveCount) {
            this.$message.error('请为所有选中的教材输入用书册数')
            return
          }
          this.form.distributionDetailList = this.getBookList()
        }

        if (this.radio && this.useBookYear) {
          this.form.useBookYear = `${this.useBookYear}${this.radio}`
        }
        if (this.form.id) {
          // 编辑
          updateOpportunity(this.form)
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '更新成功！',
                })
                this.$router.back()
              } else {
                this.$message({
                  type: 'error',
                  message: '保存失败！',
                })
              }
            })
            .catch((err) => {
              this.$message({
                type: 'error',
                message: '保存失败：' + err.message,
              })
            })
        } else {
          // 验证部门
          this.$refs.verifyDeparment.verify()

        }
      })
    },
    submitWithDepartment(departmentId) {
      this.form.createDepartmentId = departmentId;
      // 添加
      addOpportunity(this.form)
      .then((result) => {
        if (result.data) {
          this.$message({
            type: 'success',
            message: '添加成功！',
          })
          this.$router.back()
        } else {
          this.$message({
            type: 'error',
            message: '保存失败！',
          })
        }
      })
      .catch((err) => {
        this.$message({
          type: 'error',
          message: '保存失败：' + err.message,
        })
      })
    },
    getBookList(){
      var list = []
      this.bookList.forEach(item => {
        list.push({
          "goodsId":item.goodsId,
          "name":item.name,
          "price":item.price,
          "number":item.number,
        })
      });
      return list
    },
    submitSuccess(list) {
      var fileList = Array()
      list.forEach((element) => {
        var fileData = {}
        var fileTypeName = getFileType(element.fileName)
        var type = this.fileType[fileTypeName]
        if (!type) {
          type = 4
        }
        fileData.fileName = element.fileName
        fileData.fileType = type
        fileData.url = element.url
        fileData.fileSize = element.fileSize
        fileList.push(fileData)
        console.log('item', fileData)
      })
      this.form.fileInfoList = fileList
    },
    chooseBook() {
      this.bookDialogVisible = true
      this.$nextTick(() => {
        this.$refs.book.loadBookData({
          opportunityId: this.form.id ? this.form.id : '',
          type: 2,
        })
        // 设置已选中的书籍
        this.$refs.book.setSelectedBooks(this.bookList)
      })
    },

    updateBookVisible(val) {
      this.bookDialogVisible = val
    },
    removeBook(index) {
      this.bookList.splice(index, 1)
    },
    updateData(data) {
      this.bookList = data.map((book) => ({
        ...book,
        goodsId:book.goodsId || book.id,
        number: book.number || undefined,
        isSelect: true,
      }))
    },
    lookBookInfo() {
      this.popoverVisibble = true
      queryBookInfo(this.form.materialId)
        .then((result) => {
          this.bookInfo = result.data
        })
        .catch((err) => {})
    },
  },
}
</script>
<style scoped>
.mytable{
  margin-top: 30px !important;
}
/deep/.el-input-number--without-controls .el-input-number__decrease,
.el-input-number--without-controls .el-input-number__increase {
  display: none;
}
/deep/.el-input-number {
  width: 100%;
}
.rbtn {
  cursor: pointer;
}
.ml10 {
  margin-left: 10px;
}
.w100 {
  width: 110px;
}
.width2 {
  width: calc(100% - 120px) !important;
}
/* .else50{
    width: 50%;
} */
.matdetail p {
  line-height: 30px;
}
.matdetail {
  position: relative;
}
.tr {
  position: absolute;
  top: -30px;
  right: 0;
}
.wid100 {
  width: calc(100% - 130px);
  margin-right: 10px;
}
.wid2 {
  width: calc(100% - 100px);
}
.wid {
  width: 100px;
}
.tagcss {
  margin-right: 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  background: #dfeafd;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}

.studiocss img {
  width: 16px;
  height: 16px;
  margin-right: 3px;
  margin-top: -3px;
}

.uploadtext {
  color: #4285f4;
  cursor: pointer;
}

.pltcss {
  color: #cdcdcd;
}

.unitbtn {
  color: #333333;
  position: relative;
}

.width100 {
  width: 100%;
}

.wid98 {
  width: 98px;
}

.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}

.btncenter {
  text-align: center;
}

.quanxiancss /deep/.el-form-item__label {
  margin-left: -7px;
  width: 125px !important;
}

.mainbg {
  margin: 16px 0px;
  padding: 20px 16px;
  background-color: white;
  border-radius: 8px;
}

.pd0 {
  padding-right: 0px !important;
}
.flex {
  display: flex;
}
.flex span {
  margin-left: 10px;
}
</style>
<style>
</style>
