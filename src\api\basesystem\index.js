import service from "@/utils/request.js";
export function repositoryList(data) {
  return service.request({
    method: "post",
    url: "/resource/selectAll",
    data
  });
}

//政策修改
export function updatePolicy(data) {
  return service.request({
    url: "/platform/business/afficheinfo/update",
    method: "POST",
    data
  });
}

// 获取政策详情
export function policyDetail(id) {
  return service.request({
    url: "/platform/business/afficheinfo/info/" + id,
    method: "get"
  });
}

//删除标准
export function deletePolicy(params) {
  return service.request({
    method: "post",
    url: `/platform/business/afficheinfo/delete`,
    params
  });
}

//标准政策添加
export function policySave(data) {
  return service.request({
    method: "post",
    url: "platform/business/afficheinfo/save",
    data
  });
}

export function getList(params) {
  return service.request({
    method: "get",
    url: `/platform/business/afficheinfo/list`,
    params
  });
}

export function getadvertisList(params) {
  return service.request({
    method: "get",
    url: `/common/business/advertisingspace/list`,
    params
  });
}

//广告位添加
export function addeBanner(data) {
  return service.request({
    url: "/common/business/advertisingspace/save",
    method: "POST",
    data
  });
}

//广告位更新
export function updateBanner(data) {
  return service.request({
    url: "/common/business/advertisingspace/update",
    method: "POST",
    data
  });
}

//广告位删除
export function deleteBanner(data) {
  return service.request({
    url: "/common/business/advertisingspace/delete",
    method: "post",
    data
  });
}

// 获取轮播图详情
export function bannerDetail(id) {
  return service.request({
    url: "/common/business/advertisingspace/info/" + id,
    method: "get"
  });
}
