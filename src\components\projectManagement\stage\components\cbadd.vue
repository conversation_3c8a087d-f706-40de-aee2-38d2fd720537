<template>
    <el-dialog 
    :title="title" 
    class="adddetailcss"
    width="45%" 
    :visible.sync="addVisible" 
    append-to-body 
    :before-close="handleClose"
    center>
        <el-form :model="formParams"  :ref="formConfig.ref" :inline="formConfig.inline" class="revisitcss myform"   :label-width="formConfig.labelWidth">
            <el-row type="flex" justify="center" :gutter="0">
                <el-col :span="16">
                    <el-form-item
          :label="item.label"
          v-for="(item, index) in formDataList"
          :key="index"
          :prop="item.prop"
          :rules="item.rules"
        >
          <el-input
            class="definput"
            type="number"
            v-if="item.type === 'input'"
            v-model="formParams[item.prop]"
            :placeholder="item.placeholder"
          ></el-input>
          <el-select
            v-if="item.type === 'select'"
            style="width: 100%"
            v-model="formParams[item.prop]"
            :placeholder="item.placeholder"
          >
            <el-option
              :label="opItem.label"
              :value="opItem.value"
              v-for="(opItem, index) in item.options"
              :key="index"
            ></el-option>
          </el-select>
          <el-date-picker
            v-if="item.type === 'date'"
            type="date"
            style="width: 100%"
            v-model="formParams[item.prop]"
            :placeholder="item.placeholder"
          ></el-date-picker>
          <el-radio-group
            v-if="item.type === 'radio'"
            v-model="formParams[item.prop]"
            :placeholder="item.placeholder"
          >
            <el-radio
              :label="opItem.label"
              v-for="(opItem, index) in item.options"
              :key="index"
              :rules="item.rules"
            ></el-radio>
          </el-radio-group>
          <el-input
            class="textarea-box"
            show-word-limit
            :maxlength="item.maxlength"
            v-if="item.type === 'textarea'"
            type="textarea"
            v-model="formParams[item.prop]"
            :placeholder="item.placeholder"
          ></el-input>
          <el-switch
            v-if="item.type === 'switch'"
            v-model="formParams[item.prop]"
          ></el-switch>
        </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="center">
            <!-- <el-button class="submitbtn defaultbtn" type="primary" @click="submitAction">提交</el-button> -->
            <span slot="footer" class="dialog-footer">
            <!-- <el-button @click="btnCancel">取 消</el-button> -->
            <el-button type="primary" @click="btnOk">确 定</el-button>
        </span>
        </div>
     
    </el-dialog>
</template>

<script>
export default {
    props:{
        visible:{
            type:Boolean,
            default:false
        },
        title:{
            type:String,
            default:''
        },
        formConfig: {
            type: Object,
            default: () => {}
        },
        formParams: {
            type: Object,
            default:()=>{}            
        },
        formDataList: {
            type: Array,
            default:()=>[]
        },
    },
    data(){
        return{
            form:{
                
            }
        }
    },
    computed:{
        addVisible:{
            get(){
                return this.visible
            },
            set(val){
                console.log('===')
                this.$emit('updateVisible',val)
            }
        }
    },
    methods:{
      handleClose(done) {
        this.$refs[this.formConfig.ref].resetFields();
        done();
      },
    btnCancel() {
            this.$refs[this.formConfig.ref].resetFields();
            this.$emit("btnCancel", false);
    },
    btnOk() {
      this.$refs[this.formConfig.ref].validate((valid) => {
        if (valid) {
          this.$emit("btnOk", this.formParams);
        } else {
          return false;
        }
      });
    },
    }
}
</script>

<style scoped lang="scss">
/deep/ .textarea-box .el-input__count {
  position: absolute;
  top: 0 !important;
  right: -37px !important;
}
.w100{
    width: 100% !important;
}
.center{
    text-align: center;
}
.submitbtn{
    margin-top: 30px;
}
.revisitcss /deep/.el-form-item{
    margin-bottom: 18px;
}

</style>