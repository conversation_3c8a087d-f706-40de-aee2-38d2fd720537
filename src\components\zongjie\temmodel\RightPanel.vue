<template>
  <div class="right-board">
    <p class="blueline">组件控制</p>
    <div class="field-box">
      <!-- 组件属性 -->
      <el-form
        label-position="left"
        v-show="currentTab === 'field' && showField"
        size="small"
        label-width="100px"
      >
        <el-form-item
          v-if="activeData.__config__.label !== undefined"
          label="组件标题"
        >
          <el-input
            v-model="activeData.__config__.label"
            placeholder="请输入组件标题"
            @input="changeRenderKey"
          />
        </el-form-item>
        <el-form-item
          v-if="activeData.__config__.itemLimitNumber !== undefined && activeData.__config__.itemType==3"
          label="附件个数限制"
        >
          <el-input
            v-model="activeData.__config__.itemLimitNumber"
            placeholder="请输入附件个上限数量"
            @input="changeRenderKey"
          />
        </el-form-item>
        <el-form-item
        v-if="activeData.__config__.itemLimitNumber !== undefined && activeData.__config__.itemType!=3"
        label="文字个数限制"
      >
        <el-input
          v-model="activeData.__config__.itemLimitNumber"
          placeholder="请输入文字个数上限数量"
          @input="changeRenderKey"
        />
      </el-form-item>
        <el-form-item
          v-if="activeData.__config__.itemDescribe !== undefined"
          label="组件标注"
        >
          <el-input
            v-model="activeData.__config__.itemDescribe"
            placeholder="请输入组件标注"
            @input="changeRenderKey"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          v-if="activeData.__config__.required !== undefined"
          label="是否必填"
        >
          <el-switch
            active-color="#13ce66"
            inactive-color="#dcdfe6"
            v-model="activeData.__config__.required"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { saveFormConf } from '@/utils/db'
// 使changeRenderKey在目标组件改变时可用
const needRerenderList = ['tinymce']

export default {
  components: {},
  props: ['showField', 'activeData', 'formConf'],
  data() {
    return {
      currentTab: 'field',
    }
  },
  computed: {},
  watch: {
    formConf: {
      handler(val) {
        saveFormConf(val)
      },
      deep: true,
    },
  },
  methods: {
    changeRenderKey() {
      if (needRerenderList.includes(this.activeData.__config__.tag)) {
        this.activeData.__config__.renderKey = +new Date()
      }
    },
  },
}
</script>

<style  scoped>
.blueline {
  border-left: 4px solid #2490ff;
  padding-left: 20px;
  margin-top: 0;
  margin-bottom: 20px;
}
.right-board {
  width: 370px;
  padding-top: 20px;
  background: #fff;
  height: calc(100vh - 160px);
}
.right-board .field-box {
  position: relative;
  height: calc(100vh - 201px);
  box-sizing: border-box;
  overflow: hidden;
  width: 310px;
  margin: 0 auto;
}
.select-item {
  display: flex;
  border: 1px dashed #fff;
  box-sizing: border-box;
}
.select-item .close-btn {
  cursor: pointer;
  color: #f56c6c;
}
.select-item .el-input + .el-input {
  margin-left: 4px;
}
.select-item + .select-item {
  margin-top: 4px;
}
.select-item.sortable-chosen {
  border: 1px dashed #409eff;
}
.select-line-icon {
  line-height: 32px;
  font-size: 22px;
  padding: 0 4px;
  color: #777;
}
.option-drag {
  cursor: move;
}
.time-range.el-date-editor {
  width: 227px;
}
.time-range >>> .el-icon-time {
  display: none;
}
.node-label {
  font-size: 14px;
}
.node-icon {
  color: #bebfc3;
}
</style>
