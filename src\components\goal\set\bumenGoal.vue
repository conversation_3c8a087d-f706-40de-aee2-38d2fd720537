<template>
  <div>
    <div class="listcss">
      <dataitem class="itemcss" :item="item" :span="4" v-for="(item,index) in list" :key="index"></dataitem>
    </div>
    <el-table
      class="mytable mt30"
      :data="tableDataAll"
      :span-method="objectSpanMethod"
      border
      
      style="width: 100%;">
      <el-table-column
        prop="departmentName"
        label="部门"
        width="180"
        align="center">
      </el-table-column>
      <el-table-column
        width="160"
        prop="name"
        label="目标"
        align="center">
      </el-table-column>
      <el-table-column
        v-for="(item,index) in timeList"
        :key="`${index}+${item.label}`"
        :prop="item.value"
        :label="item.label"
        align="center">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dataitem from '../show/components/dataitem.vue';
export default {
  components:{
    dataitem
  },
  props:{
    bumenObj:{
      type:Object,
      default:()=>({})
    }
  },
  data(){
    return{
      list:[
        {id:1,goalType:1,name:"新增客户",goal:0,finishGoal:0,icon:require('../../../assets/img5.png'),unit:"个",color:"#F46D40"},
        {id:2,goalType:2,name:"新增拜访与跟进",goal:0,finishGoal:0,icon:require('../../../assets/img2.png'),unit:"次",color:"#4A8BF6"},
        {id:3,goalType:3,name:"新增机会",goal:0,finishGoal:0,icon:require('../../../assets/xiaoshou.png'),unit:"次",color:"#56C36E"},
        {id:4,goalType:4,name:"新增合同",goal:0,finishGoal:0,icon:require('../../../assets/img/hetong_icon.png'),unit:"个",color:"#4A8BF6"},
        {id:5,goalType:5,name:"合同金额",goal:0,finishGoal:0,icon:require('../../../assets/img3.png'),unit:"万元",color:"#E85D5D"},
        {id:6,goalType:6,name:"回款金额",goal:0,finishGoal:0,icon:require('../../../assets/img4.png'),unit:"万元",color:"#FF8D1A"}
      ],
      timeList:[],
        tableDataAll:[],
        nameToObj:{
          0:'新增客户',
          1:'新增拜访与跟进',
          2:'新增机会',
          3:'新增合同',
          4:'合同金额(万元)',
          5:'回款金额(万元)',
        },
        mounthTo:{
          0:'january',
          1:'february',
          2:'march',
          3:'april',
          4:'may',
          5:'june',
          6:'july',
          7:'august',
          8:'september',
          9:'october',
          10:'november',
          11:'december'
        }
    }
  },
  methods:{
    handleData(){
        let goalSummaryVoList = this.bumenObj.goalSummaryVoList
        let obj = {}
        if(goalSummaryVoList && goalSummaryVoList.length>0){
          goalSummaryVoList.forEach(item=>{
            obj[item.goalType] = item
          })
          this.list.forEach((item)=>{
            item.goal = obj[item.goalType].goal
            item.finishGoal = obj[item.goalType].finishGoal
          })
        }else{
          this.list.forEach((item)=>{
            item.goal = 0
            item.finishGoal = 0
          })
        }

     


        let personalGoalsDetailList = this.bumenObj.personalGoalsDetailList
        if(personalGoalsDetailList && personalGoalsDetailList.length>0){
          this.tableDataAll = []
          let onOff = false
          personalGoalsDetailList.forEach((item,index)=>{
              item.departmentName = this.bumenObj.departmentName
              item.name = this.nameToObj[index]
              item[item.year+'-01'] = item.january
              if(!onOff){
                this.timeList = []
                for(let i = 0;i<12;i++){
                  if(i>8){
                    this.timeList.push({
                      label:item.year+`-${i+1}`,
                      value:this.mounthTo[i]
                    })
                  }else{
                    this.timeList.push({
                      label:item.year+`-0${i+1}`,
                      value:this.mounthTo[i]
                    })
                  }
                }
                onOff = true
              }
              this.tableDataAll.push(item)
          })
        }else{
          this.tableDataAll = []
        }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let lastId = ''
        if (columnIndex === 0) {
          if (rowIndex % this.list.length == 0) {
            lastId = row.id;
            return {
              rowspan: 6,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      }
    }
}
</script>
<style scoped>
.mt30{
  margin-top: 20px !important;
}
.itemcss{
  width: 16%;
  min-width: 200px !important;
  margin-right: 12px;
}
.itemcss:last-child{
  margin-right: 0px !important;
}
.listcss{
  display: flex;
  align-items: center;
  width: 100%;
  overflow-x: scroll;
  padding-bottom: 10px;
  padding: 0 10px;
}
</style>
<style>

</style>