<template>
  <div class="resource app-container">
    <el-table
      class="tableResouze"
      :data="dataList"
      border
      default-expand-all
      stripe
      v-loading="dataListLoading"
      style="width: 100%"
      :row-key="getRowKeys"
      :tree-props="{children: 'children'}"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
    >
      <el-table-column 
        prop="name" 
        label="部门名称" 
        fixed="left" 
        width="300">
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间"  align="center">
      </el-table-column>

      <el-table-column prop="modifyTime" label="更新时间"  align="center">
      </el-table-column>

      <el-table-column prop="sortNo" label="排序"  align="center">
      </el-table-column>

      <el-table-column label="操作" width="180" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-plus"
            @click="handleShowAddChildDialog(scope.row)"
            >添加子部门</el-button
          >
          <el-button
            type="text"
            size="mini"
            @click="showDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>


    <AddChildDepartmentDialog
      v-model="addChildDepartmentVisible"
      :department="currentDepartment"
      @submit="addChildDepartment"
    />

    <DepartmentDetailDrawer
      v-model="detailDrawerVisible"
      :department-id="departmentId"
      @edit="editDepartment"
      @migrate="handleMigrate"
    />
  </div>
</template>

<script>
import AddChildDepartmentDialog from './components/AddChildDepartmentDialog.vue';
import DepartmentDetailDrawer from './components/DepartmentDetailDrawer.vue';
import { treeDepartmentList, saveDepartment } from '@/api/framework/user';

export default {
  data() {
    return {
      dataList: [],
      dataListLoading: false,
      addChildDepartmentVisible: false,
      detailDrawerVisible: false,
      currentParentId: '',
      currentDepartment: {},
      departmentId: ''
    };
  },
  components: {
    AddChildDepartmentDialog,
    DepartmentDetailDrawer
  },
  created() {
    this.loadData();
  },
  methods: {
    getRowKeys(row) {
      return row.id;
    },
    loadData() {
      this.dataListLoading = true;
      treeDepartmentList()
        .then(res => {
          if (res.status == 0) {
            this.dataList = res.data;
            this.dataListLoading = false;
          } else {
            this.$message.error(res.msg);
            this.dataListLoading = false;
          }
        })
    },
    
    handleShowAddChildDialog(row) { 
      this.currentParentId = row.uniqueCode || row.id;
      this.currentDepartment = row;
      this.addChildDepartmentVisible = true;
    },
    
    showDetail(row) {
      this.currentDepartment = row;
      this.departmentId = row.id;
      this.detailDrawerVisible = true;
    },
    
    editDepartment(departmentInfo) {
      this.loadData();
    },
    
    addChildDepartment(formData) {
      console.log('添加子部门数据:', formData);
      saveDepartment(formData).then(res => {
        if (res.status == 0) {
          this.$message.success('添加子部门成功');
          this.loadData();
          this.addChildDepartmentVisible = false;
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    
    handleMigrate(migrateInfo) {
      console.log('部门迁移成功:', migrateInfo);
      this.loadData();
    }
  }
};
</script>
<style scoped lang="scss">
.top-btn{
  display: flex;
  justify-content: flex-end;
}
</style>
