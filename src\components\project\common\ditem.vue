<template>
  <div class="maskVcss">
    <div class="valuecss" :style="{color:valueColor,marginTop:ptop+'px'}">
        {{value}}<span class="unitcss">{{unit}}</span>
    </div>
    <div class="labelcss">
        {{label}}
    </div>
  </div>
</template>

<script>
export default {
    name:"ditem",
    props:{
        value:{
            type:String,
            default:''
        },
        ptop:{
            type:Number,
            default:16
        },
        valueColor:{
            type:String,
            default:'#333333'
        },
        label:{
            type:String,
            default:'label'
        },
        unit:{
            type:String,
            default:''
        }
    },
    data(){
        return{

        }
    },
}
</script>

<style scoped>
.unitcss{
    color: #999999;
    font-size: 16px;
    font-family: Roboto, Roboto;
    font-weight: bold;
}

.maskVcss{
    text-align: center;
}
.valuecss{
    height: 38px;
    font-family: <PERSON><PERSON>, <PERSON>o;
    font-weight: bold;
    font-size: 32px;
    color: #4285F4;
    line-height: 38px;
}
.labelcss{
    height:34px;
    line-height: 34px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
}
</style>