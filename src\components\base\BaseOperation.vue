<!-- 翻出插件 -->
<template>
  <div class="BaseBackBook" :style="getHeight" v-if="isBookShow">
    <div class="book-wrap">
      <div class="book-box" :style="setStyle">
        <div class="book-po">
          <div class="book-file" :style="orderStyle">
            <div v-for="(item, index) in chooseImg" :key="index">
              <div class="img-left img-center" :style="centerStyle">
                <img :src="item.img1" alt="" />
              </div>
              <div class="img-right img-center" :style="centerStyle">
                <img :src="item.img2" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="book-tools book-top">
      <span class="book-stop" @click="isBookShow = false">X</span>
    </div>
    <div class="book-tools book-bottom"></div>
    <div class="left-bar book-bar" @click="handleLeft()"></div>
    <div class="right-bar book-bar" @click="handleRight()"></div>
  </div>
</template>

<script>
export default {
  props: {
    isBookMask: {
      type: Boolean,
    },
  },
  data() {
    return {
      isBookShow: false,
      bodyHeight: document.body.clientHeight,
      bodyWidth: document.body.clientWidth,
      centerBodyWidth: document.body.clientWidth - 64 - 64 - 100,
      centerBodyHight: document.body.clientHeight - 92 - 20,
      num: 0,
      // allImg: [
      //   {
      //     img1: require('@/assets/operation/1.jpg'),
      //     img2: require('@/assets/operation/2.jpg')
      //   },
      //   {
      //     img1: require('@/assets/operation/3.jpg'),
      //     img2: require('@/assets/operation/4.jpg')
      //   }
      // ],
      chooseImg: [],
    }
  },
  watch: {
    num(val) {
      console.log(val)
    },
    isBookMask(val) {
      this.isBookShow = val
    },
    isBookShow(val) {
      this.$emit('update:isBookMask', val)
    },
  },
  computed: {
    setStyle() {
      let height = this.bodyHeight
      let width = this.bodyWidth
      return {
        width: `${width}px`,
        height: `${height}px`,
        transform: 'translateY(0px)',
        transformOrigin: '50% 50% 0px',
      }
    },
    getHeight() {
      let height = this.bodyHeight
      return {
        height: `${height}px`,
      }
    },
    orderStyle() {
      let width = this.centerBodyWidth
      let height = this.centerBodyHight
      let left = width / 2
      let top = height / 2
      return {
        width: `${width}px`,
        height: `${height}px`,
        left: `-${left}px`,
        top: `-${top}px`,
      }
    },
    centerStyle() {
      return {
        width: `${this.centerBodyWidth / 2}px`,
        height: `${this.centerBodyHight}px`,
      }
    },
  },
  methods: {
    handle() {
      let width = document.body.clientWidth - 64 - 64 - 100 + 'px'
      let height = document.body.clientHeight - 92 - 20 + 'px'
      let left = (document.body.clientWidth - 64 - 64 - 100) / 2 + 'px'
      let top = (document.body.clientHeight - 92 - 20) / 2 + 'px'
      console.log(width, height, left, top)
      return {
        width: `${width}px`,
        height: `${height}px`,
        left: `-${left}px`,
        top: `-${top}px`,
      }
    },
    imgAry(val) {
      let ary = [
        {
          img1: '',
          img2: '',
        },
      ]
      if (!this.allImg[val].img1) {
        return false
      }
      if (val === 0) {
        ary[0].img1 = this.allImg[val].img1
        ary[0].img2 = this.allImg[val].img2
      } else {
        ary[0].img1 = this.allImg[val].img1
        ary[0].img2 = this.allImg[val].img2
      }
      this.chooseImg = ary
    },
    // 向右
    handleRight() {
      this.num++
      this.imgAry(this.num)
    },
    // 向左
    handleLeft() {
      this.num--
      this.imgAry(this.num)
    },
  },
  mounted() {
    this.imgAry(0)
    window.onresize = () => {
      // this.handle()
    }
  },
  created() {
    this.isBookShow = this.isBookMask
  },
}
</script>

<style lang="scss" scoped>
.BaseBackBook {
  // background: url('../../assets/404_images/innerMainbgImgUrl.jpg') no-repeat;
  background-size: 100% 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  .book-wrap {
    overflow: hidden;
    position: absolute;
    width: 100%;
    height: 100%;
  }
  .book-box {
    position: absolute;
    .book-po {
      position: absolute;
      top: 50%;
      left: 50%;
      margin: auto;
    }
  }
  .book-file {
    // width: 678px;
    // height: 467px;
    // left: -338.834px;
    // top: -233.5px;
    position: relative;
    margin-left: 0;
    transition: margin-left 500ms;
  }
  .img-center {
    position: absolute;
    overflow: hidden;
    top: 0;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .img-left {
    left: 0;
    right: auto;
    // background: url('../../assets/operation/3.jpg') 50% 50% / cover no-repeat;
  }
  .img-right {
    right: 0;
    left: auto;
    // background: url('../../assets/operation/4.jpg') 50% 50% / cover no-repeat;
  }
  .book-top {
    top: 0;
    .book-stop {
      width: 50px;
      text-align: center;
      height: 100%;
      float: right;
      margin-right: 10%;
      color: #fff;
      line-height: 46px;
      font-size: 20px;
      cursor: pointer;
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-bottom: 2px solid #ecf5fb;
      }
    }
  }
  .book-bottom {
    bottom: 0;
    text-align: center;
  }
  .book-tools {
    position: absolute;
    height: 46px;
    width: 100%;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.6);
  }
  .book-bar {
    width: 64px;
    height: 160px;
    position: absolute;
    top: 50%;
    margin-top: -80px;
    cursor: pointer;
    z-index: 888;
    &:hover {
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
  .left-bar {
    left: -1px;
    // background: url('../../assets/404_images/previous_normal.png') no-repeat
    //   center center;
  }
  .right-bar {
    right: 0;
    // background: url('../../assets/404_images/next_normal.png');
  }
}
</style>
