// dialog.js - 核心封装文件

import Vue from 'vue';
import DialogComponent from './DialogComponent.vue';

// 创建 Dialog 构造函数
const DialogConstructor = Vue.extend(DialogComponent);

// 存储当前实例
let currentInstance = null;

// 关闭当前对话框
function closeDialog() {
  if (currentInstance) {
    currentInstance.visible = false;
  }
}

// 创建并显示对话框
function createDialog(options) {
  // 关闭已存在的对话框
  closeDialog();

  // 合并默认选项
  const defaultOptions = {
    title: '提示',
    content: '',
    width: '500px',
    showConfirmButton: true,
    showCancelButton: false,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'default', // 'default', 'success', 'warning', 'error', 'info'
    beforeClose: null,
    onConfirm: null,
    onCancel: null
  };

  const mergedOptions = { ...defaultOptions, ...options };

  // 创建实例
  currentInstance = new DialogConstructor({
    data() {
      return {
        visible: false,
        options: mergedOptions
      };
    }
  });

  // 监听关闭事件
  currentInstance.$on('close', () => {
    currentInstance.visible = false;
  });

  // 监听确认事件
  currentInstance.$on('confirm', () => {
    if (typeof mergedOptions.onConfirm === 'function') {
      mergedOptions.onConfirm();
    }
    closeDialog();
  });

  // 监听取消事件
  currentInstance.$on('cancel', () => {
    if (typeof mergedOptions.onCancel === 'function') {
      mergedOptions.onCancel();
    }
    closeDialog();
  });

  // 挂载实例
  const vm = currentInstance.$mount();
  document.body.appendChild(vm.$el);

  // 显示对话框
  currentInstance.visible = true;

  // 返回实例，方便手动控制
  return currentInstance;
}

// 提供 Promise 风格的调用方式
function confirm(options) {
  return new Promise((resolve, reject) => {
    createDialog({
      ...options,
      showConfirmButton: true,
      showCancelButton: true,
      onConfirm: () => resolve(true),
      onCancel: () => resolve(false)
    });
  });
}

// 导出 API
export default {
  open: createDialog,
  close: closeDialog,
  confirm,
  alert(options) {
    return createDialog({
      ...options,
      showConfirmButton: true,
      showCancelButton: false
    });
  }
};
