<template>
  <div class="mainbg fixpb">
    <el-form
      class="myform clearfix"
      inline
      @keyup.enter.native="projectListData()"
    >
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.projectName"
          class="definput"
          placeholder="项目名称"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.businessOwnerName"
          class="definput"
          placeholder="业务负责人"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.chargePersonName"
          class="definput"
          placeholder="项目负责人"
        >
        </el-input>
      </el-form-item>

      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.createByName"
          class="definput w150"
          placeholder="创建人"
        >
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.createByDepartment"
          class="definput w150"
          placeholder="创建人部门"
        >
        </el-input>
      </el-form-item> -->
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.customerName"
          class="definput w150"
          placeholder="客户名称"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.unitName"
          class="definput w150"
          placeholder="客户单位"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select
          clearable
          class="definput w110"
          popper-class="removescrollbar"
          v-model="pageBean.projectStatus"
          placeholder="请选择"
        >
          <el-option
            v-for="item in optionsStatus"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型：">
        <el-select
          @change="changeValue"
          clearable
          class="definput w110"
          popper-class="removescrollbar"
          v-model="pageBean.projectType"
          placeholder="请选择"
        >
          <el-option
            v-for="item in types"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
        -
        <el-select
          clearable
          class="definput w150 ml ellipsis"
          popper-class="removescrollbar"
          v-model="pageBean.projectTypeSub"
          placeholder="请选择"
        >
          <el-option
            v-for="item in subTypes"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="项目金额:">
        <el-select
          class="definput w150"
          popper-class="removescrollbar"
          clearable
          v-model="pageBean.amountType"
          placeholder="请选择"
        >
          <el-option
            v-for="item in projectArr"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间：">
        <datepicker @submitAction="submitAction"></datepicker>
      </el-form-item>
      <el-form-item label="" class="mr20">
        <el-checkbox v-model="pageBean.involved">我参与的</el-checkbox>
      </el-form-item>
      <el-form-item label="" class="mr20">
        <el-checkbox v-model="pageBean.overdue">只看逾期</el-checkbox>
      </el-form-item>

      <el-form-item label="">
        <el-button
          @click="search"
          class="defaultbtn mt"
          icon="el-icon-search"
          type="primary"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item label="" class="fr">
        <el-button
          @click="showAdd"
          class="defaultbtn mt"
          icon="el-icon-plus"
          type="primary"
        >
          新建项目</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="customertable mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column label="项目名称" width="257">
        <template slot-scope="scope">
          <span class="cusnamecss" @click="toDetail(scope.row)">
            {{ scope.row.projectName }}
            <span class="overduecss" v-if="scope.row.overdue">已逾期</span>
          </span>
        </template>
      </el-table-column>

      <el-table-column
        prop="projectTypeSubName"
        label="类型"
        width="180px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.projectTypeName + '-' + scope.row.projectTypeSubName
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="customerName"
        label="客户名称"
        width="120px"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="unitName"
        label="客户单位"
        min-width="180px"
        show-overflow-tooltip
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column prop="projectStatusName" label="项目状态" width="120px">
        <template slot-scope="scope">
          <span :class="statusCss(scope.row)">
            {{ scope.row.projectStatusName }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="progress"
        label="进度"
        width="100px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="beginTime"
        label="开始时间"
        width="120px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="endTime"
        label="项目截止时间"
        width="120px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="remainingTime"
        label="剩余时间"
        width="120px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="businessOwnerName"
        label="业务负责人"
        width="120px"
      >
      </el-table-column>
      <el-table-column prop="chargePersonName" label="项目负责人" width="120px">
      </el-table-column>
      <el-table-column
        prop="bidwinnerName"
        label="中标公司"
        width="180px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="projectAmount"
        label="项目金额(万元)"
        width="120px"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.projectAmount ? scope.row.projectAmount : '暂无预算'
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isCommission" label="是否提成" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.isCommission ? '已提成' : '未提成' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createByName"
        label="创建人"
        width="100px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="createByDepartment"
        label="创建人部门"
        width="180px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="180px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column prop="edit" width="280px" fixed="right" label="更多操作">
        <template slot-scope="scope">
            <el-button
              v-if="
                scope.row.projectStatus != 2
              "
              :disabled="!scope.row.isOperate"
              class="bbtn mr10"
              type="text"
              @click="handleCommand(2, scope.row)"
            >
              启动
            </el-button>
            <el-dropdown
              v-else
              placement="bottom-end"
              @command="(e) => handleCommand(e, scope.row)"
              trigger="click"
            >
              <el-button
                class="bbtn mr10"
                type="text"
                :disabled="!scope.row.isOperate"
              >
                进度管理 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="3">完成</el-dropdown-item>
                <el-dropdown-item :command="4">暂停</el-dropdown-item>
                <el-dropdown-item :command="5">关闭</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          <el-button
            class="bbtn"
            type="text"
            @click="toQuest(scope.row)"
            v-isShow="'crm:controller:task:list'"
          >
            任务管理</el-button
          >
          <el-dropdown
            placement="bottom-end"
            @command="(e) => handleMoreCommand(e, scope.row)"
            trigger="click"
          >
            <el-button class="bbtn ml10" type="text">
              更多 <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="1"
                v-isShow="'crm:controller:taskworkhours:list'"
              >
                工时管理
              </el-dropdown-item>
              <el-dropdown-item
                command="2"
                v-isShow="'crm:controller:taskcost:list'"
              >
                成本管理
              </el-dropdown-item>
              <el-dropdown-item
                command="3"
                :disabled="getDisabled(scope.row)"
                v-isShow="'crm:controller:project:update'"
              >
                编辑
              </el-dropdown-item>
              <el-dropdown-item
                command="4"
                :disabled="!scope.row.isOperate"
                v-isShow="'crm:controller:project:delete'"
              >
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <dc-dialog
      iType="1"
      title="温馨提示"
      width="500px"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <p class="pcc">确定要删除项目么?</p>
    </dc-dialog>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import { projectList, projectDelete, projectUpdate } from '@/api/project'
import { getDict, getParStr } from '@/utils/tools'
import datepicker from '@/components/common/datepicker'
import { queryVoListByCode } from '@/api/wapi.js'
// import { checkPermission } from "@/utils/permission";
export default {
  components: {
    page,
    // drawer,
    nolist,
    datepicker,
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      props: {
        value: 'id',
        label: 'name',
      },
      dialogVisible: false,
      types: [],
      subTypes: [],
      projectTypeSub: [],
      optionsStatus: [
        {
          value: '',
          name: '全部',
        },
        {
          value: '1',
          name: '未开始',
        },
        {
          value: '2',
          name: '进行中',
        },
        {
          value: '3',
          name: '已完成',
        },
        {
          value: '4',
          name: '暂停',
        },
        {
          value: '5',
          name: '停止',
        },
      ],
      projectArr: [
        {
          id: '1',
          name: '0-20万',
        },
        {
          id: '2',
          name: '20-50万',
        },
        {
          id: '3',
          name: '50-100万',
        },
        {
          id: '4',
          name: '100万以上',
        },
      ],
      datarange: [],
      isLoading: false,
      tableData: [],
      total: 100,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        projectStatus: '',
        projectType: '',
        projectTypeSub: '',
        createStartTime: '',
        createEndTime: '',
        createByName: '',
        customerName: '',
        unitName: '',
        createByDepartment: '',
        amountType: '',
        involved: false,
        overdue: false,
        chargePersonName: '',
        projectName: '',
        businessOwnerName: '',
      },
      selectValue: '',
      userId: '',
      statusToCss: {
        1: 'c1',
        2: 'c2',
        3: 'c3',
        4: 'c4',
        5: 'c5',
      },
      dId: '',
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
      this.pageBean.involved = Boolean(this.pageBean.involved)
      this.pageBean.overdue = Boolean(this.pageBean.overdue)
    }
    this.projectListData()
    this.getTypesData()
    this.userId = sessionStorage.getItem('userid') || ''
  },
  computed: {
    computedCss() {
      return function (row) {
        let topWidth = row.top ? 35 : 10
        let tagWidth =
          row.chargePerson == this.userId ||
          row.collaborator.split(',').includes(this.userId)
            ? 42
            : 10
        let width = 227 - topWidth - tagWidth
        return {
          width: width + 'px',
        }
      }
    },
    statusCss() {
      return (row) => {
        return [this.statusToCss[row.projectStatus]]
      }
    },
  },
  methods: {
    valueFormatter(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue
      }
      return '—'
    },
    getDisabled(data) {
      if (data.isOperate) {
        if (data.projectStatus == 3 || data.projectStatus == 5) {
          return true
        } else {
          return false
        }
      } else {
        return true
      }
    },
    getTypesData() {
      queryVoListByCode({ code: 'ContractType' }).then((res) => {
        if (res.status == 0 && res.data.length > 0) {
          this.types = res.data
          if (this.pageBean.projectType) {
            let item = this.types.filter(
              (item) => item.id === this.pageBean.projectType
            )
            this.subTypes = item[0].children
          }
        }
      })
    },
    changeType(data) {
      console.log('ddsdddddd', data)
      this.pageBean.projectTypeSub = data[data.length - 1]
    },
    changeDateRange(date) {
      if (date) {
        this.pageBean.createStartTime = date[0]
        this.pageBean.createEndTime = date[1]
      } else {
        this.pageBean.createStartTime = ''
        this.pageBean.createEndTime = ''
      }
    },
    search() {
      this.pageBean.pageNum = 1
      this.projectListData()
    },
    showAdd() {
      this.$router.push({
        path: '/project/manager/add',
      })
    },
    projectListData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      projectList(this.pageBean)
        .then((res) => {
          this.isLoading = false
          if (res.status == 0) {
            this.tableData = res.data
            this.total = res.page.total
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    changeDrawer(v) {
      this.drawer = v
    },
    changeValue(val) {
      if (val == '') {
        this.pageBean.projectTypeSub = ''
        this.subTypes = []
      } else {
        this.pageBean.projectTypeSub = ''
        let item = this.types.filter((item) => item.id === val)
        this.subTypes = item[0].children
      }
    },
    submitDialog() {
      this.dialogVisible = false
      projectDelete({ id: this.dId })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.projectListData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    deleteAction(data) {
      this.dId = data.id
      this.dialogVisible = true
    },
    toDetail(data) {
      if (this.checkPermission('crm:controller:project:info')) {
        this.$router.push({
          path: '/project/manager/detail',
          query: {
            id: data.id,
            name: data.projectName,
          },
        })
      } else {
        this.msgError('没有查看权限')
        return
      }
    },
    toQuest(data) {
      this.$router.push({
        path: '/project/manager/quest/index',
        query: {
          projectId: data.id,
          name: data.projectName,
          sta: data.projectStatus,
        },
      })
    },
    onTime(data) {
      this.$router.push({
        path: '/project/manager/timeManage',
        query: {
          id: data.id,
        },
      })
    },
    onCost(data) {
      this.$router.push({
        path: '/project/manager/costManage',
        query: {
          id: data.id,
        },
      })
    },
    onEdit(data) {
      this.$router.push({
        path: '/project/manager/add',
        query: {
          id: data.id,
        },
      })
    },
    handleCommand(index, data) {
      var par = {
        id: data.id,
        projectStatus: index,
      }
      projectUpdate(par)
        .then((result) => {
          if (result.data) {
            this.projectListData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
      console.log('index', index, par)
    },
    handleMoreCommand(command, data) {
      switch (command) {
        case '1':
          this.onTime(data)
          break
        case '2':
          this.onCost(data)
          break
        case '3':
          this.onEdit(data)
          break
        case '4':
          this.deleteAction(data)
          break
        default:
          break
      }
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.projectListData()
    },
    submitAction(type, dateRange) {
      if (type == 11) {
        if (dateRange) {
          this.pageBean.createStartTime = dateRange[0]
          this.pageBean.createEndTime = dateRange[1]
        } else {
          this.pageBean.createStartTime = ''
          this.pageBean.createEndTime = ''
        }
        this.pageBean.time = ''
      } else {
        this.pageBean.time = type
        this.pageBean.createStartTime = ''
        this.pageBean.createEndTime = ''
      }
    },
  },
}
</script>
<style scoped>
.el-icon--right {
  margin-left: 0px;
}
.overduecss {
  min-width: 50px !important;
  height: 24px;
  line-height: 24px;
  background: #fff5f6;
  border-radius: 12px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #f45961;
  text-align: center;
  margin-left: 10px;
}
.w150 {
  width: 150px;
}
.w110 {
  width: 110px;
}
.wid {
  width: calc(100% - 130px);
}
.mr20 {
  margin-right: 20px;
}
.w300 {
  width: 280px;
}
</style>
<style scoped lang="scss">
.mainbg {
  background-color: white;
  padding: 20px;
  min-height: 100%;
}
.fr {
  float: right;
}
.smt {
  margin-top: 19px;
}

.protext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #409eff;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  /* background-color: red; */
  display: inline-block;
  margin-top: 16px;
  vertical-align: top;
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
  color: #4285f4;
  line-height: 24px;
  cursor: pointer;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 5px;
  text-align: center;
}

.c1 {
  color: #4285f4;
}
.c2 {
  color: #ff8d1a;
}
.c3 {
  color: #56c36e;
}
.c4 {
  color: #f56c6c;
}
.c5 {
  color: #f56c6c;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.customertable /deep/.cell {
}

.customertable .el-button {
  padding: 2px;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}
.ml10{
  margin-left: 10px;
}
</style>
