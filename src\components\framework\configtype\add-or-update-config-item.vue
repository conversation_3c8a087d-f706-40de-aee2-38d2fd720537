<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="370">
      <div style="overflow-y: auto;">
        <el-form :model="dataForm" :rules="Rule.CONFIG_ITEM" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="160px">
          <el-form-item label="所属类型" prop="configtypeName" >
            <el-input v-model="dataForm.configtypeName" placeholder="所属类型" disabled></el-input>
          </el-form-item>
          <el-form-item label="配置项名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="配置项名称"></el-input>
          </el-form-item>
          <el-form-item label="配置项编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="配置项编码"></el-input>
          </el-form-item>
          <el-form-item label="控件类型" prop="editorClass" v-if="!dataForm.id">
            <el-select v-model="dataForm.editorClass" placeholder="选择控件类型">
              <el-option
              v-for="item in Dict.CONFIG_NEW"
              :key="item.value"
              :label="item.label"
              :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="配置项默认值" prop="value" v-if="dataForm.editorClass=='TEXT'">
            <el-input v-model="dataForm.value" placeholder="配置项默认值" >
              <template slot="append"><BaseTooltip slot="" content="例如：数字70，输入70；数字70%，输入0.7"></BaseTooltip></template>
            </el-input>
          </el-form-item>
          <el-form-item label="配置项默认值" prop="value" v-if="dataForm.editorClass=='DATE'">
            <el-date-picker v-model="dataForm.value" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="配置项默认值" prop="value" v-if="!dataForm.id && dataForm.editorClass=='COMBOBOX'">
            <el-input v-model="dataForm.value" placeholder="请输入单选默认值" :disabled="disableIsShow"></el-input>
          </el-form-item>
          <el-form-item label="配置项默认值" prop="value" v-if="dataForm.editorClass=='COLOR'">
            <el-color-picker v-model="dataForm.value"></el-color-picker>
          </el-form-item>
          <el-form-item label="配置项默认值" prop="value" v-if="!dataForm.id && dataForm.editorClass=='CHECKBOXTREE'">
            <el-input v-model="dataForm.value" placeholder="请输入多选默认值" :disabled="disableIsShow"></el-input>
          </el-form-item>
          <!-- <el-form-item label="日期格式" prop="dateFormat" v-if="dataForm.editorClass=='DATE'">
            <el-input v-model="dataForm.dateFormat" placeholder="日期格式" :disabled="disableIsShow"></el-input>
          </el-form-item> -->
          <el-form-item label="下拉框提供者类型" prop="comboboxProviderType" v-if="isType">
            <el-input v-model="dataForm.comboboxProviderType" placeholder="下拉框提供者类型"></el-input>
          </el-form-item>
          <el-form-item label="下拉选择项集合键值对（xxx:yyy,ttt:yyy）" prop="items" v-if="isType">
            <el-input v-model="dataForm.items" placeholder="下拉选择项集合键值对（0:否,1:是）"></el-input>
          </el-form-item>
          <el-form-item label="下拉选择项集合获取sql" prop="selectItemsSql" v-if="isType">
            <el-input v-model="dataForm.selectItemsSql" placeholder="下拉选择项集合获取sql"></el-input>
          </el-form-item>
          <el-form-item label="TAB页URL地址" prop="tabUrl" v-if="dataForm.editorClass=='TAB'">
            <el-input v-model="dataForm.tabUrl" placeholder="TAB页URL地址"></el-input>
          </el-form-item>
          <el-form-item label="校验正则表达式" prop="validatorRegexp" v-if="isType">
            <el-input v-model="dataForm.validatorRegexp" placeholder="校验正则表达式"></el-input>
          </el-form-item>
          <el-form-item label="正则表达式提示信息" prop="regexpInfo" v-if="isType">
            <el-input v-model="dataForm.regexpInfo" placeholder="正则表达式提示信息"></el-input>
          </el-form-item>
          <el-form-item label="配置项描述" prop="description">
            <el-input v-model="dataForm.description" placeholder="配置项描述"></el-input>
          </el-form-item>
          <el-form-item label="是否只读" prop="readonly" v-if="!dataForm.id">
            <el-select v-model="dataForm.readonly" placeholder="是否只读">
              <el-option key="1" label="是" :value="1"></el-option>
              <el-option key="0" label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否必须" prop="required" v-if="!dataForm.id">
            <el-select v-model="dataForm.required" placeholder="是否必须">
              <el-option key="1" label="是" :value="1"></el-option>
              <el-option key="0" label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否为系统内置" prop="systemic">
            <el-row>
              <el-col :span="20">
                <el-select v-model="dataForm.systemic" placeholder="选择控件类型">
                <el-option key="1" label="系统内置" :value="1"></el-option>
                <el-option key="0" label="自定义" :value="0"></el-option>
              </el-select>
              </el-col>
              <el-col :span="4" style="text-align: center;">
                <BaseTooltip content="系统内置不允许删除" ></BaseTooltip>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="下拉单选菜单" v-if="selectOne">
            <el-select v-model="time" @change="handleChangeOne">
              <el-option
                v-for="item in optionsaryOne"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="下拉多选菜单" v-if="selectTow">
            <el-select v-model="timeary" multiple @change="handleChangeTow">
              <el-option
                v-for="item in optionsaryTow"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序号" prop="sortNo">
            <el-input-number type="number" :min="0" v-model="dataForm.sortNo" placeholder="排序号"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>
</template>

<script>
import BaseTooltip from '@/components/base/BaseTooltip.vue'
import getPageTitle from '@/utils/get-page-title'
export default {
  components: {
    BaseTooltip
  },
  data () {
    return {
      visible: false,
      optionsaryOne: [],
      optionsaryTow: [],
      time: '',
      timeary: [],
      selectOne: false,
      selectTow: false,
      dataForm: {
        id: '',
        configtypeId: '',
        code: '',
        name: '',
        comboboxProviderType: '',
        items: '',
        selectItemsSql: '',
        description: '',
        editorClass: 'TEXT',
        readonly: 1,
        value: '',
        valueArray: [],
        regexpInfo: '',
        required: 1,
        tabUrl: '',
        validatorRegexp: '',
        sortNo: '',
        systemic: 0
      }
    }
  },
  computed: {
    disableIsShow () {
      return !!this.dataForm.id
    },
    isType () {
      if (!this.dataForm.id && (this.dataForm.editorClass === 'COMBOBOX' || this.dataForm.editorClass === 'CHECKBOXTREE')) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {
    // 监听下拉框
    handleChangeOne (val) {
      this.dataForm.value = val
    },
    handleChangeTow (val) {
      // this.dataForm.value = val
      // console.log(val)
    },
    clearDataForm () {
      this.selectOne = false;
      this.selectTow = false;
      // this.dataForm.id = '';
      // this.dataForm.configtypeId = '';
      this.dataForm.code = '';
      this.dataForm.name = '';
      this.dataForm.comboboxProviderType = '';
      this.dataForm.items = '';
      this.dataForm.selectItemsSql = '';
      this.dataForm.dateFormat = '年月日';
      this.dataForm.description = '';
      this.dataForm.editorClass = 'TEXT';
      this.dataForm.readonly = 1;
      this.dataForm.value = '';
      this.dataForm.regexpInfo = '';
      this.dataForm.required = 1;
      this.dataForm.tabUrl = '';
      this.dataForm.validatorRegexp = '';
      this.dataForm.sortNo = '';
      this.dataForm.systemic = 0;
    },
    init (id, configtypeId, configtypeName) {
      this.dataForm.id = id
      this.dataForm.configtypeId = configtypeId
      this.dataForm.configtypeName = configtypeName
      this.visible = true

      this.$nextTick(async () => {
        // this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          let data = {
            id: this.dataForm.id
          }
          let res = await this.$axios.get('/sf/business/configitem/info', { params: data })
          if (res.status === 0) {
            let _data = res.data;
            this.dataForm.configtypeId = _data.configtypeId
            this.dataForm.code = _data.code
            this.dataForm.name = _data.name
            this.dataForm.comboboxProviderType = _data.comboboxProviderType
            this.dataForm.items = _data.items
            this.dataForm.selectItemsSql = _data.selectItemsSql
            this.dataForm.dateFormat = _data.dateFormat
            this.dataForm.description = _data.description
            this.dataForm.editorClass = _data.editorClass
            this.dataForm.readonly = _data.readonly
            this.dataForm.value = _data.value
            // 下拉单选
            if (_data.editorClass === 'COMBOBOX' && this.dataForm.id) {
              this.selectOne = true
              this.time = _data.value
              this.handleData(_data.items, 'optionsaryOne')
            } else {
              this.selectOne = false
            }
            // 下拉多选
            if (_data.editorClass === 'CHECKBOXTREE' && this.dataForm.id) {
              this.timeary = []
              this.selectTow = true
              let aryva = _data.value.replace(/，/g, ',')
              let ary = aryva.split(',')
              ary.forEach(item => {
                this.timeary.push(item)
              })
              this.handleData(_data.items, 'optionsaryTow')
            } else {
              this.selectTow = false
            }
            this.dataForm.value = _data.value
            this.dataForm.regexpInfo = _data.regexpInfo
            this.dataForm.required = _data.required
            this.dataForm.tabUrl = _data.tabUrl
            this.dataForm.validatorRegexp = _data.validatorRegexp
            this.dataForm.sortNo = _data.sortNo
            this.dataForm.systemic = _data.systemic
          }
        } else {
          this.clearDataForm()
        }
      })
    },
    handleData (str, key) {
      this[key] = []
      let itemsary = str;
      let itemsre = itemsary.replace(/，/g, ',');
      let itemrepla = itemsre.replace(/：/g, ':');
      let itemreplaray = itemrepla.split(',');
      let item = null;
      for (let i = 0; i < itemreplaray.length; i++) {
        item = itemreplaray[i].split(':');
        this[key].push({
          value: item[0],
          label: item[1]
        })
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          // let str = '';
          // console.log(this.timeary)
          // if (this.timeary instanceof Array && this.timeary.length > 0) {
          //   this.timeary.forEach(item => {
          //     str += ',' + item
          //   })
          //   this.dataForm.value = str.substring(1)
          // }
          var params = {
            'id': this.dataForm.id || undefined,
            'configtypeId': this.dataForm.configtypeId,
            'code': this.dataForm.code,
            'name': this.dataForm.name,
            'comboboxProviderType': this.dataForm.comboboxProviderType,
            'items': this.dataForm.items,
            'selectItemsSql': this.dataForm.selectItemsSql,
            'dateFormat': this.dataForm.dateFormat,
            'description': this.dataForm.description,
            'editorClass': this.dataForm.editorClass,
            'readonly': this.dataForm.readonly,
            'value': this.dataForm.value,
            'regexpInfo': this.dataForm.regexpInfo,
            'required': this.dataForm.required,
            'tabUrl': this.dataForm.tabUrl,
            'validatorRegexp': this.dataForm.validatorRegexp,
            'sortNo': this.dataForm.sortNo,
            'systemic': this.dataForm.systemic
          }
          let url = !this.dataForm.id ? '/sf/business/configitem/save' : '/sf/business/configitem/update'
          let res = await this.$axios.post(`${url}`, params)
          if (res.status === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshConfigItemData')
                this.$store.dispatch('user/getTitle').then(res => {
                  document.title = getPageTitle('配置管理', res)
                })
                
              }
            })
          } else {
            // this.$message.error(res.msg)
          }
        }
      })
    }
  }
}
</script>
