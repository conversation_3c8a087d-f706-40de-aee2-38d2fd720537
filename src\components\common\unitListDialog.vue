<template>
    <el-dialog :before-close="handleClose" class="unitdialog" title="选择单位" top="70px" :visible.sync="dialogTableVisible" width="70%" center>
        <el-form class="unitfcss" :model="pageBean" :inline="true">
            <el-form-item label="单位名称：">
                <el-input class="definput" v-model="pageBean.unitName" clearable placeholder="请输入单位名称"></el-input>
            </el-form-item>
            <el-form-item label="负责人：">
                <el-input class="definput" v-model="pageBean.chargePersonName" clearable placeholder="负责人姓名" disabled></el-input>
            </el-form-item>
            <el-form-item label="负责人部门：">
                <el-input class="definput" v-model="pageBean.chargePersonDepartment" clearable placeholder="负责人部门" disabled></el-input>
            </el-form-item>
            <el-form-item label="">
                <el-button class="defaultbtn ml20" icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
            </el-form-item>
        </el-form>
        
        <el-table class="mytable" :data="dataList" height="478px" v-loading="isLoading">
            <el-table-column class-name="column_blue" property="unitName" label="单位名称" align="center"></el-table-column>
            <el-table-column 
                sortable="custom" 
                prop="customerNumber" 
                label="下属客户数量" 
                align="center" 
                min-width="140px">
                <template slot-scope="scope">
                    <span class="d_color defbtn">{{
                        scope.row.customerNumber
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="provinceName"
                label="省"
                align="center"
                min-width="150px"
            >
            </el-table-column>
            <el-table-column
                prop="cityName"
                label="市"
                align="center"
                min-width="150px"
            >
                <template slot-scope="scope">
                    {{ scope.row.cityName ? scope.row.cityName : '--' }}
                </template>
            </el-table-column>
            <el-table-column align="center" min-width="160px" class-name="chargePersoncss" label="单位负责人">
                <template slot-scope="scope">
                    <div v-if="scope.row.chargePersonNames && scope.row.chargePersonNames.length > 0">
                        <el-tag class="cuscss" v-for="(item, index) in scope.row.chargePersonNames" :key="index">{{ item }}</el-tag>
                    </div>
                    <div v-else>—</div>
                </template>
            </el-table-column>
            <el-table-column align="center" min-width="160px" class-name="chargePersoncss" label="负责人部门">
                <template slot-scope="scope">
                    <div v-if="scope.row.chargePersonDepartments && scope.row.chargePersonDepartments.length > 0">
                        <span class="cpdepartmentcss" v-for="(item, index) in scope.row.chargePersonDepartments" :key="index">{{
                            index == scope.row.chargePersonDepartments.length - 1 ? item : `${item},`
                        }}</span>
                    </div>
                    <div v-else>—</div>
                </template>
            </el-table-column>
            <el-table-column property="phone" label="电话" align="center"></el-table-column>
            <el-table-column property="edit" label="操作" align="center">
                <template slot-scope="scope" >
                    <div class="rbtn deffont" v-if="isUnitSelected(scope.row.id)" @click="chooseAction(scope.row)">取消选择</div>
                    <div class="bbtn deffont" v-else @click="chooseAction(scope.row)">选择</div>
                </template>
            </el-table-column>
        </el-table>
        <div class="center">
            <el-button class="defaultbtn" type="primary" @click="submitAction">提交</el-button>
        </div>
        <page 
        :currentPage="pageBean.pageNum" 
        :pageSize="pageBean.pageSize" 
        :total="total"
         @updatePageNum="handleCurrentChange"
        ></page>
        
        <!-- 添加客户列表弹框 -->
        <unitcustomer
            ref="unitcustomer"
            :unitName="unitCustomerName"
            :visible="dialogCusVisible"
            @updateVisible="updateUnitCusVisible"
        ></unitcustomer>
    </el-dialog>
</template>

<script>
import page from './page.vue';
import { listUnit } from "@/api/clientmanagement/unit";
// 导入客户组件
import unitcustomer from '../clientManagement/unit/components/unitcustomer.vue';

export default {
    components:{
        page,
        unitcustomer
    },
    data(){
        return{
            isLoading: false,
            dataList: [],
            pageBean: {
                isDeleted: 0,
                unitName: "",
                pageNum: 1,
                pageSize: 8,
                // 这里使用与index.vue相同的参数结构
                source: '',
                chargePersonName: '',
                chargePersonDepartment: '',
                createByName: '',
                createByDepartment: '',
                startTime: '',
                endTime: '',
                time: '',
                unitType: '',
                unitCharacter: '',
                provinceId: '',
                cityId: '',
            },
            total: 0,
            selectedUnits: [],
            // 添加客户弹框需要的数据
            dialogCusVisible: false,
            unitCustomerName: ''
        }
    },
    props:{
        visible:{
            type: Boolean,
            default: false
        },
        selectedUnitId: {  
            type: [String, Array],
            default: ''
        },
        chargePersonName: {
            type: String,
            default: ''
        },
        chargePersonDepartment: {
            type: String,
            default: ''
        }
    },
    computed:{
        dialogTableVisible:{
            get(){
                return this.visible;
            },
            set(val){
                this.$emit('updateVisible', val);
            },
        }
    },
    methods:{
        handleClose(){
            this.pageBean.pageNum = 1;
            this.pageBean.unitName = '';
            this.selectedUnits = [];  
            this.dialogTableVisible = false;
        },
        searchAction(){
            this.pageBean.pageNum = 1;
            this.loadData();
        },
        loadData(selectedIds = []) {
            this.isLoading = true;
            
            this.pageBean.chargePersonName = this.chargePersonName;
            this.pageBean.chargePersonDepartment = this.chargePersonDepartment;
            
            if(!this.chargePersonName) {
                this.isLoading = false;
                this.$message.warning('请先选择负责人');
                this.dialogTableVisible = false;
                return;
            }
            
            if(selectedIds && !Array.isArray(selectedIds)) {
                selectedIds = selectedIds ? [selectedIds] : [];
            }
            
            listUnit(this.pageBean).then((result) => {
                this.isLoading = false;
                this.dataList = result.data;
                this.total = result.page.total;
                
                if(selectedIds.length > 0) {
                    selectedIds.forEach(id => {
                        const unit = this.dataList.find(item => item.id === id);
                        if(unit && !this.isUnitSelected(unit.id)) {
                            this.selectedUnits.push(unit);
                        }
                    });
                }
            }).catch(() => {
                this.isLoading = false;
            });
        },
        submitAction(){
            if(this.selectedUnits.length === 0) {
                this.$message.warning('请选择至少一个单位');
                return;
            }
            
            const unitIds = this.selectedUnits.map(unit => unit.id);
            const unitNames = this.selectedUnits.map(unit => unit.unitName);
            
            this.$emit('updateUnit', {
                unitIds: unitIds,
                unitNames: unitNames,
                id: unitIds[0] 
            });
            
            this.dialogTableVisible = false;
            this.selectedUnits = [];
        },
        handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.loadData();
        },
        chooseAction(unit) {
            const index = this.selectedUnits.findIndex(item => item.id === unit.id);
            if(index > -1) {
                this.selectedUnits.splice(index, 1);
            } else {
                this.selectedUnits.push(unit);
            }
        },
        isUnitSelected(unitId) {
            return this.selectedUnits.some(unit => unit.id === unitId);
        },
        
        updateUnitCusVisible(val) {
            this.dialogCusVisible = val;
        }
    },
    watch: {
        visible(newVal) {
            if(newVal) {
                let selectedIds = Array.isArray(this.selectedUnitId) 
                    ? this.selectedUnitId 
                    : (this.selectedUnitId ? [this.selectedUnitId] : []);
                    
                this.loadData(selectedIds);
            }
        },
        chargePersonName(newVal) {
            this.pageBean.chargePersonName = newVal;
        },
        chargePersonDepartment(newVal) {
            this.pageBean.chargePersonDepartment = newVal;
        },
    }
}
</script>

<style scoped>
.center{
    margin-top: 50px;
    text-align: center;
}
.bbtn{
    color: #4285F4;
    cursor: pointer;
}
.rbtn{
    color: #F45961;
    cursor: pointer;
}

.unitfcss{
    line-height: 34px;
}
.unitfcss /deep/.el-form-item__label{
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
}
.unitfcss /deep/.el-form-item{
    margin-bottom: 20px;
}
.ml20{
    margin-left: 20px;
}
.unitdialog /deep/.el-dialog__body{
    padding: 20px;
    padding-top: 0px;
}
.unitdialog /deep/.el-dialog__header{
    border: none;
}

/* 添加新的样式 */
.chargePersoncss .cell {
  padding-bottom: 7px !important;
}
.cpdepartmentcss {
  font-size: 12px !important;
}
.cuscss {
  margin-right: 5px;
  margin-bottom: 5px;
}
.d_color {
  color: #4285F4;
}
.defbtn {
  cursor: pointer;
}
</style> 