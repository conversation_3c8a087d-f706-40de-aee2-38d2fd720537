<template>
       <div class="viewer__wrapper">
            <div class="viewer__mask">
              <span @click="closehandle" class="viewer__btn viewer__close"><i class="el-icon-close"></i></span>
              <div class="viewer__img" v-if="type==1">
                  <img class="img" :src="url"/>
              </div>
              <div class="viewer__img" v-if="type==2">
                <video-player  class="video-player vjs-custom-skin videocss"
                  ref="videoPlayer" 
                  :playsinline="true" 
                  :options="playerOptions"
                ></video-player>
              </div>
            </div>
        </div>
</template>

<script>
import VideoPlayer from 'vue-video-player'
import Vue from 'vue'
require('video.js/dist/video-js.css')
require('vue-video-player/src/custom-theme.css')
Vue.use(VideoPlayer)
  export default {
    data(){
        return {
          playerOptions : {
              playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
              autoplay: false, //如果true,浏览器准备好时开始回放。
              muted: false, // 默认情况下将会消除任何音频。
              loop: false, // 导致视频一结束就重新开始。
              preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
              language: 'zh-CN',
              aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
              fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
              sources: [{
                type: "",//这里的种类支持很多种：基本视频格式、直播、流媒体等，具体可以参看git网址项目
                src: this.url //url地址
              }],
              poster: "../../static/images/test.jpg", //你的封面地址
              // width: document.documentElement.clientWidth, //播放器宽度
              notSupportedMessage: '此视频暂无法播放，请稍后再试', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
              controlBar: {
                timeDivider: true,
                durationDisplay: true,
                remainingTimeDisplay: false,
                fullscreenToggle: true  //全屏按钮
              }
          }
        }
    },
    methods:{
      closehandle(){
        this.$el.parentNode.removeChild(this.$el)
        this.onClose()
      }
    },
    mounted(){
      this.playerOptions.sources[0].src = this.url
    }
  }
</script>

<style lang="scss" scoped>
.videocss{
  width:60%;
}
.img{
    margin-left: 0px;
    margin-top: 0px;
    max-height: 100%;
    max-width: 100%;
    opacity: 1;
    background-color: #FFFFFF;
}
.viewer__img{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.viewer__close {
    top: 40px;
    right: 40px;
    width: 40px;
    height: 40px;
    font-size: 24px;
    color: #fff;
    background-color: #606266;
}
.viewer__btn {
    position: absolute;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    opacity: .8;
    cursor: pointer;
    box-sizing: border-box;
    user-select: none;
}
.viewer__wrapper{
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2005;
}
.viewer__mask{
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background:rgba(0,0,0,.5)
}

</style>