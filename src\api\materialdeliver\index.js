import service from '@/utils/request.js'



export function deliverList(params) {
  return service.request({
    method: 'get',
    url: `/crm/business/materialdeliver/list`,
    params
  });
}


// /crm/business/giftinfo/save

export function adddeliver(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/materialdeliver/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
export function updatedeliver(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/materialdeliver/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 
export function addtrack(data) {
    return service.request({
      method: 'post',
      url: '/crm/business/delivertrack/save',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    });
  }
export function deletedeliver(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/materialdeliver/delete',
    data,
  });
} 
// /crm/business/materialdeliver/verify
export function verify(data) {
    return service.request({
      method: 'post',
      url: '/crm/business/materialdeliver/verify',
      data,
    });
  } 

export function deliverInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/business/materialdeliver/info/${id}`,
  });
}
