@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei, Helvetica Neue,
    Helvetica, PingFang SC, Hiragino Sans GB, Arial, sans-serif;
}

.gColor {
  color: #56c36e;
}

.fixpb {
  padding-bottom: 56px !important;
  transform: none;
}

.fixpage {
  position: fixed;
  bottom: 0;
  width: calc(100vw - 170px);
  right: 0px;
  z-index: 10;
  background: #fff;
  box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.16);
  padding-bottom: 20px;
}

.mtb20 {
  margin: 14px 0;
}

.el-icon-close {
  font-size: 20px;
}

.pcc {
  width: 340px;
  margin: 0 auto;
  text-align: center;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #666666;
  line-height: 24px;
}

.ed .el-tabs__header {
  margin-bottom: 28px;
}

.circle {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  vertical-align: middle;
  position: relative;
  top: -1px;
  margin-right: 8px;
}

.c-one {
  background: rgb(50, 145, 248);
}

.c-two {
  background: rgb(255, 153, 89);
}

.c-three {
  background: rgb(94, 188, 100);
}

.c-four {
  background: rgb(255, 37, 37);
}

.c-five {
  background: rgb(252, 202, 0);
}

.ed .el-drawer__header span {
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.edt .el-tabs__header {
  margin-bottom: 30px;
}

.edt .el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #666666;
  height: 52px;
  line-height: 52px;
}

.edt .is-active {
  color: #4285f4;
}

.ed .el-drawer {
  min-width: 755px;
  overflow-y: auto;
}

.el-textarea .el-input__count {
  background-color: transparent;
  bottom: -27px;
}

.bBtn,
.bBtn:hover,
.bBtn:focus {
  color: #4285f4;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
}

.dBtn,
.dBtn:hover,
.dBtn:focus {
  color: #f45961;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
}

.search {
  margin-left: 10px;
}

label {
  font-weight: 700;
}

.right {
  float: right;
}

html {
  height: 100%;
  box-sizing: border-box;
}

.left {
  float: left;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

:focus {
  outline: 0;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.app-container {
  padding: 20px;
  background: #fff;
}

.zhanwei {
  visibility: hidden;
}

/* 为所有具有滚动条的元素自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  /* 设置滚动条的宽度 */
}

/* 设置滚动条的轨道（背景）样式 */
::-webkit-scrollbar-track {
  background: white;
  /* 设置轨道的背景颜色 */
}

/* 设置滚动条的滑块（滚动条）样式 */
::-webkit-scrollbar-thumb {
  background: #dbdbdb;
  /* 设置滑块的背景颜色 */
  border-radius: 3px !important;
}

/* 设置滚动条滑块的hover样式 */
::-webkit-scrollbar-thumb:hover {
  background: #dbdbdb;
  /* 设置滑块的hover背景颜色 */
}

.page-controller {
  margin-top: 20px;
  text-align: center;
}

.defaultbtn {
  padding: 9px 20px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4 !important;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961 !important;
}

.selectedCss {
  font-size: 14px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333 !important;
}

.defr {
  float: right !important;
}

.definput .el-range__icon {
  margin-left: 0px;
}

.definput .el-input__inner {
  line-height: 34px;
  height: 34px;
  font-size: 14px;
  color: #333333;
  font-weight: 400;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei !important;
}

.definput .el-input__icon {
  line-height: 34px;
}

.definput.el-input__inner {
  padding: 0;
  line-height: 34px !important;
  height: 34px !important;
  font-size: 14px !important;
  color: #333333 !important;
  font-weight: 400 !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei !important;
}

.dateRWidth {
  width: 350px !important;
}

.inputWid150 {
  width: 150px !important;
}

.removescrollbar {
  .el-scrollbar__thumb {
    display: none;
  }

  .el-select-dropdown__item {
    line-height: 40px !important;
  }
}

.mytable {
  margin-top: 0px !important;
}

.mytable .cell {
  min-height: 52px;
  padding: 14px 10px !important;
  line-height: 24px;
}

.mytable th {
  background-color: #f6f7fb !important;
  height: 52px;
  line-height: 52px;
  border: none;
}

.mytable td {
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 0;
}

.mytable th {
  padding: 0;
  border: 0 !important;
}

.mytable th>.cell {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.mytable::before {
  height: 0px !important;
}

.mytable .el-table__fixed-right::before {
  height: 0px !important;
}

.column_blue .cell {
  color: #4285f4;
}

.deffont {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.btn_h42 {
  padding: 12px 20px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: white;
  background-color: #4285f4;
}

.myform .el-form-item__label {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.myform .el-form-item {
  margin-right: 16px !important;
  margin-bottom: 16px;
}

.myform .el-form-item:last-child {
  margin-right: 0px !important;
}

.addfcss .el-form-item__label {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 34px;
}

.addfcss .el-form-item {
  margin-bottom: 18px;
  padding-right: 3vw;
  min-height: 34px;
  line-height: 34px;
}

.addfcss .el-form-item__content {
  min-height: 34px;
  line-height: 34px;
}

.labeltext label {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.unitbtn {
  height: 34px;
  line-height: 32px;
  padding: 0px 15px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 100%;
  text-align: left;
  color: #cbcbcb;
  font-size: 14px;
  cursor: pointer;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.w100 {
  width: 100%;
}

.cflex {
  display: flex;
}

.cfd {
  flex-direction: column;
}

.cf1 {
  flex: 1;
}

.mt100 {
  margin-top: 100px !important;
}

.bgwhite {
  background: #fff;
  padding: 20px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

.el-table__fixed-right-patch {
  background-color: #f6f7fb !important;
}

/* 火狐 */
input[type="number"] {
  -moz-appearance: textfield;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.clearfix:after {
  content: "";
  display: block;
  height: 0;
  visibility: hidden;
  clear: both;
}


/**
 * "S--VIP客户":"#AC6FFB",
        "A--成交客户":"#56C36E",
        "B--重要客户":"#FD7A41",
        "C--潜在客户":"#F45961",
        "D--新建客户":"#4285F4"
 */
.s_color {
  color: #AC6FFB;
}

.a_color {
  color: #56C36E;
}

.b_color {
  color: #FD7A41;
}

.c_color {
  color: #F45961;
}

.d_color {
  color: #4285F4;
}

.cuscss {
  margin-left: 8px;
  height: 24px !important;
  line-height: 22px !important;
  padding: 0px 5px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  background: #dfeafd;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
  margin-bottom: 8px;
}

.cuscss:last-child {
  margin-right: 0px;
}

.upcss {
  color: #F46D40 !important;
}

.downcss {
  color: #56C36E !important;
}

.chipingcss {
  color: #4285F4 !important;
}

.multiline {
  display: -webkit-box; /* 必须设置为box */
  -webkit-box-orient: vertical; /* 垂直排列盒子 */
  -webkit-line-clamp: 2; /* 限制显示的行数为3 */
  overflow: hidden; /* 隐藏溢出的内容 */
}