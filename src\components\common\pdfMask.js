import pdfMask from './pdfMask.vue'
let instance;
let Vue
let getInstance = () => {
  const pdfMaskConstructor = Vue.extend(pdfMask);
  instance = new pdfMaskConstructor().$mount();
}
export default {
  install(_Vue) {
    Vue = _Vue
    let viewMask = (options) => {
      if (!instance) {
        getInstance();
      }
      console.log(options)
      document.body.appendChild(instance.$el);
      instance.url = options.url
      instance.type = options.type
      instance.visible = true
    }
    _Vue.prototype.$viewMask = viewMask
  }
}
