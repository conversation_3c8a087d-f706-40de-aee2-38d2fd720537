<template>
  <div>
    <div class="backc">
      <back>单位数据</back>
    </div>
      <el-tabs v-model="activeName" @tab-click="handleClick"  class="tabscss">
        <el-tab-pane label="出版社数据" name="first">
          <p class="bcss" v-show="isPress" @click="changePress">切换为码洋数据>></p>
          <p class="bcss" v-show="!isPress" @click="changePress">切换为数量数据>></p>
          <div v-show="isPress">
            <el-table @sort-change="sortChange"  class=" mytable"  :data="tableDataUnit"  v-loading="isLoading">
              <el-table-column prop="unitName" label="出版社名称" align="center">
              </el-table-column>
              <el-table-column sortable prop="customerNumber" label="数量综合占比" align="center">
              </el-table-column>
              <el-table-column sortable prop="unitTypeName" label="总册数" align="center">
              </el-table-column>
              <el-table-column sortable prop="unitCharacterName" label="合作册数参占比" align="center">
              </el-table-column>
              <el-table-column sortable  label="非合作册数参占比" align="center">
              </el-table-column>
              <el-table-column
                prop="edit"
                width="100"
                fixed="right"
                label="操作">
                <template slot-scope="scope">
                  <el-button class="bbtn"    type="text" @click="restoreUnit(scope.row)"> 查看明细</el-button>
                </template>
              </el-table-column>
              <template slot="empty">
                <nolist></nolist>
              </template>
            </el-table>
            <page 
            v-show="activeName=='first'"
            :currentPage="pageBeanUnit.pageNum" 
            :total="totalUnit" 
            :pageSize="pageBeanUnit.pageSize" 
            @updatePageNum="handleCurrentChangeUnit"
            ></page>   
          </div>
          <div v-show="!isPress">
            <el-table  class=" mytable"  :data="tableDataUnit"  v-loading="isLoading">
              <el-table-column prop="unitName" label="出版社名称" align="center">
              </el-table-column>
              <el-table-column sortable prop="customerNumber" label="码洋综合占比" align="center">
              </el-table-column>
              <el-table-column sortable prop="unitTypeName" label="总码洋(万元)" align="center">
              </el-table-column>
              <el-table-column sortable prop="unitCharacterName" label="合作码洋与占比" align="center">
              </el-table-column>
              <el-table-column sortable  label="非合作码洋与占比" align="center">
              </el-table-column>
              <el-table-column
                prop="edit"
                width="100"
                fixed="right"
                label="操作">
                <template slot-scope="scope">
                  <el-button class="bbtn"    type="text" @click="restoreUnit(scope.row)"> 查看明细</el-button>
                </template>
              </el-table-column>
              <template slot="empty">
                <nolist></nolist>
              </template>
            </el-table>
            <page 
            v-show="activeName=='first'"
            :currentPage="pageBeanUnit.pageNum" 
            :total="totalUnit" 
            :pageSize="pageBeanUnit.pageSize" 
            @updatePageNum="handleCurrentChangeUnit"
            ></page>
          </div>
  
        </el-tab-pane>
        <el-tab-pane  label="学科数据" name="second">
          <p class="bcss" v-show="isXue" @click="changeXue">切换为码洋数据>></p>
          <p class="bcss" v-show="!isXue" @click="changeXue">切换为数量数据>></p>
          <div class="isXue" v-show="isXue">
            <el-table  class=" mytable"  :data="tableDataUnit"  v-loading="isLoading">
              <el-table-column prop="unitName" label="专业名称" align="center">
              </el-table-column>
              <el-table-column sortable prop="customerNumber" label="数量综合占比" align="center">
              </el-table-column>
              <el-table-column sortable prop="unitTypeName" label="总册数" align="center">
              </el-table-column>
              <el-table-column sortable prop="unitCharacterName" label="合作册数参占比" align="center">
              </el-table-column>
              <el-table-column sortable  label="非合作册数参占比" align="center">
              </el-table-column>
              <el-table-column
                prop="edit"
                width="100"
                fixed="right"
                label="操作">
                <template slot-scope="scope">
                  <el-button class="bbtn"    type="text" @click="restoreUnit(scope.row)"> 查看明细</el-button>
                </template>
              </el-table-column>
              <template slot="empty">
                <nolist></nolist>
              </template>
            </el-table>
            <page 
            v-show="activeName=='first'"
            :currentPage="pageBeanUnit.pageNum" 
            :total="totalUnit" 
            :pageSize="pageBeanUnit.pageSize" 
            @updatePageNum="handleCurrentChangeUnit"
            ></page> 
          </div>
          <div class="" v-show="!isXue">
            <el-table  class=" mytable"  :data="tableDataUnit"  v-loading="isLoading">
              <el-table-column prop="unitName" label="专业名称" align="center">
              </el-table-column>
              <el-table-column sortable prop="customerNumber" label="码洋综合占比" align="center">
              </el-table-column>
              <el-table-column sortable prop="unitTypeName" label="总码洋(万元)" align="center">
              </el-table-column>
              <el-table-column sortable prop="unitCharacterName" label="合作码洋与占比" align="center">
              </el-table-column>
              <el-table-column sortable  label="非合作码洋与占比" align="center">
              </el-table-column>
              <el-table-column
                prop="edit"
                width="100"
                fixed="right"
                label="操作">
                <template slot-scope="scope">
                  <el-button class="bbtn"    type="text" @click="restoreUnit(scope.row)"> 查看明细</el-button>
                </template>
              </el-table-column>
              <template slot="empty">
                <nolist></nolist>
              </template>
            </el-table>
            <page 
            v-show="activeName=='first'"
            :currentPage="pageBeanUnit.pageNum" 
            :total="totalUnit" 
            :pageSize="pageBeanUnit.pageSize" 
            @updatePageNum="handleCurrentChangeUnit"
            ></page> 
          </div>
        
        </el-tab-pane>
        <el-tab-pane  label="营销数据" name="three">
          <el-table  class=" mytable"  :data="tableDataUnit"  v-loading="isLoading">
            <el-table-column prop="unitName" label="业务经理" align="center">
            </el-table-column>
            <el-table-column  prop="customerNumber" label="所属部门" align="center">
            </el-table-column>
            <el-table-column sortable prop="unitTypeName" label="数量综合占比" align="center">
            </el-table-column>
            <el-table-column sortable prop="unitCharacterName" label="码洋综合占比" align="center">
            </el-table-column>
            <el-table-column
              prop="edit"
              width="100"
              fixed="right"
              label="操作">
              <template slot-scope="scope">
                <el-button class="bbtn"    type="text" @click="restoreUnit(scope.row)"> 查看明细</el-button>
              </template>
            </el-table-column>
            <template slot="empty">
              <nolist></nolist>
            </template>
          </el-table>
          <page 
          v-show="activeName=='first'"
          :currentPage="pageBeanUnit.pageNum" 
          :total="totalUnit" 
          :pageSize="pageBeanUnit.pageSize" 
          @updatePageNum="handleCurrentChangeUnit"
          ></page>   
        </el-tab-pane>
      </el-tabs>
  </div>
</template>

<script>
import page from '../../common/page.vue';
import nolist from '../../common/nolist.vue';
import { recycleCustomerList, physicalDelete , restoreCustomer,restoreUnit,physicalDeleteUnit } from "@/api/clientmanagement/customer";
import { listUnit} from '@/api/clientmanagement/unit'
import back from '../../common/back.vue'
export default {
    components:{
        page,
        nolist,
        back
    },
    data(){
        return{  
            isXue:true,
            isPress:true,
            totalUnit:0,
            sourceName:{
                1:'公司资源',
                2:'自主开拓'
            },
            dialogVisible:false,
            deleteData:{},
            isLoading:false,
            tableData:[],
            tableDataUnit:[],
            total:0,
            pageBean:{
                isDeleted:1,
                pageNum:1,
                pageSize:10,
            },
            pageBeanUnit:{
                isDeleted:1,
                pageNum:1,
                pageSize:10,
            },
            activeName:'first',
            deleteType:1,
        }
    },
    created(){
        this.loadData();
        this.loadDataUnit()
    },
    methods:{
      sortChange(column) {
        //打印看看参数有哪些？
        console.log('排序', column.prop, column.order);
        //排序默认是从第一页开始
      },
      changeXue(){
        this.isXue = !this.isXue
      },
      changePress(){
          this.isPress = !this.isPress
      },
        loadDataUnit() {
                this.isLoading = true;
                listUnit(this.pageBeanUnit).then((result) => {
                        this.tableDataUnit = result.data;
                        this.totalUnit = result.page.total;
                        this.isLoading = false;
                }).catch((err) => {
                        this.isLoading = false;
                });
       },
        handleClick(tab, event) {
                this.activeName = tab.name;
                if(tab.name=='first'){
                    this.pageBean.pageNum = 1
                    this.loadData();
                    this.deleteType = 1;
                }else{
                    this.pageBeanUnit.pageNum = 1
                    this.loadDataUnit();
                    this.deleteType = 2
                }
        },
        loadData(){
            this.isLoading = true;
            recycleCustomerList(this.pageBean).then((result) => {
                this.tableData = result.data;
                this.total = result.page.total;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        submitDialog(){
          let res 
          if(this.deleteType == 1){
            physicalDeleteUnit({id:this.deleteData.id}).then(res=>{
                    this.$message({
                        type:'success',
                        message:'已删除'
                    })
                    this.loadDataUnit();
            })
          }else{
            physicalDelete({id:this.deleteData.id}).then(res=>{
                    this.$message({
                        type:'success',
                        message:'已删除'
                    })
                    this.loadData();
            })
          }
          this.dialogVisible = false;
        },
        deleteAction(data){
            this.deleteData = data;
            this.dialogVisible = true;
        },
        deleteActionUnit(data){
            this.deleteData = data;
            this.dialogVisible = true;
        },
        restore(data){
            restoreCustomer({id:data.id}).then((result) => {
                if (result.data) {
                    this.$message({
                        type:'success',
                        message:'已还原，请到客户管理进行查看。'
                    })
                    this.loadData();
                } else {
                    this.$message({
                        type:'error',
                        message:result.msg
                    })
                }
            }).catch((err) => {
                
            });
        },
        restoreUnit(data){
             restoreUnit({id:data.id}).then((result) => {
                if (result.data) {
                    this.$message({
                        type:'success',
                        message:'已还原，请到单位管理进行查看。'
                    })
                    this.loadDataUnit();
                } else {
                    this.$message({
                        type:'error',
                        message:result.msg
                    })
                }
            }).catch((err) => {
                
            });
        },
        handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.loadData();
        },
        handleCurrentChangeUnit(page){
            this.pageBeanUnit.pageNum = page;
            this.loadDataUnit();
        }
    }
}
</script>

<style scoped>
  .bcss{
    color: #409EFF;
    text-align: right;
    cursor: pointer;
    font-size: 12px;
    margin-bottom: 10px;
  }
  .backc{
    margin-bottom: 20px;
  }
.pcc{
    margin: 0 auto;
    text-align: center;
}
.smtext{
    zoom: 0.8;
}
.fxcenter{
    width: 50px;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.zhiding{
    width: 16px;
    height: 16px;
    margin-right: 4px;
}
.mt{
    margin-top: 4px;
}
.cusnamecss{
    display: flex;
}
.tagcss{
    font-size: .625em !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 14px;
    min-width: 30px;
    line-height: 11px;
    border-radius: 2px;
    margin-right: 20px;
}
.genjin{
    color: #4285F4;
    background-color: #DFEAFD;
}
.fuze{
    color: #FEF2E7;
    background-color: #FF8D1A;
}
.xiezuo{
    color: #56C36E;
    background-color: #F3FEF6;
}
.tagcss:nth-child(2n+2){
    margin-top: 4px;
}
.customertable{
    margin-top: 10px !important;
}
.customertable .el-button+.el-button{
    margin-left: 40px;
}
.customertable .el-button{
    padding: 2px ;
}
.mr10{
    margin-right: 10px;
}
.bbtn,.bbtn:hover,.bbtn:focus{
  color: #4285F4;
}
.rbtn,.rbtn:hover,.rbtn:focus{
  color: #F45961;
}
.right{
    text-align: right;
}

</style>
