<template>
  <div>
    <back>返回</back>
    <div class="mainbg">
      <el-form ref="ruleForm" :model="form" :rules="rules" class="addfcss">
        <div class="content">
          <div class="flex">
            <div class="title">适用部门</div>
            <div class="box mh200">
              <el-form-item label="" prop="departmentIds">
                <div class="unitbtn" @click="chooseDepartment">
                  <span v-if="departmentNames" class="deffont"
                    >请选择适用部门</span
                  >
                  <span v-else class="pltcss">请选择适用部门</span>
                  <i
                    v-if="departmentNames"
                    @click.stop="clearDepartment"
                    class="rcenter el-icon-circle-close"
                  />
                  <i v-else class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
              <div class="deptbox">
                <div
                  class="deptitem"
                  v-for="item in selDepartmentData"
                  :key="item.id"
                >
                  <span class="deptname">{{ item.name }}</span>
                  <i class="el-icon-delete" @click="removeDepartment(item)"></i>
                </div>
              </div>
            </div>
          </div>
          <div class="title">审核人</div>
          <div class="box">
            <div class="approverbox">
              <div
                v-for="(approver, index) in approvers"
                :key="index"
                class="approver-item"
              >
                <el-form-item
                  :label="'审批人'"
                  :error="getApproverError(index)"
                >
                  <div
                    class="unitbtn"
                    @click="clickXuan('请选择审批人', 1, index)"
                  >
                    <span v-if="approver.id" class="deffont">{{
                      approver.name
                    }}</span>
                    <span v-else class="pltcss">请选择审批人</span>
                    <i class="rcenter el-icon-arrow-down" />
                  </div>
                </el-form-item>
                <i
                  v-if="approvers.length > 1"
                  class="el-icon-delete delete-approver-icon"
                  @click="removeApprover(index)"
                ></i>
              </div>
              <div class="add-approver" v-if="approvers.length < 3">
                <el-button type="text" @click="addNextApprover">
                  <i class="el-icon-plus"></i> 添加下一级审批人
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="pt20 btncenter">
          <el-form-item>
            <el-button
              class="btn_h42 wid98"
              type="primary"
              @click="submitForm"
              v-dbClick
              >保存</el-button
            >
          </el-form-item>
        </div>
      </el-form>

      <!-- 选择部门 -->
      <departmentDialog
        ref="deRef"
        dType="1"
        :visible.sync="dialogDepartmentVisible"
        @updateVisible="updateDepartmentVisible"
        @submitData="submitDepartmentData"
      >
      </departmentDialog>

      <!-- 选择人员 -->
      <systemDialog
        ref="systemdialog"
        :name="dialogName"
        :multipleNum="multipleNum"
        :visible.sync="dialogVisible"
        @updateVisible="updateSystemVisible"
        @submitData="submitData"
      >
      </systemDialog>
      <msg-dialog ref="msgdialog"/>
    </div>
  </div>
</template>

<script>
import departmentDialog from '@/components/common/departmentDialog.vue'
import systemDialog from '@/components/common/systemDialog.vue'
import back from '../../common/back.vue'
import { ruleSave, ruleInfo, ruleUpdate } from '@/api/managementCenter/index'

export default {
  components: {
    departmentDialog,
    systemDialog,
    back,
  },
  data() {
    return {
      form: {
        departmentIds: [],
        id: '',
      },
      rules: {
        departmentIds: [
          { required: true, message: '请选择适用部门', trigger: 'change' },
        ],
      },
      approvers: [{ id: '', name: '' }],
      approverErrors: [],
      departmentNames: '',
      selDepartmentData: [],
      dialogDepartmentVisible: false,
      dialogVisible: false,
      dialogName: '',
      multipleNum: 1,
      currentApproverIndex: 0,
    }
  },
  created() {
    this.loadInfo()
  },

  methods: {
    loadInfo() {
      var id = this.$route.query.id
      if (id) {
        this.form.id = id
        this.isLoading = true
        ruleInfo(id)
          .then((result) => {
            let data = result.data
            if (data.oneLevelUser) {
              this.approvers[0].id = data.oneLevelUser
              this.approvers[0].name = data.oneLevelUserName
            }
            if (data.twoLevelUser) {
              this.approvers.push({
                id: data.twoLevelUser,
                name: data.twoLevelUserName,
              })
            }
            if (data.threeLevelUser) {
              this.approvers.push({
                id: data.threeLevelUser,
                name: data.threeLevelUserName,
              })
            }
            this.isLoading = false
            this.selDepartmentData = result.data.departmentList
            this.form.departmentIds = this.selDepartmentData.map(
              (dep) => dep.id
            )
            this.form.id = result.data.id
          })
          .catch((err) => {
            this.isLoading = false
          })
      }
    },
    getApproverError(index) {
      return this.approverErrors[index] || ''
    },

    validateApprovers() {
      this.approverErrors = this.approvers.map((approver) => {
        return approver.id ? '' : '请选择审批人'
      })
      return this.approverErrors.every((error) => !error)
    },
    chooseDepartment() {
      this.dialogDepartmentVisible = true
      this.$refs.deRef.loadData()
      this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
    },

    clearDepartment() {
      this.departmentNames = ''
      this.form.departmentIds = []
      this.selDepartmentData = []
    },

    removeDepartment(item) {
      const index = this.selDepartmentData.findIndex(
        (dep) => dep.id === item.id
      )
      if (index !== -1) {
        this.selDepartmentData.splice(index, 1)
        this.form.departmentIds = this.selDepartmentData.map((dep) => dep.id)
        this.departmentNames = this.selDepartmentData
          .map((dep) => dep.name)
          .join(',')
      }
      if (this.form.departmentIds.length === 0) {
        this.departmentNames = ''
      }
    },

    updateDepartmentVisible(val) {
      this.dialogDepartmentVisible = val
    },

    submitDepartmentData(data) {
      this.dialogDepartmentVisible = false
      this.selDepartmentData = data
      console.log(data)
      let departmentIds = data.map((item) => item.id)
      let departmentNames = data.map((item) => item.name)
      this.form.departmentIds = departmentIds
      this.departmentNames = departmentNames.join(',')
      this.$refs.ruleForm.validateField('departmentIds')
    },

    clickXuan(name, multipleNum, index) {
      this.dialogName = name
      this.multipleNum = multipleNum
      this.currentApproverIndex = index
      this.$refs.systemdialog.loadData()
      if (name == '请选择审批人') {
        const approver = this.approvers[index]
        approver.id
          ? this.$refs.systemdialog.updateWorksId([
              { id: approver.id, name: approver.name },
            ])
          : this.$refs.systemdialog.updateWorksId([])
      }
      this.dialogVisible = true
    },

    updateSystemVisible(value) {
      this.dialogVisible = value
    },

    submitData(data, type) {
      if (type == '请选择审批人') {
        const index = this.currentApproverIndex
        const exists = this.approvers.some((item) => item.id === data[0].id)
        if (exists) {
          this.$message.error('审核人重复')
          return
        }
        if (data.length > 0) {
          this.approvers[index].id = data[0].id
          this.approvers[index].name = data[0].name
        } else {
          this.approvers[index].id = ''
          this.approvers[index].name = ''
        }
        this.$set(this.approverErrors, index, '')
      }
      this.updateSystemVisible(false)
    },

    addNextApprover() {
      if (this.approvers.length < 3) {
        this.approvers.push({ id: '', name: '' })
        this.approverErrors.push('')
      }
    },

    removeApprover(index) {
      if (this.approvers.length > 1) {
        this.approvers.splice(index, 1)
        this.approverErrors.splice(index, 1)
      }
    },
    ruleSaveApi(params) {
      let saveOrUpdate = this.$route.query.id ? ruleUpdate : ruleSave
      saveOrUpdate(params).then((res) => {
        if (res.status == 0) {
          this.$message.success('保存成功')
          this.$router.go(-1)
        }else{
          this.$refs.msgdialog.show({
            type:'error',
            title:'保存失败',
            msg:'所选部门中，存在已有该规则部门，请取消选择后重试',
          })
        }

      }).catch(()=>{

      })
    },
    submitForm() {

      const approverValid = this.validateApprovers()
      this.$refs.ruleForm.validate((valid) => {
        if (valid && approverValid) {
          const formData = {
            departments: this.form.departmentIds.join(','),
            ruleType: this.$route.query.ruleType,
            id: this.form.id ? this.form.id : '',
          }
          let obj = {
            0: 'one',
            1: 'two',
            2: 'three',
          }
          this.approvers.forEach((item, index) => {
            formData[`${obj[index]}LevelUser`] = item.id
          })
          this.ruleSaveApi(formData)
          console.log('提交表单', formData)
        } else {
          if (!approverValid) {
            this.$message.error('请选择所有审批人')
          }
          return false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: 100%;
  border-radius: 10px;
  margin: 16px 0px;
}

.btncenter {
  text-align: center;
}

.wid98 {
  width: 98px;
}

.unitbtn {
  width: 200px;
  height: 34px;
  line-height: 34px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 15px;
  position: relative;
  cursor: pointer;
}

.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}

.pltcss {
  color: #cdcdcd;
}

.deffont {
  color: #333333;
}
.box {
  width: 100%;
  min-height: 400px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  margin-bottom: 20px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.mh200 {
  min-height: 200px;
}
.title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}
.rb {
  border-right: 1px solid #f0f0f0;
}
.deptbox {
  width: 100%;
  height: 100%;
  padding-left: 10px;
  display: grid;
  gap: 10px;
  grid-template-columns: repeat(auto-fill, 230px);
}
.deptitem {
  min-width: 230px;
  height: 30px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 0 10px;
  margin-bottom: 10px;
  text-align: center;
  line-height: 30px;
  position: relative;
}
.deptname {
  margin-right: 20px;
}
.deptitem .el-icon-delete {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #c0c4cc;
}
.approverbox {
  display: flex;
  flex-direction: column;
}
.approver-item {
  display: flex;
  align-items: center;
  position: relative;
}
.delete-icon {
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px;
  color: #c0c4cc;
}
.delete-approver-icon {
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px;
  color: #c0c4cc;
  transition: color 0.3s;
}
.delete-approver-icon:hover {
  color: #f56c6c;
}
.add-approver {
  margin-top: 15px;
}
</style>
