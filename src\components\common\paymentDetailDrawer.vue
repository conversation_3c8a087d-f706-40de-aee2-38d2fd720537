<template>
  <el-drawer
    :append-to-body="true"
    center
    class="mydrawer"
    title="回款详情"
    size="50%"
    :before-close="beforeClose"
    :visible.sync="drawer"
    direction="rtl"
  >
    <div class="concss">
      <el-form class="infoform" ref="form" label-width="120px">
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="回款金额：" class="labeltext">
              <span>{{ form.returnAmount }}万元</span>
            </el-form-item>
            <el-form-item label="实际回款日期：" class="labeltext">
              <span>{{ form.actualReturnDay }}</span>
            </el-form-item>
            <el-form-item label="是否预收：" class="labeltext">
              <span>{{ form.isAdvance == 1 ? '是' : '否' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="币种：" class="labeltext">
              <span>{{ form.currencyName }}</span>
            </el-form-item>
            <el-form-item label="付款方式：" class="labeltext">
              <span>{{ paymentWays[form.paymentWay] }}</span>
            </el-form-item>
            <el-form-item label="回款日期：" class="labeltext">
              <span>{{ form.examTime || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="收款账户：" class="labeltext">
              <span>{{ form.collectionAccount }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="交易流水号：" class="labeltext">
              <span>{{ form.transactionNumber }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="申请人：" class="labeltext">
              <span>{{ form.operator }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="申请时间：" class="labeltext">
              <span>{{ form.createTime }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="checkpeople">
          <span class="spanname">审核人：</span>
          <eltimeline :businessId="businessId" :status="status" v-if="drawer"></eltimeline>
        </div>
      </el-form>
    </div>
  </el-drawer>
</template>

<script>
import { contractreturnInfo, selectReturnOrRefund } from '@/api/contract/index'
import { getDict } from '@/utils/tools'
import eltimeline from '@/components/common/eltimeline.vue'
export default {
  components: {
    eltimeline,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      paymentWays: {
        1: '现金',
        2: '银行转账',
        3: '微信转账',
        4: '支付宝转账',
        5: '其他',
      },
      typeToName: {
        1: '是',
        0: '否',
      },
      form: {
        id: '',
        contractId: '',
        refundAmount: '',
        currency: '',
        paymentWay: '',
        actualReturnDay: '',
        refundWay: '',
        refundAccount: '',
        collectionAccount: '',
        transactionNumber: '',
        refundReason: '',
        createTime: '',
        isAdvance: '',
      },
      businessId: '',
      status:''
    }
  },
  computed: {
    drawer: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('updateVisible', val)
      },
    },
  },
  created() {
    getDict('Currency')
      .then((result) => {
        this.currencys = result
      })
      .catch((err) => {})
  },
  methods: {
    loadAmount(id) {
      selectReturnOrRefund({ id: id })
        .then((result) => {
          console.log('dddd', result)
          this.noInvoicingTotalAmount = result.data.noInvoicingTotalAmount
        })
        .catch((err) => {})
    },
    beforeClose() {
      this.drawer = false
      var keys = Object.keys(this.form)
      keys.forEach((element) => {
        this.form[element] = ''
      })
    },
    async loadData(id,status) {
      this.businessId = id
      this.status = status
      let res
      res = await contractreturnInfo(id)
      if (res.status == 0) {
        this.form = res.data
      }
    },
  },
}
</script>

<style scoped>
.spanname {
  width: 110px;
  padding-right: 8px;
  text-align: right;
  flex-shrink: 0;
}
.checkpeople {
  display: flex;
  margin-left: 20px;
}
.pb12 {
  padding-bottom: 12px;
}
.lh /deep/.el-form-item__content {
  line-height: 18px !important;
  padding: 0;
  padding-top: 12px;
}
.infoform /deep/.el-form-item {
  margin-bottom: 0px;
}
.concss {
  padding: 0px 20px;
  padding-top: 30px;
}
.mydrawer /deep/.el-drawer__header {
  text-align: center;
  color: #333333;
}
</style>
