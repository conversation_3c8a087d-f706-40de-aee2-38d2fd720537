<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="30%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="100px">
      <el-form-item label="父级结构名称" v-if="dataForm.parentId!=0">
        <el-input v-model="dataForm.parentName" disabled></el-input>
      </el-form-item>
      <el-form-item label="结构名称" prop="name">
        <el-input  v-model="dataForm.name" placeholder="请输入结构名称"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>

import {unitSave,unitUpdate} from '@/api/unit'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        parentId: '',
        name: ''
      },
      dataRule: {
        name: [
          { required: true, message: '结构名称不能为空', trigger: 'blur' }
        ]
      },
      unitId:'',
      dialogVisible:false
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    cancle(){
      this.dialogVisible = false
    },
    init ({id = '',parentId = 0,parentName = '',unitId,name=''} = {}) {
      this.dataForm.id = id
      this.dataForm.parentId = parentId
      this.dataForm.parentName = parentName
      this.dataForm.name = name
      this.unitId = unitId
      this.dialogVisible = true
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          var params = {
            'id': this.dataForm.id || undefined,
            'parentId': this.dataForm.parentId,
            'name': this.dataForm.name,
            "unitId":this.unitId
          }
          let res
          if (!this.dataForm.id) {
            res  = await unitSave(params)
          } else {
            res  = await unitUpdate(params)
          }
          if (res.status === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
            })
            this.dialogVisible = false
            this.$emit('refreshTagTypeTree')
          } else {
            this.$message.error(res.msg)
          }
        }
      })
    }
  }
}
</script>
