<template>
  <div>
    <el-card v-loading="isLoading" class="mt10">
      <el-form label-width="85px" class="myform" inline>
            <el-form-item label="" label-width="0px">
              <el-input
                class="definput"
                v-model="pageBean.materialName"
                clearable
                placeholder="请输入教材名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="用书专业：">
              <el-select
                class="definput"
                popper-class="removescrollbar"
                clearable
                v-model="pageBean.useBookSpecialty"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in majorList"
                  :key="item.id"
                  :label="item.specialtyName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="用书时间：">
              <el-input
                class="definput cpdding"
                v-model="pageBean.useBookYear"
                clearable
                placeholder="例:2023年春季"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
              class="defaultbtn1 mt"
              icon="el-icon-search"
              type="primary"
              @click="searchAction"
              >搜索</el-button
            >
            </el-form-item>

      </el-form>
      <el-table
        class="customertable mytable tootiptable"
        height="590px"
        :data="tableData"
      >
        <el-table-column
          prop="materialName"
          label="教材名称"
          align="center"
          width="167px"
        >
          <template slot-scope="scope">
            <span class="column_blue">{{ scope.row.materialName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isbn" label="ISBN" align="center" width="167">
        </el-table-column>
        <el-table-column prop="platformName" label="出版社" align="center" width="167">
        </el-table-column>
        <el-table-column
          prop="specialtyName"
          align="center"
          label="用书专业"
          width="167px"
        >
        </el-table-column>
        <el-table-column
          prop="customerName"
          align="center"
          label="用书客户"
          width="167px"
        >
        </el-table-column>
        <el-table-column
          prop="useBookYear"
          label="用书时间"
          align="center"
          width="167px"
        >
        </el-table-column>

        <el-table-column prop="price" label="教材定价" align="center" width="167">
        </el-table-column>


        <el-table-column prop="reserveNum" label="预定数量" align="center" width="167">
        </el-table-column>

        <el-table-column prop="reserveRetailPrice" label="报订码洋(万元)" align="center" width="167">
        </el-table-column>

        <el-table-column prop="actualNum" label="实报数量" align="center" width="167">
        </el-table-column>

        <el-table-column prop="discount" label="折扣" align="center" width="167">
        </el-table-column>

        <el-table-column prop="actualRetailPrice"  label="实际码洋(万元)" align="center" width="167">
        </el-table-column>

        <el-table-column prop="completionRate" label="完成率" align="center" width="100">
          <template slot-scope="scope">
            {{ scope.row.completionRate || '--'}}
          </template>
        </el-table-column>
        <el-table-column prop="createName" label="报订人" align="center" width='167'>
        </el-table-column>

        <el-table-column prop="bidwinnerName" label="中标公司" align="center" width='167'>
        </el-table-column>

        <el-table-column prop="sourceGoods" label="货源" align="center" width='167'>
        </el-table-column>

        <el-table-column prop="createTime" label="报订时间" align="center" width='167'>
        </el-table-column >
        <el-table-column
          prop="edit"
          width="250"
          align="center"
          fixed="right"
          label="更多操作"
        >
          <template slot-scope="scope">
            <el-button
              class="bbtn"
              v-isShow="'crm:business:customerbook:list'"
              type="text"
              @click="toDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <nolist></nolist>
        </template>
      </el-table>
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </el-card>
    <subscribeDetailDrawer
    ref="subdetail"
    v-model="isShow">

    </subscribeDetailDrawer>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import back from '../../common/back.vue'
import {
  baodinglist,
 } from '@/api/baoding'
import {
  selectSpecialty,
  customerbookList,
  customerbookSave,
  customerbookUpdate,
  customerbookInfo,
  customerbookDelete,
} from '@/api/clientmanagement/customer'
import { selectUintSpecialty, customerbook } from '@/api/clientmanagement/unit'
import { getDict } from '@/utils/tools'
import subscribeDetailDrawer  from '@/components/subscribe/subscribeDetailDrawer.vue'
export default {
  components: {
    back,
    page,
    nolist,
    subscribeDetailDrawer
  },
  data() {
    return {
      typeObj: {
        1: '过往合作',
        2: '非合作',
      },
      isCoo: [
        {
          id: 1,
          name: '过往合作',
        },
        {
          id: 2,
          name: '非合作',
        },
      ],
      isShow:false,
      employeeDialogVisible: false,
      customerId: this.$route.query.id,
      bookDialogVisible: false,
      dialogFormVisible: false,
      dialogInfoVisible: false,
      dialogTitle: '',
      dialogVisible: false,
      dialogType: '1',
      deleteCustomerId: '',
      isShowCancel: true,
      typename: '',
      notes: '',
      levels: [],
      majorList: [],
      isLoading: false,
      tableData: [],
      bookInfoForm: {
        unitId: this.$route.query.id,
        materialId: '',
        bookNumber: '',
        useBookYear: '',
        useBookSpecialty: '',
        note: '',
        isJoin: undefined,
        operationId: [],
        employeeNames: '',
      },
      useBookYear: '',
      radio: '春季',
      total: 0,
      deletemsg: '',
      pageBean: {
        materialName: '',
        useBookYear: '',
        userBookSpecialty: '',
        status:3,
        pageNum: 1,
        pageSize: 10,
      },
      rules: {
        materialId: [
          { required: true, message: '请选择教材', trigger: 'change' },
        ],
        isJoin: [{ required: true, message: '请选择类型', trigger: 'change' }],
        bookNumber: [
          { required: true, message: '请输入教材用量', trigger: 'blur' },
        ],
        useBookSpecialty: [
          { required: true, message: '请选择用书专业', trigger: 'change' },
        ],
        useBookYear: [
          { required: true, message: '请选择用书时间', trigger: 'change' },
        ],
        operationId: [
          { required: true, message: '请选择业务经理', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.pageBean.unitId = this.$route.query.id
    this.loadData()
    this.selectUintSpecialtyApi()
  },
  methods: {
    selectUintSpecialtyApi() {
      let params = {
        unitId: this.$route.query.id,
      }
      selectUintSpecialty(params).then((res) => {
        if (res.status == 0) {
          this.majorList = res.data
        }
      })
    },
    chooseEmployee() {
      this.employeeDialogVisible = true
      this.$nextTick(() => {
        this.$refs.employeeDialog.loadData()
      })
    },
    updateEmployeeVisible(val) {
      this.employeeDialogVisible = val
    },
    submitEmployeeData(data) {
      this.bookInfoForm.operationId = data.map((item) => item.id)
      this.bookInfoForm.employeeNames = data.map((item) => item.name).join(', ')
      this.$refs.addform.validateField('operationId')
      this.employeeDialogVisible = false
    },
    loadSepcialty() {
      selectSpecialty({
        customerId: this.customerId,
      })
        .then((result) => {
          this.majorList = result.data
        })
        .catch((err) => {})
    },
    changePicker(val) {
      if (val) {
        this.bookInfoForm.useBookYear = val
      } else {
        this.bookInfoForm.useBookYear = ''
      }
    },
    loadData() {
      this.isLoading = true
      baodinglist(this.pageBean)
        .then((result) => {
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    changeOption(data) {
      if (data == '1') {
        this.pageBean.chargePerson = window.sessionStorage.getItem('userid')
        this.pageBean.collaborator = ''
      } else if (data == '2') {
        this.pageBean.chargePerson = ''
        this.pageBean.collaborator = window.sessionStorage.getItem('userid')
      } else {
        this.pageBean.chargePerson = ''
        this.pageBean.collaborator = ''
      }
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    submitDialog() {
      this.dialogVisible = false
      if (this.dialogType == 1) {
        this.deleteData()
      }
    },
    deleteData() {
      customerbookDelete({ id: this.deleteCustomerId })
        .then((result) => {
          if (result.data) {
            this.loadData()
            this.$message({
              type: 'success',
              message: '移除成功',
            })
          } else {
            this.dialogType = 2
            this.isShowCancel = false
            this.deletemsg = result.msg
            this.dialogVisible = true
          }
        })
        .catch((err) => {})
    },

    addInfo() {
      this.dialogFormVisible = true
      this.dialogTitle = '新增用书信息'
    },
    editInfo(data) {
      this.dialogFormVisible = true
      this.dialogTitle = '编辑用书信息'
      this.loadInfoById(data.id)
    },
    loadInfoById(id) {
      customerbookInfo(id)
        .then((result) => {
          this.bookInfoForm = result.data
          var useBookYear = this.bookInfoForm.useBookYear
          var index = useBookYear.indexOf('年')
          this.useBookYear = useBookYear.substring(0, index + 1)
          this.radio = useBookYear.substring(index + 1, useBookYear.length)
        })
        .catch((err) => {})
    },
    deleteAction(data) {
      this.dialogType = 1
      this.isShowCancel = true
      this.dialogVisible = true
      this.deletemsg = '是否删除该用书信息？'
      this.deleteCustomerId = data.id
    },
    lookInfo(data) {
      this.dialogInfoVisible = true
      this.notes = data.note
    },
    onEdit(data) {
      this.$router.push({
        path: '/clientManagement/customer/add',
        query: {
          id: data.id,
        },
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    handleClose() {
      this.dialogFormVisible = false
      this.dialogTitle = ''
      Object.keys(this.bookInfoForm).forEach((element) => {
        if (element != 'customerId') {
          this.bookInfoForm[element] = ''
        }
      })
      this.bookInfoForm.operationId = []
      this.bookInfoForm.employeeNames = ''
      this.useBookYear = ''
      this.radio = '春季'
      this.$nextTick(() => {
        this.$refs.addform.clearValidate()
      })
    },
    submitAction() {
      if (this.useBookYear && this.radio) {
        this.bookInfoForm.useBookYear = `${this.useBookYear}${this.radio}`
      }
      this.$refs['addform'].validate((valid) => {
        if (valid) {
          delete this.bookInfoForm.employeeNames
          if (this.bookInfoForm.id) {
            this.update()
          } else {
            this.add()
          }
        } else {
          return false
        }
      })
    },
    add() {
      customerbookSave(this.bookInfoForm)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功',
            })
            this.loadData()
            this.handleClose()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    update() {
      customerbookUpdate(this.bookInfoForm)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '更新成功',
            })
            this.loadData()
            this.handleClose()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    chooseBook() {
      this.bookDialogVisible = true
      this.$nextTick(() => {
        this.$refs.book.loadBookData({
          customerId: this.customerId,
          type: 2,
        })
      })
    },
    updateVisible(val) {
      this.bookDialogVisible = val
    },
    updateData(data) {
      ;(this.bookInfoForm.materialId = data.id),
        (this.bookInfoForm.materialName = data.name)
      this.$refs.addform.validateField('materialId')
    },
    toDetail(data){
      this.isShow = true;
      this.$nextTick(()=>{
        this.$refs.subdetail.loadinfo(data.id)
      })
    },
  },
}
</script>
<style>
.tootiptable .el-tooltip {
  text-align: left;
}

.cpdding .el-input__inner {
  padding-right: 0;
}
</style>

<style scoped>
.mr10 {
  margin-right: 10px !important;
}

.pdl {
  padding-left: 10px;
}

.defaultbtn1 {
  padding: 9px 10px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}

.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}

.namecss {
  display: flex;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  min-width: 30px !important;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  margin-top: -4px;
  vertical-align: middle;
  position: relative;
  top: 21px;
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
}

.column_blue {
  color: #4285f4;
}

.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}

.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}

.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.tagcss:nth-child(3n) {
  margin-top: 4px;
}

.customertable /deep/.cell {
}

.customertable .el-button {
  padding: 2px;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}
</style>
