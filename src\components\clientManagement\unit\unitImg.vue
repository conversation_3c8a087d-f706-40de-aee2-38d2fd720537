<template>
  <div class="center">
    <img v-if="unitImg" class="jiegou" :src="unitImg" alt="">
    <nolist v-else></nolist>
  </div>
</template>

<script>
import nolist from '../../common/nolist.vue';
import { mapGetters } from 'vuex'
  export default {
    components:{
      nolist
    },
    data(){
      return{
        url:""
      }
    },
    computed:{
      ...mapGetters(['unitImg'])
    }
  }
</script>

<style lang="scss" scoped>
.jiegou{
  margin: 20px;
  padding: 0px 50px;
  max-width: 100%;
  height: auto;
  margin-top: 0px;
  object-fit: contain;
  // background-color: red;
}
.center{
  text-align:  center;
}
</style>