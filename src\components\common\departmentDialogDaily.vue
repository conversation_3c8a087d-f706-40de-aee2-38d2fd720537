<template>
  <div  id="elDialog">
    <el-dialog
    @click.native.stop
    :title="dType==1?'选择部门':'选择员工'"
    :visible.sync="show"
    :width="dType==1?'800px':'800px'"
    center
    class="unittree"
    @close="closeAction"
    >
    <div class="condiv">
      <div class="ldiv" :class="{fixwidth:dType==3 || dType==1}">
        <span class="spantext">部门</span>
        <el-tree
          :default-checked-keys="elTreeDataIds"
          @check-change="treeChange"
          :check-strictly="true"
          ref="treeRef"
          :data="data"
          :show-checkbox="dType==3 || dType==1"
          class="ptop"
          node-key="id"
          default-expand-all
          :props="defaultProps"
          @node-click="nodeClick">
        </el-tree>
      </div>
      <div class="line" v-if="dType==2 || dType==3 || dType==1"></div>
      <div class="rdiv" v-if="dType==2 || dType==3 || dType==1">
        <span class="spantext">
          员工
        </span>
        <el-checkbox-group  class="workercheckbox"  v-model="checkedWorkers" >
          <el-checkbox class="boxitem" v-for="item in workers" :label="item.id" :key="item.id" @change=" e =>handleCheckedWorkersChange(e,item)">{{item.name}}</el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click.stop="saveAction">确 定</el-button>
    </span>
  </el-dialog>
  </div>
 
</template>

<script>
import { listDept,listSatff } from '@/api/framework/dept'
import { removeById,contains,removeArrById } from '@/utils/tools.js'
import { deepClone} from '@/utils/tools.js'
export default {
    props:{
      dialogDepartmentVisible:{
          type:Boolean,
          default:false,
      },
      dType:{
        type:String,
        default:'1'
      },
      isDaily:{
        type:Boolean,
        default:false
      },
    },
    data(){
        return{
            show:false,
            workers:[],
            checkedWorkers:[],
            data: [],
            defaultProps: {
              children: 'children',
              label: 'name'
            },
            submitData:[],
            peopleData:[],
            elTreeDataIds:[],
            dailyPeople:[],
        }
    },
    mounted() {
        this.show = this.dialogDepartmentVisible
    },
    methods:{
        loadData(){
          listDept().then((result) => {
            this.data = result.data;
          })
        },
        updateWorksId(data){
            if (this.dType == 3) {
              this.submitData = data
              let selList = data;
              selList.forEach(element => {
                if(element.allowType == 1){
                  this.elTreeDataIds.push(element.id);
                }else if(element.allowType == 2){
                  this.checkedWorkers.push(element.id);
                }
              });
            }else if(this.dType == 2){
              this.peopleData = deepClone(data)
              let selList = data;
              selList.forEach(element => {
                this.checkedWorkers.push(element.id);
              });
            }
        },
        nodeClick(data){
              listSatff({departmentId:data.id}).then((result) => {
                this.workers = result.data
              })
        },
        handleData(boolean ,data,allowType){
          if(boolean){
            let obj = {
              id:data.id,
              name:data.name,
              allowType,
            }
            if (this.dType == 3) {
              if(contains(this.submitData,data.id)){
                  return 
              }
              this.submitData.push(obj)
            }else if(this.dType == 2){
              if(contains(this.peopleData,data.id)){
                  return 
              }
              obj.logo = data.logo
              this.peopleData.push(obj)
            }else if(this.dType == 1){
              if(contains(this.dailyPeople,data.id)){
                  return 
              }
              this.dailyPeople.push(obj)
            }
          }else{
            if (this.dType == 3) {
              this.submitData = removeById(this.submitData,data.id)
            }else if(this.dType == 2){
              this.peopleData = removeById(this.peopleData,data.id)
            }else if(this.dType == 1){
              this.dailyPeople = removeById(this.dailyPeople,data.id)
            }
          }
        },
        handleCheckedWorkersChange(checked,data){
            this.handleData(checked,data,2)
        },
        closeAction(e){
          this.show = false
          this.$emit('updateVisible',false)
        },
        treeChange(data,boo){
          if(this.isDaily){
              listSatff({departmentId:data.id}).then((result) => {
                this.workers = result.data
                let selList = this.workers;
                selList.forEach(element => {
                  if(boo){
                    if(contains(this.dailyPeople,element.id)){
                        return 
                    }
                    this.checkedWorkers.push(element.id);
                    this.dailyPeople.push(element)
                  }else{
                    removeArrById(this.checkedWorkers,element.id)
                    this.dailyPeople = removeById(this.dailyPeople,element.id)
                  }
                });
              })
          }else{
            this.handleData(boo,data,1)
          }
        },
        saveAction(){
          if (this.dType == 3) {
            if(this.submitData.length == 0){
                this.msgError('请选择部门或者人员')
                return 
            }
            this.$emit('submitData',this.submitData);
          }else if(this.dType == 2){
            if(this.peopleData.length == 0){
                this.msgError('请选择人员')
                return 
            }
            this.$emit('submitData',this.peopleData);
          }else if(this.dType == 1){
            // console.log(this.dailyPeople)
            this.$emit('submitData',this.dailyPeople);
          }
        }
    }
}
</script>

<style scoped lang="scss">
.unittree /deep/ .el-tree{
  display: inline-block;
  min-width: 100%;
  padding-right: 10px;
}
.ptop{
  padding-top: 10px;
}
.workercheckbox{
  padding-top: 10px;
}
.workercheckbox /deep/.el-checkbox__label{
  line-height: 32px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  min-width: 65px;
}
.unittree /deep/.el-dialog__body{
  height: 600px;
  padding: 0 !important;
}
.unittree /deep/.el-tree-node__label{
  font-size: 12px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 14px;
}
.unittree /deep/.el-tree-node__expand-icon{
  color: #333333;
}
.unittree /deep/.el-tree-node__expand-icon.is-leaf{
  color: transparent;
}
.condiv{
  display: flex;
  height: 100%;
}
.ldiv{
  padding-top: 30px;
  padding-left: 26px;
  padding-right: 26px;
  width: 35%;
  overflow-y: scroll;
  overflow-x: scroll;
}
.fixwidth{
  width: 50%;
  box-sizing: border-box;
}
.line{
  margin-right:30px;
  width: 1px;
  height: 100%;
  background-color: #F0F0F0;
}
.spantext{
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.rdiv{
  width: 50%;
  padding-top: 30px;
  padding-left: 0px;
  overflow-y: scroll;
}

</style>