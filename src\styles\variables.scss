$menuText: #333333;
$menuActiveText: #409EFF;
$subMenuActiveText: #f4f4f5; //https://github.com/ElemeFE/element/issues/12951

$menuBg: #fff;


$menuHover: #DFEAFD;

$subMenuBg: #DFEAFD;
// $subMenuHover: #001528;



$subMenuHover: #DFEAFD;
$sideBarWidth: 170px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}