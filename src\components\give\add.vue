<template>
  <div>
    <div>
      <back>{{ form.id ? '编辑' : '新建' }}</back>
    </div>
    <div class="mainbg">
      <el-form
        ref="addform"
        class="addfcss"
        :model="form"
        :rules="rules"
        label-width="118px"
      >
        <textBorder>基础信息</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="10">
              <el-form-item label="事由：" prop="reason">
                <el-input
                  class="definput"
                  v-model="form.reason"
                  placeholder="请输入事由"
                  maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item required label="客户：" prop="customerId">
                <div class="unitbtn" @click="chooseCustomer">
                  <span v-if="form.customerId" class="deffont">{{
                    form.customerName
                  }}</span>
                  <span v-else class="pltcss">请选择</span>
                  <i class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
              <el-form-item label="时间：" prop="distributionTime">
                <el-date-picker
                  class="definput width100"
                  v-model="form.distributionTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择时间"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                v-if="$route.query.type != 3"
                label="回访提醒："
                prop="revisitTime"
              >
                <el-date-picker
                  class="definput width100"
                  v-model="form.revisitTime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  type="datetime"
                  :picker-options="{ disabledDate: disabledDate }"
                  placeholder="选择回访提醒日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                v-if="$route.query.type != 3"
                label="发放方式："
                prop="distributionForm"
              >
                <el-select
                  class="definput"
                  popper-class="removescrollbar"
                  v-model="form.distributionForm"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in giveTypes"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="$route.query.type == 3"
                label="金额："
                prop="price"
              >
                <el-input-number
                  class="definput width100 textleft"
                  type="number"
                  :controls="false"
                  v-model="form.price"
                  :min="0"
                  :precision="2"
                  placeholder="请输入金额"
                ></el-input-number>
              </el-form-item>

              <el-form-item label="业务经理：" prop="operationId">
                <el-select
                  class="definput"
                  popper-class="removescrollbar"
                  v-model="form.operationId"
                  placeholder="请先选择客户"
                >
                  <el-option
                    v-for="item in peopleList"
                    :key="item.operationId + '_' + item.departmentId"
                    :label="item.operationName + '-' + item.departmentName"
                    :value="item.operationId + '_' + item.departmentId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="$route.query.type == 1"
                prop="useBookYear"
                label="用书时间："
              >
                <el-date-picker
                  class="definput wid100"
                  v-model="useBookYear"
                  type="year"
                  format="yyyy"
                  value-format="yyyy年"
                  placeholder="选择年"
                  @change="changeUseBookYear"
                >
                </el-date-picker>
                <span class="pdl">
                  <el-radio class="mr10" v-model="radio" label="春季"
                    >春季</el-radio
                  >
                  <el-radio class="mr10" v-model="radio" label="秋季"
                    >秋季</el-radio
                  >
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder v-if="$route.query.type != 3" class="mt30">{{
          types[$route.query.type]
        }}</textBorder>
        <div v-show="$route.query.type == 1" class="pt20 bbline">
          <el-table class="mytable" :data="bookList">
            <el-table-column
              label="教材名称"
              width="200px"
              prop="name"
              align="center"
            >
              <template slot-scope="scope">
                <div class="flex">
                  <div class="unitbtn2">
                    <span v-if="scope.row.id" class="deffont">{{
                      scope.row.name
                    }}</span>
                  </div>
                  <span
                    class="pltcss2"
                    @click="chooseBook(scope.$index, scope.row)"
                    >选择</span
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="用书专业"
              width="150px"
              prop=""
              align="center"
            >
              <template slot-scope="scope">
                <el-select
                  size="mini"
                  popper-class="removescrollbar"
                  v-model="scope.row.bookSpecialty"
                  :placeholder="form.customerId ? '请选择' : '请先选择客户'"
                >
                  <el-option
                    v-for="item in majorList"
                    :key="item.id"
                    :label="item.specialtyName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              label="isbn"
              width="150px"
              prop="isbn"
              align="center"
            ></el-table-column>
            <el-table-column
              label="主编"
              width="150px"
              prop="author"
              align="center"
            ></el-table-column>
            <el-table-column
              label="出版社"
              width="150px"
              prop="platformName"
              align="center"
            ></el-table-column>
            <el-table-column
              label="出版时间"
              width="150px"
              prop="publicationRevisionTime"
              align="center"
            ></el-table-column>
            <el-table-column
              label="价格"
              width="150px"
              fixed="right"
              prop="price"
              align="center"
            ></el-table-column>
            <el-table-column
              label="数量"
              width="150px"
              fixed="right"
              prop="number"
              align="center"
            >
              <template slot-scope="scope">
                <el-input-number
                  :controls="false"
                  :precision="0"
                  :step="1"
                  :min="1"
                  size="mini"
                  placeholder="设置数量"
                  v-model="scope.row.number"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column
              label="总码洋"
              width="150px"
              fixed="right"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{
                  scope.row.number &&
                  scope.row.price &&
                  (scope.row.price * scope.row.number).toFixed(2)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="150px"
              fixed="right"
              align="center"
            >
              <template slot-scope="scope">
                <span class="rbtn" @click="delBook(scope.$index, scope.row)"
                  >删除</span
                >
              </template>
            </el-table-column>
          </el-table>
          <div class="bottomView">
            <el-button type="text" icon="el-icon-plus" @click="addBook"
              >添加教材</el-button
            >
          </div>
        </div>
        <div v-show="$route.query.type == 2" class="pt20 bbline">
          <el-table class="mytable" :data="giftList">
            <el-table-column
              label="礼品名称"
              width="200px"
              prop="name"
              align="center"
            >
              <template slot-scope="scope">
                <div class="flex">
                  <div class="unitbtn2">
                    <span v-if="scope.row.id" class="deffont">{{
                      scope.row.name
                    }}</span>
                  </div>
                  <span
                    class="pltcss2"
                    @click="chooseGift(scope.$index, scope.row)"
                    >选择</span
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="价格"
              prop="price"
              align="center"
            ></el-table-column>
            <el-table-column label="数量" prop="number" align="center">
              <template slot-scope="scope">
                <el-input-number
                  :controls="false"
                  :precision="0"
                  :step="1"
                  :min="1"
                  size="mini"
                  placeholder="设置数量"
                  v-model="scope.row.number"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="总金额" align="center">
              <template slot-scope="scope">
                <span>{{
                  scope.row.number &&
                  (scope.row.price * scope.row.number).toFixed(2)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="150px"
              fixed="right"
              align="center"
            >
              <template slot-scope="scope">
                <span class="rbtn" @click="delGift(scope.$index, scope.row)"
                  >删除</span
                >
              </template>
            </el-table-column>
          </el-table>
          <div class="bottomView">
            <el-button type="text" icon="el-icon-plus" @click="addGift"
              >添加礼品</el-button
            >
          </div>
        </div>
        <textBorder class="mt30">补充信息</textBorder>
        <div class="pt20">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="20">
              <el-form-item label="备注：" prop="notes">
                <el-input
                  class="definput"
                  v-model="form.notes"
                  maxlength="300"
                  show-word-limit
                  type="textarea"
                  rows="4"
                  placeholder="请输入备注"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="pt20 btncenter">
          <el-form-item>
            <el-button
              class="btn_h42 wid98"
              type="primary"
              @click="submitForm"
              :loading="isSubmit"
              >保存</el-button
            >
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 关联客户 -->
    <cuatomerDialog
      ref="customer"
      :visible.sync="customerDialogVisible"
      @updateVisible="updateVisible"
      @updateCustomer="updateCustomer"
    >
    </cuatomerDialog>
    <!-- 业务经理 -->
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
    <!-- 选择教材 -->
    <chooseBookDialog
      ref="bookdialog"
      :visible.sync="bookDialogVisible"
      @updateVisible="updateBookVisible"
      @updateData="updateBookData"
      :disableIds="disableIds"
    >
    </chooseBookDialog>
    <chooseGiftDialog
      ref="giftdialog"
      :visible.sync="giftDialogVisible"
      @updateVisible="updateGiftVisible"
      @updateData="updateGiftData"
      :disableIds="disableGiftIds"
      :curSelIds="curSelGiftId"
    ></chooseGiftDialog>
    <!-- 部门验证组件 -->
    <verifyDeparment
      ref="verifyDeparment"
      @submit="addData"
    ></verifyDeparment>
  </div>
</template>

<script>
import back from '@/components/common/back.vue'
import textBorder from '@/components/common/textBorder.vue'
import cuatomerDialog from '@/components/common/customerDialog.vue'
import chooseBookDialog from '@/components/common/chooseBookDialog.vue'
import chooseGiftDialog from '@/components/common/chooseGiftDialog.vue'
import systemDialog from '@/components/common/systemDialog.vue'
import {
  addDistribution,
  updateDistribution,
  distributionInfo,
  queryCustomerChargePersonDepartmentList,
} from '@/api/clientMaintenance/give'
import { selectUintSpecialty } from '@/api/clientmanagement/customer'
import { queryBookInfo } from '@/api/product/index'
import { giveTypes } from '../../utils/dict'
import VerifyDeparment from '../common/verifyDeparment.vue'
export default {
  components: {
    back,
    textBorder,
    cuatomerDialog,
    chooseBookDialog,
    chooseGiftDialog,
    systemDialog,
    VerifyDeparment
  },
  data() {
    var validateUseBookYear = (rule, value, callback) => {
      if (value === '') {
        switch (parseInt(this.$route.query.type)) {
          case 1:
            callback(new Error('请选择用书时间'))
            break
          case 2:
            callback()
            break
          default:
            callback()
            break
        }
      } else {
        callback()
      }
    }
    var validatePrice = (rule, value, callback) => {
      if (!value) {
        console.log(
          'shangshuceshi===>',
          parseInt(this.$route.query.type),
          value
        )
        switch (parseInt(this.$route.query.type)) {
          case 3:
            callback(new Error('请输入金额'))
            break
          default:
            callback()
            break
        }
      } else {
        callback()
      }
    }
    return {
      peopleList: [],
      giveTypes,
      isSubmit: false,
      giftDialogVisible: false,
      popoverVisibble: false,
      bookDialogVisible: false,
      customerDialogVisible: false,
      dialogVisible: false,
      isSmall: window.screen.availWidth < 1500 ? true : false,
      dialogName: '',
      multipleNum: 1,
      inputValue: '',
      form: {
        id: '',
        reason: '',
        type: this.$route.query.type,
        distributionForm: '',
        customerId: '',
        operationId: '',
        distributionDepartmentId: '',
        distributionTime: '',
        revisitTime: '',
        useBookYear: '',
        notes: '',
        distributionDetailList: [],
      },
      types: {
        1: '样书',
        2: '礼品',
        3: '商务',
      },
      rules: {
        reason: [
          { required: true, message: '请输入事由', trigger: 'blur' },
          {
            min: 3,
            max: 50,
            message: '长度在 3 到 50 个字符',
            trigger: 'blur',
          },
        ],
        distributionTime: [
          { required: true, message: '请选择时间', trigger: 'change' },
        ],
        customerId: [
          { required: true, message: '请选择关联客户', trigger: 'change' },
        ],
        operationId: [
          { required: true, message: '请选择业务经理', trigger: 'change' },
        ],
        distributionForm: [
          { required: true, message: '请选择发放类型', trigger: 'change' },
        ],
        price: [{ required: true, validator: validatePrice, trigger: 'blur' }],
        useBookYear: [
          { required: true, validator: validateUseBookYear, trigger: 'change' },
        ],
      },
      showmessage: '请选择',
      majorList: [],
      useBookYear: '',
      radio: '春季',
      bookInfo: {
        name: '',
        isbn: '',
      },
      bookList: [],
      curBookIndex: '',
      curBookRow: {},
      disableIds: [],
      curSelId: '',
      giftList: [],
      curGiftIndex: '',
      curGiftRow: {},
      disableGiftIds: [],
      curSelGiftId: '',
    }
  },
  created() {
    this.$route.query.id ? this.loadData() : null
    ;(this.showmessage == this.form.type) == 1 ? '请选择样书' : '请选择礼品'
    if (
      this.$route.query.customerId &&
      this.$route.query.customerName &&
      this.$route.query.unitId
    ) {
      this.form.customerId = this.$route.query.customerId
      this.form.customerName = this.$route.query.customerName
      this.form.unitId = this.$route.query.unitId
      this.loadMajor()
    }
  },
  methods: {
    disabledDate(time) {
      var date = new Date()
      return (
        time <
        new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)
      )
    },
    loadData() {
      this.isLoading = true
      distributionInfo(this.$route.query.id)
        .then((result) => {
          this.form = result.data
          if (result.data.distributionForm == 0) {
            this.form.distributionForm = ''
          }
          if (this.form.type == 1) {

            if (this.form.distributionDetailList.length>0) {
              this.bookList = this.form.distributionDetailList
            }else if (this.form.goodsId) {
              this.bookList = [{
                goodsId:this.form.goodsId,
                id:this.form.goodsId,
                name:this.form.goodsName,
                isbn:this.form.isbn,
                platformName:this.form.platformName,
                price:this.form.price,
                bookSpecialty:this.form.bookSpecialty,
                number:this.form.useBookNumber,
              }]
            }
          } else if (this.form.type == 2) {
            if (this.form.distributionDetailList.length>0) {
              this.giftList = this.form.distributionDetailList
            }else if (this.form.goodsId) {
              this.giftList = [{
                goodsId:this.form.goodsId,
                id:this.form.goodsId,
                name:this.form.goodsName,
                price:this.form.price,
                number:this.form.useBookNumber,
              }]
            }
          }
          if (this.form.unitId) {
            this.loadMajor()
          }
          var useBookYear = this.form.useBookYear
          if (useBookYear) {
            var index = useBookYear.indexOf('年')
            this.useBookYear = useBookYear.substring(0, index + 1)
            this.radio = useBookYear.substring(index + 1, useBookYear.length)
          }
          this.isLoading = false
          this.form.operationId = this.form.operationId + "_" + this.form.distributionDepartmentId
          this.loadJing()
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    loadMajor() {
      selectUintSpecialty({
        unitId: this.form.unitId,
      })
        .then((result) => {
          this.majorList = result.data
        })
        .catch((err) => {})
    },
    loadJing() {
      let paramsObj = {
        customerId: this.form.customerId,
        methodName: 'list',
        className: 'DistributionController',
      }
      queryCustomerChargePersonDepartmentList(paramsObj).then((res) => {
        if (res.status == 0) {
          this.peopleList = res.data
        }
      })
    },
    updateCustomer(data) {
      if (this.form.unitId != data.unitId && this.form.type == 1) {
        this.bookList.forEach((element) => {
          element.bookSpecialty = ''
        })
      }
      this.form.customerName = data.customerName
      this.form.customerId = data.id
      this.form.unitId = data.unitId
      this.form.unitName = data.unitName
      this.$refs.addform.validateField('customerId')
      if (this.form.unitId) {
        this.loadMajor()
      }
      this.loadJing()
    },
    chooseCustomer() {
      if (this.$route.query.customerId && this.$route.query.customerName) {
        return
      }
      this.customerDialogVisible = true
      this.$refs.customer.selectCustomerData({
        id: this.form.customerId,
        className: 'DistributionController',
      })
    },
    updateVisible(val) {
      this.customerDialogVisible = val
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    //  获取选中的业务经理
    submitData(data, type, departmentId) {
      this.form.operationId = data.length > 0 ? data[0].id : ''
      this.form.operationName = data.length > 0 ? data[0].name : ''
      this.form.distributionDepartmentId = departmentId
      this.$refs.addform.validateField('operationId')
      this.updateSystemVisible(false)
    },
    findItemById(items, operationId) {
      return items.find((item) => item.operationId === operationId)
    },
    // 提交表单
    submitForm() {
      this.$refs['addform'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.radio && this.useBookYear) {
          this.form.useBookYear = `${this.useBookYear}${this.radio}`
        }
        const arr = this.form.operationId.split('_')
        this.form.operationId = arr[0]
        this.form.distributionDepartmentId = arr[1]

        if (this.$route.query.type == 1) {
          if (!this.validateBookList()) {
            return
          }
          this.form.distributionDetailList = this.getBookList()
        }
        if (this.$route.query.type == 2) {
          if (!this.validateGiftList()) {
            return
          }
          this.form.distributionDetailList = this.getGiftList()
        }
        // if (this.form.operationId) {
        //   let params = this.findItemById(this.peopleList, this.form.operationId)
        //   this.form.operationId = params.operationId
        //   this.form.operationName = params.operationName
        //   this.form.distributionDepartmentId = params.departmentId
        // }
        if (this.form.id) {
          // 编辑
          this.updateData()
        } else {
          // 验证部门
          this.$refs.verifyDeparment.verify()
        }
      })
    },
    /**
     * goodsId  教材或礼品id
          type 发放类型（1：教材；2：物品）
          name  教材名称/物品名称
          price
          number
     */
    getBookList() {
      var list = []
      this.bookList.forEach((item) => {
        list.push({
          goodsId: item.goodsId,
          name: item.name,
          price: item.price,
          number: item.number,
          bookSpecialty: item.bookSpecialty,
        })
      })
      return list
    },
    getGiftList() {
      var list = []
      this.giftList.forEach((item) => {
        list.push({
          goodsId: item.goodsId,
          name: item.name,
          price: item.price,
          number: item.number,
        })
      })
      return list
    },
    validateBookList() {
      var isValidate = true
      if (this.bookList.length == 0) {
        isValidate = false
        this.$message.error(`请完善教材信息`)
      } else {
        for (let index = 0; index < this.bookList.length; index++) {
          const element = this.bookList[index]
          if (!element.id) {
            this.$message.error(`请完善第${index + 1}行的教材信息`)
            isValidate = false
            return
          }
          if (!element.bookSpecialty) {
            this.$message.error(`${element.name}的用书专业为空，请设置`)
            isValidate = false
            return
          }
          if (!element.number) {
            this.$message.error(`${element.name}的数量为空，请设置`)
            isValidate = false
            return
          }
        }
      }

      return isValidate
    },
    validateGiftList() {
      var isValidate = true
      if (this.giftList.length == 0) {
        isValidate = false
        this.$message.error(`请完善礼品信息`)
      } else {
        for (let index = 0; index < this.giftList.length; index++) {
          const element = this.giftList[index]
          if (!element.id) {
            this.$message.error(`请完善第${index + 1}行的礼品信息`)
            isValidate = false
            return
          }
          if (!element.number) {
            this.$message.error(`${element.name}的数量为空，请设置`)
            isValidate = false
            return
          }
        }
      }
      return isValidate
    },
    updateData() {
      this.isSubmit = true
      updateDistribution(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '更新成功！',
            })
            this.$router.back()
          } else {
            this.$message({
              type: 'error',
              message: '保存失败！',
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    addData(departmentId) {
      this.form.createDepartmentId = departmentId;
      this.isSubmit = true
      addDistribution(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功！',
            })
            this.$router.back()
          } else {
            this.$message({
              type: 'error',
              message: '保存失败！',
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    addBook() {
      if (this.bookList.length>=100) {
        this.$message.error('最多选择100种教材')
        return
      }0
      this.bookList.push({
        goodsId: '',
        name: '',
        price: '',
        number: undefined,
        bookSpecialty: '',
      })
    },
    delBook(index, data) {
      this.bookList.splice(index, 1)
    },
    chooseBook(index, row) {
      this.curSelId = row.goodsId || ''
      this.disableIds = []
      this.bookList.forEach((element) => {
        if (element.goodsId && element.goodsId != this.curSelId) {
          this.disableIds.push(element.goodsId)
        }
      })
      this.curBookIndex = index
      this.curBookRow = row
      this.bookDialogVisible = true
      this.$nextTick(() => {
        this.$refs.bookdialog.loadBookData(
          {
            // opportunityId: this.form.id ? this.form.id : "",
            type: 2,
          },
          this.curSelId
        )
      })
    },
    updateBookVisible(val) {
      this.bookDialogVisible = val
    },
    updateBookData(data) {
      if (data) {
        data.goodsId = data.id
        this.curBookRow = Object.assign(this.curBookRow, data)
        this.bookList.splice(this.curBookIndex, 1, this.curBookRow)
      } else {
        this.curBookRow = {}
        this.bookList.splice(this.curBookIndex, 1, this.curBookRow)
      }
    },
    lookBookInfo() {
      this.popoverVisibble = true
      queryBookInfo(this.form.goodsId)
        .then((result) => {
          this.bookInfo = result.data
        })
        .catch((err) => {})
    },
    changeUseBookYear(data) {
      if (this.radio && this.useBookYear) {
        this.form.useBookYear = `${this.useBookYear}${this.radio}`
      }
    },
    addGift() {
      if (this.giftList.length>=100) {
        this.$message.error('最多选择100种礼品')
        return
      }
      this.giftList.push({
        goodsId: '',
        name: '',
        price: '',
        number: undefined,
      })
    },
    delGift(index, data) {
      this.giftList.splice(index, 1)
    },
    chooseGift(index, row) {
      this.curSelGiftId = row.goodsId || ''
      this.disableGiftIds = []
      this.giftList.forEach((element) => {
        if (element.goodsId && element.goodsId != this.curSelGiftId) {
          this.disableGiftIds.push(element.goodsId)
        }
      })
      this.curGiftIndex = index
      this.curGiftRow = row
      this.giftDialogVisible = true
      this.$nextTick(() => {
        this.$refs.giftdialog.loadData({})
      })
    },
    updateGiftVisible(val) {
      this.giftDialogVisible = val
    },
    updateGiftData(data) {
      if (data) {
        data.goodsId = data.id
        this.curGiftRow = Object.assign(this.curGiftRow, data)
        this.giftList.splice(this.curGiftIndex, 1, this.curGiftRow)
      } else {
        this.curGiftRow = {}
        this.giftList.splice(this.curGiftIndex, 1, this.curGiftRow)
      }
    },
  },
}
</script>
<style scoped>
.rbtn {
  cursor: pointer;
}
.bottomView {
  text-align: center;
  padding-bottom: 10px;
}
.wid100 {
  width: calc(100% - 150px);
  margin-right: 10px;
}
.wid2 {
  width: calc(100% - 100px);
}
.wid {
  width: 100px;
}
.pdl {
  margin-left: 10px;
}
.pltcss {
  color: #cdcdcd;
  font-size: 13px;
}
.unitbtn {
  width: 100%;
  color: #333;
  position: relative;
}
.unitbtn2 {
  width: calc(100% - 50px);
  background-color: white;
  color: #333;
  position: relative;
  border: 1px solid #dcdfed;
  border-radius: 4px;
  line-height: 26px;
  height: 28px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.pltcss2 {
  color: #4285f4;
  font-size: 13px;
  line-height: 28px;
  cursor: pointer;
}
.width100 {
  width: 100%;
}
.wid98 {
  width: 98px;
}
.uploadtext {
  color: #4285f4;
  cursor: pointer;
}
.uploadcss {
  width: 88px;
  height: 88px;
  line-height: 24px;
  background: #f5f5f5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #cccccc;
}
.uploadcss i {
  margin-top: 20px;
  font-size: 24px;
  color: #4285f4;
}
.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}
.btncenter {
  text-align: center;
}
.quanxiancss /deep/.el-form-item__label {
  margin-left: -7px;
  width: 125px !important;
}
.mainbg {
  margin: 16px 0px;
  padding: 20px 16px;
  background-color: white;
  border-radius: 8px;
}
.pd0 {
  padding-right: 0px !important;
}
.flex {
  display: flex;
}
.flex span {
  margin-left: 10px;
}
.tr {
  position: absolute;
  top: 10px;
  right: 10px;
}
.textleft /deep/.el-input__inner {
  text-align: left !important;
}
</style>
