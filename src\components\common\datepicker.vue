<template>
    <div>
        <el-popover
        placement="bottom"
        title="时间筛选"
        width="400"
        trigger="manual"
        v-model="visible"
        
        >
        <div class="clearfix">
            <div class="flexdiv">
                <div class="btnitem" :class="{'seltext':selitemIndex == item.id}" @click="clickItem(item.id)" v-for="item in types" :key="item.id">
                    {{item.value}}
                </div>
            </div>
            <div v-if="selitemIndex == 11">
                <el-date-picker
                class="definput dateRWidth"
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="changeDateRange">
                </el-date-picker>
            </div>
            <el-button class="defaultbtn mt right"  @click="submitAction" type="primary">确认</el-button>
        </div>
            
            <div class="deftimebtn"  slot="reference" @click="showPopver">
                <div v-if=" selitemIndex == ''" >
                    请选择
                </div>
                <div v-else class="defwid">
                    <span class="context wid" v-if="selitemIndex == 11 && startTime && endTime">
                        {{startTime}} - {{endTime}}
                    </span>
                    <span class="context" v-else-if="selitemIndex != 11 && seltext">
                        {{seltext}}
                    </span>
                    <span v-else>
                        请选择
                    </span>
                    <img v-if="seltext || (startTime && endTime)" class="cancelbtn right" @click.stop="reset" src="../../assets/img/<EMAIL>" alt="">
                </div>
            </div>
        </el-popover>
    </div>
</template>

<script>
export default {
    data(){
        return{
            typesName:{
                1:"本月",
                2:"本季度",
                3:"本年度",
                4:"上月",
                5:"上季度",
                6:"上年度",
                7:"本周",
                8:"上周",
                9:"今天",
                10:"昨天",
                11:"自定义",

            },
            types: [
                {
                    id: 9,
                    value: '今天'
                },
                {
                    id: 10,
                    value: '昨天'
                },
                {
                    id: 7,
                    value: '本周'
                },
                {
                    id: 8,
                    value: '上周'
                },
                {
                    id: 1,
                    value: '本月'
                },
                {
                    id: 2,
                    value: '本季度'
                },
                {
                    id: 3,
                    value: '本年度'
                },
                {
                    id: 4,
                    value: '上月'
                },
                {
                    id: 5,
                    value: '上季度'
                },
                {
                    id: 6,
                    value: '上年度'
                },
                {
                    id: 11,
                    value: '自定义'
                },
            ],
            selitemIndex:'',
            seltext:'',
            startTime:'',
            endTime:'',
            dateRange:[],
            visible:false,
        }
    },
    methods:{
        reset(){
            this.dateRange = [];
            this.selitemIndex = ''
            this.seltext = '';
            this.startTime = '';
            this.endTime = '';
            this.$emit('submitAction',this.selitemIndex,this.dateRange)
        },
        leave(){
            this.visible = false;
        },
        clickItem(e){
            this.selitemIndex = e
            if (e != 11) {
                this.dateRange = []
            }
        },
        changeDateRange(){

        },
        showPopver(){
            this.visible = !this.visible;
        },
        submitAction(){
            if (this.selitemIndex == 11) {
                this.startTime = this.dateRange[0];
                this.endTime = this.dateRange[1];
                this.seltext = ''
            }else if(this.selitemIndex != ''){
                this.startTime = '';
                this.endTime = '';
                this.seltext = this.typesName[this.selitemIndex]
            }
            this.visible = false;
            this.$emit('submitAction',this.selitemIndex,this.dateRange)
        },
    }
}
</script>

<style lang="scss" scoped>
.right{
    float: right
}
.cancelbtn{
    margin-top: 7px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}
.mt{
    margin-top: 16px;
}
.wid{
    width: 150px;
}
.context{
    color: #333333;
    white-space: nowrap;
    overflow:hidden;
    text-overflow: ellipsis;
}
.btnitem{
    font-size: 12px;
    color: #333333;
    padding: 5px 10px;
    border: 1px solid #DCDFE6;
    margin-right: 16px;
    margin-bottom: 16px;
    border-radius: 4px;
}
.seltext{
    font-size: 12px;
    color: white;
    border: none;
    background-color: #4285F4;
}
.flexdiv{
    display: flex;
    flex-wrap: wrap;
}
.deftimebtn{
    width: 220px;
    margin-top: 4px;
    height: 34px;
    line-height: 32px;
    border-radius: 4px;
    border:1px solid #DCDFE6;
    padding: 0px 10px;
    font-size: 14px;
    color: #DCDFE6;
    z-index: 1;
}
</style>