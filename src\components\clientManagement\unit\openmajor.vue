<template>
  <div class="mainbg" v-loading="isLoading">
    <div class="backdiv">
      <back>开设专业</back>
    </div>
    <div class="page-header">
      <el-form class="myform" ref="form" :model="pageBean" :inline="true">
        <el-form-item label="专业名称：">
          <el-input
            v-model="pageBean.specialtyName"
            class="definput"
            clearable
            placeholder="请输入专业名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-button
            class="defaultbtn"
            type="primary"
            icon="el-icon-search"
            @click="onSearch"
            >查询</el-button
          >
        </el-form-item>
      </el-form>
      <div>
        <el-button
          class="defaultbtn"
          type="primary"
          icon="el-icon-plus"
          @click="addUnit"
          >选择专业</el-button
        >
      </div>
    </div>
    <el-table class="unittable mytable" :data="tableData">
      <el-table-column prop="specialtyName" label="专业名称" align="center">
      </el-table-column>
      <el-table-column prop="edit" width="100" align="center" label="操作">
        <template slot-scope="scope">
          <el-button
            class="rbtn tabbtn"
            type="text"
            @click="deleteAction(scope.row)"
          >
            移除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page
      :currentPage="pageBean.pageNum"
      :total="total"
      :pageSize="pageBean.pageSize"
      @updatePageNum="handleCurrentChange"
    ></page>
    <el-dialog
      :before-close="handleClose"
      top="6vh"
      width="70%"
      class="customD"
      title="专业选择"
      :visible.sync="dialogTableVisible"
    >
      <div class="page-header" slot="title">
        <el-form ref="form" :model="pageBeanD" :inline="true">
          <el-form-item class="cbottom" label="专业名称：">
            <el-input
              class="definput"
              v-model="pageBeanD.specialtyName"
              clearable
              placeholder="请输入专业名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="专业类型：">
            <el-select
              clearable=""
              class="definput"
              popper-class="removescrollbar"
              v-model="pageBeanD.parentId"
              placeholder="请选择"
            >
              <el-option
                v-for="item in unitTypeData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="cbottom">
            <el-button
              class="search btn defaultbtn"
              type="primary"
              icon="el-icon-search"
              @click="onSearchD"
              >搜索
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table class="mytable" :data="dataList" v-loading="isLoading">
        <el-table-column prop="name" label="专业名称"> </el-table-column>
        <el-table-column prop="parentName" label="专业类型"> </el-table-column>
        <el-table-column prop="edit" width="80" label="操作">
          <template slot-scope="scope">
            <el-button
              class="bBtn"
              type="text"
              @click="onSelected(scope.row)"
              v-if="!scope.row.isSelected"
            >
              选择
            </el-button>
            <el-button class="bBtn" type="text" v-if="scope.row.isSelected">
              已选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <page
        :currentPage="pageBeanD.pageNum"
        :total="totalD"
        :pageSize="pageBeanD.pageSize"
        @updatePageNum="handleCurrentChangeD"
      ></page>
    </el-dialog>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import { listMajor, deleteMajor, nonSelectListAll, addMajor } from '@/api/wapi'
import back from '../../common/back.vue'
import { getDict } from '@/utils/tools'
export default {
  components: {
    page,
    nolist,
    back,
  },
  data() {
    return {
      dataList: [],
      totalD: 0,
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        specialtyName: '',
        pageNum: 1,
        pageSize: 10,
        unitId: '',
      },
      pageBeanD: {
        specialtyName: '',
        pageNum: 1,
        pageSize: 10,
        unitId: '',
        parentId: '',
      },
      dialogTableVisible: false,
      unitTypeData: [],
    }
  },
  created() {
    this.pageBean.unitId = this.$route.query.id
    this.loadData()
    getDict('SpecialType')
      .then((result) => {
        this.unitTypeData = result
      })
      .catch((err) => {})
  },
  methods: {
    handleClose() {
      this.loadData()
      this.dialogTableVisible = false
    },
    onSearch() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    onSearchD() {
      this.pageBeanD
      this.pageBeanD.pageNum = 1
      this.nonSelectListAllApi()
    },
    onSelected(row) {
      let params = {
        unitId: this.$route.query.id,
        specialtyId: row.id,
      }
      addMajor(params).then((res) => {
        if (res.status == 0) {
          // this.loadData()
          this.$message({
            type: 'success',
            message: '添加成功',
          })
          this.$set(row, 'isSelected', true)
        }
      })
    },
    loadData() {
      this.isLoading = true
      listMajor(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    addUnit() {
      this.pageBeanD.parentId = ''
      this.pageBeanD.specialtyName = ''
      this.dialogTableVisible = true
      this.nonSelectListAllApi()
    },
    nonSelectListAllApi() {
      this.pageBeanD.unitId = this.$route.query.id
      nonSelectListAll(this.pageBeanD).then((res) => {
        if (res.status == 0) {
          this.dataList = res.data
          this.totalD = res.page.total
        }
      })
    },
    deleteAction(data) {
      deleteMajor({ id: data.id }).then((result) => {
        if (result.data) {
          this.$message({
            type: 'success',
            message: '移除成功',
          })
          this.loadData()
        } else {
          this.$message({
            type: 'error',
            message: result.msg,
          })
        }
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    handleCurrentChangeD(page) {
      this.pageBeanD.pageNum = page
      this.nonSelectListAllApi()
    },
  },
}
</script>

<style scoped>
.cbottom {
  margin-bottom: 0 !important;
}
.backdiv {
  margin-bottom: 12px;
}
.definput {
  width: 180px;
}
.page-header {
  display: flex;
  justify-content: space-between;
}
.tabbtn {
  font-size: 14px;
  cursor: pointer;
  padding: 2px;
}
.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.btn {
  height: 34px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  letter-spacing: 1px;
}
.mainbg {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
}
</style>