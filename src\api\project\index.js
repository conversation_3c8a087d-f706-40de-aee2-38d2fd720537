import service from '@/utils/request.js'
import { data } from 'browserslist';
export function uploadFile(data) {
  return service.request({
    method: 'post',
    url: '/aliyun/oss/uploadFiles',
    data
  });
}

export function projectList(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/project/list`,
    params: data,
    type: '1111',
  });
}


export function taskList(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/task/list`,
    params: data,
  });
}


export function selectTemplate() {
  return service.request({
    method: 'get',
    url: `/crm/controller/projectstagetemplate/selectTemplate`,
  });
}

export function messageList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/projectmessage/list`,
    params
  });
}


export function projectlogList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/projectlog/list`,
    params
  });
}





export function proInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/project/info/${id}`,
  });
}

// 根据项目id查询详情页面任务数量统计
export function queryTaskStatusCount(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/project/queryTaskStatusCount/${id}`,
  });
}

// 项目日志列表
export function projectlog(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/projectlog/list`,
    params:data
  });
}

export function messageSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/projectmessage/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function messageDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/projectmessage/delete',
    data,
  });
}

export function projectSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/project/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function projectUpdate(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/project/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function taskworkhoursSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskworkhours/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function taskcostSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskcost/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function taskworkhoursDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskworkhours/delete',
    data,
  });
}

export function constDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskcost/delete',
    data,
  });
}


export function projectDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/project/delete',
    data,
  });
}

// 项目看板：根据项目id查询项目及项目下任务所有参与人员列表
export function queryProjectMembers(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/project/queryProjectMembers`,
    params:data
  });
}
// 项目看板：根据项目id查询项目效率统计
export function queryProjectEfficiency(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/project/queryProjectEfficiency/${id}`,
  });
}
// 项目看板：查询项目任务及子任务消耗工时列表

export function queryProjectCostById(id,data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/task/queryProjectCostById/${id}`,
    params:data
  });
}

export function taskInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/task/info/${id}`,
  });
}

export function taskcostInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/taskcost/info/${id}`,
  });
}

export function queryWorkListForTask(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/taskworkhours/queryWorkListForTask`,
    params:data
  });
}

// 
export function queryCostListForTask(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/taskcost/queryCostListForTask`,
    params:data
  });
}


export function queryOutputByProjectId(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/task/queryOutputByProjectId/${id}`,
  });
}



export function hourWorkList(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/taskworkhours/list`,
    params:data
  });
}

export function constList(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/taskcost/list`,
    params:data
  });
}

export function taskDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/task/delete',
    data,
  });
}



export function taskUpdate(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/task/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
export function taskCostUpdate(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskcost/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function taskSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/task/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function queryProjectDashboard(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/project/queryProjectDashboard`,
    params:data
  });
}

// 

export function queryProjectOverviewForPage(data) {
  return service.request({
    method: 'get',
    url: `/crm/controller/project/queryProjectOverviewForPage`,
    params:data
  });
}


// /crm/controller/task/queryProjectWorkHoursList

export function queryProjectWorkHoursList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/task/queryProjectWorkHoursList`,
    params
  });
}

// 

export function exportProjectWorkHoursList(data) {
  return service.request({
    method: 'post',
    url: `/crm/controller/task/exportProjectWorkHoursList`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    responseType:'blob'
  });
}


export function queryTaskList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/task/queryTaskList`,
    params
  });
}
