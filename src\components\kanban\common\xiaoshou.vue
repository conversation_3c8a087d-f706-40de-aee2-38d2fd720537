<template>
    <div>
        <el-card class="mokuai">
            <div class="title">
                <textBorder class="biaoti">销售情况</textBorder>
            </div>

            <el-row :gutter="12">
                <el-col :span="12">
                    <el-row :gutter="12">
                        <el-col :span="12">
                            <div class="qukuai">
                                <div class="toptitle">
                                    <textBorder class="wenzi">出版社教材情况</textBorder>
                                </div>
                                <div class="jiaocaiqukuai">
                                    <el-table
                                    :data="pressMaterialList"
                                    style="width: 100%"
                                    class="table"
                                    >
                                    <el-table-column
                                        label=""
                                        width="22">
                                        <template slot-scope="scope">
                                                {{scope.$index+1+'.'}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                      prop="pressName"
                                      label="单位"
                                      
                                      >
                                    </el-table-column>
                                    <el-table-column
                                        prop="number"
                                        label="数量"
                                        align="right"
                                        >
                                        <template slot-scope="scope">
                                            {{ scope.row.number+'册' }} 
                                        </template>
                                    </el-table-column>
                                  </el-table>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="qukuai">
                                <div class="toptitle">
                                    <textBorder class="wenzi">单位教材情况</textBorder>
                                    
                                </div>
                                <div class="jiaocaiqukuai">
                                    <el-table
                                    :data="unitMaterialList"
                                    style="width: 100%"
                                    class="table"
                                    >
                                    <el-table-column
                                        label=""
                                        width="22">
                                        <template slot-scope="scope">
                                                {{scope.$index+1+'.'}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                      prop="unitName"
                                      label="单位"
                                      
                                      >
                                    </el-table-column>
                                    <el-table-column
                                        prop="number"
                                        label="数量"
                                        align="right"
                                        >
                                        <template slot-scope="scope">
                                            {{ scope.row.number+'册' }} 
                                        </template>
                                    </el-table-column>
                                  </el-table>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </el-col>

                <el-col :span="12">
                    <div class="qukuai">
                        <div class="toptitle">
                            <textBorder class="wenzi">信息化情况</textBorder>
                        </div>
                        <div class="jiaocaiqukuai">
                            <el-table
                            :data="informationContractList"
                            style="width: 100%"
                            class="table"
                            >
                            <el-table-column
                            prop="contractName"
                            label="合同名称"
                            align="center"
                            >
                          </el-table-column>
                          <el-table-column
                            prop="contractType"
                            label="类型"
                            align="center"
                            
                            >
                           </el-table-column>
                            <el-table-column
                              prop="contractUnit"
                              label="单位"
                              align="center"
                              >
                            </el-table-column>

                          </el-table>
                        </div>
                    </div>

                </el-col>
            </el-row>
            <el-row :gutter="12">
                <el-col :span="12">
                    <div class="qukuai">
                        <div class="prcss clearfix">
                            <textBorder class="wenzi">单位教材业绩情况</textBorder>
                            <el-button type="text" v-isShow="'kanban:yeji:detail'" class="anniu" @click="goToUnitDetails(1)">···</el-button>
                        </div>
                        <div class="jiaocaiqukuai">
                            <el-table
                            :data="unitbookData"
                            style="width: 100%"
                            class="table"
                            >
                            <el-table-column
                            type="index"
                            label="排序"
                            align="center"
                            >
                          </el-table-column>
                          <el-table-column
                          prop="unitName"
                          label="单位"
                          align="center"
                          >
                        </el-table-column>

                        <el-table-column
                            prop="thisYearUnitPerformance"
                            label="业绩"
                            align="center">
                            <template slot-scope="scope">
                            {{ scope.row.thisYearUnitPerformance+'万元'}} 
                            </template>
                        </el-table-column>
                        
                        <el-table-column
                            prop="riseOrFallRange"
                            label="较去年变化"
                            align="center">
                            <template slot-scope="scope">
                                <span :class="typeClass[scope.row.isIncrease]">{{ scope.row.riseOrFallRange}} </span>
                            </template>
                        </el-table-column>


                          </el-table>
                        </div>
                    </div>

                </el-col>

                <el-col :span="12">
                    <div class="qukuai">
                        <div class="prcss clearfix">
                                <textBorder class="wenzi">单位信息化业绩情况</textBorder>  
                            <el-button type="text" v-isShow="'kanban:yeji:detail'" class="anniu" @click="goToUnitDetails(2)">···</el-button>
                        </div>
                        <div class="jiaocaiqukuai">
                            <el-table
                            :data="infoList"
                            style="width: 100%"
                            class="table"
                            >
                            <el-table-column
                            type="index"
                            label="排序"
                            align="center"
                            >
                          </el-table-column>
                          <el-table-column
                          prop="unitName"
                          label="单位"
                          align="center"
                          >
                        </el-table-column>

                            <el-table-column
                            prop="thisYearUnitPerformance"
                            label="业绩"
                            align="center">
                            <template slot-scope="scope">
                            {{ scope.row.thisYearUnitPerformance+'万元'}} 
                            </template>
                        </el-table-column>
                        
                        <el-table-column
                            prop="compare"
                            label="较去年变化"
                            align="center">
                            <template slot-scope="scope">
                             <span :class="typeClass[scope.row.isIncrease]">{{ scope.row.riseOrFallRange}} </span>
                            </template>
                        </el-table-column>


                          </el-table>
                        </div>
                    </div>

                </el-col>
            </el-row>

        </el-card>
    </div>
</template>

<script>
import '../detail.css';
import textBorder from '@/components/common/textBorder.vue';
export default {
    components:{
        textBorder
    },
    data() {
        return {
            unitMaterialList:[],
            pressMaterialList:[],
            informationContractList:[],
            unitbookData:[],
            infoList:[],
            typeClass:{
                "1":'upcss',
                "2":'downcss',
                '0':'chipingcss'
            },

        }
    },
    mounted() {

    },
    methods: {
        loadData(data){
            this.pressMaterialList = data.pressMaterialList;
            this.unitMaterialList = data.unitMaterialList;
            this.informationContractList = data.informationContractList;
        },
        loadMatData(data){
            this.unitbookData = data
        },
        loadInfoData(data){
            this.infoList = data
        },
        goToUnitDetails(type) {
            var vpar = Object.assign(this.$route.query,{type:type})
            this.$router.push({
                path:'/kanban/salesman/unitDetails',
                query:vpar
            });
        },
    }
}
</script>
<style lang="scss" scoped>
.prcss{
    position: relative
}
.mokuai {
    height: 738px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    margin-bottom: 12px;
}
.qukuai {
    height: 322px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
    padding: 16px;
    margin-bottom: 12px;
}

.jiaocaiqukuai {
    margin-top: 20px;
    height: 252px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
}
.jiaocaiqukuai {
    display: flex;
}
.table {
    margin: 0;
    overflow: auto;
    
}
.anniu {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    font-size: 20px;
    color: #666666;
}
/* 隐藏滚动条 */
.table::-webkit-scrollbar {
    display: none;
}

/* 当鼠标悬停时显示滚动条 */
.table:hover::-webkit-scrollbar {
    display: block;
}
</style>