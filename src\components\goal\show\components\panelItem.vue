<template>
  <div class="panelcss">
    <text-border>{{item.name}}</text-border>
    <div class="statuscss" :class="{'successColor':status == 1,'failColor':status==2,'noColor':status==0}">{{getStatus(item)}}</div>
    <div class="chartdiv" :id="`gauge${item.goalType}`"></div>
    <div class="gdiv">
        <span> 目标新增：{{this.item.goal}} {{this.item.unit}}</span>
        <span>剩余：{{this.item.residueGoal}} {{this.item.unit}}</span>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import textBorder from '../../../common/textBorder.vue';
export default {
    components:{
        textBorder
    },
    data(){
        return{
            status:0,
            item:{
                goalType:"",
                name:'',
                goal:0,
                finishGoal:0,
                finishRate:0,
                residueGoal:0,
            },
            type:{
                1:'新增客户',
                2:'新增拜访与跟进',
                7:'样书发放',
                4:'新增合同',
                5:'合同金额',
                6:'回款金额',
                7:'发放次数'
            },
            unitName:{
                1:'个',
                2:'次',
                7:'次',
                4:'个',
                5:'万元',
                6:'万元',
                7:'次'
            },
            option:{
                series: [
                    {
                    type: 'gauge',
                    max:100,
                    center:['50%','55%'],
                    progress: {
                        show: true,
                        width: 18,
                        itemStyle: {
                            color: "rgba(66,133,244, 1)",
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            width: 18
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        distance:-18,
                        length: 9,
                        lineStyle: {
                            width: 1.5,
                            color: '#AFC4E8'
                        }
                    },
                    axisLabel: {
                        distance:-20,
                        color: '#999999',
                        fontSize: 14
                    },
                    anchor: {
                        show: true,
                        showAbove: true,
                        size:8,
                        itemStyle: {
                            borderWidth: 4,
                            color:'rgb(255,255,255)',
                            borderColor:'rgb(51,51,51)'
                        }
                    },
                    pointer:{
                        icon:'roundRect',
                        width:4,
                        length:'60%',

                    },
                    itemStyle: {
                        color: "rgba(51,51,51, 1)",
                    },
                    title: {
                        show: true,
                        color:"#999999",
                        fontSize: 14,
                        fontFamily: 'Microsoft YaHei-Regular, Microsoft YaHei',
                        fontWeight: '400',
                        offsetCenter: [0, '40%'],
                    },
                    detail: {
                        valueAnimation: true,
                        color:'#333333',
                        fontSize: 32,
                        fontFamily: 'Roboto-Bold, Roboto',
                        fontWeight: 'bold',
                        offsetCenter: [0, '70%'],
                        formatter: '{value}%'
                    },
                    
                    data: [{value: 0,name: "完成率"}]
                    }
                ]
                }
        }
    },
    methods:{
        setPanelData(data){
            this.item = data;
            this.item.name = this.type[data.goalType];
            this.item.unit = this.unitName[this.item.goalType]
            this.$nextTick(()=>{
                var chartDom = document.getElementById(`gauge${this.item.goalType}`);
                var myChart = echarts.init(chartDom);
                this.option.series[0].data[0].value = Number(this.item.finishRate * 100).toFixed(1);
                this.option && myChart.setOption(this.option);
            })
        },
        getStatus(data){
            if(data.goal == 0){
                this.status = 0;
                return '暂无目标'
            }else if(data.goal <= data.finishGoal){
                this.status = 1;
                return `已完成`
            }else{
                this.status = 2;
                return "未完成"
            } 
        },
    }
}
</script>
<style scoped>
.gdiv{
    position: absolute;
    bottom: 32px;
    left: 20px;
    right: 20px;
    width: calc( 100% - 40px );
    text-align: center;
}
.gdvi span{

    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;

}
.gdiv span+span{
    margin-left: 50px;
}
.successColor{
    background-color: #DFFBE6;
    color: #56C36E !important;
}
.failColor{
    background-color: #FFE3E5;
    color: #F45961 !important;
}
.noColor{
    color: #333333;
    background-color: #F5F5F5;
}
.chartdiv{
    position: absolute;
    left: 20px;
    top: 50px;
    right: 20px;
    bottom: 50px;
    /* background-color: red; */
}
.panelcss{
    padding: 20px;
    min-height: 400px;
    background-color: white;
    margin-bottom: 12px;
    border-radius: 8px;
    position: relative;
}
.statuscss{
    position: absolute;
    right: 0px;
    top: 0px;
    width: 94px;
    height: 36px;
    line-height: 36px;
    border-radius: 0px 8px 0px 50px;
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    text-align: center;

}
</style>
<style>

</style>