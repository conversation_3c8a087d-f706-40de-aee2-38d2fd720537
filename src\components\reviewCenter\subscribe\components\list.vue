<template>
  <div class="mainbg fixpb">
    <div class="headerdiv">
      <el-form :inline="true" label-width="85px" class="myform clearfix">
        <el-form-item label="">
          <el-input
            v-model="pageBean.materialName"
            class="definput inputWid150"
            clearable
            placeholder="教材名称或出版社名称或ISBN"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="pageBean.unitName"
            class="definput inputWid150"
            clearable
            placeholder="请输入用书单位"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="pageBean.customerName"
            class="definput inputWid150"
            clearable
            placeholder="请输入用书客户"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="pageBean.applicantName"
            class="definput inputWid150"
            clearable
            placeholder="请输入报订人"
          >
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            @click="search"
            class="defaultbtn mt"
            icon="el-icon-search"
            type="primary"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-table class="mytable"
      :data="tableData"
      style="width: 100%"
      >
      <el-table-column prop="materialSubscriptionVo.materialName" label="教材名称" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.isbn" label="ISBN" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.platformName" label="出版社" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.unitName" label="用书单位" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.specialtyName" label="用书专业" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.customerName" label="客户" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.useBookYear" label="用书时间" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.price" label="教材定价(元)" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.reserveNum" label="预定数量" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.reserveRetailPrice" label="报订码洋(万元)" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.actualNum" label="实报数量" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.discount" label="折扣" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.actualRetailPrice" label="实际码洋(万元)" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.completionRate" label="完成率" align="center" width="167">
        <template slot-scope="scope">
          {{scope.row.materialSubscriptionVo.completionRate || '--'}}
        </template>
      </el-table-column>
      <el-table-column prop="status" v-if="handleType == 2" label="状态" align="center" width="167">
        <template slot-scope="scope">
          <span
            class="circle"
            :class="{
              'c-four': scope.row.status == 4,
              'c-three': scope.row.status == 3,
              'c-two': scope.row.status == 2,
              'c-one': scope.row.status == 1,
            }"
          ></span>
          {{statusMap[scope.row.status] || '--'}}
        </template>
      </el-table-column>
      <el-table-column prop="applicantName" label="报订人" align="center" width="167"/>
      <el-table-column prop="taskDepartmentName" label="报订人部门" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.bidwinnerName" label="中标公司" align="center" width="167"/>
      <el-table-column prop="materialSubscriptionVo.sourceGoods" label="货源" align="center" width="167">
        <template slot-scope="scope">
          {{scope.row.materialSubscriptionVo.sourceGoods || '--'}}
        </template>
      </el-table-column>
      <el-table-column prop="applyTime" label="报批时间" align="center" width="167"/>

      <el-table-column
        prop="edit"
        width="200"
        align="center"
        fixed="right"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            class="bbtn tabbtn"
            type="text"
            v-if="handleType == 2"
            @click="reviewAction(scope.row)"
          >
            详情</el-button>
            <el-button
              class="bbtn tabbtn"
              type="text"
              v-else
              @click="reviewAction(scope.row)"
            >
              审核</el-button
            >
        </template>
      </el-table-column>

    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <review-drawer
    ref="reviewDrawer"
    v-model="isShow"
    :data="reviewData"
    @reload="loadData"
    :handleType="handleType"></review-drawer>
  </div>
</template>

<script>
import page from '@/components/common/page'
import { centerStatus } from '@/utils/status-tool'
import { queryVoListByCode } from '@/api/wapi.js'
import { querySubscriptionList } from '@/api/reviewCenter'
import reviewDrawer from './reviewDrawer.vue'
export default {
  components: {
    page,
    reviewDrawer
  },
  data() {
    return {
      centerStatus,
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        keyword:'',
        applicantName:'',
        customerUnit:'',
        status:'',
        contractType: '',
        contractTypeSub: '',
        ruleType:4,
      },
      optionsContract: [],
      contractSubTypes: [],
      isShow:false,
      reviewData:{},
      tableData: [],
      statusMap:{
        1:'未报批',
        2:'审核中',
        3:'已通过',
        4:'已驳回'
      },
    }
  },
  props:{
    handleType:{
      type:Number,
      default:1,
    }
  },
  created() {
    this.pageBean.handleType = this.handleType

  },
  methods: {
    setValue(val) {
      let item = this.optionsContract.filter((item) => item.id === val)
      this.contractSubTypes = item[0].children
    },
    getDictApi() {
      queryVoListByCode({ code: 'ContractType' }).then((res) => {
        if (res.status == 0 && res.data.length > 0) {
          this.optionsContract = res.data
          if (this.pageBean.contractType) {
            this.setValue(this.pageBean.contractType)
          }
        }
      })
    },
    loadData(){
      querySubscriptionList(this.pageBean).then(res =>{
        this.tableData = res.data || []
        this.total = res.page.total
      }).catch(err =>{

      })
    },
    search() {
      console.log('search')
      this.pageBean.pageNum = 1;
      this.loadData()
    },
    reload(){
      this.pageBean.handleType = this.handleType;
      Object.keys(this.pageBean).forEach(item =>{
        if (item == 'pageNum') {
          this.pageBean.pageNum = 1;
        }else if (item != 'pageSize' && item != 'ruleType' && item != 'handleType') {
          this.pageBean[item] = ''
        }
      })
      this.loadData()
    },
    handleCurrentChange(pageNum) {
      console.log(pageNum)
      this.pageBean.pageNum = pageNum;
      this.loadData()
    },
    reviewAction(data){
      this.isShow = true;
      this.reviewData = data
    }

  },
}
</script>
<style lang="scss" scoped>
.bbtn {
  cursor: pointer;
}

.w1 {
  width: 120px;
}

.ml {
  margin-left: 10px;
}

</style>
