<template>
    <div>
        <!-- 年度概况 -->
        <el-card class="niandu">
            <div class="title">
                <textBorder class="biaoti">年度概况</textBorder>
            </div>
            <el-row :gutter="12">
                <el-col :span="3">
                    <div class="user" v-if="$route.query.userId">
                        <img v-if="viewData.userLogo" :src="viewData.userLogo" alt="" class="headeruser">
                        <div v-else class="headeruser">{{ viewData.userName }}</div>
                        <div class="name">{{viewData.userName}}</div>
                        <div class="bumen">{{viewData.userDepartment}}</div>
                    </div>
                    <div class="user" v-else>
                        <div class="bumen1" v-for="(item,index) in getList()" :key="index">{{item}}</div>
                    </div>
                </el-col>
                <el-col :span="3" >
                    <div class="kuai">
                        <div class="toptitle">
                            <textBorder class='wenzi'>全年累计业绩</textBorder>
                        </div>
                        <div class="icon">
                            <img src="@/assets/Group132.png" alt="">
                            <div class="number">
                                <span class="number-style">{{ viewData.thisYearPerformance || '--' }}</span>
                            万元
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="3" >
                    <div class="kuai">
                        <div class="toptitle">
                            <textBorder class='wenzi'>教材类业绩</textBorder>
                        </div>
                        <div class="icon">
                            <img src="@/assets/Group126.png" alt="">
                            <div class="number">
                                <span class="number-style">{{ viewData.thisYearMaterialPerformance || '--' }}</span>
                                万元
                            </div>
                            <div class="dan">
                                <div class="qiandan">签单：</div>
                                <div class="number-style1">{{ viewData.materialSignNumber || '--'}}</div>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="3" >
                    <div class="kuai">
                        <div class="toptitle">
                            <textBorder class='wenzi'>信息化业绩</textBorder>
                        </div>
                        <div class="icon">
                            <img src="@/assets/Group3490.png" alt="">
                            <div class="number" >
                                <span class="number-style">{{ viewData.thisYearInformationPerformance || '--' }}</span>
                                万元
                            </div>
                            <div class="dan" >
                                <div class="qiandan">签单：</div>
                                <div class="number-style1">{{ viewData.informationSignNumber || '--'}}</div>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="3" >
                    <div class="kuai">
                        <div class="toptitle">
                            <textBorder class='wenzi'>跟进与拜访</textBorder>
                        </div>
                        <div class="icon">
                            <img src="@/assets/Group117.png" alt="">
                            <div  class="nowyear" >
                                <div class="nowyear1">今年：</div>
                                <div class="number-style2">{{ viewData.annualVisitNumber || '--' }}</div>
                                <div class="nowyear1">次</div>
                            </div>
                            <div class="nowyear mt12 lastyear" >
                                <div class="nowyear1 lastyear1">去年：</div>
                                <div class="number-style2">{{ viewData.lastYearVisitNumber || '--' }}</div>
                                <div class="nowyear1">次</div>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="3" >
                    <div class="kuai">
                        <div class="toptitle">
                            <textBorder class='wenzi'>新增客户</textBorder>
                        </div>
                        <div class="icon">
                            <img src="@/assets/Group101.png" alt="">
                            <div  class="nowyear" >
                                <div class="nowyear1">今年：</div>
                                <div class="number-style2">{{ viewData.annualCustomerNumber || '--' }}</div>
                                <div class="nowyear1">个</div>
                            </div>
                            <div class="nowyear mt12 lastyear" >
                                <div class="nowyear1 lastyear1">去年：</div>
                                <div class="number-style2">{{ viewData.lastYearCustomerNumber || '--' }}</div>
                                <div class="nowyear1">个</div>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="3" >
                    <div class="kuai">
                        <div class="toptitle">
                            <textBorder class='wenzi'>成交单位</textBorder>
                        </div>
                        <div class="icon">
                            <img src="@/assets/Group1117.png" alt="">
                            <div  class="nowyear" >
                                <div class="nowyear1">今年：</div>
                                <div class="number-style2">{{ viewData.annualDealUnitNumber || '--' }}</div>
                                <div class="nowyear1">个</div>
                            </div>
                            <div class="nowyear mt12 lastyear" >
                                <div class="nowyear1 lastyear1">去年：</div>
                                <div class="number-style2">{{ viewData.lastYearDealUnitNumber || '--' }}</div>
                                <div class="nowyear1">个</div>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="3" >
                    <div class="kuai">
                        <div class="toptitle">
                            <textBorder class='wenzi'>回款金额</textBorder>
                        </div>
                        <div class="icon">
                            <img src="@/assets/Group3490.png" alt="">
                            <div  class="nowyear" >
                                <div class="nowyear1">今年：</div>
                                <div class="number-style2">{{ viewData.annualReceivables || '--' }}</div>
                                <div class="nowyear1">万元</div>
                            </div>
                            <div class="nowyear mt12 lastyear" >
                                <div class="nowyear1 lastyear1">去年：</div>
                                <div class="number-style2">{{ viewData.lastYearReceivables || '--' }}</div>
                                <div class="nowyear1">万元</div>
                            </div>
                        </div>
                    </div>
                </el-col>
             </el-row>
        </el-card>
    </div>
</template>    

<script>
import '../detail.css';
import textBorder from '../../../components/common/textBorder.vue';
export default {
    components:{
        textBorder
    },
    data() {
        return {
            viewData:{},
        }
    },
    mounted() {

    },
    methods: {
        loadData(data){
            this.viewData = data;
        },
        getList(){
            var list =  this.$route.query.name.split(',')
           return list
        },
    }
}
</script>

<style lang="scss" scoped>
.user{
    padding-top: 10px;
    overflow-y: auto;
}
::-webkit-scrollbar{
    display: none !important;
}
.bumen1{
    padding: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    // background-color: #F6F7FB;
}
.mt12{
    margin-top: 12px !important;
}
.headeruser{
    margin: 0 auto;
    margin-top: 24px;
    width: 65px;
    height: 65px;
    border-radius: 50%;
    background-color: #4285F4;
    line-height: 65px;
    color: white;
    
}
</style>


