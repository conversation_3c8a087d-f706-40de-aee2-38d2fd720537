<template>
    <div>
        <back>单位{{this.name[$route.query.type]}}业绩明细</back>
        <el-card class="card">
            <el-form label-width="85px">
                <el-row :gutter="20" >
                    <el-col :span="6">
                        <el-form-item label="单位名称：">
                            <el-input class="definput" v-model="pageBean.unitName" clearable placeholder="请输入单位名称"  ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="高级筛选">
                            <el-select class="definput" popper-class="removescrollbar" clearable v-model="pageBean.riseOrFall"
                                placeholder="请选择">
                                <el-option key="" label="全部" value="">
                                </el-option>
                                <el-option key="1" label="只看增长" value="1">
                                </el-option>
                                <el-option key="2" label="只看降低" value="2">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-button type="primary" icon="el-icon-search" @click="searchAction">搜索</el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
            :data="tableData"
            style="width: 100%"
            @sort-change="changeSort"
            >
            <el-table-column
              prop="unitName"
              label="单位"
              align="center"
              >
            </el-table-column>
            <el-table-column
              sort-by="thisYearUnitPerformance"
              prop="thisYearOrder"
              label="今年业绩"
              align="center"
              sortable>
              <template slot-scope="scope">
                {{ scope.row.thisYearUnitPerformance+'万元'}} 
                </template>
            </el-table-column>
            <el-table-column
              sort-by="lastYearUnitPerformance"
              prop="lastYearOrder"
              align="center"
              label="去年业绩"
              sortable>
              <template slot-scope="scope">
                {{ scope.row.lastYearUnitPerformance+'万元'}} 
                </template>
            </el-table-column>
            <el-table-column
              sort-by="riseOrFallRange"
              prop="riseOrFallOrder"
              align="center"
              label="变化"
              
              sortable>
              <template slot-scope="scope">
                <span :class="typeClass[scope.row.isIncrease]">{{ scope.row.riseOrFallRange}} </span>
                </template>
            </el-table-column>
          </el-table>
          <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"></page>
        </el-card>
    </div>
</template>

<script>
import page from '@/components/common/page.vue';
import back from '@/components/common/back.vue'
import { 
    queryUnitPerformancePage
} from '@/api/bigdata';
export default {
    components: {
        back,
        page
    },
    data() {
        return {
            name:{
                "1":'教材',
                "2":'信息化'
            },
            typeClass:{
                "1":'upcss',
                "2":'downcss',
                '0':'chipingcss'
            },
            levels: [
                {
                    name:'1231'
                }
            ], 
            tableData: [],
            total: 0,
            pageBean: {
                    userId:this.$route.query.userId,
                    departmentId:this.$route.query.departmentId,
                    year:this.$route.query.year,
                    type:this.$route.query.type,
                    pageNum: 1,
                    pageSize: 10,
                }
        }
    },
    created(){
        this.loadData()
    },
    methods: {
        loadData(){
            queryUnitPerformancePage(this.pageBean).then((result) => {
                this.tableData = result.data;
                this.total = result.page.total;
            }).catch((err) => {
                
            });
        },
        searchAction(){
            this.pageBean.pageNum = 1;
            this.clearSort()
            this.loadData()
        },
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.loadData()
        },

        changeSort(e){
            console.log("resssssss",e.prop,e.order);
            if (e.order === 'ascending') {
                this.pageBean[e.prop] = 1
            }else if (e.order === 'descending') {
                this.pageBean[e.prop] = 2
            }else{
                this.pageBean[e.prop] = ''
            }
            this.clearSort(e.prop)
            this.pageBean.pageNum = 1;
            this.loadData()
        },
        clearSort(prop){
            var array = ['thisYearOrder','lastYearOrder','riseOrFallOrder']
            array.forEach(element => {
                if (element != prop) {
                    this.pageBean[element] = ''
                }
            });
        },
    }
}
</script>

<style scoped lang="scss">
.card {
    margin-top: 20px;
}


</style>