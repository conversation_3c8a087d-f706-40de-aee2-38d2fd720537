<template>
  <div :class="classObj" class="app-wrapper">
    <div class="headerdiv">
      <!-- <div :class="{'fixed-header':fixedHeader}">
        <navbar />
      </div> -->
      <img class="logo1" :src="gslogo" alt="">
      <span class="cname">{{applicationName}}</span>
      <div class="right">
        <headuser class="logo" :url="logo" :username="name" width="32"></headuser>
        <span class="pname">{{ name }}</span>
      </div>
      <div class="right">
         <el-button @click="logout">退出</el-button>
      </div>
    </div>
    <div class="empty"></div>
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <div class="sheight">
      <sidebar class="sidebar-container" />
      <div class="main-container">
        <app-main />
      </div>
    </div>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain } from './components'
import headuser from '../components/common/headuser.vue';
import ResizeMixin from './mixin/ResizeHandler'

export default {
  name: 'Layout',
  data(){
    return {
      name: sessionStorage.getItem('username'),
      logo: sessionStorage.getItem('logo'),
      applicationName: sessionStorage.getItem('applicationName'),
      gslogo:require('@/assets/wutonghua.png'),
    }
  },
  components: {
    // Navbar,
    Sidebar,
    AppMain,
    headuser
  },
  mixins: [ResizeMixin],
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  methods: {
    async logout() {
      await this.$store.dispatch('user/logout')
      await this.$store.dispatch('permission/resertLookRouters') //清空路由
      this.$router.push(`/login`)
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  @import "~@/styles/variables.scss";
  .empty{
    height: 56px;
  }
  .sheight{
    height:calc(100% - 56px)
  }
  .pname{
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FFFFFF;
    margin-right: 40px;
    // margin-left: 8px;
  }
  .cname{
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FFFFFF;
    margin-left: 12px;
  }
  .logo{
    width: 32px;
    height: 32px;
    margin-left: 20px;
    border-radius: 50%;
    margin-top: 12px;
  }
   .logo1{
    width: 24px;
    height: 24px;
    margin-left: 20px;
    margin-top: -3px;
  }
  .headerdiv{
    height: 56px;
    background: #161C25;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    line-height: 56px
  }

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: calc(100%);
    width: 100%;
    &.mobile.openSidebar{
      position: fixed;
      top: 0;
    }
  }
  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$sideBarWidth});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px)
  }

  .mobile .fixed-header {
    width: 100%;
  }
</style>
