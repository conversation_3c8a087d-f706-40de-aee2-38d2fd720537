import request from '@/utils/request'

// 客户关联的-销售机会
export function opportunityList(params) {
    return request({
      url: "/crm/controller/opportunity/list",
      method: 'get',
      params
    })
}

export function addOpportunity(data) {
    return request({
      url: '/crm/controller/opportunity/save',
      method: 'post',
      headers:{
        'Content-Type' : 'application/json;charset=UTF-8'
      },
      data,
      
    })
  }

export function updateOpportunity(data) {
    return request({
      url: '/crm/controller/opportunity/update',
      method: 'post',
      headers:{
        'Content-Type' : 'application/json;charset=UTF-8'
      },
      data,
      
    })
  }

// 机会详情
export function opportunityInfo(id) {
  return request({
    url: `/crm/controller/opportunity/info/${id}`,
    method: 'get',
  })
}

// 删除机会

export function deleteOpportunity(data) {
  return request({
    url: '/crm/controller/opportunity/delete',
    method: 'post',
    data,
    
  })
}

// 快捷创建合同
export function addContract(opportunityId){
  return request({
    url: `/crm/controller/opportunity/addContract/${opportunityId}`,
    method: 'get',
  })
}