<template>
    <div class="resource app-container">
        <back>员工管理</back>
        <el-form :model="form" ref="dataForm" :rules="rules"  :inline="true" label-width="120px">
            <div class="topContent">
                <div class="topTitle">
                    <div class="line"></div>
                    <div class="title">数据迁移</div>
                </div>
                <div class="qianyi">
                    <div class="text">迁移方式：</div>
                    <el-radio-group v-model="radio" @change="handleRadioChange">
                        <el-radio :label="1">区域分配</el-radio>
                        <el-radio :label="2">单位分配</el-radio>
                        <el-radio :label="3">负责人分配</el-radio>
                    </el-radio-group>
                </div>
                
                <el-form-item label="转出负责人：" prop="transferOutId">
                    <div class="unitbtn" :class="{ selectedCss: form.fromManagerName }" @click="clickXuan('选择转出负责人', 1)">
                        {{ form.fromManagerName ? form.fromManagerName : '请选择转出负责人' }}
                        <i class="rcenter el-icon-arrow-down" />
                    </div>
                </el-form-item>
                <el-form-item label="转入负责人：" prop="transferInId">
                    <div class="unitbtn" :class="{ selectedCss: form.toManagerName }" @click="clickXuan('选择转入负责人', 1)">
                        {{ form.toManagerName ? form.toManagerName : '请选择转入负责人' }}
                        <i class="rcenter el-icon-arrow-down" />
                    </div>
                </el-form-item>
                
                <el-form-item v-if="radio == 1" label="选择区域：" prop="addressData" >
                    <el-cascader
                        v-model="form.addressData"
                        class="w250"
                        :options="options"
                        :props="{
                            value: 'id',
                            label: 'name',
                            children: 'areaList',
                            checkStrictly: true,
                        }"
                        @change="changeAddress"
                        placeholder="请选择区域"
                    ></el-cascader>
                </el-form-item>
                
                <el-form-item v-if="radio == 2" label="选择单位：" prop="unitId">
                    <el-select
                        v-model="form.unitName"
                        placeholder="请选择单位"
                        class="w250"
                        clearable
                        :disabled="!form.fromManagerName"
                        @click.native.stop="chooseunit"
                        @focus="$event.target.blur()">
                        <el-option
                            v-if="form.unitName"
                            :value="form.unitName"
                            :label="form.unitName">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="small" @click="queryData">查询数据</el-button>
                </el-form-item>
            </div>

            <div class="bottomContent">
                <div class="topTitle">
                    <div class="line"></div>
                    <div class="title">数据模块</div>
                </div>
                <el-table :data="tableData" style="width: 1000px" border>
                    <el-table-column prop="module" label="模块" align="center" :formatter="moduleFormatter"></el-table-column>
                    <el-table-column prop="transferCount" label="数据" width="300" align="center"></el-table-column>
                </el-table>
                <div class="tip">设置数据迁移时，负责人是从所有可用账号中进行选择，该操作不可逆，请谨慎操作</div>
            </div>
            <div class="btn">
                <el-button type="primary" size="small" class="submitBtn" @click="submitForm">确认</el-button>
            </div>
        </el-form>
        
        <!-- 负责人选择弹框 -->
        <systemDialog
            ref="systemdialog"
            :name="dialogName"
            :multipleNum="multipleNum"
            :visible.sync="dialogVisible"
            @updateVisible="updateSystemVisible"
            @submitData="submitData"
        ></systemDialog>
        
        <!-- 单位选择弹框 -->
        <unitListDialog
            ref="unitdialog"
            :visible.sync="unitDialogVisible"
            :selectedUnitId="form.unitId"
            :chargePersonName="form.fromManagerName"
            :chargePersonDepartment="form.fromManagerDepartment"
            @updateVisible="updateUnitVisible"
            @updateUnit="updateUnit"
        ></unitListDialog>
        
        <el-dialog
            title="数据迁移进度"
            :visible.sync="progressDialogVisible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false"
            width="600px">
            <div class="progress-container">
                <div class="progress-status">{{ progressStatus }}</div>
                <el-progress :percentage="progressPercentage" :format="progressFormat" :status="progressColorStatus" :stroke-width="20" text-inside></el-progress>
                <div class="progress-actions">
                    <el-button v-if="!migrationCompleted && !migrationCanceled" type="danger" size="small" @click="handleCancelMigration">取消</el-button>
                    <el-button v-if="migrationCompleted && !migrationCanceled" type="primary" size="small" @click="handleComplete">完成</el-button>
                    <el-button v-if="migrationCompleted || migrationCanceled" size="small" @click="closeProgressDialog">关闭</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import back from '../../common/back.vue'
import { queryAreaVoList } from '@/api/index'
import { queryMigrateData, dataMigration, queryProgress, cancelMigration } from '@/api/framework/user'
import systemDialog from '../../common/systemDialog.vue'
import unitListDialog from '../../common/unitListDialog.vue'

export default {
    components: {
        back,
        systemDialog,
        unitListDialog
    },
    data() {
        return {
            radio: 3,
            tableData:[],
            form: {
                applicationId: '',
                transferWay: 3,
                unitId: [],
                unitName: '',
                provinceId: '',
                cityId: '',
                transferInId: '',
                transferInDepartmnetId: '',
                transferOutId: '',
                transferOutDepartmnetId: '',
                addressData: [],
                fromManagerName: '',
                fromManagerDepartment: '',
                toManagerName: ''
            },
            managerList: [], // 负责人列表
            areaList: [], // 区域列表
            unitList: [], // 单位列表
            options: [], // 地区数据
            rules: {
                transferOutId: [
                    { required: true, message: '请选择转出负责人', trigger: 'blur' }
                ],
                transferInId: [
                    { required: true, message: '请选择转入负责人', trigger: 'blur' }
                ],
                unitId: [
                    { required: true, message: '请选择单位', trigger: 'blur' }
                ],
                addressData: [
                    { required: true, message: '请选择区域', trigger: 'blur' }
                ]
            },
            dialogVisible: false,
            dialogName: '',
            multipleNum: 1,
            unitDialogVisible: false,
            moduleMap: {
                1: '单位',
                2: '客户',
                3: '销售机会',
                4: '合同',
                5: '项目',
                6: '工单',
                7: '教材发放',
                8: '样书发放',
                9: '礼品发放',
                10: '商务活动'
            },
            progressDialogVisible: false,
            progressPercentage: 0,
            progressTimer: null,
            migrationCompleted: false,
            progressRequestData: null, 
            progressStatus: '数据迁移中..........', 
            migrationCanceled: false, 
        };
    },
    created() {
        this.queryAreaVoListApi();
    },
    methods: {
        handleTreeList(list) {
            // 删除第三级children
            for (var i = 0; i < list.length; i++) {
                if (list[i].areaList.length < 1) {
                    // 判断children的数组长度
                    list[i].areaList = undefined
                } else {
                    this.handleTreeList(list[i].areaList)
                }
            }
            return list
        },
        queryAreaVoListApi() {
            queryAreaVoList({ level: 0 }).then((res) => {
                if (res.status == 0) {
                    this.options = this.handleTreeList(res.data);
                }
            })
        },
        changeAddress(val) {
            console.log('选择的区域：', val);
        },
        handleRadioChange(value) {
            this.form.transferWay = value;
            this.tableData = [];
            
            const resetFields = {
                addressData: [],
                unitId: [],
                unitName: '',
                provinceId: '',
                cityId: ''
            };
            
            Object.keys(resetFields).forEach(key => {
                this.form[key] = resetFields[key];
            });
            
            this.$nextTick(() => {
                if (this.$refs.dataForm) {
                    this.$refs.dataForm.clearValidate();
                }
            });
        },
        
        moduleFormatter(row, column, cellValue) {
            return this.moduleMap[cellValue] ;
        },
        
        prepareRequestData() {
            if (this.radio == 1 && this.form.addressData) {
                this.form.provinceId = this.form.addressData[0];
                this.form.cityId = this.form.addressData.length > 1 ? this.form.addressData[1] : '';
            }
            
            return {
                applicationId: this.form.applicationId,
                transferWay: this.form.transferWay,
                unitId: this.form.unitId,
                provinceId: this.form.provinceId,
                cityId: this.form.cityId,
                transferInId: this.form.transferInId,
                transferInDepartmnetId: this.form.transferInDepartmnetId,
                transferOutId: this.form.transferOutId,
                transferOutDepartmnetId: this.form.transferOutDepartmnetId
            };
        },
        
        queryData() {
            this.$refs.dataForm.validate(valid => {
                if (valid) {
                    const requestData = this.prepareRequestData();
                    
                    queryMigrateData(requestData).then(res => {
                        if (res.status == 0) {
                            console.log('接口返回数据:', res.data);
                            this.tableData = res.data;
                        } else {
                            this.$message.error(res.message);
                        }
                    })
                }
            });
        },
        
        clickXuan(name, multipleNum) {
            this.dialogName = name;
            this.multipleNum = multipleNum;
            this.$refs.systemdialog.loadData();
            
            if (name === '选择转出负责人') {
                if (this.form.transferOutId) {
                    this.$refs.systemdialog.updateWorksId([{
                        id: this.form.transferOutId,
                        name: this.form.fromManagerName
                    }]);
                } else {
                    this.$refs.systemdialog.updateWorksId([]);
                }
            } else if (name === '选择转入负责人') {
                if (this.form.transferInId) {
                    this.$refs.systemdialog.updateWorksId([{
                        id: this.form.transferInId,
                        name: this.form.toManagerName
                    }]);
                } else {
                    this.$refs.systemdialog.updateWorksId([]);
                }
            }
            
            this.dialogVisible = true;
        },
        
        updateSystemVisible(value) {
            this.dialogVisible = value;
        },
        
        submitData(data, type, departmentId) {
            const isTransferOut = type === '选择转出负责人';
            const selectedId = data.length > 0 ? data[0].id : '';
            const selectedName = data.length > 0 ? data[0].name : '';
            
            if (isTransferOut) {
                // 更改转出负责人，清空已选单位
                if (this.form.transferOutId !== selectedId) {
                    this.form.unitId = [];
                    this.form.unitName = '';
                }
                
                this.form.transferOutId = selectedId;
                this.form.fromManagerName = selectedName;
                this.form.transferOutDepartmnetId = departmentId;
                this.form.fromManagerDepartment = data.length > 0 && data[0].departmentName ? data[0].departmentName : '';
                this.$refs.dataForm.validateField('transferOutId');
            } else {
                this.form.transferInId = selectedId;
                this.form.toManagerName = selectedName;
                this.form.transferInDepartmnetId = departmentId;
                this.$refs.dataForm.validateField('transferInId');
            }
            
            this.updateSystemVisible(false);
        },
        
        chooseunit() {
            if (!this.form.fromManagerName) {
                this.$message.warning('请先选择转出负责人');
                return;
            }
            this.unitDialogVisible = true;
        },
        
        updateUnitVisible(val) {
            this.unitDialogVisible = val;
        },
        
        updateUnit(data) {
            this.form.unitName = data.unitNames.join('，');
            this.form.unitId = data.unitIds;
            this.$refs.dataForm.validateField('unitId');
        },
        
       
        submitForm() {
            this.$refs.dataForm.validate(valid => {
                if (valid) {
                    if (!this.tableData || this.tableData.length == 0) {
                        this.$message.warning('请先查询数据');
                        return;
                    }
                    
                    if (this.form.transferOutId && this.form.transferInId && 
                        this.form.transferOutId == this.form.transferInId) {
                        this.$message.warning('转出负责人和转入负责人不能是同一个人');
                        return;
                    }
                    
                    const requestData = this.prepareRequestData();
                    
                    this.initProgressDialog(requestData);
                    
                    dataMigration(requestData).then(res => {
                        if (res.status != 0 && !this.migrationCanceled) {
                            this.$message.error(res.msg);
                            this.progressDialogVisible = false;
                        }
                    })
                }
            });
        },
        
        // 初始化进度弹窗
        initProgressDialog(requestData) {
            this.progressRequestData = {...requestData};
            this.progressDialogVisible = true;
            this.progressPercentage = 0;
            this.migrationCompleted = false;
            this.migrationCanceled = false;
            this.progressStatus = '数据迁移中..........';
            this.startProgressPolling();
        },
        
        // 开始轮询
        startProgressPolling() {
            // 清除定时器
            if (this.progressTimer) {
                clearInterval(this.progressTimer);
            }
            
            // 设置新的定时器
            this.progressTimer = setInterval(() => {
                this.queryMigrationProgress();
            }, 1000);
        },
        
        // 查询迁移进度
        queryMigrationProgress() {
            queryProgress(this.progressRequestData).then(res => {
                if (res.status == 0) {
                    if (res.data >= 1) {
                        this.progressPercentage = 100;
                        this.migrationCompleted = true;
                        this.progressStatus = '数据迁移完成';
                        this.stopProgressPolling();
                        return;
                    }
                    
                    this.progressPercentage = parseFloat((res.data * 100).toFixed(2)) || 0;
                    
                    if (this.progressPercentage >= 100) {
                        this.migrationCompleted = true;
                        this.progressStatus = '数据迁移完成';
                        this.stopProgressPolling();
                    }
                } else {
                    this.$message.error( res.msg);
                }
            })
        },
        
        // 停止轮询
        stopProgressPolling() {
            if (this.progressTimer) {
                clearInterval(this.progressTimer);
                this.progressTimer = null;
            }
        },
        
        // 进度条格式化
        progressFormat(percentage) {
            return percentage + '%';
        },
        
        // 取消迁移
        handleCancelMigration() {
            this.$confirm('确定要取消数据迁移吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                if (this.migrationCompleted) {
                    this.$message.warning('数据已经迁移完成，无法取消');
                    return;
                }
                
                cancelMigration().then(res => {
                    if (res.status == 0) {
                        this.$message.success('已取消数据迁移');
                        this.stopProgressPolling();
                        this.migrationCanceled = true;
                        this.progressStatus = '数据迁移已停止';
                    } else {
                        this.$message.error(res.message);
                    }
                });
            })
        },
        
        handleComplete() {
            this.closeProgressDialog();
        },
        
        closeProgressDialog() {
            this.progressDialogVisible = false;
            this.stopProgressPolling();
            
            if (this.migrationCompleted || this.migrationCanceled) {
                this.refreshTableData();
            }
        },
        
        refreshTableData() {
            if (this.form.transferOutId && this.form.transferInId) {
                this.queryData();
            }
        },

    },
    computed: {
        progressColorStatus() {
            if (this.migrationCompleted) {
                return 'success';
            } else if (this.migrationCanceled) {
                return 'exception'; 
            } else {
                return undefined 
            }
        },
    },
    beforeDestroy() {
        this.stopProgressPolling();
    },
    watch: {
        'form.unitName': function(val) {
            if (!val) {
                this.form.unitId = [];
                if (this.radio == 2) {
                    this.$refs.dataForm.validateField('unitId');
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.topTitle{
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
    background: #f0f3fa;
    border-radius: 4px;
    padding: 0 16px;
    margin-bottom: 20px;
    margin-top: 20px;
}
.line{
    width: 3px;
    height: 14px;
    background-color: #386cfc;
    margin-right: 12px;

}
.title{
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2d2f33;
    text-align: left;
    font-style: normal;
    text-transform: none;
}
.qianyi{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}
.tip{
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #f45961;
}
.btn{
    text-align: center;
}
.submitBtn{
    margin-top: 20px;
    width: 100px;
}
.w250{
    width: 250px;
}
.unitbtn {
    width: 250px;
    height: 34px;
    line-height: 34px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    padding: 0 10px;
    position: relative;
    cursor: pointer;
    color: #c0c4cc;
}
.selectedCss {
    color: #333333;
}
.disabledBtn {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
    opacity: 0.7;
}
.rcenter {
    position: absolute;
    right: 10px;
    line-height: 34px;
    font-size: 14px;
    color: #c0c4cc;
    top: 0;
    z-index: 10;
}
.progress-container {
    padding: 20px;
}
.progress-actions {
    margin-top: 20px;
    text-align: center;
}
.progress-status {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2d2f33;
    text-align: center;
    margin-bottom: 20px;
}
</style> 