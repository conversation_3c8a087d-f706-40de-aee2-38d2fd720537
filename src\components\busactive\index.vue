<template>
  <div>
      <el-card v-loading="isLoading" class="mt10">
          <el-form label-width="85px" class="myform " >
              <el-row  type="flex" justify="start">
                      <el-form-item label="客户名称：" >
                          <el-input class="definput " v-model="pageBean.materialName" clearable placeholder="请输入"></el-input>
                      </el-form-item>
                    <el-form-item label="单位名称：" >
                        <el-input class="definput " v-model="pageBean.materialName" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="业务经理：" >
                        <el-input class="definput " v-model="pageBean.materialName" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item label="时间：">
                      <el-date-picker
                        class="pcss"
                        v-model="pageBean.value1"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                      </el-date-picker>
                    </el-form-item>
                  <el-button class="searb" icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
              </el-row>
              <el-row  type="flex" justify="end">
                <el-button class="searb1"  type="primary" @click="searchAction">导出</el-button>
                <el-button class="searb1"  type="primary" @click="searchAction">新增</el-button>
            </el-row>
          </el-form>
          <el-table :cell-style="cell" @sort-change="sortChange" class="customertable mytable tootiptable" :data="tableData" style="width: 100%">
            <el-table-column prop="platformName" label="事由" align="center" >
                <template slot-scope="scope">
                    <div @click="openDetails(scope.row)">{{ scope.row.platformName }}</div>
                </template>
            </el-table-column>
            <el-table-column   prop="platformName" label="金额" align="center" >
            </el-table-column>
            <el-table-column prop="platformName" label="客户" align="center" >
            </el-table-column>
              <el-table-column  prop="useNum" label="单位" align="center" >
              </el-table-column>
              <el-table-column  prop="hezuo"  label="业务经理" align="center" >
              </el-table-column>
              <el-table-column  prop="author" label="时间" align="center" >
              </el-table-column>
              <template slot="empty">
                  <nolist></nolist>
              </template>
          </el-table>
          <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
              @updatePageNum="handleCurrentChange"></page>
      </el-card>
  </div>
  
</template>

<script>
  import page from '@/components/common/page.vue';
  import nolist from '@/components/common/nolist.vue';
  import { selectSpecialty, customerbookList, customerbookSave,customerbookUpdate,customerbookInfo,customerbookDelete } from "@/api/clientmanagement/customer";
  export default {
      components: {
          page,
          nolist,
      },
      data() {
          return {
              customerId:this.$route.query.id,
              majorList:[],
              isLoading: false,
              tableData: [],
              total: 0,
              pageBean: {
                  materialName:'',
                  userBookSpecialty:'',
                  isJoin:undefined,
                  pageNum: 1,
                  pageSize: 10,
                  useTime:'',
                  startObj: null,
                  endObj: null,
              },
          }
      },
      created() {
          this.loadSepcialty();
          this.pageBean.useTime = this.getDate()
          this.loadData();
      },
      methods: {
        openDetails(row){
                this.$router.push({
                    path:'/bigdata/datadetail'
                })
        },
        cell({row , column , rowIndex , columnIndex}){
          if(columnIndex==0){
            return 'color: #4285F4;cursor:pointer'
          }
        },
        sortChange(column) {
            //打印可以分别得到上下箭头的数据
            console.log(column);
            if (column.order == "ascending") {
                this.orderBy = "+";//根据自己的业务需求来
            } else if (column.order == "descending") {
                this.orderBy = "-";
            } else {
                this.orderBy = "";
            }
            return 
            //有多列排序时会用到
            // if (column.prop == "publishTime") {
            //     this.key = "publish_time ";
            // }  else if (column.prop == "updateTime") {
            //     this.key = "update_time";
            // } else {
            //     this.key = "";
            // }
            this.currentPage = 1;
            this.searchData();//查询列表方法
        },
        getDate() {
            var now = new Date();
            var year = now.getFullYear(); 
            return year+'';
        },
          loadSepcialty(){
              selectSpecialty({
                  customerId:this.customerId,
              }).then((result) => {
                  this.majorList = result.data;
              }).catch((err) => {
                  
              });
          },
          loadData() {
              this.isLoading = true;
              customerbookList(this.pageBean).then((result) => {
                  this.tableData = result.data ? result.data : [];
                  this.total = result.data ? result.page.total : 0
                  this.isLoading = false;
              }).catch((err) => {
                  this.isLoading = false;
              });
          },
          searchAction() {
                this.$router.push({
                    path:'/bigdata/datadetail',
                    query:{
                            name:"张三"
                    }
                })
              return
              this.pageBean.pageNum = 1;
              this.loadData();
          },
          handleCurrentChange(page) {
              this.pageBean.pageNum = page;
              this.loadData();
          },
      }
  }
</script>
<style>
    .yearcss .el-input__inner{
        height: 34px;
    }
  .tootiptable .el-tooltip {
      text-align: left;
  }
  .cpdding .el-input__inner{
          padding-right: 0;
  }       
    .wip{
        min-width: 60px;
        width: 40%;
        height: 34px;
    }
    .wip .el-input__inner{
       height: 28px;
       position: relative;
       top: -4px;
       line-height: 1px !important;
    }
    .wip .el-input__suffix{
        height: 28px;
        line-height: 34px;
    }
   
</style>

<style scoped>
  .pcss{
    height: 34px;
  }
  .pcss /deep/.el-range-separator{
      line-height: 26px;
  }
  .pcss /deep/.el-range__icon{
    line-height: 26px;
  }
  .searb{
    padding: 4px 10px;
    height: 34px;
    position: relative;
    top:2px;
  }
  .searb1{
    padding: 4px 24px;
    height: 34px;
  }
    .numdiv{
        height: 34px;
        border: 1px solid   #DCDFE6;
        padding: 0 10px;
        margin-top: 3px;
    }
    
        .form-center{
            display: inline-block;
            height: 34px;
            width: 20%;
            text-align: center;
            line-height: 34px;
            vertical-align: top;
        }
    .form-conten{
      position: relative;
    }  
   .remaining{
    position:absolute;
    bottom: 10px;
    right: 30px;
  }
    .el-date-editor.el-input{
            width: 100%;
    }
    .useTime{
        width: 120px;
        display: inline-block;
        margin-right: 20px;
    }
    .myform{
        padding: 20px 0;
    }
    .myform  /deep/.el-form-item__content{
        margin-right: 10px;
    } 
    .logoti{
        height: 37px;
        font-size: 28px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        color: #333333;
        display: inline-block;
        vertical-align: middle;
        margin-left: 12px;
    }
    .datalogo{
        text-align: center;
        padding-bottom: 20px;
        border-bottom: 1px solid #F0F0F0;
    }
  .resultp{
    text-align: right;
    margin-bottom: 10px;
  }
.mr10{
  margin-right: 10px !important;
}
.pdl{
  padding-left: 10px;
}
.defaultbtn1 {
  padding: 9px 10px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
 


  .fxcenter {
      min-width: 30px !important;
      height: 52px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
  }

  .zhiding {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      margin-top: -4px;
      vertical-align: middle;
      position: relative;
      top: 21px;
  }

  .mt {
      margin-top: 4px;
  }

 




  .customertable .el-button {
      padding: 2px;
  }
  .mr10 {
      margin-right: 10px;
  }

  .right {
      text-align: right;
  }
</style>