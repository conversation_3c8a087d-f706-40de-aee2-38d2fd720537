<template>
  <el-dialog
    :title="dialogNameNew"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="50%">
    <el-form :model="dataForm" :rules="Rule.RESOURCERULE" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-form-item label="资源类型：" v-if="typeResourceStatus===2" prop="sType">
        <el-select clearable v-model="dataForm.sType" placeholder="请选择资源类型">
          <el-option
            v-for="item in Dict.RESOURCE_TYPE"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="资源名称：" prop="name">
        <el-input v-model="dataForm.name" placeholder="资源名称"></el-input>
      </el-form-item>
      <el-form-item label="授权标识：" prop="permissionKey">
        <el-input v-model.trim="dataForm.permissionKey" placeholder="具有唯一性，规则为：大模块名称:小模块名称:具体功能名称 如：sf:scheduler:task:list"></el-input>
      </el-form-item>
      <el-form-item label="资源URL：" prop="sUrl" v-if="dataForm.sType==0 || dataForm.sType==1">
        <el-input v-model.trim="dataForm.sUrl" placeholder="资源的URL"></el-input>
      </el-form-item>

      <el-form-item label="图标：" prop="sIcon">
        <el-input placeholder="选择图标" v-model="dataForm.sIcon" :prefix-icon="dataForm.sIcon">
          <template slot="append" @click="opneIconSet()"><i style="cursor: pointer;" class="el-icon-menu" @click="opneIconSet()"></i></template>
        </el-input>
      </el-form-item>
      <el-form-item label="排序号：" prop="sortNo">
        <el-input-number style="width:100%;" v-model="dataForm.sortNo" :min="1" placeholder="排序号"></el-input-number>
      </el-form-item>
      <el-form-item label="资源状态：">
        <el-switch
          v-model="dataForm.status"
          active-text="正常"
          inactive-text="隐藏"
          active-color="#13ce66"
          inactive-color="#cccccc">
        </el-switch>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
    <IconSet v-if="iconSetVisible" ref="iconSet" @setIconData="setIconData"></IconSet>
  </el-dialog>
</template>

<script>
import { addResource ,resourceInfo,updateResource} from '../../../api/framework/resource'
import IconSet from './icon-set'
export default {
  data () {
    return {
      visible: false,
      visibleIco: false,
      pitchOn: '',
      iconSetVisible: false,
      dialogNameNew: '',
      typeStatus: '',
      typeResourceStatus: '',
      applicationDisabled: false,
      dataForm: {
        id: '',
        parentId: 0,
        parentName: '',
        permissionKey: '',
        name: '',
        status: true,
        sType: '',
        sUrl: '',
        sIcon: '',
        icon: '',
        sortNo: '',
      }
    }
  },
  components: {
    IconSet,
  },
  methods: {
    // 设置icon的值
    setIconData(icon) {
      this.dataForm.sIcon = icon
    },
    clearNmae () {
      this.dataForm.name = '';
      this.dataForm.permissionKey = '';
      this.dataForm.operationPermissionKey = []
    },
    // 初始化
    init (obj, type) {
      this.visible = true
      this.typeStatus = type;
      if (type === 1) { // 新增
        this.clearNmae()
        if (obj !== "" && obj !== "null") {
          this.dialogNameNew = `新增-${obj.name}`;
          this.typeResourceStatus = 2;
          this.dataForm.parentId = obj.id || '';
          this.dataForm.sType = (obj.sType + 1).toString();
          this.dataForm.sIcon = '';
          this.dataForm.sortNo = '';
          this.dataForm.id = '';
        } else {
          this.dialogNameNew = '新增目录'
          this.typeResourceStatus = 1;
          this.dataForm.parentId = 0;
          this.dataForm.sType = '0';
        }
        this.dataForm.name = '';
        this.dataForm.permissionKey = '';
        this.dataForm.sUrl = '';
      } else { 
        this.dialogNameNew = `修改-${obj.name}`;
        this.typeResourceStatus = 2; 
        this.dataForm.parentId = '';
        this.dataForm.id = obj.id;
        this.dataForm.sType = (obj.sType).toString();
        this.$nextTick(async () => {
          let res = await resourceInfo(obj.id)
          if (res.status === 0) {
            this.dataForm.name = res.data.name; 
            this.dataForm.permissionKey = res.data.permissionKey; 
            this.dataForm.sIcon = res.data.sIcon;
            this.dataForm.sortNo = res.data.sortNo; 
            this.dataForm.sUrl = res.data.sUrl || ''; 
            this.dataForm.status = res.data.status === 0; 
          }
        })
      }
    },
    opneIconSet () {
      this.iconSetVisible = true
      this.$nextTick(() => {
        this.$refs.iconSet.init()
      })
    },
    dataFormSubmit () {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          var params = {
            'parentId': this.dataForm.parentId, 
            'permissionKey': this.dataForm.permissionKey,
            'name': this.dataForm.name,
            'sType': this.dataForm.sType,
            'sUrl': this.dataForm.sUrl,
            'sIcon': this.dataForm.sIcon,
            'sortNo': this.dataForm.sortNo,
            'status': this.dataForm.status ? '0' : '1',
          }
          let res 
          if (this.typeStatus === 1) {
           res= await addResource(params)
          } else {
            params.id = this.dataForm.id
            res = await updateResource(params)
          }
          if (res.status === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
            })
            this.visible = false
            this.$emit('refreshDataList')
          } else {
            this.$message.error(res.msg)
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.el-dialog__title{
  font-size: 14px;
}
</style>
