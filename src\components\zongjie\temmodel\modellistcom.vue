<template>
  <div class="mainbg fixpb" v-loading="isLoading">
    <div class="page-header">
      <el-form class="myform" ref="form" :model="pageBean" :inline="true">
        <el-form-item label="类型：">
          <el-select  clearable="" class="definput"  v-model="pageBean.templateType"
            placeholder="请选择">
            <el-option v-for="item in searchTypeData" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="部门：">
          <div class="unitbtn" @click="chooseunit(1)">
            <span v-if="pageBean.templateDepartmentIds" class="deffont">{{
              searchdepartmentNames
            }}</span>
            <span v-else>请选择</span>
            <i @click.stop="closeOpp"
            v-show="searchdepartmentNames" class="rcenter el-icon-close"></i> <i
            v-show="!searchdepartmentNames" class="rcenter el-icon-arrow-down" /> 
          </div>
        </el-form-item>
        <el-form-item label="">
          <el-button class=" defaultbtn" type="primary" icon="el-icon-search" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button v-isShow="'crm:business:template:save'" class="defaultbtn" type="primary" icon="el-icon-plus"
          @click="addUnit">创建模板</el-button>
      </div>
    </div>
    <el-table class="mytable" :data="tableData" style="width: 100%" >
      <el-table-column prop="templateName" label="模板名称" align="center">
      </el-table-column>
      <el-table-column prop="departmentName" label="部门" align="center">
        <template slot-scope="scope">
          {{ scope.row.departmentName.join(',') }}
        </template>
      </el-table-column>
      <el-table-column prop="customerNumber" label="类型" align="center">
        <template slot-scope="scope">
              {{sourceName[scope.row.templateType]}}
        </template>
      </el-table-column>
      <el-table-column prop="edit" width="160" align="center" label="操作">
        <template slot-scope="scope">
          <el-button v-isShow="'crm:business:template:info'"  class="bbtn tabbtn" type="text" @click="onView(scope.row)">
            预览</el-button>
          <el-button v-isShow="'crm:business:template:update'" :disabled="getDisabled(scope.row)" class="bbtn tabbtn" type="text" @click="onEdit(scope.row)">
            编辑</el-button>
          <el-button v-isShow="'crm:business:template:delete'" :disabled="getDisabled(scope.row)" class="rbtn tabbtn" type="text"
            @click="deleteAction(scope.row)">
            删除</el-button>
        </template>

      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
  


      <div class="fixpage">
        <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"></page>
    </div>

    <dc-dialog :iType="giveType" title="提示" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
      <template>
      </template>
      <p class="pcc">{{ giveMessage }}</p>
    </dc-dialog>
    <departmentDialog ref="deRef" :dType="dType" :visible.sync="dialogVisibleDe" @updateVisible="updateSystemVisible"
      @submitData="submitData">
    </departmentDialog>

    <el-dialog
      title="创建模板"
      :visible.sync="dialogVisibleModel"
      width="400px"
      center
      >
      <el-form :rules="rules" label-width="100px" class="addform" ref="formModel" :model="formModel" >
        <el-form-item label="模板类型：" prop="templateType">
          <el-select  clearable="" class="definput width100"  v-model="formModel.templateType"
            placeholder="请选择">
            <el-option v-for="item in formMode" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="部门：" prop="templateDepartmentId">
          <div class="unitbtnModel unitbtn" @click="chooseunit(2)">
            <span v-if="formModel.templateDepartmentId" class="deffont">{{
              formModel.departmentNames
            }}</span>
            <span v-else>请选择</span>
            <i class=" rcenter el-icon-arrow-down" />
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextButton">下一步</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

  import page from '../../common/page.vue';
  import dcDialog from '../../common/dcDialog.vue';
  import nolist from '../../common/nolist.vue';
  import { listMoban, deleteMoban,unitType,queryUnitCharacter} from '@/api/moban/index'
  import { getDict } from '@/utils/tools'
  import departmentDialog from '@/components/common/departmentDialog.vue';
  import { getParStr } from "@/utils/tools";
  export default {
    components: {
      page,
      dcDialog,
      nolist,
      departmentDialog
    },
    props:{
          typeModel:{
            type:String,
            default:''
          },
    },
    data() {
      return {
        dialogVisibleModel:false,
        dialogVisibleDe:false,
        dType:'1',
        level: sessionStorage.getItem('dataScope'),
        userId: sessionStorage.getItem('userid'),
        giveType: 1,
        giveMessage: '',
        isLoading: false,
        dialogVisible: false,
        sourceName:{
            1:'年计划',
            2:'月计划',
            3:'周计划',
            4:'年总结',
            5:'月总结',
            6:'周总结'
        },
        deleteData: {},
        tableData: [],
        total: 0,
        pageBean: {
          templateDepartmentIds: '',
          pageNum: 1,
          pageSize: 10,
          templateType: '',
          type:'',
        },
        searchdepartmentNames:'',
        formModel:{
          departmentNames:'',
          templateDepartmentId:'',
          templateType:'',
        },
        zongTypeData:[
          {
            name: "年总结",
            id: "4",
          }, {
            name: "月总结",
            id: "5",
          }, {
            name: "周总结",
            id: "6",
          }
        ],
        jiTypeData:[
          {
            name: "年计划",
            id: "1",
          }, {
            name: "月计划",
            id: "2",
          }, {
            name: "周计划",
            id: "3",
          }
        ],
        searchTypeData:[],
        addModelZong:[
          {
            name: "年总结模板",
            id: "4",
          }, {
            name: "月总结模板",
            id: "5",
          }, {
            name: "周总结模板",
            id: "6",
          }
        ],
        addModelJi:[
          {
            name: "年计划模板",
            id: "1",
          }, {
            name: "月计划模板",
            id: "2",
          }, {
            name: "周计划模板",
            id: "3",
          }
        ],
        formMode:[],
        submitType:'',
        rules: {
          templateDepartmentId: [
            { required: true, message: '请选择部门', trigger: 'blur' },
          ],
          templateType: [
            { required: true, message: '请选择模板类型', trigger: 'change' }
          ],
        },
        selDepartmentData:[],
      }
    },
    created() {
      if (Object.keys(this.$route.query).length>0) {
          this.pageBean = Object.assign(this.pageBean,this.$route.query)
          this.pageBean.pageNum = Number(this.pageBean.pageNum)
          this.pageBean.pageSize = Number(this.pageBean.pageSize)
      }
      if(this.typeModel == 3){
        this.formMode = this.addModelZong
        this.searchTypeData = this.zongTypeData
        this.pageBean.type = 2   //总结
      }else if(this.typeModel == 2){
        this.pageBean.type = 1   //计划
        this.formMode = this.addModelJi
        this.searchTypeData = this.jiTypeData
      }
      this.loadData();
    },

    methods: {
      onView(item){ 
              if(this.typeModel == 3){
                this.$router.push({
                  path:'/zongjie/temmodel/viewmodel',
                  query:{
                      modelType:3,
                      id: item.id,
                      templateType:item.templateType
                  }
                }) 
              }else if(this.typeModel == 2){
                this.$router.push({
                  path:'/plan/modellist/jimodelview',
                  query:{
                      modelType:2,
                      id: item.id,
                      templateType:item.templateType
                  }
                }) 
              }
      },
      submitData(data) {
            this.dialogVisibleDe = false;
            this.selDepartmentData = data;
            let departmentIds = data.map(item=>item.id)
            let departmentNames = data.map(item=>item.name)
            if(this.submitType ==1){
              this.searchdepartmentNames = departmentNames.join(',')
              this.pageBean.templateDepartmentIds = departmentIds.join(',')
            }else{
              this.formModel.departmentNames = departmentNames.join(',')
              this.formModel.templateDepartmentId = departmentIds
              this.$refs['formModel'].validateField('templateDepartmentId');
            }
        },
        closeOpp() {
              this.searchdepartmentNames = ''
              this.pageBean.templateDepartmentIds =''
              this.selDepartmentData = []
            },
      updateSystemVisible(value) {
            this.dialogVisibleDe = value;
        },
      chooseunit(type) {
            this.submitType = type;
            this.dialogVisibleDe = true
            this.$refs.deRef.loadData()
            this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
        },
      loadData() {
        history.replaceState(null,null,`#${this.$route.path}?${getParStr(this.pageBean)}`) 
        this.isLoading = true;
        listMoban(this.pageBean).then((result) => {
          this.tableData = result.data;
          this.total = result.page.total;
          this.isLoading = false;
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      getDisabled(data) {
        if (this.level == 4 || this.userId == data.createBy) {
          return false
        }
        return true;
      },
      onSearch() {
        this.pageBean.pageNum = 1;
        this.loadData();
      },
      addUnit() {
        this.formModel.templateType = ''
        this.formModel.templateDepartmentId = ''
        this.formModel.departmentNames = ''
        this.dialogVisibleModel = true
        this.$nextTick(()=>{
          this.$refs['formModel'].resetFields();
        })
      },
      nextButton(){
        this.$refs['formModel'].validate((valid) => {
          if (valid) {
            sessionStorage.setItem('departmentIds',this.formModel.templateDepartmentId.join(','))
            sessionStorage.setItem('templateType',this.formModel.templateType)
            if(this.typeModel == 3){
                this.$router.push({
                  path:'/zongjie/temmodel/zongjieaddmodel',
                  query:{
                      modelType:3
                  }
                }) 
              }else if(this.typeModel == 2){
                this.$router.push({
                  path:'/plan/modellist/add',
                  query:{
                      modelType:2
                  }
                }) 
              }
          } else {
            return false;
          }
        });
      },
      onEdit(item) {
             if(this.typeModel == 3){
              this.$router.push({
                path:'/zongjie/temmodel/zongjieaddmodel',
                query:{
                    modelType:3,
                    id: item.id 
                }
              }) 
            }else if(this.typeModel == 2){
              this.$router.push({
                path:'/plan/modellist/add',
                query:{
                    modelType:2,
                    id: item.id 
                }
              }) 
            }
      },
      deleteAction(data) {
        this.deleteData = data;
        this.dialogVisible = true;
        this.giveMessage = '是否确认删除该模板？'
      },
      submitDialog() {
        this.dialogVisible = false;
        this.delete();
      },
      delete() {
        deleteMoban({ id: this.deleteData.id }).then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData();
          } else {
            this.$message({
              type: 'error',
              message: result.msg
            })
          }
        }).catch((err) => {

        });
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.loadData();
      }
    }
  }

</script>

<style scoped>
    .rcenter {
    position: absolute;
    right: 10px;
    line-height: 34px;
    font-size: 14px;
    color: #c0c4cc;
    top: 0;
  }
  .unitbtn{
    width: 200px;
    position: relative;
    top: 4px;
  }
  .unitbtnModel{
    width: 100%;
    position: relative;
    top: 4px;
  }
  .width100{
    width: 100% !important;
  }
  .definput{
    width: 180px;
  }
  .page-header {
    display: flex;
    justify-content: space-between;
  }
  .myform /deep/.el-form-con
  .tabbtn {
    font-size: 14px;
    cursor: pointer;
    padding: 2px;
  }

  .mbtn {
    margin-right: 40px;
  }

  .mbtn,
  .mbtn:hover,
  .mbtn:focus {
    color: #FF8D1A !important;
  }

  .bbtn,
  .bbtn:hover,
  .bbtn:focus {
    color: #4285F4;
  }

  .rbtn,
  .rbtn:hover,
  .rbtn:focus {
    color: #F45961;
  }

  .btn {
    height: 34px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FFFFFF;
    letter-spacing: 1px;
  }

  .search {
    width: 96px;
  }

  .mainbg {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    min-height:calc(100vh - 106px);
  }

</style>