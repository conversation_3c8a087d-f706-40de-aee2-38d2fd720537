<!-- 复制岗位管理 -->
<template>
  <div class="mod-config">
    <BaseDialog title="岗位管理" :isshow.sync="dialogShow" width="1000px" height="650px">
      <section class="dialog-box">
        <el-table
          ref="datatable"
          :data="dataList"
          border
          :empty-text="$emptyFont"
          stripe
          height="500"
          v-loading="dataListLoading"
          :row-key="getRowKeys"
          style="width: 100%; overflow-y: scroll;"
          element-loading-text="加载中..." element-loading-spinner="el-icon-loading">
          <el-table-column
            prop="name"
            label="部门名称"
            fixed="left"
            width="300"
            header-align="center">
          </el-table-column>
          <el-table-column
            prop="name"
            label="岗位名称"
            fixed="left"
            header-align="center">
            <template slot-scope="scope">
              <span>岗位1</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="province"
            header-align="center"
            align="center"
            label="管理省域">
            <template slot-scope="scope">
              <span v-for="(item, index) in scope.row.areaList" :key="index">{{ item.areaName}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="province"
            header-align="center"
            align="center"
            label="管理权限">
            <template slot-scope="scope">
              <span v-for="(item, index) in scope.row.resourceList" :key="index">{{ item.positionResource}}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            header-align="center"
            align="center"
            width="250"
            label="操作">
            <template slot-scope="scope">
              <el-button size="mini" v-isShow="'sf:business:companyposition:save'" @click="addOrUpdateHandle('add', scope.row)" v-if="scope.row.dataType===2">添加岗位</el-button>
              <el-button type="primary" v-isShow="'sf:business:companyposition:update'" size="mini" @click="addOrUpdateHandle('update', scope.row,)" v-if="scope.row.dataType===3">编辑</el-button>
              <el-button type="danger" v-isShow="'sf:business:companyposition:delete'" size="mini" @click="deleteHandle(scope.row)" v-if="scope.row.dataType===3">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
          v-if="pagingObj.totalSum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagingObj.currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagingObj.totalSum">
        </el-pagination>
      </section>
    </BaseDialog>
    <!-- 弹窗, 新增 / 修改 -->
    <addCompanyMent v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList(search)"></addCompanyMent>
  </div>
</template>

<script>
import addCompanyMent from './add-companyMent'
import pagination from '@/mixin/pagination'
import BaseDialog from '@/components/base/BaseDialog.vue'
export default {
  mixins: [pagination],
  data () {
    return {
      dialogShow: false,
      search: {},
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dataListLoading: false,
      addOrUpdateVisible: false
    }
  },
  components: {
    addCompanyMent,
    BaseDialog
  },
  methods: {
    init (val) {
      this.dialogShow = true
      this.search.pageNum = '1';
      this.search.pageSize = '10';
      this.search.companyId = val
      this.getDataList(this.search)
    },
    getRowKeys (row) {
      return row.id;
    },
    // 权限
    handlePression (id) {
      this.$nextTick(() => {
        this.$refs.basepremission.init(id)
      })
    },
    // 查询父级id name
    searchFatherIdName (childId, list) {
      let result = {};
      for (let i = 0; i < list.length; i++) {
        if (list[i].children && list[i].children.length > 0) {
          list[i].children.forEach(res => {
            if (res.id === childId) {
              result.id = list[i].id;
              result.name = list[i].name
            }
          })
        }
      }
      return result
    },
    // 新增 / 修改
    addOrUpdateHandle (flag, obj) {
      let parent = '';
      if (flag === 'add') {
        parent = this.searchFatherIdName(obj.id, this.dataList) || 'null'
      }
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(flag, obj, parent)
      })
    },
    // 删除
    deleteHandle (obj) {
      let name = obj.name;
      let id = obj.id;
      this.$confirm(`确定对${name}进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await this.$axios.post(`/sf/business/companyposition/delete/${id}`)
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList(this.search)
            }
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取数据列表
    async getDataList (obj) {
      this.dataListLoading = true
      let res = await this.$axios.get('/sf/business/companyposition/findPositionByCompanyId', { params: obj })
      if (res.status === 0) {
        this.dataListLoading = false
        this.dataList = res.data[0].children || [];
      }
    }
  }
}
</script>

<style>
.mod-config .el-table .cell{
  text-align: left !important;
}
</style>

<style lang="scss" scoped>
.dialog-box{
  padding: 10px;
}
</style>
