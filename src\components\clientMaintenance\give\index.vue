<template>
    <div class="mainbg">
        <el-form label-width="85px" class="myform ">
            <el-row :gutter="60" type="flex" justify="start">
                <el-col :span="6">
                    <el-form-item label="发放类型：">
                        <el-select class="definput" popper-class="removescrollbar" v-model="pageBean.type" clearable
                            placeholder="请选择">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="客户名称：">
                        <el-input class="definput" v-model="pageBean.customerName" clearable
                            placeholder="请输入客户姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发放人：">
                        <el-input class="definput" v-model="pageBean.createByName" clearable
                            placeholder="请输入发放人姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-button class="defaultbtn mt" icon="el-icon-search" type="primary"
                        @click="searchAction()">搜索</el-button>
                    <el-button class="defaultbtn mt right" icon="el-icon-plus" type="primary"
                        v-isShow="'crm:controller:distribution:save'" @click="addGive">我要发放</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-table class="jhtable mytable" :data="tableData" style="width: 100%"  v-loading="isLoading">
            <el-table-column prop="reason" label="发放事由" class-name="column_blue" align="center" min-width="200px">
            </el-table-column>
            <el-table-column prop="type" label="发放类型" align="center" min-width="167px">
                <template slot-scope="scope">
                    <div>{{ types[scope.row.type] }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="customerName" label="客户名称" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="unitName" label="客户单位" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="createByName" label="发放人" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="orderNumber" label="发放单号" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="distributionTime" label="发放时间" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="edit" label="是否回访" align="center" min-width="120px">
                <template slot-scope="scope">
                    <div v-if="scope.row.isRevisit" class="yesc">是</div>
                    <div v-else class="noc">否</div>
                </template>
            </el-table-column>
            <el-table-column prop="edit" width="250" align="center" fixed="right" label="更多操作">
                <template slot-scope="scope">
                    <el-dropdown placement="bottom-end" @command="e => handleCommand(e, scope.row)" trigger="click">
                        <el-button class="bbtn mr20" type="text">
                            更多
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item v-isShow="'crm:controller:distributiondetail:list'"
                                :command="0">查看明细</el-dropdown-item>
                            <el-dropdown-item v-isShow="'crm:controller:distributionrevisit:list'"
                                :command="1">回访</el-dropdown-item>
                            <el-dropdown-item v-isShow="'crm:controller:distribution:info'"
                                :command="2">详情</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-button class="bbtn " v-isShow="'crm:controller:distribution:update'" type="text"
                        @click="onEdit(scope.row)"> 编辑</el-button>
                    <el-button class="rbtn " v-isShow="'crm:controller:distribution:delete'" type="text"
                        @click="deleteAction(scope.row)"> 删除</el-button>
                </template>

            </el-table-column>
            <template slot="empty">
                <nolist></nolist>
            </template>
        </el-table>
        <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"></page>
        <dc-dialog :iType="giveType" title="提示" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
            <template>

            </template>
            <p class="pcc">{{ giveMessage }}</p>
        </dc-dialog>
        <revisit ref="revisitDialog" :visible.sync="revisitVisible" @updateVisible="updateVisible"></revisit>
        <giveDetailsDialog :visible.sync="givedialogVisible" ref="giveDialog" @updateVisible="updateGiveVisible">
        </giveDetailsDialog>
    </div>
</template>

<script>
import page from '@/components/common/page.vue';
import revisit from './components/revisitDialog.vue';
import giveDetailsDialog from './components/giveDetailsDialog.vue';
import { distributionList, deleteDistribution } from '@/api/clientMaintenance/give';
import nolist from '@/components/common/nolist.vue';
export default {
    components: {
        page,
        revisit,
        giveDetailsDialog,
        nolist
        // stageDialog
    },
    data() {
        return {
            giveType: 1,
            giveMessage: "",
            givedialogVisible: false,
            dialogVisible: false,
            revisitVisible: false,
            types: {
                1: '样书',
                2: "礼品"
            },
            options: [{
                value: "",
                label: '全部',
            }, {
                value: "1",
                label: '样书',
            }, {
                value: "2",
                label: '礼品',
            }],
            isLoading: false,
            tableData: [],
            deleteData: {},
            total: 0,
            pageBean: {
                type: "",
                customerName: "",
                createByName: '',
                pageNum: 1,
                pageSize: 10,
            }
        }
    },
    created() {
        this.loadData();
    },
    methods: {
        loadData() {
            this.isLoading = true;
            distributionList(this.pageBean).then((result) => {
                this.tableData = result.data;
                this.total = result.page.total;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        searchAction() {
            this.pageBean.pageNum = 1;
            this.loadData()
        },
        updateGiveVisible(val) {
            this.givedialogVisible = val;
        },
        updateVisible(val) {
            this.revisitVisible = val;
        },
        submitDialog() {
            this.dialogVisible = false;
            if (this.giveType = 1) {
                // 删除
                deleteDistribution({ id: this.deleteData.id }).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: "删除成功"
                        })
                        this.loadData();

                    } else {
                        this.giveType = 2;
                        this.giveMessage = result.msg;
                        this.dialogVisible = true;
                    }
                }).catch((err) => {

                });
            }
        },
        addGive() {
            this.$router.push({
                path: '/clientMaintenance/give/add'
            })
        },
        deleteAction(data) {
            this.giveType = 1;
            this.giveMessage = '是否删除该发放记录?';
            this.dialogVisible = true;
            this.deleteData = data;
        },
        onEdit(data) {
            this.$router.push({
                path: '/clientMaintenance/give/add',
                query: {
                    id: data.id,
                }
            })
        },
        handleCommand(index, data) {
            switch (index) {
                case 0:// 查看明细
                    this.givedialogVisible = true;
                    this.$nextTick(() => {
                        this.$refs.giveDialog.init(data);
                    })
                    break;
                case 1:// 回访
                    this.revisitVisible = true;
                    this.$nextTick(() => {
                        this.$refs.revisitDialog.init(data);
                    })
                    break;
                case 2:
                    {
                        this.$router.push({
                            path: '/clientMaintenance/give/detail',
                            query: { id: data.id } // 机会id
                        })
                    }
                    break;
                default:
                    break;
            }

        },
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.loadData();
        }
    }
}
</script>

<style scoped>
.yesc {
    color: #56C36E;
}

.noc {
    color: #F45961;
}

.mr20 {
    margin-right: 20px;
}

.jhtable .el-button+.el-button {
    margin-left: 20px;
}

.mainbg {
    background-color: white;
    padding: 20px;
}

.pcc {
    margin: 0 auto;
    text-align: center;
}

.smtext {
    zoom: 0.8;
}

.fxcenter {
    width: 50px;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.zhiding {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    /* background-color: red; */
}

.mt {
    margin-top: 4px;
}

.cusnamecss {
    display: flex;
}

.tagcss {
    font-size: .625em !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 14px;
    min-width: 30px;
    line-height: 11px;
    border-radius: 2px;
    margin-right: 20px;
}

.genjin {
    color: #4285F4;
    background-color: #DFEAFD;
}

.fuze {
    color: #FEF2E7;
    background-color: #FF8D1A;
}

.xiezuo {
    color: #56C36E;
    background-color: #F3FEF6;
}

.tagcss:nth-child(2n+2) {
    margin-top: 4px;
}

.jhtable /deep/.cell {}

.jhtable .el-button {
    padding: 2px;
}

.mr10 {
    margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
    color: #4285F4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
    color: #F45961;
}

.bumen /deep/.el-form-item__label {
    width: 130px !important;
    margin-left: -45px;

}

.right {
    text-align: right;
}
</style>
