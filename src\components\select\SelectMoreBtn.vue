<!-- 按钮层：快捷方式 -->
<template>
  <div>
    <el-form-item label="操作按钮" prop="departmentId">
      <el-select multiple placeholder="请选择" v-model="myValue" @change="change">
        <el-option
          v-for="item in options"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
export default {
  props: ['value'],
  data () {
    return {
      myValue: [],
      options: ''
    }
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    change (val) {
      let ary = [];
      for (let i = 0; i < val.length; i++) {
        this.options.map(item => {
          if (val[i] === item.id) {
            ary.push(`${item.name},${item.permissionKey}`)
          }
        })
      }
      this.$emit('getBtnData', ary)
    },
    async getReqData () {
      let res = await this.$axios.get('/sf/business/resourceoperation/getallvalidresourceoperationvo')
      if (res.status === 0) {
        this.options = res.data
      }
    }
  },
  created () {
    this.myValue = this.value || ''
    this.getReqData()
  }
}
</script>
