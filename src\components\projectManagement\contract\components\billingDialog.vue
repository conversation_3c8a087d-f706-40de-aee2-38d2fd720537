<template>
  <el-dialog
    :title="isEdit ? '编辑开票' : '开票'"
    class="adddetailcss"
    width="60%"
    top="60px"
    :visible.sync="addVisible"
    append-to-body
    center
    @closed="closed"
  >
    <el-form
      ref="addform"
      class="refundcss"
      :model="form"
      :rules="rules"
      label-width="130px"
    >
      <el-row
        class="mb30 bbline"
        type="flex"
        justify="space-around"
        :gutter="0"
      >
        <el-col :span="12">
          <el-form-item label="合同金额：">
            <span>{{ contractAmount }}万元</span>
          </el-form-item>
          <el-form-item label="未开票金额：">
            <span>{{ noInvoicingTotalAmount }}万元</span>
          </el-form-item>
          <el-form-item label="币种：" prop="currency">
            <el-select
              class="definput"
              popper-class="removescrollbar"
              v-model="form.currency"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="抬头名称：" prop="invoiceHeader">
            <el-input
              class="definput"
              v-model="form.invoiceHeader"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开票金额：" prop="invoicingAmount">
            <el-input
              class="definput"
              type="number"
              v-model="form.invoicingAmount"
              placeholder="请输入开票金额（单位：万元）"
            ></el-input>
          </el-form-item>

          <el-form-item label="开票类型：" prop="invoicingType">
            <el-select
              class="definput"
              popper-class="removescrollbar"
              v-model="form.invoicingType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in invoicingTypes"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单位税号：" prop="taxIdentificationNumber">
            <el-input
              class="definput"
              type="number"
              v-model="form.taxIdentificationNumber"
              placeholder="请输入单位税号"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <p class="tips">选填内容（开户行，银行账号等）</p>
      <el-row
        class="mb30 bbline"
        type="flex"
        justify="space-around"
        :gutter="0"
      >
        <!-- <el-col :span="12">
                    <el-form-item   label='抬头类型：' prop="invoiceHeaderType">
                        <el-select class="definput" popper-class="removescrollbar" v-model="form.invoiceHeaderType" placeholder="请选择抬头类型">
                            <el-option
                            v-for="item in options2"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item   label='纳税识别号：' prop="taxIdentificationNumber">
                        <el-input  class="definput" v-model="form.taxIdentificationNumber" placeholder="请输入纳税识别号"></el-input>
                    </el-form-item>
                    <el-form-item   label='开户账号：' prop="openingAccount">
                        <el-input  class="definput" v-model="form.openingAccount" placeholder="请输入开户账号"></el-input>
                    </el-form-item>
                    <el-form-item   label='联系人：' prop="contacts">
                        <el-input  class="definput" v-model="form.contacts" placeholder="请输入联系人姓名"></el-input>
                    </el-form-item>
                    <el-form-item   label='寄送人：' prop="sender">
                        <el-input  class="definput" v-model="form.sender" placeholder="请输入寄送人姓名"></el-input>
                    </el-form-item>
                </el-col> -->
        <el-col :span="12">
          <el-form-item label="开户银行：" prop="openingBank">
            <el-input
              class="definput"
              v-model="form.openingBank"
              placeholder="请输入开户银行"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位地址：" prop="unitAddress">
            <el-input
              class="definput"
              v-model="form.unitAddress"
              placeholder="请输入单位地址"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户地址：" prop="openingAddress">
            <el-input
              class="definput"
              v-model="form.openingAddress"
              placeholder="请输入开户地址"
            ></el-input>
          </el-form-item>

          <el-form-item label="联系电话：" prop="contactsPhone">
            <el-input
              class="definput"
              v-model="form.contactsPhone"
              placeholder="请输入联系电话"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="0">
        <el-col :span="24">
          <el-form-item label="附件：" prop="fileInfoList">
            <upload2
              ref="uploadFile"
              :limit="10"
              @submitImg="submitFile"
              accept=".pdf"
              :fileList="fileListF"
              :onpreview="true"
            >
              <span class="studiocss">
                <img src="../../../../assets/img/file_icon.png" />
                <span class="uploadtext deffont">点击上传附件</span>
              </span>
              <template slot="ptip">
                <p>只能上传pdf文件,最多上传10个</p>
              </template>
            </upload2>
          </el-form-item>
          <el-form-item label="备注：" prop="notes">
            <el-input
              maxlength="300"
              show-word-limit
              class="definput"
              type="textarea"
              :rows="4"
              v-model="form.notes"
              placeholder="请输入备注内容"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <div class="center">
      <el-button class="submitbtn defaultbtn" @click="beforeClose"
        >取消</el-button
      >
      <el-button
        class="submitbtn defaultbtn"
        type="primary"
        @click="submitAction"
        >提交审核</el-button
      >
    </div>
    <verifyDeparment ref="verifyDeparment" @submit="addData"></verifyDeparment>
  </el-dialog>
</template>

<script>
import {
  addContractinvoicing,
  selectReturnOrRefund,
  contractinvoicingInfo,
  updateContractinvoicing,
} from '@/api/contract/index'
import upload2 from '@/components/common/upload2.vue'
import { getDict } from '@/utils/tools'
import verifyDeparment from '@/components/common/verifyDeparment.vue'
export default {
  components: {
    upload2,
    verifyDeparment,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    contractId: {
      type: String,
      default: '',
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      rules: {
        invoicingAmount: [
          { required: true, message: '请输入开票金额', trigger: 'blur' },
        ],
        taxIdentificationNumber: [
          { required: true, message: '请输入单位税号', trigger: 'blur' },
        ],
        invoiceHeader: [
          { required: true, message: '请输入抬头名称', trigger: 'blur' },
        ],
        invoicingType: [
          { required: true, message: '请选择开票类型', trigger: 'change' },
        ],
      },
      contractAmount: 0,
      noInvoicingTotalAmount: 0,
      form: {
        contractId: this.contractId,
        invoicingAmount: '',
        currency: '',
        invoicingType: '',
        invoiceHeader: '',
        taxIdentificationNumber: '',
        openingBank: '',
        openingAddress: '',
        contactsPhone: '',
        unitAddress: '',
        invoicingDepartmentId: '',
      },
      isSubmit: false,
      options: [],
      options2: [
        {
          id: '1',
          name: '企业',
        },
      ],
      types: [
        {
          id: 1,
          name: '是',
        },
        {
          id: 2,
          name: '否',
        },
      ],
      invoicingTypes: [
        {
          id: 1,
          name: '增值税专用发票',
        },
        {
          id: 2,
          name: '增值税普通发票',
        },
        {
          id: 3,
          name: '电子普通发票',
        },
        {
          id: 4,
          name: '收据',
        },
      ],
      fileListF: [],
    }
  },
  computed: {
    addVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('updateVisible', val)
      },
    },
  },
  created() {
    getDict('Currency').then((result) => {
      this.options = result
    })
  },
  methods: {
    loadDetailData(id) {
      contractinvoicingInfo(id).then((res) => {
        if (res.status == 0) {
          this.form = { ...res.data }
          this.contractAmount = res.data.contractAmount
          if (res.data.contractId) {
            this.loadAmount()
          }
          this.form.currency = this.form.currency == 0 ? '' : this.form.currency
        }
      })
    },
    setContractAmount(data) {
      this.contractAmount = data
      this.loadAmount()
    },
    loadAmount() {
      selectReturnOrRefund({
        id: this.form.contractId || this.contractId,
      }).then((result) => {
        this.noInvoicingTotalAmount = result.data.noInvoicingTotalAmount
      })
    },
    submitFile(fileList) {
      this.fileListF = fileList
    },
    submitAction() {
      this.$refs['addform'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.isEdit) {
          this.updateData()
        } else {
          this.$refs.verifyDeparment.verify()
        }
      })
    },
    closed() {
      var keys = Object.keys(this.form)
      this.$refs['addform'].resetFields()
      keys.forEach((element) => {
        if (element != 'contractId') {
          this.form[element] = ''
        }
      })
      this.fileListF = []
      if (this.$refs.uploadFile) {
        this.$refs.uploadFile.setFileList([])
      }
    },
    beforeClose() {
      this.addVisible = false
      var keys = Object.keys(this.form)
      this.$refs['addform'].resetFields()
      keys.forEach((element) => {
        if (element != 'contractId') {
          this.form[element] = ''
        }
      })
    },
    addData(departmentId) {
      this.form.invoicingDepartmentId = departmentId
      this.isSubmit = true
      addContractinvoicing(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功',
            })
            this.beforeClose()
            this.$emit('submitSuccess')
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    // 更新数据
    updateData() {
      this.isSubmit = true
      updateContractinvoicing(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '修改成功',
            })
            this.beforeClose()
            this.$emit('submitSuccess')
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
  },
}
</script>

<style scoped>
.tips {
  color: rgba(154, 154, 154, 1);
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 12px;
}
.studiocss img {
  width: 16px;
  height: 16px;
  margin-right: 3px;
  margin-top: -3px;
}
.myform /deep/.el-upload__tip {
  margin-top: 0px !important;
  height: 34px;
  line-height: 32px;
}
.uploadtext {
  color: #4285f4;
  cursor: pointer;
}
.w100 {
  width: 100% !important;
}
.center {
  text-align: center;
}
.submitbtn {
  margin-top: 30px;
}
.refundcss /deep/.el-form-item {
  margin-bottom: 18px;
}
.refundcss {
  /* padding-right: 4vw;
    padding-left: 2vw; */
}
</style>
