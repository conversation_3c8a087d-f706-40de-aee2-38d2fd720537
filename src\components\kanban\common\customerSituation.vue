<template>
  <div>
    <el-card class="mokuai">
      <div class="title">
        <textBorder class="biaoti">业务经理客户星级情况</textBorder>
        <el-button
          v-isShow="'kanban:customer:detail'"
          type="text"
          class="anniu bbtn"
          @click="goToCustomerDetail"
          >查看更多</el-button
        >
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        :default-sort="{ prop: 'date', order: 'ascending' }"
      >
        <el-table-column
          prop="chargePersonName"
          label="业务经理"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="relatedCustomerNum"
          label="关联客户数量"
          align="center"
          :formatter="valueformatter"
        >
        </el-table-column>
        <el-table-column
          prop="threeStarCustomer"
          align="center"
          label="3星客户"
          :formatter="valueformatter"
        >
        </el-table-column>
        <el-table-column
          prop="twoStarCustomer"
          align="center"
          label="2星客户"
          :formatter="valueformatter"
        >
        </el-table-column>
        <el-table-column
          prop="oneStarCustomer"
          align="center"
          label="1星客户"
          :formatter="valueformatter"
        >
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import '../detail.css'
import textBorder from '@/components/common/textBorder.vue'
export default {
  components: {
    textBorder,
  },
  data() {
    return {
      tableData: [],
    }
  },
  mounted() {},
  methods: {
    valueformatter(row, column, cellValue, index) {
      return cellValue + '位'
    },
    loadData(data) {
      this.tableData = data
    },
    goToCustomerDetail() {
      this.$emit('gotoDetail')
    },
  },
}
</script>
<style lang="scss" scoped>
.mokuai {
  height: 566px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 12px;
}
.title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.anniu {
  font-size: 14px;
  padding: 0;
}
.left-container {
  display: flex;
}
</style>