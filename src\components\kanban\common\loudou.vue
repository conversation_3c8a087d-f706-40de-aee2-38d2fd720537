<template>
    <div>
        <el-card class="mokuai">
            <div class="title">
                <textBorder class="biaoti">销售漏斗</textBorder>
            </div>
            <el-row>
                <!-- 左边漏斗图 -->
                <el-col :span="12">
                    <div class="loudou" ref="initFunnelChart"></div>
                </el-col>
                <!-- 右边 -->
                <el-col :span="10" :offset="1" >
                    <el-row :gutter="20">
                        <el-col :span="12" v-for="(region, index) in regions" :key="index">
                            <div class="region">
                                <div class="toptu">
                                    <div :class="['circle', ]" :style="{ backgroundColor: region.color }"></div>
                                    <div class="title-text">{{ region.title }}</div>
                                </div>
                                <div :class="['shuzi', ]" :style="{ color: region.color }">{{ region.value }}</div>
                            </div>
                        </el-col>
                    </el-row>
                </el-col>


            </el-row>
        </el-card>
    </div>
</template>

<script>
import '../detail.css';
import * as echarts from 'echarts';
import textBorder from '@/components/common/textBorder.vue';
export default {
    components:{
        textBorder
    },
    data() {
        return {
            regions: [
            { title: '赢单率', value: '', color: '#55C36E' },
            { title: '流失率', value: '', color: '#F36969' },
            { title: '跟进次数', value: '', color: '#4285F4' },
            ]
        }
    },
    mounted() {

    },

    methods: {
        loadData(data){
            console.log('33444' ,data)
            this.regions[0].value = data.winningRate
            this.regions[1].value = data.lossRate
            this.regions[2].value = data.visitCount + '次'
            this.initFunnelChart(data.resultList)
        },
    initFunnelChart(data) {
        this.chart = echarts.init(this.$refs.initFunnelChart);
        const option = {
            color:['#4285F4','#489DD2','#4CAFB9','#52BD97','#56C36E'],
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                return `<div style="display: flex; align-items: center;">
                            <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ${params.color}; margin-right: 5px;"></span>
                            ${params.name} : <span style="font-size: 20px; font-weight: bold; margin-left: 20px;">${params.value-1}</span>
                        </div>`;
            }
            },
            legend: {
                top:'10%',
                right:'30',
                icon: 'rect', 
                itemWidth: 15, 
                itemHeight: 15, 
                orient: "vertical",
                itemGap: 40,
            },
            series: [
                {
                    type: 'funnel',
                    left: '5%',
                    right:'150',
                    gap: 5, 
                    label: {
                        show:false,
                    },
                    labelLine: {
                        show: false
                    },
                    itemStyle: {
                        borderRadius: [5, 5, 5, 5] 
                    },
                    emphasis: {
                        label: {
                            show:false,
                        }
                    },
                    data:[
                        {value:data[0]+1,name:'初步洽谈'},
                        {value:data[1]+1,name:'需求确定'},
                        {value:data[2]+1,name:'方案报价'},
                        {value:data[3]+1,name:'谈判审核'},
                        {value:data[4]+1,name:'赢单'}

                    ]
                }
            ]
        };

        this.chart.setOption(option);
        window.addEventListener('resize',function(){
                this.chart.resize();
            }.bind(this)) 
        
    }
}
}
</script>
<style lang="scss" scoped>
.mokuai{
    height: 458px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    margin-bottom: 12px;
}
.region {
    height: 134px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
    margin-bottom: 40px;
    padding: 20px;
}
.loudou {
    width: 580px;
    height: 350px;
    margin: 0 auto;
}
.toptu {
    display: flex;
    align-items: center;
}

.circle {
    width: 9px; 
    height: 9px;
    border-radius: 50%; 
    background-color: #55C36E; 
    margin-right: 9px; 
}

.title-text {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
}
.shuzi {
    text-align: center;
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 32px;
    color: #55C36E;
}

</style>