<template>
  <el-drawer
    class="mydrawer"
    :visible.sync="visible"
    direction="rtl"
    size="45%"
    title="审核"
    @close="handleClose"
    center
  >
    <div class="drawer-content">
      <el-form class="infoform" ref="form" label-width="120px">
        <el-row :gutter="0" class="width100  pb12 mb20 bbline" type="flex" justify="center">
          <el-col :span="12">
            <el-form-item label="合同金额(万元)：" class="labeltext">
                <span>{{data.contractAmount || '--'}}万元</span>
            </el-form-item>
            <el-form-item label="币种：" class="labeltext">
              <span>{{form.currencyName || '--'}}</span>
            </el-form-item>
            <el-form-item label="退款方式：" class="labeltext">
              <span>{{paymentWays[form.refundWay] || '--'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退款金额(万元)：" class="labeltext">
              <span>{{form.refundAmount || '--'}}万元</span>
            </el-form-item>
            <el-form-item label="实际退款日期：" class="labeltext">
              <span>{{form.actualRefundDate || '--'}}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="0" class="width100  pb12 mb20 bbline"  type="flex" justify="center">
            <el-col :span="11">
              <el-form-item label="退款收款账户：" class="labeltext">
                  <span>{{form.refundAccount || '--'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="退款原因：" class="labeltext">
                  <span>{{form.refundReason || '--'}}</span>
              </el-form-item>
            </el-col>
        </el-row>

        <el-row :gutter="0" class="width100  pb12 mb20 bbline"  type="flex" justify="center">
            <el-col :span="11">
              <el-form-item label="申请人：" class="labeltext">
                  <span>{{form.createByName || '--'}}</span>
              </el-form-item>
              <el-form-item label="备注：" class="labeltext">
                  <span>{{form.notes || '--'}}</span>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="申请时间：" class="labeltext">
                  <span>{{form.createTime || '--'}}</span>
              </el-form-item>
            </el-col>
        </el-row>
        <div class="checkpeople">
          <span class="spanname">审核意见：</span>
          <eltimeline
          :businessId="data.businessId"
          :status="data.status"
          v-if="visible"
        ></eltimeline>
        </div>
      </el-form>
      <div class="btns" v-if="handleType === 1">
        <el-button @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="handleApprove">通过</el-button>
      </div>
    </div>

    <!-- 通过对话框 -->
    <approve-dialog
      :visible.sync="approveDialogVisible"
      @submit="onApproveSubmit"
    ></approve-dialog>

    <!-- 驳回对话框 -->
    <reject-dialog
      :visible.sync="rejectDialogVisible"
      @submit="onRejectSubmit"
    ></reject-dialog>
  </el-drawer>
</template>

<script>
import ApproveDialog from '../../common/approveDialog.vue'
import RejectDialog from '../../common/rejectDialog.vue'
import { approve } from '@/api/reviewCenter';
import eltimeline from '@/components/common/eltimeline.vue'
import {
  contractrefundInfo,
} from '@/api/contract/index'

export default {
  name: 'ReviewDrawer',
  components: {
    ApproveDialog,
    RejectDialog,
    eltimeline
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    handleType: {
      type: Number,
      default: 1,
    },
    data:{
      type:Object,
      default:()=>{
        return new Object()
      }
    }
  },
  data() {
    return {
      approveDialogVisible: false,
      rejectDialogVisible: false,
      form:{},
      paymentWays:{
          1:"现金",
          2:"银行转账",
          3:'微信转账',
          4:'支付宝转账',
          5:'其他'
      },
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    loadinfo(){
      contractrefundInfo(this.data.businessId).then((result) => {
        console.log('ddddffffgggg=====',result);
        this.form = result.data;
      }).catch((err) => {

      });
    },
    handleClose() {
      this.visible = false
    },
    handleApprove() {
      this.approveDialogVisible = true
    },
    handleReject() {
      this.rejectDialogVisible = true
    },
    onApproveSubmit() {

      this.submit({
        id:this.data.id,
        status:3
      })
    },
    onRejectSubmit(data) {
      console.log('驳回提交的数据', data)
      this.submit({
        id:this.data.id,
        status:4,
        remark:data
      })
    },
    submit(par){
      approve(par).then((result) => {
        if (result.data) {
          this.visible = false
          if (par.status == 3) {
             this.msgSuccess('审核通过')
          }else{
            this.msgSuccess('已驳回')
          }
          this.$emit('reload')
        }else{
          this.$message.error(result.msg)
        }
      }).catch((err) => {

      });
    },
  }
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
}
.boxItem {
  display: flex;
  align-items: center;
  line-height: 18px;
}
.title{
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
}
/deep/ .el-drawer {
  .el-drawer__header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
}
.mydrawer /deep/.el-drawer__header{
    text-align: center;
    color: #333333;
}
.btns{
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.mb20{
  margin-bottom: 20px;
}
.spanname {
  width: 110px;
  padding-right: 8px;
  text-align: right;
  flex-shrink: 0;
}
.checkpeople {
  display: flex;
  margin-left: 6px;
}
</style>
