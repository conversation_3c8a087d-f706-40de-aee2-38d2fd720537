<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="30%"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="100px"
    >
      <el-form-item label="标签类型">
        <el-input
          v-model="dataForm.tag"
          placeholder="标签类型"
          disabled
        ></el-input>
      </el-form-item>
      <el-form-item label="标签项名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="标签项名称"></el-input>
      </el-form-item>
      <el-form-item label="标签项编码">
        <el-input v-model="dataForm.code" placeholder="标签项编码"></el-input>
      </el-form-item>
      <el-form-item label="排序号" prop="sortNo">
        <el-input
          type="number"
          v-model="dataForm.sortNo"
          placeholder="排序号"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button
        v-isShow="'sf:business:tagitem:save'"
        type="primary"
        @click="dataFormSubmit()"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: '',
        tagtypeId: '',
        tagtypeCode: '',
        tag: '',
        name: '',
        sortNo: '',
        code: '',
      },
      dataRule: {
        name: [
          { required: true, message: '标签项名称不能为空', trigger: 'blur' },
        ],
        sortNo: [
          {
            required: false,
            message: '排序号，默认正序排列不能为空',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    init(id, tagtypeId, tagtypeCode, tagtypeName) {
      this.dataForm.id = id
      this.dataForm.tagtypeId = tagtypeId
      this.dataForm.tagtypeCode = tagtypeCode
      this.dataForm.tag = tagtypeName
      this.visible = true
      this.$nextTick(async () => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          let res = await this.$axios.get(`/sf/business/tagitem/info/${id}`)
          if (res.status === 0) {
            this.dataForm.tagtypeId = res.data.tagtypeId
            this.dataForm.tagtypeCode = res.data.tagtypeCode
            this.dataForm.name = res.data.name
            this.dataForm.sortNo = res.data.sortNo
            this.dataForm.code = res.data.code
          }
        } else {
          this.dataForm.code = ''
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          var params = {
            id: this.dataForm.id || undefined,
            tagtypeId: this.dataForm.tagtypeId,
            tagtypeCode: this.dataForm.tagtypeCode,
            name: this.dataForm.name,
            sortNo: this.dataForm.sortNo,
            code: this.dataForm.code,
          }
          let url = ''
          if (!this.dataForm.id) {
            url = '/sf/business/tagitem/save'
          } else {
            url = '/sf/business/tagitem/update'
          }
          let res = await this.$axios.post(url, params)
          if (res.status === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshTagItemData')
              },
            })
          } else {
            this.$message.error(res.msg)
          }
        }
      })
    },
  },
}
</script>
