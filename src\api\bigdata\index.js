import request from '@/utils/request'

// 客户信息变化
export function queryCustomerOverviewData(data) {
  return request({
    url: "/crm/controller/customermanager/overview",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

// 客户信息分析
export function queryCustomerIntegrityData(data) {
  return request({
    url: "/crm/controller/customermanager/queryIntegrity",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}
// 业务经理客户情况
export function querySituation(data) {
  return request({
    url: "/crm/controller/customermanager/querySituation",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

// /crm/controller/customermanager/queryStarCustomerList
export function queryStarCustomerList(data) {
  return request({
    url: "/crm/controller/customermanager/queryStarCustomerList",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

// 业务经理看板年度概况
export function businessmanagerOverView(data) {
  return request({
    url: "/crm/controller/businessmanager/overview",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

// 业务经理年度产出、投入
export function performanceInvestment(data) {
  return request({
    url: "/crm/controller/businessmanager/performanceInvestment",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

// 业绩日历
export function performanceCalendar(data) {
  return request({
    url: "/crm/controller/businessmanager/performanceCalendar",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

// 
// 销售漏斗
export function querySalesFunnel(data) {
  return request({
    url: "/crm/controller/businessmanager/querySalesFunnel",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

//业务经理年度销售
export function queryAnnualSales(data) {
  return request({
    url: "/crm/controller/businessmanager/queryAnnualSales",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}
// queryUnitPerformancePage
export function queryUnitPerformancePage(data) {
  return request({
    url: "/crm/controller/businessmanager/queryUnitPerformancePage",
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

export function queryData(data) {
  return request({
    url: "/crm/business/customerbook/queryData",
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

export function queryDataInfo(params) {
  return request({
    url: "/crm/business/customerbook/queryDataInfo",
    method: 'get',
    params
  })
}

export function updateDistribution(data) {
  return request({
    url: '/crm/controller/distribution/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}

// 详情
export function distributionInfo(id) {
  return request({
    url: `/crm/controller/distribution/info/${id}`,
    method: 'get',
  })
}

//  删除发放
export function deleteDistribution(data) {
  return request({
    url: '/crm/controller/distribution/delete',
    method: 'post',
    data,

  })
}

// 发放明细  

export function distributionDetailList(params) {
  return request({
    url: '/crm/controller/distributiondetail/list',
    method: 'get',
    params
  })
}
// 新增发放明细
export function addDistributionDetail(data) {
  return request({
    url: '/crm/controller/distributiondetail/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}
// 删除发放明细
export function deleteDistributionDetail(data) {
  return request({
    url: '/crm/controller/distributiondetail/delete',
    method: 'post',
    data
  })
}
//  /crm/controller/distributiondetail/queryDetailOverview
//  发放明细列表中物品数量、总金额
export function queryDetailOverview(params) {
  return request({
    url: '/crm/controller/distributiondetail/queryDetailOverview',
    method: 'get',
    params
  })
}

// 发放回访列表 /crm/controller/distributionrevisit/list
export function distributionRevisitList(params) {
  return request({
    url: '/crm/controller/distributionrevisit/list',
    method: 'get',
    params
  })
}

// 新增回访
export function addDistributionRevisit(data) {
  return request({
    url: '/crm/controller/distributionrevisit/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}
// 删除回访
export function deleteDistributionRevisit(data) {
  return request({
    url: '/crm/controller/distributionrevisit/delete',
    method: 'post',
    data
  })
}

// /crm/controller/distributiondetail/downloadTemplate
// 发放明细模板下载
export function downloadTemplate(params) {
  return request({
    url: '/crm/controller/distributiondetail/downloadTemplate',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// /crm/controller/distributiondetail/batchImportExcel

export function batchImportExcel(params) {
  return request({
    url: '/crm/controller/distributiondetail/batchImportExcel',
    method: 'get',
    params
  })
}

// 导出筛选数据
export function downloadFilteredData(params) {
  return request({
    url: '/crm/business/customerbook/downloadList',
    method: 'get',
    responseType: 'blob',
    params
  })
}
//导出详细数据
export function downloadDetailedData(params) {
  return request({
    url: '/crm/business/customerbook/downloadDetailList',
    method: 'get',
    responseType: 'blob',
    params
  })
}