<!-- 订单头部总额 -->
<template>
  <div class="enveloright">
  <section class="enveloH1" v-for="(item, index) in moneyList" :key="index">
    <span>{{item.label}}：</span><span><i>{{item.value}}</i>元</span>
  </section>
  </div>
</template>
<script>
export default {
  props: ['moneyList']
}
</script>

<style lang="scss" scoped>
.enveloright {
  position: absolute;
  right: 33px;
  top: 42px;
  font-size: 14px;
  & .enveloH1:last-child{
    margin-right: 0px;
  }
  .enveloH1 {
    display: inline-block;
    margin-right: 30px;
    i {
      margin-right: 5px;
      color: #ef5496;
    }
  }
}
</style>
