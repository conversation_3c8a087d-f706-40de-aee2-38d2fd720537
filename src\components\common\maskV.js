import Vue from 'vue'
import maskV from './maskV.vue'
let maskVConstructor = Vue.extend(maskV)
let instant
let seed = 0
const maskVObject = (options) => {
  if (!(typeof options === 'object' && options !== null)) {
    throw new Error("请传入对象")
  }
  let userOnClose = options.onClose
  let id = seed++
  instant = new maskVConstructor({
    data: options
  })
  instant.onClose = function () {
    if (typeof userOnClose === 'function') {
      userOnClose()
    }
  }
  instant.id = id
  instant.$mount()
  document.body.appendChild(instant.$el)
  return instant
}

maskVObject.show = (options) => {
  return maskVObject({
    ...options
  })
}

export default maskVObject
