<template>
    <el-dialog :title="dialogTitle" center width="768px" :visible.sync="dialogFormVisible">
        <div class="mainform">
            <div class="confirm-text">
                {{ confirmText }}
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button type="danger" @click="handleLoseOpportunity">否，输单</el-button>
            <el-button type="primary" @click="handleContinue">确认</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { getDict } from '@/utils/tools'
import { updateOpportunity } from "@/api/clientMaintenance/opportunity";
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        dialogFormVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('updateVisible', value)
            }
        }
    },
    data() {
        return {
            formLabelWidth: '90px',
            options:[],
            form: {
                opportunityStage: "",
            },
            currentStage: '',
            dialogTitle: '阶段确认',
            confirmText: ''
        }
    },
    methods:{
        getTypeList(){
            getDict('OpportunityStage').then((result) => {
                this.options = result;
            })
        },
        updateData(data){
            this.form.id = data.id;
            this.form.opportunityStage = data.opportunityStage;
            this.currentStage = data.stage;
            this.setConfirmText(data.stage);
            console.log('阶段确认数据==',this.form);
        },
        setConfirmText(stageName) {
            const stageConfirmMap = {
                '初步接洽': '是否完成该机会的初步接洽？',
                '需求确认': '是否完成该机会的需求确认？',
                '方案报价': '是否完成该机会的方案报价？',
                '谈判审核': '是否完成该机会的谈判审核？'
            }
            this.confirmText = stageConfirmMap[stageName] || '是否完成该机会的初步接洽？'
        },
        getNextStageId(currentStage) {
            const stageProgressMap = {
                '初步接洽': this.getStageIdByName('需求确认'),
                '需求确认': this.getStageIdByName('方案报价'),
                '方案报价': this.getStageIdByName('谈判审核'),
                '谈判审核': this.getStageIdByName('赢单')
            }
            return stageProgressMap[currentStage]
        },
        getStageIdByName(stageName) {
            const stage = this.options.find(item => item.name === stageName)
            return stage ? stage.id : null
        },
        handleContinue(){
            // 确认继续，推进到下一阶段
            const nextStageId = this.getNextStageId(this.currentStage)
            if (nextStageId) {
                this.form.opportunityStage = nextStageId
                this.submitStageUpdate('机会阶段已变更')
            }
        },
        handleLoseOpportunity(){
            // 设置为输单状态
            const loseStageId = this.getStageIdByName('输单')
            if (loseStageId) {
                this.form.opportunityStage = loseStageId
                this.submitStageUpdate('机会已变更为输单')
            }
        },
        submitStageUpdate(successMessage){
            console.log("阶段更新-提交",this.form);
            updateOpportunity(this.form).then((result) => {
                if (result.data) {
                    this.$message.success(successMessage)
                    this.$emit('successBlock');
                }
            })
        },
    }
}
</script>



<style lang="scss" scoped>
@import "@/styles/mixin.scss";

 .mainform {
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.confirm-text {
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    text-align: center;
    line-height: 1.5;
}

.timediv {
    display: flex;
    justify-content: flex-start;
}

.datepicker /deep/.el-input__prefix {
    left: calc(100% - 30px);
}

.datepicker /deep/.el-input__inner {
    @include formatFont13;
    padding-left: 15px !important;
}

.datepicker /deep/.el-input__icon {
    line-height: 34px;
}

.mainform /deep/.el-form-item {
    margin-bottom: 30px;
    width: 350px;
}

.mainform /deep/.el-form-item__label {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400 !important;
    color: #333333;
    line-height: 34px;
}

.mainform /deep/.el-form-item__content {
    line-height: 34px;
}

.mainform /deep/.el-textarea__inner {
    @include formatFont13;
    line-height: 1.5;
}

.mainform /deep/.el-input__inner {
    @include formatFont13;
}

.canclebtn {
    margin-top: 7px;
    margin-left: 10px;
    width: 20px;
    height: 20px;
}
</style>
