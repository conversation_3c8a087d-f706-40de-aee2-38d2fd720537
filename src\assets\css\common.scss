@import './element.scss';
/*标签色系,tg为tag标签缩写*/
$tgBlueMain: #67c23a; //标签主要蓝色
$tgYellowMain: #FF8C10; //标签主要黄色
$tgPurpleMain: #8D57FA; //标签主要紫色
$tgYellowShadow: #803102; //标签投影黄色
$tgPurpleShadow: #581F83; //标签投影紫色
$tgBlueGradD: #3778FF; //标签渐变深蓝
$tgBlueGradL: #4B91FC; //标签渐变浅蓝
$tgYellowGradD: #F66A1A; //标签渐变深黄
$tgYellowGradL: #FF8C10; //标签渐变浅黄
$tgPurpleGradD: #6643C9; //标签渐变深紫
$tgPurpleGradL: #8D57FA; //标签渐变浅紫
$colorWhite: #fff;

html,
body,
#app {
	width: 100%;
	height: 100%;
}

.el-dialog__body {
	min-height: 154px !important;
	overflow: auto;
}

.bbline {
	border-bottom: 1px solid #f0f0f0;
}

.mt10 {
	margin-top: 10px;
}

.mt30 {
	margin-top: 30px;
}

.mb10 {
	margin-bottom: 10px;
}

.mb30 {
	margin-bottom: 30px;
}

.pb5 {
	padding-bottom: 5px;
}

.pb20 {
	padding-bottom: 20px;
}

.pt20 {
	padding-top: 20px;
}

.mb20 {
	margin-bottom: 20px;
}

.mt20 {
	margin-left: 20px;
	display: inline-block
}

.mr10 {
	margin-right: 10px;
}

.tree-icon {
	position: absolute;
	right: 0;
	margin-top: -9px;
}

.el-select {
	width: 100%;
}

.alink {
	color: #3778ff;
	text-decoration: none;
}

/*
* @Desc 页面列表状态 
*/
.tg-blue {
	color: $tgBlueMain;
	background-color: #f0f9eb;
	border-color: #e1f3d8;
}

/*
* 页面 el-input
*/
.input-30 {
	width: 30%;
}

.input-40 {
	width: 40%;
}

.input-50 {
	width: 50%;
}

.input-60 {
	width: 60%;
}

.input-100 {
	width: 100%
}

.input-160 {
	width: 160px !important;
}

.input-230 {
	width: 230px !important;
}

.input-20 {
	width: 20% !important;
	display: inline-block;
}

.el-tree-node {
	margin-top: 5px !important;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
	background-color: #eff0ff;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content .sblock {
	background-color: #eff0ff;
}

.dropMore {
	color: #409EFF;
	cursor: pointer;
	font-size: 12px;
}

// 导入
.el-icon-my-inport {
	background: url(../img/icon/inport_icon.png) no-repeat;
	font-size: 14px;
	background-size: contain;
}

.el-icon-my-inport:before {
	content: "替";
	font-size: 14px;
	visibility: hidden;
}

//下载
.el-icon-my-download {
	background: url(../img/icon/download_icon.png) no-repeat;
	font-size: 14px;
	background-size: contain;
}

.el-icon-my-download:before {
	content: "替";
	font-size: 14px;
	visibility: hidden;
}

// 退款
.el-icon-my-refund {
	background: url(../img/icon/refund-icon.png) no-repeat;
	font-size: 14px;
	background-size: contain;
}

.el-icon-my-refund:before {
	content: "替";
	font-size: 14px;
	visibility: hidden;
}

//biling-icon.png
.el-icon-my-billing {
	background: url(../img/icon/biling-icon.png) no-repeat;
	font-size: 14px;
	background-size: contain;
}

.el-icon-my-billing:before {
	content: "替";
	font-size: 14px;
	visibility: hidden;
}