<template>
  <el-dialog
    title="消息"
    :visible.sync="dialogVisible"
    width="884px"
    center
    :before-close="handleClose"
    class="todo-dialog"
  >
    <div class="todo-content">
      <left-tabs
        :tabs="todoTabs"
        :active-tab="activeTab"
        @tab-change="handleTabChange"
      >
        <template #default="{ activeTab }">
          <component :is="currentComponent" :key="componentKey" />
        </template>
      </left-tabs>
    </div>

  </el-dialog>
</template>

<script>
import LeftTabs from './LeftTabs.vue'
import Schedule from '../message/Schedule.vue'
import customerTime from '../message/customerTime.vue'
import followVisit from '../message/followVisit.vue'
import Opportunity from '../message/Opportunity.vue'
import contractStatus from '../message/contractStatus.vue'
import Task from '../message/Task.vue'
import WorkOrder from '../message/WorkOrder.vue'
import ContractInvoice from '../message/ContractInvoice.vue'
import ContractRefund from '../message/ContractRefund.vue'
import returnFunds from '../message/returnFunds.vue'
import projectCost from '../message/Cost.vue'
import BookOrder from '../message/BookOrder.vue'
import planAndSummary from '../message/planAndSummary.vue'

export default {
  components: {
    LeftTabs,
    Schedule,
    customerTime,
    followVisit,
    Opportunity,
    contractStatus,
    Task,
    WorkOrder,
    ContractInvoice,
    ContractRefund,
    returnFunds,
    projectCost,
    BookOrder,
    planAndSummary
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      activeTab: 1,
      componentKey: 0, 
      todoTabs: [
        { label: '日程', value: 1 },
        { label: '客户重要日期', value: 2 },
        { label: '跟进与拜访', value: 3 },
        { label: '机会阶段', value: 4 },
        { label: '合同状态', value: 5 },
        { label: '项目任务', value: 6 },
        { label: '工单', value: 7 },
        { label: '合同开票', value: 8 },
        { label: '合同回款', value: 9 },
        { label: '合同退款', value: 10 },
        { label: '项目成本', value: 11 },
        { label: '教材报订', value: 12 },
        { label: '计划与总结', value: 13 },
      ]
    }
  },
  computed: {
    currentComponent() {
      const componentMap = {
        1: 'Schedule',
        2: 'customerTime',
        3: 'followVisit',
        4: 'Opportunity',
        5: 'contractStatus',
        6: 'Task',
        7: 'WorkOrder',
        8: 'ContractInvoice',
        9: 'returnFunds',
        10: 'ContractRefund',
        11: 'projectCost',
        12: 'BookOrder',
        13: 'planAndSummary'
      }
      return componentMap[this.activeTab]
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        // 弹框打开后，强制重新渲染组件
        this.componentKey++
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.activeTab = 1 
      this.$emit('update:visible', false)
    },
    handleTabChange(tab) {
      this.activeTab = tab.value
      console.log('切换到标签页:', tab)
    }
  }
}
</script>

<style lang="scss" scoped>
.todo-content {
  height: 500px;
}
:deep(){
  .el-dialog__body{
    padding: 0 !important;
  }
}
</style>
