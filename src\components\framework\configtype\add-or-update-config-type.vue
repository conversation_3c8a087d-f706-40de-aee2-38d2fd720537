<!-- 配置管理-新增修改弹框 -->
<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="30%">
    <el-form :model="dataForm" :rules="Rule.CONFIG_ADD" ref="dataForm" label-width="100px">
      <el-form-item label="所属类型" prop="parentId" v-if="dataForm.parentId!=0">
        <el-input v-model="dataForm.parentName" placeholder="所属类型"></el-input>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="名称" ></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sortNo">
        <el-input-number type="number" :min="0" v-model="dataForm.sortNo" placeholder="排序"></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        parentId: '',
        parentName: '',
        name: '',
        sortNo: '',
        systemic: ''
      }
    }
  },
  methods: {
    init (id, parentId, parentName) {
      console.log(id, parentId, parentName)
      this.dataForm.id = id
      this.dataForm.parentId = parentId || 0
      this.dataForm.parentName = parentName
      this.visible = true
      this.$nextTick(async () => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          let data = {
            id: this.dataForm.id
          }
          let res = await this.$axios.get('/sf/business/configtype/info', { params: data })
          if (res.status === 0) {
            this.dataForm.parentId = res.data.parentId
            this.dataForm.name = res.data.name
            this.dataForm.sortNo = res.data.sortNo
            this.dataForm.systemic = res.data.systemic
          }
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          var params = {
            'id': this.dataForm.id || undefined,
            'parentId': this.dataForm.parentId,
            'name': this.dataForm.name,
            'sortNo': this.dataForm.sortNo,
            'systemic': this.dataForm.systemic
          }
          let url = !this.dataForm.id ? '/sf/business/configtype/save' : '/sf/business/configtype/update'
          let res = await this.$axios.post(`${url}`, params);
          if (res.status === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshConfigTypeData')
              }
            })
          } else {
            this.$message.error(res.msg)
          }
        }
      })
    }
  }
}
</script>
