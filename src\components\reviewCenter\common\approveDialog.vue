<template>
  <el-dialog
    title="通过"
    :visible.sync="visible"
    width="500px"
    @close="handleClose"
    append-to-body
    center
  >
    <!-- <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="附件：">
        <upload2 :fileMaxSize='30' ref="upload2" @submitImg="submitFile" accept=".pdf,.doc,.docx,.xlsx"
          :fileList="fileList">
          <span class="studiocss">
            <img src="../../../../assets/img/file_icon.png">
            <span class="uploadtext deffont">点击上传附件</span>
          </span>
          <template slot="ptip">
            <p>只能上传pdf,doc,docx,xlsx文件</p>
          </template>
        </upload2>
      </el-form-item>
    </el-form> -->
    <p class="pcc">是否确认通过审核？</p>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submit">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import upload2 from '@/components/common/upload2.vue'

export default {
  name: 'ApproveDialog',
  components: {
    upload2
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      rules: {
        fileInfoList: [
          { required: true, message: '请上传附件', trigger: 'change' }
        ]
      },
      form: {
        fileInfoList: []
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    submitFile(fileList) {
      this.fileList = fileList
    },
    submit() {
      // if (this.fileList.length > 0) {
      //   this.fileList.forEach(item => {
      //     this.form.fileInfoList.push({
      //       ...item,
      //       fileType: this.getFileTypeNum(item.url)
      //     })
      //   })
      // }
      this.$emit('submit')
      this.handleClose()
    },
    getFileTypeNum(url) {
      const ext = url.split('.').pop().toLowerCase()
      if (['pdf', 'doc', 'docx', 'xlsx'].includes(ext)) {
        return 3
      }
      return 3
    }
  }
}
</script>

<style lang="scss" scoped>
.studiocss img {
  width: 16px;
  height: 16px;
  margin-right: 3px;
  margin-top: -3px;
}
.uploadtext {
  color: #4285F4;
  cursor: pointer;
}
</style>
