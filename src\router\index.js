import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '../components/login.vue'
import home from '../components/home/<USER>'
/* Layout */
import Layout from '@/layout'
Vue.use(VueRouter)
// export const asyncRoutes = [
//   {
//     path: '/home',
//     component: Layout,
//     meta: { title: '首页', icon: 'el-icon-s-help' },
//     children: [
//       {
//         path: '',
//         component: () => HomeIndex,
//         meta: { title: '首页' },
//       },
//     ]
//   },
//   {
//     path: '/basesystem',
//     meta: { title: '基本管理', icon: 'system' },
//     component: Layout,
//     redirect: '/basesystem/policy',
//     children: [
//       {
//         path: '/basesystem/policy',
//         name: 'policy',
//         meta: { title: '政策管理' },
//         component: () => import('@/components/basesystem/policy'),
//       },
//       {
//         path: '/basesystem/standard',
//         name: 'standard',
//         meta: { title: '标准管理' },
//         component: () => import('@/components/basesystem/standard'),
//       },
//       {
//         path: '/basesystem/banner',
//         name: 'banner',
//         meta: { title: 'banner管理' },
//         component: () => import('@/components/basesystem/banner'),
//       },
//     ]
//   },
//   {
//     path: '/framework',
//     component: Layout,
//     meta: { title: '系统管理', icon: 'el-icon-setting' },
//     redirect: '/framework/appManage',
//     children: [
//       {
//         path: '/framework/appManage',
//         component: () => import('@/components/framework/appManage'),
//         meta: { title: 'APP版本管理' },

//       },
//       {
//         path: '/framework/article',
//         component: () => import('@/components/framework/article'),
//         meta: { title: '文章管理' }
//       },
//       {
//         path: '/framework/application',
//         component: () => import('@/components/framework/application'),
//         meta: { title: '应用平台管理' }
//       },
//       {
//         path: '/framework/resource',
//         component: () => import('@/components/framework/resource'),
//         meta: { title: '资源管理' }
//       },
//       {
//         path: '/framework/tagtype',
//         component: () => import('@/components/framework/tagtype'),
//         meta: { title: '标签管理' }
//       },
//       {
//         path: '/framework/configtype',
//         component: () => import('@/components/framework/configtype'),
//         meta: { title: '配置管理' }
//       },

//       {
//         path: '/framework/roleposition',
//         component: () => import('@/components/framework/roleposition'),
//         meta: { title: '角色管理' }
//       },
//       {
//         path: '/framework/user',
//         name: 'User',
//         component: () => import('@/components/framework/user'),
//         meta: { title: '用户管理' }   //用户管理
//       },
//       {
//         path: '/framework/identity',
//         name: 'identity',
//         component: () => import('@/components/framework/identity'),
//         meta: { title: '身份管理' }   //用户管理
//       },
//       {
//         path: '/framework/log',
//         component: () => import('@/components/framework/index'),
//         meta: { title: '日志管理' },
//         redirect: '/framework/operationlog',
//         children: [
//           {
//             path: '/framework/operationlog',
//             component: () => import('@/components/framework/operationlog'),
//             meta: { title: '操作日志' },
//           },
//           {
//             path: '/framework/loginlog',
//             component: () => import('@/components/framework/loginlog'),
//             meta: { title: '登录日志' },
//           }
//         ]
//       },
//     ]
//   },
// ]

export const constantRoutes = [

  // {
  //   path: '/clientManagement',
  //   meta: { title: '客户管理', icon: 'kehu' },
  //   component: Layout,
  //   redirect: '/clientManagement/customer/index',
  //   children: [
  //     {
  //       path: '/clientManagement/customer/index',
  //       name: 'customer',
  //       meta: { title: '客户管理' },
  //       component: () => import('@/components/clientManagement/customer/index'),
  //     },
  //     {
  //       path: '/clientManagement/customer/add',
  //       name: 'addCustomer',
  //       meta: { title: '新增客户' },
  //       component: () => import('@/components/clientManagement/customer/add'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientManagement/customer/detail',
  //       name: 'customerDetail',
  //       meta: { title: '客户详情' },
  //       component: () => import('@/components/clientManagement/customer/detail'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientManagement/unit/index',
  //       name: 'unit',
  //       meta: { title: '单位管理' },
  //       component: () => import('@/components/clientManagement/unit/index'),
  //     },
  //     {
  //       path: '/clientManagement/unit/detail',
  //       name: 'unitDetail',
  //       meta: { title: '单位详情' },
  //       component: () => import('@/components/clientManagement/unit/detail'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientManagement/unit/unitSystem',
  //       name: 'unitSystem',
  //       meta: { title: '结构管理' },
  //       component: () => import('@/components/clientManagement/unit/unitSystem'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientManagement/unit/unitChart',
  //       name: 'unitChart',
  //       meta: { title: '架构图预览' },
  //       component: () => import('@/components/clientManagement/unit/components/unitEchart'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientManagement/unit/components/unitTreeChart',
  //       name: 'unitTreeChart',
  //       meta: { title: '树形架构图' },
  //       component: () => import('@/components/clientManagement/unit/components/unitTreeChart'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientManagement/unit/add',
  //       name: 'unitAdd',
  //       meta: { title: '新建单位' },
  //       component: () => import('@/components/clientManagement/unit/add'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientManagement/recycle-bin/index',
  //       name: 'recycle-bin',
  //       meta: { title: '回收站' },
  //       component: () => import('@/components/clientManagement/recycle-bin/index'),
  //     },

  //   ]
  // },
  // {
  //   path: '/clientMaintenance',
  //   meta: { title: '客户维护', icon: 'kehuweihu' },
  //   component: Layout,
  //   redirect: '/clientMaintenance/followVisit/index',
  //   children: [
  //     {
  //       path: '/clientMaintenance/followVisit/index',
  //       name: 'followVisit',
  //       meta: { title: '跟进与拜访' },
  //       component: () => import('@/components/clientMaintenance/followVisit/index'),
  //     },
  //     {
  //       path: '/clientMaintenance/followVisit/add',
  //       name: 'addVisit',
  //       meta: { title: '新增拜访' },
  //       component: () => import('@/components/clientMaintenance/followVisit/add'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientMaintenance/salesLead/index',
  //       name: 'salesLead',
  //       meta: { title: '销售机会' },
  //       component: () => import('@/components/clientMaintenance/salesLead/index'),
  //     },
  //     {
  //       path: '/clientMaintenance/salesLead/add',
  //       name: 'addLead',
  //       meta: { title: '新增机会' },
  //       component: () => import('@/components/clientMaintenance/salesLead/add'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientMaintenance/salesLead/detail',
  //       name: 'leadDetail',
  //       meta: { title: '机会详情' },
  //       component: () => import('@/components/clientMaintenance/salesLead/detail'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientMaintenance/give/index',
  //       name: 'give',
  //       meta: { title: '发放' },
  //       component: () => import('@/components/clientMaintenance/give/index'),
  //     },
  //     {
  //       path: '/clientMaintenance/give/add',
  //       name: 'addGive',
  //       meta: { title: '新增发放' },
  //       component: () => import('@/components/clientMaintenance/give/add'),
  //       hidden: true,
  //     },
  //     {
  //       path: '/clientMaintenance/give/detail',
  //       name: 'giveDetail',
  //       meta: { title: '发放详情' },
  //       component: () => import('@/components/clientMaintenance/give/detail'),
  //       hidden: true,
  //     },

  //   ]
  // },
  // {
  //   path: '/projectManagement',
  //   meta: { title: '项目管理', icon: 'peoject' },
  //   component: Layout,
  //   redirect: '/projectManagement/project/index',
  //   children: [
  //     {
  //       path: '/projectManagement/project/index',
  //       name: 'project',
  //       meta: { title: '项目管理' },
  //       component: () => import('@/components/projectManagement/project/index'),
  //     },
  //     {
  //       path: '/projectManagement/project/add',
  //       name: 'addProject',
  //       meta: { title: '项目管理' },
  //       component: () => import('@/components/projectManagement/project/add'),
  //       hidden: true
  //     },

  //     {
  //       path: '/projectManagement/stage/taskadd',
  //       name: 'project',
  //       meta: { title: '新建任务' },
  //       component: () => import('@/components/projectManagement/stage/taskadd'),
  //       hidden: true
  //     },

  //     {
  //       path: '/projectManagement/stage/index',
  //       name: 'stage',
  //       meta: { title: '阶段管理' },
  //       component: () => import('@/components/projectManagement/stage/index'),
  //       hidden: true
  //     },


  //     {
  //       path: '/projectManagement/contract/index',
  //       name: 'contract',
  //       meta: { title: '合同管理' },
  //       component: () => import('@/components/projectManagement/contract/index'),
  //     },
  //     {
  //       path: '/projectManagement/template/index',
  //       name: 'template',
  //       meta: { title: '模版管理' },
  //       component: () => import('@/components/projectManagement/template/index'),
  //     },
  //     {
  //       path: '/projectManagement/contract/add',
  //       name: 'addContract',
  //       meta: { title: '新建合同' },
  //       component: () => import('@/components/projectManagement/contract/add'),
  //       hidden: true
  //     },
  //     {
  //       path: '/projectManagement/contract/detail',
  //       name: 'contractDetail',
  //       meta: { title: '合同详情' },
  //       component: () => import('@/components/projectManagement/contract/detail'),
  //       hidden: true
  //     },
  //     {
  //       path: '/projectManagement/template/add',
  //       name: 'templateAdd',
  //       meta: { title: '新增模版' },
  //       component: () => import('@/components/projectManagement/template/add'),
  //       hidden: true
  //     },
  //   ]
  // },
  // {
  //   path: '/subscribe',
  //   meta: { title: '教材报订', icon: 'peoject' },
  //   component: Layout,
  //   redirect: '/subscribe/index',
  //   children: [
  //     {
  //       path: '/subscribe/index',
  //       name: 'subscribe',
  //       meta: { title: '教材报订' },
  //       component: () => import('@/components/subscribe/index'),
  //     },
  //     {
  //       path: '/subscribe/add',
  //       name: 'subscribeAdd',
  //       meta: { title: '新增报订' },
  //       component: () => import('@/components/subscribe/add'),
  //     },
  //   ]
  // },
  // {
  //   path: '/reviewCenter',
  //   meta: { title: '审核中心', icon: 'peoject' },
  //   component: Layout,
  //   redirect: '/reviewCenter/invoice/index',
  //   children: [
  //     {
  //       path: '/reviewCenter/invoice/index',
  //       name: 'reviewCenter',
  //       meta: { title: '开票审核' },
  //       component: () => import('@/components/reviewCenter/invoice/index'),
  //     },
  //   ]
  // },



  // {
  //   path: '/workOrder',
  //   meta: { title: '工单管理', icon: 'gongdan' },
  //   component: Layout,
  //   redirect: '/workOrder/index',
  //   children: [
  //     {
  //       path: '/workOrder/index',
  //       name: 'workOrder',
  //       meta: { title: '工单管理' },
  //       component: () => import('@/components/workOrder/index'),
  //     },
  //     {
  //       path: '/workOrder/add',
  //       name: 'addWorkOrder',
  //       meta: { title: '新增工单' },
  //       component: () => import('@/components/workOrder/add'),
  //       hidden: true
  //     },
  //   ]
  // },
  // {
  //   path: '/studyShare',
  //   meta: { title: '学习与分享', icon: 'study' },
  //   component: Layout,
  //   redirect: '/studyShare/caseLibrary/index',
  //   children: [
  //     {
  //       path: '/studyShare/caseLibrary/index',
  //       name: 'studyShare',
  //       meta: { title: '学习与分享' },
  //       component: () => import('@/components/studyShare/caseLibrary/index'),
  //     },
  //     {
  //       path: '/studyShare/caseLibrary/rank',
  //       name: 'studyRank',
  //       meta: { title: '排行' },
  //       component: () => import('@/components/studyShare/caseLibrary/rank'),
  //       hidden: true
  //     },
  //     {
  //       path: '/studyShare/caseLibrary/detail',
  //       name: 'shareDeatil',
  //       meta: { title: '分享详情' },
  //       component: () => import('@/components/studyShare/caseLibrary/detail'),
  //       hidden: true
  //     },
  //     {
  //       path: '/studyShare/caseLibrary/add',
  //       name: 'addStudyShare',
  //       meta: { title: '新增工单' },
  //       component: () => import('@/components/studyShare/caseLibrary/add'),
  //       hidden: true
  //     },
  //   ]
  // },
  // {
  //   path: '/goal',
  //   meta: { title: '目标管理', icon: 'mubiao' },
  //   component: Layout,
  //   redirect: '/goal/show/index',
  //   children: [
  //     {
  //       path: '/goal/show/index',
  //       name: 'goal',
  //       meta: { title: '目标排行' },
  //       component: () => import('@/components/goal/show/index'),
  //     },
  //     {
  //       path: '/goal/show/detail',
  //       name: 'goalDetail',
  //       meta: { title: '目标展示' },
  //       component: () => import('@/components/goal/show/detail'),
  //     },
  //     {
  //       path: '/goal/show/statistics',
  //       name: 'statistics',
  //       meta: { title: '目标汇总' },
  //       component: () => import('@/components/goal/show/statistics'),
  //       hidden: true,
  //     },

  //     {
  //       path: '/goal/set/personal',
  //       name: 'setgoal',
  //       meta: { title: '目标设置' },
  //       component: () => import('@/components/goal/set/personal'),
  //     },
  //     {
  //       path: '/goal/set/group',
  //       name: 'setgoal',
  //       meta: { title: '目标设置查看' },
  //       component: () => import('@/components/goal/set/group'),
  //     },
  //   ]
  // },
  // {
  //   path: '/busactive',
  //   component: Layout,
  //   meta: { title: '商务活动', icon: 'set' },
  //   redirect: '/busactive/index',
  //   children: [
  //     {
  //       path: '/busactive/index',
  //       component: () => import('@/components/busactive/index'),
  //       meta: { title: '教材数据' }
  //     },
  //     {
  //       path: '/bigdata/bookopp',
  //       component: () => import('@/components/bigdata/bookopp'),
  //       meta: { title: '教材数据' }
  //     },
  //     {
  //       path: '/bigdata/datadetail',
  //       component: () => import('@/components/bigdata/datadetail'),
  //       meta: { title: '教材数据' },
  //       hidden:true
  //     },
  //   ]
  // },

  // {
  //     path: '/kanban',
  //     meta: { title: '大数据', icon: 'mubiao' },
  //     component: Layout,
  //     redirect: '/kanban/index',
  //     children: [
  //         {
  //           path: '/kanban/index',
  //           component: () => import('@/components/kanban/index'),
  //           meta: { title: '业务经理大数据' }
  //         },
  //         {
  //           path: '/kanban/detail',
  //           component: () => import('@/components/kanban/detail'),
  //           meta: { title: '个人数据' }
  //         },
  //         {
  //           path: '/kanban/common/unitDetails',
  //           component: () => import('@/components/kanban/common/unitDetails'),
  //           meta: { title: '单位教材业绩明细' }
  //         },
  //         {
  //           path: '/kanban/common/customerDeatil',
  //           component: () => import('@/components/kanban/common/customerDeatil'),
  //           meta: { title: '客户详情' }
  //         },
  //         {
  //           path: '/kanban/kehudetail',
  //           component: () => import('@/components/kanban/kehudetail'),
  //           meta: { title: '客户看板' }
  //         },
  //       ]
  //    },
  {
    path: '/login',
    component: () => import('@/components/login'),
    hidden: true
  },
  {
    path: '/',
    redirect: '/home',
    component: () => home,
  },
  {
    path: '/404',
    component: () => import('@/components/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/components/error/401'),
    hidden: true
  },
]
const createRouter = () => new VueRouter({
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}
const router = createRouter()
export default router
