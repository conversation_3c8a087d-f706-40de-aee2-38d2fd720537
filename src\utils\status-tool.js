export const contractStatus = [
    { value: '', label: '全部' },
    { value: 1, label: '制作中' },
    { value: 2, label: '签订中' },
    { value: 3, label: '备案中' },
    { value: 4, label: '已归档' },
]

export const contractStatusMap = contractStatus.reduce((map, item) => {
    map[item.value] = item.label;
    return map;
}, {});

export const commonStatus = [
    { value: 1, label: '待审核' },
    { value: 2, label: '审核中' },
    { value: 3, label: '已通过' },
    { value: 4, label: '已驳回' },
    { value: 5, label: '已撤销' },
]

export const commonStatusMap = commonStatus.reduce((map, item) => {
    map[item.value] = item.label;
    return map;
}, {});

export const bookQuantityRange = [
    { value: 1, label: '100册以内' },
    { value: 2, label: '100-300册' },
    { value: 3, label: '300-500册' },
    { value: 4, label: '500-1000册' },
    { value: 5, label: '1000册以上' },
]

export const bookQuantityRangeMap = bookQuantityRange.reduce((map, item) => {
    map[item.value] = item.label;
    return map;
}, {});

export const confirmStatus = [
    { value: 1, label: '未报批' },
    { value: 3, label: '已通过' },
    { value: 2, label: '审核中' },
    { value: 4, label: '被驳回' },
]

export const confirmStatusMap = confirmStatus.reduce((map, item) => {
    map[item.value] = item.label;
    return map;
}, {});
//审核中心状态选项
export const centerStatus = [
    { value: 1, label: '审核中' },
    { value: 2, label: '已通过' },
    { value: 3, label: '被驳回' },
]

export const centerStatusMap = centerStatus.reduce((map, item) => {
    map[item.value] = item.label;
    return map;
}, {});

export const ruleTypeStatus = [
    { value: 1, label: '合同开票' },
    { value: 2, label: '合同回款' },
    { value: 3, label: '合同退款' },
    { value: 4, label: '教材报订' },
    { value: 5, label: '成本审核' },
]

export const ruleTypeStatusMap = ruleTypeStatus.reduce((map, item) => {
    map[item.value] = item.label;
    return map;
}, {});

// 工作产出类型选项
export const workOutputType = [
    { value: 1, label: '成品时长', unit: '分钟' },
    { value: 2, label: '课件页数', unit: '页' },
    { value: 3, label: '拍摄讲数', unit: '讲' },
    { value: 4, label: '组稿页数', unit: '页' },
    { value: 5, label: '排版页数', unit: '页' },
    { value: 6, label: '校对页数', unit: '页' },
    { value: 7, label: '资源配套', unit: '个' },
    { value: 8, label: 'UI设计', unit: '' },
    { value: 9, label: '原型制作', unit: '' },
    { value: 10, label: '功能实现', unit: '' },
    { value: 11, label: '软件测试', unit: '' },
    { value: 12, label: '教案', unit: '个' },
    { value: 13, label: '试题', unit: '个' },
]

export const workOutputTypeMap = workOutputType.reduce((map, item) => {
    map[item.value] = item.label;
    return map;
}, {});

export const workOutputUnitMap = workOutputType.reduce((map, item) => {
    map[item.value] = item.unit;
    return map;
}, {});

// 审批状态选项
export const approvalStatus = [
    { value: '', label: '全部' },
    { value: 1, label: '待审核' },
    { value: 2, label: '审核中' },
    { value: 3, label: '已通过' },
    { value: 4, label: '被驳回' },
    { value: 5, label: '已撤销' },
]

export const approvalStatusMap = approvalStatus.reduce((map, item) => {
    map[item.value] = item.label;
    return map;
}, {});

// 审批状态颜色映射
export const approvalStatusColorMap = {
    1: 'c1', // 待审核 - 蓝色
    2: 'c2', // 审核中 - 橙色
    3: 'c3', // 已通过 - 绿色
    4: 'c4', // 被驳回 - 红色
    5: 'c5', // 已撤销 - 红色
};






