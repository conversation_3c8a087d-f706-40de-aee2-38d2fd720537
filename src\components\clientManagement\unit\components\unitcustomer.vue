<template>
  <el-dialog
    :title="title + `(${unitName})`"
    top="50px"
    width="80%"
    :visible.sync="dialogTableVisible"
  >
    <div class="page-header">
      <el-form ref="form" :model="pageBean" :inline="true">
        <el-form-item class="cbottom">
          <el-input
            class="definput"
            v-model="pageBean.customerName"
            clearable
            placeholder="请输入客户名称"
          ></el-input>
        </el-form-item>
        <el-form-item class="cbottom">
          <el-button
            class="search btn defaultbtn"
            type="primary"
            icon="el-icon-search"
            @click="onSearch"
            >搜索
          </el-button>
          <el-button
            v-isShow="'crm:controller:customer:exportExcel'"
            class="search btn defaultbtn"
            type="primary"
            @click="exportCustomer"
            >导出客户
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      class="customertable mytable tootiptable"
      height="590px"
      v-loading="isLoading"
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column
        prop="customerName"
        label="客户名称"
        align="center"
        width="90px"
      >
        <template slot-scope="scope">
          <span class="bbtn" @click="toDetail(scope.row)">{{
            scope.row.customerName
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="unitDepartment"
        label="部门"
        align="center"
        width="120px"
      >
      </el-table-column>
      <el-table-column prop="duties" label="职务" align="center" width="120px">
      </el-table-column>

      <el-table-column
        prop="customerLevelName"
        label="客户级别"
        align="center"
        width="100px"
      >
        <template slot-scope="scope">
          <span :class="levelColor[scope.row.customerLevelName]">{{
            scope.row.customerLevelName
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="phone"
        align="center"
        label="联系电话"
        width="120px"
      >
      </el-table-column>
      <el-table-column
        prop="majorNames"
        label="负责专业"
        align="center"
        min-width="200px"
      >
        <template slot-scope="scope">
          {{
            scope.row.specialtyName.length > 0
              ? scope.row.specialtyName.join(',')
              : '—'
          }}
        </template>
      </el-table-column>
      <el-table-column label="负责人" align="center" width="160px">
        <template slot-scope="scope">
          <div v-if="scope.row.chargePersonNames.length > 0">
            <el-tag
              class="cuscss"
              v-for="(item, index) in scope.row.chargePersonNames"
              :key="index"
              >{{ item }}</el-tag
            >
          </div>
          <div v-else>—</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="160px"
      >
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page
      :currentPage="pageBean.pageNum"
      :total="total"
      :pageSize="pageBean.pageSize"
      @updatePageNum="handleCurrentChange"
    ></page>
  </el-dialog>
</template>

<script>
import page from '@/components/common/page.vue'
import nolist from '@/components/common/nolist.vue'
import { listCustomer, customerExport } from '@/api/clientmanagement/customer'
import { getDict } from '@/utils/tools'
import { customerLevelColors } from '@/utils/dict.js'
import { checkPermission } from '@/utils/permission'
import { downloadExcelFile, downloadExcelFileCommon } from '@/utils/tools'
export default {
  components: {
    page,
    nolist,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    unitName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      title: '单位下属客户',
      dialogType: '1',
      isShowCancel: true,
      typename: '',
      levels: [],
      isLoading: false,
      tableData: [],
      total: 0,
      deletemsg: '',
      dateRange: [],
      levelColor: customerLevelColors,
      pageBean: {
        isDeleted: 0,
        unitId: '',
        pageNum: 1,
        pageSize: 10,
        customerName: '',
      },
      globalPageNum: 1,
    }
  },
  computed: {
    computedCss() {
      return function () {
        let width = 200 - 50
        return {
          width: width + 'px',
        }
      }
    },
  },
  created() {
    getDict('CustomerLevel')
      .then((result) => {
        this.levels = result
      })
      .catch((err) => {})
  },
  computed: {
    dialogTableVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('updateVisible', val)
      },
    },
  },
  methods: {
    onSearch() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    loadData(id) {
      if (id) {
        this.pageBean.unitId = id
        this.pageBean.pageNum = 1
        this.tableData = []
      }
      this.isLoading = true
      listCustomer(this.pageBean)
        .then((result) => {
          result.data &&
            result.data.forEach((element) => {
              var userid = window.sessionStorage.getItem('userid')
              if (
                element.collaborator.includes(userid) &&
                element.chargePerson != userid
              ) {
                element.isCollaborator = true
              } else {
                element.isCollaborator = false
              }
              element.isChargePerson =
                element.chargePerson == userid ? true : false
            })
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    changeDateRange(date) {
      if (date) {
        this.pageBean.startTime = date[0]
        this.pageBean.endTime = date[1]
      } else {
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
    },
    getIsEnable(data, typeStr) {
      queryIsEdit(data.id)
        .then((result) => {
          if (result.data) {
            if (typeStr == 'edit') {
              this.onEdit(data)
            } else {
              this.deleteAction(data)
            }
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    changeOption(data) {
      if (data == '1') {
        this.pageBean.chargePerson = window.sessionStorage.getItem('userid')
        this.pageBean.collaborator = ''
      } else if (data == '2') {
        this.pageBean.chargePerson = ''
        this.pageBean.collaborator = window.sessionStorage.getItem('userid')
      } else {
        this.pageBean.chargePerson = ''
        this.pageBean.collaborator = ''
      }
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    //  删除弹框删除客户
    submitDialog() {
      this.dialogVisible = false
      if (this.dialogType == 1) {
        this.deleteData()
      }
    },
    deleteData() {
      deleteCustomer({ id: this.deleteCustomerId })
        .then((result) => {
          if (result.data) {
            this.loadData()
            this.$message({
              type: 'success',
              message: '删除成功,删除后该客户将进入回收站!',
            })
          } else {
            this.dialogType = 2
            this.isShowCancel = false
            this.deletemsg = result.msg
            this.dialogVisible = true
          }
        })
        .catch((err) => {})
    },

    addCustomer() {
      this.$router.push({
        path: '/clientManagement/customer/add',
      })
    },
    deleteAction(data) {
      this.dialogType = 1
      this.isShowCancel = true
      this.dialogVisible = true
      this.deletemsg = '是否删除该客户？删除后将在回收站保留15天。'
      this.deleteCustomerId = data.id
    },
    toDetail(data) {
      if (checkPermission('crm:controller:customer:info')) {
        this.$router.push({
          path: '/clientManagement/customer/detail',
          query: { id: data.id },
        })
      }
    },
    toBook(data) {
      this.$router.push({
        path: '/clientManagement/customer/bookinfo',
        query: { id: data.id },
      })
    },
    onEdit(data) {
      this.$router.push({
        path: '/clientManagement/customer/add',
        query: {
          id: data.id,
        },
      })
    },
    handleCommand(index, data) {
      var rpath = ''
      switch (index) {
        case 0: // 跟进与拜访
          rpath = '/clientMaintenance/followVisit/add'
          this.$router.push({
            path: rpath,
            query: {
              customerId: data.id,
              customerName: data.customerName,
              unitId: data.unitId,
              unitName: data.unitName,
            },
          })
          break
        case 1:
          this.toDetail(data)
          break
        case 2:
          {
            rpath = '/clientManagement/customer/Importantdate'
            this.$router.push({
              path: rpath,
              query: {
                customerId: data.id,
              },
            })
          }
          break
        default:
          break
      }
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    getColor(name) {
      return
    },
    exportCustomer() {
      this.isDownload = true
      let params = {
        ...this.pageBean,
        unitId: this.pageBean.unitId,
        exportType: 2,
        pageNum: this.globalPageNum,
      }
      delete params.pageSize
      customerExport(params)
        .then((result) => {
          this.isDownload = false
          downloadExcelFileCommon(result.data, `单位客户`)
          if (result.data.isExport) {
            this.$confirm('本次导出1千条数据,是否继续导出?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                this.globalPageNum++
                this.exportCustomer()
              })
              .catch(() => {
                this.globalPageNum = 1
                this.$message({
                  type: 'info',
                  message: '已取消',
                })
              })
          } else {
            this.$message({
              type: 'success',
              message: '数据已全部导出',
            })
            this.globalPageNum = 1
          }
        })
        .catch((err) => {
          this.isDownload = false
        })
    },
  },
}
</script>
<style>
.tootiptable .el-tooltip {
  text-align: left;
}
</style>

<style scoped>
.pr {
  position: relative;
}
.defr {
  float: right !important;
}
.width {
  width: 120px;
}
.width160 {
  width: 160px;
}
.departcss {
  width: 100%;
  text-align: center;
}
.namecss {
  display: flex;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  min-width: 30px !important;
  /* height: 52px; */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  margin-top: -4px;
  vertical-align: middle;
  position: relative;
  top: 8px;
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
}

.column_blue {
  /* padding-top: 14px; */
  line-height: 24px;
  color: #4285f4;
  display: inline-block;
  text-align: left;
  /* overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis; */
}

.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}

.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}

.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.tagcss:nth-child(3n) {
  margin-top: 4px;
}

.customertable /deep/.cell {
}

.customertable .el-button {
  padding: 2px;
}
.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.right {
  text-align: right;
}
</style>