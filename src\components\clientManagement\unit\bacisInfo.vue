<template>
  <div>
    <el-form class="infoform" ref="form" :model="form" label-width="120px">
      <textBorder>基本信息</textBorder>
      <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
        <el-col :span="8">
          <el-form-item label="单位名称:" class="labeltext">
            <span>{{ form && form.unitName }}</span>
          </el-form-item>
          <el-form-item label="联系电话:" class="labeltext">
            <span>{{ form && form.phone }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源:" class="labeltext">
            <span>{{ form && source[form.source] }}</span>
          </el-form-item>
          <el-form-item label="传真:" class="labeltext">
            <span>{{ form && form.fax }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="网址:" class="labeltext">
            <span>{{ form && form.website }}</span>
          </el-form-item></el-col
        >
      </el-row>

      <textBorder>单位画像</textBorder>
      <el-row :gutter="20" class="width100 mt10">
        <el-col :span="8">
          <el-form-item label="单位类型:" class="labeltext">
            <span>{{ form && form.unitTypeName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单位特点:" class="labeltext">
            <span>{{ form && form.unitCharacterName }}</span>
          </el-form-item></el-col
        >
      </el-row>
      <el-row :gutter="60" class="pb5 mb30 bbline">
        <el-col :span="24">
          <el-form-item label="单位地址:" class="labeltext mulitline pb10">
            <span>{{ form && form.provinceName + '-' + form.cityName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="详细地址:" class="labeltext mulitline pb10">
            <span>{{ form && form.address }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="教材供应商:" class="labeltext mulitline pb10">
            <span>{{ form && form.supplier }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="信息化合作商:" class="labeltext mulitline pb10">
            <span>{{ form && form.informatizationPartner }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <textBorder>单位负责人</textBorder>
      <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
        <el-col :span="24">
          <el-form-item label="负责人:" class="labeltext">
            <el-tag
              class="cptext"
              v-for="(item, index) in form.chargePersonList"
              :key="index"
              >{{ item.chargePersonName }}</el-tag
            >
          </el-form-item>
        </el-col>
      </el-row>
      <textBorder>系统信息</textBorder>
      <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
        <el-col :span="8">
          <el-form-item label="创建人:" class="labeltext">
            <span>{{ form && form.createByName }}</span>
          </el-form-item>
          <el-form-item label="创建人所在部门:" class="labeltext">
            <span>{{ form && form.companyDepartmentNames }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最后修改时间:" class="labeltext">
            <span>{{ form && form.modifyTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建时间:" class="labeltext">
            <span>{{ form && form.createTime }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <textBorder>补充信息</textBorder>
      <el-row :gutter="60">
        <el-col :span="16">
          <el-form-item label="备注:" class="labeltext">
            <span>{{ form.notes }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import textBorder from '../../common/textBorder.vue'
import { unitInfo } from '@/api/clientmanagement/unit'
export default {
  data() {
    return {
      form: {
        address: '',
        chargePersonName: '',
        collaboratorName: '',
        commonUserInfoVo: '',
        companyDepartmentNames: [],
        createByName: '',
        createTime: '',
        customerNumber: '',
        enrollmentNumber: '',
        fax: '',
        id: '',
        isSelect: false,
        level: '',
        modifyByName: '',
        modifyTime: '',
        notes: '',
        phone: '',
        source: '',
        unitName: '',
        unitStructure: '',
        unitStructureUrl: '',
        website: '',
        provinceName: '',
      },
      source: {
        1: '公司资源',
        2: '自主开拓',
      },
    }
  },
  components: {
    textBorder,
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      unitInfo(this.$route.query.id)
        .then((result) => {
          this.form = result.data
          this.form.enrollmentNumber =
            this.form.enrollmentNumber == 0 ? '' : this.form.enrollmentNumber
          this.form.companyDepartmentNames =
            result.data.companyDepartmentNames.join('/')
          this.$store.commit('cus/CHANGE_KEY', {
            key: 'unitImg',
            value: this.form.unitStructureUrl,
          })
        })
        .catch((err) => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.cptext {
  margin-right: 10px;
  border: none;
}
.mulitline /deep/.el-form-item__content {
  padding-top: 10px;
  line-height: 20px;
}

.pb10 {
  padding-bottom: 10px;
}
</style>
<style scoped>
.infoform /deep/.el-form-item {
  margin-bottom: 0px;
}
</style>
