/**
 * @Author: hjp
 * @Desc 自定义校验器规则   手机号校验  数字校验
 */
import { validatePhoneNum,validateInputNumber } from '@/utils/tools'
export default {
  phone (rule, value, callback) {
    if (!validatePhoneNum(value)) {
      callback(new Error())
    } else {
      callback()
    }
  },
  
  numbers (rule, value, callback) {
    if (!validateInputNumber(value)) {
      callback(new Error())
    } else {
      callback()
    }
  },
}
