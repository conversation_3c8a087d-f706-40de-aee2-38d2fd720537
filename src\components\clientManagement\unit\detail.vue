<template>
  <div>
    <div>
      <back>单位详情</back>
    </div>
    <div class="card">
      <p class="titletext">数据概览</p>
      <el-row :gutter="20" class="cardcss">
        <el-col :span="5" v-for="(item, index) in dataViewList" :key="index">
          <div class="piececss">
            <img class="imgf left" :src="item.imgUrl" alt="" />
            <div>
              <div class="numcolor" :class="[`color-${index}`]">
                {{ item.numText }} <span class="numtext">{{ item.unit }}</span>
              </div>
              <div class="numtext">{{ item.text }}</div>
            </div>
            <img
              v-if="false"
              class="dianbtn"
              src="../../../assets/img/dian_icon.png"
              @click="goPage(item)"
            />
          </div>
        </el-col>
      </el-row>
      <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
        stretch
        class="tabscss"
      >
        <el-tab-pane label="基本信息" name="first">
          <bacisInfo ref="cusinfo"></bacisInfo>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:visit:list')"
          label="跟进与拜访"
          name="second"
        >
          <followvisit ref="cusvisit"></followvisit>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:opportunity:list')"
          label="机会"
          name="third"
        >
          <opportunity ref="cusopportunity"></opportunity>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:business:contract:list')"
          label="合同"
          name="fourth"
        >
          <contract ref="cuscontract"></contract>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:project:list')"
          label="项目"
          name="five"
        >
          <projectView ref="cusproject"></projectView>
        </el-tab-pane>
        <el-tab-pane label="教材报订" name="jiao">
          <unitBooklist ref="unitBooklist"></unitBooklist>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:distribution:list')"
          label="样书发放"
          name="seven"
        >
          <records ref="cusdistribution"></records>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:distribution:list')"
          label="礼品发放"
          name="six"
        >
          <recordsli ref="cusdistributionli"></recordsli>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import bacisInfo from './bacisInfo.vue'

import followvisit from './followvisit.vue'
import opportunity from './opportunity.vue'

import contract from './contract.vue'
import projectView from './project.vue'

import unitImg from './unitImg.vue'

import records from './records.vue'

import recordsli from './recordsli.vue'
import unitBooklist from './unitBooklist.vue'
import { queryUnitOverview } from '@/api/clientmanagement/unit'

export default {
  components: {
    back,
    followvisit,
    opportunity,
    contract,
    projectView,
    unitImg,
    records,
    bacisInfo,
    recordsli,
    unitBooklist,
  },
  data() {
    return {
      activeName: 'first',
      unitId: this.$route.query.id,
      refData: {
        first: 'cusinfo',
        second: 'cusvisit',
        third: 'cusopportunity',
        fourth: 'cuscontract',
        five: 'cusproject',
        seven: 'cusdistribution',
        six: 'cusdistributionli',
        jiao: 'unitBooklist',
      },
      dataViewList: [
        {
          imgUrl: require('../../../assets/xiaoshou.png'),
          numText: 0,
          isGo: this.checkPermission('crm:controller:opportunity:list'),
          text: '销售机会',
          unit: '个',
        },
        {
          imgUrl: require('../../../assets/img2.png'),
          numText: 0,
          isGo: this.checkPermission('crm:business:contract:list'),
          text: '合同订单',
          unit: '个',
        },
        {
          imgUrl: require('../../../assets/img3.png'),
          numText: 0,
          isGo: false,
          text: '合同订单总额',
          unit: '万元',
        },
        {
          imgUrl: require('../../../assets/img4.png'),
          numText: 0,
          isGo: false,
          text: '回款总额',
          unit: '万元',
        },
        {
          imgUrl: require('../../../assets/img5.png'),
          numText: 0,
          isGo: this.checkPermission('crm:controller:customer:list'),
          text: '下属客户',
          unit: '个',
        },
      ],
    }
  },
  created() {
    queryUnitOverview({ id: this.$route.query.id })
      .then((result) => {
        this.dataViewList.forEach((element) => {
          switch (element.text) {
            case '销售机会':
              element.numText = result.data.salesOpportunities
              break
            case '合同订单':
              element.numText = result.data.contractNumber
              break
            case '合同订单总额':
              element.numText = result.data.contractTotalAmount
              break
            case '回款总额':
              element.numText = result.data.returnTotalAmount
              break
            case '下属客户':
              element.numText = result.data.customerNumber
              break

            default:
              break
          }
        })
      })
      .catch((err) => {})
  },
  methods: {
    handleClick(tab, event) {
      var tabName = tab.name
      var refName = this.refData[tabName]
      this.$refs[refName].loadData()
    },
    goPage(item) {
      var path = ''
      switch (item.text) {
        case '销售机会':
          path = '/clientMaintenance/salesLead/index'
          break
        case '合同订单':
          path = '/projectManagement/contract/index'
          break
        case '下属客户':
          path = '/clientManagement/customer/index'
          break
        default:
          break
      }
      if (path.length > 0) {
        this.$router.push({
          path: path,
          query: { unitId: this.unitId },
        })
      }
    },
    onSubmit() {},
    handleRemove(file, fileList) {},
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
  },
}
</script>
<style>
.dianbtn {
  position: absolute;
  right: 16px;
  top: 10px;
  width: 24px;
  height: 24px;
  color: #666666;
  letter-spacing: 3px;
  font-size: 18px;
  font-weight: bold;
  line-height: 24px;
  cursor: pointer;
}

.tabscss .el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 56px;
  line-height: 56px;
  /* padding: 0 60px; */
}

.tabscss .el-tabs__header {
  margin-bottom: 30px;
}

.cardcss {
  margin-bottom: 10px;
}

.el-col-5 {
  width: 20%;
}

.labeltext label {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
</style>

<style lang="scss" scoped>
.color-0 {
  color: #56c36e;
}

.color-1 {
  color: #4a8bf6;
}

.color-2 {
  color: #e85d5d;
}

.color-3 {
  color: #ec9037;
}

.color-4 {
  color: #f46d40;
}

.titletext {
  margin-bottom: 30px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.numtext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.numcolor {
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
  line-height: 24px;
}

.imgf {
  margin-left: 20px;
  margin-right: 14px;
  width: 48px;
  height: 48px;
}

.piececss {
  position: relative;
  height: 148px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  // border: 1px solid #D6D6D6;
  width: 100%;
  padding: 50px 0;
  box-sizing: border-box;
  box-shadow: 0px 2px 16px 0px rgba(15, 27, 50, 0.08);
}

.savecss {
  display: block;
  margin: 0 auto;
}

.mt20 {
  margin-top: 20px;
  width: 100%;
}

.card {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
  min-height: calc(100vh - 135px) !important;
}
</style>