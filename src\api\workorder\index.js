import service from '@/utils/request.js'

// 新增模版
export function addWorkOrder(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/workorder/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 编辑模版
export function updateWorkorder(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/workorder/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 工单列表
export function workorderList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/workorder/list',
    params,
  });
}

// 模版详情
export function workorderInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/workorder/info/${id}`,
  });
}

// 
// 删除模版
export function deleteWorkorder(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/workorder/delete',
    data,
  });
}
// 

export function changeToTask(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/workorder/changeToTask',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}