<template>
  <div class="mainbg fixpb">
    <div class="headerdiv">
      <el-form :inline="true" label-width="85px" class="myform clearfix">
        <el-form-item label="">
          <el-input v-model="pageBean.customerName" class="definput inputWid150" clearable placeholder="客户名称">
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model="pageBean.chargePersonName" class="definput inputWid150" clearable placeholder="负责人名称">
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model="pageBean.contractDepartmentName" class="definput inputWid150" clearable
            placeholder="负责人部门">
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model="pageBean.unitName" class="definput inputWid150" clearable placeholder="客户单位">
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model="pageBean.contractTitle" class="definput inputWid150" clearable placeholder="合同标题">
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model="pageBean.contractNumber" class="definput inputWid150" clearable placeholder="合同编号">
          </el-input>
        </el-form-item>
        <el-form-item label="开票状态：">
          <el-select class="definput w1" popper-class="removescrollbar" clearable v-model="pageBean.invoicingStatus"
            placeholder="请选择">
            <el-option v-for="item in invoicStatusArr" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="合同金额:">
          <el-select class="definput w1" popper-class="removescrollbar" clearable v-model="pageBean.amountType"
            placeholder="请选择">
            <el-option v-for="item in moneyArr" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="回款状态：">
          <el-select class="definput w1" popper-class="removescrollbar" clearable v-model="pageBean.returnStatus"
            placeholder="请选择">
            <el-option v-for="item in returnStatusArr" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="协作负责：">
          <el-select clearable class="definput w1" popper-class="removescrollbar" v-model="selectValue"
            placeholder="请选择">
            <el-option v-for="item in optionsResponsible" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合同类型：">
          <el-select @change="changeValue" clearable class="definput w1" popper-class="removescrollbar"
            v-model="pageBean.contractType" placeholder="请选择">
            <el-option v-for="item in optionsContract" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <el-select clearable class="definput inputWid150 ml ellipsis" popper-class="removescrollbar"
            v-model="pageBean.contractTypeSub" placeholder="请选择">
            <el-option v-for="item in contractSubTypes" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间：">
          <datepicker @submitAction="submitAction"></datepicker>
        </el-form-item>
        <el-form-item label="合同状态：">
          <el-select class="definput w1" popper-class="removescrollbar" clearable v-model="pageBean.contractStatus"
            placeholder="请选择">
            <el-option v-for="item in contractStatus" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="search" class="defaultbtn mt" icon="el-icon-search" type="primary">搜索</el-button>
        </el-form-item>
        <el-form-item class="fr">
          <el-button class="defaultbtn mt" v-isShow="'crm:controller:contract:save'" icon="el-icon-plus" type="primary"
            @click="addContract">新建合同</el-button>
        </el-form-item>
        <el-form-item class="fr">
          <el-button v-isShow="'crm:controller:contract:exportExcel'" class="defaultbtn" icon="el-icon-my-download"
            type="primary" :loading="isDownload" @click="handleContractExport">合同导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table class="contracttable mytable" :data="tableData" style="width: 100%" v-loading="isLoading">
      <el-table-column prop="contractTitle" label="合同标题" min-width="200px" align="center">
        <template slot-scope="scope">
          <span class="bbtn" @click="toDetail(scope.row)">{{
            scope.row.contractTitle
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="contractNumber" label="合同编号" width="167px" align="center">
      </el-table-column>
      <el-table-column prop="contractDictItem" label="合同类型" width="167px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.contractDictType
            }}{{
              scope.row.contractDictItem ? `-${scope.row.contractDictItem}` : ''
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户名称" width="167px" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="unitName" label="客户单位" width="167px" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="contractAmount" label="合同金额(万元）" width="150px" align="center">
      </el-table-column>
      <el-table-column prop="contractStatus" label="合同状态" width="150px" align="center">
        <template slot-scope="scope">
          <span :class="{
              'text-success': scope.row.contractStatus == 4,
              'text-three': scope.row.contractStatus == 3,
              'text-two': scope.row.contractStatus == 2,
              'text-one': scope.row.contractStatus == 1,
            }">
            {{ contractStatusMap[scope.row.contractStatus] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="discount" label="整单折扣" width="100px" align="center">
        <template slot-scope="scope">
          {{ scope.row.discount == 0 ? '暂无折扣' : scope.row.discount }}
        </template>
      </el-table-column>
      <el-table-column prop="returnStatus" label="回款状态" width="100px" align="center">
        <template slot-scope="scope">
          {{ returnToName[scope.row.returnStatus] }}
        </template>
      </el-table-column>
      <el-table-column prop="invoicingStatus" label="开票状态" width="100px" align="center">
        <template slot-scope="scope">
          {{ invoicToName[scope.row.invoicingStatus] }}
        </template>
      </el-table-column>
      <el-table-column prop="endTime" label="结束时间" width="120px" :formatter="valueFormatter">
      </el-table-column>

      <el-table-column prop="isExpire" label="是否到期" width="167px" align="center">
        <template slot-scope="scope">
          <span :class="{ success: !scope.row.isExpire, error: scope.row.isExpire }">{{
              scope.row.isExpire ? `${scope.row.expireTime}已到期` : '未到期'
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="chargePersonName" label="负责人" align="center" width="120px">
      </el-table-column>
      <el-table-column prop="contractDepartmentName" label="负责人部门" width="167px" align="center">
      </el-table-column>
      <el-table-column prop="collaboratorName" label="协作人" align="center" min-width="220px">
        <template slot-scope="scope">
          {{ scope.row.collaboratorName || '—' }}
        </template>
      </el-table-column>
      <el-table-column prop="visitNum" label="跟进拜访次数" align="center" width="110px">
        <template slot-scope="scope">
          <span class="numtime" @click="gotoDetail(scope.row)">
            {{ scope.row.visitNum + '次' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="createByName" label="创建人" width="100px" align="center">
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间" width="167px" align="center">
      </el-table-column>
      <el-table-column prop="edit" align="center" width="200px" fixed="right" label="更多操作">
        <template slot-scope="scope">
          <el-dropdown placement="bottom-end" @command="(e) => handleCommand(e, scope.row)" trigger="click">
            <el-button class="bbtn mr10" type="text"> 更多 </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="scope.row.isShow" :command="0">回款</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.isShow" :command="1">发货</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.isShow" :command="2">开票</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.isShow" :command="3">退款</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.contractStatus == 1" :command="4">完成制作</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.contractStatus == 2" :command="5">完成签订</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.contractStatus == 3" :command="6">完成备案</el-dropdown-item>
              <el-dropdown-item v-isShow="'crm:controller:project:save'" :command="8">创建项目
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button class="bbtn" v-isShow="'crm:controller:contract:info'" type="text" @click="toDetail(scope.row)">
            详情</el-button>
          <el-button class="bbtn" :disabled="!scope.row.isShow" type="text" @click="onEdit(scope.row)"
            v-isShow="'crm:controller:contract:update'">
            编辑
          </el-button>
          <el-button v-isShow="'crm:controller:contract:delete'" class="rbtn" type="text"
            @click="deleteAction(scope.row)">
            删除</el-button>
          <!-- :disabled="!scope.row.isShow" -->
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"></page>
    </div>

    <dc-dialog :iType="deleteType" title="温馨提示" width="500px" :dialogVisible.sync="dialogVisible"
      @submit="submitDialog">
      <template> </template>
      <p class="pcc">{{ deleteMessage }}</p>
      <!-- <p class="pcc">除，请先接触客户与单位的关系。</p> -->
    </dc-dialog>
    <dc-dialog iType="2" title="温馨提示" width="500px" :dialogVisible.sync="dialogMarkVisible" @submit="createDialog">
      <template> </template>
      <p class="pcc">
        您已创建{{
          newContract && newContract.contractTitle
        }}，是否快捷创建合同项目？
      </p>
    </dc-dialog>
  </div>
</template>

<script>
  import page from '../../common/page.vue'
  import nolist from '../../common/nolist.vue'
  import {
    getDict,
    getParStr,
    downloadExcelFileCommon,
    valide,
  } from '@/utils/tools'
  import {
    contractList,
    deleteContract,
    addProject,
    updateContract,
  } from '@/api/contract'
  import datepicker from '@/components/common/datepicker'
  import {
    checkPermission
  } from '@/utils/permission.js'
  import {
    queryVoListByCode,
    contractExport
  } from '@/api/wapi.js'
  import {
    contractStatusMap,
    contractStatus
  } from '@/utils/status-tool.js'
  export default {
    components: {
      page,
      nolist,
      datepicker,
    },
    data() {
      return {
        deleteType: 1,
        contractStatusMap,
        contractStatus,
        deleteMessage: '是否删除该合同？',
        isDownload: false,
        deleteData: {},
        selectValue: '',
        newContract: {},
        optionsResponsible: [{
            value: '0',
            name: '全部',
          },
          {
            value: '1',
            name: '我协作的',
          },
          {
            value: '2',
            name: '我负责的',
          },
        ],
        returnStatusArr: [{
            id: '1',
            name: '未回款',
          },
          {
            id: '2',
            name: '部分回款',
          },
          {
            id: '3',
            name: '全部回款',
          },
          {
            id: '4',
            name: '超额回款',
          },
        ],
        returnToName: {
          1: '未回款',
          2: '部分回款',
          3: '全部回款',
          4: '超额回款',
        },
        invoicToName: {
          1: '未开票',
          2: '部分开票',
          3: '全部开票',
          4: '超额开票',
        },
        invoicStatusArr: [{
            id: '1',
            name: '未开票',
          },
          {
            id: '2',
            name: '部分开票',
          },
          {
            id: '3',
            name: '全部开票',
          },
          {
            id: '4',
            name: '超额开票',
          },
        ],
        moneyArr: [{
            id: '1',
            name: '0-20万',
          },
          {
            id: '2',
            name: '20-50万',
          },
          {
            id: '3',
            name: '50-100万',
          },
          {
            id: '4',
            name: '100万以上',
          },
        ],
        dialogVisible: false,
        dialogMarkVisible: false,
        options: [{
            value: 'value',
            name: 'name',
          },
          {
            value: 'value1',
            name: 'name',
          },
        ],
        isLoading: false,
        tableData: [],
        total: 0,
        pageBean: {
          invoicingStatus: '',
          amountType: '',
          customerName: '',
          contractTitle: '',
          returnStatus: '',
          unitName: '',
          pageNum: 1,
          pageSize: 10,
          contractNumber: '',
          contractType: '',
          contractTypeSub: '',
          time: '',
          createStartTime: '',
          createEndTime: '',
          createByNameL: '',
          createByDepartment: '',
        },
        optionsContract: [],
        contractSubTypes: [],
        typeToValue: {
          1: 'collaborator',
          2: 'chargePerson',
        },
        globalPageNum: 1,
      }
    },
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.dialogMarkVisible =
          from.name === 'addContract' ? from.params.isSaveSuccess : false
        vm.newContract = from.name === 'addContract' ? from.params.data : {}
      })
    },
    created() {
      if (Object.keys(this.$route.query).length > 0) {
        this.pageBean = Object.assign(this.pageBean, this.$route.query)
        this.pageBean.pageNum = Number(this.pageBean.pageNum)
        this.pageBean.pageSize = Number(this.pageBean.pageSize)
      }
      this.projectListData()
      this.getDictApi()
    },
    methods: {
      valueFormatter(row, column, cellValue, index) {
        if (cellValue) {
          return cellValue
        }
        return '—'
      },
      setValue(val) {
        let item = this.optionsContract.filter((item) => item.id === val)
        this.contractSubTypes = item[0].children
      },
      getDictApi() {
        queryVoListByCode({
          code: 'ContractType'
        }).then((res) => {
          if (res.status == 0 && res.data.length > 0) {
            this.optionsContract = res.data
            if (this.pageBean.contractType) {
              this.setValue(this.pageBean.contractType)
            }
          }
        })
      },
      changeValue(val) {
        if (val == '') {
          this.pageBean.contractTypeSub = ''
          this.contractSubTypes = []
        } else {
          this.pageBean.contractTypeSub = ''
          let item = this.optionsContract.filter((item) => item.id === val)
          this.contractSubTypes = item[0].children
        }
      },
      projectListData() {
        if (this.selectValue == 0) {
          this.pageBean.chargePerson && delete this.pageBean.chargePerson
          this.pageBean.collaborator && delete this.pageBean.collaborator
        }
        if (this.selectValue == 1) {
          this.pageBean.chargePerson && delete this.pageBean.chargePerson
          this.pageBean[this.typeToValue[this.selectValue]] =
            sessionStorage.getItem('userid') || ''
        }
        if (this.selectValue == 2) {
          this.pageBean.collaborator && delete this.pageBean.collaborator
          this.pageBean[this.typeToValue[this.selectValue]] =
            sessionStorage.getItem('userid') || ''
        }
        history.replaceState(
          null,
          null,
          `#${this.$route.path}?${getParStr(this.pageBean)}`
        )
        this.isLoading = true
        contractList(this.pageBean)
          .then((result) => {
            if (result.status == 0) {
              this.tableData = result.data
              this.total = result.page.total
            }
            this.isLoading = false
          })
          .catch((err) => {
            this.isLoading = false
          })
      },
      search() {
        this.projectListData()
      },
      submitDialog() {
        this.dialogVisible = false
        if (this.deleteType == 1) {
          deleteContract({
              id: this.deleteData.id
            })
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '删除成功',
                })
                this.projectListData()
              } else {
                this.deleteType = 2
                this.deleteMessage = result.msg
                this.dialogVisible = true
              }
            })
            .catch((err) => {})
        }
      },
      // 创建项目
      createDialog() {
        this.$router.push({
          path: '/projectManagement/project/add',
          query: {
            contractId: this.newContract.id,
          },
        })
      },
      addContract() {
        this.$router.push({
          path: '/projectManagement/contract/add',
        })
      },
      deleteAction(data) {
        this.deleteData = data
        this.deleteType = 1
        this.deleteMessage = '是否删除该合同？'
        this.dialogVisible = true
      },
      toDetail(data) {
        if (!checkPermission('crm:controller:contract:info')) {
          this.$message({
            type: 'error',
            message: '暂无详情查看权限',
          })
          return
        }
        this.$router.push({
          path: '/projectManagement/contract/detail',
          query: {
            activeName: 'first',
            id: data.id
          },
        })
      },
      onEdit(data) {
        this.$router.push({
          path: '/projectManagement/contract/add',
          query: {
            id: data.id,
          },
        })
      },
      saveProject(id) {
        addProject(id)
          .then((result) => {
            if (result.data) {
              this.$confirm('该机会的项目内容已生成，是否查看', '提示', {
                  distinguishCancelAndClose: true,
                  confirmButtonText: '查看项目',
                  cancelButtonText: '取消',
                })
                .then(() => {
                  this.$router.push({
                    path: '/project/manager/index',
                  })
                })
                .catch((action) => {})
            } else {
              this.$message({
                type: 'error',
                message: result.msg,
              })
            }
          })
          .catch((err) => {})
      },
      handleCommand(index, data) {
        let statusMap = {
          4: 2,
          5: 3,
          6: 4,
        }
        if (index == 4 || index == 5 || index == 6) {
          updateContract({
            contractStatus: statusMap[index],
            id: data.id,
          }).then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功',
            })
            this.projectListData()
          })
          return false
        }
        if (index == 8) {
          this.$confirm('是否生成该合同的项目信息？', '确认信息', {
              distinguishCancelAndClose: true,
              confirmButtonText: '创建',
              cancelButtonText: '取消',
            })
            .then(() => {
              // 创建合同
              this.$router.push({
                path: '/project/manager/add',
                query: {
                  contractId: data.id,
                },
              })
            })
            .catch((action) => {})
          return
        }
        var rpath = '/projectManagement/contract/detail'
        var activeName = ''
        switch (index) {
          case 0: // 跟进与拜访
            activeName = 'five'
            break
          case 1:
            activeName = 'second'
            break
          case 2:
            activeName = 'fourth'
            break
          case 3:
            activeName = 'third'
            break
          default:
            break
        }
        if (rpath.length > 0) {
          this.$router.push({
            path: rpath,
            query: {
              activeName: activeName,
              id: data.id
            }, // 客户id
          })
        }
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page
        this.projectListData()
      },
      submitAction(type, dateRange) {
        if (type == 11) {
          if (dateRange) {
            this.pageBean.createStartTime = dateRange[0]
            this.pageBean.createEndTime = dateRange[1]
          } else {
            this.pageBean.createStartTime = ''
            this.pageBean.createEndTime = ''
          }
          this.pageBean.time = ''
        } else {
          this.pageBean.time = type
          this.pageBean.createStartTime = ''
          this.pageBean.createEndTime = ''
        }
      },
      handleContractExport() {
        this.isDownload = true
        let params = {
          ...this.pageBean,
          pageNum: this.globalPageNum,
        }
        delete params.pageSize
        contractExport(params)
          .then((result) => {
            this.isDownload = false
            downloadExcelFileCommon(result.data, `合同管理`)
            if (result.data.isExport) {
              this.$confirm('本次导出1千条数据,是否继续导出?', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning',
                })
                .then(() => {
                  this.globalPageNum++
                  this.handleContractExport()
                })
                .catch(() => {
                  this.globalPageNum = 1
                  this.isDownload = false
                  this.$message({
                    type: 'info',
                    message: '已取消',
                  })
                })
            } else {
              this.$message({
                type: 'success',
                message: '数据已全部导出',
              })
              this.globalPageNum = 1
            }
          })
          .catch((err) => {
            this.isDownload = false
          })
      },
      gotoDetail(row) {
        if (!valide('/projectManagement/contract/contractvisitlist')) {
          this.$message({
            type: 'error',
            message: '您没有访问权限,请联系管理员',
          })
          return false
        }
        this.$router.push({
          path: '/projectManagement/contract/contractvisitlist',
          query: {
            id: row.id,
          },
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .text-success {
    color: rgba(94, 188, 100, 1);
  }

  .text-one {
    color: rgba(252, 202, 0, 1);
  }

  .text-two {
    color: rgba(255, 114, 114, 1);
  }

  .text-three {
    color: rgba(255, 98, 0, 1);
  }

  .numtime {
    color: #4285f4;
    cursor: pointer;
  }

  .bbtn {
    cursor: pointer;
  }

  .w1 {
    width: 120px;
  }

  .w2 {
    width: calc(100% - 120px);
  }

  .ml {
    margin-left: 10px;
  }

  .success {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #56c36e;
  }

  .error {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #f45961;
  }

  .headerdiv {
    display: flex;
    justify-content: center;
  }

  .headerdiv /deep/.myform {
    width: 100%;
  }

  .rightdiv {
    width: 120px;
    text-align: center;
  }

  .rightdiv .el-button {
    margin: 5px 0px;
    margin-bottom: 18px;
  }

  .mainbg {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
  }

  .pcc {
    margin: 0 auto;
    text-align: center;
  }

  .smtext {
    zoom: 0.8;
  }

  .fxcenter {
    width: 50px;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .zhiding {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    /* background-color: red; */
  }

  .mt {
    margin-top: 4px;
  }

  .cusnamecss {
    display: flex;
  }

  .tagcss {
    font-size: 0.625em !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 14px;
    min-width: 30px;
    line-height: 11px;
    border-radius: 2px;
    margin-right: 20px;
  }

  .genjin {
    color: #4285f4;
    background-color: #dfeafd;
  }

  .fuze {
    color: #fef2e7;
    background-color: #ff8d1a;
  }

  .xiezuo {
    color: #56c36e;
    background-color: #f3fef6;
  }

  .tagcss:nth-child(2n + 2) {
    margin-top: 4px;
  }

  .contracttable .el-button {
    padding: 2px;
  }

  .mr10 {
    margin-right: 10px;
  }

  .bbtn,
  .bbtn:hover,
  .bbtn:focus {
    color: #4285f4;
  }

  .rbtn,
  .rbtn:hover,
  .rbtn:focus {
    color: #f45961;
  }

  .right {
    text-align: right;
  }

  .myform .el-form-item:last-child {
    margin-right: 20px !important;
  }
</style>
