<!--审核记录-->
<template>
    <div>
        <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
            prop="createByName"
            label="审核人"
            width="180">
            </el-table-column>
            <el-table-column
            prop="createBy"
            label="审核工号"
            width="180">
            </el-table-column>
            <el-table-column
            label="审核部门"
             width="180">
            </el-table-column>
            <el-table-column
            prop="description"
            label="审核内容"
             width="180">
            </el-table-column>
            <el-table-column
            label="审核状态"
            width="180">
              <template slot-scope="scope">
                <span>{{scope.row.processingStatus===0?'冻结成功':'已解冻'}}</span>
              </template>
            </el-table-column>
            <el-table-column
            label="审核时间">
              <template slot-scope="scope">
                <span>{{ruleTimeSuccess(scope.row.createTime)}}</span>
              </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import { ruleTimeSuccess } from '../../../../utils/tools'

export default {
  props: [ 'third' ],
  data() {
    return {
      tableData: [

      ],
      search: {}
    }
  },
  components: {
    ruleTimeSuccess
  },
  watch: {
    thirdSelectValue(val) {
      this.third = val
      this.getRecord(this.search)
    }
  },
  computed: {
    thirdSelectValue() {
      return this.third
    }
  },
  methods: {
    async getRecord(obj) {
      obj.userId = this.third.id
      let res = await this.$axios.get('sf/member/ocs/userFreezeRecord', { params: obj })
      if (res.status === 0) {
        this.tableData = res.data
      }
    },
    // 时间戳转化为时分秒
    ruleTimeSuccess (val) {
      return ruleTimeSuccess(val)
    }
  },
  created() {
    this.search.pageNum = '1';
    this.search.pageSize = '10';
    this.getRecord(this.search)
  }
}
</script>
