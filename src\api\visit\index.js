import service from '@/utils/request.js'

export function contractList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/contract/list',
    params,
  });
}


export function visitSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/visit/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function visitUpdate(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/visit/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function visitList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/visit/list',
    params,
  });
}

export function commentList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/visitcomment/list',
    params,
  });
}

export function sendSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/visitcomment/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function commentDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/visitcomment/delete',
    data,
  });
}

export function visitInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/visit/info/${id}`,
  });
}


export function visitDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/visit/delete',
    data,
  });
}


// 导出
export function visitDownloadExcel(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/visit/visitDownloadExcel',
    params
  });
}

export function isShowDetail(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/customer/queryIsPermission',
    params
  });
}