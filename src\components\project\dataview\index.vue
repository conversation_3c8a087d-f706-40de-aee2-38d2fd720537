<template>
  <div class="bgdiv">
    <img class="maskbg" src="../../../assets/img/maskbg.png" alt="" srcset="">
    <div class="formdiv">
        <img class="comicon" src="../../../assets//img/comicon.png" alt="">
        <el-form ref="myform" class="myform" :rules="rules" :model="form">
            <div class="titlecss">项目大数据</div>
            <el-form-item label="" prop="projectType">
                <el-select clearable="" @change="changeValue"  class="definput els40 ellipsis"
                    popper-class="removescrollbar" v-model="form.projectType" placeholder="项目大类">
                    <el-option v-for="item in optionsContract" :key="item.id" :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
                <el-select clearable="" class="definput els50 ellipsis" popper-class="removescrollbar"
                    v-model="form.projectTypeSub" placeholder="项目子类">
                    <el-option v-for="item in optionsContractDetail" :key="item.id" :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="" prop="date">
                <el-date-picker
                v-model="form.year"
                type="year"
                class="definput wid100"
                format="yyyy年"
                data-format="yyyy"
                value-format="yyyy"
                placeholder="选择年">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="">
                <el-button class="defaultbtn wid100" type="primary" @click="submitAction">开始统计</el-button>
            </el-form-item>
        </el-form>
    </div>
  </div>
</template>

<script>
import { getDict } from '@/utils/tools'
export default {
    data(){
        return{
            form:{
                year:'',
                projectType:'',
                projectTypeSub:''
            },
            props:{
                value:'id',
                label:'name',
            },
            optionsContract:[],
            optionsContractDetail:[],
            rules:{
                year: [
                    { type: 'date', required: true, message: '请选择年份', trigger: 'change' }
                ],
            }
        }
    },
    created(){
        this.getTypesData();
    },
    methods:{
        handleChange(value){

        },
        getTypesData(){
            getDict('ContractType').then(res=>{
                this.handleTypes(res)
                console.log('res===>',res)
                this.optionsContract = res
            })
        },
        handleTypes(list){
            console.log('list===>',list.children)
            list.forEach(item => {
                if (item.children.length>0) {
                    this.handleTypes(item.children)
                }else{
                    delete item.children ;
                }
            });
          },
          changeValue(val) {
                if (val == '') {
                    this.form.projectTypeSub = ''
                    this.optionsContractDetail = []
                } else {
                    this.form.projectTypeSub = ''
                    let item = this.optionsContract.filter(item => item.id === val)
                    this.optionsContractDetail = item[0].children
                }
            },
            setValue(val) {
                let item = this.optionsContract.filter(item => item.id === val)
                this.optionsContractDetail = item[0].children
            },
        submitAction(){
            this.$refs.myform.validate((valid) => {
                if (valid) {
                    this.$router.push({
                        path:"/project/dataview/detail",
                        query:this.form
                    })
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.els50{
    width: calc(60% - 10px)
}
.els40{
    width: 40%;
    margin-right: 10px;
}
.defaultbtn{
    margin-top: 30px;
}
.wid100{
    width: 100% !important;
}
.myform /deep/.el-form-item{
    margin-right: 0px !important;
}
.myform{
    margin: 60px;
}
.titlecss{
    text-align: left;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    margin-top: 40px;
    margin-bottom: 20px;

}
.comicon{
    margin-top: 40px;
    height: 100px;
}
.formdiv{
    position: absolute;
    left: 100px;
    top: calc(50% - 250px);
    width: 400px;
    height: 500px;
    background: rgba(255,255,255,0.3);
    box-shadow: 0px 20px 40px 0px #DEE1FF;
    border-radius: 20px ;
    text-align: center;
}
.maskbg{
    margin: 0px;
    width: 100%;
    height: calc(100vh - 100px);
}
.bgdiv{
    position: relative;
}
</style>