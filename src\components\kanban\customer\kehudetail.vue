<template>
  <div>
    <div class="riqi">
      <el-date-picker
        v-model="selectedYear"
        type="year"
        placeholder="请选择年份"
        format="yyyy年"
        value-format="yyyy"
        :picker-options="{
          disabledDate(time) {
            return time.getTime() > Date.now()
          },
        }"
        class="xuanze"
        @change="changeAction"
      >
      </el-date-picker>
      <el-select
        :title="deLevel"
        class="definput xiala shenglue"
        popper-class="removescrollbar"
        v-model="deLevel"
        placeholder="请选择"
      >
        <el-option
          @click.native="changeOp(item)"
          v-for="item in levels"
          :key="item.id"
          :label="item.value"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <div class="notime">
        <nowtime></nowtime>
      </div>
    </div>
    <!-- 客户变化 -->
    <bianhua ref="bianhua"></bianhua>
    <!-- 增量分析 -->
    <zengliang ref="zengliang"></zengliang>
    <!-- 客户信息完整度分析 -->
    <kequn ref="kequn"></kequn>
    <!-- 业务经理客户星级情况 -->
    <customerSituation
      ref="customerSituation"
      @gotoDetail="gotoDetail"
    ></customerSituation>

    <deDialogHome
      ref="deRef"
      :dType="dType"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
      :multiple="false"
    >
    </deDialogHome>
  </div>
</template>    

<script>
import nowtime from '@/components/kanban/common/nowtime.vue'
import bianhua from '@/components/kanban/common/bianhua.vue'
import zengliang from '@/components/kanban/common/zengliang.vue'
import kequn from '@/components/kanban/common/kequn.vue'
import customerSituation from '@/components/kanban/common/customerSituation.vue'
import deDialogHome from '@/components/common/deDialogHome.vue'
import {
  queryCustomerOverviewData,
  queryCustomerIntegrityData,
  querySituation,
} from '@/api/bigdata/index'
export default {
  components: {
    nowtime,
    bianhua,
    zengliang,
    kequn,
    customerSituation,
    deDialogHome,
  },
  data() {
    return {
      dialogVisible: false,
      dType: '1',
      selectedYear: new Date().getFullYear().toString(),
      kequnData: {},
      deLevel: '',
      levels: [],
      levelTypeToList: {
        1: [
          {
            id: 4,
            value: '看自己',
          },
        ],
        2: [
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
          {
            id: 4,
            value: '看自己',
          },
        ],
        3: [
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
          {
            id: 4,
            value: '看自己',
          },
        ],
        4: [
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
        ],
      },
      hparams: {
        departmentIds: [],
        userIds: [],
      },
    }
  },
  mounted() {},

  created() {
    this.levels =
      this.levelTypeToList[sessionStorage.getItem('dataScope')] || []
    if (sessionStorage.getItem('dataScope') != 4) {
      this.hparams.userIds = [sessionStorage.getItem('userid')]
    }
    this.loadData()
  },

  methods: {
    gotoDetail() {
      let paramsObj = {
        departmentIds: this.hparams.departmentIds,
        userIds: this.hparams.userIds,
      }
      this.$router.push({
        path: '/kanban/customer/customerDeatil',
        query: {
          year: this.selectedYear,
          paramsObj: JSON.stringify(paramsObj),
        },
      })
    },
    submitData(data) {
      this.dialogVisible = false
      if (this.dType == 1) {
        this.hparams.departmentIds = data.map((item) => item.id)
        this.hparams.userIds = []
      } else if (this.dType == 2) {
        let userids = data.map((item) => item.id)
        this.hparams.departmentIds = []
        this.hparams.userIds = userids
      }
      let names = data.map((item) => item.name)
      this.deLevel = names.join(',')
      this.loadCustomerOverviewData()
      this.loadCustomerIntegrityData()
      this.loadSituationData()
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    changeOp(item) {
      if (item.id == 2) {
        this.dialogVisible = true
        this.$refs.deRef.loadData()
        this.dType = '1'
      } else if (item.id == 3) {
        this.dialogVisible = true
        this.$refs.deRef.loadData()
        this.dType = '2'
      } else if (item.id == 4) {
        this.hparams.userIds = [sessionStorage.getItem('userid')]
        this.loadData()
      }
    },
    loadData() {
      this.loadCustomerOverviewData()
      this.loadCustomerIntegrityData()
      this.loadSituationData()
    },
    loadCustomerIntegrityData() {
      queryCustomerIntegrityData({
        year: Number(this.selectedYear),
        departmentId: this.hparams.departmentIds,
        userId: this.hparams.userIds,
      })
        .then((result) => {
          var list = [
            result.data.oneStarCustomerList.pop(),
            result.data.twoStarCustomerList.pop(),
            result.data.threeStarCustomerList.pop(),
          ]
          this.$nextTick(() => {
            this.$refs.kequn.initChart(result.data)
            this.$refs.kequn.initPieChart(list)
          })
        })
        .catch((err) => {})
    },
    loadCustomerOverviewData() {
      queryCustomerOverviewData({
        year: Number(this.selectedYear),
        departmentId: this.hparams.departmentIds,
        userId: this.hparams.userIds,
      })
        .then((result) => {
          this.$nextTick(() => {
            this.$refs.bianhua.initChart(result.data.customerTotalList)
            this.$refs.zengliang.initChart(result.data.customerIncrementList)
          })
        })
        .catch((err) => {})
    },
    loadSituationData() {
      querySituation({
        pageNum: 1,
        pageSize: 10,
        departmentId: this.hparams.departmentIds,
        userId: this.hparams.userIds,
        year: Number(this.selectedYear),
      })
        .then((result) => {
          this.$refs.customerSituation.loadData(result.data)
        })
        .catch((err) => {})
    },
    changeAction() {
      this.loadData()
    },
  },
}
</script>
<style>
.shenglue .el-input__inner {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: normal;
  white-space: nowrap;
}
</style>
<style lang="scss" scoped>
.xuanze /deep/.el-input__inner {
  height: 34px;
}
.xuanze /deep/.el-input__icon {
  line-height: 34px;
}
.notime {
  float: right;
}
.riqi {
}
.xiala {
  width: 210px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: none;
}
</style>