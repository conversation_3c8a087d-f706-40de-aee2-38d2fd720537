<template lang="html">
  <el-card class="box-card">
    <div  class="tools-bar">
      <el-button v-isShow="'ear:company:expertlecturerinfo:save'" type="success" icon="el-icon-plus" size="small" @click="addUser">新增</el-button>
    </div>
    <div>
      <el-table
        ref="singleTable"
        :data="tableData"
        border
        highlight-current-row
        style="width: 100%">
        <el-table-column
          prop="name"
          label="政策名称"
          min-width="120">
        </el-table-column>
        <el-table-column
          min-width="210"
          label="宣传图">
             <template slot-scope="scope">
              <el-image fit="contain" :src="scope.row.fileUrl" class="itemimg" />
            </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          min-width="210"
          label="创建时间">
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          width="150">
          <template slot-scope="scope">
  <div>
    <el-button
      v-isShow="'ear:company:expertlecturerinfo:info'"
      type="primary"
      size="small"
      @click="handleEdit(scope.$index, scope.row)"
      >编辑</el-button
    >
    <el-button
      v-isShow="'ear:company:expertlecturerinfo:delete'"
      type="danger"
      size="small"
      @click="handleResetPwd(scope.$index, scope.row)"
      >删除</el-button
    >
  </div>
</template>
        </el-table-column>
      </el-table>
      <div class="pagination-bar">
        <el-pagination
          @size-change="handleSizeChange"
          :current-page="1"
          @current-change="handleCurrentChange"
          :page-size="pageBean.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="totalRecord">
        </el-pagination>
      </div>
    </div>
    <el-dialog width="60%" :title="dialogTitle" :visible.sync="dialogVisible" @close="onDialogClose()">
      <el-form ref="dataForm"  :model="dataForm" label-width="100px">
         <el-form-item class="expertList" label="政策名称" prop="name">
          <el-input v-model="dataForm.name" placeholder="请输入政策名称"></el-input>
        </el-form-item>
        <el-form-item label="宣传图">
            <el-upload
              class="upload-demo"
              :action="getUrl"
              name="file"
              :limit="1"
              accept=".jpg, .png, .JPG, .jpeg, .JPEG, .PNG, .gif, .mp4"
              :on-preview="handlePreviewDet"
              :on-remove="handleRemoveDet"
              :file-list="fileListDet"
              :headers="headerUrl"
              :on-success="handleAvatarSuccessDet"
              :data="imgDetails"
              list-type="picture"
            >
              <el-button size="small" type="primary"
                >点击上传图片</el-button
              >
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
          </el-form-item>
          <el-form-item label="政策详情">
                          <template>
  <vue-ueditor-wrap
    v-model="dataForm.content"
    :config="myConfig"
  ></vue-ueditor-wrap>
</template>
          </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancleDialog">取 消</el-button>
        <el-button v-isShow="'ear:company:expertlecturerinfo:update'" type="primary" @click="onDialogSubmit()" >确定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>
<script>
import VueUeditorWrap from 'vue-ueditor-wrap'
import {
  getList,
  policySave,
  deletePolicy,
  policyDetail,
  updatePolicy,
} from '@/api/basesystem/index.js'
export default {
  data() {
    return {
      search: '',
      stashList: [],
      totalRecord: 0,
      tableLoading: false,
      dialogVisible: false,
      dialogTitle: '新增',
      rules: {
        schoolName: [
          { required: true, message: '请输入学校名称', trigger: 'blur' },
        ],
      },
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        afficheType: '1',
      },
      dataForm: {
        name: '',
        afficheType: '1',
        content: '',
      },
      tableData: [],
      getUrl: `${process.env.VUE_APP_BASE_API}aliyun/oss/uploadFiles`,
      fileListDet: [],
      headerUrl: { Authorization: window.sessionStorage.getItem('token') },
      imgDetails: { commonName: 'company' },
      myConfig: {
        toolbars: [
          [
            "undo", //撤销
            "bold", //加粗
            "indent", //首行缩进
            "italic", //斜体
            "underline", //下划线
            "strikethrough", //删除线
            "subscript", //下标
            "fontborder", //字符边框
            "superscript", //上标
            "formatmatch", //格式刷
            "fontfamily", //字体
            "fontsize", //字号
            "justifyleft", //居左对齐
            "justifycenter", //居中对齐
            "justifyright", //居右对齐
            "justifyjustify", //两端对齐
            "insertorderedlist", //有序列表
            "insertunorderedlist", //无序列表
            "lineheight", //行间距
          ],
        ],
        // 编辑器不自动被内容撑高
        autoHeightEnabled: false,
        // 初始容器高度
        initialFrameHeight: 240,
        // 初始容器宽度
        initialFrameWidth: '100%',
        // 上传文件接口（这个地址是我为了方便各位体验文件上传功能搭建的临时接口，请勿在生产环境使用！！！）
        // serverUrl: 'http://**************:8000/controller.php',
        // UEditor 资源文件的存放路径，如果你使用的是 vue-cli 生成的项目，通常不需要设置该选项，vue-ueditor-wrap 会自动处理常见的情况，如果需要特殊配置，参考下方的常见问题2
        UEDITOR_HOME_URL: '/UEditor/',
      },
    }
  },
  created() {
    this.initList()
  },
  watch: {},
  mounted() {},
  methods: {
    // 上传详情图
    handleRemoveDet(file, fileList) {},
    handlePreviewDet(file) {},
    handleAvatarSuccessDet(res, file, fileList) {
      this.fileListDet = fileList
      this.dataForm.fileUrl = res.data.url
      this.dataForm.fileName = res.data.fileName
      this.dataForm.fileSize = res.data.size
    },
    cancleDialog() {
      this.dialogVisible = false
      this.fileListDet = []
    },
    async initList() {
      let res = await getList(this.pageBean)
      if (res.status != 0) return this.$message.error(res.msg)
      this.tableData = res.data
      this.totalRecord = res.page.total
    },
    addUser() {
      this.dialogVisible = true
      this.fileListDet = []
      for (let key in this.dataForm) {
        this.dataForm[key] = ''
      }
      this.dataForm.afficheType = '1'
    },
    handleStatus(row) {},
    onDialogClose() {
      this.$refs.dataForm.resetFields()
      this.fileListDet = []
    },
    handleSizeChange(val) {
      this.pageBean.pageNum = val
      this.initList()
    },
    handleCurrentChange(val) {
      this.pageBean.pageNum = val
      this.initList()
    },
    handleChangeStatus(index, row) {},
    handleResetPwd(index, row) {
      this.$confirm('确认删除该数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let res = await deletePolicy({ ids: [row.id].join(',') })
          if (res.status !== 0) return this.$message.error(res.msg)
          this.initList()
          this.$message({ message: '删除成功', type: 'success' })
        })
        .catch(() => {})
    },
    async handleEdit(index, row) {
      this.dialogVisible = true
      this.dialogTitle = '修改'
      this.dataForm.id = row.id
      this.editExpert(row.id)
    },
    async editExpert(id) {
      let res = await policyDetail(id)
      this.dataForm = res.data
      this.fileListDet.push({ url: res.data.fileUrl })
    },
    onDialogSubmit() {
      this.$refs.dataForm.validate(async (valid) => {
        if (valid) {
          if (this.dialogTitle == '修改') {
            delete this.dataForm.modifyTime
            delete this.dataForm.createTime
            let res = await updatePolicy(this.dataForm)
            if (res.status !== 0) return this.$message.error(res.msg)
            this.$message.success('修改成功')
          } else {
            let res = await policySave(this.dataForm)
            if (res.status !== 0) return this.$message.error(res.msg)
            this.$message({ message: '添加成功', type: 'success' })
          }
          this.dialogVisible = false
          this.initList()
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>
.expertList {
  width: 400px;
}
.fr {
  float: right;
}
.fl {
  float: left;
}
.search-bar {
  overflow: hidden;
}
.tools-bar {
  margin-bottom: 20px;
}
.itemimg {
  width: 100px;
  height: 60px;
  margin-right: 5px;
}
</style>
