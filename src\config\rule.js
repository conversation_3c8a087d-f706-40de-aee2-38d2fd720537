/**
 * @Rule 页面所有表单校验配置
 * @Name 命名要大写，多个单词用下划线隔开
 */
import validator from './validator'
export default {
  LOGIN: {
    username: [
      { required: true, trigger: 'change', message: '用户名不能为空' }
    ],
    password: [
      { required: true, trigger: 'change', message: '密码不能为空' }
    ],
    kaptcha: [
      { required: true, trigger: 'change', message: '验证码不能为空' }
    ]
  },
  //系统管理-APP版本管理
  ADD_APPversion: {
    appCode: [
      { required: true, message: '应用不能为空', trigger: 'blur' }
    ],
    versionFirst: [
      { required: true, message: '版本名称不能为空', trigger: 'blur' }
    ],
    versionSecond: [
      { required: true, message: '版本名称不能为空', trigger: 'blur' }
    ],
    versionThird: [
      { required: true, message: '版本名称不能为空', trigger: 'blur' }
    ],
    miniCode: [
      { required: true, message: '最小更新版本号不能为空', trigger: 'blur' }
    ],
    versionCode: [
      { required: true, message: '版本号不能为空', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '升级描述不能为空', trigger: 'blur' }
    ]
  },
  // 系统管理-资源管理
  RESOURCERULE: {
    permissionKey: [
      { required: true, message: '授权KEY不能为空', trigger: 'blur' }
    ],
    applicationId: [
      { required: true, message: '所属应用平台不能为空', trigger: 'change' }
    ],
    name: [
      { required: true, message: '资源名称不能为空', trigger: 'blur' }
    ],
    sType: [
      { required: true, message: '资源类型不能为空', trigger: 'change' }
    ],
    sUrl: [
      { required: true, message: '资源的URL不能为空', trigger: 'blur' }
    ],
    sIcon: [
      { required: false, message: '图标不能为空', trigger: 'blur' }
    ]
  },
  // 系统管理-岗位管理
  COMPANY_POSITION: {
    name: [
      { required: true, message: '岗位名称不能为空', trigger: 'blur' }
    ]
  },
  // 添加员工
  ADD_STAFF: {
    name: [
      { required: true, message: '员工姓名不能为空', trigger: 'blur' }
    ],
    mobilephone: [
      { required: true, message: '员工手机号不能为空', trigger: 'blur' }
    ]
  },
  // 配置管理-新增
  CONFIG_ADD: {
    parentId: [
      { required: true, message: '父标志,根节点的值为0不能为空', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '名称不能为空', trigger: 'blur' }
    ],
    sortNo: [
      { required: false, message: '排序号不能为空', trigger: 'blur' }
    ],
    systemic: [
      { required: false, message: '是否为系统内置，系统内置不允许删除；1为系统内置，0为用户自定义不能为空', trigger: 'blur' }
    ]
  },
  // 配置管理-添加配置项
  CONFIG_ITEM: {
    configtypeId: [
      { required: true, message: '类型标志不能为空', trigger: 'blur' }
    ],
    configtypeName: [
      { required: true, message: '所属类型不能为空', trigger: 'blur' }
    ],
    code: [
      { required: true, message: '注意全系统唯一，不能重复不能为空重复不能为空', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '名称不能为空', trigger: 'blur' }
    ],
    comboboxProviderType: [
      { required: false, message: '下拉框提供者类型不能为空', trigger: 'blur' }
    ],
    items: [
      { required: false, message: '下拉选择项集合键值对（xxx:yyy,ttt:yyy）不能为空', trigger: 'blur' }
    ],
    selectItemsSql: [
      { required: false, message: '下拉选择项集合获取sql不能为空', trigger: 'blur' }
    ],
    dateFormat: [
      { required: false, message: '日期格式不能为空', trigger: 'blur' }
    ],
    description: [
      { required: false, message: '描述不能为空', trigger: 'blur' }
    ],
    editorClass: [
      { required: true, message: '控件类型不能为空', trigger: 'change' }
    ],
    readonly: [
      { required: false, message: '是否只读,1为是，0为否不能为空', trigger: 'blur' }
    ],
    value: [
      { required: true, message: '配置项默认值不能为空', trigger: 'blur' }
    ],
    regexpInfo: [
      { required: false, message: '正则表达式提示信息不能为空', trigger: 'blur' }
    ],
    required: [
      { required: false, message: '是否必须,1为是，0为否不能为空', trigger: 'blur' }
    ],
    tabUrl: [
      { required: false, message: 'TAB页URL地址不能为空', trigger: 'blur' }
    ],
    validatorRegexp: [
      { required: false, message: '校验正则表达式不能为空', trigger: 'blur' }
    ],
    sortNo: [
      { required: false, message: '排序号不能为空', trigger: 'blur' }
    ],
    systemic: [
      { required: false, message: '是否为系统内置不能为空', trigger: 'blur' }
    ]
  },
  // 计划任务列表
  PLANY_LIST: {
    beanName: [
      { required: true, message: '执行任务Bean的名称，不能为空', trigger: 'blur' }
    ],
    jobGroup: [
      { required: true, message: '任务分组不能为空', trigger: 'blur' }
    ],
    cronExpression: [
      { required: true, message: 'cron表达式，不能为空', trigger: 'blur' }
    ],
    jobName: [
      { required: true, message: '任务名称，不能为空', trigger: 'blur' }
    ],
    methodName: [
      { required: true, message: '所要执行Bean的方法名称，不能为空', trigger: 'blur' }
    ]
  },
  // 新增发行计划
  NEW_PLAY: {
    name: [
      { required: true, message: '发行计划不能为空', trigger: 'blur' }
    ],
    amount: [
      { required: true, message: '发行总额不能为空', trigger: 'blur' },
      { type: 'number', message: '发行总额必须为数字值' }
    ]
  },
  // 角色管理新增
  NEW_MANAGEMENT: {
    name: [
      { required: true, message: '新增角色不能为空', trigger: 'blur' }

    ],
    applicationId: [
      { required: true, message: '角色类型不能为空', trigger: 'blur' }
    ]
  },
  // 添加公司管理
  ADD_COMPANY: {
    name: [
      { required: true, message: '公司名称不能为空', trigger: 'blur' }
    ],
    type: [
      { required: true, message: '公司类型不能为空', trigger: 'blur' }
    ],
    provinceId: [
      { required: true, message: '所属省份不能为空', trigger: 'blur' }
    ],
    cityId: [
      { required: true, message: '所属城市不能为空', trigger: 'blur' }
    ],
    countyId: [
      { required: true, message: '所属区县不能为空', trigger: 'blur' }
    ],
    address: [
      { required: true, message: '公司地址不能为空', trigger: 'blur' }
    ],
    companyDetail: {
      usCreditCode: [
        { required: true, message: '统一社会信用代码不能为空', trigger: 'blur' }
      ],
      businessLicenseImg: [
        { required: true, message: '营业执照不能为空', trigger: 'blur' }
      ],
      bankAccountName: [
        { required: true, message: '开户名称不能为空', trigger: 'blur' }
      ],
      depositBank: [
        { required: true, message: '开户银行不能为空', trigger: 'blur' }
      ],
      bankAccountNumber: [
        { required: true, message: '开户账号不能为空', trigger: 'blur' }
      ],
      dividendRatio: [
        { required: true, message: '分红比例不能为空', trigger: 'blur' }
      ],
      legalRepresentative: [
        { required: true, message: '法人不能为空', trigger: 'blur' }
      ],
      idCardNo: [
        { required: true, message: '法人身份证号不能为空', trigger: 'blur' }
      ],
      idCardFront: [
        { required: false, message: '法人身份证照片不能为空', trigger: 'change' }
      ],
      principalName: [
        { required: true, message: '负责人不能为空', trigger: 'blur' }
      ],
      mobilephone: [
        { required: true, message: '联系方式不能为空', trigger: 'blur' },
        { validator: validator.phone, message: '手机号码格式不正确' }
      ],
      mail: [
        { required: false, message: '邮箱不能为空', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    },
    companyPaymentAli: {
      payUrl: [
        { required: true, message: '支付地址不能为空', trigger: 'blur' }
      ],
      payAppid: [
        { required: true, message: '支付应用ID不能为空', trigger: 'blur' }
      ],
      publicKey: [
        { required: true, message: '公共密钥不能为空', trigger: 'blur' }
      ],
      privateKey: [
        { required: true, message: '私有密钥不能为空', trigger: 'blur' }
      ],
      callbackUrl: [
        { required: true, message: '支付回调地址不能为空', trigger: 'blur' }
      ],
      productCode: [
        { required: true, message: '商品编码不能为空', trigger: 'blur' }
      ],
      paymentMethod: [
        { required: true, message: '支付方式不能为空', trigger: 'blur' }
      ]
    }
  },
  // 添加员工
  ADD_EMPLOYEE: {
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' }
    ],
    username: [
      { required: true, message: '昵称不能为空', trigger: 'blur' }
    ],
    positionIds1: [
      { required: true, message: '岗位不能为空', trigger: 'change' }
    ],
    mobilephone: [
      { required: true, message: '手机号不能为空', trigger: 'blur' },
      { validator: validator.phone, message: '手机号码格式不正确' }
    ],
    userInfoDetail: {
      avatar: [
        { required: true, message: '头像不能为空', trigger: 'blur' }
      ],
      companyId: [
        { required: true, message: '公司不能为空', trigger: 'change' }
      ],
      departmentId: [
        { required: true, message: '部门不能为空', trigger: 'change' }
      ],
      gender: [
        { required: true, message: '性别不能为空', trigger: 'change' }
      ],
      birthday: [
        { required: true, message: '出生日期不能为空', trigger: 'change' }
      ],
      education: [
        { required: true, message: '学历不能为空', trigger: 'change' }
      ],
      hasCertificate: [
        { required: true, message: '职称不能为空', trigger: 'change' }
      ],
      certificateImg: [
        { required: false, message: '职称证明不能为空', trigger: 'blur' }
      ],
      profession: [
        { required: true, message: '专业不能为空', trigger: 'change' }
      ],
      nationality: [
        { required: true, message: '民族不能为空', trigger: 'change' }
      ],
      bloodType: [
        { required: true, message: '血型不能为空', trigger: 'change' }
      ],
      maritalStatus: [
        { required: true, message: '婚姻状况不能为空', trigger: 'change' }
      ],
      provinceId: [
        { required: true, message: '居住地不能为空', trigger: 'change' }
      ],
      householdProvinceId: [
        { required: true, message: '户籍地不能为空', trigger: 'change' }
      ],
      idCardNo: [
        { required: false, message: '身份证号不能为空', trigger: 'blur' }
      ],
      identityFront: [
        { required: false, message: '身份证不能为空', trigger: 'change' }
      ]
    }
  },
  // 员工调岗
  EMPLOYEE_STATION: {
    name: [
      { required: true, message: '公司不能为空', trigger: 'blur' }
    ],
    departmentId: [
      { required: true, message: '部门不能为空', trigger: 'change' }
    ],
    positionIds: [
      { required: true, message: '岗位不能为空', trigger: 'change' }
    ]
  },
  // 红包活动 节日红包
  REDACTIVITIE: {
    name: [
      { required: true, message: '红包名称不能为空', trigger: 'blur' }
    ],
    totalAmount: [
      { required: true, message: '活动金额不能为空', trigger: 'blur' }
    ],
    ruleActivityStartTime: [
      { required: true, message: '活动生效日期不能为空', trigger: 'blur' }
    ],
    ruleActivityEndTime: [
      { required: true, message: '活动失效日期不能为空', trigger: 'blur' }
    ],
    ruleValidType: [
      { required: true, message: '红包有效期不能为空', trigger: 'change' }
    ],
    ruleLimitRegion: [
      { required: true, message: '限制区域不能为空', trigger: 'change' }
    ],
    ruleLimitPlatform2: [
      { required: true, message: '限制平台不能为空', trigger: 'change' }
    ],
    festivalGeneralRatio: [
      { required: true, message: '普通红包不能为空', trigger: 'blur' }
    ],
    festivalGeneralUseLimit: [
      { required: true, message: '普通红包门槛不能为空', trigger: 'change' }
    ],
    festivalVipRatio: [
      { required: true, message: 'VIP红包不能为空', trigger: 'blur' }
    ],
    festivalVipUseLimit: [
      { required: true, message: 'VIP红包门槛不能为空', trigger: 'change' }
    ],
    festivalEmeraldRatio: [
      { required: true, message: '翡翠红包不能为空', trigger: 'blur' }
    ],
    festivalEmeraldUseLimit: [
      { required: true, message: '翡翠红包门槛不能为空', trigger: 'change' }
    ],
    festivalDiamondRatio: [
      { required: true, message: '钻石红包不能为空', trigger: 'blur' }
    ],
    festivalDiamondUseLimit: [
      { required: true, message: '钻石红包门槛不能为空', trigger: 'change' }
    ]
  },
  // 红包活动 常规红包
  RED_FORM: {
    name: [
      { required: true, message: '红包名称不能为空', trigger: 'blur' }
    ],
    totalAmount: [
      { required: true, message: '活动金额不能为空', trigger: 'blur' }
    ],
    ruleActivityStartTime: [
      { required: true, message: '活动生效日期不能为空', trigger: 'blur' }
    ],
    ruleActivityEndTime: [
      { required: true, message: '活动失效日期不能为空', trigger: 'blur' }
    ],
    ruleValidType: [
      { required: true, message: '红包有效期不能为空', trigger: 'change' }
    ],
    ruleLimitRegion: [
      { required: true, message: '限制区域不能为空', trigger: 'change' }
    ],
    ruleLimitPlatform2: [
      { required: true, message: '限制平台不能为空', trigger: 'change' }
    ]
  },
  // 红包审核
  CHECKPASS: {
    auditStatus: [
      { required: true, message: '审核不能为空', trigger: 'blur' }
    ]
  },
  // 会员管理 冻结
  SUSPEND_ACCOUNT: {
    freezeType: [
      { required: true, message: '审核必须选中一个', trigger: 'change' }
    ],
    typeId: [
      { required: true, message: '冻结原因不能为空', trigger: 'change' }
    ],
    typeName: [
      { required: true, message: '冻结描述不能为空', trigger: "blur" }
    ]
  },
  // 文章管理 列表 新增弹框
  ARTICLE_DIALOG: {
    name: [
      { required: true, message: '名称不能为空', trigger: 'blur' }
    ],
    type: [
      { required: true, message: '类型不能为空', trigger: 'change' }
    ],
    sortNo: [
      { required: true, message: '排序号不能为空', trigger: "blur" }
    ]
  },
  // 文章管理 文章 新增
  ARTICLE_ADD: {
    title: [
      { required: true, message: '标题不能为空', trigger: 'blur' }
    ],
    author: [
      { required: true, message: '作者不能为空', trigger: 'blur' }
    ],
    summany: [
      { required: true, message: '简介不能为空', trigger: 'blur' }
    ],
    isPublication: [
      { required: true, message: '是否发布不能为空', trigger: 'blur' }
    ],
    content: [
      { required: false, message: '文章内容不能为空', trigger: 'blur' }
    ],
    sortNo: [
      { required: true, message: '排序号不能为空', trigger: 'blur' }
    ]
  },
  // 员工资料
  EMPLOYEE_RULE: {
    userInfoDetailVo: {
      avatar: [
        { required: false, message: '头像不能为空', trigger: 'change' }
      ],
      education: [
        { required: false, message: '学历不能为空', trigger: 'change' }
      ],
      hasCertificate: [
        { required: false, message: '职称不能为空', trigger: 'change' }
      ],
      maritalStatus: [
        { required: false, message: '婚姻状况不能为空', trigger: 'change' }
      ],
      alipayNo: [
        { required: false, message: '支付宝不能为空', trigger: 'change' }
      ],
      weixinNo: [
        { required: false, message: '微信不能为空', trigger: 'change' }
      ],
      qqNo: [
        { required: false, message: 'QQ不能为空', trigger: 'change' }
      ],
      email: [
        { required: false, message: '邮箱不能为空', trigger: 'change' }
      ]
    }
  },
  // 公司资料
  COMPANY_RULE: {
    address: [
      { required: false, message: '公司地址不能为空', trigger: 'blur' }
    ],
    companyDetail: {
      principalName: [
        { required: false, message: '负责人不能为空', trigger: 'blur' }
      ],
      mail: [
        { required: false, message: '邮箱不能为空', trigger: 'blur' }
      ]
    }
  },
  // 部门添加
  ADDDEPARTMENT: {
    parentId: [
      { required: true, message: '父标志,根节点的值为0不能为空', trigger: 'blur' }
    ],
    companyId: [
      { required: true, message: '公司标志ID不能为空', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '部门名称不能为空', trigger: 'blur' }
    ],
    sortNo: [
      { required: false, message: '排序号不能为空', trigger: 'blur' }
    ]
  },
  // 添加幸福港
  ADD_PORT: {
    name: [
      { required: true, message: '公司名称不能为空', trigger: 'blur' }
    ],
    provinceId: [
      { required: true, message: '所属省份不能为空', trigger: 'change' }
    ],
    address: [
      { required: true, message: '公司地址不能为空', trigger: 'blur' }
    ],
    companyDetail: {
      usCreditCode: [
        { required: true, message: '统一社会信用代码不能为空', trigger: 'blur' }
      ],
      businessLicenseImg: [
        { required: true, message: '营业执照不能为空', trigger: 'change' }
      ],
      bankAccountName: [
        { required: true, message: '开户名称不能为空', trigger: 'blur' }
      ],
      depositBank: [
        { required: true, message: '开户银行不能为空', trigger: 'blur' }
      ],
      bankAccountNumber: [
        { required: true, message: '开户账号不能为空', trigger: 'blur' }
      ],
      legalRepresentative: [
        { required: true, message: '法人不能为空', trigger: 'blur' }
      ],
      idCardNo: [
        { required: true, message: '法人身份证号不能为空', trigger: 'blur' }
      ],
      idCardFront: [
        { required: true, message: '法人身份证照片不能为空', trigger: 'change' }
      ],
      principalName: [
        { required: true, message: '负责人不能为空', trigger: 'blur' }
      ],
      mobilephone: [
        { required: true, message: '联系方式不能为空', trigger: 'blur' },
        { validator: validator.phone, message: '手机号码格式不正确' }
      ],
      mail: [
        { required: false, message: '邮箱不能为空', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    }
  },

  //新增、编辑活动管理
  ADDANDEDIT: {
    name: [
      { required: true, message: '请输入活动名称', trigger: 'blur' }
    ],
    theme: [
      { required: true, message: '请选择活动主题风格', trigger: 'blur' }
    ],
    dayTickets: [
      { required: true, message: '请选择活动一天最多投票数', trigger: 'blur' }
    ],
    intervalTime: [
      { required: true, message: '请选择活动一天内投票间隔时间', trigger: 'blur' }
    ],
    introduction: [
      { required: true, message: '请输入活动介绍', trigger: 'blur' }
    ],
    startTime: [
      { required: true, message: '请选择活动开始时间', trigger: 'blur' }
    ],
    endTime: [
      { required: true, message: '请选择活动结束时间', trigger: 'blur' }
    ],
    organizerName: [
      { required: true, message: '请输入主办方名称', trigger: 'blur' }
    ],
    coOrganizerName: [
      { required: true, message: '请输入协办单位名称', trigger: 'blur' }
    ],
    activityImage: [
      { required: true, message: '请上传主题图片', trigger: 'blur' }
    ],
    prizeImage: [
      { required: false, message: '请上传部分奖项图片', trigger: 'blur' }
    ],
    selectionForm: [
      { required: true, message: '请输入评选形式', trigger: 'blur' }
    ],
    medias: [
      { required: true, message: '请输入宣传媒体', trigger: 'blur' }
    ],
    workRequirements: [
      { required: true, message: '请输入作品要求', trigger: 'blur' }
    ],
    competitionDescription: [
      { required: true, message: '请输入大赛奖项', trigger: 'blur' }
    ],
    participant: [
      { required: true, message: '请输入参评对象', trigger: 'blur' }
    ],

    winnersList: [
      { required: true, message: '请输入如何获取获奖名单', trigger: 'blur' }
    ],
    importantHint: [
      { required: true, message: '请输入重要提示', trigger: 'blur' }
    ],

  },

  //新增、编辑礼物
  //新增、编辑活动管理
  ADDANDEDITGIFT: {
    name: [
      { required: true, message: '请输入礼物名称', trigger: 'blur' }
    ],
    amount: [
      { required: true, message: '请输入礼物金额', trigger: 'blur' },
      { validator: validator.numbers, message: '礼物金额只能是数字' }
    ],
    sortNo: [
      { required: true, message: '请输入排序号', trigger: 'blur' },
      { validator: validator.numbers, message: '排序号只能是数字' }
    ],
    numberVotes: [
      { required: true, message: '请输入此礼物对应的票数', trigger: 'blur' },
      { validator: validator.numbers, message: '此礼物对应的票数只能是数字' }
    ],
    intervalTime: [
      { required: true, message: '请选择活动一天内投票间隔时间', trigger: 'blur' }
    ],
  },

  //新增、编辑选手
  ADDANDEDITPERSONAL: {
    name: [
      { required: true, message: '请输入选手名称', trigger: 'blur' }
    ],
    images: [
      { required: true, message: '请上传选手照片', trigger: 'blur' }
    ],
  }
}
