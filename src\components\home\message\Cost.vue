<template>
  <div class="box" ref="scrollContainer" @scroll="handleScroll">
    <div class="boxItem" v-for="(item,index) in boxList" :key="index">
      <div class="title">{{ item.projectName }}</div>
      <div class="container">
        <el-row>
          <el-col :span="12">
            <div class="content">编号：{{ item.taskNum }}</div>
          </el-col>
          <el-col :span="12">
            <div class="content">成本金额（万元）：{{ item.costAmount }}</div>
          </el-col>
        </el-row>
      </div>
      <div class="container">
        <div class="content" v-if="item.status == 3">你的项目成本已通过</div>
        <div class="content" v-if="item.status == 4">你的项目成本被驳回</div>
      </div>
      <div class="justBetween">
        <div class="time">提醒时间：{{ item.processTime }}</div>
      </div>
    </div>
    <div v-if="loading" class="loading-text">加载中...</div>
    <div v-if="noMore && boxList.length > 0" class="no-more-text">没有更多数据了</div>
    <el-empty v-if="boxList.length == 0 && !loading" description="暂无项目成本消息"></el-empty>
  </div>
</template>

<script>
import {todoList} from '@/api/index'
import {myMinxin} from '../common/handleScroll.js'


export default {
  mixins:[myMinxin],
  data() {
    return {
      pageBean:{
        pageNum: 1,
        pageSize: 10,
        kind:2,
        ruleType:5
      },
      boxList:[]
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    getList(isLoadMore = false){
      todoList(this.pageBean).then(res=>{
        if (isLoadMore) {
          this.boxList = [...this.boxList, ...res.data]
        } else {
          this.boxList = res.data
        }

        this.noMore = !res.page.hasNextPage
      }).finally(() => {
        this.loading = false
        this.isScrollLoading = false
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.boxItem{
  height: 199px;
  padding: 12px 20px;
  background: #F6F7FB;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 16px;
}
.boxItem:last-child{
  margin-bottom: 0;
}
.title{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #333333;
}
.container{
  border-bottom: 1px solid #E6E9F5;
  padding-bottom: 12px;
  margin-top: 12px;
  margin-bottom: 12px;
}
.content{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}
.content:last-child{
  margin-bottom: 0;
}
.justBetween{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.btn{
  display: flex;
  align-items: center;
  cursor: pointer;
}
.btnText{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #4285F4;
  margin-right: 2px;
}
.btnImg{
  width: 16px;
  height: 17px;
}
.time{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}
.box {
  max-height: 100%;
  overflow-y: auto;
}
.loading-text, .no-more-text {
  text-align: center;
  padding: 20px;
  font-size: 14px;
  color: #999;
}
</style>
