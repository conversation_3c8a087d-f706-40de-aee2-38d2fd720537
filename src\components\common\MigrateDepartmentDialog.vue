<template>
    <el-dialog
         title="选择部门"
         :visible.sync="dialogVisible"
         width="500px"
         :append-to-body="true"
         center
         class="unittree"
         @close="closeAction">
         <div class="condiv">
           <div class="ldiv fixwidth">
             <span class="spantext">部门</span>
             <el-tree
               ref="treeRef"
               :data="treeData"
               show-checkbox
               check-strictly
               node-key="id"
               default-expand-all
               :props="defaultProps"
               @check="handleCheckChange">
             </el-tree>
           </div>
         </div>
         <span slot="footer" class="dialog-footer">
           <el-button type="primary" @click="saveAction">确 定</el-button>
         </span>
       </el-dialog>
 </template>
 
 <script>
 import { listDept } from '@/api/framework/dept'
 
 export default {
     props:{
       visible:{
           type:Boolean,
           default:false,
       },
       currentDepartmentId: {
           type: [String, Number],
           default: ''
       },
       currentParentId: {
           type: [String, Number],
           default: ''
       }
     },
     computed:{
         dialogVisible:{
             get(){
                 return this.visible;
             },
             set(value){
                 this.$emit('updateVisible',value)
             }
         }
     },
     data(){
         return{
             treeData: [],
             defaultProps: {
               children: 'children',
               label: 'name',
               disabled: 'disabled'
             },
             checkedNode: null
         }
     },
 
     methods:{
         loadData(){
           listDept({}).then((result) => {
             // 每次都使用原始数据进行处理，避免累积禁用状态
             this.treeData = this.processTreeData(JSON.parse(JSON.stringify(result.data)));
           })
         },



         processTreeData(data) {
           return data.map(node => {
             const newNode = { ...node };

             // 禁用当前部门
             if (node.id == this.currentDepartmentId) {
               newNode.disabled = true;
             }
             // 禁用当前部门的父部门
             else if (node.id == this.currentParentId) {
               newNode.disabled = true;
             }

             if (node.children && node.children.length) {
               newNode.children = this.processTreeData(node.children);
             }

             return newNode;
           });
         },
         handleCheckChange(data, checked) {
           if (checked.checkedKeys.includes(data.id)) {
             if (this.checkedNode && this.checkedNode.id !== data.id) {
               this.$refs.treeRef.setChecked(this.checkedNode.id, false);
             }
             this.checkedNode = data;
           } else {
             if (this.checkedNode && this.checkedNode.id === data.id) {
               this.checkedNode = null;
             }
           }
         },
         
         closeAction(){
           this.$emit('cancel');
         },
         
         saveAction() {
           if (!this.checkedNode) {
             this.$message.error('请选择部门');
             return;
           }
           
           const selectedDept = {
             id: this.checkedNode.id,
             name: this.checkedNode.name
           };
           
           this.$emit('submit', [selectedDept]);
         },
         
         reset() {
           if (this.$refs.treeRef) {
             
             this.$refs.treeRef.setCheckedKeys([]);
             this.checkedNode = null;
           }
         }
     }
 }
 </script>
 
 <style scoped lang="scss">
 .unittree  /deep/ .el-tree{
   display: inline-block;
   min-width: 100%;
   padding-right: 10px;
 }
 .ptop{
   padding-top: 10px;
 }
 .unittree /deep/.el-dialog__body{
   height: 500px;
   padding: 0 !important;
 }
 .unittree /deep/.el-tree-node__label{
   font-size: 12px !important;
   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
   font-weight: 400;
   color: #333333;
   line-height: 14px;
 }
 .unittree /deep/.el-tree-node__expand-icon{
   color: #333333;
 }
 .unittree /deep/.el-tree-node__expand-icon.is-leaf{
   color: transparent;
 }
 .condiv{
   display: flex;
   height: 100%;
 }
 .ldiv{
   padding-top: 30px;
   padding-left: 26px;
   padding-right: 26px;
   width: 35%;
   overflow-y: scroll;
   overflow-x: scroll;
 }
 
 .fixwidth{
   width: 100%;
   box-sizing: border-box;
 }
 .spantext{
   font-size: 14px;
   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
   font-weight: 400;
   color: #333333;
 }
 </style>