<!-- 树形下拉框 -->
<template>
  <div>
    <el-form-item label="所属部门" prop="departmentId">
    <el-select v-model="myValue" :disabled="disabled">
      <el-option :value="valueTitle" :label="label">
        <el-tree
          id="tree-option"
          ref="selectTree"
          accordion
          :data="options"
          :props="defaultProps"
          @current-change="handleNodeClick"
        ></el-tree>
      </el-option>
    </el-select>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: "el-tree-select",
  props: {
    companyid: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean
    }
  },
  data() {
    return {
      options: [],
      myValue: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      valueTitle: '',
      label: ''
    };
  },
  watch: {
    value(val) {
      // this.myValue = val
    }
  },
  methods: {
    handleNodeClick (data) {
      this.myValue = data.id
      this.$emit('input', this.myValue)
    },
    async init () {
      let data = {
        companyId: this.companyid
        // companyId: '44326098309222097'
      }
      let res = await this.$axios.get('/sf/business/companydepartment/findByCompanyId', { params: data })
      if (res.status === 0) {
        this.options = res.data
      }
    }
  },
  mounted() {
    this.init()
  }
};
</script>

<style scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item{
  height: auto;
  max-height: 274px;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}
.el-select-dropdown__item.selected{
  font-weight: normal;
}
ul li >>>.el-tree .el-tree-node__content{
  height:auto;
  padding: 0 20px;
}
.el-tree-node__label{
  font-weight: normal;
}
.el-tree >>>.is-current .el-tree-node__label{
  color: #409EFF;
  font-weight: 700;
}
.el-tree >>>.is-current .el-tree-node__children .el-tree-node__label{
  color:#606266;
  font-weight: normal;
}
</style>
