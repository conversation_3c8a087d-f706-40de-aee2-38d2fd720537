<template>
  <div>
    <div>
      <back>返回</back>
    </div>
    <div class="mainbg fixpb" v-loading="isLoading">
      <div class="page-header">
        <el-form
          class="myform clearfix"
          ref="form"
          :model="pageBean"
          :inline="true"
        >
          <el-form-item label="单位名称：">
            <el-input
              v-model="pageBean.unitName"
              class="definput inputWid150"
              clearable
              placeholder="单位名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位类型：">
            <el-select
              @change="changeType"
              clearable=""
              class="definput width"
              popper-class="removescrollbar"
              v-model="pageBean.unitType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in unitTypeData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="特点：" v-if="pageBean.unitType">
            <el-select
              clearable=""
              class="definput w150"
              popper-class="removescrollbar"
              v-model="pageBean.unitCharacter"
              placeholder="请选择单位特点"
            >
              <el-option
                v-for="item in characterData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="单位地址:" prop="addressData">
            <el-cascader
              clearable=""
              v-model="addressData"
              class="definput unitf"
              :options="optionsAddress"
              :props="{
                value: 'id',
                label: 'name',
                children: 'areaList',
                checkStrictly: true,
              }"
            ></el-cascader>
          </el-form-item>

          <el-form-item label="">
            <el-button
              class="defaultbtn"
              type="primary"
              icon="el-icon-search"
              @click="onSearch"
              >查询</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <el-table
        ref="unitTable"
        row-key="id"
        class="unittable mytable"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          prop="materialName"
          label="教材名称"
          align="center"
          min-width="200px"
        >
        </el-table-column>
        <el-table-column
          prop="pressName"
          label="出版社"
          align="center"
          min-width="200px"
        >
        </el-table-column>
        <el-table-column
          prop="isbn"
          label="ISBN"
          align="center"
          min-width="200px"
        >
        </el-table-column>
        <el-table-column prop="totalBookNumber" label="用量" align="center">
        </el-table-column>
        <el-table-column
          prop="unitName"
          label="用书单位"
          align="center"
          min-width="200px"
        >
        </el-table-column>
        <el-table-column
          prop="unitTypeName"
          label="单位类型"
          align="center"
          :formatter="valueFormatter"
        >
        </el-table-column>
        <el-table-column
          prop="unitCharacterName"
          label="单位特点"
          align="center"
          min-width="150px"
          :formatter="valueFormatter"
        >
        </el-table-column>
        <el-table-column
          prop="provinceName"
          label="省"
          align="center"
          min-width="150px"
        >
        </el-table-column>
        <el-table-column
          prop="cityName"
          label="市"
          align="center"
          min-width="150px"
        >
          <template slot-scope="scope">
            {{ scope.row.cityName ? scope.row.cityName : '--' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="edit"
          width="120"
          align="center"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              class="bbtn tabbtn"
              type="text"
              @click="onViewBookData(scope.row)"
            >
              查看用书信息</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <nolist></nolist>
        </template>
      </el-table>
      <div class="fixpage">
        <page
          :currentPage="pageBean.pageNum"
          :total="total"
          :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"
        ></page>
      </div>
    </div>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import { queryMaterialStatisticsDetail } from '@/api/kanban/index'
import { getDict } from '@/utils/tools'
import { getParStr } from '@/utils/tools'
import { queryAreaVoList } from '@/api/index'
import { queryUnitCharacter } from '@/api/clientmanagement/unit'
export default {
  components: {
    page,
    nolist,
    back,
  },
  data() {
    return {
      optionsAddress: [],
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        unitName: '',
        pageNum: 1,
        pageSize: 10,
        unitType: '',
        unitCharacter: '',
        provinceId: '',
        cityId: '',
        materialName: this.$route.query.materialName,
      },
      addressData: [],
      unitTypeData: [],
      characterData: [],
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
      if (this.$route.query.unitType) {
        this.queryUnitCharacterApi(this.$route.query.unitType)
      }
      // if (this.$route.query.provinceId) {
      //   this.addressData[0] = this.$route.query.provinceId
      // }
      // if (this.$route.query.cityId) {
      //   this.addressData[1] = this.$route.query.cityId
      // }
    }
    this.loadData()
    getDict('UnitType').then((result) => {
      this.unitTypeData = result
    })
    this.queryAreaVoListApi()
  },
  methods: {
    onViewBookData(row) {
      this.$router.push({
        path: '/kanban/bookdata/bookusedata',
        query: {
          materialId: row.materialId,
          unitId: row.unitId,
        },
      })
    },
    handleTreeList(list) {
      for (var i = 0; i < list.length; i++) {
        if (list[i].areaList.length < 1) {
          list[i].areaList = undefined
        } else {
          this.handleTreeList(list[i].areaList)
        }
      }
      return list
    },
    queryAreaVoListApi() {
      queryAreaVoList({ level: 0 }).then((res) => {
        if (res.status == 0) {
          this.optionsAddress = this.handleTreeList(res.data)
        }
      })
    },
    valueFormatter(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue
      }
      return '—'
    },
    queryUnitCharacterApi(val, boolean) {
      queryUnitCharacter({ parentId: val, isDeleted: 0 }).then((res) => {
        if (res.status == 0) {
          if (boolean) {
            this.pageBean.unitCharacter = ''
          }
          this.characterData = res.data
        }
      })
    },
    changeType(val) {
      if (!val) {
        this.characterData = []
        this.pageBean.unitCharacter = ''
      } else {
        this.queryUnitCharacterApi(val, true)
      }
    },
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      queryMaterialStatisticsDetail(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    onSearch() {
      if (this.addressData) {
        if (this.addressData.length == 1) {
          this.pageBean.provinceId = this.addressData[0]
          this.pageBean.cityId = ''
        } else {
          this.pageBean.provinceId = this.addressData[0]
          this.pageBean.cityId = this.addressData[1]
        }
      } else {
        this.pageBean.provinceId = ''
        this.pageBean.cityId = ''
      }
      this.pageBean.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>

<style lang="scss" scoped>
.cpdepartmentcss {
  font-size: 12px !important;
}
.ml20 {
  margin-right: 16px;
}
.m0 {
  margin-right: 0px !important;
}
.defbtn {
  cursor: pointer;
}

.page-header {
  display: flex;
  justify-content: space-between;
}

.unittable .el-button + .el-button {
  margin-left: 20px;
}

.tabbtn {
  font-size: 14px;
  cursor: pointer;
  padding: 2px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.btn {
  height: 34px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  letter-spacing: 1px;
}

.search {
  width: 96px;
}

.mainbg {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  min-height: 100%;
  margin-top: 20px;
}
.myform .el-form-item:last-child {
  margin-right: 20px !important;
}
</style>
