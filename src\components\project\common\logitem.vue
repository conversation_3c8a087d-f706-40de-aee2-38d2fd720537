<template>
  <div class="logitem">
    <div class="timecss">
        {{itemData.createTime}}
    </div>
    <div class="right">
        <div class="linecss" :class="getClassStyle()">
            <div class="timeline-icon-con"></div>
        </div>
        <div class="listcss" v-if="itemData && itemData.projectLogVoList.length>0">
            <div class="logmsg" v-for="(item,index) in itemData.projectLogVoList" :key="index"  :class="getClassStyle2(index,itemData.projectLogVoList.length-1)">
                <div class="timeline-icon-con2">
                </div>
                <div class="timetext ml24">
                    {{getDateTime(item.createTime)}}
                </div>
                <div class="usrname timetext"> {{item.createByName}}</div>
                <div class="edtype ">{{item.operate}} </div>
                <div class="usrname timetext lh"> {{item.content}}</div>
            </div>
        </div>
    </div>
    
  </div>
</template>

<script>
export default {
    props:{
        itemData:{
            type:Object,
            default:()=>{
                return new Object()
            }
        },
        isFirst:{
            type:Boolean,
            default:false,
        },
        isLast:{
            type:Boolean,
            default:false
        }
    },
    data(){
        return{

        }
    },
    created(){
        
    },
    methods:{
        getDateTime(str){
            return str.substring(11,str.length-3)
        },
        getClassStyle(){
            if (this.isFirst && this.isLast) {
                return ''
            }
            if(this.isFirst){
                return "isfirst"
            }
            if (this.isLast) {
                return 'islast'
            }
            
            return 'deflinecss'
        },
        getClassStyle2(index,last){
            if (index  == 0 && last == 0) {
                return ''
            }
            if(index == 0){
                return "logfirst"
            }
            if (index == last) {
                return 'loglast'
            }
            return 'logline'
        },
    }
}
</script>

<style scoped>
.lh{
    line-height: 20px;
    text-align: justify !important;
    overflow-wrap: break-word !important;
}
.edtype{
    padding: 0 10px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #989898;
    min-width: 60px;

}
.usrname{
    min-width: 70px;
    text-align: center;
}
.ml24{
    margin-left: 20px;
}
.timetext{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    min-width: 60px;
}
.logmsg:last-child{
    padding-bottom: 0px;
}
.logmsg{
    display: flex;
    align-items: center;
    position: relative;
    min-height: 30px;
    margin: 3px 0;
    padding-bottom: 24px;
}
.loglast::before{
    /* content: '';
    position: absolute;
    left: 4px;
    top: 0;
    height: 50%;
    border-left: 2px dashed #E6E6E6;
    background-size:1px 6px; */
}
.logfirst::before{
    content: '';
    position: absolute;
    left: 4px;
    top: 10px;
    height: calc(100% - 10px);
    border-left: 2px dashed #E6E6E6;
    background-size:1px 6px;
}
.logline::before{
    content: '';
    position: absolute;
    left: 4px;
    top: 0;
    height: 100%;
    border-left: 2px dashed #E6E6E6;
    background-size:1px 6px;
}
.listcss{
    border-radius: 8px 8px 8px 8px;
    border: 1px dashed #D9D9D9;
    padding: 24px 20px;
    width: 100%;
    margin: 16px 0;
}
.timeline-icon-con2{
    min-width: 8px;
    height: 8px;
    background-color: #D9D9D9;
    border-radius: 4px;
    z-index: 1;
}
.right{
    display: flex;
    align-items: stretch;
    width: 100%;
    /* padding: 16px 0px; */
}
.timeline-icon-con{
    position: absolute;
    left: calc(50% - 5px);
    top: calc(50% - 5px);
    width: 10px;
    height: 10px;
    background-color: #D9D9D9;
    border-radius: 5px;
    z-index: 1;
}
.linecss{
    position: relative;
    min-width: 120px;
    min-height: 100px;
    margin:3px 0px;
}
.linecss::after{
    content: '';
    position: absolute;
    left: 0px;
    top:calc(50% - 0.5px);
    width: 100%;
    border-top: 1px dashed #E6E6E6;

}
.deflinecss::before{
    content: '';
    position: absolute;
    left: calc(50% - 1px);
    top: 0;
    height: 100%;
    border-left: 2px dashed #E6E6E6;
    background-size:1px 6px;
}
.isfirst::before{
    content: '';
    position: absolute;
    left: calc(50% - 1px);
    top: calc(50% - 0.5px);
    height: 50%;
    border-left: 2px dashed #E6E6E6;
    background-size:1px 6px;
}
.islast::before{
    content: '';
    position: absolute;
    left: calc(50% - 1px);
    top:0px;
    height: 50%;
    border-left: 2px dashed #E6E6E6;
    background-size:1px 6px;
}
.timecss{
    min-width: 160px;
    height: 50px;
    line-height: 50px;
    background: #ECF3FF;
    border-radius: 4px 4px 4px 4px;
/* text */
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 20px;
    color: #4285F4;
    text-align: center;

}
.logitem{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100% !important;
}
</style>