<template>
  <div class="fdiv">
    <el-form :model="form" ref="form" label-width="120px" :rules="rules">
      <el-form-item prop="workTime" label="日期：" v-if="recordType == 'work'">
        <el-date-picker
          value-format="yyyy-MM-dd"
          class="definput w100"
          v-model="form.workTime"
          type="date"
          placeholder="请选择日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="costDate" label="日期：" v-if="recordType == 'cost'">
        <el-date-picker
          value-format="yyyy-MM-dd"
          class="definput w100"
          v-model="form.costDate"
          type="date"
          placeholder="请选择日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        prop="workHours"
        label="消耗工时："
        v-if="recordType == 'work'"
      >
        <el-input
          @input="limitWorkHoursNum"
          type="number"
          :max="9999"
          v-model="form.workHours"
          placeholder="请输入"
          ><template slot="append">小时</template></el-input>
      </el-form-item>

      <el-form-item
        prop="content"
        :label="`${getText[recordType].label}：`"
        v-if="recordType == 'work'"
      >
        <el-input
          maxlength="200"
          show-word-limit
          type="textarea"
          v-model="form.content"
          :rows="6"
          :placeholder="getText[recordType].pltext"
        ></el-input>
      </el-form-item>
      <el-form-item
        prop="reason"
        :label="`${getText[recordType].label}：`"
        v-if="recordType == 'cost'"
      >
        <el-input
          maxlength="200"
          show-word-limit
          type="textarea"
          v-model="form.reason"
          :rows="6"
          :placeholder="getText[recordType].pltext"
        ></el-input>
      </el-form-item>

      <el-form-item
        prop="workType"
        label="工作产出："
        v-if="recordType == 'work'"
      >
        <el-select
          v-model="form.workType"
          placeholder="请选择工作产出类型"
          clearable
        >
          <el-option
            v-for="item in workTypeOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        prop="costType"
        v-if="recordType == 'cost'"
        label="费用类型："
      >
        <el-select v-model="form.costType" placeholder="请选择费用类型">
          <el-option
            v-for="item in costTypeOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.workType === 1" label="时长：">
        <el-input
          @input="limitPhoneNum"
          class="minute"
          type="number"
          :max="9999"
          v-model="minuteValue"
          placeholder="请输入分钟"
        ></el-input>
        <span class="linecss">--</span>
        <el-input
          @input="limitToSeconds"
          class="second"
          :max="9999"
          v-model="secondValue"
          placeholder="请输入秒(00)"
        ></el-input>
      </el-form-item>

      <el-form-item v-if="form.workType && form.workType != 1" :label="workTypeOptionMap[form.workType] + '：'">
        <el-input-number
          class="definput widm"
          v-model="form.duration"
          :controls="false"
          :precision="0"
          :step="1"
          :max="9999"
          placeholder="请输入课件页数"
        ></el-input-number>
      </el-form-item>

      <!-- <el-form-item v-if="form.workType === 3" label="拍摄讲数：">
        <el-input-number
          class="definput widm"
          v-model="form.duration"
          :controls="false"
          :precision="0"
          :step="1"
          :max="9999"
          placeholder="请输入拍摄讲数"
        ></el-input-number>
      </el-form-item> -->
      <el-form-item
        prop="costAmount"
        v-if="recordType == 'cost'"
        label="金额："
      >
        <el-input-number
          clearable
          :controls="false"
          :min="0"
          :max="99999999"
          :precision="2"
          v-model="form.costAmount"
          class="definput widm textleft"
          placeholder="请输入金额（单位：元）"
        ></el-input-number>
      </el-form-item>
    </el-form>
    <el-button
      :disabled="taskStatus == 3 || taskStatus == 5"
      class="submitBtn"
      type="primary"
      @click="submit"
      >提交</el-button
    >
  </div>
</template>
  
  <script>
import { mapGetters } from 'vuex'
import { queryVoListByCode } from '@/api/wapi.js'
import { workTypeOptions,workTypeOptionMap } from '@/utils/dict'
export default {
  props: {
    recordType: {
      type: String,
      default: 'work',
    },
  },
  data() {
    return {
      form: {
        taskId: '',
        type: '',
        workHours: '',
        costDate: '',
        duration: '',
        workTime: '',
        content: '',
        projectType: '',
        costType: '',
        costAmount: undefined,
        workType: '',
      },
      secondValue: '',
      minuteValue: '',
      rules: {
        content: [
          { required: true, message: '请输入本次工作内容', trigger: 'blur' },
        ],
        workTime: [
          { required: true, message: '请选择日期', trigger: 'change' },
        ],
        costDate: [
          { required: true, message: '请选择日期', trigger: 'change' },
        ],
        reason: [
          { required: true, message: '请输入成本事项', trigger: 'blur' },
        ],
        workHours: [
          { required: true, message: '请输入消耗工时', trigger: 'blur' },
        ],
        costAmount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
        ],
        workType: [
          { required: true, message: '请选择工作产出类型', trigger: 'change' },
        ],
        costType: [
          { required: true, message: '请选择费用类型', trigger: 'change' },
        ],
      },

      // 工作产出选项
      workTypeOptions,
      workTypeOptionMap,
      // 费用类型选项
      costTypeOptions: [],
      getText: {
        work: {
          label: '工作内容',
          pltext: '请输入本次工作内容',
        },
        cost: {
          label: '事项',
          pltext: '请输入成本事项',
        },
      },
    }
  },
  computed: {
    ...mapGetters(['taskStatus']),
  },
  created() {
    this.loadTypes()
  },
  methods: {
    loadTypes() {
      queryVoListByCode({ code: 'CostType' }).then((res) => {
        if (res.status == 0) {
          this.costTypeOptions = res.data
        }
      })
    },
    limitPhoneNum(value) {
      if (value.toString().length > 4) {
        this.minuteValue = this.minuteValue.toString().slice(0, 4)
      }
    },
    limitWorkHoursNum(value) {
      if (value.toString().indexOf('.') >= 0) {
        this.form.workHours = value
          .toString()
          .substring(0, value.toString().indexOf('.') + 2)
      }
    },
    limitToSeconds(value) {
      value = value.replace('.', '')
      const regex = /^[0-5][0-9]?$/
      if (!regex.test(value)) {
        this.secondValue = ''
      } else {
        this.secondValue = value
      }
    },
    resetForm() {
      this.$refs.form.resetFields()
      for (let key in this.form) {
        if (key == 'costAmount') {
          this.form[key] = undefined
        } else {
          this.form[key] = ''
        }
      }
      this.minuteValue = ''
      this.secondValue = ''
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.workType === 1) {
            if (this.minuteValue !== '' || this.secondValue !== '') {
              let minutes = this.minuteValue !== '' ? this.minuteValue : '00'
              let seconds = this.secondValue !== '' ? this.secondValue : '00'

              if (minutes.length === 1) {
                minutes = '0' + minutes
              }
              if (seconds.length === 1) {
                seconds = '0' + seconds
              }

              this.form.duration = `${minutes}:${seconds}`
            } else {
              this.form.duration = ''
            }
          }

          this.$emit('submitData', {
            form: this.form,
            recordType: this.recordType,
          })
        } else {
          return false
        }
      })
    },
  },
}
</script>
  
  <style lang="scss" scoped>
.widm {
  width: 100%;
}
.textleft /deep/.el-input__inner {
  text-align: left !important;
}
.linecss {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  width: 10%;
}
.minute {
  width: 45%;
}
.second {
  width: 45%;
  float: right;
}
.submitBtn {
  width: 80px;
  padding: 10px 0 !important;
  display: block;
  margin: auto;
  margin-top: 60px;
}
.bottom {
}
.fdiv {
  height: 100%;
  position: relative;
}
.w100.el-input {
  width: 100% !important;
}
</style>