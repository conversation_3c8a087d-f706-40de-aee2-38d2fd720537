<template>
  <div class="commentdiv" :class="{secondcss:item.parentId!=0}">
      <ul>
        <li class="commentli">
          <div>
                <headuser :url="item.userLogo" width="40" :username="item.userName"></headuser>
                <p class="">
                  <span class="pname">{{ item.userName }}</span>
                  <span class="spantext">{{ item.createTime }}</span>
                  <span @click="replayAction" class="reply" v-if="item.parentId == 0">回复</span>
                  <span class="reply dBtn" v-if="(userId == item.createBy) || (dataScope == 4)" @click="deleteAction" v-dbClick >删除</span>
                </p>
                <p class="ptext">{{ item.comment }}</p>
            </div>
            <template v-if="item.childrenNum" >
              <commentitem :item="item" v-for="(item,index) in item.childrenList" :key="index" v-on="$listeners"></commentitem>
            </template>
        </li>
      </ul>
  </div>
</template>

<script>
import touxiang from '@/assets/touxiang.png';
import headuser from './headuser.vue'
  export default {
    name:'commentitem',
    props:{
      item:{
        type:Object,
        default:()=>{}
      }
    },
    components:{
      headuser
    },
    created(){
    },
    data(){
      return{
        touxiang:touxiang,
        userId:window.sessionStorage.getItem('userid'),
        dataScope:window.sessionStorage.getItem('dataScope'),
      }
    },
    methods:{
      replayAction(){
        this.$emit('reply',{id:this.item.id,name:this.item.userName})
      },
      deleteAction(){
        this.$emit('deleteAction',this.item.id)
      }
    }
    
  }
</script>

<style lang="scss" scoped>
.dBtn{
  color: #F45961 !important;
}
.pname{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-right: 12px;
}
.secondcss{
  margin-left: 47px !important;
  margin-top: 20px !important;
}
.commentli{
  margin-bottom: 20px;
}
.ptext{
  margin-top: 8px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-left: 48px;
  line-height: 23px;
}
.reply{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  margin-left: 16px;
  cursor: pointer;
}
.spantext{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.head{
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 8px;
}
.commentdiv{
  margin-left: 14px;
  margin-top: 10px;
}

</style>