  <template>
  <div>
    <back>返回</back>
    <div class="maindiv" :class="{ pb100: setupData.isUserExamine }">
      <div class="owntop clearfix" :class="{ w60: isShow }">
        <div class="plantitle">
          <div>{{ titleStr.mainTitle }}</div>
          <div v-if="titleStr.subTitle" class="subtitle">
            ({{ titleStr.subTitle }})
          </div>
        </div>
        <div class="personalplan">
          <textBorder class="ltextcss"
            >个人{{ types[form.goalsType].label }}</textBorder
          >
          <el-form class="myform" inline>
            <el-row :gutter="10">
              <el-col :span="8">
                <div class="carditem">
                  <div class="flex">
                    <img class="picon" src="../../assets/img2.png" alt="" />
                    <div class="ptitle">新增拜访与跟进（次）</div>
                  </div>
                  <div>
                    <div class="pconcss">
                      {{
                        (formLabelAlign.newVisit && formLabelAlign.newVisit) ||
                        '--'
                      }}
                    </div>
                    <div class="pmbcss">目标</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="carditem">
                  <div class="flex">
                    <img class="picon" src="../../assets/img5.png" alt="" />
                    <div class="ptitle">新增客户(个)</div>
                  </div>
                  <div>
                    <div class="pconcss">
                      {{
                        (formLabelAlign.newCustomers &&
                          formLabelAlign.newCustomers) ||
                        '--'
                      }}
                    </div>
                    <div class="pmbcss">目标</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="carditem">
                  <div class="flex">
                    <img class="picon" src="../../assets/img3.png" alt="" />
                    <span class="ptitle">业绩(万元)</span>
                  </div>
                  <div>
                    <div class="pconcss">
                      {{
                        (formLabelAlign.contractAmount &&
                          formLabelAlign.contractAmount) ||
                        '--'
                      }}
                    </div>
                    <div class="pmbcss">目标</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="carditem">
                  <div class="flex">
                    <img class="picon" src="../../assets/img4.png" alt="" />
                    <div class="ptitle">回款金额(万元)</div>
                  </div>
                  <div class="pconcss">
                    {{
                      (formLabelAlign.contractReturnAmount &&
                        formLabelAlign.contractReturnAmount) ||
                      '--'
                    }}
                  </div>
                  <div class="pmbcss">目标</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="carditem">
                  <div class="flex">
                    <img
                      class="picon"
                      src="../../assets/hetongicon.png"
                      alt=""
                    />
                    <div class="ptitle">新增合同(个)</div>
                  </div>
                  <div>
                    <div class="pconcss">
                      {{
                        (formLabelAlign.newContract &&
                          formLabelAlign.newContract) ||
                        '--'
                      }}
                    </div>
                    <div class="pmbcss">目标</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="carditem">
                  <div class="flex">
                    <img
                      class="picon"
                      src="../../assets/yangshuicon.png"
                      alt=""
                    />
                    <div class="ptitle">样书发放(次)</div>
                  </div>
                  <div>
                    <div class="pconcss">
                      {{
                        (formLabelAlign.materialDeliverNum &&
                          formLabelAlign.materialDeliverNum) ||
                        '--'
                      }}
                    </div>
                    <div class="pmbcss">目标</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <textBorder v-if="form.goalsType == '1' && unitGoalList.length > 0"
          >单位年度目标</textBorder
        >
        <div v-if="form.goalsType == '1'">
          <el-row :gutter="8">
            <el-col :span="6">
              <unititem
                name="信息化业绩"
                unit="万元"
                :icon="require('../../assets/img3.png')"
                :num="unitData.informationAmount"
              ></unititem>
            </el-col>
            <el-col :span="6">
              <unititem
                name="教材业绩"
                unit="万元"
                :icon="require('../../assets/img4.png')"
                :num="unitData.teachingMaterialAmount"
              ></unititem>
            </el-col>
            <el-col :span="6">
              <unititem
                name="合同数量"
                unit="个"
                :icon="require('../../assets/hetongicon.png')"
                :num="unitData.totalContract"
              ></unititem>
            </el-col>
            <el-col :span="6">
              <unititem
                name="新增跟进与拜访"
                unit="次"
                :icon="require('../../assets/img2.png')"
                :num="unitData.totalVisit"
              ></unititem>
            </el-col>
          </el-row>
        </div>
        <div
          class="unitplan"
          v-if="form.goalsType == '1' && unitGoalList.length > 0"
        >
          <el-table
            class="mytable"
            v-for="(item, index) in unitGoalList"
            :key="index"
            :data="item"
            :span-method="objectSpanMethod"
            border
          >
            <el-table-column prop="unitName" label="学校">
              <template slot-scope="scope">
                <span>{{ scope.row.unitName }} </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="unitgoalTypeName"
              label="目标维度"
              width="200"
            ></el-table-column>
            <el-table-column prop="goalNum" label="目标" width="200">
              <template v-slot="{ row }">
                <span>{{ row.goalNum }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div>
          <textBorder>计划内容</textBorder>
          <div class="pt20">
            <showmodel
              :viewDataList="formLabelAlign.templateItemList"
            ></showmodel>
          </div>
        </div>
        <div>
          <textBorder>抄送人</textBorder>
          <div class="pt20">
            <ul class="uflex" v-if="formLabelAlign.copyPersons">
              <li
                v-for="(item, index) in formLabelAlign.copyPersons"
                :key="index"
                class="pli"
              >
                <img v-if="item.logo" class="pimg" :src="item.logo" alt="" />
                <div class="noimg" v-else>
                  {{ item.name }}
                </div>
                <p class="pname">{{ item.name }}</p>
              </li>
            </ul>
          </div>
        </div>
        <div>
          <textBorder>审核记录</textBorder>
        </div>
        <!-- 评论 -->

        <div>
          <mystep :setupData="setupData"></mystep>
        </div>
        <div>
          <textBorder>评论</textBorder>
        </div>
        <div class="savediv">
          <el-input
            show-word-limit
            maxlength="400"
            clearable=""
            ref="elRef"
            class="inputcommont"
            v-model.trim="value"
            placeholder="请输入评论内容"
          ></el-input>
          <el-button @click="commentButton()" class="savebutton" type="primary"
            >发送</el-button
          >
        </div>

        <!-- 评论列表 -->
        <el-collapse-transition>
          <div v-show="expand">
            <comment
              @reply="reply"
              v-for="(item, index) in commentArr"
              :key="index"
              :item="item"
              @deleteAction="deleteAction"
            ></comment>
          </div>
        </el-collapse-transition>

        <div class="buttoncursor" @click="expandOrShou()">
          <img
            v-show="!expand && commentArr.length > 0"
            src="@/assets/xia.png"
            alt=""
          />
          <span v-show="!expand && commentArr.length > 0" class="expand"
            >展开</span
          >

          <img
            v-show="expand && commentArr.length > 0"
            src="@/assets/shang.png"
            alt=""
          />
          <span v-show="expand && commentArr.length > 0" class="expand"
            >收起</span
          >
        </div>
        <replyDialog
          ref="replyDialog"
          :visible.sync="replayDialogVisible"
          :title="replayTitle"
          @updateVisible="updateReplayVisible"
          @replyData="replyData"
        ></replyDialog>
      </div>

      <div class="rightdiv" :class="{ w40: isShow }">
        <div v-if="isShow" class="reviewmask">
          <planreview
            ref="planreview"
            v-if="reviewType == 'planreview'"
            :date="planTime"
            :page="pageData"
            :viewData="viewData"
            @closereview="closereview"
            :plantype="form.goalsType"
          ></planreview>
          <summaryreview
            v-if="reviewType == 'summaryreview'"
            @closereview="closereview"
            :date="planTime"
            :page="pageData"
            :viewData="viewData"
            :plantype="`${+form.goalsType + 3}`"
          ></summaryreview>
        </div>
        <div v-else class="tright">
          <div class="bbtn" id="planreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            计划回顾
          </div>
          <div class="bbtn" id="summaryreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            总结回顾
          </div>
        </div>
      </div>
    </div>

    <div class="bottomdiv" v-if="setupData.isUserExamine">
      <el-button
        class="clasbtn"
        type="primary"
        dblclick
        @click="changeLogsStatus(3)"
        >驳回</el-button
      >
      <el-button
        class="clasbtn"
        type="primary"
        dblclick
        @click="changeLogsStatus(2)"
        >通过</el-button
      >
    </div>

    <el-dialog
      title="选择回顾时间"
      :visible.sync="dialogPlanVisible"
      width="340px"
      center
      :before-close="handlePlanClose"
    >
      <span>
        <el-form class="mt30" label-width="85px">
          <el-form-item
            :label="`${
              types[
                reviewType == 'summaryreview'
                  ? +form.goalsType + 3
                  : form.goalsType
              ].label
            }：`"
          >
            <el-date-picker
              class="definput"
              :picker-options="pickerOptions"
              @change="changeDatePicker"
              v-model="planTime"
              :type="types[form.goalsType].type"
              :format="types[form.goalsType].format"
              placeholder="请选择"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextAction">下一步</el-button>
      </span>
    </el-dialog>
    <esign
      :dialogVisible="dialogVisible"
      @updateVisible="updateVisible"
      @submitAction="submitAction"
      :hasLastSign="isHas"
    ></esign>
  </div>
</template>

  <script>
import { sendSave, commentList, commentDelete } from '@/api/visit/index'
import comment from '@/components/clientMaintenance/followVisit/components/comment.vue'
import replyDialog from '@/components/common/replyDialog.vue'
import textBorder from '@/components/common/textBorder.vue'
import back from '@/components/common/back.vue'
import mystep from '@/components/common/mystep.vue'
import planreview from '@/components/common/planreview.vue'
import summaryreview from '@/components/common/summaryreview.vue'
import { deepClone } from '@/utils/tools.js'
import { getWeek, getPlanTime } from '@/utils/index'
import { toCheckList, check, jiHuaInfo, queryReviewGoals } from '@/api/goal'
import showmodel from '@/components/zongjie/temmodel/showmodel.vue'
import esign from '@/components/common/esign.vue'
import unititem from './common/unititem.vue'
export default {
  components: {
    back,
    textBorder,
    planreview,
    summaryreview,
    showmodel,
    mystep,
    esign,
    unititem,
    comment,
    replyDialog,
  },
  data() {
    return {
      value: '',
      replayDialogVisible: false,
      replayTitle: '',
      commentArr: [],
      expand: false,
      parentId: 0,
      isChange: false,
      isHas: false,
      titleStr: '',
      pickerOptions: {
        disabledDate: this.endPickerTime,
      },
      isShow: false,
      isLoading: false,
      dialogPlanVisible: false,
      getUrl: `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`,
      year: new Date(this.$route.query.time).getFullYear(),
      tableData: [],
      fileList: [],
      types: {
        1: {
          name: '年计划',
          label: '年度计划',
          type: 'year',
          format: 'yyyy',
        },
        2: {
          name: '月计划',
          label: '月度计划',
          type: 'month',
          format: 'yyyy-M',
        },
        3: {
          name: '周计划',
          label: '周计划',
          type: 'week',
          format: 'yyyy-W',
        },
        4: {
          name: '年总结',
          label: '年度总结',
          type: 'year',
          format: 'yyyy',
        },
        5: {
          name: '月总结',
          label: '月度总结',
          type: 'month',
          format: 'yyyy-M',
        },
        6: {
          name: '周总结',
          label: '周总结',
          type: 'week',
          format: 'yyyy 第 W 周',
        },
      },
      form: {
        goalsType: this.$route.query.type + '',
        year: '',
        month: '',
        week: '',
        pageNum: 1,
        pageSize: 1,
      },
      reviewType: '',
      goalDetailList: [
        {
          goalType: 1,
          goalTypeName: '新增客户',
          goalTypeKey: 'newCustomers',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 2,
          goalTypeName: '新增拜访',
          goalTypeKey: 'newVisit',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 3,
          goalTypeName: '新增机会',
          goalTypeKey: 'newOpportunity',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 4,
          goalTypeName: '新增合同',
          goalTypeKey: 'newContract',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 5,
          goalTypeName: '合同金额（万元）',
          goalTypeKey: 'contractAmount',
          sum: 0,
          year: this.year,
        },
        {
          goalType: 6,
          goalTypeName: '回款金额（万元）',
          goalTypeKey: 'contractReturnAmount',
          sum: 0,
          year: this.year,
        },
      ],
      keyDict: {
        1: 'newCustomers',
        2: 'newVisit',
        3: 'newOpportunity',
        4: 'newContract',
        5: 'contractAmount',
        6: 'contractReturnAmount',
      },
      nameDict: {
        1: '新增客户',
        2: '新增拜访',
        3: '新增机会',
        4: '新增合同',
        5: '合同金额（万元）',
        6: '回款金额（万元）',
      },
      options: [],
      labelPosition: 'right',
      formLabelAlign: {
        newCustomers: null,
        newContract: null,
        newVisit: null,
        newOpportunity: null,
        contractAmount: null,
        contractReturnAmount: null,
        year: this.year,
        personalGoalsUnitList: [],
        personalGoalsDetailList: [],
        copyPersons: [],
      },
      unitData: {
        informationAmount: 0,
        teachingMaterialAmount: 0,
        totalContract: 0,
        totalVisit: 0,
      },
      unitGoalItem: [
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'informationAmount',
          unitgoalTypeName: '信息化业绩（万元）',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'teachingMaterialAmount',
          unitgoalTypeName: '教材业绩金额（万元）',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'totalContract',
          unitgoalTypeName: '合同数量',
          goalNum: '',
          year: '',
        },
        {
          unitId: '',
          unitName: '',
          unitgoalType: 'totalVisit',
          unitgoalTypeName: '拜访次数',
          goalNum: '',
          year: '',
        },
      ],
      keyNames: {
        informationAmount: '信息化业绩（万元）',
        teachingMaterialAmount: '教材业绩金额（万元）',
        totalContract: '合同数量',
        totalVisit: '拜访次数',
      },
      unitGoalItem2: [],
      unitGoalList: [],
      unitIds: [],
      unitDialogVisible: false,
      currentYear: '',
      planTime: '',
      setupData: {},
      viewData: [],
      pageData: {},
      dialogVisible: false,
      statusPar: {
        relateId: this.$route.query.id,
        logsStatus: '',
        url: '',
      },
      peopleList: [],
      defunitList: [
        [
          {
            unitName: '暂未选择学校',
            unitgoalType: 'informationAmount',
            unitgoalTypeName: '信息化业绩（万元）',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'teachingMaterialAmount',
            unitgoalTypeName: '教材业绩金额（万元）',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'totalContract',
            unitgoalTypeName: '合同数量',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'totalVisit',
            unitgoalTypeName: '拜访次数',
            goalNum: '--',
          },
        ],
      ],
    }
  },
  mounted: function () {},
  created() {
    this.unitGoalItem2 = deepClone(this.unitGoalItem)
    this.loadInfo()
    this.loadAuditList()
    this.commentListData()
  },

  methods: {
    commentButton() {
      if (this.value == '') {
        this.msgError('请输入评论内容')
        return
      }
      this.save(this.value)
    },

    save(comment) {
      let params = {
        visitId: this.$route.query.id,
        comment: comment,
        commentType: 3,
        parentId: this.parentId || 0,
      }
      sendSave(params)
        .then((res) => {
          if (res.status == 0) {
            this.commentListData()
            this.expand = true
            this.value = ''
          }
          this.parentId = 0
        })
        .catch((err) => {
          this.parentId = 0
        })
    },

    commentListData() {
      commentList({ visitId: this.$route.query.id }).then((res) => {
        if (res.status == 0) {
          this.commentArr = res.data
        }
      })
    },

    deleteAction(commentId) {
      commentDelete({ id: commentId })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.commentListData()
          } else {
            this.$message({
              type: 'error',
              message: '删除失败',
            })
          }
        })
        .catch((err) => {})
    },

    expandOrShou() {
      this.expand = !this.expand
      if (this.expand && !this.commentArr.length) {
        this.commentListData()
      }
    },

    reply(data) {
      this.parentId = data.id
      this.updateReplayVisible(true)
      this.replayTitle = `回复@${data.name}`
    },
    updateReplayVisible(val) {
      this.replayDialogVisible = val
    },
    replyData(data) {
      this.save(data)
      this.$refs.replyDialog.closeAction()
    },

    loadData(data) {
      data.createBy = this.formLabelAlign.createBy
      queryReviewGoals(data).then((result) => {
        if (result.data && result.data.length > 0) {
          this.viewData = result.data
          this.pageData = result.page
          this.isShow = true
          this.dialogPlanVisible = false
        } else {
          this.$message({
            type: 'error',
            message: '暂无相关回顾可查看',
          })
        }
      })
    },
    getUnitTotal() {
      this.formLabelAlign.personalGoalsUnitList.forEach((item) => {
        this.unitData.informationAmount =
          this.unitData.informationAmount + item.informationAmount
        this.unitData.teachingMaterialAmount =
          this.unitData.teachingMaterialAmount + item.teachingMaterialAmount
        this.unitData.totalContract =
          this.unitData.totalContract + item.totalContract
        this.unitData.totalVisit = this.unitData.totalVisit + item.totalVisit
        console.log('this.unitData', this.unitData)
      })
    },
    loadInfo() {
      jiHuaInfo({
        id: this.$route.query.id,
        type: 1,
      })
        .then((result) => {
          this.formLabelAlign = result.data
          this.titleStr = this.generateTitleStr()
          this.getUnitTotal()
          if (this.formLabelAlign.personalGoalsUnitList.length > 0) {
            this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList)
          } else {
            this.unitGoalList = this.defunitList
          }
        })
        .catch((err) => {})
    },

    generateTitleStr() {
      const createByName = this.$route.query.createByName || ''
      const planTime = getPlanTime(this.formLabelAlign, this.form.goalsType)
      const planType = this.types[this.form.goalsType].name
      const timeRange = this.$route.query.timeRange || ''

      let mainTitle = `${createByName}${planTime}${planType}`
      let subTitle = timeRange

      return {
        mainTitle,
        subTitle,
      }
    },

    updateVisible(val) {
      this.dialogVisible = val
    },
    submitAction(fileData) {
      if (fileData) {
        this.statusPar.url = fileData.url
      }
      console.log('console.log', this.statusPar)
      check(this.statusPar)
        .then((result) => {
          if (result.data) {
            this.updateVisible(false)
            this.$message({
              type: 'success',
              message: result.msg,
            })
            this.loadAuditList()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    // 计划审核
    changeLogsStatus(data) {
      this.statusPar.logsStatus = data
      this.dialogVisible = true
    },
    // 审核记录
    loadAuditList() {
      toCheckList({ id: this.$route.query.id })
        .then((result) => {
          this.setupData = result.data[0]
          let checkList = result.data.slice(1, result.data.length)
          this.setupData.examineVoList = checkList
          for (
            let index = 0;
            index < this.setupData.examineVoList.length;
            index++
          ) {
            const element = this.setupData.examineVoList[index]
            if (element.isStatus) {
              this.setupData.isUserExamine = true
            }
            if (element.logsStatus === 3) {
              this.setupData.activeIndex = element.logsSort + 1;
              break;
            }else if (element.logsStatus === 1) {
              this.setupData.activeIndex = element.logsSort + 1
              this.isHas = element.url.length > 0 ? true : false
              this.statusPar.url = element.url
              break
            }
          }
          if (!this.setupData.activeIndex) {
            console.log('sssasasa')
            this.setupData.activeIndex = this.setupData.examineVoList.length + 1
          }
        })
        .catch((err) => {})
    },
    endPickerTime(time) {
      if (this.reviewType == 'planreview') {
        return false
      }
      const today = new Date()
      return time > today
    },
    changeDatePicker(date) {
      var newDate = new Date(date)
      this.form.year = newDate.getFullYear()
      if (this.form.goalsType == 2) {
        this.form.month = newDate.getMonth() + 1
      } else if (this.form.goalsType == 3) {
        var time = getWeek(newDate)
        var times = time.split('-')
        this.form.year = times[0]
        this.form.week = times[1]
      }
    },
    handlePlanClose() {
      this.dialogPlanVisible = false
      this.planTime = ''
    },
    nextAction() {
      if (this.planTime) {
        if (this.reviewType == 'summaryreview') {
          var fdata = Object.assign({}, this.form)
          fdata.goalsType = +fdata.goalsType + 3
          this.loadData(fdata)
        } else {
          this.loadData(this.form)
        }
      } else {
        this.$message({
          type: 'info',
          message: `请选择${this.types[this.form.goalsType].label}`,
        })
      }
    },
    openreview() {
      this.isShow = true
      this.dialogPlanVisible = false
    },
    closereview() {
      this.isShow = false
      this.reviewType = ''
    },
    getUnitGoalList(array) {
      this.unitGoalList = []
      this.unitIds = []
      var keys = [
        'informationAmount',
        'teachingMaterialAmount',
        'totalContract',
        'totalVisit',
      ]
      array.forEach((element, idx) => {
        var list = []
        this.unitIds.push(element.unitId)
        keys.forEach((key) => {
          var item = {
            id: element.id,
            index: idx,
            unitId: element.unitId,
            unitName: element.unitName,
            year: element.year,
            goalNum: element[key],
            unitgoalType: key,
            unitgoalTypeName: this.keyNames[key],
          }
          list.push(item)
        })
        this.unitGoalList.push(list)
      })

      console.log('sssssa00000', this.unitGoalList)
    },
    showView(e) {
      this.planTime = ''
      this.dialogPlanVisible = true
      this.reviewType = e.target.id
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (columnIndex === 0 && rowIndex === 0) {
          return {
            rowspan: 4,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
  },
}
</script>
  <style lang="scss" scoped>
.inputcommont {
  width: calc(100% - 88px);
  margin-right: 16px;
  margin-bottom: 10px;
}
.inputcommont /deep/ .el-input__inner {
  height: 34px !important;
  line-height: 34px !important;
}
.savebutton {
  width: 72px;
  padding: 9px 5px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
}
.buttoncursor {
  cursor: pointer;
  display: inline-block;
}

.expand {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
}
.savediv {
  margin-top: 20px;
}
.flex {
  display: flex;
  align-items: center;
}
.carditem {
  width: 100%;
  height: 190px;
  background: #ffffff;
  box-shadow: 0px 2px 16px 0px rgba(15, 27, 50, 0.08);
  border-radius: 8px 8px 8px 8px;
  padding: 16px 20px;
  margin-bottom: 10px;
}
.pmbcss {
  width: 100%;
  height: 21px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  line-height: 19px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 20px;
}
.pconcss {
  margin-top: 36px;
  width: 100%;
  height: 28px;
  font-family: Roboto, Roboto;
  font-weight: bold;
  font-size: 24px;
  color: #333333;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.picon {
  min-width: 40px;
  height: 40px;
}
.ptitle {
  padding-left: 10px;
  max-height: 40px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 1em;
  color: #999999;
  font-style: normal;
  text-transform: none;
}
.plantitle {
  height: 30px;
  width: 100%;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
  line-height: 23px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.pname {
  text-align: center;
}
.noimg {
  width: 48px;
  height: 48px;
  line-height: 48px;
  border-radius: 8px;
  background-color: #4285f4;
  text-align: center;
  color: #fff;
  font-size: 12px;
}
.uflex {
  display: flex;
  flex-wrap: wrap;
}
.pli {
  margin-right: 24px;
  margin-bottom: 24px;
  position: relative;
}
.pimg {
  width: 48px;
  height: 48px;
  border-radius: 8px 8px 8px 8px;
}
.pflex {
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
}
.bottomdiv {
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  height: 100px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.16);
  border-radius: 12px 12px 0px 0px;
  z-index: 2;
}
.clasbtn {
  margin-right: 20px;
  padding: 10px;
  width: 100px;
}
.mystep /deep/.el-step__title {
  font-size: 12px !important;
  padding-left: 4px;
  line-height: 20px !important;
}
.headdiv {
  margin-top: 10px;
  display: flex;
}
.tablebox {
  height: 337px;
  overflow-x: hidden;
  padding-right: 10px;
  margin: 16px 0;
}
.myform /deep/.el-form-item {
  margin-bottom: 0px !important;
}
.myform {
  margin: 16px 0;
}
.mr {
  margin-left: 20px;
}
.upload-demo {
  margin-top: 20px;
}
.pd20 {
  padding: 20px 0;
}
.tright {
  text-align: center;
}

.maindiv {
  display: flex;
  padding-top: 20px;
}
.pb100 {
  padding-bottom: 100px;
}
.rightdiv {
  width: 20%;
  margin-left: 20px;
  border-radius: 10px;
}
.w40 {
  width: 50%;
  background-color: white;
}
.submitmask {
  width: 100%;
  text-align: center;
  margin-top: 20px;
}
.textAlign /deep/.el-input__inner {
  text-align: center !important;
}
.flexcss {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.textAlignRight {
  text-align: right;
}
.width200 {
  width: 200px;
  margin-bottom: 16px;
}
.definput /deep/.el-input-group__append {
  background-color: #f6f7fb !important;
  color: #333333 !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.rtextcss {
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  color: #999999;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.owntable /deep/ td,
th {
  padding: 8px 0 !important;
}
.owntable /deep/ th {
  background-color: #f6f7fb !important;
}
.owntable /deep/ .cell {
  font-size: 14px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400 !important;
  color: #333333 !important;
}
.mubiao {
  background: #fff;
  margin-top: 10px;
  padding: 24px 20px;
  min-height: 500px;
  box-sizing: border-box;
}
.inputcss {
  height: 36px !important;
}
/deep/ .el-input__inner {
  line-height: 36px !important;
  height: 36px;
}
.deleteimg {
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.tablebox {
  height: 337px;
  overflow-x: hidden;
  padding-right: 10px;
}
.asbu {
  position: absolute;
  right: 0;
  top: 0;
}
.rela {
  position: relative;
  margin-bottom: 24px;
}
/deep/.el-input-group__append {
  padding: 0 12px !important;
}
.mt {
  margin-top: 20px;
}
.elform {
  width: 100%;
}
.elform .el-form-item {
  margin-bottom: 20px !important;
}
.bbtn {
  height: 48px;
  line-height: 48px;
  background-color: #dfeafd;
  font-size: 16px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  letter-spacing: 1px;
  border-radius: 4px;
  cursor: pointer;
}
.bbtn:hover {
  background-color: #4285f4;
  color: white !important;
}
.bbtn + .bbtn {
  margin-top: 10px;
}
.owntop {
  width: 80%;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
}
.w60 {
  width: 50%;
}
.personalplan {
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  box-sizing: border-box;
  margin-top: 48px;
}
.unitplan {
  height: 290px;
  overflow: auto;
  flex: 1;
  background: #fff;
  box-sizing: border-box;
  margin-top: 20px;
}

.progress /deep/.el-progress-bar__outer {
  height: 4px !important;
  background-color: #dfeafd;
}
.progress /deep/.el-progress-bar__inner {
  background-color: #5a98ff;
}
.progress /deep/.el-progress-bar {
  margin-right: 0px;
}
</style>
