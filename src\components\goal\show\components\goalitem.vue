<template>
    <div class="rankcss">
        <!-- achieveGoalVoList -->
        <div class="flex" >
            <img class="img24" v-if="itemData.ranking<4" :src="srtImgs[itemData.ranking]" alt="">
            <span class="text24" v-else>{{itemData.ranking}}</span>

            <headuser class="rankhead" :url="itemData.userLogo" width="48" :username="itemData.userName"></headuser>
            <!-- <img class="rankhead" :src="itemData.userLogo ? itemData.userLogo :touxiang" alt=""> -->
            <div class="progressdiv">
                <div class="deffont namecss">{{itemData.userName}}_{{itemData.departmentName}} <span class="goaltextcss ml16" :class="{'successColor':status == 1,'failColor':status == 2}">{{statusText}}</span></div>
                <el-progress class="progress" :percentage="loadData()" :format="()=>''"></el-progress>
            </div>
            <span class="goaltextcss mr12">目标{{type[itemData.goalType]}}{{itemData.goal}}{{this.unitName[itemData.goalType]}}</span>
            <span class="goaltextcss">已完成{{type[itemData.goalType]}}{{itemData.finishGoal}}{{this.unitName[itemData.goalType]}}</span>
        </div>
    </div>
</template>

<script>
import touxiang from "../../../../assets/touxiang.png";
import headuser from '../../../common/headuser.vue';
export default {
    components:{
        headuser
    },
    props:{
        itemData:{
            type:Object,
            default:()=>({}),
        },
        time:{
            type:String,
            default:''
        },
        isEnd:{
            type:Boolean,
            default:false
        },
        sort:{
            type:Number,
            default:null
        },
        unitText:{
            type:String,
            default:'个'
        }
    },
    data(){
        return{
            touxiang:touxiang,
            statusText:'',
            status:0,// 0:默认/进行中未完成  ；1:完成 ；2:超时未完成
            type:{
                1:'新增客户',
                2:'新增拜访与跟进',
                3:'新增机会',
                4:'新增合同',
                5:'合同金额',
                6:'回款金额',
                7:'发放次数'
            },
            unitName:{
                1:'个',
                2:'次',
                7:'次',
                4:'个',
                5:'万元',
                6:'万元',
                7:'次'
            },
            srtImgs:{
                1:require('../../../../assets/img/rank_1.png'),
                2:require('../../../../assets/img/rank_2.png'),
                3:require('../../../../assets/img/rank_3.png'),
            },
        }
    },
    created(){
    },
    methods:{
        loadData(){
            this.getStatus();
            if (Object.keys(this.itemData).length<=0) {
                return 0
            }
            if (this.itemData.goal == 0 || this.itemData.finishGoal == 0) {
                return 0;
            }else if(this.itemData.finishGoal/this.itemData.goal>1){
                return 100;
            } else {
                return this.itemData.finishRate *100;
            }
        },
        clickItem(){
            this.$router.push({
                path:'/goal/show/detail',
                query:{
                    id:123
                }
            })
        },
        getStatus(){
            if(this.itemData.goal == 0){
                this.statusText = '暂无目标'
                this.status = 0;
            }else if(this.itemData.goal <= this.itemData.finishGoal){
                this.statusText = `恭喜！已经完成${this.time}${this.type[this.itemData.goalType]}目标`
                this.status = 1;
            }else{
                if (this.isEnd) {
                    this.statusText =` 未完成${this.time}${this.type[this.itemData.goalType]}目标`
                    this.status = 2;
                }else{
                    var value = this.itemData.goal - this.itemData.finishGoal;
                    this.statusText =`距离目标达成还差${value}${this.unitName[this.itemData.goalType]}`
                    this.status = 0;
                }
            }
        },
    }
}
</script>

<style scoped>
.successColor{
    color: #56C36E !important;
}
.failColor{
    color: #F45961 !important;
}
.ml16{
    margin-left: 16px;
}
.mr12{
    margin-right: 12px;
}
.goaltextcss{

    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #999999;

}
.progress /deep/.el-progress-bar__outer{
    height: 4px !important;
    background-color: #DFEAFD;
}
.progress /deep/.el-progress-bar__inner{
    background-color: #5A98FF;
}
.progress /deep/.el-progress-bar{
    margin-right: 0px;
}
.progress{

}
.progressdiv{
    padding-top: 1em;
    width: 50vw;
    min-width: 500px;
    height: 72px;
    /* background-color: red; */
    position: relative;
    /* background-color: red; */
}
.namecss{

font-size: 14px;
font-family: Microsoft YaHei-Regular, Microsoft YaHei;
font-weight: 400;
color: #333333;
padding-bottom: 1em;

}
.tcss{
    margin-top: -3px;
    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #999999;


}
.text24{
    width: 24px;
    padding-top: 3px;
    text-align: center;
    font-size: 16px;
    font-family: Roboto-Medium, Roboto;
    font-weight: 500;
    color: #666666;
    margin-right: 12px;

}
.flex{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.img24{
  width: 24px;
  height: 24px;
  margin-right: 12px;
}
.rankhead{
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-left: 8px;
  margin-right: 12px;
}
.rankcss{
  height: 72px;
  margin: 0px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

</style>
