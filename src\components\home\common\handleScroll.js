export const myMinxin = {
    data() {
        return {
          loading: false,
          noMore: false,
          isScrollLoading: false
        }
      },
      methods: {
        // 滚动事件处理
        handleScroll() {
            if (this.loading || this.noMore || this.isScrollLoading) return
    
            const container = this.$refs.scrollContainer
            const scrollTop = container.scrollTop
            const scrollHeight = container.scrollHeight
            const clientHeight = container.clientHeight
    
            // 当滚动到距离底部50px时触发加载更多
            if (scrollTop + clientHeight >= scrollHeight - 50) {
            this.loadMore()
            }
        },
    
        // 加载更多数据
        loadMore() {
            if (this.loading || this.noMore || this.isScrollLoading) return

            this.loading = true
            this.isScrollLoading = true
            this.pageBean.pageNum++
            this.getList(true)
        },

        // 初始加载数据
        loadData() {
            if (this.loading) return

            this.loading = true
            this.getList(false)
        },
      }
}