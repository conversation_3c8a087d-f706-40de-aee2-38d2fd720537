<!-- 公共弹框 -->
<template>
  <section class="dialog-wrap" v-if="showMask" @click.self="showMask = false">
    <div id="dialogMove" class="dialog-container" :style="defaultSty" >
        <div class="dialog-title" @mousedown="handleMove">{{title}}<i class="el-icon-close dialog-right" @click="showMask = false"></i></div>
        <slot></slot>
        <slot name="footer"></slot>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    isshow: {
      type: Boolean
    },
    title: {
      type: String,
      default: '提示'
    },
    width: {
      type: String
    },
    height: {
      type: String
    }
  },
  computed: {
    defaultSty () {
      return {
        width: this.width,
        height: this.height
      }
    }
  },
  watch: {
    isshow (val) {
      this.showMask = val
    },
    showMask (val) {
      this.$emit('update:isshow', val)
    }
  },
  data () {
    return {
      showMask: false,
      isMosedown: true
    }
  },
  methods: {
    // 移动
    handleMove (ev) {
      let dialogMove = document.getElementById('dialogMove');
      this.isMosedown = true
      let oEvent = ev;
      oEvent.preventDefault();
      let disX = oEvent.clientX - dialogMove.offsetLeft;
      let disY = oEvent.clientY - dialogMove.offsetTop;
      document.onmousemove = function (ev) {
        let e = ev || event;
        e.preventDefault();
        dialogMove.style.left = e.clientX - disX + 'px';
        dialogMove.style.top = e.clientY - disY + 'px';
      }
      document.onmouseup = function () {
        document.onmousemove = null;
        document.onmouseup = null;
      }
      document.onmouseleave = function () {
        document.onmousemove = null;
        document.onmouseup = null;
      }
    }
  },
  updated () {
    // let dialogMove = document.getElementById('dialogMove');
    // dialogMove.style.left = '50%';
    // dialogMove.style.top = '30%'
  },
  created () {
    this.showMask = this.isshow;
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 6;
  // display: flex;
  // align-items: center;
  // justify-content: center;
  .dialog-container {
    position: absolute;
    left: 50%;
    top: 10%;
    transform: translateX(-50%);
    overflow: hidden;
    min-height: 100px;
    background: #ffffff;
    // position: absolute;
    // top: 50%;
    // left: 50%;
    border-radius: 5px;
    animation: action_translateY 0.5s forwards ease-out;
    @keyframes action_translateY {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    .dialog-title {
      width: 100%;
      height: 50px;
      font-size: 16px;
      color: #303133;
      border-bottom: 1px solid #E4E7ED;
      padding: 0 0 0 30px;
      line-height: 50px;
      box-sizing: border-box;
      cursor: move;
      // background: $digTitleMain;
      .dialog-right:hover{
        animation: rotate1 0.5s linear;
      }
      @keyframes rotate1
      {
        from {
        transform:rotate(0deg);
        -ms-transform:rotate(0deg);
        -moz-transform:roate(0deg);
        -webkit-transform:rotate(0deg);
        -o-transform:rotate(0deg);
        }
        to {
        transform:rotate(180deg);
        -ms-transform:rotate(180deg);
        -moz-transform:rotate(180deg);
        -webkit-transform:rotate(180deg);
        -o-transform:rotate(180deg);
        }
      }
      .dialog-right{
        float: right;
        line-height: 50px;
        margin-right: 17px;
        font-size: 20px;
        cursor: pointer;
      }
    }
    .footer {
      text-align: center;
      width: 100%;
      height: 60px;
      // line-height: 60px;
      position: absolute;
      bottom: 0;
      left: 0;
      padding: 0 16px;
      box-sizing: border-box;
    }
    .el-button--small {
      width: 98px;
      height: 32px;
    }
  }
}
</style>
