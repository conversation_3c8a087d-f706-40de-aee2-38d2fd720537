<template>
  <div>
    <div>
      <back>合同详情</back>
    </div>
    <div class="card">
      <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
        :stretch="isShow"
        class="tabscss"
        :class="{ width200: !isShow }"
      >
        <el-tab-pane label="基本信息" name="first">
          <bacisInfo ref="coninfo"></bacisInfo>
        </el-tab-pane>
        <el-tab-pane v-if="isShow" label="发货单" name="second">
          <invoiceRecord ref="contractinvoice"></invoiceRecord>
        </el-tab-pane>
        <el-tab-pane v-if="isShow" label="退款记录" name="third">
          <refundRecord ref="contractrefund"></refundRecord>
        </el-tab-pane>
        <el-tab-pane v-if="isShow" label="开票记录" name="fourth">
          <billingRecord ref="contractinvoicing"></billingRecord>
        </el-tab-pane>
        <el-tab-pane v-if="isShow" label="回款记录" name="five">
          <paymentRecord ref="contractreturn"></paymentRecord>
        </el-tab-pane>
        <el-tab-pane label="电子合同" name="six">
          <eContract ref="e-contract"></eContract>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import bacisInfo from './basicInfo.vue'
import invoiceRecord from './invoiceRecord.vue'
import refundRecord from './refundRecord.vue'
import paymentRecord from './paymentRecord.vue'
import billingRecord from './billingRecord.vue'
import eContract from './eContract.vue'
import { mapGetters } from 'vuex'
export default {
  computed: {
    ...mapGetters(['isShow']),
  },
  components: {
    back,
    bacisInfo,
    invoiceRecord,
    refundRecord,
    paymentRecord,
    billingRecord,
    eContract,
  },
  data() {
    return {
      activeName: this.$route.query.activeName
        ? this.$route.query.activeName
        : 'first',
      unitId: this.$route.query.id,
      refData: {
        first: 'coninfo',
        second: 'contractinvoice',
        third: 'contractrefund',
        fourth: 'contractinvoicing',
        five: 'contractreturn',
        six: 'e-contract',
      },
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleClick(tab, event) {
      var tabName = tab.name
      var refName = this.refData[tabName]
      if (refName != 'e-contract') {
        this.$refs[refName].loadData()
      }
    },
    onSubmit() {},
    handleRemove(file, fileList) {},
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
  },
}
</script>
<style>
.dianbtn {
  position: absolute;
  right: 16px;
  top: 10px;
  width: 24px;
  height: 24px;
  color: #666666;
  letter-spacing: 3px;
  font-size: 18px;
  font-weight: bold;
  line-height: 24px;
  cursor: pointer;
}
.tabscss .el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 56px;
  line-height: 56px;
}
.width200 .el-tabs__item {
  width: 15vw;
  text-align: center;
}
.tabscss .el-tabs__header {
  margin-bottom: 30px;
}
.cardcss {
  margin-bottom: 10px;
}
.el-col-5 {
  width: 20%;
}
.labeltext label {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
</style>

<style lang="scss" scoped>
.color-0 {
  color: #56c36e;
}
.color-1 {
  color: #4a8bf6;
}
.color-2 {
  color: #e85d5d;
}
.color-3 {
  color: #ec9037;
}
.color-4 {
  color: #f46d40;
}
.titletext {
  margin-bottom: 30px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.numtext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.numcolor {
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
}
.imgf {
  margin-left: 30px;
  margin-right: 20px;
}
.piececss {
  position: relative;
  height: 148px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #d6d6d6;
  width: 100%;
  padding: 50px 0;
  box-sizing: border-box;
}
.savecss {
  display: block;
  margin: 0 auto;
}
.mt20 {
  margin-top: 20px;
  width: 100%;
}
.card {
  background: #fff;
  min-height: calc(100vh - 135px);
  padding: 20px;
  margin-top: 20px;
}
</style>