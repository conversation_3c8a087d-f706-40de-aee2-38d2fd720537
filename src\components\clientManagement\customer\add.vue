<template>
  <div class="flex">
    <div>
      <back>{{ form.id ? "编辑客户" : "新建客户" }}</back>
    </div>
    <div class="mainbg">
      <el-form
        ref="addform"
        class="addfcss"
        :rules="rules"
        :model="form"
        label-width="118px"
      >
        <textBorder>基础信息</textBorder>
        <div class="pt20  bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="8">
              <el-form-item label="客户名称：" prop="customerName">
                <el-input
                  class="definput"
                  placeholder="请输入客户名称"
                  v-model="form.customerName"
                  maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item label="职务：" prop="duties">
                <el-input
                  class="definput"
                  placeholder="请输入职务"
                  v-model="form.duties"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="客户单位：" prop="unitId">
                <div class="unitbtn" @click="chooseunit">
                  <span v-if="form.unitId" class="deffont">{{
                    form.unitName
                  }}</span>
                  <span v-else>请选择</span>
                  <i class=" rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
              <el-form-item label="客户级别：" prop="customerLevel">
                <el-select
                  class="definput"
                  popper-class="removescrollbar"
                  v-model="form.customerLevel"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in levels"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="部门：" prop="unitDepartment">
                <el-input
                  class="definput"
                  v-model="form.unitDepartment"
                  placeholder="请输入客户部门"
                ></el-input>
              </el-form-item>
              <el-form-item label="负责专业：" prop="specialtys">
                <el-select
                  class="definput"
                  multiple
                  popper-class="removescrollbar"
                  v-model="form.specialtys"
                  @change="changeMajor"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in majorList"
                    :key="item.id"
                    :label="item.specialtyName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="负责课程：" prop="responsibleCourse">
                <el-input
                  class="definput"
                  v-model="form.responsibleCourse"
                  placeholder="请输入负责的课程名称"
                ></el-input>
              </el-form-item>
              <el-form-item width="800" class="mb10" label="客户照片：" prop="imageUrl">
                <upload
                  ref="photoRef"
                  :limit="1"
                  :multiple="false"
                  accept=".jpg,.jpeg,.png,.JPG,.JPEG,.PNG"
                  :fileList="photoFileList"
                  @submitImg="submitPhotoImg"
                  :data="photoFileData">
                </upload>
                <div class="tip">支持.jpg/.png/.jpeg格式;只可上传1张</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">联系信息</textBorder>
        <div class="pt20  bbline">
          <el-row :gutter="0" type="flex" justify="start" prop="phone">
            <el-col :span="8">
              <el-form-item label="联系电话：" prop="phone">
                <el-input
                  class="definput"
                  placeholder="请输入联系电话"
                  v-model="form.phone"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="微信：" prop="wechat">
                <el-input
                  class="definput"
                  placeholder="请输入微信"
                  v-model="form.wechat"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="邮箱：" prop="mailbox">
                <el-input
                  class="definput"
                  type="email"
                  placeholder="请输入邮箱"
                  v-model.trim="form.mailbox"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">客户标签</textBorder>
        <div class="pt20  bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="24" class="tagcol">
              <el-form-item label="标签：" prop="label">
                <div @click="clickInput">
                  <el-tag
                    :key="index"
                    v-for="(tag, index) in dynamicTags"
                    closable
                    class="tagcss"
                    size="small"
                    :disable-transitions="false"
                    @close="handleClose(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                  <el-input
                    class="input-new-tag"
                    placeholder="按回车键Enter创建标签"
                    v-model="inputValue"
                    v-if="dynamicTags && dynamicTags.length != 5"
                    ref="saveTagInput"
                    size="small"
                    :maxlength="10"
                    @keyup.enter.native="handleInputConfirm"
                    @keyup.delete.native="deleteTag"
                    @blur="handleInputConfirm"
                  >
                  </el-input>
                  <span
                    class="right pr10"
                    v-if="dynamicTags && dynamicTags.length != 5"
                  >
                    还能添加{{ 5 - dynamicTags.length }}个标签
                  </span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30" v-if="form.id">负责与协作</textBorder>
        <div class="pt20  bbline" v-if="form.id">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="24">
              <el-form-item label="负责人：" prop="collaborator">
                <el-tag
                  class="tagcss"
                  size="small"
                  v-for="item in form.chargePersonNames"
                  :key="item.id"
                  >{{ item }}</el-tag
                >
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">数据画像</textBorder>
        <div class="pt20 ">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="8">
              <el-form-item label="性别：" prop="sex">
                <el-select
                  class="definput"
                  popper-class="removescrollbar"
                  v-model="form.sex"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in genders"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="年龄：" prop="age">
                <el-input
                  class="definput"
                  type="number"
                  placeholder="请输入年龄"
                  v-model="form.age"
                ></el-input>
              </el-form-item>
              <el-form-item label="出生年月日：" prop="birthday">
                <el-date-picker
                  class="definput"
                  v-model="form.birthday"
                  type="date"
                  placeholder="请选择出生日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证号：" prop="cardNumber">
                <el-input
                  class="definput"
                  placeholder="请输入身份证信息"
                  v-model="form.cardNumber"
                ></el-input>
              </el-form-item>
              <el-form-item label="籍贯：" prop="nativePlace">
                <el-input
                  class="definput"
                  placeholder="请输入籍贯信息"
                  v-model="form.nativePlace"
                ></el-input>
              </el-form-item>
              <el-form-item label="民族：" prop="native">
                <el-select
                  class="definput"
                  popper-class="removescrollbar"
                  v-model="form.nation"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in nationList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="职称：" prop="professionalTitles">
                <el-input
                  class="definput"
                  placeholder="请输入职称"
                  v-model="form.professionalTitles"
                ></el-input>
              </el-form-item>
              <el-form-item label="职称时间：" prop="professionalTime">
                <el-input
                  class="definput"
                  placeholder="请输入职称时间"
                  v-model="form.professionalTime"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="24">
              <el-form-item label="工作地址：" prop="address">
                <el-input
                  class="definput addresscss"
                  :autosize="{ minRows: 1, maxRows: 3 }"
                  v-model="form.address"
                  type="textarea"
                  placeholder="请输入工作地址"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="24">
              <el-form-item label="家庭地址：" prop="homeAddress">
                <el-input
                  class="definput addresscss"
                  :autosize="{ minRows: 1, maxRows: 3 }"
                  v-model="form.homeAddress"
                  type="textarea"
                  placeholder="请输入家庭地址"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="24">
              <el-form-item label="爱好情况：" prop="hobby">
                <el-input
                  class="definput addresscss"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 3 }"
                  placeholder="请输入爱好情况"
                  v-model="form.hobby"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="24">
              <el-form-item label="论文课题：" prop="thesisTopic">
                <el-input
                  class="definput addresscss"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 3 }"
                  placeholder="请输入论文课题"
                  v-model="form.thesisTopic"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="24">
              <el-form-item label="研究方向：" prop="researchDirection">
                <el-input
                  class="definput addresscss"
                  type="textarea"
                  maxlength="500"
                  show-word-limit
                  :autosize="{ minRows: 1, maxRows: 3 }"
                  placeholder="请输入研究方向"
                  v-model="form.researchDirection"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="车辆信息：" prop="carInfo">
                <el-input
                  class="definput"
                  placeholder="请输入车辆信息"
                  v-model="form.carInfo"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车牌号：" prop="carNumber">
                <el-input
                  class="definput"
                  placeholder="请输入车牌号"
                  v-model="form.carNumber"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="在意的人：" prop="importPeople">
                <el-input
                  class="definput"
                  placeholder="请输入在意的人(按回车键Enter进行创建)"
                  v-model="inputValue2"
                  @keyup.enter.native="handleInput1"
                ></el-input>
              </el-form-item>
              <el-form-item
                label=""
                prop="hobby"
                v-for="(item, index) in inputTags1"
                :key="index"
              >
                <el-tag class="width100">
                  {{ item }}
                </el-tag>
                <i
                  class="el-icon-delete frcenter"
                  @click="deleteItem(index)"
                ></i>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="婚恋情况：" prop="marriage">
                <el-input
                  class="definput"
                  type="textarea"
                  maxlength="300"
                  show-word-limit
                  rows="4"
                  placeholder="请输入婚恋情况"
                  v-model="form.marriage"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="子女情况：" prop="children">
                <el-input
                  class="definput"
                  type="textarea"
                  rows="4"
                  maxlength="300"
                  show-word-limit
                  placeholder="请输入子女情况"
                  v-model="form.children"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="备注：" prop="notes">
                <el-input
                  class="definput addresscss"
                  type="textarea"
                  rows="4"
                  maxlength="1000"
                  show-word-limit
                  placeholder="请输入备注"
                  v-model="form.notes"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item width="800" class="mb10 m20" label="附件：">
            <!-- <upload ref="imgRef" accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PBG,.GIF,.mp4,.pdf"
                :fileList="fileList" @submitImg="submitImg" :data="fielData"></upload> -->
                <upload2 :fileMaxSize='100' ref="imgRef" @submitImg="submitImg" :data="fielData" accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PNG,.GIF,.pdf,.doc,.docx,.xlsx,.mp4"
                    :fileList="fileList">
                    <span class="studiocss">
                        <img src="../../../assets/img/file_icon.png">
                        <span class="uploadtext  deffont">点击上传附件</span>
                    </span>
                    <template slot="ptip">
                        <p>只能上传pdf,doc,docx,ppt,pptx,xlsx文件</p>
                    </template>
                </upload2>
            </el-form-item>
        </div>
        <div class="pt20 btncenter">
          <el-form-item>
            <el-button class="w98 btn_h42" type="primary" @click="submitForm"
              >保存</el-button
            >
          </el-form-item>
        </div>
      </el-form>
    </div>
    <unitDialog
      ref="unitdialog"
      :visible.sync="unitDialogVisible"
      className="CustomerController"
      @updateVisible="updateVisible"
      @updateUnit="updateUnit"
    ></unitDialog>
     <!-- 部门验证组件 -->
    <verifyDeparment
      ref="verifyDeparment"
      @submit="addData"
    ></verifyDeparment>
  </div>
</template>

<script>
import back from "../../common/back.vue";
import textBorder from "../../common/textBorder.vue";
import unitDialog from "../../common/unitDialog.vue";
import upload2 from '@/components/common/upload2.vue'
import upload from '@/components/common/upload.vue'
import {
  addCustomer,
  updateCustomer,
  customerInfo,
  selectUintSpecialty
} from "@/api/clientmanagement/customer";
import {
  getDict,
  ValidatePhone,
  validEmail,
} from "@/utils/tools";
import { timenow } from "@/utils/index";
import { getFileTypeNum, addSnop } from '@/utils/tools'
import VerifyDeparment from "@/components/common/verifyDeparment.vue";
export default {
  components: {
    back,
    textBorder,
    unitDialog,
    upload2,
    upload,
    VerifyDeparment
  },
  data() {
    return {
        fileList: [],
        fielData:{
          serviceName:"customerFile"
        },
        photoFileList: [],
        photoFileData: {
          serviceName: "customerPhoto"
        },
      isShowChargePerson: this.$route.query.id ? true : false,
      isSmall: window.screen.availWidth < 1500 ? true : false,
      multipleNum: 1,
      dialogName: "",
      inputValue: "",
      isDeleteTag: false,
      isLoading: false,
      dynamicTags: [],
      inputValue2: "",
      inputTags1: [],
      inputValue3: "",
      inputTags2: [],
      timeValue: "",
      courseImageList: [],
      rules: {
        customerName: [
          { required: true, message: "请输入客户名称", trigger: "blur" },
          { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" }
        ],
        label: [{ required: true, message: "请输入标签", trigger: "blur" }],
        duties: [{ required: true, message: "请输入职务", trigger: "blur" }],
        customerLevel: [
          { required: true, message: "请选择客户级别", trigger: "change" }
        ],
        unitDepartment: [
          { required: true, message: "请选择部门", trigger: "blur" }
        ],
        limits: [
          { required: true, message: "请选择客户展示权限", trigger: "change" }
        ],
        unitId: [
          { required: true, message: "请选择客户单位", trigger: "change" }
        ],

        sex:[
            { required: true, message: '请选择性别', trigger: 'change' }
        ],
        // age:[
        //     { required: true, message: '请输入年龄', trigger: 'blur' }
        // ],
        address:[
            { required: true, message: '请输入工作地址', trigger: 'blur' }
        ],
        phone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
        wechat: [{ required: true, message: "请输入微信", trigger: "blur" }]
      },
      form: {
        id: this.$route.query.id,
        customerType: 1,
        customerName: "",
        unitName: "",
        unitId: "",
        unitDepartment: "",
        duties: "",
        label: "",
        customerLevel: "",
        chargePerson: window.sessionStorage.getItem("userid"),
        chargePersonName: "",
        customerDepartmentId: "",
        phone: "",
        wechat: "",
        mailbox: "",
        fax: "",
        address: "",
        homeAddress: "",
        sex: "",
        age: "",
        nativePlace: "",
        birthday: "",
        marriage: "",
        children: "",
        hobby: "",
        notes: "",
        specialtys: [],
        imageUrl: "",
        fileInfoEntities:[]
      },
      genders: [
        {
          label: "男",
          value: 1
        },
        {
          label: "女",
          value: 2
        }
      ],
      levels: [],
      nationList: [],
      majorList: [],
      unitDialogVisible: false
    };
  },
  created() {
    getDict("CustomerLevel")
      .then(result => {
        this.levels = result;
      })
      .catch(err => {});
    getDict("Nation")
      .then(result => {
        this.nationList = result;
      })
      .catch(err => {});
    if (this.$route.query.id) {
      this.form.id = this.$route.query.id;
      this.loadInfo();
    }

    if (this.$route.query.unitId) {
      this.form.unitId = this.$route.query.unitId
      this.form.unitName = this.$route.query.unitName
      this.form.specialtys = [];
      this.loadMajor();
    }
    if (this.$route.query.structureId) {
      this.form.structureId = this.$route.query.structureId
    }

  },
  methods: {
    submitImg(fileList) {
        this.fileList = fileList
        console.log("文件资源==",this.fileList);
    },
    submitPhotoImg(fileList) {
        this.photoFileList = fileList;
        if (fileList && fileList.length > 0) {
            this.form.imageUrl = fileList[0].url;
        } else {
            this.form.imageUrl = "";
        }
        console.log("客户照片==", this.photoFileList);
    },
    loadInfo() {
      customerInfo(this.$route.query.id)
        .then(result => {
          this.form = result.data;
          if (this.form.unitId) {
            this.loadMajor();
          }

          this.form.sex = this.form.sex == 0 ? "" : this.form.sex;
          this.form.age = this.form.age == 0 ? "" : this.form.age;
          this.form.nation = this.form.nation == 0 ? "" : this.form.nation;
          this.dynamicTags = result.data.label.length>0?result.data.label.split(","):[],
          this.inputTags1 =
            result.data.importPeople && result.data.importPeople.length > 0
              ? result.data.importPeople.split(",")
              : [];

            this.fileList = result.data.fileInfoEntities
            this.fileList.forEach(item =>{
                item.name = item.fileName;
                item.status = 'success',
                item._url = item.fileType == 1 ? item.url : addSnop(item.url)
              }
            )
            console.log('2222222',this.fileList);
            this.$refs.imgRef.setFileList(this.fileList)

            if (result.data.imageUrl) {
                this.photoFileList = [{
                    url: result.data.imageUrl,
                    name: '客户照片',
                    fileName: '客户照片',
                    status: 'success',
                    _url: result.data.imageUrl
                }];
                this.$refs.photoRef.setFileList(this.photoFileList);
            }
        })
        .catch(err => {});
    },
    deleteItem(index) {
      this.inputTags1.splice(index, 1);
    },

    updateUnit(data) {
      this.form.unitName = data.unitName;
      this.form.unitId = data.id;
      this.$refs.addform.validateField("unitId");
      if (this.form.unitId) {
        this.form.specialtys = [];
        this.loadMajor();
      }
    },
    loadMajor() {
      selectUintSpecialty({
        unitId: this.form.unitId
      })
        .then(result => {
          this.majorList = result.data;
        })
        .catch(err => {});
    },
    chooseunit() {
      if (this.$route.query.unitId) {
        return
      }
      this.unitDialogVisible = true;
      this.$refs.unitdialog.loadData(this.form.unitId);
    },
    updateVisible(val) {
      this.unitDialogVisible = val;
    },
    handleInputConfirm() {
      if (this.inputValue.length > 0) {
        this.dynamicTags.push(this.inputValue);
        this.form.label =
          this.dynamicTags.length > 0 ? this.dynamicTags.join(",") : "";
        this.$refs.addform.validateField("label");
        this.inputValue = "";
      }
    },
    clickInput() {
      if (this.$refs.saveTagInput) {
        this.$refs.saveTagInput.focus();
      }
    },
    deleteTag() {
      // if (this.inputValue.length<=0) {
      //     // 删除tag
      //     if (this.isDeleteTag) {
      //         this.dynamicTags.pop();
      //         console.log(this.dynamicTags);
      //         this.form.label  = this.dynamicTags.length>0  ? this.dynamicTags.join(',') : ''
      //     }else{
      //         this.isDeleteTag = true;
      //     }
      // }else{
      //     this.isDeleteTag = false;
      // }
    },
    handleClose(tag) {
      var index = this.dynamicTags.indexOf(tag);
      this.dynamicTags.splice(index);
      this.form.label =
        this.dynamicTags.length > 0 ? this.dynamicTags.join(",") : "";
      this.$refs.addform.validateField("label");
    },
    arrForEach(arrFileList) {
      this.form.fileInfoEntities = []
        arrFileList.forEach(item => {
            this.form.fileInfoEntities.push({
                ...item,
                fileType: getFileTypeNum(item.url)
            })
        })
    },
    submitForm() {
      this.$refs["addform"].validate(valid => {
        if (!valid) {
          return false;
        }
        this.arrForEach(this.fileList)
        if (this.inputTags1.length > 0) {
          this.form.importPeople = this.inputTags1.join(",");
        }else{
          this.form.importPeople = ''
        }
        if (this.form.phone) {
          if (!ValidatePhone(this.form.phone)) {
            this.$message({
              type: "error",
              message: " 请输入正确的联系方式"
            });
            return;
          }
        }
        if (this.form.mailbox) {
          if (!validEmail(this.form.mailbox)) {
            this.$message({
              type: "error",
              message: " 请输入正确的邮箱"
            });
            return;
          }
        }
        if (this.courseImageList && this.courseImageList.length > 0) {
          this.courseImageList.forEach(item => {
            this.form.fileInfoEntities.push({
              ...item,
              fileType: getFileTypeNum(item.url)
            })
          })
        }
        if (this.form.id) {
          this.updateData();
        } else {
          // 验证部门
          this.$refs.verifyDeparment.verify()
        }

      });
    },
    addData(departmentId) {
      this.form.createDepartmentId = departmentId;
      addCustomer(this.form)
        .then(result => {
          if (result.data) {
            this.$message({
              type: "success",
              message: "添加成功！"
            });
            this.$router.back();
          } else {
            this.$message({
              type: "error",
              message: "保存失败！"
            });
          }
        })
        .catch(err => {});
    },
    updateData() {
      updateCustomer(this.form)
        .then(result => {
          if (result.data) {
            this.$message({
              type: "success",
              message: "更新成功！"
            });
            this.$router.back();
          } else {
            this.$message({
              type: "error",
              message: "保存失败！"
            });
          }
        })
        .catch(err => {});
    },

    handleInput1() {
      this.inputTags1.push(timenow() + " " + this.inputValue2);
      this.inputValue2 = "";
    },
    changeMajor() {
      if (this.form.specialtys.length > 0) {
      } else {
        this.form.specialtys = [];
      }
      this.$refs.addform.validateField("specialtys");
    },
    submitCourseImg(fileList) {
      this.courseImageList = fileList
      console.log("课程图片==", this.courseImageList);
    },
  }
};
</script>
<style scoped>
.m20{
  margin-top: 20px;
}
.addbtn {
  margin-left: 5px;
  font-size: 20px;
  position: absolute;
  font-size: 20px;
  right: 0px;
  top: 6px;
  color: #4285f4 !important;
  cursor: pointer;
}
.width45 {
  width: 45%;
}
.width45 + .width45 {
  margin-left: 10px;
}
.frcenter {
  position: absolute;
  font-size: 20px;
  right: 0px;
  top: 6px;
  color: #f44242 !important;
  cursor: pointer;
}
.width100 {
  width: calc(100% - 100px) !important;
}
.pr10 {
  padding-right: 10px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 34px;
}

.w98 {
  width: 98px;
}

.flex {
  display: flex;
  flex-direction: column;
}

.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}

.btncenter {
  text-align: center;
}

.quanxiancss /deep/.el-form-item__label {
  margin-left: -7px;
  width: 125px !important;
}

.addresscss {
  max-height: 68px;
}

.tagText {
  margin-right: 10px;
  color: #4285f4;
}

.input-new-tag {
  width: 180px;
  margin-left: -8px;
}

.input-new-tag /deep/.el-input__inner {
  border: none;
  background-color: rgba(0, 0, 0, 0);
}

.tagcss /deep/ .el-icon-close {
  width: 12px;
  height: 12px;
  line-height: 12px;
  background-color: #4285f4;
  color: white;
}
.tagcss {
  margin-left: 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  background: #dfeafd;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}

.tagcol /deep/.el-form-item__label {
  height: 34px;
  line-height: 34px;
}

.tagcol /deep/.el-form-item__content {
  background: #f5f5f5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  min-height: 34px;
  line-height: 34px;
}

.mainbg {
  margin: 16px 0px;
  padding: 20px 16px;
  background-color: white;
  border-radius: 8px;
}
.dbtn {
  color: #f44242;
}
.pd0 {
  /* padding-right: 0px !important; */
}
.tip {
  color: #999999;
}
</style>
