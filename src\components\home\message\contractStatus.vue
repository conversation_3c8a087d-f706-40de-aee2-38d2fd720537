<template>
  <div class="box" ref="scrollContainer" @scroll="handleScroll">
    <div class="boxItem" v-for="(item,index) in boxList" :key="index">
      <div class="title">{{ item.title }}</div>
      <div class="container">
        <el-row>
          <el-col :span="12">
            <div class="content">负责人：{{ item.chargePersonName }}</div>
            <div class="content">类型：{{ item.subName }}</div>
          </el-col>
          <el-col :span="12">
            <div class="content">单位：{{ item.unitName }}</div>
            <div class="content">客户：{{ item.customerName }}</div>
          </el-col>
        </el-row>
      </div>
      <div class="container">
        <div class="content">变更阶段：{{ item.content }}</div>
      </div>
      <div class="justBetween">
        <div class="time">提醒时间：{{ item.remindTime }}</div>
        <div class="btn" @click="goDetail(item)">
          <div class="btnText">详情</div>
          <img src="@/assets/right.png" alt="" class="btnImg">
        </div>
      </div>
    </div>
    <div v-if="loading" class="loading-text">加载中...</div>
    <div v-if="noMore && boxList.length > 0" class="no-more-text">没有更多数据了</div>
    <el-empty v-if="boxList.length == 0 && !loading" description="暂无合同状态变更消息"></el-empty>
  </div>
</template>

<script>
import {remindMessageList} from '@/api/index'
import {myMinxin} from '../common/handleScroll.js'
export default {
  mixins:[myMinxin],
  data() {
    return {
      pageBean:{
        pageNum: 1,
        pageSize: 10,
        type:8
      },
      boxList:[]
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    getList(isLoadMore = false){
      remindMessageList(this.pageBean).then(res=>{
        if (isLoadMore) {
          this.boxList = [...this.boxList, ...res.data]
        } else {
          this.boxList = res.data
        }

        this.noMore = !res.page.hasNextPage
      }).finally(() => {
        this.loading = false
        this.isScrollLoading = false
      })
    },
    goDetail(item){
      this.$router.push({
        path:'/projectManagement/contract/detail',
        query:{
          id:item.taskId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.boxItem{
  height: 199px;
  padding: 12px 20px;
  background: #F6F7FB;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 16px;
}
.boxItem:last-child{
  margin-bottom: 0;
}
.title{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #333333;
}
.container{
  border-bottom: 1px solid #E6E9F5;
  padding-bottom: 12px;
  margin-top: 12px;
  margin-bottom: 12px;
}
.content{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}
.content:last-child{
  margin-bottom: 0;
}
.justBetween{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.btn{
  display: flex;
  align-items: center;
  cursor: pointer;
}
.btnText{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #4285F4;
  margin-right: 2px;
}
.btnImg{
  width: 16px;
  height: 17px;
}
.time{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}
.box {
  max-height: 100%;
  overflow-y: auto;
}
.loading-text, .no-more-text {
  text-align: center;
  padding: 20px;
  font-size: 14px;
  color: #999;
}
</style>
