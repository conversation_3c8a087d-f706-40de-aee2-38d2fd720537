<!--更多资料-->
<template>
    <div>
        <!-- 详细资料 -->
        <h2><span>详细资料</span></h2>
        <div class="details-data">
            <div>
                <ul>
                    <li v-for="item in detailsData1" :key="item.id">
                        {{ item.label }}{{ item.value }}
                    </li>
                </ul>
            </div>
            <div>
                <ul>
                    <li v-for="item in detailsData2" :key="item.id">
                        {{ item.label }}{{ item.value }}
                    </li>
                </ul>
            </div>
            <div>
                <ul>
                    <li v-for="item in detailsData3" :key="item.id">
                        {{ item.label }}{{ item.value }}
                    </li>
                </ul>
            </div>
            <div>
                <ul>
                    <li v-for="item in detailsData4" :key="item.id">
                        {{ item.label }}{{ item.value }}
                    </li>
                </ul>
            </div>
        </div>
        <!-- 择偶意向 -->
        <h2><span>择偶意向</span></h2>
        <div class="mating-intention">
            <div>
                <ul>
                    <li v-for="item in intention1" :key="item.id">
                        {{ item.label }}{{ item.value }}
                    </li>
                </ul>
            </div>
            <div>
                <ul>
                    <li v-for="item in intention2" :key="item.id">
                        {{ item.label }}{{ item.value }}
                    </li>
                </ul>
            </div>
            <div>
                <ul>
                    <li v-for="item in intention3" :key="item.id">
                        {{ item.label }}{{ item.value }}
                    </li>
                </ul>
            </div>
            <div>
                <ul>
                    <li v-for="item in intention4" :key="item.id">
                        {{ item.label }}{{ item.value }}
                    </li>
                </ul>
            </div>
        </div>
        <!-- 择偶目的 -->
        <h2><span>择偶目的</span></h2>
        <div class="mating-purposes">
            <p>我希望Ta：善解人意，不粘人，周末一起爬爬山，谈谈天。</p>
        </div>
        <!-- 自我介绍 -->
        <h2><span>自我介绍</span></h2>
        <div class="self-introduction">
            <p>{{ this.secondData.selfIntroduction }}</p>
        </div>
    </div>
</template>

<script>
export default {
  props: [ 'second' ],
  data() {
    return {
      mateSelection: {},
      spouse: {},
      detailsData1: [
        { id: 1, label: '性别：', value: '' }, // this.secondData.data.genderDesc
        { id: 2, label: '性格：', value: '' }, // this.secondData.data.characterNames
        { id: 3, label: '专业：', value: '' }, // this.secondData.data.occupationName
        { id: 4, label: '爱好：', value: '' }, // this.secondData.data.hobbyInfo
        { id: 5, label: '厨艺：', value: '' } // this.secondData.data.cookingSkillDesc
      ],
      detailsData2: [
        { id: 1, label: '月收入：', value: '' }, // this.secondData.data.monthlyIncomeDesc
        { id: 2, label: '星座：', value: '' }, // this.secondData.data.constellationName
        { id: 3, label: '宗教信仰：', value: '' }, // this.secondData.data.beliefName
        { id: 4, label: '血型：', value: '' }, // this.secondData.data.bloodTypeName
        { id: 5, label: '健康状况：', value: '' } // this.secondData.data.healthDesc
      ],
      detailsData3: [
        { id: 1, label: '何时结婚：', value: '' }, // this.secondData.data.marryTimeDesc
        { id: 2, label: '何时要小孩：', value: '' }, // this.secondData.data.marryTimeDesc
        { id: 3, label: '是否喜欢宠物：', value: '' }, // this.secondData.data.lovePetsLevelDesc
        { id: 4, label: '购房情况：', value: '' }, // this.secondData.data.housePurchaseDesc
        { id: 5, label: '购车情况：', value: '' } // this.secondData.data.carPurchaseDesc
      ],
      detailsData4: [
        { id: 1, label: '是否吸烟：', value: '' }, // this.secondData.data.smokingDesc
        { id: 2, label: '是否喝酒：', value: '' }, // this.secondData.data.drinkingDesc
        { id: 3, label: '父母职业：', value: '' }, // this.secondData.data.fatherJobName
        { id: 4, label: '兄弟姐妹：', value: '' }, // this.secondData.data.familyStateName
        { id: 5, label: '家中排行：', value: '' }
      ],
      intention1: [],
      intention2: [],
      intention3: [],
      intention4: [],
      secondData: {}
    }
  },
  watch: {
    secondSelectValue(val) {
      this.second = val
      this.detailData()
      this.matingIntention()
    }
  },
  computed: {
    secondSelectValue() {
      return this.second
    }
  },
  methods: {
    // 详情数据
    async detailData () {
      let res = await this.$axios.get(`/sf/member/ocs/info/${this.second.id}`);
      if (res.status === 0) {
        this.secondData = res.data;
        this.detailsData1[0].value = this.secondData.genderDesc
        this.detailsData1[1].value = this.secondData.characterNames
        this.detailsData1[2].value = this.secondData.occupationName
        this.detailsData1[3].value = this.secondData.hobbyInfo
        this.detailsData1[4].value = this.secondData.cookingSkillDesc
        this.detailsData2[0].value = this.secondData.monthlyIncomeDesc
        this.detailsData2[1].value = this.secondData.constellationName
        this.detailsData2[2].value = this.secondData.beliefName
        this.detailsData2[3].value = this.secondData.bloodTypeName
        this.detailsData2[4].value = this.secondData.healthDesc
        this.detailsData3[0].value = this.secondData.marryTimeDesc
        this.detailsData3[1].value = this.secondData.childWantDesc
        this.detailsData3[2].value = this.secondData.lovePetsLevelDesc
        this.detailsData3[3].value = this.secondData.housePurchaseDesc
        this.detailsData3[4].value = this.secondData.carPurchaseDesc
        this.detailsData4[0].value = this.secondData.smokingDesc
        this.detailsData4[1].value = this.secondData.drinkingDesc
        this.detailsData4[2].value = `${this.secondData.fatherJobName},${this.secondData.motherJobName}`
        this.detailsData4[3].value = this.secondData.brotherSisterNames
        this.detailsData4[4].value = this.secondData.familyStateName
      } else {
        this.$message.error(res.msg)
      }
    },
    // 择偶意向
    async matingIntention() {
      this.mateSelection.userId = this.second.id
      this.mateSelection.type = "1"
      let res = await this.$axios.get('sf/member/ocs/info/makeFriend', { params: this.mateSelection })
      if (res.status === 0) {
        this.spouse = res.data;
        this.intention1 = [
          { id: 1, label: '性别：', value: this.spouse.genderDesc },
          { id: 2, label: '年龄：', value: this.spouse.ageRange },
          { id: 3, label: '身高：', value: this.spouse.heightRange },
          { id: 4, label: '学历：', value: this.spouse.educationDesc }
        ];
        this.intention2 = [
          { id: 1, label: '体型：', value: this.spouse.bodilyFormName },
          { id: 2, label: '婚姻状况：', value: this.spouse.maritalStatusDesc },
          { id: 3, label: '有无子女：', value: this.spouse.childSituationDesc },
          { id: 4, label: '月收入：', value: this.spouse.monthlyIncomeDesc }
        ]
        this.intention3 = [
          { id: 1, label: '居住地：', value: this.spouse.provinceName },
          { id: 2, label: '健康状况：', value: this.spouse.healthDesc },
          { id: 3, label: '血型：', value: this.spouse.bloodTypeName },
          { id: 4, label: '购房情况：', value: this.spouse.housePurchaseDesc }
        ]
        this.intention4 = [
          { id: 1, label: '购车情况：', value: this.spouse.carPurchaseDesc },
          { id: 2, label: '是否吸烟：', value: this.spouse.smokingDesc },
          { id: 3, label: '是否喝酒：', value: this.spouse.drinkingDesc }
        ]
      } else {
        this.$message.error(res.msg)
      }
    }
  },
  created() {
    this.detailData()
    this.matingIntention()
  }
}
</script>

<style lang="scss" scoped>
    h2 {
        padding-bottom: 10px;
        border-bottom: 1px solid #E6E6E6;
    }
    .details-data{
      font-size: 14px;
    }
    .details-data,.mating-intention {
        display: flex;
        div {
            flex: 1;
            ul li {
                list-style: none;
                line-height: 50px;
            }
        }
    }
    .mating-purposes,.self-introduction {
        p {
            margin: 15px 0 20px 0;
            color: #666;
        }
    }
</style>
