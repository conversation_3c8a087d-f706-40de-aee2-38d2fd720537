<template>
  <div class="flex">
    <div>
      <back>{{ titleName }}</back>
    </div>
    <div class="mainbg">
      <el-form
        :rules="rules"
        :model="form"
        ref="addform"
        class="addfcss"
        label-width="130px"
      >
        <textBorder>基础信息</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="项目名称：" prop="projectName">
                <el-input
                  clearable=""
                  v-model="form.projectName"
                  class="definput"
                  maxlength="100"
                  show-word-limit
                  placeholder="请输入项目名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="客户：" prop="customerId">
                <div
                  class="unitbtn"
                  :class="{ selectedCss: form.customerName }"
                  @click="chooseCustomer"
                >
                  {{ form.customerName ? form.customerName : '请选择客户' }}
                  <i class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
              <el-form-item prop="beginTime" label="开始时间：">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  class="eldate"
                  v-model="form.beginTime"
                  type="date"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="关联合同：" prop="contractId">
                <div
                  class="unitbtn sw"
                  :class="{ selectedCss: form.contractName }"
                  @click="chooseContract"
                >
                  {{ form.contractName ? form.contractName : '请选择' }}
                  <i
                    @click.stop="closeCon"
                    v-show="form.contractName"
                    class="rcenter el-icon-close"
                  ></i>
                  <i
                    v-show="!form.contractName"
                    class="rcenter el-icon-arrow-down"
                  />
                </div>
              </el-form-item>
              <el-form-item label="是否提成：" v-if="$route.query.id">
                <el-radio v-model="form.isCommission" :label="0"
                  >未提成</el-radio
                >
                <el-radio v-model="form.isCommission" :label="1"
                  >已提成</el-radio
                >
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目类型：" prop="projectType">
                <el-select
                  clearable=""
                  @change="changeValue"
                  :disabled="getDisable()"
                  class="definput els40 ellipsis"
                  popper-class="removescrollbar"
                  v-model="form.projectType"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in optionsContract"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
                <el-select
                  clearable=""
                  class="definput els50 ellipsis"
                  :disabled="getDisable()"
                  popper-class="removescrollbar"
                  v-model="form.projectTypeSub"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in optionsContractDetail"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="中标公司：" prop="bidwinnerId">
                <el-select
                  clearable=""
                  class="definput ellipsis"
                  popper-class="removescrollbar"
                  v-model="form.bidwinnerId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in companyList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="endTime" label="结束时间：">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  class="eldate"
                  v-model="form.endTime"
                  type="date"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="项目金额(万元)：" prop="projectAmount">
                <el-input-number
                  clearable
                  type="number"
                  :controls="false"
                  :min="0"
                  :precision="4"
                  v-model="form.projectAmount"
                  class="definput widm textleft"
                  placeholder="请输入项目金额"
                ></el-input-number>
                <span>
                  <el-checkbox v-model="isOpen">不公开</el-checkbox>
                </span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="20">
              <el-form-item label="项目描述：" prop="description" class="mb20">
                <el-input
                  clearable=""
                  type="textarea"
                  :rows="5"
                  maxlength="500"
                  show-word-limit
                  v-model="form.description"
                  class="definput"
                  placeholder="请在这里对项目进行简单描述"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">负责与协作</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="业务负责人：" prop="businessOwner">
                <span class="mr10">{{ form.businessOwnerName }}</span>
                <el-button
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择业务负责人', 1)"
                >
                  点击选择业务负责人</el-button
                >
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目负责人：" prop="chargePerson">
                <span class="mr10">{{ form.chargePersonName }}</span>
                <el-button
                  class="bBtn"
                  icon="el-icon-plus"
                  type="text"
                  @click="clickXuan('选择项目负责人', 1)"
                >
                  点击选择项目负责人</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">补充信息</textBorder>
        <div class="pt20 bbline">
          <el-form-item label="附件：">
            <upload2
              :fileMaxSize="100"
              ref="upload2"
              @submitImg="submitPdf"
              accept=".pdf,.doc,.docx,.xlsx"
              :fileList="fileListPdf"
            >
              <span class="studiocss">
                <img src="../../../assets/img/file_icon.png" />
                <span class="uploadtext deffont">点击上传附件</span>
              </span>
              <template slot="ptip">
                <p>只能上传pdf，doc，docx，xlsx文件</p>
              </template>
            </upload2>
          </el-form-item>
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="12">
              <el-form-item label="备注：" class="mb20">
                <el-input
                  maxlength="300"
                  show-word-limit
                  v-model="form.notes"
                  class="definput"
                  type="textarea"
                  rows="4"
                  placeholder="请输入备注"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="pt20 btncenter">
          <el-form-item>
            <el-button
              :disabled="disabled"
              class="w98 btn_h42"
              type="primary"
              @click="saveButton"
              >保存
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :filtrationList="filtrationList"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
    <!-- 客户选择 -->
    <cuatomerDialog
      ref="customer"
      :visible.sync="customerDialogVisible"
      @updateVisible="updateVisible"
      @updateCustomer="updateCustomer"
    ></cuatomerDialog>

    <!-- 合同选择 -->
    <contractDialog
      ref="contractRef"
      :visible.sync="contractVisible"
      @updateVisible="updateContractVisible"
      @updateData="updateContractData"
    ></contractDialog>
    <!-- 部门验证组件 -->
    <verifyDeparment
      ref="verifyDeparment"
      @submit="submitWithDepartment"
    ></verifyDeparment>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import textBorder from '../../common/textBorder.vue'
import systemDialog from '../../common/systemDialog.vue'
import cuatomerDialog from '../../common/customerDialog.vue'
import contractDialog from '../../common/contractDialog.vue'
import { getDict } from '@/utils/tools.js'
import { projectSave, projectUpdate, proInfo } from '@/api/project/index'
import { contractInfo } from '@/api/contract'
import upload2 from '@/components/common/upload2.vue'
import { getFileTypeNum } from '@/utils/tools'

import { selectTemplate } from '@/api/project/index.js'
import { queryVoListByCode,queryDictTypeList } from '@/api/wapi.js'
import VerifyDeparment from '@/components/common/verifyDeparment.vue'
export default {
  components: {
    back,
    textBorder,
    systemDialog,
    cuatomerDialog,
    contractDialog,
    upload2,
    VerifyDeparment
  },
  data() {
    return {
      rules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' },
        ],
        customerId: [
          { required: true, message: '请选择客户', trigger: 'change' },
        ],
        projectStatus: [
          { required: true, message: '请选择项目状态', trigger: 'change' },
        ],
        projectType: [
          { required: true, message: '请选择项目类型', trigger: 'change' },
        ],
        beginTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' },
        ],
        projectAmount: [
          { required: true, message: '请输入项目金额', trigger: 'blur' },
        ],
        chargePerson: [
          { required: true, message: '请选择项目负责人', trigger: 'change' },
        ],
        businessOwner: [
          { required: true, message: '请选择业务负责人', trigger: 'change' },
        ],
        contractId: [
        { required: true, message: '请选择关联合同', trigger: 'change' },
        ],
        bidwinnerId: [
          { required: true, message: '请选择中标公司', trigger: 'change' },
        ],
      },
      optionsStatus: [
        {
          value: 1,
          name: '未开始',
        },
        {
          value: 2,
          name: '进行中',
        },
        {
          value: 3,
          name: '已完成',
        },
        {
          value: 4,
          name: '暂停',
        },
        {
          value: 5,
          name: '停止',
        },
      ],
      pStatus: {
        1: [
          {
            value: 1,
            name: '未开始',
          },
          {
            value: 2,
            name: '启动',
          },
        ],
        2: [
          {
            value: 2,
            name: '进行中',
          },
          {
            value: 3,
            name: '已完成',
          },
          {
            value: 4,
            name: '暂停',
          },
          {
            value: 5,
            name: '停止',
          },
        ],
        3: [
          {
            value: 3,
            name: '已完成',
          },
        ],
        4: [
          {
            value: 2,
            name: '启动',
          },
          {
            value: 4,
            name: '暂停',
          },
        ],
        5: [
          {
            value: 5,
            name: '停止',
          },
        ],
      },
      optionsContract: [],
      optionsContractDetail: [],
      unitDialogVisible: false,
      fileList: [],
      dialogVisible: false,
      dialogName: '',
      options: [],
      customerDialogVisible: false,
      isOpen: false,
      form: {
        projectName: '',
        projectStatus: '',
        customerId: '',
        contractId: '',
        contractName: '',
        beginTime: '',
        endTime: '',
        projectType: '',
        projectTypeSub: '',
        projectAmount: undefined,
        isTemplate: 0,
        templateId: '',
        chargePerson: '',
        businessOwner: '',
        description: '',
        notes: '',
        isPublic: true,
        fileInfoList: [],
        bidwinnerId: '',
      },
      filtrationList: [],
      contractAmount: '',
      contractVisible: false,
      multipleNum: 1,
      fileListPdf: [],
      proId: '',
      xiezuolist: [],
      titleName: '新建项目',
      showTem: false,
      disabled: false,
      companyList: [],
    }
  },
  created() {
    if (this.$route.query.contractId) {
      this.form.contractId = this.$route.query.contractId
      this.loadContract(this.form.contractId)
    }
    this.projectstagetemplateListData()
    this.getCompany()
    if (this.$route.query.id) {
      this.proId = this.$route.query.id
      this.proInfoData()
      this.titleName = '编辑项目'
    } else {
      this.showTem = true
      this.getDictApi()
    }
  },
  methods: {
    closeCon() {
      this.form.contractName = ''
      this.form.contractId = ''
      this.contractAmount = ''
      this.$refs.addform.validateField(['contractId'])
    },
    getDictApi() {
      queryVoListByCode({ code: 'ContractType' }).then((res) => {
        if (res.status == 0 && res.data.length > 0) {
          this.optionsContract = res.data
          if (this.form.projectType) {
            this.setValue(this.form.projectType)
          }
        }
      })
    },
    getCompany() {
      queryDictTypeList({code:'Company'}).then((result) => {
        this.companyList = result.data['Company'][0].dictItemVoList || []
      }).catch((err) => {

      });
    },
    proInfoData() {
      proInfo(this.proId).then((res) => {
        if (res.status == 0) {
          this.form = res.data
          this.contractAmount =
            res.data.contractAmount == 0 ? '' : res.data.contractAmount
          this.isOpen = !this.form.isPublic
          this.optionsStatus = this.pStatus[res.data.projectStatus]
          var dataScope = sessionStorage.getItem('dataScope')
          if (res.data.projectStatus == 3 && dataScope != 4) {
            this.disabled = true
          }
          var ids = res.data.collaborator && res.data.collaborator.split(',')
          let names =
            res.data.collaboratorName && res.data.collaboratorName.split(',')
          ids &&
            ids.forEach((item, index) => {
              var data = {}
              data.id = item
              data.name = names[index]
              this.xiezuolist.push(data)
            })
          res.data.fileInfoList.forEach((item) => {
            item.name = item.fileName
          })
          this.fileListPdf = res.data.fileInfoList
          this.$refs.upload2.setFileList(this.fileListPdf)
          this.getDictApi()
        }
      })
    },
    projectstagetemplateListData() {
      selectTemplate().then((res) => {
        if (res.status == 0) {
          this.options = res.data
        }
      })
    },
    submitPdf(fileList) {
      this.fileListPdf = fileList
    },
    loadContract(contractId) {
      contractInfo(contractId)
        .then((result) => {
          this.form.contractName = result.data.contractTitle
          this.form.projectName = result.data.contractTitle
          this.form.projectType = result.data.contractType
          this.form.projectTypeSub = result.data.contractTypeSub
          this.form.customerId = result.data.customerId
          this.form.customerName = result.data.customerName
          this.contractAmount = result.data.contractAmount
          this.form.beginTime = result.data.beginTime
          this.form.endTime = result.data.endTime
          this.getDictApi()
        })
        .catch((err) => {})
    },
    changeValue(val) {
      if (val == '') {
        this.form.projectTypeSub = ''
        this.optionsContractDetail = []
      } else {
        this.form.projectTypeSub = ''
        let item = this.optionsContract.filter((item) => item.id === val)
        this.optionsContractDetail = item[0].children
      }
    },
    setValue(val) {
      let item = this.optionsContract.filter((item) => item.id === val)
      this.optionsContractDetail = item[0].children
    },
    arrForEach(arrFileList) {
      arrFileList.forEach((item) => {
        this.form.fileInfoList.push({
          ...item,
          fileType: getFileTypeNum(item.url),
        })
      })
    },
    saveButton() {
      this.$refs['addform'].validate((valid) => {
        if (valid) {
          if (this.form.customerId == '') {
            this.msgError('请选择客户')
            return
          }
          if (this.form.projectType == '' || this.form.projectTypeSub == '') {
            this.msgError('请选择项目类型')
            return
          }
          if (this.form.chargePerson == '') {
            this.msgError('请选择项目负责人')
            return
          }
          if (this.form.businessOwner == '') {
            this.msgError('请选择业务负责人')
            return
          }

          if (this.form.beginTime > this.form.endTime) {
            this.msgError('结束时间不能小于开始时间')
            return
          }
          if (this.$refs.upload2.getUploading()) {
            this.msgError('请文件上传成功后再保存')
            return
          }
          this.form.isPublic = !this.isOpen
          this.form.fileInfoList = []
          if (this.fileListPdf.length > 0) {
            this.arrForEach(this.fileListPdf)
          }
          if (this.proId) {
            projectUpdate(this.form).then((res) => {
              if (res.status == 0) {
                this.msgSuccess('更新成功')
                this.$router.back()
              }
            })
          } else {
            this.$refs.verifyDeparment.verify()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    submitWithDepartment(departmentId) {
      this.form.createDepartmentId = departmentId;
      projectSave(this.form).then((res) => {
        if (res.status == 0) {
          this.msgSuccess('添加成功')
          this.$router.back()
        }
      })
    },
    chooseContract() {
      if (this.$route.query.contractId) {
        return
      }
      if (this.form.contractId == 0) {
        this.form.contractId = ''
      }
      let params = {
        id: this.form.contractId || '',
        methodName: 'list',
        className: 'ProjectController',
        opportunityId: '',
      }
      this.$refs.contractRef.selectContractData(params)
      this.contractVisible = true
    },
    updateContractData(data) {
      this.form.contractName = data.contractTitle
      this.form.contractId = data.id
      this.contractAmount = data.contractAmount
      this.$refs.addform.validateField(['contractId'])
    },
    updateContractVisible(val) {
      this.contractVisible = val
    },
    updateCustomer(data) {
      this.form.customerName = data.customerName
      this.form.customerId = data.id
      this.form.unitId = data.unitId
      this.form.unitName = data.unitName
      this.$refs.addform.validateField(['customerId'])
    },
    updateVisible(val) {
      this.customerDialogVisible = val
    },
    chooseCustomer() {
      if (this.$route.query.contractId) {
        return
      }
      this.customerDialogVisible = true
      this.$refs.customer.selectCustomerData({
        id: this.form.customerId,
        className: 'ProjectController',
      })
    },
    // 选择协作人
    clickXuan(name, multipleNum) {
      this.dialogName = name
      this.multipleNum = multipleNum
      this.$refs.systemdialog.loadData()
      if (name == '选择业务负责人') {
        this.filtrationList = this.form.chargePerson
          ? [this.form.chargePerson]
          : []
        this.form.businessOwner
          ? this.$refs.systemdialog.updateWorksId([
              {
                id: this.form.businessOwner,
                name: this.form.businessOwnerName,
                departmentId: this.form.projectDepartmentId,
              },
            ])
          : this.$refs.systemdialog.updateWorksId([])
      } else if (name == '选择项目负责人') {
        this.filtrationList = this.form.businessOwner
          ? [this.form.businessOwner]
          : []
        this.form.chargePerson
          ? this.$refs.systemdialog.updateWorksId([
              {
                id: this.form.chargePerson,
                name: this.form.chargePersonName,

              },
            ])
          : this.$refs.systemdialog.updateWorksId([])
      }
      this.dialogVisible = true
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    submitData(data, type, departmentId) {
      if (type == '选择业务负责人') {
        this.form.businessOwner = data.length > 0 ? data[0].id : ''
        this.form.businessOwnerName = data.length > 0 ? data[0].name : ''
        this.form.projectDepartmentId = departmentId
        this.$refs.addform.validateField(['businessOwner'])
      } else if (type == '选择项目负责人') {
        this.form.chargePerson = data.length > 0 ? data[0].id : ''
        this.form.chargePersonName = data.length > 0 ? data[0].name : ''

        this.$refs.addform.validateField(['chargePerson'])
      }
      this.updateSystemVisible(false)
    },
    getDisable() {
      if (this.$route.query.contractId) {
        return true
      }
      return false
    },
  },
}
</script>
<style scoped lang="scss">
.mb20 {
  margin-bottom: 40px !important;
}
.widm {
  width: calc(100% - 130px);
  margin-right: 20px;
}
.discss {
  background-color: #f5f7fa;
}

.tagcss /deep/ .el-icon-close {
  width: 12px;
  height: 12px;
  line-height: 12px;
  background-color: #4285f4;
  color: white;
}

.tagcss {
  margin-left: 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  background: #dfeafd;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}

.ellipsis /deep/.el-input__inner {
  padding: 0 8px !important;
}

/deep/ .el-input__suffix {
  right: 0 !important;
}
.textleft /deep/.el-input__inner {
  text-align: left !important;
}
.studiocss img {
  width: 16px;
  height: 16px;
  margin-right: 3px;
  margin-top: -3px;
}

.eldate {
  width: 100% !important;
  padding: 0;

  /deep/.el-input__inner {
    height: 34px;
    line-height: 34px;
  }

  /deep/.el-input__icon {
    line-height: 34px;
  }
}

.els50 {
  width: 58%;
}

.els40 {
  width: 40%;
  margin-right: 1%;
}

.els50:last-child {
  margin-right: 0;
  margin-left: 1%;
  float: right;
}

.elflex {
  display: flex;
}

.w98 {
  width: 98px;
}

.flex {
  display: flex;
  flex-direction: column;
}

.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}

.btncenter {
  text-align: center;
}

.quanxiancss /deep/.el-form-item__label {
  margin-left: -7px;
  width: 125px !important;
}

.addresscss {
  max-height: 68px;
}

.input-new-tag {
  width: 200px;
  margin-left: -8px;
}

.input-new-tag /deep/.el-input__inner {
  border: none;
  background-color: rgba(0, 0, 0, 0);
}

.tagcol /deep/.el-form-item__label {
  height: 34px;
  line-height: 34px;
}

.tagcol /deep/.el-form-item__content {
  background: #f5f5f5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  min-height: 34px;
  line-height: 34px;
}

.mainbg {
  margin: 16px 0px;
  padding: 20px 16px;
  background-color: white;
  border-radius: 8px;
}

.pd0 {
  padding-right: 0px !important;
}
</style>
<style>
</style>
