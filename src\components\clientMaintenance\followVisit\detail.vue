<template>
  <el-dialog
    title="跟进与拜访详情"
    :visible.sync="centerDialogVisible"
    fullscreen
    center>
    <div class="mainbg" ref="box">
    <div class="scrolldiv">
      <el-row :gutter="20">
        <el-col :span="ccol">
          <listitem @setWidth="setWidth"   @updateCommont="updateCommont" @loadData="loadData" @audioPlay="audioPlay" :item="item"></listitem>
        </el-col>
        <el-col  :span="rcol" class="">
          <el-card :style="{width: divWidth}" class="box-card lefttable rdiv">
            <i class="el-icon-close closeicon" @click="closeR"></i>
            <el-tabs v-model="activeName" @tab-click="handleClick"  class="tabscss vili">
              <el-tab-pane label="详情" name="first">
                <el-form class="infoform" ref="form" :model="formInfo" label-width="80px">
                  <textBorder>基本信息</textBorder>
                  <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline" >
                      <el-col :span="12">
                          <el-form-item label="客户名称:" class="labeltext mulitline">
                              <span>{{formInfo.customerName}}</span>
                          </el-form-item>
                          <el-form-item label="部门:" class="labeltext">
                            <span>{{formInfo.unitDepartment}}</span>
                          </el-form-item>
                          <el-form-item label="客户级别:" class="labeltext mulitline">
                            <span :class="customerLevelColors[formInfo.customerLevelName]">{{formInfo.customerLevelName}}</span>
                            </el-form-item>
                            <el-form-item label="负责课程:" class="labeltext">
                              <span>{{formInfo.responsibleCourse}}</span>
                          </el-form-item>
                      </el-col>
                      <el-col :span="12">  
                          <el-form-item label="客户单位:" class="labeltext">
                                <span>{{formInfo.unitName}}</span>
                          </el-form-item>
                          <el-form-item label="职务:" class="labeltext">
                              <span class="colorcss">{{formInfo.duties}}</span>
                          </el-form-item>
                          <el-form-item label="负责专业:" class="labeltext">
                            <span>{{formInfo.specialtyName?formInfo.specialtyName.join(','):''}}</span>
                        </el-form-item>
                      </el-col>
                  </el-row>
                  <textBorder>客户标签</textBorder>
                  <el-row :gutter="20" class="width100 mtop20 bbline mb30" >
                      <el-col :span="24" class="mb20 ml40">
                          <span class="tagitemcss" :style="colors[index]" :key="index" v-for="(item,index) in formInfo.tags">{{item}}</span>
                      </el-col>
                  </el-row>
                  <textBorder>联系信息</textBorder>
                  <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
                      <el-col :span="12" class="mb10">
                          <el-form-item label="电话:" class="labeltext mulitline">
                              <span>{{formInfo.phone}}</span>
                          </el-form-item>
                          <el-form-item label="微信:" class="labeltext mulitline">
                            <span>{{formInfo.wechat}}</span>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12" class="mb10">  
                          <el-form-item label="邮箱:" class="labeltext mulitline">
                          <span>{{formInfo.mailbox}}</span>
                          </el-form-item>
                      </el-col>
                  </el-row>
                  <textBorder>数据画像</textBorder>
        <el-row :gutter="20" class="width100 mt10 pb5">
            <el-col :span="12" class="">
                <el-form-item label="性别:" class="labeltext">
                    <span>{{gender[formInfo.sex]}}</span>
                </el-form-item>
                <el-form-item label="年龄:" class="labeltext">
                    <span>{{formInfo.age}}</span>
                </el-form-item>
                <el-form-item label="民族:" class="labeltext">
                    <span>{{formInfo.nationName}}</span>
                </el-form-item>
                <el-form-item label="职称:" class="labeltext">
                    <span>{{formInfo.professionalTitles}}</span>
                </el-form-item>
                <el-form-item label="工作地址:" class="labeltext mulitline">
                    <span>{{formInfo.address}}</span>
                </el-form-item>
                <el-form-item label="家庭地址:" class="labeltext mulitline">
                <span>{{formInfo.homeAddress}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="mb10">  
                <el-form-item label="身份证号:" class="labeltext mulitline">
                    <span>{{formInfo.cardNumber}}</span>
                </el-form-item>
                <el-form-item label="籍贯:" class="labeltext mulitline">
                    <span>{{formInfo.nativePlace}}</span>
                </el-form-item>
                <el-form-item label="生日:" class="labeltext">
                    <span>{{formInfo.birthday}}</span>
                </el-form-item>
                <el-form-item label="职称时间:" class="labeltext">
                    <span>{{formInfo.professionalTime}}</span>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="width100 mt10 mb50 bbline ">
            <el-col :span="24">
                <el-form-item label="爱好情况:" class="labeltext mulitline">
                    <span>{{formInfo.hobby}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="论文课题:" class="labeltext mulitline">
                    <span>{{formInfo.thesisTopic}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="研究方向:" class="labeltext mulitline">
                    <span>{{formInfo.researchDirection}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="车辆信息:" class="labeltext mulitline">
                            <span>{{formInfo.carInfo}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="车牌号:" class="labeltext mulitline">
                            <span>{{formInfo.carNumber}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>
            <el-col :span="24" >
                 <el-form-item label="婚恋情况:" class="labeltext mulitline">
                    <span>{{formInfo.marriage}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="在意的人:" class="labeltext mulitline">
                    <p class='carep' v-for="(item,index) in inputTags1" :key="index">{{item}}</p>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="mb10">
                <el-form-item label="子女情况:" class="labeltext mulitline">
                    <span>{{formInfo.children}}</span>
                </el-form-item>
            </el-col>
        </el-row>
              </el-form>
              </el-tab-pane>
              <el-tab-pane  label="历史用书情况" name="second">
                <div class="">
                  <el-table class="unittable mytable" :data="tableData" >
                    <el-table-column prop="materialName" label="教材名称" align="center">
                    </el-table-column>
                    <el-table-column prop="platformName" label="出版社" align="center">
                    </el-table-column>
                    <el-table-column prop="author" label="主编" align="center">
                    </el-table-column>
                    <el-table-column prop="price" label="价格" align="center">
                    </el-table-column>
                    <el-table-column prop="bookNumber" label="用书量" align="center">
                    </el-table-column>
                    <el-table-column prop="useBookYear" label="用书时间" align="center">
                    </el-table-column>
                    <el-table-column prop="bookSpecialtyName" label="用书专业" align="center">
                    </el-table-column>
                    <el-table-column prop="isJoin" label="类型" align="center" >
                      <template slot-scope="scope">
                          <span>{{typeObj[scope.row.isJoin]}}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                  <page :currentPage="pageBeanLi.pageNum" :total="totalLi" :pageSize="pageBeanLi.pageSize"
                    @updatePageNum="handleCurrentChange"></page>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
  </el-dialog>

  
</template>

<script>
import listitem from './listitem.vue';
import { visitInfo } from '@/api/visit/index'
import textBorder from '@/components/common/textBorder.vue'
import page from '@/components/common/page.vue';
import {customerInfo } from "@/api/clientmanagement/customer";
import { customerbook} from '@/api/clientmanagement/unit'
import { customerLevelColors } from "@/utils/dict";
import { downloadFileByUrl } from "@/utils/tools";
export default {
  components:{
    listitem,
    textBorder,
  },
  data(){
    return{
      centerDialogVisible:false,
      customerLevelColors:customerLevelColors,
      ccol:24,
      rcol:0,
      loading:true,
      dataList:[],
      datarange:[],
      isDownload:false,
      pageBean:{
        pageNum:1,
        pageSize:10,
        assistType:'',
        customerName:'',
        unitName:'',
        createByName:'',
        createByDepartment:"",
        startTime:'',
        endTime:'',
        time:'',
        keyword:"",
      },
      total:0,
      types:[
                {
                    label:"电话拜访",
                    value:"1",
                },
                {
                    label:"线上拜访",
                    value:"2",
                },
                {
                    label:"实地拜访",
                    value:"3",
                },
            ],
            onOff:false,
            loadBoo:false,
            divWidth:0,
            activeName:'first',
            formInfo:{},
            colors:[
                {
                    color:'#FD7A41',
                    background:'#FFECE3'
                },
                {
                    color:'#4285F4',
                    background:'#DFEAFD'
                },
                {
                    color:'#FFA44C',
                    background:'#FFF2E5'
                },
                {
                    color:'#AC6FFB',
                    background:'#F0E6FE'
                },
                {
                    color:'#67C23A',
                    background:'#E6F3D8'
                },
          ],
          tableData: [],
          totalLi: 0,
          inputTags1:[],
          gender:{
            1:'男',
            2:'女'
          },
          pageBeanLi:{
            pageNum: 1,
            pageSize: 6,
            customerId: '',
          },
          typeObj:{
              1:'过往合作',
              3:'历史发货',
              2:'非合作'
          },
          item:{},
    }
  },
  created(){
  },
  mounted () {
  },
  beforeDestroy(){
  },
  methods:{
    show(){
      this.centerDialogVisible = true;
    },
    hidden(){
      this.centerDialogVisible = false;
    },
    loadData(id){
      visitInfo(id).then((result) => {
        this.item = result.data
      }).catch((err) => {
        
      });
    },
    customerbookApi(){
        customerbook(this.pageBeanLi).then(res=>{
          if(res.status == 0){
                this.tableData = res.data
                this.totalLi = res.page.total
          }
        })
    },
    loadDataInfo(customerId){
            customerInfo(customerId).then((result) => {
                this.formInfo = result.data;
                this.formInfo.tags = this.formInfo.label && this.formInfo.label.split(',')
                this.inputTags1 = result.data.importPeople&& result.data.importPeople.split(',');
                this.formInfo.sex = this.formInfo.sex == 0 ? '' :this.formInfo.sex;
                this.formInfo.age = this.formInfo.age == 0 ? '' :this.formInfo.age;
            }).catch((err) => {
                
            });
        },
      handleCurrentChange(page) {
        this.pageBeanLi.pageNum = page;
        this.customerbookApi()
      },
      handleClick(tab, event) {
                this.activeName = tab.name;
      },
    setWidth(customerId){
        this.ccol = 12;
        this.rcol = 12
        this.$nextTick(()=>{
          this.divWidth = document.getElementsByClassName("itemheader")[0].clientWidth  + 'px'
        })
        this.loadDataInfo(customerId)
        this.pageBeanLi.customerId = customerId
        this.pageBeanLi.pageNum =1
        this.customerbookApi()
    },
    closeR(){
      this.ccol = 24;
      this.rcol = 0
      this.$nextTick(()=>{
          this.divWidth = 0  + 'px'
      })
      this.$store.commit('cus/CHANGECOL',6)
    },
  updateCommont(item){
  },
    audioPlay(item,boolean){
        this.dataList.forEach(item1=>{
          if(item1.soundList.length>0){
            item1.isPlay = false
          }
        })
       item.isPlay =boolean
    },
    addVisit(){
      this.$router.push('/clientMaintenance/followVisit/add')
    },
    exportVisit(){
      var exportPar = Object.assign({},this.pageBean)
     delete exportPar.pageNum
     delete exportPar.pageSize
     this.isDownload = true;
      visitDownloadExcel(exportPar).then((result) => {
        downloadFileByUrl(result.data.url,result.data.fileName)
        this.isDownload = false;
      }).catch((err) => {
        this.isDownload = false;
      });
    },
    submitAction(type,dateRange){
        if (type == 11) {
            if (dateRange) {
                this.pageBean.startTime = dateRange[0];
                this.pageBean.endTime = dateRange[1]
            }else{
                this.pageBean.startTime = ''
                this.pageBean.endTime = ''
            }
            this.pageBean.time = ''
        }else{
            this.pageBean.time = type;
            this.pageBean.startTime = '';
            this.pageBean.endTime = '';
        }
    },
  }

}
</script>
<style>
  .vili .el-tabs__header {
  }
</style>
<style scoped>
  .mb50{
    margin-bottom: 50px;
  }
  .fixbottom{
    position: fixed;
    right: 25px;
    bottom: 0;
    left: 190px;
    z-index:10;
    padding-bottom: 20px;
    background: #fff;
    box-shadow: 0px -2px 20px 0px rgba(0,0,0,0.16);
  }
  .width120{
    width: 120px;
  }
  .carep{
    line-height: 30px;
  }
  .carep:first-child{
    margin-top: 5px;
  }
  .labeltext {
    margin-bottom: 5px !important;
  }
  .mtop20{
  margin-top: 20px;
}
  .closeicon{
    position: absolute;
    right: 10px;
    cursor: pointer;
  }
  .lefttable{
    position: relative;
  }
  .tagitemcss{
    font-size: 12px;
    padding:4px 8px;
    margin: 0px 4px;
    background-color: #DFEAFD;
    border-radius: 2px;
    color: #4285F4;
}
  .rdiv{
    position: fixed;
    top: 250px;
    right: 34px;
    bottom: 20px;
    overflow-y: auto;
  }
.nolist{
  height: calc(100vh - 283px);
  text-align: center;
  overflow: hidden;
}
.noimg{
  margin-top: 180px;
}
.loading{
  width: 100px;
  height: 60px;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.f16{
  font-size: 16px;
  color: #333333;
}

.pb15{
  padding-bottom: 15px;
}
.mt3{
  margin-top: 3px;
}
.ml{
  margin-left: -80px;
}
.mainbg{
  background-color: white;
  padding: 20px;
  position: relative;
  padding-bottom: 86px;
}

.right{
    text-align: right;
}
</style>
