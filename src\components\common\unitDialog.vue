<template>
    <el-dialog :before-close="handleClose" class="unitdialog" title="选择单位" top="70px" :visible.sync="dialogTableVisible" width="70%" center>
        <el-form class="unitfcss" :inline="true">
            <el-form-item label="单位名称：">
                <el-input class="definput" v-model="pageBean.unitName" clearable placeholder="请输入单位名称"></el-input>
            </el-form-item>
            <el-form-item label="">
                <el-button class="defaultbtn ml20" icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table class="mytable" :data="dataList" height="478px" v-loading="isLoading">
            <el-table-column class-name="column_blue" property="unitName" label="单位名称" align="center"></el-table-column>
            <el-table-column property="phone" label="电话" align="center"></el-table-column>
            <el-table-column property="edit" label="操作" align="center">
                <template slot-scope="scope" >
                    <div class="rbtn deffont" v-if="scope.row.id == unitData.id">取消选择</div>
                    <div class="bbtn deffont" v-else @click="chooseAction(scope.row)">选择</div>
                </template>
            </el-table-column>
        </el-table>
        <div class="center">
            <el-button class="defaultbtn" type="primary" @click="submitAction">提交</el-button>
        </div>
        <page 
        :currentPage="pageBean.pageNum" 
        :pageSize="pageBean.pageSize" 
        :total="total"
         @updatePageNum="handleCurrentChange"
        ></page>
    </el-dialog>
</template>

<script>

import page  from './page.vue';
import { selectUnit } from "@/api/clientmanagement/unit";
export default {
    components:{
        page
    },
    data(){
        return{
            isLoading:false,
            dataList:[],
            pageBean:{
                isDeleted: 0,
                unitIds:'',
                unitName:"",
                methodName:"list",
                className:this.className,
                pageNum:1,
                pageSize:8,
            },
            total:0,
            unitData:{}
        }
    },
    props:{
        visible:{
            type:Boolean,
            default:false
        },
        className:{
            type:String,
            default:"CustomerController"
        }
    },
    computed:{
        dialogTableVisible:{
            get(){
                return this.visible;
            },
            set(val){
                this.$emit('updateVisible',val);
            },
        }
    },
    methods:{
        handleClose(){
            this.pageBean.pageNum = 1
            this.pageBean.unitName = ''
            this.dialogTableVisible = false;
        },
        searchAction(){
            this.pageBean.pageNum = 1;
            this.loadData();
        },
        loadData(data){
            this.isLoading = true;
            this.pageBean.unitIds = data ?  data : '';
            if (this.pageBean.className === 'PersonalGoalsController') {
                this.pageBean.methodName = 'info';
            }
            selectUnit(this.pageBean).then((result) => {
                this.isLoading = false;
                this.dataList = result.data;
                this.total = result.page.total;
                
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        submitAction(){
            this.$emit('updateUnit',this.unitData)
            this.dialogTableVisible = false;
            this.unitData = {};
        },
        handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.loadData();
        },
        chooseAction(val){
            this.unitData = val
        }
    }
}
</script>
<style scoped>
.center{
    margin-top: 50px;
    text-align: center;
}
.bbtn{
    color: #4285F4;
    cursor: pointer;
}
.rbtn{
    color: #F45961;
    cursor: pointer;
}

.unitfcss{
    line-height: 34px;
}
.unitfcss /deep/.el-form-item__label{
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
}
.unitfcss /deep/.el-form-item{
    margin-bottom: 20px;
    /* padding-right: 60px; */
}
.ml20{
    margin-left: 20px;
}
.unitdialog /deep/.el-dialog__body{
    padding: 20px;
    padding-top: 0px;
}
.unitdialog /deep/.el-dialog__header{
    border: none;
}
</style>
<style>

</style>