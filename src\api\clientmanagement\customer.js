import request from '@/utils/request'

// 客户列表
export function listCustomer(params) {
  return request({
    url: '/crm/controller/customer/list',
    method: 'get',
    params
  })
}
// 客户详情
export function customerInfo(id) {
  return request({
    url: `/crm/controller/customer/info/${id}`,
    method: 'get',
  })
}

// 新建客户
export function addCustomer(data) {
  return request({
    url: '/crm/controller/customer/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}

// 查看跟进
export function workReportSetRead(data) {
  return request({
    url: '/crm/business/visitrecords/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}





// 客户新增时根据单位id查询单位开设的专业
export function selectUintSpecialty(params) {
  return request({
    url: "/crm/business/unitspecialty/selectUintSpecialty",
    method: 'get',
    params
  })
}

// 更新客户
export function updateCustomer(data) {
  return request({
    url: '/crm/controller/customer/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

// 数据概览
export function queryCustomerOverview(params) {
  return request({
    url: "/crm/controller/customer/queryCustomerOverview",
    method: 'get',
    params
  })
}
// 逻辑删除客户
export function deleteCustomer(data) {
  return request({
    url: '/crm/controller/customer/delete',
    method: 'post',
    data
  })
}
// /crm/business/customercare/queryVoList
export function customercareQueryVoList(params) {
  return request({
    url: "/crm/business/customercare/queryVoList",
    method: 'get',
    params
  })
}
// /crm/business/customercare/save
export function customercareSave(data) {
  return request({
    url: '/crm/business/customercare/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

// /crm/business/customercare/update
export function customercareUpdate(data) {
  return request({
    url: '/crm/business/customercare/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

// 回收站-客户列表
export function recycleCustomerList(params) {
  return request({
    url: '/crm/controller/customer/recycleCustomerList',
    method: 'get',
    params
  })
}
// 回收站删除
export function physicalDelete(data) {
  return request({
    url: '/crm/controller/customer/physicalDelete',
    method: 'post',
    data
  })
}

// 回收站删除单位
export function physicalDeleteUnit(data) {
  return request({
    url: '/crm/controller/unit/physicalDelete',
    method: 'post',
    data
  })
}
// 回收站-批量删除单位
export function batchDeleteUnit(data) {
  return request({
    url: '/crm/controller/unit/deleteList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}

//回收站-批量删除客户
export function batchDeleteCustomer(data) {
  return request({
    url: '/crm/controller/customer/deleteList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  })
}
// 回收站-还原客户数据
export function restoreCustomer(data) {
  return request({
    url: '/crm/controller/customer/restore',
    method: 'post',
    data
  })
}
// 回收站-还原单位
export function restoreUnit(data) {
  return request({
    url: '/crm/controller/unit/restore',
    method: 'post',
    data
  })
}


export function queryIsEdit(id) {
  return request({
    url: `/crm/controller/customer/queryIsEdit/${id}`,
    method: 'get',
  })
}

// 用书信息
// 添加客户用书时，选择教材
export function querySelectMaterialVo(params) {
  return request({
    url: `/crm/business/customerbook/querySelectMaterial`,
    method: 'get',
    params
  })
}
// 添加用书信息
export function customerbookSave(data) {
  return request({
    url: '/crm/business/customerbook/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}
// 修改用书信息

export function customerbookUpdate(data) {
  return request({
    url: '/crm/business/customerbook/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

// 移除用书信息

export function customerbookDelete(data) {
  return request({
    url: '/crm/business/customerbook/delete',
    method: 'post',
    data
  })
}

// /crm/business/customerbook/selectSpecialty
// 用书专业
export function selectSpecialty(params) {
  return request({
    url: `/crm/business/customerbook/selectSpecialty`,
    method: 'get',
    params
  })
}
// /crm/business/customerbook/list

export function customerbookList(params) {
  return request({
    url: `/crm/business/customerbook/list`,
    method: 'get',
    params
  })
}
// /crm/business/customerbook/info/{id}

export function customerbookInfo(id) {
  return request({
    url: `/crm/business/customerbook/info/${id}`,
    method: 'get',
  })
}

// 客户-下载模版
export function customerDownLoad() {
  return request({
    url: `/crm/controller/customer/customerDownLoad`,
    method: 'get',
    responseType: "blob"
  })
}
//用书信息-下载模版
export function customerbookDownLoad(data) {
  return request({
    url: `/crm/business/customerbook/customerbookDownLoad`,
    method: 'get',
    params: data
  })
}


//客户导出
export function customerExport(params) {
  return request({
    url: `/crm/controller/customer/exportExcel`,
    method: 'get',
    params
  })
}