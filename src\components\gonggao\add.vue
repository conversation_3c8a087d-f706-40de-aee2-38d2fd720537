<template>
    <div>
        <bacK class="return">返回</bacK>
        <el-card>
            <div class="tilte">
                <div class="biaoti">标题:</div>
                <el-input maxlength="50" placeholder="单行输入" v-model="gonggaoData.version" clearable class="inpuit" size="medium"></el-input>
            </div>
            <div ref="editor"></div>
            <el-button type="primary" class="submitBtn" @click="saveContent">保存</el-button>
        </el-card>
    </div>
  </template>
  
<script>
    import E from 'wangeditor'
    import bacK from '../../components/common/back'
    import { saveGonggao,fetchGonggaoInfo,updateGonggao } from '@/api/gongao/index.js'

    export default {
    components:{
        bacK
    },
    data() {
        return {
            editor: null,
            content: '' ,
            gonggaoData: {
                version: '',
                description: ''
            },
            isEdit: false
            
        }
    },
    mounted() {
        this.initEditor();
        const id = this.$route.query.id;
        if (id) {
            this.isEdit = true;
            this.fetchGonggaoInfo(id);
        }
    },
    methods: {
        initEditor() {
            const editor = new E(this.$refs.editor);
            editor.config.onchange = (html) => {
                this.gonggaoData.description = html;
            }
            editor.create();
            this.editor = editor;
        },
        fetchGonggaoInfo(id) {
            fetchGonggaoInfo(id).then(response => {
                this.gonggaoData = response.data;
                this.editor.txt.html(this.gonggaoData.description);
            }).catch(error => { 
                console.error('获取公告详情失败:', error);
            });
        },
        saveContent() {
            const saveOrUpdate = this.isEdit ? updateGonggao : saveGonggao;
            saveOrUpdate(this.gonggaoData).then(response => {
                this.$message.success('保存成功');
                this.$router.push('/gonggao/index');
            }).catch(error => {
                console.error('保存失败:', error);
            });
        }
    }
}
  </script>
  
  <style scoped>
.tilte{
    display: flex;
    margin-bottom: 20px;
}
.biaoti{
    font-size: 20px;
    font-weight: bold;
    margin-right: 10px;
    margin-top: 5px;
}
.inpuit{
    width: 300px;
}
.return{
    margin-bottom: 20px;
}
.submitBtn{
    display: block;
    margin: 0 auto;
    margin-top: 20px;
}
  </style>