<template>
  <div>
    <div class="headdiv flex">
        <back>返回</back>
        <div class="flex">
            <div class="width250">
              <el-select clearable="" @change="changeValue"  class="definput els40 ellipsis"
                    popper-class="removescrollbar" v-model="pageBean.projectType" placeholder="请选择项目大类">
                    <el-option v-for="item in optionsContract" :key="item.id" :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
                <el-select clearable="" class="definput els50 ellipsis" @change="changeSub" popper-class="removescrollbar"
                    v-model="pageBean.projectTypeSub" placeholder="请选择项目子类">
                    <el-option v-for="item in optionsContractDetail" :key="item.id" :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
            </div>
            <el-date-picker
            v-model="par.year"
            type="year"
            class="definput fr ml20"
            format="yyyy年"
            data-format="yyyy"
            value-format="yyyy"
            placeholder="选择年"
            @change="chanegYear">
            </el-date-picker>
        </div>
    </div>
    <div class="condiv">
        <el-row :gutter="20" >
            <el-col  :span="4" v-for="item in datalist" :key="item.key" >
                <ditem class="br stadata"  :label="item.label" :value="`${item.value}`" :valueColor="item.color" :unit="item.unit"></ditem>
            </el-col>
        </el-row>
        <div class="mg30">
            <textBorder>项目概况表</textBorder>
        </div>
        <el-table
        class="mytable"
        :data="tableData"
        style="width: 100%"
        :default-sort = "{prop: 'totalWorkHours', order: 'descending'}"
        @sort-change="sortChange"
        >
        <el-table-column
        prop="projectName"
        label="项目名称"
        align="center"
        min-width="200">
        <template slot-scope="scope">
          <span class="bbtn" @click="toProjectDetail(scope.row)">{{scope.row.projectName}}</span>
        </template>
        </el-table-column>
        <el-table-column
        prop="totalWorkHours"
        label="项目总工时(小时)"
        sortable="custom"
        align="center"
        >
        </el-table-column>
        <el-table-column
        prop="projectAmount"
        label="项目金额(万元)"
        sortable="custom"
        align="center"
        >
        </el-table-column>
        <el-table-column
        prop="costAmount"
        label="成本金额(万元)"
        sortable="custom"
        align="center">
        </el-table-column>
        <el-table-column
        prop="grossMargin"
        label="毛利率"
        sortable="custom"
        :formatter="formatter"
        align="center">
        </el-table-column>
    </el-table>
    <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"></page>
    </div>
  </div>
</template>

<script>
import back from '@/components/common/back.vue';
import page from '@/components/common/page.vue';
import textBorder from '@/components/common/textBorder.vue';
import ditem from '../common/ditem.vue';
import { queryProjectDashboard,queryProjectOverviewForPage  } from '@/api/project/index';
import { getDict } from '@/utils/tools'
import { checkPermission } from '@/utils/permission.js'

export default {
    components:{
        back,
        page,
        textBorder,
        ditem
    },
    data(){
        return{
          optionsContract:[],
          optionsContractDetail:[],
          datalist:[
              {"key":'projectNum',label:'项目个数',value:"",unit:'个',color:'#4285F4'},
              {"key":'overdueProjectNum',label:'逾期项目',value:"",unit:'个',color:'#F45961'},
              {"key":'totalAmount',label:'项目总金额',value:"",unit:'万元',color:'#FF8D1A'},
              {"key":'totalWorkHours',label:'总工时',value:"",unit:'小时',color:'#4285F4'},
              {"key":'totalCost',label:'总成本',value:"",unit:'万元',color:'#FF8D1A'},
              {"key":'grossMargin',label:'平均毛利率',value:"",unit:'',color:'#55C36E'},
              ],
          par:{
            year:this.$route.query.year,
            projectType:this.$route.query.projectType,
            projectTypeSub:this.$route.query.projectTypeSub
          },
          pageBean:{
            pageNum:1,
            pageSize:10,
            projectType:this.$route.query.projectType,
            projectTypeSub:'',
            workHoursSort:2,
            projectAmountSort:0,
            costAmountSort:0,
            grossMarginSort:0,
            year:this.$route.query.year,
            projectTypeSub:this.$route.query.projectTypeSub
          },
          propValue:{
            "totalWorkHours":'workHoursSort',
            "projectAmount":"projectAmountSort",
            "costAmount":"costAmountSort",
            "grossMargin":"grossMarginSort"
          },
          tableData: [],
          total:0,
          props:{
              value:"id",
              label:'name'
          }
        }
    },
    created(){
      this.getTypesData()
      this.loadData();
      this.loadProjectOverviewForPage();
    },
    
    methods:{
      loadData(){
        queryProjectDashboard(this.par).then((result) => {
          console.log("dsssssss",result.data);
          this.datalist.forEach(element => {
            element.value = result.data[element.key].toString()
          });
        }).catch((err) => {
          
        });
      },
      loadProjectOverviewForPage(){
        queryProjectOverviewForPage(this.pageBean).then((result) => {
          console.log("dsssssss",result.data);
          this.tableData = result.data;
          this.total = result.page.total;
        }).catch((err) => {
          
        });
      },
      getTypesData(){
            getDict('ContractType').then(res=>{
                console.log('res===>',res)
                this.optionsContract = res
                if (this.$route.query.projectType ) {
                  let item = this.optionsContract.filter(item => item.id === this.$route.query.projectType)
                  this.optionsContractDetail = item[0].children
                }
                 
            })
        },
          changeType(data){
            console.log("ddsdddddd",data);
            this.par.projectTypeSub = data[data.length-1]
            this.pageBean.projectTypeSub = data[data.length-1]
            
          },
          changeValue(val) {
                if (val == '') {
                    this.pageBean.projectTypeSub = ''
                    this.par.projectType = ''
                    this.optionsContractDetail = []
                } else {
                    this.pageBean.projectTypeSub = ''
                    this.par.projectType = val
                    let item = this.optionsContract.filter(item => item.id === val)
                    this.optionsContractDetail = item[0].children
                }
                
                this.pageBean.pageNum = 1;
                this.loadData();
                this.loadProjectOverviewForPage()
            },
            setValue(val) {
                let item = this.optionsContract.filter(item => item.id === val)
                this.optionsContractDetail = item[0].children
            },

            changeSub(val){
                this.par.projectTypeSub = val
                this.pageBean.pageNum = 1;
                this.loadData();
                this.loadProjectOverviewForPage()
            },
          chanegYear(){
            this.pageBean.year = this.par.year
            this.pageBean.pageNum = 1;
            this.loadData();
            this.loadProjectOverviewForPage()
          },
          handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.loadProjectOverviewForPage()
          },
          sortChange(column){
            Object.keys(this.propValue).forEach(item => {
              if (item == column.prop) {
                this.pageBean[this.propValue[column.prop]] = column.order == 'descending' ? 2 :1
              }else{
                this.pageBean[this.propValue[item]] = 0
              }
            });
            this.pageBean.pageNum = 1;
            this.loadProjectOverviewForPage()
          },
          formatter(row, column) {
            return row.grossMargin + '%';
          },
          toProjectDetail(data){
            if (checkPermission('crm:controller:project:info')) {
              this.$router.push({
                  path: '/project/manager/detail',
                  query: { 
                    id:data.projectId,
                    name: data.projectName 
                }
              })
            }
          },
    }
}
</script>

<style lang="scss" scoped>
.width250{
  width: 250px !important;
}
.els50{
    width: calc(60% - 10px)
}
.els40{
    width: 40%;
    margin-right: 10px;
}
.bbtn{
  cursor: pointer;
}
.stadata{
    padding: 20px 0;
}
.mg30{
  margin-top: 30px;
  margin-bottom: 10px;
}
.condiv{
    margin-top: 20px;
    background-color: white;
    min-height: calc(100vh - 160px);
    padding:30px 20px ;
    border-radius: 8px;
}
.br{
    // width: 268px;
    // height: 154px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
}
.fr{
    // position: fixed;
    float: right !important;
}
.ml20{
    margin-left: 20px;
}
.flex{
    display: flex;
    align-items: center;
}
.headdiv{
    justify-content: space-between;
    position: relative;
    width: 100%;
}
</style>