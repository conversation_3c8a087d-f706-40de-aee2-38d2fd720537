<template>
  <div class="upload-container">
    <el-upload
      ref="rebateUpload"
      :class="[disabledInput ? 'upLoadShowNone' : '']"
      class="upload-demo el-upload1"
      :accept="accept"
      :action="UploadUrl()"
      :multiple="multiple"
      :on-success="handleSuccess"
      :on-preview="handlePictureCardPreview"
      :disabled="disabledInput"
      :show-file-list="false"
      :file-list="fileList"
      :auto-upload="false"
      list-type=""
      :on-change="handleChange"
    >
      <el-button :size="size" :type="type" icon="el-icon-upload" :plain="plain">
        {{ btnMSg }}
      </el-button>
      <div slot="tip" class="el-upload__tip">
        {{ itemDescribe }}
      </div>
    </el-upload>
    <ul class="el-upload-list el-upload-list--text">
      <template v-if="fileList.length > 0">
        <li
          style="width: 500px"
          v-for="(file, index) in fileList"
          :key="index"
          tabindex="0"
          class="el-upload-list__item is-success"
        >
          <a class="el-upload-list__item-name fileName"
            ><i class="el-icon-document"></i>{{ file.name }}
          </a>
          <label class="iconposti el-upload-list__item-status-label"
            ><i
              v-bind:class="{
                'el-icon-upload-success': file.status === 'success',
                'el-icon-circle-check': file.status === 'success',
              }"
            ></i>
          </label>
          <i
            class="iconposti el-icon-close"
            @click="handleRemoveClick(file, fileList)"
          ></i>
          <i class="el-icon-close-tip"></i>
          <el-progress
            v-if="file.status === 'uploading'"
            :percentage="parsePercentage(file.percentage)"
          >
          </el-progress>
          <span
            v-if="file.status !== 'uploading'"
            class="yulanbutton"
            @click="yulanclick(file)"
            >预览</span
          >
        </li>
      </template>
    </ul>
  </div>
</template>

<script>
Array.prototype.remove = function (val) {
  var index = this.indexOf(val)
  if (index > -1) {
    this.splice(index, 1)
  }
}
function arrayUnique2(arr, name) {
  var hash = {}
  return arr.reduce(function (item, next) {
    hash[next[name]] ? '' : (hash[next[name]] = true && item.push(next))
    return item
  }, [])
}
import { Message } from 'element-ui'
import { deleteFileById } from '../../../../api/index'

export default {
  name: 'UploadFile',
  props: {
    multiple: {
      type: Boolean,
      default: true,
    },
    disabledInput: {
      type: Boolean,
      default: false,
    },
    fileList: {
      type: Array,
      default: function () {
        return []
      },
    },
    limit: {
      type: Number,
      default: 1,
    },
    accept: {
      type: String,
      default: '',
    },
    itemDescribe: { type: String, default: '' },
    size: {
      type: String,
      default: 'small',
    },
    type: {
      type: String,
      default: 'primary',
    },
    placeholder: {
      type: String,
      default: '文件大小不得超过100M，仅支持上传PDF,最多上传一个',
    },
    btnMSg: {
      type: String,
      default: '点击上传',
    },
    plain: {
      type: Boolean,
      default: false,
    },
    itemType: {
      type: Number,
      default: -1,
    },
    itemLimitNumber: {
      type: Number,
      default: 1,
    },
  },
  created() {},
  data() {
    return {
      dialogVisibleImg: false,
      ImgName: '',
      dialogImageUrl: '',
      arr: [],
      num: 0,
      timer: null,
    }
  },
  computed: {},
  methods: {
    parsePercentage(val) {
      return parseInt(val, 10)
    },
    yulanclick(file) {
      this.$emit('fatherMethod', file)
    },
    async handleRemoveClick(file, fileList) {
      if (file.id) {
        // let res = await deleteFileById(file.id)
        // if (res.status != 0) return this.$message.error(res.msg)
        fileList.remove(file)
        this.$emit('update:fileList', fileList)
      } else {
        fileList.remove(file)
        this.$emit('update:fileList', fileList)
      }
    },
    //上传路径
    UploadUrl: function () {
      return `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`
    },
    handleSuccess(res, file, fileList) {
      this.$emit('update:fileList', fileList)
    },
    debounce(fn, delay) {
      console.log('-------------------')
      //设置time为定时器
      var time = null
      //闭包原理，返回一个函数
      return function (e) {
        console.log(3333333)
        //如果定时器存在则清空定时器
        if (time) {
          clearTimeout(time)
        }
        //设置定时器，规定时间后执行真实要执行的函数
        time = setTimeout(() => {
          console.log('===============')
          //此箭头函数里的this指向btn这个按钮
          fn.call(this, arguments) //改变真实要执行函数的this指向，原submit函数里面的this指向window
        }, delay)
      }
    },
    handleChange(file, fileList) {
       var suffix  = file.name.substring(file.name.lastIndexOf('.'))
       if(this.accept.indexOf(suffix) == -1){
          this.$message.error('请上传符合格式的文件')
          this.$refs.rebateUpload.handleRemove(file)
          return
       }
      if (!this.timer) {
        this.timer = setTimeout(() => {
          console.log(fileList.length, this.itemLimitNumber)
          if (fileList.length > this.itemLimitNumber) {
            this.$message.error('文件超出个数限制,请先删除后在上传')
            this.timer = null
            var i = fileList.length
            while (i--) {
              if (fileList[i].status == 'ready') {
                fileList.splice(i, 1)
              }
            }
            clearTimeout(this.timer)
            return
          }
          this.$emit('update:fileList', fileList)
          this.$refs.rebateUpload.submit()
          clearTimeout(this.timer)
          this.timer = null
        }, 0)
      }
    },
    handlePictureCardPreview(file) {
      let contentType = ''
      if (file.contentType) {
        contentType = file.contentType.split('/')[0]
      } else {
        contentType = file.response.body.contentType.split('/')[0]
      }

      if (contentType == 'image') {
        this.dialogImageUrl = file.url
        this.dialogVisibleImg = true
        this.ImgName = file.name
      } else {
        window.open(file.url)
      }
    },
  },
}
</script>
<style>
.el-upload1 .el-upload {
  text-align: left !important;
}
</style>

<style  scoped>
.el-upload-list__item {
  margin-top: 8px !important;
  height: 20px;
}
.el-upload__tip {
  margin-top: 0;
  line-height: 20px;
}
.yulanbutton {
  position: absolute;
  right: 5px;
  color: #0064b0;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  cursor: pointer;
}

.fileName {
  display: inline-block;
  width: 374px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.iconposti {
  right: 100px !important;
}
</style>
