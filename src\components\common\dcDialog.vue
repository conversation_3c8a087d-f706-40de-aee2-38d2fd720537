<template>
  <el-dialog  class="dialog" center :visible.sync="tDialogVisible" :width="width" @open="open"
    @closed="dialogClose" :append-to-body="appendToBody" :fullscreen="fullscreen" :top='top'
    :modal='modal' @before-close='beforeClose'
    :close-on-click-modal='closeOnClickModel' @close="close">
      <div  slot="title" class="postion" >
        <img class="tan" :src="imgUrl" alt="">
        <span class="titlecss">{{ title }}</span>
       </div>
    <slot></slot>
    <span slot="footer" v-if="showCancel || showSubmit" class="dialog-footer">
      <el-button @click="tDialogVisible = false" v-if="showCancel">取 消</el-button>
      <el-button :class="computedCss" type="danger" @click="submit" v-if="showSubmit" v-dbClick>{{ couputedText }}</el-button>
    </span>
  </el-dialog>

</template>

<script>
export default {
  data() {
    return {
      tDialogVisible: this.dialogVisible,
      cssData:{
        1:'deletecss',
        2:'yellowcss',
        3:''
      },
      textData:{
        1:'删除',
        2:"确定",
        3:'确定'
      },
      imgData:{
        1:require('../../assets/tan.png'),
        2:require('../../assets/tips.png'),
        3:require('../../assets/tips.png'),
      },
    };
  },
  created(){
  },
  computed: {
    computedCss: function () {
      return this.cssData[this.iType]
    },
    couputedText:function () {
      return this.textData[this.iType]
    },
    imgUrl:function(){
      return this.imgData[this.iType]
    },
  },
  props: {
    iType:{
        default:1,
    },
    dialogVisible: {
      default: false,
    },
    width: {
      default: '33%'
    },
    title: {
      default: '确定删除么?'
    },
    showCancel: {
      default: true
    },
    showSubmit: {
      default: true
    },
    appendToBody: {
      default: true
    },
    fullscreen: {
      default: false
    },
    top: {
      default: '30vh'
    },
    modal: {
      default: true
    },
    closeOnClickModel: {
      default: true
    }
  },
  watch: {
    dialogVisible: function (n) {
      this.tDialogVisible = n;
    },

    tDialogVisible(n){
      this.$emit('update:dialogVisible',n)
    }
  },
  methods: {
    open() {
      this.$emit('open')
    },
    dialogClose() {
      this.$emit('closed')
    },
    beforeClose() {
      this.$emit('beforeClose')
    },
    close(){
      this.$emit('close')
    },
    submit(){
      this.$emit('submit')
    }
  },
};
</script>

<style lang='scss' scoped>
.dialog-footer .el-button+.el-button{
  margin-left: 32px;
}
.yellowcss{
  background: #FF8D1A;
  border:1px solid #FF8D1A;
}
.tan{
  display: inline-block;
  vertical-align: top;
  position: relative;
  top: 3px;
}
.titlecss{
  margin-left: 3px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.dialog {
  z-index: 3000 !important;
}
.dialog  /deep/ .el-dialog__header{
    border-bottom: none;
}
.dialog  /deep/.el-dialog{
  height: 294px !important;
}
</style>
