<template>
  <div class="mainbg">
      <zongAddmodelCom :typeModel="typeModel"></zongAddmodelCom>
  </div>
</template>
<script>
import zongAddmodelCom from './addmodel.vue';
  export default {
      components: {
        zongAddmodelCom,
      },
      data() {
          return {
            typeModel:'3'
          }
      },
      created() {
      },
      mounted() {
         
      },
      methods: {
   
      }
  }
</script>
<style scoped>
</style>