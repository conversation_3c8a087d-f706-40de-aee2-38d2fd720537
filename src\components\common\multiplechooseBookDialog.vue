<template>
    <el-dialog :before-close="handleClose" class="unitdialog" title="选择教材" top="80px" :visible.sync="dialogTableVisible"
        width="70%" center>
        <el-form class="unitfcss" :inline="true">
            <el-form-item label="教材名称：">
                <el-input clearable="" class="definput" v-model="pageBean.name" placeholder="请输入教材名称"></el-input>
            </el-form-item>
            <el-form-item label="">
                <el-button class="defaultbtn ml20" icon="el-icon-search" type="primary"
                    @click="handleClick">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table class="mytable" :data="dataList" :height="478" v-loading="isLoading">
            <el-table-column class-name="column_blue" property="name" label="教材名称" align="center"
                min-width="200px"></el-table-column>
            <el-table-column property="isbn" label="ISBN" align="center" min-width="200px"></el-table-column>
            <el-table-column property="platformName" label="出版社" align="center" min-width="200px"></el-table-column>
            <el-table-column property="publicationRevisionTime" label="出版时间" align="center"
                width="150px"></el-table-column>
            <el-table-column property="author" label="作者" align="center" width="150px">
            </el-table-column>
            <el-table-column property="price" label="价格（元）" align="center" width="150px">
            </el-table-column>
            <el-table-column property="edit" label="操作" align="center" fixed="right">
                <template slot-scope="scope">
                    <div class="rbtn deffont" v-show="scope.row.isSelect" @click="chooseAction(scope.row,false)">取消选择
                    </div>
                    <div class="bbtn deffont" v-show="scope.row.isSelect == 0" @click="chooseAction(scope.row,true)">选择
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="center">
            <el-button class="defaultbtn" type="primary" @click="submitAction">提交</el-button>
        </div>
        <page :currentPage="pageBean.pageNum" :pageSize="pageBean.pageSize" :total="total"
            @updatePageNum="handleCurrentChange"></page>
    </el-dialog>
</template>

<script>

    import page from './page.vue';
    import { querySelectMaterialVo } from "@/api/clientmanagement/customer";

    export default {
        components: {
            page
        },
        data() {
            return {
                isLoading: false,
                dataList: [],
                selectedBooks: [],
                selectedBookIds: [],
                pageBean: {
                    name: "",
                    pageNum: 1,
                    pageSize: 8,
                },
                total: 0,
                tempData: {},
                chanceData: {}
            }
        },
        props: {
            visible: {
                type: Boolean,
                default: false
            },
        },
        computed: {
            dialogTableVisible: {
                get() {
                    return this.visible;
                },
                set(val) {
                    this.$emit('updateVisible', val);
                },
            }
        },
        methods: {
            setSelectedBooks(books) {
                this.selectedBookIds = books;
                console.log('sdssssss',this.selectedBookIds)
                this.dataList.forEach(item => {
                    this.selectedBookIds.forEach(element => {
                        if (item.id === element.goodsId) {
                            this.$set(item, 'isSelect',true);
                        }else{
                            this.$set(item, 'isSelect',false);
                        }
                    });
                    
                });
            },
            handleClose() {
                this.pageBean.pageNum = 1
                this.pageBean.name = ''
                this.selectedBooks = []
                this.dialogTableVisible = false;
            },
            loadBookData(params) {
                if (params) {
                    this.tempData = params;
                } else {
                    params = this.tempData;
                }
                this.isLoading = true;
                let sData = { ...params, ...this.pageBean };
                querySelectMaterialVo(sData).then(res => {
                    this.isLoading = false;
                    if (res.status == 0) {
                        this.dataList = res.data;
                        this.total = res.page.total;
                        this.dataList.forEach(item => {
                            this.$set(item, 'isSelect',false);
                            this.selectedBookIds.forEach(element => {
                                if (item.id === element.goodsId && !item.isSelect) {
                                    this.$set(item, 'isSelect',true);
                                }
                            });
                        });
                    } else {
                        this.msgError(res.msg);
                    }
                }).catch((err) => {
                    this.isLoading = false;
                });
            },
            selected(dataList) {
                dataList.forEach(item => {
                    if (item.id === this.chanceData.id) {
                        this.$set(item, 'isSelect', true)
                    }
                })
            },
            handleClick() {
                this.pageBean.pageNum = 1;
                this.loadBookData()
            },
            submitAction() {
                console.log('sssssssssssss=======', this.selectedBookIds);
                this.$emit('updateData', this.selectedBookIds);
                this.dialogTableVisible = false;
                this.selectedBookIds = undefined;
            },
            handleCurrentChange(page) {
                this.pageBean.pageNum = page;
                this.loadBookData()
            },
            chooseAction(val, type) {
                if (this.selectedBookIds.length>=100 && type) {
                    this.$message.error('最多选择100种教材')
                    return
                }
                val.isSelect = type;
                if (type) {
                    val.goodsId = val.id;
                    this.selectedBookIds.push(val);
                } else {
                    this.selectedBookIds = this.selectedBookIds.filter(item => item.goodsId !== val.id);
                }
            },
        }
    }
</script>
<style scoped>
    .center {
        margin-top: 50px;
        text-align: center;
    }

    .bbtn {
        color: #4285F4;
        cursor: pointer;
    }

    .rbtn {
        color: #F45961;
        cursor: pointer;
    }

    .unitfcss {
        line-height: 34px;
    }

    .unitfcss /deep/.el-form-item__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #333333;
    }

    .unitfcss /deep/.el-form-item {
        margin-bottom: 20px;
        /* padding-right: 60px; */
    }

    .ml20 {
        margin-left: 20px;
    }

    .unitdialog /deep/.el-dialog__body {
        padding: 20px;
        padding-top: 0px;
    }

    .unitdialog /deep/.el-dialog__header {
        border: none;
    }
</style>
<style scoped>
    .tabscss /deep/.el-tabs__item {
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        height: 40px;
        width: 240px !important;
        text-align: center;
        line-height: 40px;
        /* padding: 0 60px; */
    }
</style>