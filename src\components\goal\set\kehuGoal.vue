<template>
  <div>
    <div class="listcss" :gutter="12">
        <dataitem class="itemcss" :span="6" v-for="(item,index) in list" :key="index" :item="item"></dataitem>
    </div>
    <div :key="index" v-for="(item,index) in tableDataAll">
      <el-table
      class="mytable mt30"
      :data="item"
      :span-method="objectSpanMethod"
      border
      style="width: 100%;">
      <el-table-column
        prop="unitName"
        label="单位"
        width="180"
        align="center">
      </el-table-column>
      <el-table-column
        prop="name"
        label="目标维度"
        align="center">
      </el-table-column>
      <el-table-column
        prop="goalNum"
        label="目标"
        align="center">
      </el-table-column>
    </el-table>
    </div>
  </div>
</template>

<script>
import dataitem from '../show/components/dataitem.vue';
export default {
  components:{
    dataitem
  },
  props:{
    kehuObj:{
      type:Object,
      default:()=>({})
    }
  },
  data(){
    return{
      list:[
        {id:1,name:"信息化业绩",goal:0,finishGoal:0,icon:require('../../../assets/img5.png'),unit:"万元",color:"#F46D40"},
        {id:2,name:"教材业绩",goal:0,finishGoal:0,icon:require('../../../assets/img3.png'),unit:"万元",color:"#E85D5D"},
        {id:3,name:"合同数量",goal:0,finishGoal:0,icon:require('../../../assets/img/hetong_icon.png'),unit:"个",color:"#4A8BF6"},
        {id:4,name:"新增拜访与跟进",goal:0,finishGoal:0,icon:require('../../../assets/img2.png'),unit:"次",color:"#4A8BF6"},
      ],
      tableData: [],
      numObj:{
          0:{
            name:'信息化业绩(万元)',
            key:'informationAmount'
          },
          1:{
            name:'教材业绩(万元)',
            key:'teachingMaterialAmount'
          },
          2:{
            name:'合同数量(个)',
            key:'totalContract'
          },
          3:{
            name:'新增拜访与跟进(次)',
            key:'totalVisit'
          },
      },
      tableDataAll:[],
    }
  },
  methods:{
    handleData(){
        let goalUnitVo = this.kehuObj.goalUnitVo

        if(goalUnitVo && Object.keys(goalUnitVo).length>0){
              this.list[0].goal = goalUnitVo.goalInformationAmount
              this.list[0].finishGoal = goalUnitVo.achieveInformationAmount

              this.list[1].goal = goalUnitVo.goalTeachingMaterialAmount
              this.list[1].finishGoal = goalUnitVo.achieveTeachingMaterialAmount

              this.list[2].goal = goalUnitVo.goalTotalContract
              this.list[2].finishGoal = goalUnitVo.achieveTotalContract

              this.list[3].goal = goalUnitVo.goalTotalVisit
              this.list[3].finishGoal = goalUnitVo.achieveTotalVisit
        }else{
          this.list.forEach(item=>{
            item.goal = 0
            item.finishGoal = 0
          })
        }
        let personalGoalsUnitList = this.kehuObj.personalGoalsUnitList
        if(personalGoalsUnitList && personalGoalsUnitList.length>0){
          this.tableDataAll = []
          personalGoalsUnitList.forEach(item=>{
              let arr = new Array(4).fill({})
              let newArr = arr.map((item1,index)=>{
                return {
                    id:item.unitId,
                    name:this.numObj[index].name,
                    unitName:item.unitName,
                    goalNum:item[this.numObj[index].key],
                }
              })
              this.tableDataAll.push(newArr)
          })
        }else{
          this.tableDataAll = []
        }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
          if (columnIndex === 0 && rowIndex === 0) {
            return {
              rowspan: 4,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      }
    }
}
</script>
<style scoped>
.mt30{
  margin-top: 20px !important;
}
.itemcss{
  width: 16%;
  min-width: 200px !important;
  margin-right: 12px;
}
.itemcss:last-child{
  margin-right: 0px !important;
}
.listcss{
  display: flex;
  align-items: center;
  width: 100%;
  overflow-x: scroll;
  padding-bottom: 10px;
}
</style>
<style>

</style>