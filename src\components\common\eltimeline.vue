<template>
  <div>
    <el-timeline class="timeline">
      <el-timeline-item
        :color="activity.color || '#409EFF'"
        v-for="(activity, index) in timeList"
      >
        <div>
          <p class="flexp">
            <span>{{ activity.approverName }}</span>
            <span
              class="checkstatus textch"
              :class="{
                'c-fail': activity.status == 4,
                'c-success': activity.status == 3,
                'c-wait': activity.status == 1,
              }"
              >{{ statusToName[activity.status] }}</span
            >
            <span class="checkTime">{{
              activity.status == 1 ? '--' : activity.approvalTtime
            }}</span>
          </p>
          <p class="flexp mtpx20" v-if="activity.status == 4">
            <span class="zhanspan"></span>
            <span class="textch">驳回理由</span>
            <span class="checkTime">{{ activity.remark }}</span>
          </p>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>
<script>
import { queryApproveInfo } from '@/api/contract/index'
export default {
  props: {
    businessId: {
      type: String,
      default: '',
    },
    status:{
      type:Number,
      default:null
    },
  },
  data() {
    return {
      timeList: [],
      statusToName: {
        1: '待审核',
        3: '审核通过',
        4: '驳回',
      },
    }
  },
  created() {
    this.queryApproveInfoApi()
  },
  methods: {
    queryApproveInfoApi() {
      queryApproveInfo({ businessId: this.businessId,status:this.status }).then((res) => {
        this.timeList = res.data
      })
    },
  },
}
</script>
<style lang='scss'>
.c-fail {
  color: #f56c6c;
}
.c-success {
  color: #67c23a;
}
.c-wait {
  color: #409eff;
}
.timeline {
  margin-top: 5px;
  .mtpx20 {
    margin-top: 12px;
  }
  .checkTime {
    display: inline-block;
    flex: 1;
  }
  .textch {
    width: 60px;
    display: inline-block;
  }
  .checkTime {
    margin-left: 40px;
  }
  .zhanspan {
    width: 81px;
    display: inline-block;
  }
  .flexp {
    display: flex;
  }
  .el-timeline-item__tail {
    border-left: 2px solid #409eff;
  }
  .checkstatus {
    margin-left: 40px;
  }
}
</style>
