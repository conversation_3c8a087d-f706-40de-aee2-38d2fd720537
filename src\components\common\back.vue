<template>
  <div>
    <span class="cursor" @click="goback">  
      <img  class="backimg" src="../../assets/back.png" alt="">
      <span class="backtext"><slot></slot></span>
    </span>
  </div>
</template>

<script>
  export default {
    props: {
      type: {
        type: Number,
      }
    },
      methods:{
        goback(){
              if(!this.type){
                this.$router.go(-1)
              }else{
                    this.$emit('goback')
              }
        }
      }
  }
</script>

<style lang="scss" scoped>
.cursor{
  cursor: pointer;
}
.backimg{
  display: inline-block;
  vertical-align: top;
  position: relative;
  top: 1px;
}
.backtext{
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

</style>