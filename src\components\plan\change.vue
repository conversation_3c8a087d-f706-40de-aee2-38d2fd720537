<template>
    <div class="bgdiv">
      <back>变更{{types[form.type].name}}</back>
      <div class="maindiv">
        <div class="owntop w60">
          <div class="personalplan">
            <textBorder>个人{{types[form.type].label}}设置</textBorder>
            <el-form class="mt elform myform" :label-position="labelPosition"   :model="formLabelAlign">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="跟进拜访：" label-width="100px">
                    <el-input  placeholder="请输入拜访次数" class="definput" v-model="formLabelAlign.newVisit" type="number"><template slot="append">次</template></el-input>
                  </el-form-item>
                  <el-form-item label="业绩：" label-width="100px">
                    <el-input  placeholder="请输入金额" class="definput"  v-model="formLabelAlign.contractAmount" type="number"> <template slot="append">万元</template></el-input>
                  </el-form-item>
                  <el-form-item label="合同新增：" label-width="100px">
                    <el-input placeholder="请输入新增合同个数" class="definput" v-model="formLabelAlign.newContract" type="number"><template slot="append">个</template></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客户新增：" label-width="120px">
                    <el-input placeholder="请输入新增客户数量" class="definput"  v-model="formLabelAlign.newCustomers" type="number"> <template slot="append">个</template></el-input>
                  </el-form-item>
                  <el-form-item label="回款：" label-width="120px">
                    <el-input placeholder="请输入金额" class="definput" v-model="formLabelAlign.contractReturnAmount" type="number"><template slot="append">万元</template></el-input>
                  </el-form-item>
                  <el-form-item label="样书发放：" label-width="120px">
                    <el-input placeholder="请输入样书发放次数" class="definput" v-model="formLabelAlign.materialDeliverNum" type="number"><template slot="append">次</template></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="unitplan" v-if="form.type == '1'">
            <div class="rela">
              <textBorder>单位年度目标设置</textBorder>
            </div>
            <div class="borderdi">
              <div class="bposi">
                <p class="zhu">注：此处目标为针对某院校或单位设置的年度业绩与拜访目标</p>
                <el-button
                  class="defaultbtn  asbu"
                  icon="el-icon-plus"
                  type="primary"
                  @click="chooseunit"
                  >选择单位</el-button
                >
              </div>
              <div class="tablebox">
                <el-table
                  class="mytable"
                  v-for="(item, index) in unitGoalList"
                  :key="index"
                  :data="item"
                  :span-method="objectSpanMethod"
                  border
                >
                  <el-table-column prop="unitName" label="学校">
                    <template slot-scope="scope">
                      <div>
                        {{ scope.row.unitName }}
                        <img
                          class="deleteimg"
                          src="../../assets/delete.png"
                          alt=""
                          @click="deleteUnitGoal(scope.row)"
                        />
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="unitgoalTypeName"
                    label="目标维度"
                  ></el-table-column>
                  <el-table-column prop="goalNum" label="目标">
                    <template v-slot="{ row }">
                      <el-input
                        size="normal"
                        v-model="row.goalNum"
                        type="number"
                        clearable
                      ></el-input>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
          <addmodelcom ref="modelCom" @headdenForm="headdenForm" ></addmodelcom>
          <div>
            <div class="pflex">
              <ul class="uflex">
                <span class="pd10">抄送人：</span>
                <li v-for="(item,index) in peopleList" :key="index" class="pli">
                    <img @click="closeTag(item.id)" src="@/assets/close.png" alt="" class="closeimg">
                    <img v-if="item.logo" class="pimg" :src="item.logo" alt="">
                    <div class="noimg" v-else>
                      {{item.name}}
                    </div>
                    <p class="pname">{{item.name}}</p>
                </li>
                <li class="adddi" @click="clickXuan">
                  <i class="el-icon-plus iadd"></i>
                </li>
              </ul>
            </div>
          </div>
          <div class="submitmask">
            <el-button
              class="defaultbtn"
              type="primary"
              @click="saveGoalAction"
              v-dbClick
              >保存</el-button
            >
          </div>
        </div>
        <div class="rightdiv w40" >
           <div class="reviewmask">
            <planreview v-if="viewData.length>0"  @closereview="closereview" :viewData="viewData" :plantype="form.type" viewtype="change">
            </planreview>
           </div>
        </div>
      </div>
      <unitDialog
        ref="unitdialog"
        :visible.sync="unitDialogVisible"
        className="PersonalGoalsController"
        @updateVisible="updateVisible"
        @updateUnit="updateUnit">
      </unitDialog>
      <systemDialog
      ref="systemdialog"
      name="选择抄送人"
      :multipleNum="0"
      :visible.sync="dialogSystemVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
    </div>
  </template>

  <script>
  import textBorder from '@/components/common/textBorder.vue';
  import unitDialog from '@/components/common/unitDialog.vue';
  import systemDialog from "@/components/common/systemDialog.vue";
  import back from '@/components/common/back.vue';
  import planreview from '@/components/common/planreview.vue';
  import {deepClone,removeById} from '@/utils/tools.js'
  import { updatepersonalgoals,personalgoalsInfo,personalgoalsunitDelete,jiHuaInfo, } from "@/api/goal";

  import addmodelcom from "@/components/zongjie/temmodel/addmodelcom.vue";
  export default {
    components:{
      back,
      textBorder,
      unitDialog,
      planreview,
      addmodelcom,
      systemDialog
    },
    data(){
      return{
        isShow:false,
        isLoading:false,
        dialogPlanVisible:false,
        year:new Date(this.$route.query.time).getFullYear(),
        tableData:[],
        fileList:[],
        planTime:'',
        types:{
          "1":{
            name:"年计划",
            label:'年度计划',
            type:'year',
            format:'yyyy'
          },
          "2":{
            name:"月计划",
            label:"月度计划",
            type:"month",
            format:'yyyy-M'
          },
          "3":{
            name:"周计划",
            label:"周计划",
            type:'week',
            format:'yyyy 第 W 周'
          }
        },
        form:{
          type: this.$route.query.type +'',
          year:"",
          month:"",
          week:"",
        },
        reviewType:'',
        goalDetailList: [{
            goalType: 1,
            goalTypeName: '新增客户',
            goalTypeKey:'newCustomers',
            sum:0,
            year:this.year,
          }, {
            goalType: 2,
            goalTypeName: '新增拜访',
            goalTypeKey:'newVisit',
            sum:0,
            year:this.year,
          }, {
            goalType: 3,
            goalTypeName: '新增机会',
            goalTypeKey:'newOpportunity',
            sum:0,
            year:this.year,
          }, {
            goalType: 4,
            goalTypeName: '新增合同',
            goalTypeKey:'newContract',
            sum:0,
            year:this.year,
          }, {
            goalType: 5,
            goalTypeName: '合同金额（万元）',
            goalTypeKey:'contractAmount',
            sum:0,
            year:this.year,
          },
        {
          goalType: 6,
          goalTypeName: '回款金额（万元）',
          goalTypeKey:'contractReturnAmount',
          sum:0,
          year:this.year,

        }],
        keyDict:{
          1:'newCustomers',
          2:'newVisit',
          3:'newOpportunity',
          4:'newContract',
          5:'contractAmount',
          6:'contractReturnAmount',
        },
        nameDict:{
          1:'新增客户',
          2:'新增拜访',
          3:'新增机会',
          4:'新增合同',
          5:'合同金额（万元）',
          6:'回款金额（万元）'
        },
        options:[],
        labelPosition: 'right',
        formLabelAlign: {
          newCustomers: null,
          newContract: null,
          newVisit: null,
          materialDeliverNum: null,
          contractAmount: null,
          contractReturnAmount: null,
          year: "",
          personalGoalsUnitList: [],
          week:'',
          month:'',
          copyPersons:[],
        },
        unitGoalItem:[
          {
            unitId:'',
            unitName:'',
            unitgoalType:'informationAmount',
            unitgoalTypeName:'信息化业绩（万元）',
            goalNum:'',
            year:'',
          },{
            unitId:'',
            unitName:'',
            unitgoalType:'teachingMaterialAmount',
            unitgoalTypeName:'教材业绩金额（万元）',
            goalNum:'',
            year:'',
          },{
            unitId:'',
            unitName:'',
            unitgoalType:'totalContract',
            unitgoalTypeName:'合同数量',
            goalNum:'',
            year:'',
          },{
            unitId:'',
            unitName:'',
            unitgoalType:'totalVisit',
            unitgoalTypeName:'拜访次数',
            goalNum:'',
            year:'',
          },
        ],
        keyNames:{
          informationAmount:"信息化业绩（万元）",
          teachingMaterialAmount:'教材业绩金额（万元）',
          totalContract:'合同数量',
          totalVisit:'拜访次数'
        },
        unitGoalItem2:[],
        unitGoalList: [],
        unitIds:[],
        unitDialogVisible:false,
        currentYear :'',
        viewData:[],
        peopleList:[],
        dialogSystemVisible:false,
      }
    },
    mounted: function() {
    },
    created(){
        if(this.$route.query.id){
              this.loadInfo()
              this.loadOldData()
        }
     },
      methods:{
        saveGoalAction() {
            if(this.formLabelAlign.templateItemList.length==0){
                this.$message({
                  type:"error",
                  message:'暂时没有模板,不能保存'
                })
                return
            }
            this.formLabelAlign.personalGoalsUnitList = this.getUnitData();
            this.formLabelAlign.copyPersons =  this.peopleList.length>0 ? this.peopleList.map(item =>item.id) :[]
            this.$refs.modelCom.submitForm();
        },
        headdenForm() {
            let formData = this.$refs.modelCom.getViewData();
            this.formLabelAlign.templateItemList = formData;
            if (this.formLabelAlign.id) {
              delete this.formLabelAlign.createTime
              this.formLabelAlign.isChange = 2
              this.update();
            }
        },
        closereview(){
          this.isShow = false;
          this.reviewType = ''
        },
        getUnitGoalList(array){
          this.unitGoalList = [];
          this.unitIds = [];
          var keys = ['informationAmount','teachingMaterialAmount','totalContract','totalVisit'];
          array.forEach((element,idx) => {
              var list = [];
              this.unitIds.push(element.unitId)
              keys.forEach(key => {
                var item = {
                  id:element.id,
                  index:idx,
                  unitId:element.unitId,
                  unitName:element.unitName,
                  year:element.year,
                  goalNum:element[key],
                  unitgoalType:key,
                  unitgoalTypeName:this.keyNames[key],
                };
                list.push(item);
              });
            this.unitGoalList.push(list);
          });
        },
        loadInfo() {
          jiHuaInfo({ id: this.$route.query.id,type:2 })
            .then(result => {
              this.formLabelAlign = result.data;
              this.templateItemVos = this.formLabelAlign.templateItemList
              this.$refs.modelCom.setViewList(this.templateItemVos);
              this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList);
              result.data.copyPersons.forEach(item =>{
                var data = {
                  id:item.userId,
                  name:item.name,
                  logo:item.logo
                }
                this.peopleList.push(data)
              });
              this.formLabelAlign.copyPersons = result.data.copyPersons.map(item =>item.userId)
            })
            .catch(err => {});
        },
        loadOldData(){
          jiHuaInfo({ id: this.$route.query.id,type:1})
            .then(result => {
              this.viewData = [result.data]
            })
            .catch(err => {});
        },
        update(){
          this.isLoading = true;
          updatepersonalgoals(this.formLabelAlign).then((result) => {
            this.isLoading = false;
            if (result.data) {
              this.$message({
                type:"success",
                message:'保存成功'
              })
              this.$router.go(-1)
            }else{
              this.$message({
                type:"error",
                message:result.msg
              })
            }
          }).catch((err) => {
            this.isLoading = false;
          });
        },
        getUnitData(){
          var list = [];
          var keys = ['informationAmount','teachingMaterialAmount','totalContract','totalVisit'];
          this.unitGoalList.forEach(item => {
              var obj = Object();
             item.forEach(unitItem => {
                obj.unitId = unitItem.unitId;
                obj.year = unitItem.year;
                if (keys.indexOf(unitItem.unitgoalType)>=0) {
                  obj[unitItem.unitgoalType] = unitItem.goalNum;
                }
             });
             list.push(obj);
          });
          return list;
        },
        chooseunit(){
            this.unitDialogVisible = true;
            var idsStr = this.unitIds.join(',');
            this.$refs.unitdialog.loadData(idsStr);
        },
        updateUnit(data){
          this.unitIds.push(data.id);
          var list = deepClone(this.unitGoalItem2)
          list.forEach(element => {
            element.unitId = data.id;
            element.unitName = data.unitName;
            element.year = this.year;
            element.goalNum = ''
          });
          this.unitGoalList.push(list);
        },
        updateVisible(val){
              this.unitDialogVisible = val;
          },
        deleteUnitGoal(data){
          if (data.id) {
            personalgoalsunitDelete({id:data.id}).then((result) => {
              if (result.data) {
                this.unitGoalList.splice(data.index,1);
                this.unitIds = this.unitIds.filter(item=>item!==data.unitId)
                this.$message({
                  type:'success',
                  message:result.msg
                })
              }else{
                this.$message({
                  type:'error',
                  message:result.msg
                })
              }
            }).catch((err) => {

            });
          }else{
            this.unitGoalList.splice(data.index,1);
            this.unitIds = this.unitIds.filter(item=>item!==data.unitId)
          }
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
          if (columnIndex === 0) {
            if (columnIndex === 0 && rowIndex === 0) {
              return {
                rowspan: 4,
                colspan: 1
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0
              };
            }
          }
        },
        closeTag(idtemId){
          this.peopleList = removeById(this.peopleList,idtemId)
        },
        clickXuan(){
          this.$refs.systemdialog.loadData();
            this.peopleList
              ? this.$refs.systemdialog.updateWorksId(this.peopleList)
              : this.$refs.systemdialog.updateWorksId([]);
          this.dialogSystemVisible = true;
        },
        updateSystemVisible(val){
          this.dialogSystemVisible = val
        },
        submitData(data){
          this.peopleList = data
          this.dialogSystemVisible = false;
        },
      }
  }
  </script>

  <style lang="scss" scoped>
  .pname{
    text-align: center;
  }
  .noimg{
    width: 48px;
    height: 48px;
    line-height: 48px;
    border-radius: 8px;
    background-color: #4285F4;
    text-align: center;
    color: #fff;
    font-size: 12px;
  }
.uflex{
    display: flex;
    flex-wrap: wrap;
  }
  .closeimg{
    position: absolute;
    right:-8px;
    top: -8px;
    cursor: pointer;
  }
  .pli{
    margin-right: 24px;
    margin-bottom: 24px;
    position: relative;
  }
  .pimg{
    width: 48px;
    height: 48px;
    border-radius: 8px 8px 8px 8px;

  }
  .pflex{
    display: flex;
    margin-top: 10px;
    flex-wrap: wrap;
  }
  .adddi{
    width: 44px;
    height: 44px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #D6D6D6;
    text-align: center;
    line-height: 44px;
    font-size: 18px;
    color: #D6D6D6;
    cursor: pointer;
  }
    .borderdi{
      border:1px solid #DCDFE6;
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
    }
  .bposi{
    position: relative;
    height: 34px;
    margin-bottom: 10px;
  }
  .mr{
    margin-left: 20px;
  }
  .upload-demo{
    margin-top: 20px;
  }
  .pd20{
    padding: 20px 0;
  }
  .tright{
    text-align: center;
  }

  .maindiv{
    display: flex;
    padding-top: 20px;
  }
  .rightdiv{
    width: 20%;
    margin-left: 20px;
    border-radius: 10px;
  }
  .w40{
    width:45%;
    background-color: white;
  }
  .submitmask{
    width: 100%;
    text-align: center;
    margin-top: 20px;
  }
  .textAlign /deep/.el-input__inner{
    text-align: center !important;
  }
  .flexcss{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .textAlignRight{
    text-align: right;
  }
  .definput{
    width: 100% !important;
  }
  .definput /deep/.el-input-group__append{
    background-color: #F6F7FB !important;
    color: #333333 !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
  }
  .rtextcss{
    height: 24px;;
    line-height: 24px;
    font-size: 12px;
    color: #999999;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
  }
  .owntable /deep/ td,  th {
    padding: 8px 0 !important;
  }
  .owntable /deep/ th{
    background-color: #F6F7FB !important;
  }
  .owntable /deep/ .cell{
    font-size: 14px !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400 !important;
    color: #333333 !important;
  }
  .mubiao{
    background: #fff;
    margin-top: 10px;
    padding: 24px 20px;
    min-height: 500px;
    box-sizing: border-box;
  }
  .inputcss{
    height: 36px !important;
  }
  .elform /deep/ .el-input__inner{
    line-height: 36px !important;
    height: 36px;
  }
  .deleteimg{
    position: absolute;
    right: 12px;
    bottom: 12px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  .tablebox{
    height: 337px;
    overflow-x: hidden;
    padding-right: 10px;
  }
  .asbu{
    position: absolute;
    right: 0;
    top: 0;
  }
  .rela{
    position: relative;
    margin-bottom: 24px;
  }
  .elform /deep/ .el-input-group__append{
      padding: 0 12px !important;
  }
  .mt{
    margin-top: 20px;
  }
  .elform{
    width: 100%;
  }
  .elform .el-form-item{
      margin-bottom: 20px !important;
      margin-right: 0px !important;
  }
  .bbtn{
    height: 48px;
    line-height: 48px;
    background-color: #DFEAFD;
    font-size: 16px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    letter-spacing: 1px;
    border-radius: 4px;
    cursor: pointer;
  }
  .bbtn:hover{
    background-color: #4285F4;
    color: white !important;
  }
  .bbtn + .bbtn{
    margin-top:10px;
  }
  .owntop{
    width:80%;
    background-color: white;
    border-radius: 10px;
    padding:20px;
  }
  .w60{
    width: 55%;
  }
      .personalplan{
        background: #fff;
        border-radius: 8px 8px 8px 8px;
        box-sizing: border-box;
        padding: 0px 0px 10px 0px;
      }
      .unitplan{
        flex:1;
        background: #fff;
        border-radius: 8px 8px 8px 8px;
        box-sizing: border-box;
      }
  </style>
