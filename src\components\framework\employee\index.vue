<template>
  <div class="mod-config">
    <el-row :gutter="20" class="el-rowone">
      <el-col :span="5" class="wcolor clrp letree">
        <div class="cflex cfd leall">
          <div class="clearfix tdiv ">
            <span class="zutext">企业组织架构</span>
          </div>
          <div class="top bgcolor">
            <div class="topTitle">员工</div>
            <div class="titleText">
              <div class="title" @click="handleStaffTypeClick(1)">所有员工</div>
              <div class="title" @click="handleStaffTypeClick(2)">新加入的员工</div>
              <div class="title" @click="handleStaffTypeClick(3)">禁用的员工</div>
            </div>
          </div>
          <el-divider></el-divider>
          <div class="topTitle">部门</div>
          <el-tree :default-expand-all="true" class="filter-tree  cfd cf1" :data="tagTypeData" :props="defaultProps"
            ref="tagTypeTree" :highlight-current="true" :expand-on-click-node="false" @node-click="getTreeNode">
          </el-tree>
        </div>
      </el-col>
      <el-col :span="19" class="cpding">
        <div class="tablecss wcolor" ref="configtypetable">
          <div class="page-header">
            <el-form size="small" ref="form" :model="pageBean" :inline="true">
              <el-form-item label="筛选角色：">
                <el-select clearable="" v-model="pageBean.roleId" placeholder="请选择">
                  <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="员工姓名：">
                <el-input v-model="pageBean.name" clearable placeholder="请输入员工姓名"></el-input>
              </el-form-item>
              <el-form-item label="">
                <el-button class="search btn defaultbtn" type="primary" icon="el-icon-search" @click="onSearch">搜索
                </el-button>
              </el-form-item>
              <el-form-item label="">
                <el-button class="search btn defaultbtn" v-isShow="'crm:controller:userinfodetail:synchronizeEmployees'" type="primary" @click="handleSynchronizeEmployees">同步员工</el-button>
              </el-form-item>
              <el-form-item label="">
                <el-button class="search btn defaultbtn" type="primary" @click="goMigration">数据迁移</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="全部" name="one"></el-tab-pane>
            </el-tabs>
            <el-table :height="tableHeight" class="mytable1" :data="dataList" v-loading="isLoading">
              <el-table-column prop="name" label="员工信息">
                <template slot-scope="scope">
                  <headuser :url="scope.row.logo" width="48" :username="scope.row.name"></headuser>
                  <span class="username">{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态">
                <template slot-scope="scope">
                  <span :class="scope.row.status == 1 ? 'status-normal' : 'status-disabled'">
                    {{ statusTextMap[scope.row.status] || '--' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="departmentName" label="部门">
              </el-table-column>
              <el-table-column prop="roleName" label="角色">
              </el-table-column>
              <el-table-column prop="edit" width="240" label="操作" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.status == 1 || scope.row.status == 2"
                    class="bBtn"
                    type="text"
                    @click="handleChangeStatus(scope.row)">
                    {{ scope.row.status == 1 ? '禁用' : '启用' }}
                  </el-button>
                  <el-button class="bBtn" type="text" @click="editRoleHandle(scope.row)">编辑角色</el-button>
                  <el-button class="bBtn" type="text" @click="migrateDepartmentHandle(scope.row)">迁移部门</el-button>
                  <!-- <el-button class="bBtn" type="text" v-isShow="'crm:controller:userinfodetail:delete'" @click="handleDelete(scope.row)">删除</el-button> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
          <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"></page>
        </div>
      </el-col>
    </el-row>
    <AddOrUpdateTagType v-if="addOrUpdateTagTypeVisible" ref="addOrUpdateTagType" @refreshTagTypeTree="userListData">
    </AddOrUpdateTagType>
    <EditRoleDialog v-if="editRoleDialogVisible" ref="editRoleDialog" @refreshTagTypeTree="userListData">
    </EditRoleDialog>
  </div>
</template>

<script>
  import AddOrUpdateTagType from '@/components/framework/employee/components/add-or-update-tag-type'
  import EditRoleDialog from '@/components/framework/employee/components/edit-role-dialog'
  import page from '../../common/page.vue';
  import { getRoleList } from '@/api/framework/roleposition'
  import { treeDepartmentList, userList, synchronizeUpdateDepartments, updateEnable, deleteUser, synchronizeEmployees } from '@/api/framework/user'
  import headuser from '@/components/common/headuser.vue'
  import { Loading, Notification, MessageBox } from 'element-ui';
  export default {
    data() {
      return {
        activeName: 'one',
        isLoading: false,
        pageBean: {
          roleId: '',
          name: '',
          pageNum: 1,
          pageSize: 15,
          departmentId: '',
          isNew: false,
          status: ''
        },
        dataList: [],
        total: 0,
        addOrUpdateTagTypeVisible: false,
        editRoleDialogVisible: false,
        defaultProps: {
          children: 'children',
          label: 'name',
        },
        tableHeight: window.innerHeight - 312,
        roleList: [],
        dimg: require('@/assets/touxiang.png'),
        tagTypeData: [],
        statusTextMap: {
          1: '正常',
          2: '禁用'
        },
        currentStaffType: 1
      }
    },
    components: {
      AddOrUpdateTagType,
      EditRoleDialog,
      page,
      headuser
    },
    methods: {
      userListData() {
        this.isLoading = true;
        userList(this.pageBean).then(res => {
          this.isLoading = false;
          if (res.status == 0) {
            this.dataList = res.data
            this.total = res.page.total
          }
        }).catch(() => {
          this.isLoading = false;
        })
      },
      treeDepartmentListData() {
        treeDepartmentList().then(res => {
          if (res.status == 0) {
            this.tagTypeData = res.data
          }
        })
      },
      getRoleListData() {
        getRoleList().then(res => {
          if (res.status === 0) {
            this.roleList = res.data
          }
        })
      },
      handleClick(tab, event) {
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.userListData()
      },
      onEdit(item) {
        this.addOrUpdateTagItemVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdateTagItem.init(
          )
        })
      },
      onSearch() {
        this.pageBean.pageNum = 1;
        this.userListData()
      },
      getTreeNode(data) {
        this.pageBean.departmentId = data.id
        this.userListData()
      },
      // 迁移部门
      migrateDepartmentHandle(row) {
        this.addOrUpdateTagTypeVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdateTagType.init(row.id)
          this.$refs.addOrUpdateTagType.isNewEmployee = row.isNew || false
        })
      },
      // 编辑角色
      editRoleHandle(row) {
        this.editRoleDialogVisible = true
        this.$nextTick(() => {
          this.$refs.editRoleDialog.init(row.id)
        })
      },
      handleDelete(row) {
        MessageBox.confirm('确认删除该员工？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteUser({userId: row.id}).then(res => {
            this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.userListData();
          })
        })
      },
      goMigration() {
        this.$router.push('/framework/employee/datamigration');
      },
      handleChangeStatus(row) {
        const targetStatus = row.status == 1 ? 2 : 1;
        const statusText = targetStatus == 1 ? '启用' : '禁用';
        
        MessageBox.confirm(`确认${statusText}该员工？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateEnable({id: row.id, status: targetStatus}).then(res => {
            this.$message({
              type: 'success',
              message: `${statusText}成功`
            });
            this.userListData();
          })
        })
      },
      handleStaffTypeClick(type) {
        this.currentStaffType = type;
        this.pageBean.pageNum = 1;
        
        this.pageBean.isNew = false;
        this.pageBean.status = '';
        
        if (type == 1) { 
        } else if (type == 2) { 
          this.pageBean.isNew = true;
        } else if (type == 3) {
          this.pageBean.status = 2;
        }
        
        this.userListData();
      },
      handleSynchronizeEmployees() {
        this.isLoading = true;
        synchronizeEmployees().then(res => {
          this.isLoading = false;
          this.$message({
            type: 'success',
            message: '同步员工成功'
          });
          this.userListData();
        })
      }
    },
    mounted() {
      this.$notify({
        title: '警告',
        message: '未设置部门和角色的员工无法正常操作',
        type: 'warning',
        offset: 120,
      });
    },
    created() {
      this.getRoleListData()
      this.treeDepartmentListData()
      this.userListData()
    },
  }
</script>

<style scoped lang="scss">
  .defaultbtn1 {
    padding: 9px 10px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
  }

  .username {
    clear: both;
    position: relative;
    top: 10px;
  }


  .avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .leall {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
  }

  .letree {
    display: flex;
  }

  .mytable1 {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    margin-bottom: 0px !important;
  }

  .mod-config {
    height: 100%;
  }

  .search {
    margin-left: 10px;
  }

  .tablecss {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  .clrp {
    padding-left: 0 !important;
    padding-right: 0 !important;
    min-width: 240px;
  }

  .cpding {
    padding-right: 0 !important;
    padding-left: 16px !important;
    display: flex;
    flex-direction: column;
  }

  .el-rowone {
    margin-left: 0 !important;
    margin-right: 0 !important;
    display: flex;
    height: 100%;
  }

  .wcolor {
    background: #fff;
  }

  .tdiv {
    line-height: 40px;
    height: 56px;
    box-sizing: border-box;
    border-bottom: 1px solid #F0F0F0;
    text-align: center;
  }

  .zutext {
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #999999;
  }

  .pcc {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    text-align: center;
    line-height: 24px;
  }

  .ml16 {
    margin-left: 16px;
  }

  .mr16 {
    margin-right: 16px;
  }

  .filter-tree {
    font-size: 14px;
    overflow-y: auto;
    overflow-x: auto;
    width: 100%;
    /deep/.el-tree-node__children {
      overflow: visible;
    }
  }

  .filter-tree,
  .grid-contenttable {
    overflow-y: scroll;
  }
  .titleText{
    margin-left: 10px;
  }
  .title{
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    line-height: 26px;
  }
  .title:hover{
    background-color: #F5F7FA;
    cursor: pointer;
  }
  .bgcolor{
    border-radius: 8px;
    padding: 10px;
  }
  .topTitle{
    font-weight: bold;
    margin-bottom: 10px;
  }
  .status-normal {
    color: #67C23A; 
  }
  
  .status-disabled {
    color: #F56C6C;  
  }
</style>