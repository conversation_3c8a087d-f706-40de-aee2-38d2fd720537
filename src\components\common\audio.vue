<template>
            <div class="newAudio">
                  <div class=" yuyindiv" @click="click" v-if="url">
                      <img v-show="btnStatus" class="donghua" src="../../assets/1.gif" alt="">
                      <img v-show="!btnStatus" class="donghua" src="../../assets/jindu.png" alt="">
                      <span class="yutext">{{item.soundList[0].fileDruation}}s</span>
                      <audio  ref="audio" :src="url"></audio>
                   </div>
                   <p v-else>暂无</p>
            </div>
</template>

<script>
  export default {
    props:{
      url:{
        type:String,
        default:''
      },
      item:{
        type:Object,
        default:()=>({})
      }
    },
    watch: {
      item: {
          handler(newVal, oldVal) {
            if(!newVal.isPlay){
                this.playEnd()
            }
              console.log(newVal, oldVal)
          },
        deep: true
      }
    },
    data(){
      return{
            btnStatus:false,
      }
    },
    created(){
    },
    methods:{
          click(){
					  this.btnStatus = !this.btnStatus
            this.play()
				  },
          playEnd(){
            const media = this.$refs.audio
            console.log(media)
            if(media){
              media.pause()
              media.currentTime = 0
              this.btnStatus = false
            }
          },
          play () {
            const media = this.$refs.audio
            if (this.item.isPlay) {
              media.pause()
              this.$dispatch('listItem','audioPlay',this.item,false)
            } else {
              media.play()
              this.$dispatch('listItem','audioPlay',this.item,true)
            }
          },
    }
  }
</script>

<style lang="scss" scoped>
.donghua{
  margin-left: 12px;
}
.newAudio{
  display: inline-block;
}
.yuyindiv{
  width: 175px;
  height: 30px;
  background: #DFEAFD;
  border-radius: 17px 17px 17px 17px;
  display: inline-block;
  line-height: 30px;
}
.yuyin{
  margin: 20px 0;
}
.yuplay{
  margin-left: 6px;
  margin-right: 5px;
}
.yutext{
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  float: right;
  margin-right: 10px;
}

</style>