<template>
  <div>
    <p class="cbtext clearfix">
      项目金额：<span class="yuan ">{{returnData.projectAmount}}万元</span>
      <span class="ml32">累计回款：</span>
      <span class="yuan">{{returnData.accumulatedAmount}}万元</span>
      <span class="ml32"> 剩余回款：</span>
     <span class="yuan">{{returnData.remainingAmount}}万元</span>
     <el-button :disabled="!form.isOperate" class="defaultbtn mt right" icon="el-icon-plus" type="primary" @click="addpay">添加</el-button>
    </p>
    <el-table  class="customertable mytable"  :data="tableData" style="width: 100%" v-loading="isLoading">
      <el-table-column
        prop="returnAmount"
        label="回款金额(万元)"
        >
      </el-table-column>
      <el-table-column
        prop="createByName"
        label="操作人"
        >
      </el-table-column>
      <el-table-column
        prop="departmentName"
        label="操作人部门"
        >
      </el-table-column>
      <el-table-column
        prop="returnTime"
        label="回款时间"
        >
      </el-table-column>
      <el-table-column
        width="160"
        prop="createTime"
        label="业务时间"
        >
      </el-table-column>
      <el-table-column
        width="100"
        prop="edit"
        label="操作">
        <template slot-scope="scope">
          <el-button :disabled="!scope.row.isOperate" class="bbtn"  type="text" @click="view(scope.row)"> 查看</el-button>
          <el-button :disabled="!scope.row.isOperate" class="rbtn"  type="text" @click="deleteAction(scope.row)"> 删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"></page>
    <payadd @loadData="loadData" :stageId="stageId" :show.sync="show"></payadd>
    <paymentDetailDrawerStage ref="huiRef" :visible.sync="detailVisible"  @updateVisible="updateDetailVisible"></paymentDetailDrawerStage>

    <dc-dialog :iType="dialogType" title="温馨提示" width="500px" :showCancel="isShowCancel" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
              <p class="pcc">确定要删除么?</p>
    </dc-dialog>
  </div>
</template>

<script>
import paymentDetailDrawerStage from '@/components/common/paymentDetailDrawerStage'
import {returnList,queryProjectReturnStatistics,returnDelete} from '@/api/stage/index'
import page from '@/components/common/page.vue';
  import payadd from './payadd.vue'
  export default {
    props:{
      form:{
        type:Object,
        default:()=>({})
      }
    },
       data(){
        return {
          detailVisible:false,
          isLoading:false,
          show:false,
          tableData:[],
          params:{},
          stageId:'',
          returnData:{},
          dialogType:1,
              isShowCancel:true,
              dialogVisible:false,
              itemId:'',
              total:0,
              pageBean: {
                pageNum: 1,
                pageSize: 10,
            },
      }
    },
    components:{
      payadd,
      paymentDetailDrawerStage,
      page
    },
    methods:{
      handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.rturnListData(this.stageId)
        },
      deleteAction(row){
        this.itemId = row.id
        this.dialogVisible = true
    },
      submitDialog(){
            let params = {
                id:this.itemId
            }
            returnDelete(params).then(res=>{
                    if(res.status == 0){
                      this.msgSuccess('删除成功')
                      this.returnListApi(this.params)
                      this.queryTaskCostData(this.params)
                      this.dialogVisible = false
                    }else{
                      this.msgError(res.msg)
                    }
            })
          },
      loadData(){
        this.returnListApi(this.params)
        this.queryTaskCostData(this.params)
      },
      updateDetailVisible(visible){
        this.detailVisible = visible;
      },
      addpay(){
        this.show = true
      },
      view(row){
        this.detailVisible = true;
        this.$refs.huiRef.loadData(row.id,1)
      },
      rturnListData(Id){
        this.params = {
          stageId:Id
        }
        this.stageId = Id
        this.pageBean = {...this.pageBean,...this.params}
        this.returnListApi(this.pageBean)
        this.queryTaskCostData(this.params)
      },
    returnListApi(params){
      returnList(params).then(res=>{
            if(res.status == 0){
                this.tableData  = res.data
                this.total = res.page.total
            }
      })
    },
    queryTaskCostData(params){
      queryProjectReturnStatistics(params).then(res=>{
            if(res.status == 0){
                this.returnData  = res.data
            }
      })
    }
    }
  }
</script>

<style lang="scss" scoped>
.ml32{
  margin-left: 32px;
}
.cbtext{
  margin-bottom: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.yuan{
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
</style>