import service from '@/utils/request.js'
export function list<PERSON>oban(params) {
	return service.request({
		method: 'get',
		url: '/crm/business/template/list',
		params
	});
}


export function deleteMoban(data) {
  return service.request({
    url: "/crm/business/template/delete",
    method: "post",
    data
  });
}

export function deletePeople(data) {
  return service.request({
    url: "/crm/business/template/deleteDispose",
    method: "post",
    data
  });
}




export function getMenu() {
	return service.request({
		method: 'get',
		url: `/goldcourse/manager/resource/list`,
	});
}

export function deleteFile(params) {
	return service.request({
		method: 'get',
		url: '/aliyun/oss/deleteFile',
		params
	})
}

export function queryTreeByUserId(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/companydepartment/queryTreeByUserId',
		params
	})
}

export function indexHe() {
	return service.request({
		method: 'post',
		url: '/crm/controller/homepage/index',
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
	})
}

export function hoemDetail(data) {
	return service.request({
		method: 'post',
		url: '/crm/controller/homepage/detail',
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
		data
	})
}

export function homeTaskList(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/task/list',
		params
	})
}


export function authCodeLogin(params) {
	return service.request({
		method: 'get',
		url: '/authCodeLogin',
		params
	})
}


export function queryHomeList(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/remind/queryHomeList',
		params
	})
}


export function remindSave(data) {
	return service.request({
		method: 'post',
		url: '/crm/controller/remind/save',
		data,
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
	})
}


export function remindInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/remind/info/${id}`
  });
}







export function saveTemplate(data) {
	return service.request({
		method: 'post',
		url: `/teaching/business/competmodel/save`,
		data
	});
}

export function updateTemplate(data) {
	return service.request({
		method: 'post',
		url: `/teaching/business/competmodel/update`,
		data
	});
}

export function TemplateDetail(id) {
	return service.request({
		method: 'get',
		url: `/teaching/business/competmodel/info/${id}`,
	});
}

export function detailId(id) {
	return service.request({
		method: 'get',
		url: `/teaching/business/competmodelitem/delete/${id}`,
	});
}