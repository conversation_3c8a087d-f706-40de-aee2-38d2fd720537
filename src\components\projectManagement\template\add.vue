<template>
  <div>
    <div>
        <back>新建模版</back>
    </div>
    <div class="mainbg">
        <el-form ref="addform" class="addfcss" :model="form" :rules="rules" label-width="118px">
            <textBorder>基础信息</textBorder>
            <div class="pt20  bbline">
                <el-row :gutter="0" type="flex" justify="start" >
                    <el-col :span="8">
                        <el-form-item  label="模板名称：" prop="name">
                            <el-input class="definput" clearable placeholder="请输入模板名称" v-model="form.name" ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
            <textBorder class="mt30 lh34 ">
                阶段管理 
                <el-button class="defaultbtn mt-10 right mb20" icon="el-icon-plus" type="primary" @click="addAction">新建阶段</el-button>
            </textBorder>
            <el-table  class="jhtable mytable" :data="form.projectStageList" style="width: 100%" v-loading="isLoading">
                <el-table-column
                    prop="xOrder"
                    label="序号"
                    align="center">
                    <template slot-scope="scope">
                        <div class="width50">
                              <el-input-number class="definput"  v-model="scope.row.xOrder" @change="changeSort(scope.row)" :controls="false" :min="1" ></el-input-number>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="name"
                    label="阶段名称"
                    align="center"
                    >
                </el-table-column>
                <el-table-column
                    prop="stageType"
                    label="阶段类型"
                    align="center"
                    >
                    <template slot-scope="scope">
                        <div>{{stageTypes[scope.row.stageType]}}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="edit"
                    width="300"
                    align="center"
                    label="操作">
                    <template slot-scope="scope">
                        <el-button class="rbtn "  type="text" @click="deleteAction(scope.row)"> 删除</el-button>
                    </template>
                    
                </el-table-column>
                <template slot="empty">
                    <nolist></nolist>
                </template>
            </el-table>
            <div class="pt20 btncenter">
                <!-- <el-form-item > -->
                    <el-button class="btn_h42 wid98" type="primary" @click="saveAction">保存</el-button>
                <!-- </el-form-item> -->
            </div>
        </el-form>
    </div>
    <addStageDialog 
    :visible.sync="dialogVisible"
    @updateVisible="updateVisible"
    @submitAction="submitAction"
    >
    </addStageDialog>
  </div>
</template>

<script>
import back from '../../common/back.vue';
import textBorder from '../../common/textBorder.vue';
import addStageDialog from './addStageDialog.vue';
import { addProjectstagetemplate ,updateProjectstagetemplate,projectstagetemplateInfo} from "@/api/project/template";
import nolist from '../../common/nolist.vue';
export default {
    components:{
        back,
        textBorder,
        addStageDialog,
        nolist
    },
    data(){
        return{
            dialogVisible:false,
            isLoading:false,
            stageTypes:{
                1:'一般阶段',
                2:'回款阶段'
            },
            rules:{
                name: [
                    { required: true, message: '请输入模版名称', trigger: 'blur' },
                    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
                ],
            },
            maxSort:0,
            form:{
                name:'',
                projectStageList:[]
            },
        }
    },
    created(){
        console.log(window.screen.width,this.isSmall)
        if (this.$route.query.id) {
            this.loadInfo();
        }
        
    },
    methods:{
        loadInfo(){
            projectstagetemplateInfo(this.$route.query.id).then((result) => {
                this.form.id = result.data.projectStageTemplateEntity.id;
                this.form.name = result.data.projectStageTemplateEntity.name;
                this.form.projectStageList = result.data.projectStageList;
                this.getMaxAndSort();
            }).catch((err) => {
                
            });
        },
        getMaxAndSort(){
            console.log('dataa===',this.form.projectStageList);
            this.form.projectStageList.sort((item1,item2) =>{
                return item1.xOrder - item2.xOrder
            })
            this.maxSort = this.form.projectStageList[this.form.projectStageList.length - 1].xOrder;
        },
        // 修改排序
        changeSort(){
            this.getMaxAndSort();
        },
        submitAction(data){
            console.log(data);
            var newdata = Object.assign({},data)
            newdata.yOrder = 1;
            newdata.xOrder = this.maxSort+1;
            this.maxSort = newdata.xOrder;
            this.form.projectStageList.push(newdata);
            this.dialogVisible = false;
        },
        // 删除
        deleteAction(data){
            const index = this.form.projectStageList.indexOf(data);
            this.form.projectStageList.splice(index,1);
        },
        updateVisible(val){
            this.dialogVisible = val;
        },
        addAction(){
            this.dialogVisible = true;
        },
        // 保存
        saveAction(){
            console.log("参数===》",this.form);
            this.$refs['addform'].validate((valid) => {
                if (!valid) {
                    return false;
                } 
                if (this.form.id) {
                    this.updateData();
                }else{
                    this.addData();
                }
            });
            
        },
        addData(){
            var par =  {
                projectStageTemplateEntity:{
                    name:this.form.name,
                    applicationId: sessionStorage.getItem('applicationId')
                },
            };
            par.projectStageList = this.form.projectStageList;
            addProjectstagetemplate(par).then((result) => {
                console.log("结果data==>",result);
                if (result.data) {
                    this.$message({
                    type:"success",
                    message:"添加成功！"
                    })
                    this.$router.back();
                }else{
                    this.$message({
                    type:"error",
                    message:"保存失败！"
                    })
                }
            }).catch((err) => {
                
            });
      },
      updateData(){
        var par =  {
            projectStageTemplateEntity:{
                id:this.form.id,
                name:this.form.name,
                applicationId: sessionStorage.getItem('applicationId')
            },
        };
        par.projectStageList = this.form.projectStageList;
        updateProjectstagetemplate(par).then((result) => {
            if (result.data) {
                this.$message({
                type:"success",
                message:"更新成功！"
                })
                this.$router.back();
            }else{
                this.$message({
                type:"error",
                message:"保存失败！"
                })
            }
        }).catch((err) => {
        
        });
      },
        
    }
}
</script>
<style scoped>
.addfcss{
    height: 100% !important;
    position: relative;
}
.mt-10{
    margin-top: -10px;
}
.width50 /deep/.el-input-number{
    width: 50px !important;
    text-align: center !important;
}

.studiocss img{
    width: 16px;
    height: 16px;
    margin-right: 3px;
    margin-top: -3px;
}
.width100{
    width: 100%;
}
.wid98{
  width: 98px;
}
.uploadtext{
  color: #4285F4;
  cursor: pointer;
}
.uploadcss{
  width: 88px;
  height: 88px;
  line-height: 24px;
  background: #F5F5F5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border:1px dashed #CCCCCC;
}
.uploadcss i{
  margin-top: 20px;
  font-size: 24px;
  color: #4285F4;
}
.rcenter{
    position: absolute;
    right: 10px;
    line-height: 34px;
    font-size: 14px;
    color: #c0c4cc;
}
.btncenter{
    text-align: center;
}
.mainbg{
    margin: 16px 0px;
    padding: 20px 16px;
    background-color: white;
    border-radius: 8px;
    min-height: calc(100% - 60px);
}
.pd0{
    padding-right: 0px !important;
}
</style>
<style>

</style>