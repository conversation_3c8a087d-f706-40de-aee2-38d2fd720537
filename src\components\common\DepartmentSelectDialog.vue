<template>
    <el-dialog
         title="选择部门"
         :visible.sync="dialogVisible"
         width="500px"
         :append-to-body="true"
         center
         class="unittree"
         @close="closeAction">
         <div class="condiv">
           <div class="ldiv fixwidth">
             <span class="spantext">部门</span>
             <el-tree
               ref="treeRef"
               :data="treeData"
               show-checkbox
               check-strictly
               node-key="id"
               default-expand-all
               :props="defaultProps"
               @check="handleCheckChange">
             </el-tree>
           </div>
         </div>
         <span slot="footer" class="dialog-footer">
           <el-button type="primary" @click="saveAction">确 定</el-button>
         </span>
       </el-dialog>
 </template>
 
 <script>
 import { listDept } from '@/api/framework/dept'
 
 export default {
     props:{
       visible:{
           type:Boolean,
           default:false,
       },
       userId: {
           type: [String, Number],
           default: ''
       },
       selectedDepartments: {
           type: Array,
           default: () => []
       }
     },
     computed:{
         dialogVisible:{
             get(){
                 return this.visible;
             },
             set(value){
                 this.$emit('updateVisible',value)
             }
         }
     },
     data(){
         return{
             treeData: [],
             defaultProps: {
               children: 'children',
               label: 'name'
             },
             checkedNodes: [],
             userDepartments: []
         }
     },
 
     methods:{
         async loadData(){
           try {
             // 获取所有部门树
             const deptResult = await listDept({});
             this.treeData = deptResult.data;

             // 回显已选中的部门
             this.$nextTick(() => {
               if (this.selectedDepartments && this.selectedDepartments.length > 0) {
                 const checkedIds = this.selectedDepartments.map(dept => dept.id);
                 this.$refs.treeRef.setCheckedKeys(checkedIds);
                 this.checkedNodes = [...this.selectedDepartments];
               } else {
                 // 如果没有传入已选中的部门，清空选择
                 this.$refs.treeRef.setCheckedKeys([]);
                 this.checkedNodes = [];
               }
             });
           } catch (error) {
             console.error('加载部门数据失败:', error);
           }
         },

         handleCheckChange(data, checked) {
           this.checkedNodes = checked.checkedNodes;
         },
         
         closeAction(){
           this.$emit('cancel');
         },
         
         saveAction() {
           if (!this.checkedNodes || this.checkedNodes.length === 0) {
             this.$message.error('请选择部门');
             return;
           }
           
           const selectedDepts = this.checkedNodes.map(node => ({
             id: node.id,
             name: node.name
           }));
           
           this.$emit('submit', selectedDepts);
         },
         
         reset() {
           if (this.$refs.treeRef) {
             this.$refs.treeRef.setCheckedKeys([]);
             this.checkedNodes = [];
           }
         }
     }
 }
 </script>
 
 <style scoped lang="scss">
 .unittree  /deep/ .el-tree{
   display: inline-block;
   min-width: 100%;
   padding-right: 10px;
 }
 .ptop{
   padding-top: 10px;
 }
 .unittree /deep/.el-dialog__body{
   height: 500px;
   padding: 0 !important;
 }
 .unittree /deep/.el-tree-node__label{
   font-size: 12px !important;
   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
   font-weight: 400;
   color: #333333;
   line-height: 14px;
 }
 .unittree /deep/.el-tree-node__expand-icon{
   color: #333333;
 }
 .unittree /deep/.el-tree-node__expand-icon.is-leaf{
   color: transparent;
 }
 .condiv{
   display: flex;
   height: 100%;
 }
 .ldiv{
   padding-top: 30px;
   padding-left: 26px;
   padding-right: 26px;
   width: 35%;
   overflow-y: scroll;
   overflow-x: scroll;
 }

 .fixwidth{
   width: 100%;
   box-sizing: border-box;
 }
 .spantext{
   font-size: 14px;
   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
   font-weight: 400;
   color: #333333;
 }
 </style>
