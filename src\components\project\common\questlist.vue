<template>
  <div>
    <el-table
      ref="singleTable"
      :tree-props="{ children: 'childrenList' }"
      row-key="id"
      class="mytableTree"
      :class="{ br: isBorder }"
      :height="isAutoHeight ? 'auto' : '590px'"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
      :highlight-current-row="isHighlight"
      @row-click="handleCurrentChange"
    >
      <el-table-column
        type=""
        prop="taskNum"
        label="ID"
        show-overflow-tooltip
        min-width="100"
      >
      </el-table-column>
      <el-table-column label="任务名称" min-width="200">
        <template slot-scope="scope">
          <span class="cusnamecss">
            {{ scope.row.taskName }}
            <el-tag
              class="tagml"
              size="mini"
              type="danger"
              v-if="scope.row.overdue"
              >已逾期</el-tag
            >
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级">
        <template slot-scope="scope">
          <div :style="{ color: status1[scope.row.priority].color }">
            {{ status1[scope.row.priority].name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="projectStatusName" label="状态">
        <template slot-scope="scope">
          <span :class="statusColor[scope.row.taskStatus]">
            {{ statusNames[scope.row.taskStatus] }}</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="chargePersonName" label="任务负责人" width="90px">
      </el-table-column>
      <el-table-column prop="endTime" label="截止时间" width="140px">
      </el-table-column>
      <el-table-column prop="workHours" label="消耗工时(小时)" width="120px">
      </el-table-column>
      <el-table-column
        prop="edit"
        width="370px"
        fixed="right"
        label="更多操作"
        v-if="isControl"
      >
        <template slot-scope="scope">
          <span
            v-if="
              scope.row.taskStatus != 3 &&
              scope.row.taskStatus != 5 &&
              scope.row.isOperate
            "
          >
            <el-button
              v-if="scope.row.taskStatus == 1 || scope.row.taskStatus == 4"
              class="bbtn mr10"
              type="text"
              @click="handleCommand(2, scope.row)"
            >
              启动
            </el-button>
            <el-dropdown
              v-else
              placement="bottom-end"
              @command="(e) => handleCommand(e, scope.row)"
              trigger="click"
            >
              <el-button class="bbtn mr10" type="text">
                变更 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="3">完成</el-dropdown-item>
                <el-dropdown-item :command="4">暂停</el-dropdown-item>
                <el-dropdown-item :command="5">关闭</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
          <el-button
            class="bbtn"
            type="text"
            @click="toDetail(scope.row)"
            v-isShow="'crm:controller:task:info'"
          >
            详情</el-button
          >

          <el-button class="bbtn" type="text" @click="showDialog(scope.row)">
            添加工作记录</el-button
          >
          <el-button
            class="bbtn"
            type="text"
            @click="add(scope.row)"
            v-if="scope.row.parentId == 0"
          >
            添加子任务</el-button
          >
          <el-dropdown
            placement="bottom-end"
            @command="(e) => handleCommandMore(e, scope.row)"
            trigger="click"
          >
            <el-button class="bbtn ml10" type="text">
              更多 <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown" class="special">
              <el-dropdown-item :command="2">
                <el-button class="bbtn" type="text">
                  成本申报</el-button
                ></el-dropdown-item
              >
              <el-dropdown-item :command="3">
                <el-button
                  class="bbtn colortext"
                  type="text"
                  :disabled="
                    !scope.row.isOperate ||
                    scope.row.taskStatus == 3 ||
                    scope.row.taskStatus == 5
                  "
                >
                  编辑</el-button
                >
              </el-dropdown-item>
              <el-dropdown-item :command="4">
                <el-button
                  class="rbtn colortext"
                  type="text"
                  :disabled="!scope.row.isOperate"
                >
                  删除</el-button
                >
              </el-dropdown-item>
              <el-dropdown-item :command="5">关闭</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <dc-dialog
      iType="1"
      title="温馨提示"
      width="500px"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <p class="pcc">确定要删除该任务么?</p>
    </dc-dialog>
    <!-- <recorddialog
      :idAndName="idAndName"
      ref="recordRef"
      :dialogVisible="dialogRecordVisible"
      @closeDialog="closeDialog"
    ></recorddialog> -->

    <!-- 工作记录对话框 -->
    <work-record-dialog
      :visible.sync="workRecordDialogVisible"
      :taskInfo="currentTaskInfo"
      @submit="submitWorkRecord"
    ></work-record-dialog>

    <el-dialog
      class="completeDialog"
      title="任务完成"
      :visible.sync="dialogVisibleComplete"
      width="30%"
    >
      <p class="pcen ptext">
        是否完成ID：{{ dialogObj.taskNum }}--{{ dialogObj.taskName }}的任务？
      </p>
      <span class="tipspan pcen"
        >完成任务后，将无法进行工作记录，已添加的工作记录将无法更改</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleComplete = false">取 消</el-button>
        <el-button type="primary" @click="completeButton">完 成</el-button>
      </span>
    </el-dialog>

    <!-- 成本申报 -->
    <cost-record-dialog
      :visible.sync="costRecordDialogVisible"
      :isEdit="isEditMode"
      :editData="editData"
      @submit="submitCostRecord"
      :taskObj="taskObj"
    ></cost-record-dialog>

    <msg-dialog ref="msgdialog" />
  </div>
</template>

<script>
import nolist from '@/components/common/nolist.vue'
import {
  taskUpdate,
  taskDelete,
  taskworkhoursSave,
  taskcostSave,
} from '@/api/project'
// import recorddialog from "./recorddialog.vue";
import WorkRecordDialog from './workRecordDialog.vue'
import Bus from '@/utils/EventBus'
import CostRecordDialog from './costRecordDialog.vue'
export default {
  components: {
    nolist,
    // recorddialog,
    WorkRecordDialog,
    CostRecordDialog,
  },
  props: {
    tableData: {
      type: Array,
      default: () => {
        return new Array()
      },
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    isHighlight: {
      type: Boolean,
      default: false,
    },
    isControl: {
      type: Boolean,
      default: true,
    },
    isAutoHeight: {
      type: Boolean,
      default: false,
    },
    isBorder: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      editData: {},
      isEditMode: false,
      costRecordDialogVisible: false,
      status1: {
        1: {
          name: '低',
          color: '#56C36E',
        },
        2: {
          name: '一般',
          color: '#4285F4',
        },
        3: {
          name: '紧急',
          color: '#FF8D1A',
        },
        4: {
          name: '非常紧急',
          color: '#F45961',
        },
      },
      dialogVisibleComplete: false,
      statusColor: {
        1: 'bcolor',
        2: 'icolor',
        3: 'scolor',
        4: 'ecolor',
        5: 'ecolor',
      },
      statusNames: {
        1: '未开始',
        2: '进行中',
        3: '已完成',
        4: '暂停',
        5: '停止',
      },
      dialogVisible: false,
      dialogRecordVisible: false,
      statusToCss: {
        1: 'nobegin',
        2: 'doing',
        3: 'end',
      },
      taskIdObj: {
        taskId: '',
      },
      idAndName: {},
      dialogObj: {
        taskNum: '',
        taskName: '',
      },
      currentRow: null,
      workRecordDialogVisible: false,
      currentTaskInfo: {
        id: '',
        taskNum: '',
        taskName: '',
      },
      taskObj: {},
    }
  },
  provide() {
    return {
      taskIdObj: this.taskIdObj,
    }
  },
  methods: {
    handleCommandMore(type, data) {
      if (type == 2) {
        this.taskObj = data
        this.costRecordDialogVisible = true
        return
      }
      if (type == 3) {
        this.onEdit(data)
        return
      }
      if (type == 4) {
        this.deleteAction(data)
      }
    },
    submitCostRecord(formData) {
      formData.taskId = this.taskObj.id
      const apiCall = taskcostSave(formData)
      apiCall.then((res) => {
        if (res.status == 0) {
          this.$refs.msgdialog.show({
            type: 'success',
            title: '申报成功',
            msg: '您的申报已成功提交，感谢您的配合！',
          })
          this.$emit('flushData')
        } else {
          this.$refs.msgdialog.show({
            type: 'error',
            title: '申报失败',
            msg: '您的申报未成功，请尝试重新提交。',
          })
           this.$emit('flushData')
        }
      })
    },
    completeButton() {
      var par = {
        id: this.dialogObj.id,
        taskStatus: '3',
      }
      taskUpdate(par)
        .then((result) => {
          if (result.data) {
            this.$emit('flushData')
            this.dialogVisibleComplete = false
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    handleCommand(index, data) {
      if (index == 3) {
        this.dialogVisibleComplete = true
        this.dialogObj = data
      } else {
        var par = {
          id: data.id,
          taskStatus: index,
        }
        taskUpdate(par)
          .then((result) => {
            if (result.data) {
              this.$emit('flushData')
            } else {
              this.$message({
                type: 'error',
                message: result.msg,
              })
            }
          })
          .catch((err) => {})
      }
    },
    add(row) {
      this.$router.push({
        path: '/project/manager/quest/add',
        query: {
          parentId: row.id,
          taskNum: row.taskNum,
          taskName: row.taskName,
        },
      })
    },
    submitDialog() {
      taskDelete({ id: this.dId })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.$emit('flushData')
            this.dialogVisible = false
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    deleteAction(data) {
      this.dId = data.id
      this.dialogVisible = true
    },
    toDetail(data) {
      this.$router.push({
        path: '/project/manager/quest/detail',
        query: {
          id: data.id,
          name: data.taskName,
        },
      })
    },
    onEdit(data) {
      if (data.parentId == 0) {
        this.$router.push({
          path: '/project/manager/quest/add',
          query: {
            editId: data.id,
            projectName: this.$route.query.name,
          },
        })
      } else {
        let prow = {}
        this.tableData.forEach((item) => {
          if (data.parentId == item.id) {
            prow = item
          }
          if (prow) {
            return
          }
        })
        this.$router.push({
          path: '/project/manager/quest/add',
          query: {
            editId: data.id,
            taskNum: prow.taskNum,
            taskName: prow.taskName,
          },
        })
      }
    },
    showDialog(row) {
      // 注释掉原有的逻辑
      // this.taskIdObj.taskId = row.id;
      // this.idAndName.taskNum = row.taskNum;
      // this.idAndName.taskName = row.taskName;
      // this.dialogRecordVisible = true;
      // this.$nextTick(() => {
      //   Bus.$emit("loadData");
      // });
      // this.$store.commit("task/CHANGETASKSTATES", {
      //   key: "taskStatus",
      //   value: row.taskStatus
      // });

      this.currentTaskInfo = {
        id: row.id,
        taskNum: row.taskNum,
        taskName: row.taskName,
      }
      this.workRecordDialogVisible = true
    },
    submitWorkRecord(formData) {
      taskworkhoursSave(formData).then((res) => {
        this.workRecordDialogVisible = false
        if (res.status == 0) {
          this.$refs.msgdialog.show({
            type: 'success',
            title: '提交成功',
            msg: '您的工作记录已成功提交，感谢您的配合！',
          })
          this.$emit('flushData')
        } else {
          this.$refs.msgdialog.show({
            type: 'error',
            title: '提交失败',
            msg: '您的工时提交未成功，请尝试重新提交。',
          })
          this.$emit('flushData')
        }
      })
    },
    closeDialog(isUpdate) {
      if (isUpdate) {
        this.$emit('flushData')
      }
      this.dialogRecordVisible = false
    },
    handleCurrentChange(row, column, event) {
      if (this.currentRow == row) {
        this.currentRow = null
        this.$refs.singleTable.setCurrentRow()
      } else {
        this.currentRow = row
        this.$refs.singleTable.setCurrentRow(row)
      }
      this.$emit('currentChange', this.currentRow)
    },
  },
}
</script>
<style>
.mytableTree .el-table__row--level-1 {
  background: #f5f5f5;
}
.mytableTree .el-icon-arrow-right {
  font-size: 14px;
}
.completeDialog .el-dialog__footer {
  text-align: center !important;
}
.special .el-dropdown-menu__item:hover .el-button,
.el-dropdown-item:focus .el-button {
  color: white !important;
}
</style>

<style scoped lang="scss">
.ml10 {
  margin-left: 10px;
}
.colortext {
  color: #999;
}
.ptext {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}
.pcen {
  text-align: center;
}

.tipspan {
  color: #f56c6c;
}
.el-icon--right {
  margin-left: 2px !important;
}
.tagml {
  margin-left: 6px;
}

.bcolor {
  color: #4285f4;
}
.icolor {
  color: #ff8d1a;
}
.scolor {
  color: #56c36e;
}
.ecolor {
  color: #f56c6c;
}
.br {
  border: 1px solid #e6e6e6;
  border-radius: 8px;
}
.customertable .el-button {
  padding: 2px;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}
</style>
