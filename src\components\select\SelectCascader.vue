<!-- 地区级联 -->
<template>
  <div>
    <el-cascader
      clearable
      ref="cascaderAddr"
      class="input-230"
      placeholder="请选择地区"
      change-on-select
      v-model="myValue"
      :options="options"
      filterable
      @change="handleChange"
      :props="handleprops"
      :disabled="isdisabled">
      </el-cascader>
  </div>
</template>

<script>
export default {
  props: ['value'],
  data() {
    return {
      myValue: [],
      handleprops: {
        value: 'id',
        label: 'name'
      },
      isdisabled: false,
      options: [],
      getid: []
    };
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    handleChange (val) {
      this.$emit('input', val)
    },
    changeItem (ary) {
      let identity = localStorage.getItem('identy');
      let num = Number(identity)
      if (ary instanceof Array && ary.length > 0) {
        if (num === 4 || num === 5) { // 省 默认到省
          this.myValue = [ary[0].id];
          this.isdisabled = false
        } else if (num === 2 || num === 3) { // 港 默认到区
          this.isdisabled = true
          let getprvio = ary[0].id;
          let getcity = ary[0].children[0].id
          let count = ary[0].children[0].children[0].id
          this.myValue = [getprvio, getcity, count]
        } else {
          this.isdisabled = false
          this.myValue = this.myValue
        }
      }
      this.handleChange(this.myValue)
    },
    async getDataList () {
      // let res = await this.$axios.get('/sf/business/area/areaTree')
      let res = await this.$axios.get('/sf/business/area/areaListByUser')
      if (res.status === 0) {
        this.options = res.data;
        this.changeItem(res.data)
      }
    }
  },
  created () {
    this.myValue = this.value || ''
    this.getDataList()
  }
};
</script>
