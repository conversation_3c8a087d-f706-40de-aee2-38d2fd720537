import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "./plugins/element";
import { api } from "./assets/api/axios";
import "element-ui/lib/theme-chalk/index.css";
import "@/assets/css/global.css";
import "@/assets/css/elementglobal.css";
import "@/styles/index.scss";
import "@/assets/css/common.scss";
import "font-awesome/css/font-awesome.css";
import VueUeditorWrap from "vue-ueditor-wrap";
import axios from "@/utils/request";
import "@/mixin";
import "./directives"; //v-show指令
import echarts from "echarts";
import ElTreeGrid from "element-tree-grid";
import { resetForm } from "@/utils/common";
import Pagination from "@/components/Pagination";
import { getToken } from "@/utils/auth";
import ElementUI from "element-ui";
import locale from "element-ui/lib/locale/lang/en";
import "@/icons";
import VueCropper from "vue-cropper";
import dcDialog from "@/components/common/dcDialog";

import maskV from "@/components/common/maskV";
import { getMenu } from "@/api/index.js";
import filters from "@/filter";
import { Loading } from "element-ui";
import "@/utils/dict";
import { checkPermission } from "@/utils/permission.js";

import * as dd from "dingtalk-jsapi";
import pdfView from "@/components/common/pdfMask";
import { getCode } from "@/utils/getCode";
import { authCodeLogin } from "@/api/index";
import Vue2OrgTree from 'vue-tree-color'
import Dialog from '@/components/plugins/dialog.js';
import MsgDialog from "./components/common/msgDialog.vue";
Vue.use(pdfView);
Vue.use(Vue2OrgTree);

// 全局方法挂载
Vue.prototype.checkPermission = checkPermission;

Vue.prototype.$maskV = maskV;
// Vue.prototype.$msgDialog = MsgDialog
Vue.component('msg-dialog',MsgDialog)
Vue.component("dcDialog", dcDialog);
Vue.component("vue-ueditor-wrap", VueUeditorWrap);
Vue.use(VueCropper);
Vue.component("Pagination", Pagination);
Vue.component(ElTreeGrid.name, ElTreeGrid);
Vue.use(ElementUI);
Vue.prototype.$echarts = echarts;
Vue.prototype.$api = api;
axios.defaults.baseURL = process.env.VUE_APP_BASE_API;
Vue.prototype.$http = axios;
Vue.prototype.resetForm = resetForm;
Vue.prototype.$emptyFont = "暂无数据";
Vue.prototype.$axios = axios;
Vue.prototype.$dialog = Dialog;
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key]);
});
Vue.prototype.msgSuccess = function (msg) {
  this.$message({ showClose: true, message: msg, type: "success" });
};

Vue.prototype.msgError = function (msg) {
  this.$message({ showClose: true, message: msg, type: "error" });
};

Vue.prototype.$dispatch = function (componentName, eventName, payload, boolean) {
  let parent = this.$parent;
  while (parent) {
    let name = parent.$options.name;
    if (name === componentName) {
      break;
    } else {
      parent = parent.$parent;
    }
  }
  if (parent) {
    if (eventName) {
      parent.$emit(eventName, payload, boolean);
    }
    return parent;
  }
};

Vue.prototype.msgInfo = function (msg) {
  this.$message.info(msg);
};
Vue.config.productionTip = false;
const whiteList = ["/login"];
let loadingInstance;
router.beforeEach(async (to, from, next) => {
  loadingInstance = Loading.service({
    target: "section"
  });
  const hasToken = getToken();
  if (hasToken) {
    const hasRoles =
      store.getters.lookRouter && store.getters.lookRouter.length > 0;
    if (hasRoles) {
      next();
    } else {
      try {
        store.dispatch("permission/getRouters").then(() => {
          router.addRoutes(store.getters.lookRouter);
          // if (!checkPermission('/home')) {
          //   console.log('===============')
          //   next({ ...store.getters.lookRouter[0], replace: true })
          // } else {
          //   // next({ path: '/home', replace: true })
          //   next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          // }
          // next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
        });
      } catch (error) {
        await store.dispatch("user/resetToken");
        Message.error(error || "Has Error");
        next(`/login?redirect=${to.path}`);
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      if (dd.env.platform !== "notInDingTalk") {
        getCode(code => {
          authCodeLogin({ authCode: code }).then(res => {
            if (res.status === 0) {
              sessionStorage.setItem(
                "vue_admin_template_token",
                res.data.token
              );
              sessionStorage.setItem('applicationName', res.data.applicationName)
              sessionStorage.setItem('applicationId', res.data.applicationId)
              sessionStorage.setItem("username", res.data.name);
              sessionStorage.setItem("logo", res.data.logo);
              sessionStorage.setItem("userid", res.data.userId);
              sessionStorage.setItem("dataScope", res.data.dataScope);
              sessionStorage.setItem(
                "departmentIds",
                res.data.departmentIds.join(",")
              );
              store.dispatch("permission/getRouters").then(() => {
                router.addRoutes(store.getters.lookRouter);
                next({ path: "/home", replace: true }); // hack方法 确保addRoutes已完成
                // next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
                // if (!checkPermission('/home')) {
                //   next({ ...store.getters.lookRouter[0], replace: true }) // hack方法 确保addRoutes已完成
                // } else {
                //   next({ path: '/home', replace: true }) // hack方法 确保addRoutes已完成
                //   // next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
                // }
                // next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
              });
            }
          });
        });
      } else {
        next(`/login?redirect=${to.path}`);
      }
    }
  }
});
router.afterEach(() => {
  loadingInstance.close();
});
window.wm = new Vue({
  router,
  store,
  render: h => h(App)
}).$mount("#app");
