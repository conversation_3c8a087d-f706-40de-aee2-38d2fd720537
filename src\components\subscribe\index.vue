<template>
  <div class="mainbg fixpb">
    <div class="headerdiv">
      <el-form :inline="true" label-width="85px" class="myform clearfix">
        <el-form-item label="">
          <el-input
            v-model="pageBean.materialName"
            class="definput w250"
            clearable
            placeholder="教材名称或出版社名称或ISBN"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="pageBean.unitName"
            class="definput inputWid150"
            clearable
            placeholder="请输入用书单位"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="pageBean.customerName"
            class="definput inputWid150"
            clearable
            placeholder="请输入用书客户"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="pageBean.createName"
            class="definput inputWid150"
            clearable
            placeholder="报订人"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="用书专业">
          <el-select
            v-model="pageBean.dictItemId"
            class="definput inputWid150"
            popper-class="removescrollbar"
            clearable
            placeholder="请输入用书专业"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteMethod"
          >
            <el-option
              v-for="item in majorList"
              :key="item.id"
              :label="item.name + '-' + item.parentName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="中标公司">
          <el-select
            v-model="pageBean.bidwinnerId"
            class="definput inputWid150"
            popper-class="removescrollbar"
            clearable
            placeholder="请选择中标公司"
          >
            <el-option
              v-for="item in bidwinnerList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="确认状态">
          <el-select
            v-model="pageBean.status"
            class="definput inputWid150"
            popper-class="removescrollbar"
            clearable
            placeholder="请选择状态"
          >
            <el-option
              v-for="item in confirmStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            @click="search"
            class="defaultbtn mt"
            icon="el-icon-search"
            type="primary"
            >搜索</el-button
          >
        </el-form-item>
        <el-form-item class="fr">
          <el-button
            class="defaultbtn mt"
            icon="el-icon-plus"
            v-isShow="'crm:controller:materialsubscription:save'"
            type="primary"
            @click="handleAdd"
            >新增报订</el-button
          >
        </el-form-item>
        <el-form-item class="fr">
          <el-button
            class="defaultbtn mt"
            v-isShow="'crm:controller:materialsubscription:subscriptionUpdate'"
            icon="el-icon-finished"
            type="primary"
            @click="handleBatchApproval"
            >一键报批</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-table class="mytable"
      :data="tableData"
      style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        :selectable="(row,index)=>row.cannotChoose === 1? false :true"
        width="55">
      </el-table-column>
      <el-table-column prop="materialName" label="教材名称" align="center" width="167" />
      <el-table-column prop="isbn" label="ISBN" align="center" width="167" />
      <el-table-column prop="platformName" label="出版社" align="center"  width="167" />
      <el-table-column prop="unitName" label="用书单位" align="center" width="167"  />
      <el-table-column prop="specialtyName" label="用书专业" align="center" width="167"  />
      <el-table-column prop="customerName" label="客户" align="center" width="167"  />
      <el-table-column prop="useBookYear" label="用书时间" align="center" width="167"  />
      <el-table-column prop="price" label="教材定价(元)" align="center" width="167"  />
      <el-table-column prop="reserveNum" label="预定数量" align="center" width="167"  />
      <el-table-column prop="reserveRetailPrice" label="报订码洋(万元)" align="center" width="167"  />
      <el-table-column prop="actualNum" label="实报数量" align="center" width="167" >
        <template slot-scope="scope">
          {{ scope.row.actualNum || '---' }}
        </template>
      </el-table-column>
      <el-table-column prop="discount" label="折扣" align="center" width="167" >
        <template slot-scope="scope">
          {{ scope.row.discount || '---' }}
        </template>
      </el-table-column>
      <el-table-column prop="actualRetailPrice" label="实际码洋(万元)" align="center" width="167" >
        <template slot-scope="scope">
          {{ scope.row.actualRetailPrice  || '---' }}
        </template>
      </el-table-column>
      <el-table-column prop="completionRate" label="完成率" align="center" width="167" >
        <template slot-scope="scope">
          {{ scope.row.completionRate || '---' }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center" width="167" >
        <template slot-scope="scope">
          <span
            class="circle"
            :class="{
              'c-four': scope.row.status == 4,
              'c-three': scope.row.status == 3,
              'c-two': scope.row.status == 2,
              'c-one': scope.row.status == 1,
            }"
          ></span>
          {{statusMap[scope.row.status] || '--'}}
        </template>
      </el-table-column>
      <el-table-column prop="createName" label="报订人" align="center" width="167" />
      <!--  -->
       <el-table-column prop="departmentName" label="报订人部门" align="center" width="167" />
      <el-table-column prop="bidwinnerName" label="中标公司" align="center" width="167" />
      <el-table-column prop="sourceGoods" label="货源" align="center" width="167" >
        <template slot-scope="scope">
          {{ scope.row.sourceGoods || '---' }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="报订时间" align="center" width="167" />

      <el-table-column
        prop="edit"
        width="200"
        align="center"
        fixed="right"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            class="bbtn tabbtn"
            type="text"
            v-isShow="'crm:controller:materialsubscription:list'"
            @click="handleDetail(scope.row)"
          >
            详情</el-button
          >
          <el-button
            class="bbtn tabbtn"
            type="text"
            v-isShow="'crm:controller:materialsubscription:update'"
            v-if="(scope.row.cannotChoose != 1) || (scope.row.cannotChoose == 1 && scope.row.status == 4 && (scope.row.createBy == userid || datascope == 4 ))"
            @click="handleEdit(scope.row)"
          >
            编辑 </el-button
          >
          <el-button
            class="bbtn tabbtn"
            type="text"
            v-isShow="'crm:controller:materialsubscription:subscriptionUpdate'"
            v-if="(scope.row.cannotChoose != 1) || (scope.row.cannotChoose == 1 && scope.row.status == 4 && (scope.row.createBy == userid || datascope == 4 ))"
            @click="handleApproval(scope.row)"
          >
            报批</el-button
          >
          <el-button
            v-isShow="'crm:controller:materialsubscription:delete'"
            class="rbtn tabbtn"
            type="text"
            @click="deleteAction(scope.row)"
          >
            删除</el-button
          >
        </template>
      </el-table-column>

    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <!-- 报批抽屉组件 -->
    <SubscribeDrawer
      v-model="drawerVisible"
      :data="approvalData"
      :title="drawerTitle"
      @submit="handleApprovalSubmit"
    ></SubscribeDrawer>

    <!-- 详情抽屉组件 -->
    <SubscribeDetailDrawer
      v-model="detailDrawerVisible"
      :data="detailData"
      ref='subscribeDetailDrawer'
      :title="'详情'"
    ></SubscribeDetailDrawer>
<!-- 删除提示 -->
        <dc-dialog iType="1" title="确定删除吗？" width="500px" :dialogVisible.sync="dialogDeleteVisible" @submit="submitDialog"
            :appendToBody="true">
            <template>

            </template>
            <p class="pcc">是否删除该教材报订信息？</p>
        </dc-dialog>
  </div>
</template>

<script>
import page from '../common/page'
import SubscribeDrawer from './subscribeDrawer'
import SubscribeDetailDrawer from './subscribeDetailDrawer'
import { selectUintSpecialty } from '@/api/clientmanagement/customer'
import { bookQuantityRange, confirmStatus } from '@/utils/status-tool'
import { getDict } from '@/utils/tools.js'
import { queryList } from '@/api/dict'
import {
  baodinglist,
  baodingdelete
 } from '@/api/baoding'
 import { queryDictTypeList } from "@/api/wapi";

export default {
  components: {
    page,
    SubscribeDrawer,
    SubscribeDetailDrawer
  },
  data() {
    return {
      total: 0,
      majorList: [],
      bidwinnerList:[],
      confirmStatus,
      drawerVisible: false,
      drawerTitle: '报批',
      approvalData: [],
      selectedRows: [],
      detailDrawerVisible: false,
      detailData: null,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        materialName: '',
        unitName: '',
        customerName: '',
        createName: '',
        dictItemId: '',
        bidwinnerId: '',
        status: '',
        // className: 'MaterialSubscriptionController',
      },
      tableData: [],
      deleteData:{},
      dialogDeleteVisible:false,
      userid:sessionStorage.getItem('userid'),
      datascope: sessionStorage.getItem('dataScope'),
      statusMap:{
        1:'未报批',
        2:'审核中',
        3:'已通过',
        4:'已驳回'
      },
    }
  },

  created() {
    this.getCompany()
    this.loadData()
  },
  methods: {
    handleAdd() {
      this.$router.push({
        path: '/subscribe/add',
      })
    },
    getCompany() {
      queryDictTypeList({code:'Company'}).then((result) => {
        this.bidwinnerList = result.data['Company'][0].dictItemVoList || []
      }).catch((err) => {

      });
    },
    loadData(){
      baodinglist(this.pageBean).then(res =>{
        this.tableData = res.data || []
        this.total = res.page.total
      }).catch(err=>{

      })
    },
    handleCurrentChange(val) {
      this.pageBean.pageNum = val
      this.loadData();
    },
    remoteMethod(query){
      if (query !== '') {
        queryList({
          dictTypeCode:'DisciplineSpecial',
          name:query
        }).then((result) => {
          this.majorList = result.data
        }).catch((err) => {

        });
      }else{
        this.majorList = []
      }
    },
    search() {
      console.log(this.pageBean)
      this.pageBean.pageNum = 1;
      this.loadData()
    },
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },
    handleApproval(row) {
      this.drawerTitle = '报批'
      this.approvalData = [row]
      this.drawerVisible = true
    },
    handleDetail(row) {
      this.detailData = row
      this.detailDrawerVisible = true
      this.$refs.subscribeDetailDrawer.loadinfo(row.id)
    },
    handleEdit(row){
      this.$router.push({
        path: '/subscribe/add',
        query:{
          id:row.id
        }
      })
    },
    handleBatchApproval() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条记录')
        return
      }
      this.drawerTitle = '批量报批'
      this.approvalData = this.selectedRows
      this.drawerVisible = true
    },
    handleApprovalSubmit(data) {
      console.log('提交的报批数据:', data)
      this.$message.success('报批成功')
      this.loadData()
    },
    deleteAction(data){
      this.dialogDeleteVisible = true;
      this.deleteData = data
    },
    submitDialog(){
      baodingdelete({
        id:this.deleteData.id
      }).then(res =>{
         this.dialogDeleteVisible = false;
        if(res.data){
          this.$message.success('删除成功')
          this.loadData()
        }else{
          this.$message.error("删除失败"+res.msg)
        }
      }).catch(err =>{

      })
    },
  },
}
</script>
<style lang="scss" scoped>
.w250{
  width: 250px;
}
.bbtn {
  cursor: pointer;
}
.w1 {
  width: 120px;
}
.w2 {
  width: calc(100% - 120px);
}
.ml {
  margin-left: 10px;
}
.success {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #56c36e;
}

.error {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #f45961;
}

.headerdiv {
  display: flex;
  justify-content: center;
}

.headerdiv /deep/.myform {
  width: 100%;
}

.rightdiv {
  width: 120px;
  text-align: center;
}

.rightdiv .el-button {
  margin: 5px 0px;
  margin-bottom: 18px;
}

.mainbg {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  /* background-color: red; */
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 20px;
}

.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}

.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}

.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.contracttable .el-button {
  padding: 2px;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}
.myform .el-form-item:last-child{
  margin-right: 20px !important;
}
</style>
