<template>
  <div class="tree-chart-page">
    <back>返回</back>
    <div class="title-container">
      <h3>{{ dtitle }}</h3>
    </div>
    <div class="tree-container">
      <div style="margin-left: 30px">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-switch
              v-model="horizontal"
              :width="50"
              active-text="横排"
              inactive-text="竖排"
              style="margin-top: 8px"
            />
          </el-col>
          <!-- <el-col :span="5">
            <el-switch
              v-model="expandAll"
              :width="50"
              active-text="全部展开"
              inactive-text="全部折叠"
              style="margin: 8px"
              @change="expandChange"
            />
          </el-col> -->
        </el-row>
      </div>
      <div class="tree-wrapper">
        <vue2-org-tree
          :data="treeData"
          :horizontal="horizontal"
          :collapsable="collapsable"
          :label-class-name="labelClassName"
          :expand-all="expandAll"
          @on-expand="onExpand"
          @on-node-click="onNodeClick"
        />
      </div>
    </div>

    <el-dialog
      :visible.sync="nodeDialogVisible"
      :title="`(${nodeInfo.label})${title}`"
      width="80%"
      append-to-body
      class="node-dialog"
      top="10vh"
    >
      <div class="node-detail">
        <div class="page-header">
          <el-table
            class="customertable mytable tootiptable"
            height="590px"
            v-loading="isLoading"
            :data="tableData"
            style="width: 100%"
          >
            <el-table-column
              prop="customerName"
              label="客户名称"
              align="center"
              width="90px"
            >
              <template slot-scope="scope">
                <span class="bbtn" @click="toDetail(scope.row)">{{
                  scope.row.customerName
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="unitDepartment"
              label="部门"
              align="center"
              width="120px"
            >
            </el-table-column>
            <el-table-column
              prop="duties"
              label="职务"
              align="center"
              width="120px"
            >
            </el-table-column>
            <el-table-column
              prop="customerLevelName"
              label="客户级别"
              align="center"
              width="100px"
            >
              <template slot-scope="scope">
                <span :class="levelColor[scope.row.customerLevelName]">{{
                  scope.row.customerLevelName
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="phone"
              align="center"
              label="联系电话"
              width="120px"
            >
            </el-table-column>
            <el-table-column
              prop="majorNames"
              label="负责专业"
              align="center"
              min-width="200px"
            >
              <template slot-scope="scope">
                {{
                  scope.row.specialtyName.length > 0
                    ? scope.row.specialtyName.join(',')
                    : '—'
                }}
              </template>
            </el-table-column>
            <el-table-column label="负责人" align="center" width="160px">
              <template slot-scope="scope">
                <div v-if="scope.row.chargePersonNames.length > 0">
                  <el-tag
                    class="cuscss"
                    v-for="(item, index) in scope.row.chargePersonNames"
                    :key="index"
                    >{{ item }}</el-tag
                  >
                </div>
                <div v-else>—</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="创建时间"
              align="center"
              width="160px"
            >
            </el-table-column>
            <template slot="empty">
              <nolist></nolist>
            </template>
          </el-table>
          <page
            :currentPage="pageBean.pageNum"
            :total="total"
            :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"
          ></page>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import page from '@/components/common/page.vue'
import nolist from '@/components/common/nolist.vue'
import back from '@/components/common/back.vue'
import { queryStructureTreeList, cusList } from '@/api/unit'

import { customerLevelColors } from '@/utils/dict.js'

export default {
  components: {
    page,
    nolist,
    back,
  },
  data() {
    return {
      dtitle: '',
      horizontal: false,
      expandAll: true,
      collapsable: false,
      labelClassName: 'bg-color-orange',
      treeData: {},
      nodeDialogVisible: false,
      nodeInfo: {},
      title: '下属客户',
      pageBean: {
        pageNum: 1,
        pageSize: 10,
      },
      isLoading: false,
      tableData: [],
      total: 0,
      levelColor: customerLevelColors,
    }
  },
  created() {
    const unitId = this.$route.query.id
    const unitName = this.$route.query.unitName
    if (unitId && unitName) {
      this.dtitle = unitName + '架构图'
      this.getTreeData(unitId, unitName)
    }
  },
  methods: {
    getTreeData(id, name) {
      queryStructureTreeList({ unitId: id }).then((res) => {
        if (res.status === 0) {
          if (res.data.length > 0) {
            console.log('原始数据:', JSON.stringify(res.data))
            const processedData = this.formatTreeData(res.data)
            console.log('处理后的树形数据:', JSON.stringify(processedData))

            this.treeData = {
              id: 0,
              label: name,
              children: processedData,
              expanded: true,
            }
            console.log('最终树形数据:', JSON.stringify(this.treeData))
          } else {
            this.$message.error('该单位暂无架构信息可以查看')
          }
        }
      })
    },
    formatTreeData(data) {
      return data.map((item) => {
        const node = {
          id: item.id,
          label: item.name,
          expanded: true,
          children:
            item.childList && item.childList.length > 0
              ? this.formatTreeData(item.childList)
              : [],
        }
        console.log('处理节点:', item.name, '子节点数量:', node.children.length)
        return node
      })
    },
    onExpand(e, data) {
      if (data.expanded) {
        data.expanded = false
      } else {
        data.expanded = true
      }
    },
    expandChange(val) {
      this.expandAll = val
    },
    onNodeClick(e, data) {
      console.log('ddddddd',data.id);
      if (data.id == 0) {
        // this.pageBean.unitId = this.$route.query.id
        return
      }
      this.pageBean.structureId = data.id
      this.nodeInfo = data
      this.nodeDialogVisible = true

      this.pageBean.pageNum = 1
      this.loadData()
    },
    loadData() {
      this.isLoading = true
      cusList(this.pageBean)
        .then((res) => {
          if (res.status == 0) {
            this.tableData = res.data
            this.total = res.page.total
          } else {
            this.$message.error(res.msg)
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    toDetail(row) {
      this.$router.push({
        path: '/clientManagement/unit/unitsysdetail',
        query: {
          id: row.id,
          sId: this.pageBean.structureId,
        },
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>

<style lang="scss" scoped>
.tree-chart-page {
  padding: 20px;
  background-color: #fff;
}

.title-container {
  margin: 10px 0;
  text-align: center;
}

.tree-container {
  margin-top: 20px;
}

.tree-wrapper {
  width: 100%;
  height: auto;
  overflow: auto;
  margin-top: 20px;
  border: 1px solid #eee;
  padding: 20px;
}

.node-detail {
  padding: 10px;
  line-height: 24px;
}

.bbtn {
  cursor: pointer;
}
:deep(.org-tree-node-label) {
  cursor: pointer;
}
</style>
