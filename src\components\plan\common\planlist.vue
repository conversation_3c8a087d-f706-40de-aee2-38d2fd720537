
<template>
  <div class="mainbg fixpb">
    <el-form :inline="true" class="myform">
      <el-form-item label="">
        <el-input
          v-model="pageBean.createByName"
          class="definput"
          clearable
          placeholder="请输入提交人姓名"
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="`${types[plantype].label}：`">
        <el-date-picker
          class="definput"
          @change="changeDatePicker"
          v-model="searchValue"
          :type="types[plantype].type"
          :format="types[plantype].format"
          placeholder="请选择"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="部门：">
        <div class="unitbtn mt definput" @click="chooseDepartment">
          <span v-if="pageBean.departmentIds" class="deffont">{{
            searchdepartmentNames
          }}</span>
          <span v-else>请选择</span>
          <i
            v-if="searchdepartmentNames"
            @click.stop="cleardata"
            class="rcenter el-icon-circle-close"
          />
          <i v-else class="rcenter el-icon-arrow-down" />
        </div>
      </el-form-item>
      <el-form-item label="审核状态：">
        <el-select
          class="definput w1"
          popper-class="removescrollbar"
          clearable
          v-model="pageBean.status"
          placeholder="请选择"
        >
          <el-option
            v-for="item in statusList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否变更：">
        <el-select
          clearable
          class="definput w1"
          popper-class="removescrollbar"
          v-model="pageBean.isChange"
          placeholder="请选择"
        >
          <el-option
            v-for="item in isChangeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          @click="searchAction"
          class="defaultbtn mt"
          icon="el-icon-search"
          type="primary"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item class="fr">
        <el-button
          class="defaultbtn mt"
          v-isShow="'crm:controller:personalgoals:save'"
          icon="el-icon-plus"
          type="primary"
          @click="addPlan"
          >制定计划</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        prop="year"
        :width="plantype == 3 ? 260 : 100"
        :label="types[plantype].label"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ getPlanTime(scope.row) }}</span>
          <span v-if="plantype == 3"
            >({{
              scope.row.timeRange.start + '-' + scope.row.timeRange.end
            }})</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="createByName" label="提交人" align="center">
        <template slot-scope="scope">
          <span
            >{{ scope.row.createByName }}
            <span class="noset">{{
              scope.row.logsStatus == 1 ? '(未处理)' : ''
            }}</span></span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="departmentName"
        label="部门"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column prop="year" label="审核状态" align="center">
        <template slot-scope="scope">
          <span :class="statusColor[scope.row.status]">{{
            statusNames[scope.row.status]
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="提交时间"
        align="center"
        width="170"
      >
      </el-table-column>
      <el-table-column prop="createTime" label="提交情况" align="center">
        <template slot-scope="scope">
          <span>{{ numberToName[scope.row.timeout] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isChange" label="是否变更" align="center">
        <template slot-scope="scope">
          <span :class="ischangeColor[scope.row.isChange]">{{
            ischangeNames[scope.row.isChange]
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="edit"
        width="220"
        align="center"
        label="操作"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-isShow="'crm:controller:personalgoals:info'"
            class="bbtn"
            type="text"
            @click="todetail(scope.row)"
          >
            详情</el-button
          >
          <el-button
            v-isShow="'crm:controller:personalgoals:update'"
            class="bbtn"
            type="text"
            :disabled="isEditable(scope.row)"
            @click="onEdit(scope.row)"
          >
            编辑</el-button
          >
          <el-button
            v-isShow="'crm:controller:personalgoals:update'"
            class="bbtn"
            type="text"
            :disabled="isChangeable(scope.row)"
            @click="onChange(scope.row)"
          >
            计划变更</el-button
          >
          <el-button
            v-isShow="'crm:controller:personalgoals:delete'"
            class="rbtn"
            type="text"
            :disabled="isDelete(scope.row)"
            @click="deleteAction(scope.row)"
          >
            删除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>

    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <!-- 选择部门 -->
    <departmentDialog
      ref="deRef"
      dType="1"
      :visible.sync="dialogDepartmentVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </departmentDialog>
    <!-- 删除 -->
    <dc-dialog
      iType="1"
      title="温馨提示"
      width="400px"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <template> </template>
      <p class="pcc">是否删除该计划内容？</p>
    </dc-dialog>
    <!-- 制定计划 -->
    <el-dialog
      title="制定计划"
      :visible.sync="dialogPlanVisible"
      width="500px"
      center
      :before-close="handlePlanClose"
    >
      <span>
        <el-form
          :model="addForm"
          :rules="rules"
          class="mt30 formw"
          label-width="85px"
          ref="formModel"
        >
          <el-form-item :label="`${types[plantype].label}：`" prop="planTime">
            <el-date-picker
              class="definput"
              v-model="addForm.planTime"
              :type="types[plantype].type"
              :format="types[plantype].format"
              :picker-options="pickerOptions"
              placeholder="请选择"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="部门："
            prop="departmentId"
            v-if="departmentArr.length > 1"
          >
            <el-select
              @change="cheangeDe"
              class="definput"
              popper-class="removescrollbar"
              v-model="addForm.departmentId"
              placeholder="请选择"
            >
              <el-option
                v-for="item in departmentArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="模版："
            prop="template"
            v-if="addForm.departmentId"
          >
            <el-select
              class="definput"
              popper-class="removescrollbar"
              v-model="addForm.template"
              placeholder="请选择"
            >
              <el-option
                v-for="item in templateList"
                :key="item.id"
                :label="item.templateName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextAction">下一步</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="温馨提示"
      :visible.sync="dialogDraftVisible"
      width="400px"
      center
      :close-on-click-modal="false"
    >
      <div class="pcc">
        <p>存在未提交草稿，是否继续编辑草稿？</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rewritePlan">重新填写</el-button>
        <el-button type="primary" @click="continueEditDraft">继续编辑</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import { findTemplate } from '@/api/common'
import {
  personalgoalsList,
  personalgoalsdelete,
  queryCompanyDepartment,
} from '@/api/goal/index'
import {
  getDict,
  downloadFileByUrl,
  deepQuery,
  getWeekDates,
  getParStr,
} from '@/utils/tools'
import { getToken } from '@/utils/auth'
import { getWeek, getPlanTime } from '@/utils/index'
import departmentDialog from '@/components/common/departmentDialog.vue'
import { listDept } from '@/api/framework/dept'

import { isExist } from '@/api/index'

export default {
  name: 'planlist',
  props: {
    plantype: {
      type: Number,
      default: 1,
    },
  },
  components: {
    page,
    nolist,
    departmentDialog,
  },
  data() {
    return {
      types: {
        1: {
          label: '年份',
          type: 'year',
          format: 'yyyy',
        },
        2: {
          label: '年-月',
          type: 'month',
          format: 'yyyy-M',
        },
        3: {
          label: '年-周',
          type: 'week',
          format: 'yyyy 第 WW 周',
        },
      },
      searchValue: '',
      addForm: {
        template: '',
        planTime: '',
        departmentId: '',
      },
      templateList: '',
      deleteData: {},
      draftId: '',
      detailVisible: false,
      dialogVisible: false,
      dialogPlanVisible: false,
      dialogDepartmentVisible: false,
      dialogDraftVisible: false,
      dialogFormVisible: false,
      dialogTitle: '',
      isSubmit: false,
      isLoading: false,
      searchdepartmentNames: '',
      returnStatusArr: [],
      options: [],
      tableData: [],
      total: 0,
      form: {
        name: '',
        dictTypeCode: 'Press',
        sortNo: '',
      },
      pickerOptions:{
        firstDayOfWeek:1,
      },
      makeplanTime: {
        year: '',
        month: '',
        week: '',
      },
      rules: {
        planTime: [{ required: true, message: '请选择时间', trigger: 'blur' }],
        template: [
          { required: true, message: '请选择模板', trigger: 'change' },
        ],
        departmentId: [
          { required: true, message: '请选择部门', trigger: 'change' },
        ],
      },
      statusList: [
        { id: '1', name: '待审核' },
        { id: '2', name: '审核中' },
        { id: '3', name: '已通过' },
        { id: '4', name: '驳回' },
      ],
      statusColor: {
        1: 'bcolor',
        2: 'icolor',
        3: 'scolor',
        4: 'ecolor',
      },
      statusNames: {
        1: '待审核',
        2: '审核中',
        3: '已通过',
        4: '驳回',
      },
      isChangeList: [
        { id: '1', name: '未变更' },
        { id: '2', name: '变更' },
      ],
      ischangeNames: {
        1: '否',
        2: '是',
      },
      ischangeColor: {
        1: 'fcolor',
        2: 'icolor',
      },
      addPath: {
        1: '/plan/yearplan/yearadd',
        2: '/plan/monthplan/monthadd',
        3: '/plan/weekplan/weekadd',
      },
      addChangePath: {
        1: '/plan/yearplan/yearaddchange',
        2: '/plan/monthplan/monthaddchange',
        3: '/plan/weekplan/weekaddchange',
      },
      detailPath: {
        1: '/plan/yearplan/yeardetail',
        2: '/plan/monthplan/monthdetail',
        3: '/plan/weekplan/weekdetail',
      },
      pageBean: {
        createByName: '',
        status: '',
        departmentIds: '',
        pageNum: 1,
        pageSize: 10,
        year: '',
        month: '',
        week: '',
        goalsType: '',
        isChange: '',
        goalStatus: 1
      },
      selDepartmentData: [],
      departmentArr: [],
      numberToName: {
        1: '准时提交',
        2: '迟交',
        0: '--',
      },
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
    }
    this.pageBean.goalsType = this.plantype
    this.loadData()
    this.listDeptApi()
  },
  methods: {
    listDeptApi() {
      queryCompanyDepartment({}).then((result) => {
        if (result.status == 0) {
          this.departmentArr = result.data
          if (this.departmentArr.length == 1) {
            this.addForm.departmentId = this.departmentArr[0].id
          }
        }
      })
    },
    getPlanTime(data) {
      return getPlanTime(data, this.plantype)
    },
    cheangeDe(value) {
      this.addForm.template = ''
      this.loadTemplate()
    },
    loadTemplate() {
      findTemplate({
        methodName: 'save',
        className: 'PersonalGoalsController',
        templateType: this.plantype,
        departmentId: this.addForm.departmentId,
      })
        .then((result) => {
          this.templateList = result.data
        })
        .catch((err) => {})
    },
    querySearch(queryString, callback) {
      var list = [{}]
      if (queryString && queryString.length > 0) {
        let params = {
          dictTypeCode: 'Press',
          name: queryString,
        }
        queryList(params).then((res) => {
          list = res.data.map((item) => {
            return {
              id: `${item.id}`,
              value: `${item.name}`,
            }
          })
          callback(list)
        })
      }
    },
    handlename(item) {
      this.form.name = item.value
    },
    isDelete(data) {
      if (sessionStorage.getItem('dataScope') == 4) {
        return false
      }
      if (data.status == 1 || data.status == 4) {
        return false
      }
      return true
    },
    isChangeable(data) {
      if (data.status == 1 || data.status == 2 || data.status == 4) {
        return true
      }
      return false
    },
    isEditable(data) {
      if (
        (data.status == 1 || data.status == 4) &&
        data.createBy == sessionStorage.getItem('userid')
      ) {
        return false
      }
      return true
    },
    loadData(params) {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      personalgoalsList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          if (this.plantype == 3) {
            this.tableData.forEach((item) => {
              item.timeRange = getWeekDates(item.year, item.week)
            })
          }
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    chooseDepartment(e) {
      this.dialogDepartmentVisible = true
      this.$refs.deRef.loadData()
      this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
    },
    updateSystemVisible(val) {
      this.dialogDepartmentVisible = val
    },
    submitData(data) {
      this.dialogDepartmentVisible = false
      this.selDepartmentData = data
      let departmentIds = data.map((item) => item.id)
      let departmentNames = data.map((item) => item.name)
      this.pageBean.departmentIds = departmentIds.join(',')
      this.searchdepartmentNames = departmentNames.join(',')
    },
    cleardata() {
      this.searchdepartmentNames = ''
      this.pageBean.departmentIds = ''
      this.selDepartmentData = []
    },
    onChange(data) {
      this.$router.push({
        path: `${this.addChangePath[this.plantype]}`,
        query: {
          id: data.id,
          type: this.plantype,
        },
      })
    },
    addPlan() {
      this.dialogPlanVisible = true
      if (this.departmentArr.length == 1) {
        this.loadTemplate()
      }
    },
    handlePlanClose() {
      this.dialogPlanVisible = false
      this.planTime = ''
      this.$refs['formModel'].resetFields()
    },
    nextAction() {
      this.$refs['formModel'].validate((valid) => {
        if (valid) {
          let params = {
            goalsType: this.plantype,
            departmentId: this.addForm.departmentId,
            year: '',
            month: '',
            week: '',
          }
          params.year = new Date(this.addForm.planTime).getFullYear()
          if (this.plantype == 2) {
            params.month = new Date(this.addForm.planTime).getMonth() + 1
          } else if (this.plantype == 3) {
            var time = getWeek(this.addForm.planTime)
            var times = time.split('-')
            params.week = times[1]
          }
          isExist(params).then((res) => {
            if (res.status == 0) {
              if (res.data.status == 0) {
                this.showDraftConfirmDialog(res.data.id)
              } else if (res.data.status == 1) {
                this.$message({
                  type: 'warning',
                  message: '已存在记录，无法新增计划',
                })
              } else {
                this.navigateToAddPage()
              }
            } else {
              this.$message({
                type: 'error',
                message: res.msg,
              })
            }
          })
        } else {
          return false
        }
      })
    },
    showDraftConfirmDialog(draftId) {
      this.draftId = draftId
      this.dialogDraftVisible = true
    },
    navigateToAddPage() {
      if (this.addForm.planTime && this.addForm.template) {
        this.$router.push({
          path: `${this.addPath[this.plantype]}?type=${
            this.plantype
          }&time=${this.addForm.planTime.toDateString()}&templateId=${
            this.addForm.template
          }&departmentId=${this.addForm.departmentId}`,
        })
      }
    },
    continueEditDraft() {
      // 编辑草稿，带上草稿ID
      if (this.addForm.planTime && this.addForm.template && this.draftId) {
        this.$router.push({
          path: `${this.addPath[this.plantype]}?type=${
            this.plantype
          }&time=${this.addForm.planTime.toDateString()}&templateId=${
            this.addForm.template
          }&departmentId=${this.addForm.departmentId}&id=${this.draftId}`,
        })
      }
      this.dialogDraftVisible = false
    },
    rewritePlan() {
      if (this.addForm.planTime && this.addForm.template && this.draftId) {
        this.$router.push({
          path: `${this.addPath[this.plantype]}?type=${
            this.plantype
          }&time=${this.addForm.planTime.toDateString()}&templateId=${
            this.addForm.template
          }&departmentId=${this.addForm.departmentId}&draftId=${this.draftId}`,
        })
      }
      this.dialogDraftVisible = false
    },
    submitDialog() {
      personalgoalsdelete({ id: this.deleteData.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
      this.dialogVisible = false
    },
    deleteAction(data) {
      this.deleteData = data
      this.dialogVisible = true
    },
    todetail(data) {
      this.$router.push({
        path: this.detailPath[this.plantype],
        query: {
          id: data.id,
          type: this.plantype,
          createByName: data.createByName,
          timeRange: data.timeRange
            ? `${data.timeRange.start}-${data.timeRange.end}`
            : '',
        },
      })
    },
    onEdit(data) {
      this.$router.push({
        path: `${this.addPath[this.plantype]}`,
        query: {
          id: data.id,
          type: this.plantype,
        },
      })
    },
    changeDatePicker(date) {
      if (!date) {
        this.pageBean.year = ''
        this.pageBean.month = ''
        this.pageBean.week = ''
        return
      }
      var newDate = new Date(date)
      this.pageBean.year = newDate.getFullYear()
      if (this.plantype == 2) {
        this.pageBean.month = newDate.getMonth() + 1
      } else if (this.plantype == 3) {
        var time = getWeek(newDate)
        var times = time.split('-')
        this.pageBean.year = times[0]
        this.pageBean.week = times[1]
      }
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>

<style scoped>
.noset {
  color: #e6a23c;
  margin-left: 8px;
}
.formw {
  width: 340px;
  margin: auto;
}
.mt30 {
  margin-top: 30px;
}
.el-auto {
  width: 280px;
}
.w1 {
  width: 110px;
}
.flexd {
  text-align: right;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}
.definput {
  width: 200px;
}
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: calc(100vh - 106px);
}
.pcc {
  margin: 0 auto;
  text-align: center;
}
.smtext {
  zoom: 0.8;
}
.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.mt {
  margin-top: 4px;
}
.ml {
  margin-right: 10px;
}
.cusnamecss {
  display: flex;
}
.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 20px;
}
.bcolor {
  color: #4285f4;
}
.icolor {
  color: #ff8d1a;
}
.scolor {
  color: #56c36e;
}
.ecolor {
  color: #f45961;
}
.fcolor {
  color: #333333;
}
.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}
.ltext {
  color: #4285f4;
  text-align: justify !important;
}
.mytable /deep/ .cell {
  height: auto !important;
  min-height: 52px !important;
  padding: 13px !important;
  line-height: 25px !important;
}
.mytable .el-button {
  padding: 2px;
}
.mr10 {
  margin-right: 10px;
}
.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.right {
  text-align: right;
}
.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}
</style>
