<template>
  <div class="navbar">
    <!-- <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb class="breadcrumb-container" /> -->

    <!-- <div class="right-menu">
      <el-dropdown class="avatar-container">
        <div class="avatar-wrapper">
          <span class="" style="font-size: 16px">{{ name }}</span>
          <i class="el-icon-arrow-down el-icon--right"></i>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item style="text-align: center">
            <span style="display: block; padding: 0 20px" @click="handleAddRole"
              >修改密码</span
            >
          </el-dropdown-item>
          <el-dropdown-item style="text-align: center">
            <span style="display: block; padding: 0 20px" @click="logout"
              >退出登录</span
            >
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogFormVisible"
        custom-class="new-dialog"
        append-to-body
        width="700px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="dataForm"
          :model="temp"
          :rules="rules"
          label-position="right"
          label-width="100px"
          style="width: 500px; margin-left: 50px"
        >
          <el-form-item label="原密码" prop="oldPassword">
            <el-input
              type="password"
              v-model.trim="temp.oldPassword"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              type="password"
              v-model.trim="temp.newPassword"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="repeat">
            <el-input
              type="password"
              v-model.trim="temp.repeat"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false"> 取消 </el-button>
          <el-button type="primary" @click="createData"> 保存 </el-button>
        </div>
      </el-dialog>
    </div> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import { passwordUpdate } from '@/api/user'
import md5 from 'md5'
import { isPasswordValid } from '@/utils/coding-utils'
export default {
  components: {
    // Breadcrumb,
    // Hamburger,
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'AllRouter']),
  },
  data() {
    const newPwdValidator = (rule, value, callback) => {
      if (isPasswordValid(value)) {
        callback()
      } else {
        callback(new Error('请输入6-18位的字符'))
      }
    }
    const repeatPwdValidator = (rule, value, callback) => {
      if (value === this.temp.newPassword) {
        callback()
      } else {
        callback(new Error('两次输入的密码不一致'))
      }
    }
    return {
      name: sessionStorage.getItem('username'),
      temp: {
        oldPassword: '',
        newPassword: '',
        repeat: '',
      },
      rules: {
        oldPassword: [
          { required: true, trigger: 'change', message: '原密码不能为空' },
        ],
        newPassword: [
          { required: true, validator: newPwdValidator, trigger: 'change' },
        ],
        repeat: [
          { required: true, validator: repeatPwdValidator, trigger: 'change' },
        ],
      },
      dialogFormVisible: false,
      dialogTitle: '修改密码',
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      await this.$store.dispatch('permission/resertLookRouters') //清空路由
      this.$router.push(`/login`)
    },
    resetTemp() {
      this.temp = {
        oldPassword: '',
        newPassword: '',
        repeat: '',
      }
    },
    handleAddRole() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let oldstr = md5(md5(this.temp.oldPassword))
          let newstr = md5(md5(this.temp.newPassword))
          let msg = {
            id: this.$store.state.user.userId || localStorage.getItem('userId'),
            oldPassword: oldstr,
            newPassword: newstr,
          }
          passwordUpdate(msg).then((res) => {
            if (res.status === 0) {
              this.dialogFormVisible = false
              this.$notify({
                message: '密码修改成功, 请重新登录',
                type: 'success',
                duration: 2000,
              })
              this.logout()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 56px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    cursor: pointer;
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
