<template>
  <div class="hello">
    <div class="hexin mb20">
      <div class="htitle mb20">
        核心数据
        <span class="right times">{{ heInfo.now }} 更新</span>
      </div>
      <div class="top1">
        <ul class="ulc">
          <li class="lic">
            <p class="hp">新增客户</p>
            <span class="hnum3 numc">{{ heInfo.customerNum }}</span>
            <p class="leiji">
              <span>累计:</span>&nbsp;&nbsp;&nbsp;<span>{{
                heInfo.totalCustomerNum
              }}</span>
            </p>
            <!-- <img class="htimg" src="../../assets/img/home/<USER>" alt=""> -->
          </li>
          <li class="lic">
            <p class="hp">新增机会</p>
            <span class="hnum2 numc">{{ heInfo.opportunityNum }}</span>
            <p class="leiji">
              <span>累计:</span>&nbsp;&nbsp;&nbsp;<span>{{
                heInfo.totalOpportunityNum
              }}</span>
            </p>
            <!-- <img class="htimg" src="../../assets/img/home/<USER>" alt=""> -->
          </li>
          <li class="lic">
            <p class="hp">跟进与拜访</p>
            <span class="hnum numc">{{ heInfo.visitNum }}</span>
            <p class="leiji">
              <span>累计:</span>&nbsp;&nbsp;&nbsp;<span>{{
                heInfo.totalVisitNum
              }}</span>
            </p>
            <!-- <img class="htimg" src="../../assets/img/home/<USER>" alt=""> -->
          </li>

          <li class="lic">
            <p class="hp">新增发放</p>
            <span class="hnum4 numc">{{ heInfo.distributionNum }}</span>
            <p class="leiji">
              <span>累计:</span>&nbsp;&nbsp;&nbsp;<span>{{
                heInfo.totalDistributionNum
              }}</span>
            </p>
            <!-- <img class="htimg" src="../../assets/img/home/<USER>" alt=""> -->
          </li>
          <li class="lic">
            <p class="hp">新增项目</p>
            <span class="hnum5 numc">{{ heInfo.projectNum }}</span>
            <p class="leiji">
              <span>累计:</span>&nbsp;&nbsp;&nbsp;<span>{{
                heInfo.totalProjectNum
              }}</span>
            </p>
            <!-- <img class="htimg" src="../../assets/img/home/<USER>" alt=""> -->
          </li>
        </ul>
      </div>
    </div>

      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="基础数据" name="basicdata">
        <div class="allbox">
            <div class="leftbox">
              <div class="jichu">
                <div class="htitle bcolor">基础数据</div>
                <div>
                  <el-select
                    :title="deLevel"
                    class="definput xiala shenglue"
                    popper-class="removescrollbar"
                    v-model="deLevel"
                    placeholder="请选择"
                  >
                    <el-option
                      @click.native="changeOp(item)"
                      v-for="item in levels"
                      :key="item.id"
                      :label="item.value"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    @change="changeOne"
                    class="definput xiala ml16"
                    popper-class="removescrollbar"
                    v-model="hparams.time"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in month"
                      :key="item.id"
                      :label="item.value"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
              <div class="top2 bcg">
                <p class="ptitle">客户</p>
                <div
                  @click="gotoDetail(item)"
                  class="piececss csscursor"
                  :key="index"
                  v-for="(item, index) in dataViewList"
                >
                  <img class="imgf left" :src="item.imgUrl" alt="" />
                  <div>
                    <p class="numcolor" :class="[`color-${index}`]">
                      {{ item.numText }}
                    </p>
                    <p class="numtext">{{ item.text }}</p>
                  </div>
                </div>
              </div>
              <div class="top3 mt12 bcg">
                <p class="ptitle">业务</p>
                <div
                  @click=";(index == 0 || index == 3) && gotoDetail(item)"
                  class="piececss"
                  :class="{ csscursor: index == 0 || index == 3 }"
                  :key="index"
                  v-for="(item, index) in businessList"
                >
                  <img class="imgf left" :src="item.imgUrl" alt="" />
                  <div>
                    <p
                      class="numcolor"
                      :class="[`color-${index}`]"
                      v-if="item.text === '合同金额' || item.text === '未回款金额'"
                    >
                      {{ item.numText }}<span class="ml5">万元</span>
                    </p>
                    <p class="numcolor" :class="[`color-${index}`]" v-else>
                      {{ item.numText }}
                    </p>
                    <p class="numtext">{{ item.text }}</p>
                  </div>
                </div>
              </div>
              <div class="top4 mt12 bcg">
                <p class="ptitle">项目</p>
                <div
                  @click="index == 0 && gotoDetail(item)"
                  class="piececss"
                  :class="{ csscursor: index == 0 }"
                  :key="index"
                  v-for="(item, index) in projectList"
                >
                  <img class="imgf left" :src="item.imgUrl" alt="" />
                  <div>
                    <p
                      class="numcolor"
                      :class="[`color-${index}`]"
                      v-if="item.text === '项目金额'"
                    >
                      {{ item.numText }}<span class="ml5">万元</span>
                    </p>
                    <p class="numcolor" :class="[`color-${index}`]" v-else>
                      {{ item.numText }}
                    </p>
                    <p class="numtext">{{ item.text }}</p>
                  </div>
                </div>
              </div>
              <div class="top5 mt12 bcg">
                <p class="ptitle">其他</p>
                <div class="piececss csscursor" @click="gotoDemo">
                  <img class="imgf left" src="../../assets//img/home/<USER>" alt="" />
                  <div>
                    <p class="numcolor" :class="[`color`]">{{ bInfo.caseNum }}</p>
                    <p class="numtext">案例分享</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="rightbox" v-if="showGen || showWork || showTask">
              <!-- <div class="trbox1 bcg" v-if="showGen">
                <div class="liebg">
                  <textBorder class="mt10">跟进提醒</textBorder>
                  <el-button @click="visitMore" class="mbtn tabbtn more" type="text">
                    更多
                  </el-button>
                </div>
                <div class="paing">
                  <div class="table-e2" v-if="dataList.length > 0">
                    <div class="table-header">
                      <div class="tr">
                        <div class="cell c1">
                          <div class="shenlue visitname">跟进客户</div>
                        </div>
                        <div class="cell c2">进度</div>
                        <div class="cell c3">下次跟进时间</div>
                      </div>
                    </div>
                    <div class="table-tbody">
                      <div class="tr" :key="index" v-for="(item, index) in dataList">
                        <div class="cell c1">
                          <div class="shenlue">
                            {{ item.customerName }}
                          </div>
                        </div>
                        <div class="cell c2 c2text" @click="visitDetail(item)">
                          查看跟进
                        </div>
                        <div class="cell c3 rtime">{{ item.remindTime }}</div>
                      </div>
                    </div>
                  </div>
                  <div v-if="dataList.length == 0 && load" class="nolist">
                    <img class="noimg" src="../../assets/no.png" alt="" />
                    <p class="zanwu">暂无记录~</p>
                  </div>
                </div>
              </div> -->
            <div class="trbox2  bcg">
              <div class="topHandle">
                <div class="biaoTi">日程</div>
                <div class="goBtn" @click="addTip">新增</div>
              </div>
              <!-- <div v-if="tipList.length == 0" class="tipdiv">
                <img class="noimg" src="../../assets/no.png" alt="" />
                <p class="zanwu">暂无记录~</p>
              </div>
              <div class="paing tipdiv" v-else>
                <div
                  class="workitem"
                  @click="showDetail(item)"
                  v-for="(item, index) in tipList"
                  :key="index"
                >
                  <p class="workname1">{{ item.title }}</p>
                  <p class="workstatus">{{ item.remindTime }}</p>
                </div>
                <div class="bbtn tcenter" v-if="hasNextPage" @click="changePage">
                  更多
                </div>
              </div> -->
            </div>


            <div class="h180">
              <div class="topHandle">
                <div class="biaoTi">待办</div>
                <div class="goBtn" @click="openTodoDialog">去处理</div>
              </div>
              <div class="dbNum">
                <div class="dbTitle"><span class="number">{{todoNum}}</span>个待办</div>
              </div>
            </div>

            <div class="h180">
              <div class="topHandle">
                <div class="biaoTi">消息</div>
                <div class="goBtn" @click="openMessageDialog">去处理</div>
              </div>
              <div class="dbNum">
                <div class="dbTitle"><span class="number">{{messageNum}}</span>个消息</div>
              </div>
            </div>








            <!-- <div class="trbox3 mt12 bcg" v-if="showWork">
              <div class="liebg">
                <textBorder class="mt10">工单</textBorder>
                <el-button @click="moreWork" class="mbtn tabbtn more" type="text">
                  更多
                </el-button>
              </div>
              <div class="paing">
                <div
                  class="workitem"
                  v-for="(item, index) in workList"
                  :key="index"
                >
                  <p class="workname" @click="gotoWork(item)">{{ item.content }}</p>
                  <p
                    class="workstatus"
                    :style="{ color: status[item.status].color }"
                  >
                    {{ status[item.status].name }}
                  </p>
                </div>
              </div>
              <div v-if="workList.length == 0" class="nolist">
                <p class="zanwu">暂无记录~</p>
              </div>
            </div> -->
            <!-- <div class="trbox4 mt12 bcg" v-if="showTask">
              <div class="liebg">
                <textBorder class="mt10">项目任务</textBorder>
                <el-button @click="morePro" class="mbtn tabbtn more" type="text">
                  更多
                </el-button>
              </div>
              <div class="paing">
                <div
                  class="taskitem"
                  v-for="(item, index) in taskList"
                  :key="index"
                >
                  <p class="ttitle clearfix">
                    <span class="left tname">{{ item.taskName }}</span>
                    <span class="right goto" @click="gotoTask(item)">前往确认</span>
                  </p>
                  <span class="labeltask">任务内容：</span>
                  <p class="taskcontent">{{ item.taskContent }}</p>
                  <p>
                    <span class="endtime">结束日期：</span>
                    <span class="timec">{{ item.endTime }}</span>
                  </p>
                </div>
              </div>
              <div v-if="taskList.length == 0" class="nolist">
                <img class="noimgtask" src="../../assets/no.png" alt="" />
                <p class="zanwu">暂无记录~</p>
              </div>
            </div> -->
          </div>
        </div>
        </el-tab-pane>
        <el-tab-pane label="个人目标" name="person">

        </el-tab-pane>
        <el-tab-pane label="目标排行" name="top">

        </el-tab-pane>
      </el-tabs>
      <personalGoal v-show="activeName == 'person'"  ref="personalGoal"></personalGoal>
      <goalRank v-show="activeName == 'top'" ref="goalRank"></goalRank>



    <deDialogHome
      ref="deRef"
      :dType="dType"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </deDialogHome>

    <addRemindDialog
      :visible.sync="dialogFormVisible"
      @updateVisible="updateVisible"
      @submitData="remindSubmit"
    ></addRemindDialog>
    <el-dialog
      title="提醒详情"
      :visible.sync="dialogVisibleTip"
      width="40%"
      :before-close="handleClose"
      class="txdialog"
    >
      <div>
        <span class="dbtn" @click="deleteAction(remindInfo)">
          <i class="el-icon-delete"></i>
        </span>
        <p class="ptextr">
          <span>提醒事项：</span>
          <span>{{ remindInfo.title }}</span>
        </p>
        <p class="ptextr">
          <span class="spaninline conp">提醒内容：</span>
          <span class="spaninline spanv">{{ remindInfo.content }}</span>
        </p>
        <p class="ptextr">
          <span>提醒时间：</span>
          <span>{{ remindInfo.remindTime }}</span>
        </p>
      </div>
    </el-dialog>
    <followVisitDetail ref="followvisitdetail"></followVisitDetail>

    <!-- 待办弹框 -->
    <todoDialog :visible.sync="todoDialogVisible"></todoDialog>

    <!-- 消息弹框 -->
    <messageDialog :visible.sync="messageDialogVisible"></messageDialog>
  </div>
</template>
<script>
import addRemindDialog from './common/addRemindDialog.vue'
import todoDialog from './common/todoDialog.vue'
import messageDialog from './common/messageDialog.vue'
import deDialogHome from '@/components/common/deDialogHome.vue'
import textBorder from '../common/textBorder.vue'
import {
  indexHe,
  hoemDetail,
  homeTaskList,
  queryHomeList,
  remindInfo,
  remindDelete,
  queryTodoNum,
  queryMessageNum,
} from '@/api/index'
import { visitList } from '@/api/visit/index'
import { workorderList } from '@/api/workorder/index'
import { checkPermission } from '@/utils/permission'
import followVisitDetail from '@/components/clientMaintenance/followVisit/detail.vue'
import personalGoal from '@/components/goal/show/detail.vue'
import goalRank from '@/components/goal/show/index.vue'
import Bus from "@/utils/EventBus";

export default {
  components: {
    deDialogHome,
    textBorder,
    addRemindDialog,
    todoDialog,
    messageDialog,
    followVisitDetail,
    personalGoal,
    goalRank,
  },
  data() {
    return {
      activeName: 'basicdata',
      actionsMap:{
        'person':'loadPersonData',
        'top':'loadTopData'
      },
      dialogVisibleTip: false,
      dType: '1',
      dataViewList: [
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '新增客户',
          path: '/clientManagement/customer/index',
          permiss: 'crm:controller:customer:list',
        },
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '新增客户单位',
          path: '/clientManagement/unit/index',
          permiss: 'crm:controller:unit:list',
        },
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '跟进与拜访',
          path: '/clientMaintenance/followVisit/index',
          permiss: 'crm:controller:visit:list',
        },
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '样书发放',
          path: '/give/samplebook',
          permiss: 'crm:controller:distribution:list',
        },
      ],
      businessList: [
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '合同',
          path: '/projectManagement/contract/index',
          permiss: 'crm:controller:contract:list',
        },
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '合同金额',
        },
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '未回款金额',
        },
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '销售机会',
          path: '/clientMaintenance/salesLead/index',
          permiss: 'crm:controller:opportunity:list',
        },
      ],
      projectList: [
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '当前项目',
          path: '/project/manager/index',
          permiss: 'crm:controller:project:list',
        },
        {
          imgUrl: require('../../assets/img/home/<USER>'),
          numText: '0',
          text: '项目金额',
        },
      ],
      dialogFormVisible: false,
      dialogVisible: false,
      levels: [],
      levelTypeToList: {
        1: [
          {
            id: 4,
            value: '看自己',
          },
        ],
        2: [
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
          {
            id: 4,
            value: '看自己',
          },
        ],
        3: [
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
          {
            id: 4,
            value: '看自己',
          },
        ],
        4: [
          {
            id: 1,
            value: '全公司',
          },
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
          {
            id: 4,
            value: '看自己',
          },
        ],
      },
      month: [
        {
          id: 9,
          value: '今天',
        },
        {
          id: 10,
          value: '昨天',
        },
        {
          id: 7,
          value: '本周',
        },
        {
          id: 8,
          value: '上周',
        },
        {
          id: 1,
          value: '本月',
        },
        {
          id: 4,
          value: '上月',
        },
        {
          id: 2,
          value: '本季度',
        },
        {
          id: 5,
          value: '上季度',
        },
        {
          id: 3,
          value: '本年度',
        },
        {
          id: 6,
          value: '上年度',
        },
      ],
      heInfo: {},
      hparams: {
        departmentIds: [],
        userIds: [],
        time: 3,
      },
      bInfo: {},
      dataList: [],
      load: false,
      workList: [],
      status: {
        2: {
          name: '待接单',
          color: '#FF8D1A',
        },
      },
      taskList: [],
      deLevel: '',
      showWork: true,
      showGen: true,
      showTask: true,
      tipList: [],
      remindVo: {},
      remindInfo: {},
      messagePar: {
        taskType: 1,
        pageNum: 1,
        pageSize: 3,
      },
      that:null,
      hasNextPage: false,
      todoDialogVisible: false,
      messageDialogVisible: false,
      todoNum: 0,
      messageNum: 0,
    }
  },
  created() {
    this.that = this;
    this.indexHeData()
    this.hoemDetailData()
    this.visitListData()
    this.workorderListData()
    this.homeTaskListData()
    this.levels =
      this.levelTypeToList[sessionStorage.getItem('dataScope')] || []
    this.queryHomeListApi()
    this.getTodoNumApi()
    this.getMessageNumApi()

    // 监听Schedule组件的删除事件，刷新消息数量
    Bus.$on('refreshMessageNum', () => {
      this.getMessageNumApi()
    })
  },
  mounted() {},
  beforeDestroy() {
    // 移除事件监听器
    Bus.$off('refreshMessageNum')
  },
  methods: {
    getTodoNumApi() {
      queryTodoNum().then(res => {
        this.todoNum = res.data
      })
    },
    getMessageNumApi() {
      queryMessageNum().then(res => {
        this.messageNum = res.data
      })
    },
    gotoDetail(item) {
      if (checkPermission(item.permiss)) {
        this.$router.push({
          path: item.path,
        })
      } else {
        this.msgError('没有查看权限')
        return
      }
    },
    gotoDemo() {
      if (checkPermission('crm:controller:case:list')) {
        this.$router.push({
          path: '/studyShare/caseLibrary/index',
        })
      } else {
        this.msgError('没有查看权限')
        return
      }
    },
    remindInfoApi() {
      remindInfo(this.remindInfo.id).then((res) => {
        if (res.status == 0) {
          this.remindInfo = res.data
        }
      })
    },
    remindSubmit() {
      this.messagePar.pageNum = 1
      this.queryHomeListApi()
      // 刷新待办和消息数量
      this.getMessageNumApi()
    },
    showDetail(row) {
      this.dialogVisibleTip = true
      this.remindInfo = row
      this.remindInfoApi()
    },
    handleClose() {
      this.dialogVisibleTip = false
    },
    queryHomeListApi() {
      queryHomeList(this.messagePar).then((res) => {
        if (res.status == 0) {
          if (this.messagePar.pageNum == 1) {
            this.tipList = res.data
          } else {
            this.tipList = this.tipList.concat(res.data)
          }
          this.hasNextPage = res.page.hasNextPage
        }
      })
    },
    changePage() {
      this.messagePar.pageNum++
      this.queryHomeListApi()
    },
    visitMore() {
      if (checkPermission('crm:controller:visit:list')) {
        this.$router.push({
          path: '/clientMaintenance/followVisit/index',
        })
      } else {
        this.msgError('没有查看权限')
        return
      }
    },
    moreWork() {
      if (checkPermission('crm:controller:workorder:list')) {
        this.$router.push({
          path: '/workOrder/index',
        })
      } else {
        this.msgError('没有查看权限')
        return
      }
    },
    morePro() {
      if (checkPermission('crm:controller:project:list')) {
        this.$router.push({
          path: '/project/manager/index',
        })
      } else {
        this.msgError('没有查看权限')
        return
      }
    },
    gotoTask(item) {
      this.$router.push({
        path: '/project/manager/quest/index',
        query: {
          id: item.projectId,
          name: item.projectName,
        },
      })
    },
    changeOne(val) {
      this.hparams.time = val
      this.hoemDetailData()
    },
    changeOp(item) {
      if (item.id == 2) {
        this.dialogVisible = true
        this.$refs.deRef.loadData()
        this.dType = '1'
      } else if (item.id == 3) {
        this.dialogVisible = true
        this.$refs.deRef.loadData()
        this.dType = '2'
      } else if (item.id == 1 || item.id == 4) {
        this.hparams.departmentIds = []
        this.hparams.userIds = []
        if (item.id == 4) {
          this.hparams.userIds = [sessionStorage.getItem('userid')]
        }
        this.hoemDetailData()
      }
    },
    addTip() {
      this.dialogFormVisible = true
    },
    updateVisible() {
      this.dialogFormVisible = false
    },
    gotoWork(item) {
      this.$router.push({
        path: '/workOrder/index',
        query: {
          id: item.id,
        },
      })
    },
    homeTaskListData() {
      let params = {
        chargePerson: sessionStorage.getItem('userid'),
        pageSize: 2,
        taskStatus: 1,
      }
      homeTaskList(params).then((res) => {
        this.taskList = res.data
        this.showTask = true
      })
    },
    workorderListData() {
      workorderList({
        acceptedPerson: sessionStorage.getItem('userid'),
        pageSize: 2,
        status: 2,
      })
        .then((res) => {
          this.workList = res.data
          this.showWork = true
        })
        .catch((res) => {})
    },
    visitDetail(item) {
      console.log('跟进数据', item)
      if (checkPermission('crm:controller:visit:info')) {
        this.$refs.followvisitdetail.show()
        this.$refs.followvisitdetail.loadData(item.id)
      } else {
        this.msgError('没有查看权限')
        return
      }
    },
    visitListData() {
      visitList({ isRemind: 1, pageSize: 4 }).then((res) => {
        this.dataList = res.data
        this.load = true
        this.showGen = true
      })
    },
    hoemDetailData() {
      hoemDetail(this.hparams).then((res) => {
        this.dataViewList[0].numText = res.data.customerNum
        this.dataViewList[1].numText = res.data.unitNum
        this.dataViewList[2].numText = res.data.visitNum
        this.dataViewList[3].numText = res.data.distributionNum

        this.businessList[0].numText = res.data.contractNum
        this.businessList[1].numText = res.data.contractTotalAmonut
        this.businessList[2].numText = res.data.noReturnTotalAmount
        this.businessList[3].numText = res.data.opportunityNum

        this.projectList[0].numText = res.data.projectNum
        this.projectList[1].numText = res.data.projectTotalAmount
        this.bInfo = res.data
      })
    },
    indexHeData() {
      indexHe().then((res) => {
        this.heInfo = res.data
      })
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    submitData(data) {
      this.dialogVisible = false
      if (this.dType == 1) {
        this.hparams.departmentIds = data.map((item) => item.id)
        this.hparams.userIds = []
      } else if (this.dType == 2) {
        let userids = data.map((item) => item.id)
        this.hparams.departmentIds = []
        this.hparams.userIds = userids
      }
      let names = data.map((item) => item.name)
      this.deLevel = names.join(',')
      this.hoemDetailData()
    },
    submitRemind(data) {
      this.dialogFormVisible = false
    },
    deleteRemind(data) {
      remindDelete({ id: data.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            ;(this.dialogVisibleTip = false), (this.remindInfo = {})
            this.messagePar.pageNum = 1
            this.queryHomeListApi()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {
          this.$message({
            type: 'error',
            message: '删除失败',
          })
        })
    },
    deleteAction(data) {
      this.$confirm('此操作将删除该提醒信息, 是否继续?', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          this.deleteRemind(data)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    handleClick(tab) {
      if (tab.name === 'basicdata'){
        this.hoemDetailData()
      }else {
        this.$nextTick(()=>{
          var actionName = this.actionsMap[tab.name]
          console.log('ssssssaaaaa',actionName);
          Bus.$emit(actionName)
        })

      }
    },
    openTodoDialog() {
      this.todoDialogVisible = true
    },
    openMessageDialog() {
      this.messageDialogVisible = true
    }
  },
}
</script>
<style>
.shenglue .el-input__inner {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: normal;
  white-space: nowrap;
}
</style>
<style scoped>
.rtime {
  font-size: 12px;
}
.jichu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.bcolor {
  color: #272629 !important;
}
.htitle {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 24px;
  color: #ffffff;
  line-height: 28px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.hexin {
  background: url('../../assets/databg.png') no-repeat;
  background-size: 100% 100%;
  padding: 24px 20px;
}
.tcenter {
  text-align: center;
  cursor: pointer;
}
.conp {
  line-height: 22px;
}
.ptextr {
  line-height: 28px;
}
.cptext {
  text-align: center;
}
.spanv {
  width: 76%;
  vertical-align: top;
  line-height: 22px;
}
.spaninline {
  display: inline-block;
  vertical-align: top;
}
.visitname {
  width: 134px;
}

.shenlue {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.labeltask {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.more {
  position: absolute;
  right: 19px;
  top: -6px;
}
.tipdiv {
  height: 160px;
  overflow-x: hidden;
}
.newbu {
  position: absolute;
  top: 0px;
  right: 17px;
}
.timec {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #666666;
}
.endtime {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.taskcontent {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #666666;
  line-height: 20px;
  margin-bottom: 8px;
  text-align: justify;
}

.goto {
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  cursor: pointer;
}

.tname {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: bold;
  color: #333333;
}

.ttitle {
  margin-bottom: 12px;
}

.taskitem {
  width: 328px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(204, 204, 204, 0.3);
  padding: 12px;
  margin-bottom: 12px;
}

.workstatus {
  text-align: right;
  flex: 1;
  padding-right: 10px;
  line-height: 34px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #666666;
}

.workname {
  width: 260px;
  line-height: 34px;
  padding-left: 10px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.workname1 {
  width: 200px;
  line-height: 34px;
  padding-left: 10px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.workitem {
  width: 328px;
  height: 34px;
  background: rgba(235, 235, 235, 0.3);
  border-radius: 2px 2px 2px 2px;
  display: flex;
  margin-bottom: 12px;
  cursor: pointer;
}

.c2text {
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  cursor: pointer;
}

.c2 {
  width: 92px;
}

.c3 {
  width: 134px;
}

.nolist {
  padding-top: 20px;
}

.noimg {
  margin: 0 auto;
  display: block;
}

.noimgtask {
  margin: 0 auto;
  margin-top: 60px;
  display: block;
}

.zanwu {
  text-align: center;
}

.htimg {
  position: absolute;
  bottom: 30px;
  right: 24px;
  width: 40px;
  height: 40px;
}

.leiji {
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #e6e6e6;
}

.numc {
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
  color: white;
  margin-bottom: 12px;
  display: block;
  height: 33px;
}

/* .hnum {
    color: #4285F4;
  }

  .hnum2 {
    color: #FA8544;
  }

  .hnum3 {
    color: #55C36E;
  }

  .hnum4 {
    color: #F36969;
  }

  .hnum5 {
    color: #A25EFA;
  } */

.hp {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #e6e6e6;
  margin-bottom: 16px;
}

.paing {
  padding: 0 16px;
}

.table-e2 {
  width: 100%;
  height: 170px;
  display: table;
  box-sizing: border-box;
  border-collapse: collapse;
}

.table-e2 .tr {
  text-align: center;
  height: 34px;
}

.table-e2 .tr .cell {
  display: table-cell;
  vertical-align: middle;
  height: 34px;
}

.table-e2 .table-header {
  display: table-header-group;
  height: 34px;
}

.table-header .tr .cell {
  font-weight: bold;
}

.table-e2 .table-tbody {
  display: table-row-group;
}

.table-tbody .tr:nth-child(odd) {
  background: rgba(235, 235, 235, 0.3);
}

.table-tbody .tr:hover {
  background: #eee;
}

.table-tbody .tr :last-child {
  width: 134px;
}

.table-tbody .tr :last-child a {
  margin: 0 5px;
  cursor: pointer;
  text-decoration: none;
}

.mt10 {
  margin-top: 3px;
}

.liebg {
  background: url('../../assets/liebg.png') no-repeat;
  height: 42px;
  margin-top: 12px;
  padding-left: 16px;
  background-size: 100% 100%;
  overflow: hidden;
  margin-bottom: 6px;
  position: relative;
}
.topHandle {
  background: url('../../assets/liebg.png') no-repeat;
  height: 42px;
  margin-top: 8px;
  padding: 12px 15px;
  background-size: 100% 100%;
  overflow: hidden;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ptitle {
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-top: 16px;
  margin-left: 20px;
}

.titletext {
  margin-bottom: 30px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.numtext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.numcolor {
  font-size: 20px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
}

.imgf {
  margin-left: 14px;
  margin-right: 10px;
  width: 40px;
  height: 40px;
}

.piececss {
  position: relative;
  width: 25%;
  padding: 30px 0;
  float: left;
}
.csscursor {
  cursor: pointer;
}

.trbox1 {
  height: 237px;
}

.trbox2 {
  height: 59px;
  position: relative;
}

.trbox3 {
  height: 157px;
}

.trbox4 {
  padding-bottom: 10px;
  min-height: 341px;
}

.mt12 {
  margin-top: 12px;
}

.top2 {
  height: 158px;
}

.bcg {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  overflow: hidden;
}

.top3 {
  height: 172px;
}

.top4 {
  height: 172px;
}

.top5 {
  height: 172px;
}

.ulc {
  display: flex;
}

.lic {
  width: 19.6%;
  margin-right: 0.5%;
  box-sizing: border-box;
  position: relative;
}

.lic:last-child {
  margin-right: 0;
}

.leftbox {
  min-width: 714px;
  flex: 1 1;
  margin-right: 12px;
}

.allbox {
  display: flex;
}

.rightbox {
  width: 360px;
}

.mb20 {
  margin-bottom: 20px;
}

.times {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
}

.ml16 {
  margin-left: 16px;
}

.xiala {
  width: 200px;
}

.xiaicon {
  margin-top: 10px;
}

.xiala {
  width: 210px;
  height: 34px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: none;
}

h1 {
  margin-bottom: 100px;
}
.txdialog /deep/.el-dialog__body {
  position: relative;
}
.dbtn {
  font-size: 20px;
  color: #f36969;
  position: absolute;
  right: 16px;
  top: 16px;
}
.biaoTi{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #58565C;
}
.flex{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.goBtn{
  width: 62px;
  height: 24px;
  background: #4285F4;
  border-radius: 4px 4px 4px 4px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px 10px;
  box-sizing: border-box;
}
.goBtn:hover{
  background: #66B1FF;
}
.h180{
  height: 180px;
  background: rgba(255,255,255,0.72);
  border-radius: 12px 12px 12px 12px;
  border: 2px solid #FFFFFF;
  margin-top: 16px;
}
.dbNum{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 19px;
}
.dbTitle{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #89868F;
}
.number{
  font-family: DIN, DIN;
  font-weight: 500;
  font-size: 42px;
  color: #272629;
  margin-right: 6px;
}
</style>
