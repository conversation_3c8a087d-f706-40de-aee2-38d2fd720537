<template>
  <div>
    <div>
        <back>返回</back>
    </div>
      <el-card v-loading="isLoading" class="mt10">
          <el-form label-width="85px" class="myform " >
              <el-row :gutter="10" type="flex" justify="start">
                <el-col :span="7">
                  <el-form-item label="数量：" label-width="60px">
                    <el-select class="definput nums" popper-class="removescrollbar"  v-model="pageBean.searchType"
                        placeholder="请选择">
                        <el-option v-for="item in searchList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                    <div class="numdiv">
                        <el-input @input="handleEdit" class="wip1" v-model="pageBean.searchMin" type="number">
                            <span slot="suffix">册</span>
                        </el-input>
                        <div class="form-center">--</div>
                        <el-input @input="handleEdit1" class="wip1" v-model="pageBean.searchMax"   type="number"  >
                            <span slot="suffix">册</span>
                        </el-input>
                    </div>
                  </el-form-item>
                </el-col>
                <el-form-item label="用书时间：">
                    <el-select @change="changeTime" clearable="" class="definput usetimecss" popper-class="removescrollbar" v-model="useBookYear"
                    placeholder="请选择">
                        <el-option v-for="item in timeOptions" :key="item.id" :label="item.name" :value="item.name">
                        </el-option>
                    </el-select>
                </el-form-item>
                  <el-col :span="5">
                    <el-form-item label="用书专业：">
                        <el-select @change="changeMajor" clearable="" class="definput" popper-class="removescrollbar" v-model="specialtys"
                        placeholder="请选择专业">
                          <el-option v-for="item in optionsMajor" :key="item.id" :label="item.name" :value="item.id">
                          </el-option>
                      </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="出版社：" >
                      <el-input class="definput " v-model="pageBean.platformName" clearable placeholder="请输入"></el-input>
                  </el-form-item>
              </el-col>
              <el-col :span="2">
                <el-button class="defaultbtn1 mt" icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
            </el-col>
              </el-row>
          </el-form>
          <el-table @sort-change="sortChange" class="customertable mytable tootiptable" :data="tableData" style="width: 100%">
            <el-table-column prop="specialtyNames" label="用书专业" align="center" >
            </el-table-column>
            <el-table-column prop="useBookYear" label="用书时间" align="center" >
            </el-table-column>
              <el-table-column sortable="custom" prop="yongliangNum" label="用书量" align="center" >
              </el-table-column>
              <el-table-column sortable="custom" prop="joinNum"  label="合作" align="center" >
              </el-table-column>
              <el-table-column sortable="custom" prop="notJoinNum" label="非合作" align="center" >
              </el-table-column>
              <el-table-column width="120" sortable="custom" prop="planRecruitNumber" label="计划招生" align="center" >
              </el-table-column>
              <el-table-column width="120" sortable="custom"  prop="realityRecruitNumber" align="center" label="实际招生" >
              </el-table-column>
              <el-table-column  prop="materialName" align="center" label="教材名称" >
              </el-table-column>
              <el-table-column  prop="platformName" align="center" label="出版社" >
              </el-table-column>
              <el-table-column  prop="isbn" align="center" label="ISBN" >
              </el-table-column>
              <el-table-column  prop="price" align="center" label="价格" >
              </el-table-column>
              <template slot="empty">
                  <nolist></nolist>
              </template>
          </el-table>
          <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
              @updatePageNum="handleCurrentChange"></page>
      </el-card>
  </div>
  
</template>

<script>
  import back from '../../common/back.vue';
  import page from '@/components/common/page.vue';
  import nolist from '@/components/common/nolist.vue';
  import { queryDataInfo,  } from "@/api/bigdata/index";
  export default {
      components: {
          page,
          nolist,
          back,
      },
      data() {
          return {
              timeOptions:[],
              optionsMajor:[],
              isLoading: false,
              tableData: [],
              total: 0,
              pageBean: {
                  materialName:'',
                  useBookYear:'',
                  pageNum: 1,
                  pageSize: 10,
                  searchMin: '',
                  searchMax: '',
                  specialtys:'',
                  platformName:'',
                  searchType:'1',
                  unitId:''
              },
              useBookYear:'',
              specialtys:'',
              searchList:[
                {
                    id:'1',
                    name:'用书量'
                },
                {
                    id:'2',
                    name:'合作'
                },
                {
                    id:'3',
                    name:'非合作'
                },
            ],
            rankType:1,
            updateRankType:{
                  'ascending:yongliangNum' :1,
                  'descending:yongliangNum' :2,
                  'ascending:joinNum' :3,
                  'descending:joinNum' :4,
                  'ascending:notJoinNum' :5,
                  'descending:notJoinNum' :6,
                  'ascending:planRecruitNumber' :7,
                  'descending:planRecruitNumber' :8,
                  'ascending:realityRecruitNumber' :9,
                  'descending:realityRecruitNumber' :10,
            }
          }
      },
      created() {
          this.pageBean.useBookYear = this.$route.query.useBookYear
          this.pageBean.unitId = this.$route.query.unitId
          if(sessionStorage.getItem('specialtys')){
            this.pageBean.specialtys = sessionStorage.getItem('specialtys')
          }
          if(sessionStorage.getItem('majorList')){
                this.optionsMajor = JSON.parse(sessionStorage.getItem('majorList'))
          }
          if(this.$route.query.materialName){
            this.pageBean.materialName = this.$route.query.materialName
          }
          if(this.$route.query.platformName){
            this.pageBean.platformName = this.$route.query.platformName
          }
          if(this.$route.query.useBookYear){
            let arr = []
            this.timeOptions.push({
                id:'1',
                name:this.$route.query.useBookYear+'年春季'
            })
            this.timeOptions.push({
                id:'2',
                name:this.$route.query.useBookYear+'年秋季'
            })
          }
          this.loadData();
         
      },
      methods: {
        handleEdit(e) {
            let value = e.replace(/[^\d]/g, ""); // 只能输入数字
            value = value.replace(/^0+(\d)/, "$1"); // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
            value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
            this.pageBean.searchMin = value
        },
        handleEdit1(e) {
            let value = e.replace(/[^\d]/g, ""); // 只能输入数字
            value = value.replace(/^0+(\d)/, "$1"); // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
            value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
            this.pageBean.searchMax = value
        },
        changeTime(val){
            if(val){
              this.pageBean.useBookYear = val
            }else{
                this.pageBean.useBookYear = this.$route.query.useBookYear
            }
        },
        changeMajor(val){
            if(val){
                this.pageBean.specialtys = val
            }else{
                this.pageBean.specialtys = sessionStorage.getItem('specialtys')
            }
        },
        sortChange(column) {
            let props = column.order+':'+column.prop
            this.pageBean.pageNum = 1
            this.pageBean.rankType = this.updateRankType[props]
            this.loadData();
        },
          loadData() {
              this.isLoading = true;
              queryDataInfo(this.pageBean).then((result) => {
                  this.tableData = result.data ? result.data : [];
                  this.total = result.data ? result.page.total : 0
                  this.isLoading = false;
              }).catch((err) => {
                  this.isLoading = false;
              });
          },
          searchAction() {
            if(this.pageBean.searchMax==0 && this.pageBean.searchMax!=''){
                        this.msgError('最大值必须大于0')
                        return 
            }
            if((+this.pageBean.searchMin>=0 &&this.pageBean.searchMin!='') || (+this.pageBean.searchMax>=0&&this.pageBean.searchMax!='')){
                    if((+this.pageBean.searchMin>=0 &&this.pageBean.searchMin!='') && this.pageBean.searchMax==''){
                        this.msgError('请输入数量的最大值')
                        return 
                    }
                    if((+this.pageBean.searchMin>=0 &&this.pageBean.searchMin!='') && this.pageBean.searchMax!='' && +this.pageBean.searchMax<=+this.pageBean.searchMin){
                        this.msgError('最大值得大于最小值')
                        return 
                    }
                    if(this.pageBean.searchMin=='' && (this.pageBean.searchMax!='' &&this.pageBean.searchMax>=0)){
                        this.msgError('请输入数量的最小值')
                        return 
                    }
            }
            if(this.pageBean.platformName == ''){
                    if(this.$route.query.platformName){
                        this.pageBean.platformName = this.$route.query.platformName
                    }
            }
              this.pageBean.pageNum = 1;
              this.loadData();
          },
          handleCurrentChange(page) {
              this.pageBean.pageNum = page;
              this.loadData();
          },
      }
  }
</script>
<style scoped>
     .yearcss /deep/.el-input__inner{
        height: 34px;
    }
        .wip1{
        min-width: 40px;
        width: calc(50% - 10px);
        height: 34px;
    }
    .wip1 /deep/.el-input__inner{
       height: 28px;
       position: relative;
       top: -4px;
       line-height: 1px !important;
    }
    .wip1 /deep/.el-input__suffix{
        height: 28px;
        line-height: 34px;
    }
       .nums /deep/.el-input__inner{
      padding-right: 10px !important;
      padding-left: 6px !important;
    }
    .nums /deep/.el-input__suffix{
        height: 34px;
    }
    .definput /deep/.el-input__icon {
        line-height: 32px;
        height: 32px;
    }
    .usetimecss{
        width: 106px;
    }
  .nums{
      width: 80px;
      min-width: 80px;
      display: inline-block;
      vertical-align: top;
  }

    .numdiv{
        height: 34px;
        border: 1px solid   #DCDFE6;
        padding: 0 4px;
        display: inline-block;
        margin-top: 3px;
        width: calc(100% - 90px);
        margin-left: 6px;
    }
    
        .form-center{
            display: inline-block;
            height: 34px;
            width: 20px;
            text-align: center;
            line-height: 34px;
            vertical-align: top;
        }
    .form-conten{
      position: relative;
    }  
    .el-date-editor.el-input{
            width: 100%;
    }
    .useTime{
        width: 120px;
        display: inline-block;
        margin-right: 20px;
    }
.defaultbtn1 {
  padding: 9px 10px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
  .mt {
      margin-top: 4px;
  }
  .customertable .el-button {
      padding: 2px;
  }

  .right {
      text-align: right;
  }
</style>