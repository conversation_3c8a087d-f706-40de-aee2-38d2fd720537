<template>
  <div class="fixpb mainbg">
    <el-card>
      <div v-loading="isLoading">
        <el-form :inline="true" label-width="85px" class="myform">
          <el-form-item label="">
            <el-input
              class="definput iw"
              v-model="pageBean.name"
              clearable
              placeholder="教材名称或出版社名称或ISBN"
            ></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-input
              class="definput wfi"
              v-model="pageBean.operationName"
              clearable
              placeholder="请输入业务经理姓名"
            ></el-input>
          </el-form-item>
          <el-button
            class="defaultbtn mt"
            icon="el-icon-search"
            type="primary"
            @click="searchAction"
            >搜索
          </el-button>
          <el-button
            v-isShow="'crm:controller:customer:save'"
            class="defaultbtn mt fr"
            icon="el-icon-plus"
            type="primary"
            @click="addCustomer"
            >新增</el-button
          >
          <el-upload
            class="mt ml fr"
            :action="getUrl"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="handleAvatarSuccess"
            :on-error="handleError"
            :headers="headers"
            :data="fileData"
            accept=".xlsx"
          >
            <el-button
              class="defaultbtn"
              icon="el-icon-upload2"
              type="primary"
              :loading="isImport"
              >导入</el-button
            >
          </el-upload>
          <el-button
            class="defaultbtn mt fr"
            icon="el-icon-download"
            type="primary"
            @click="download"
            :loading="isDownload"
            >下载模版</el-button
          >
        </el-form>
        <el-table class="customertable mytable tootiptable" :data="tableData">
          <el-table-column
            prop="name"
            label="教材名称"
            align="center"
            min-width="250px"
          >
          </el-table-column>
          <el-table-column
            prop="platformName"
            label="出版社名称"
            align="center"
            width="200px"
          >
          </el-table-column>
          <el-table-column prop="author" label="主编" align="center">
          </el-table-column>
          <el-table-column
            prop="isbn"
            label="ISBN"
            align="center"
            width="150px"
          >
          </el-table-column>
          <el-table-column prop="price" label="价格" align="center">
          </el-table-column>
          <el-table-column
            prop="publicationRevisionTime"
            label="出版日期"
            align="center"
            width="120px"
          >
          </el-table-column>
          <el-table-column prop="operationName" label="业务经理" align="center">
            <template slot-scope="scope">
              {{
                scope.row.operationName == '' ? '暂无' : scope.row.operationName
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="edit"
            width="160"
            align="center"
            fixed="right"
            label="操作"
          >
            <template slot-scope="scope">
              <!-- <el-button class="bbtn" type="text"
                            @click="toBook(scope.row)"> 详情 </el-button>  -->
              <el-button class="bbtn" type="text" @click="onEdit(scope.row)">
                编辑</el-button
              >
              <el-button
                class="rbtn"
                type="text"
                @click="deleteAction(scope.row)"
              >
                删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <nolist></nolist>
          </template>
        </el-table>

        <div class="fixpage">
          <page
            :currentPage="pageBean.pageNum"
            :total="total"
            :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"
          ></page>
        </div>

        <dc-dialog
          iType="1"
          title="温馨提示"
          width="500px"
          :dialogVisible.sync="dialogDeleteVisible"
          @submit="deleteButton"
        >
          <template> </template>
          <p class="pcc">是否删除该教材？删除后将无法恢复。</p>
        </dc-dialog>
      </div>
    </el-card>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogTableVisible"
      :before-close="handleClose"
      class="cDialog"
      width="40%"
      center
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="90px"
        class="demo-ruleForm"
      >
        <el-form-item label="教材名称" prop="name">
          <el-input
            maxlength="30"
            show-word-limit
            placeholder="请输入教材名称"
            class="wcss guang"
            v-model="ruleForm.name"
          ></el-input>
        </el-form-item>
        <el-form-item label="出版社" prop="platformId">
          <el-select
            class="wcss"
            v-model="ruleForm.platformId"
            placeholder="请选择出版社"
          >
            <el-option
              v-for="item in optionsPress"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主编" prop="author">
          <el-input
            placeholder="请输入主编"
            class="wcss guang"
            maxlength="10"
            v-model="ruleForm.author"
          ></el-input>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input
            type="number"
            @input="formatNum(ruleForm.price, 'price')"
            class="wcss"
            v-model="ruleForm.price"
            placeholder="请输入价格(单位：元) "
          ></el-input>
        </el-form-item>
        <el-form-item label="ISBN" prop="isbn">
          <el-input
            class="wcss"
            v-model="ruleForm.isbn"
            placeholder="请输入标准书号（ISBN） "
          ></el-input>
        </el-form-item>
        <el-form-item label="出版日期" prop="publicationRevisionTime">
          <el-date-picker
            format="yyyy-MM"
            value-format="yyyy-MM"
            v-model="ruleForm.publicationRevisionTime"
            type="month"
            class="wcss"
            placeholder="选择年月"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="业务经理">
          <div
            class="unitbtn"
            :class="{ selectedCss: ruleForm.operationManager }"
            @click="chooseCustomer"
          >
            {{
              ruleForm.operationName ? ruleForm.operationName : '请选择业务经理'
            }}
            <i class="rcenter el-icon-arrow-down pscss" />
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitButton">确 定</el-button>
      </span>
    </el-dialog>
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
    <!-- 导入提示 -->
    <dc-dialog
      iType="2"
      title="导入信息提示"
      class="mydialog"
      width="500px"
      :showCancel="false"
      :dialogVisible.sync="dialogMsgVisible"
      @submit="submitMsgDialog"
      :appendToBody="true"
    >
      <template> </template>
      <p v-for="(item, index) in errorData" :key="index">
        {{ item }}
      </p>
    </dc-dialog>
  </div>
</template>
<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import {
  bookList,
  queryPress,
  addBook,
  updateBook,
  queryBookInfo,
  deleteBook,
  pressImportTemplate,
} from '@/api/product/index'
import { queryVoListByCode } from '@/api/wapi.js'
import { getDict, downloadFileByUrl, downloadExcelFile } from '@/utils/tools'
import systemDialog from '@/components/common/systemDialog.vue'
import { getToken } from '@/utils/auth'
export default {
  components: {
    page,
    nolist,
    systemDialog,
  },
  data() {
    return {
      dialogTitle: '',
      dialogName: '选择业务经理',
      multipleNum: 1,
      dialogVisible: false,
      dType: '2',
      optionsPress: [],
      dialogTableVisible: false,
      dialogDeleteVisible: false,
      dialogType: '1',
      deleteBookId: '',
      isShowCancel: true,
      typename: '',
      levels: [],
      isLoading: false,
      tableData: [],
      total: 0,
      deletemsg: '',
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        operationName: '',
      },
      ruleForm: {
        name: '',
        author: '',
        publicationRevisionTime: '',
        isbn: '',
        price: undefined,
        platformId: '',
        operationManager: '',
      },
      rules: {
        name: [{ required: true, message: '请输入教材名称', trigger: 'blur' }],
        platformId: [
          { required: true, message: '请选择出版社', trigger: 'change' },
        ],
        author: [{ required: true, message: '请输入主编', trigger: 'blur' }],
        price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
        isbn: [
          { required: true, message: '请输入标准书号(ISBN)', trigger: 'blur' },
        ],
      },
      errorData: [],
      isDownload: false,
      dialogMsgVisible: false,
      isImport: false,
      getUrl: `${process.env.VUE_APP_BASE_API}/crm/business/teachingmaterial/batchImportExcel`,
      headers: { Authorization: getToken() },
      fileData: {
        applicationId: sessionStorage.getItem('applicationId'),
      },
    }
  },
  created() {
    this.loadData()
    this.queryPressApi()
  },
  methods: {
    formatNum(val, key) {
      let temp = val.toString()
      temp = temp.replace(/。/g, '.')
      temp = temp.replace(/[^\d.]/g, '') //清除"数字"和"."以外的字符
      temp = temp.replace(/^\./g, '') //验证第一个字符是数字
      temp = temp.replace(/\.{2,}/g, '') //只保留第一个, 清除多余的
      temp = temp.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      temp = temp.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') //只能输入两个小数
      if (temp.toString().length > 7) {
        temp = temp.toString().slice(0, 7)
      }
      this.ruleForm[key] = temp
    },
    queryPressApi() {
      queryVoListByCode({ code: 'Press' }).then((res) => {
        if (res.status == 0) {
          this.optionsPress = res.data
        }
      })
    },
    submitData(data) {
      this.ruleForm.operationManager = data.length > 0 ? data[0].id : ''
      this.ruleForm.operationName = data.length > 0 ? data[0].name : ''
      this.updateSystemVisible(false)
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    chooseCustomer() {
      this.$refs.systemdialog.loadData()
      this.ruleForm.operationManager
        ? this.$refs.systemdialog.updateWorksId([
            {
              id: this.ruleForm.operationManager,
              name: this.ruleForm.operationName,
            },
          ])
        : this.$refs.systemdialog.updateWorksId([])
      this.dialogVisible = true
    },
    submitButton() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (this.ruleForm.price.length > 9) {
          this.$message({
            type: 'error',
            message: '价格长度超出限制',
          })
          return
        }
        if (valid) {
          let result
          if (this.ruleForm.id) {
            result = await updateBook(this.ruleForm)
          } else {
            result = await addBook(this.ruleForm)
          }
          if (result.data) {
            this.$message({
              type: 'success',
              message: this.ruleForm.id ? '更新成功！' : '添加成功！',
            })
            this.loadData()
            this.handleClose()
          } else {
            this.$message({
              type: 'error',
              message: '保存失败！',
            })
          }
        } else {
          return false
        }
      })
    },
    loadData() {
      this.isLoading = true
      bookList(this.pageBean)
        .then((result) => {
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    onEdit(data) {
      queryBookInfo(data.id).then((result) => {
        if (result.data) {
          this.dialogTableVisible = true
          this.ruleForm = result.data

          this.dialogTitle = '编辑教材'
        } else {
          this.$message({
            type: 'error',
            message: result.msg,
          })
        }
      })
    },
    handleClose() {
      Object.keys(this.ruleForm).forEach((item) => {
        this.ruleForm[item] = ''
      })
      this.$refs.ruleForm.resetFields()
      this.dialogTableVisible = false
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    deleteButton() {
      deleteBook({ id: this.deleteBookId }).then((result) => {
        if (result.data) {
          this.loadData()
          this.deleteBookId = ''
          this.dialogDeleteVisible = false
        }
      })
    },
    addCustomer() {
      this.dialogTableVisible = true
      this.dialogTitle = '新增教材'
    },
    toBook(data) {
      this.$router.push({
        path: '/clientManagement/customer/bookinfo',
        query: { id: data.id },
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    deleteAction(data) {
      ;(this.deleteBookId = data.id), (this.dialogDeleteVisible = true)
    },
    download() {
      this.isDownload = true
      pressImportTemplate()
        .then((result) => {
          downloadFileByUrl(result.data.url, '教材导入模版')
          this.isDownload = false
        })
        .catch((err) => {
          this.isDownload = false
        })
    },
    submitMsgDialog() {
      this.errorData = []
      this.dialogMsgVisible = false
    },
    beforeUpload(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['xlsx']
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error('导入明细仅支持 .xlsx 格式!')
        return false
      }
      this.isImport = true
    },
    handleError(file, res) {
      this.isImport = false
    },
    handleAvatarSuccess(res, file) {
      if (res.status == 0 && res.data.errorCount <= 0) {
        this.$message({
          type: 'success',
          message: res.msg,
        })
      } else {
        this.$message({
          type: 'error',
          message: res.msg,
        })
        // 显示错误提示的弹框
        this.dialogMsgVisible = true
        this.errorData = res.data.errorData
      }
      // 刷新数据
      this.loadData()
      this.isImport = false
    },
  },
}
</script>
<style scoped>
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: calc(100vh - 106px);
}
.pscss {
  position: absolute;
  right: 9px;
  top: 10px;
  color: #c0c4cc;
}
.guang /deep/.el-input__inner {
  line-height: 1px !important;
}
.wcss {
  width: 280px;
}
.cDialog /deep/.el-dialog__body {
  display: flex;
  justify-content: center;
  align-content: center;
}
.cDialog /deep/.el-dialog {
  width: 580px !important;
}
.iw {
  width: 230px;
  height: 34px;
}
.iw /deep/.el-input__inner {
  padding-right: 6px;
}
.w100 {
  width: 100px;
}
.wfi {
  width: 180px;
}
.mt {
  margin-top: 4px;
}
.mr10 {
  margin-right: 10px;
}
.right {
  text-align: right;
}
.defaultbtn + .defaultbtn {
  margin-right: 10px;
}
.ml {
  margin-left: 10px;
}
.mydialog {
  width: 800px !important;
}
.mydialog /deep/.el-dialog__body {
  height: calc(100% - 140px);
  overflow-y: auto;
}
</style>