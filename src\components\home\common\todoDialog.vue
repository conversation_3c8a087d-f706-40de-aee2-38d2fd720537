<template>
  <el-dialog
    title="待办"
    :visible.sync="dialogVisible"
    width="884px"
    center
    :before-close="handleClose"
    class="todo-dialog"
  >
    <div class="todo-content">
      <left-tabs
        :tabs="todoTabs"
        :active-tab="activeTab"
        @tab-change="handleTabChange"
      >
        <template #default="{ activeTab }">
          <component :is="currentComponent" :key="componentKey" />
        </template>
      </left-tabs>
    </div>

  </el-dialog>
</template>

<script>
import LeftTabs from './LeftTabs.vue'
import ContractInvoice from '../toDo/ContractInvoice.vue'
import returnFunds from '../toDo/returnFunds.vue'
import ContractRefund from '../toDo/ContractRefund.vue'
import ProjectCost from '../toDo/ProjectCost.vue'
import BookOrder from '../toDo/BookOrder.vue'
import WorkOrder from '../toDo/WorkOrder.vue'
import Plan from '../toDo/Plan.vue'
import Summary from '../toDo/Summary.vue'

export default {
  name: 'TodoDialog',
  components: {
    LeftTabs,
    ContractInvoice,
    returnFunds,
    ContractRefund,   
    ProjectCost,
    BookOrder,
    WorkOrder,
    Plan,
    Summary
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      activeTab: 1,
      componentKey: 0,
      todoTabs: [
        { label: '合同开票', value: 1 },
        { label: '合同回款', value: 2 },
        { label: '合同退款', value: 3 },
        { label: '项目成本', value: 4 },
        { label: '教材报订', value: 5 },
        { label: '工单', value: 6 },
        { label: '计划', value: 7 },
        { label: '总结', value: 8 },
      ]
    }
  },
  computed: {
    currentComponent() {
      const componentMap = {
        1: 'ContractInvoice',
        2: 'returnFunds',
        3: 'ContractRefund',
        4: 'ProjectCost',
        5: 'BookOrder',
        6: 'WorkOrder',
        7: 'Plan',
        8: 'Summary'
      }
      return componentMap[this.activeTab] || 'ContractInvoice'
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        // 弹框打开后，强制重新渲染组件
        this.componentKey++
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.activeTab = 1 
      this.$emit('update:visible', false)
    },
    handleTabChange(tab) {
      this.activeTab = tab.value
      console.log('切换到标签页:', tab)
    }
  }
}
</script>

<style lang="scss" scoped>
.todo-content {
  height: 500px;
}
:deep(){
  .el-dialog__body{
    padding: 0 !important;
  }
}
</style>
