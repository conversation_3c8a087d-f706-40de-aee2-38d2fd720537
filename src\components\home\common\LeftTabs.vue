<template>
  <div class="left-tabs-container">
    <div class="tabs-sidebar">
      <div 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab-item', { 'active': activeTab === tab.value }]"
        @click="handleTabClick(tab)"
      >
        {{ tab.label }}
      </div>
    </div>
    <div class="tab-content">
      <slot :activeTab="activeTab">
        <div class="content">
          123
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LeftTabs',
  props: {
    // 标签页数据数组
    tabs: {
      type: Array,
      required: true,
      default: () => []
    },
    // 当前激活的标签页
    activeTab: {
      type: [String, Number],
      default: ''
    },
    // 标签页宽度
    tabWidth: {
      type: String,
      default: '150px'
    }
  },

  methods: {
    handleTabClick(tab) {
      if (tab.value !== this.activeTab) {
        this.$emit('tab-change', tab)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.left-tabs-container {
  display: flex;
  height: 100%;

  .tabs-sidebar {
    width: v-bind(tabWidth);
    border-right: 1px solid #F0F0F0;
    overflow-y: auto;

    .tab-item {
      padding: 16px 20px;
      cursor: pointer;
      border-bottom: 1px solid #F0F0F0;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      transition: all 0.3s;
      text-align: center;

      &:hover {
        background-color: #EBF3FF;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 14px;
        color: #4285F4;
      }

      &.active {
        background-color: #EBF3FF;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 14px;
        color: #4285F4;
      }
    }
  }

  .tab-content {
    flex: 1;
    padding: 20px;
    background-color: #ffffff;
    overflow-y: auto; 
  }
}
</style>
