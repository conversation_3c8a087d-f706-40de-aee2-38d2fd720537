import request from '@/utils/request'

//公告列表
export function fetchGonggaoList(params) {
    return request({
        method: 'get',
        url: '/crm/controller/version/list',
        params
    });
}

// 保存公告
export function saveGonggao(data) {
    return request({
        method: 'post',
        url: '/crm/controller/version/save',
        data,
        headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
    });
}

// 删除公告
export function deleteGonggao(id) {
    return request({
        method: 'get',
        url: `/crm/controller/version/delete`,
        params: { id }
    });
}

// 获取公告详情
export function fetchGonggaoInfo(id) {
    return request({
        method: 'get',
        url: `/crm/controller/version/info/${id}`
    });
}

// 更新公告
export function updateGonggao(data) {
    return request({
        method: 'post',
        url: '/crm/controller/version/update',
        data,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
    });
}

//意见反馈列表  
export function fetchFeedBackList(params) {
    return request({
        method: 'get',
        url: '/crm/controller/feedback/list',
        params
    });
}