<template>
  <div>
      <div>
          <back>{{'预览'}}</back>
          <div class="rightdiv " >
            <div  class="tright">
             <div class="bbtn" id="planreview"> 
               <i class="el-icon-notebook-2" alt=""/>
               计划回顾</div>
               <div class="bbtn" id="summaryreview"  > 
               <i class="el-icon-notebook-2" alt=""/>
               总结回顾</div>
            </div>
         </div> 
      </div>
      <div class="mainbg">
          <div class="fleft">
        <div class="titlefl" v-loading="isLoading">
            <div class="titlecss">{{temText[templateType]}}</div>
         </div>
         <el-row :gutter="12">
           <el-col :span="4" v-for="(item,index) in list" :key="index">
             <dataitem :item="item"></dataitem>
           </el-col>
         </el-row>
         <!-- <div class="goallist mt50">
           <div class="goalitem flex" v-for="(item,index) in list" :key="index">
             <img class="imgcss" :src="item.icon" alt="" >
             <div class=" width100 pl12">
               <div class="textcss">距离目标达成还差{{item.residueGoal}}{{item.unit}}{{type[item.id]}}</div>
               <el-progress class="progress" :percentage="getPercentage(item)" :format="format"></el-progress>
             </div>
           </div>
         </div> -->
        <template v-if="templateType==4">
          <div class="titlecss">单位年度总结</div>
          <div class="listcss" >
               <dataitem2 class="itemcss"  v-for="(item,index) in list1" :key="index" :item="item"></dataitem2>
           </div>
           <div class="tablebox">
               <el-table class=" mytable" v-for="(item,index) in unitGoalList" :key="index"  :data="item" :span-method="objectSpanMethod" border >
                 <el-table-column prop="unitName" label="学校" width="200">
                   <template slot-scope="scope">
                     <div>{{ scope.row.unitName }} </div>
                   </template>
                 </el-table-column>
                 <el-table-column prop="unitgoalTypeName" label="目标维度" ></el-table-column>
                 <el-table-column prop="goalNum" label="目标">
                   <template v-slot="{ row }">
                       {{row.goalNum}}
                   </template>
                 </el-table-column>
                 <el-table-column prop="goalNum" label="达成">
                   <template v-slot="{ row }">
                     {{row.reachNum}}
                   </template>
                 </el-table-column>
             </el-table>
           </div>
        </template>
        <viewmodelcom></viewmodelcom>
      </div>
      </div>
  </div>
</template>

<script>
import dataitem from '../components/dataitem.vue';
import dataitem2 from '../components/dataitem2.vue';
import back from '../../common/back.vue';
import  viewmodelcom from './viewmodelcom.vue'
  export default {
      components: {
        dataitem,
        dataitem2,
        back,
        viewmodelcom
      },
      data() {
          return {
            temText:{
              4:'个人年度总结',
              5:'个人月度总结',
              6:'个人周总结',
            },
            viewDataList: [],
            ruleForm: {
              worksSchool: '',
              activityId: '',
              specialtyId: '',
              specialtyName: '',
              worksName: '',
            },
            list:[
              {id:2,name:"新增拜访与跟进",goal:4,finishGoal:1,icon:require('../../../assets/img2.png'),unit:"次",color:"#4A8BF6",finishRate:0.25,residueGoal:3},
              {id:1,name:"新增客户",goal:5,finishGoal:1,icon:require('../../../assets/img5.png'),unit:"个",color:"#F46D40",finishRate:0.2,residueGoal:4},
              {id:3,name:"业绩",goal:10,finishGoal:1,icon:require('../../../assets/xiaoshou.png'),unit:"万元",color:"#56C36E",finishRate:0.1,residueGoal:9},
              {id:5,name:"样书发放",goal:10,finishGoal:5,icon:require('../../../assets/img3.png'),unit:"次",color:"#E85D5D",finishRate:0.5,residueGoal:5},
              {id:6,name:"回款金额",goal:100,finishGoal:100,icon:require('../../../assets/img4.png'),unit:"万元",color:"#FF8D1A",finishRate:1,residueGoal:0},
              {id:4,name:"新增合同",goal:4,finishGoal:3,icon:require('../../../assets/img/hetong_icon.png'),unit:"个",color:"#4A8BF6",finishRate:0.75,residueGoal:1},
            ],
            isLoading:false,
            type:{
              1:'客户',
              2:'拜访与跟进',
              3:'',
              4:"合同",
            },
            list1:[
              {id:1,name:"信息化业绩",goal:50,finishGoal:35,icon:require('../../../assets/img5.png'),unit:"万元",color:"#F46D40"},
              {id:2,name:"教材业绩",goal:100,finishGoal:50,icon:require('../../../assets/img3.png'),unit:"万元",color:"#E85D5D"},
              {id:3,name:"合同数量",goal:10,finishGoal:8,icon:require('../../../assets/img/hetong_icon.png'),unit:"个",color:"#4A8BF6"},
              {id:4,name:"新增拜访与跟进",goal:10,finishGoal:6,icon:require('../../../assets/img2.png'),unit:"次",color:"#4A8BF6"},
            ],
            formLabelAlign: {
              personalGoalsUnitList:[
                {
                  createBy:"1176711646287180320",
                  createTime: 1708670844000,
                  id: "1386914140391810841",
                  informationAmount: 1,
                  personalGoalsId: "1386914140391810834",
                  teachingMaterialAmount: 1,
                  totalContract: 1,
                  totalVisit: 1,
                  unitId: "1366872946815345826",
                  unitName: "苏州大学",
                  year: 2024,
                }
              ],
            },
            unitGoalList: [],
            keyNames:{
              informationAmount:"信息化业绩（万元）",
              teachingMaterialAmount:'教材业绩金额（万元）',
              totalContract:'合同数量',
              totalVisit:'拜访次数'
            },
            keyDict:{
              1:'newCustomers',
              2:'newVisit',
              3:'newOpportunity',
              4:'newContract',
              5:'contractAmount',
              6:'contractReturnAmount',
            },
            templateType:'',
          }
      },
      created() {
        this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList)
        this.templateType = this.$route.query.templateType
      },
      methods: {
        format(percentage) {
            return percentage ? `完成度: ${percentage}%`  : `${percentage}`;
        },
        getUnitGoalList(array){
              this.unitGoalList = [];
              this.unitIds = [];
              var keys = ['informationAmount','teachingMaterialAmount','totalContract','totalVisit'];
              array.forEach((element,idx) => {
                  var list = [];
                  this.unitIds.push(element.unitId)
                  keys.forEach(key => {
                    var item = {
                      id:element.id,
                      index:idx,
                      unitId:element.unitId,
                      unitName:element.unitName,
                      year:element.year,
                      goalNum:element[key],
                      unitgoalType:key,
                      unitgoalTypeName:this.keyNames[key],
                      reachNum:1
                    };
                    list.push(item);
                  });
                this.unitGoalList.push(list);
              });
      },
      loadInfo(){
        personalgoalsInfo({year:'2024'}).then((result) => {
          this.formLabelAlign = result.data;
          this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList)
        }).catch((err) => {
          
        });
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
          if (columnIndex === 0 && rowIndex === 0) {
            return {
              rowspan: 4,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      },
      loadData(){
              this.isLoading = false;
              personGoals({time:this.status}).then((result) => {
                this.isLoading = false;
                this.nowTime = result.data.nowTime;
                var list = result.data.goalSummaryVoList;
                var data = {};
                list.forEach(element => {
                  data[element.goalType] = element;
                });
                this.list.forEach(item => {
                  var dataItem = data[item.id]
                  item.goal = dataItem.goal;
                  item.finishGoal = dataItem.finishGoal;
                  item.residueGoal = dataItem.residueGoal;
                  item.finishRate = dataItem.finishRate;
                });
              }).catch((err) => {
                this.isLoading = false;
              });
    },
      getPercentage(item){
        if (Object.keys(item).length<=0) {
            return 0
        }
        if (item.goal == 0 || item.finishGoal == 0) {
            return 0;
        }else if(item.finishGoal/item.goal>1){
            return 100;
        } else {
            return item.finishRate *100;
        }
      },
      }
  }
</script>
<style scoped>
  .uploadtext {
    color: #4285F4;
    cursor: pointer;
  }
  .el-icon-plus {
    font-size: 30px !important;
  }

  .uploadcss {
  width: 88px;
  height: 88px;
  line-height: 24px;
  background: #F5F5F5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #CCCCCC;
  display: inline-block;
}

.uploadcss i {
  margin-top: 20px;
  font-size: 24px;
  color: #4285F4;
}
  .mobanlist{
    width: 800px;
  }
  .blueline {
  border-left: 4px solid #2490ff;
  padding-left: 20px;
  margin-top: 0;
  margin-bottom: 20px;
}
  .mobantitlecss {
    height: 40px;
    background: #ffffff;
    border-radius: 10px;
    margin-bottom: 20px;
  }
  .paddinginner {
  }
  .save{
      display: block;
      margin: 0 auto;
  } 
    .rightdiv{
        border-radius: 10px;
        display: flex;
        justify-content: flex-end;
      }
   .bbtn{
    height: 36px;
    line-height: 36px;
    background-color: #DFEAFD;
    font-size: 16px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    letter-spacing: 1px;
    border-radius: 4px;
    cursor: pointer;
    width: 120px;
    text-align: center;
  }
  
  .bbtn + .bbtn{
    margin-top:10px;
  }
  .tright{
      position: relative;
      width: 120px;
  }
  .fleft{
    background-color: white;
    padding: 20px 16px;
    border-radius: 8px;
  }
  .flex1{
    display: flex;
  }
    .itemcss{
        margin-right: 20px;
        flex: 1;
    }
    .itemcss:last-child{
        margin-right: 0;
    }
    .listcss{
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 20px;
    }
    .mainbg{
            height: 100%;
            overflow-y: auto;
            margin: 16px 0px;
    }
.timecss{
  margin-top: 8px;
  text-align: right;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #CCCCCC;

}
.titlefl{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.w210{
  width: 210px;
}
.width100{
  width: 80%;
}
.textcss{
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
  margin: 4px 0px;
}
.progress /deep/.el-progress-bar__outer{
    height: 4px !important;
    background-color: #DFEAFD;
}
.progress /deep/.el-progress-bar__inner{
    background-color: #5A98FF;
}
.progress /deep/.el-progress-bar{
    margin-right: 0px;
    padding-right: 0;
}
.progress /deep/.el-progress__text{
    position: absolute;
    right: 0;
    top: -16px;
}
.inblock{
  display: inline-block;
}
.mt50{
  margin-top: 50px;
}
.goalitem{
  height: 72px;
  line-height: 72px;
  margin-bottom: 20px;
}
.ml{
  margin-left: 3px;
}
.mt{
  margin-top: 5px;
}
.goalcss{

  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 16px;
}
.finishGoalcss{
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
  color: #F46D40;
  line-height: 28px;
}
.pl12{
  padding-left: 12px;
}
.flex{
  display: flex;
  align-items: center;
}
.imgcss{
  width: 3em;
  height: 3em;
  margin-left: 1.5vw;
}
.typename{
  padding-top: 20px;
  margin-left: 10px;
  margin-bottom: 1.25em;
  font-size: 1em;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.dataitem{
  margin-top: 16px;
  height: 9.375em;
  background: #FFFFFF;
  box-shadow: 0px 2px 16px 0px rgba(15,27,50,0.08);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}

.titlecss{
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  line-height: 23px;
  margin-bottom: 12px;
  margin-top: 12px;
}
.bg{
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  background-color: white;
  border-radius: 8px;
}

</style>
