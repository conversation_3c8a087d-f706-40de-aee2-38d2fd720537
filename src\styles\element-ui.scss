// cover some element-ui styles

.el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #F0F0F0;
}

.mytable .el-table__cell {
  padding: 0 !important;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}




// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// dropdown
.el-dropdown-menu {
  min-width: 120px;
  padding: 0 !important;
}

/* 消除小三角 */
.el-popper[x-placement^=bottom] .popper__arrow {
  border: none;
}

.el-popper[x-placement^=bottom] .popper__arrow::after {
  border: none;
}

.el-dropdown-menu__item {
  height: 40px !important;
  line-height: 40px !important;
  // background-color: red;
  text-align: center;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.el-dropdown-menu__item:hover,
.el-dropdown-item:focus {
  background-color: #4285F4 !important;
  color: white !important;
}

// el-select
.el-select-dropdown__list {
  padding: 0px 0px !important;
}

// 下拉框距离选择框的间隙
.el-popper[x-placement^=bottom] {
  margin-top: 8px;
}

.el-select-dropdown__item {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  height: 40px !important;
}

.el-select-dropdown__item.selected {
  background-color: #4285F4;
  color: white;
}

.el-select-dropdown .el-scrollbar {
  padding-bottom: 6px;
}

.el-range-separator {
  box-sizing: content-box;
}

//  pagination
.el-pagination {
  font-size: 14px;
  font-family: Roboto-Regular, Roboto;
  font-weight: 400;
  color: #333333;
  width: 100%;
}

.el-pagination button,
.el-pagination span:not([class*=suffix]) {
  height: 32px !important;
  line-height: 32px !important;
  font-size: 14px !important;
  font-family: Roboto-Regular, Roboto;
  font-weight: 400 !important;
  color: #333333;
}

.el-pagination .btn-prev,
.el-pagination .btn-next {
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #D6D6D6;
  margin: 0px 4px !important;
}

.el-pagination .btn-prev {
  padding-right: 0px;
}

.el-pagination .btn-next {
  padding-left: 0px;
}

.el-pagination button {
  padding: 0;
  min-width: 32px !important;
  height: 32px !important;
}

.el-pager li {
  min-width: 32px !important;
  height: 32px !important;
  line-height: 32px !important;
  border-radius: 4px;
  border: 1px solid #D6D6D6;
  font-size: 14px;
  margin-left: 8px;
}

.el-pager li:last-child {
  margin-right: 8px;
}

.el-pager li.active {
  background-color: #4285F4;
  color: white !important;
  border: none;
}

.el-pager li.active+li {
  border-left: 1px solid #D6D6D6;
}

.el-pagination__editor.el-input {
  width: 68px;
  height: 31px;
  padding: 0px 8px;
}

.el-pagination__editor.el-input .el-input__inner {
  height: 31px;
}

// el-dialog

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.el-dialog__header {
  border-bottom: 1px solid #F0F0F0;
  padding: 20px;
}

.el-dialog__title {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
}

.el-dialog__footer {
  border-top: 1px solid #F0F0F0;
  padding: 20px;
}

.el-dialog__footer .el-button {
  min-width: 72px;
  padding: 10px 12px;
  border-radius: 4px 4px 4px 4px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  letter-spacing: 1px;

}

.el-button--primary {
  background-color: #4285F4;
  border-color: #4285F4;
}