<template>
  <div>
    <back>返回</back>
    <div class="concss" >
      <el-tabs v-model="activeNametop" @tab-click="handleClickTop" class="swidth">
        <el-tab-pane  label="时长排行" name="timeRank"></el-tab-pane>
        <el-tab-pane  label="积分排行" name="scoreRank"></el-tab-pane>
      </el-tabs>
      <div class="mb20 phcss">
        <el-tabs v-model="activeName" @tab-click="handleClick"  :stretch="false" class="tabscss">
          <el-tab-pane label="月度排行" name="first"></el-tab-pane>
          <el-tab-pane label="年度排行" name="second"></el-tab-pane>
          <div class="listh">
            <el-row type="flex"  justify="space-between"  v-for="(item,index) in sortList" :key="index">
              <el-col :span="11" >
                <rankItem :rankType="rankType" v-if="index<5"  :sort="item.ranking" :itemData="item"></rankItem>
                <div v-else></div>
              </el-col>
              <el-col :span="11" v-if="sortList.length>5">
                <rankItem :rankType="rankType" v-if="sortList[index+5]" :sort="getRanking(sortList[index+5])" :itemData="sortList[index+5]"></rankItem>
                <div v-else></div>
              </el-col>
            </el-row>
          </div>
          <page :currentPage="pageBean.pageNum" align="right" :pageSize="pageBean.pageSize" :total="total" @updatePageNum="changePage"></page>
        </el-tabs>
      </div>
      <div class="phcss">
        部门排行
        <el-tabs v-model="activeName2" @tab-click="handleClick2"  :stretch="false" class="tabscss">
          <el-tab-pane label="月度排行" name="first"></el-tab-pane>
          <el-tab-pane label="年度排行" name="second"></el-tab-pane>
          <div class="mb20">
            <span class="deffont">选择部门：</span>
            <el-cascader
            class="definput wid"
            ref="mycascader"
            clearable
            :show-all-levels="false"
            :value="selList"
            :options="options"
            :props="{ checkStrictly: true,label:'name',value:'id', }"
            @change="handleChange">
            </el-cascader>
          </div>
          <div class="listh">
            <el-row type="flex"  justify="space-between"  v-for="(item,index) in departSortList" :key="index">
              <el-col :span="11" >
                <rankItem :rankType="rankType" v-if="index<5"  :sort="item.ranking" :itemData="item"></rankItem>
                <div v-else></div>
              </el-col>
              <el-col :span="11" v-if="departSortList.length>5">
                <rankItem :rankType="rankType" v-if="departSortList[index+5]" :sort="getRanking(departSortList[index+5])" :itemData="departSortList[index+5]"></rankItem>
                <div v-else></div>
              </el-col>
            </el-row>
          </div>
          
          <page :currentPage="pageBean2.pageNum" align="right" :pageSize="pageBean2.pageSize" :total="total2" @updatePageNum="changePage2"></page>
        </el-tabs>
      </div>
    </div>
    
  </div>
</template>

<script>
import back from '../../common/back.vue';
import page from '../../common/page.vue';
import rankItem from './components/rankItem.vue';
import { queryCaseLearningRanking } from "@/api/studyshare/index";
import { queryTreeByUserId } from "@/api/index";
export default {
    components:{
      back,
      page,
      rankItem
    },
    data(){
        return{
          activeName:'first',
          activeName2:"first",
          total:0,
          total2:0,
          month:new Date().getMonth()+1,
          year:new Date().getFullYear(),
          sortList:[],
          departSortList:[],
          pageBean:{
            month:'',
            year:'',
            pageNum:1,
            pageSize:10,
            rankType:1,
          },
          pageBean2:{
            month:'',
            departmentId:'',
            year:'',
            pageNum:1,
            pageSize:10,
            rankType:1,
          },
           selList: [],
            options: [],
            activeNametop:'timeRank',
            rankType:1,
            rankObj:{
              'timeRank':1,
              'scoreRank':2,
            }
        }
    },
    created(){
      var ids = sessionStorage.getItem('departmentIds').split(',');
      this.pageBean2.departmentId = ids[0]
      this.pageBean2.month = this.month;
      this.pageBean.month = this.month;
      this.getTypeList();
      this.loadRank();
      this.loadDepartmentRank();
    },
    methods:{
      handleClickTop(tab, event){
                this.activeNametop = tab.name;
                this.rankType = this.rankObj[tab.name]
                this.activeName = 'first'
                this.activeName2 = 'first'
                this.pageBean.rankType = this.rankType 
                this.pageBean2.rankType = this.rankType 
                this.pageBean.pageNum = 1
                this.pageBean2.pageNum = 1
                this.loadRank();
                this.loadDepartmentRank();
      },
      getTypeList(){
        var par = {
          methodName:"list",
          className:'CaseController'
        }
        queryTreeByUserId(par).then((result) => {
          this.options = result.data;
          this.$nextTick(()=>{
              this.selList =  this.changeDetSelect(this.pageBean2.departmentId,this.options)
          })
          
        }).catch((err) => {
          
        });
      },
      getRanking(data){
        return data.ranking
      },
      changeDetSelect(key,treeData){
        let arr = []; // 在递归时操作的数组
        let returnArr = []; // 存放结果的数组
        let depth = 0; // 定义全局层级
        // 定义递归函数
        function childrenEach(childrenData, depthN) {
            for (var j = 0; j < childrenData.length; j++) {
                depth = depthN; // 将执行的层级赋值 到 全局层级
                arr[depthN] = (childrenData[j].id);
                if (childrenData[j].id == key) {
                    returnArr = arr.slice(0, depthN+1); //将目前匹配的数组，截断并保存到结果数组，
                    break
                } else {
                    if (childrenData[j].children) {
                        depth ++;
                        childrenEach(childrenData[j].children, depth);
                    }
                }
            }
            return returnArr;
        }
        return childrenEach(treeData, depth);
      },
      loadRank(){
        queryCaseLearningRanking(this.pageBean).then((result) => {
          this.sortList = result.data;
          this.total = result.page.total;
        }).catch((err) => {
          
        });
      },
      loadDepartmentRank(){
        queryCaseLearningRanking(this.pageBean2).then((result) => {
          this.departSortList = result.data;
          this.total2 = result.page.total;
        }).catch((err) => {
          
        });
      },
      handleChange(value) {
        this.$refs.mycascader.dropDownVisible = false;
        this.selList = value;
        this.pageBean2.pageNum = 1;
        this.pageBean2.departmentId = value.length>0 ? value[value.length - 1] :'';
        this.loadDepartmentRank();
        
      },
      handleClick2(val){
        switch (val.name) {
          case 'first':
             this.pageBean2.month = this.month;
             this.pageBean2.year = '';
            break;
          case 'second':
             this.pageBean2.month = '';
             this.pageBean2.year = this.year;
            break;
          default:
            break;
        }
        this.pageBean.pageNum = 1;
        this.loadDepartmentRank();
      },
      changePage2(page){
        this.pageBean2.pageNum = page;
        this.loadDepartmentRank();
      },
      handleClick(){
        switch (this.activeName) {
          case 'first':
             this.pageBean.month = this.month;
             this.pageBean.year = '';
            break;
          case 'second':
             this.pageBean.month = '';
             this.pageBean.year = this.year;
            break;
          default:
            break;
        }
        this.pageBean.pageNum = 1;
        this.loadRank();
      },
      changePage(page){
        this.pageBean.pageNum = page;
        this.loadRank();
      },
    }
}
</script>
<style scoped>
  .swidth /deep/.el-tabs__item{
    width: 120px;
    text-align: center;
  }
.listh{
  height: 280px;
}
.mb20{
  margin-bottom: 20px !important;
}
.tabscss /deep/.el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 56px;
  width: 104px !important;
  text-align: center;
  line-height: 56px;
  /* padding: 0 60px; */
}
.wid{
  width: 300px !important;
}
.phcss{
  margin: 0 auto;
  width: 70%;
}
.concss{
  margin: 20px 0px;
  width: 100%;
  padding: 20px 0;
  background-color: white;
}
</style>