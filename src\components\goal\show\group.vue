<template>
  <div class="mainbg">
    <div class="typelist bbline">
        <span class="ttext">目标分类：</span>
        <span
        class="typeItem"
        :class="{'typesel'
        :item.id == pageBean.goalType}"
        v-for="(item) in types"
        :key="item.id"
        @click="clickType(item)">
            {{item.name}}
        </span>
        <span v-isShow="'/goal/show/statistics'" class="deffont bbtn right btn" @click="lookData"><img class="iconimg" src="../../../assets/img/huizong.png" alt=""> 看汇总</span>
    </div>
    <div class="formdiv">
        <span class="tnamecss"> <img class="limg" src="../../../assets/img/paihang.png" alt=""> 达成排行</span>
        <el-select class="definput right ml16 w210" popper-class="removescrollbar" v-model="pageBean.time" @change="changeTime" placeholder="请选择时间维度">
            <el-option
            v-for="item in options1"
            :key="item.id"
            :label="item.name"
            :value="item.id">
            </el-option>
        </el-select>
        <el-select class="definput right ml16 w210"  popper-class="removescrollbar" v-model="isDep"  placeholder="请选择查看范围">
            <el-option
            v-if="userType == 4"
            :key="''"
            :label="'全公司'"
             @click.native="chooseRange"
            :value="''">
            </el-option>
            <el-option
            @click.native="chooseDepartment"
            :key="'1'"
            :label="'选部门'"
            :value="'1'">
            </el-option>
        </el-select>
        <el-select class="definput right w210" popper-class="removescrollbar" @change="changeStatus" clearable placeholder="请选择" v-model="pageBean.finishRate">
            <el-option
            v-for="item in options2"
            :key="item.id"
            :label="item.name"
            :value="item.id">
            </el-option>
        </el-select>
    </div>
    <div class="timediv">
        {{nowTime}}
    </div>
    <div class="mblist" :class="{mblistHeight:!curData.ranking}" v-loading="isLoading">
        <goalitem v-for="(item,index) in list" :key="index" :itemData="item" :time="nowTime" :isEnd="isEnd" ></goalitem>
        <noList v-if="list.length == 0 && loadBoo "></noList>
    </div>
    <div class="shadowcss">
        <!-- <goalitem  class="bbline" v-if="curData && curData.ranking"  :itemData="curData" :time="nowTime" :isEnd="isEnd" ></goalitem> -->
        <page :currentPage="pageBean.pageNum" :pageSize="pageBean.pageSize" :total="total" @updatePageNum="changePage"></page>
    </div>
    <!-- 选部门 -->
    <deDialog
    ref="deRef"
    :dType="dType"
    :visible.sync="dialogDepVisible"
    @updateVisible="updateSystemVisible"
    @submitData="submitData">
    </deDialog>
  </div>
</template>

<script>
import goalitem from './components/goalitem.vue';
import page from '../../common/page.vue';
import deDialog from '@/components/common/deDialog.vue';
import { achieveGoalVoList } from "@/api/goal";
import noList from '@/components/common/nolist.vue'
import Bus from "@/utils/EventBus";
export default {
    components:{
        goalitem,
        page,
        deDialog,
        noList
    },
    data(){
        return{
            loadBoo:false,
            dType:'',
            curData:{},
            isDep:'',
            dialogDepVisible:false,
            dialogVisible:false,
            userType:sessionStorage.getItem('dataScope'),
            currentitem:{type:1,goalNum:10,curNum:5},
            nowTime:'',
            list:[],
            total:0,
            isEnd:false,
            isLoading:false,
            pageBean:{
                goalType:1,
                time:3,
                departmentIds:[],
                finishRate:'',
                pageNum:1,
                pageSize:10,
            },
            types:[
                {id:1,name:'新增客户'},
                {id:2,name:'新增拜访与跟进'},
                {id:5,name:'业绩'},
                {id:6,name:'回款金额'},
                {id:4,name:'新增合同'},
                {id:7,name:'样书发放'},
            ],
            options1:[
                {id:1,name:'本月'},
                {id:2,name:'本季度'},
                {id:3,name:'本年度'},
                {id:4,name:'上月'},
                {id:5,name:'上季度'},
                {id:6,name:'上年度'}
            ],
            options2:[
                {id:'',name:'全部'},
                {id:"1",name:'已完成'},
                {id:"0",name:'未完成'}
            ],
            options3:[
                {id:'',name:'全公司'},
                {id:'1',name:'看部门'},
                // {id:4,name:'选员工'}
            ],
        }
    },
    created(){
        Bus.$on('loadTopData', (msg) => {
          this.loadData()
        })
    },
    beforeDestroy(){
      Bus.$off('loadTopData');
    },
    methods:{
        loadData(){
            this.isLoading = true;
            achieveGoalVoList(this.pageBean).then((result) => {
                this.isLoading = false;
                this.list = result.data.achieveGoalVoList;
                this.total = result.page.total;
                this.nowTime = result.data.nowTime;
                this.curData = result.data;
                this.loadBoo = true
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        chooseRange(){
            this.pageBean.departmentIds = [];
            this.pageBean.pageNum = 1
            this.loadData();
        },
        clickType(data){
            this.pageBean.pageNum = 1;
            this.pageBean.goalType = data.id;
            this.pageBean.finishRate = '';
            this.pageBean.time = 3;
            this.departmentIds = [];
            this.loadData();
        },
        changePage(page){
            this.pageBean.pageNum = page;
            this.loadData();
        },
        changeRange(data){
            if(data == 4){
                this.dialogVisible = true;
            }
        },
        updateVisible(data){
            this.dialogVisible = data;
        },
        chooseDepartment(){
            this.updateSystemVisible(true)
            this.$refs.deRef.loadData()
            this.dType = '1'
        },
        submitData(data){
            this.updateSystemVisible(false);
            this.pageBean.pageNum = 1;
            this.pageBean.departmentIds = data;
            this.loadData();
        },
        updateSystemVisible(val){
            this.dialogDepVisible = val;
        },
        choosePerson(data){

        },
        changeTime(data){
            this.pageBean.time = data;
            this.pageBean.pageNum = 1;
            this.isEnd = data>3 ? true :false
            this.loadData();
        },
        changeStatus(data){
            this.pageBean.finishRate = data;
            this.pageBean.pageNum = 1;
            this.loadData();
        },
        lookData(){
            this.$router.push('/goal/show/statistics')
        },

    }
}
</script>

<style scoped>

.shadowcss{
    margin: 0px -20px;
    padding: 12px 20px;
    border-radius: 0px 0px 8px 8px;
    background-color: #FFFFFF;
}
.mblistHeight{
    height: calc(100% - 205px) !important;
}
.mblist{
    min-height: calc(100% - 305px);
    /* background-color: red; */
    /* overflow-y: auto; */
}
.timediv{
    text-align: right;
    padding-top: 8px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #CCCCCC;
    line-height: 14px;
}
.limg{
    width: 24px;
    height: 24px;
    margin-top: -5px;
}
.tnamecss{
    font-size: 20px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 23px;
}
.formdiv{
    padding-top: 20px;
    height: 54px;
}
.ml16{
    margin-left: 16px;
}
.w210{
    width: 210px;
}
.btn{
    cursor: pointer;
}
.iconimg{
    width: 16px;
    height: 16px;
    margin-top: -2px;
}
.typeItem{
    height: 34px;
    margin:0 16px;
    padding:8px 12px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    cursor: pointer;
}
.typesel{
    background-color: #4285F4;
    color: white;
    border-radius: 4px;
}
.ttext{
font-size: 16px;
font-family: Microsoft YaHei-Regular, Microsoft YaHei;
font-weight: 400;
color: #333333;
line-height: 19px;

}
.typelist{
    line-height: 34px;
    padding-bottom: 20px;
}
.mainbg{
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    position: relative;
}
</style>
