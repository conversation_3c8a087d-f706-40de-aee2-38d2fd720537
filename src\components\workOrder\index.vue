<template>
    <div class="mainbg fixpb">
        <el-form label-width="85px" inline class="myform ">
                    <el-form-item label="工单内容：">
                        <el-input class="definput" placeholder="请输入工单内容" v-model="pageBean.content" clearable>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="受理人：">
                        <el-input class="definput" placeholder="请输入受理人名称" v-model="pageBean.acceptedPersonName"
                            clearable></el-input>
                    </el-form-item>
                    <el-form-item label="工单状态：">
                        <el-select class="definput" popper-class="removescrollbar" v-model="pageBean.status"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in options" :key="item.value" :label="item.name" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="优先级：">
                        <el-select class="definput" popper-class="removescrollbar" v-model="pageBean.priority"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in options2" :key="item.value" :label="item.name"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="负责人：">
                        <el-input class="definput" placeholder="请输入负责人名称" v-model="pageBean.chargePersonName" clearable>
                        </el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="defaultbtn mt" icon="el-icon-search" type="primary" @click="searchAction">搜索
                        </el-button>
                    </el-form-item>
                    <el-form-item class="right">
                        <el-button v-isShow="'crm:controller:workorder:save'" class="defaultbtn mt "
                        icon="el-icon-plus" type="primary" @click="addWorkOrder">新建工单</el-button>
                    </el-form-item>

        </el-form>
        <el-table class="mytable" :data="tableData" style="width: 100%;" v-loading="isLoading">
            <el-table-column prop="content" label="工单内容" align="center" min-width="200px">
                <template slot-scope="scope">
                    <div class="ltext">
                        {{scope.row.content}}
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="工单状态" align="center" width="100px" >
                <template slot-scope="scope">
                    <div :style="{color:status[scope.row.status].color}">{{status[scope.row.status].name}}</div>
                </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" align="center" width="100px" >
                <template slot-scope="scope">
                    <div :style="{color:status1[scope.row.priority].color}">{{status1[scope.row.priority].name}}</div>
                </template>
            </el-table-column>
            <el-table-column prop="chargePersonName" label="负责人" align="center" width="100px" >
            </el-table-column>
            <el-table-column prop="chargePersonDepartmentName" label="负责人所在部门" align="center" width="140">
            </el-table-column>
            <el-table-column prop="acceptedPersonName" align="center" label="受理人"  width="100px" >
            </el-table-column>
            <el-table-column prop="acceptedPersonDepartmentName" label="受理人所在部门" align="center" width="150">
            </el-table-column>
            <el-table-column prop="isConvert" label="是否转任务" align="center" width="100">
                <template slot-scope="scope">
                    <span>{{scope.row.isConvert == 1?'是':'否'}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="edit" width="150" align="center" label="更多操作" fixed="right">
                <template slot-scope="scope">
                    <el-dropdown placement="bottom-end" @command="e=>handleCommand(e,scope.row)" trigger="click">
                        <el-button class="bbtn mr10" type="text">
                            更多
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item v-isShow="'crm:controller:workorder:update'"
                                v-if="scope.row.status == 2 && userid == scope.row.acceptedPerson " :command="0">确认接单
                            </el-dropdown-item>
                            <el-dropdown-item v-isShow="'crm:controller:workorder:update'"
                                v-if="scope.row.status == 3 && userid == scope.row.acceptedPerson " :command="1">完成工单
                            </el-dropdown-item>
                            <el-dropdown-item v-isShow="'crm:controller:workorder:info'" :command="2">详情
                            </el-dropdown-item>
                            <el-dropdown-item v-isShow="'workrecord'"  :command="3">工时管理
                            </el-dropdown-item>
                            <el-dropdown-item v-isShow="'crm:controller:workorder:changeToTask'" v-if="!scope.row.isConvert"  :command="4">工单转任务
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <el-button v-isShow="'crm:controller:workorder:update'" class="bbtn " :disabled="scope.row.status>2 || scope.row.isConvert == 1"
                        type="text" @click="onEdit(scope.row)"> 编辑</el-button>
                    <el-button v-isShow="'crm:controller:workorder:delete'" :disabled="scope.row.isConvert == 1" class="rbtn " type="text"
                        @click="deleteAction(scope.row)"> 删除</el-button>
                </template>
            </el-table-column>
            <template slot="empty">
                <nolist></nolist>
            </template>
        </el-table>
            <div class="fixpage">
                <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                @updatePageNum="handleCurrentChange"></page>
            </div>

        <dc-dialog iType="1" title="温馨提示" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
            <template>
            </template>
            <p class="pcc">是否删除该工单？</p>
            <!-- <p class="pcc">除，请先接触客户与单位的关系。</p> -->
        </dc-dialog>
        <detailDrawer ref="detailDrawer" :visible.sync="detailVisible" @updateVisible="updateDetailVisible">

        </detailDrawer>
        <recorddialog
        :idAndName="idAndName"
        ref="recordRef"
        :dialogVisible="dialogRecordVisible"
        @closeDialog="closeDialog"
        type="2"
        ></recorddialog>
        <workastaskDialog ref="changeTask" @doneAction="changeDone"></workastaskDialog>
    </div>
</template>

<script>
    import page from '../common/page.vue';
    import detailDrawer from './detailDrawer.vue';
    import nolist from '../common/nolist.vue';
    import { workorderList, deleteWorkorder, updateWorkorder } from "@/api/workorder";
    import recorddialog from "../project/common/recorddialog.vue";
    import Bus from '@/utils/EventBus'
    import workastaskDialog from './workastaskDialog.vue';
    export default {
        components: {
            page,
            nolist,
            detailDrawer,
            recorddialog,
            workastaskDialog
        },
        provide() {
            return {
                taskIdObj: this.taskIdObj,
            }
        },
        data() {
            return {
                userid: window.sessionStorage.getItem('userid'),
                level: sessionStorage.getItem('dataScope'),
                deleteData: {},
                detailVisible: false,
                dialogVisible: false,
                taskIdObj: {
                    taskId: '',
                    isConvert:0,
                },
                options: [{
                    value: 2,
                    name: '待接单',
                }, {
                    value: 3,
                    name: '处理中',
                }, {
                    value: 4,
                    name: '已完成',
                }],
                options2: [{
                    value: 1,
                    name: '低',
                }, {
                    value: 2,
                    name: '一般',
                }, {
                    value: 3,
                    name: '紧急',
                }, {
                    value: 4,
                    name: '非常紧急',
                }],
                status: {
                    1: {
                        name: "未分配",
                        color: "#F45961"
                    },
                    2: {
                        name: "待接单",
                        color: "#FF8D1A"
                    },
                    3: {
                        name: "处理中",
                        color: "#4285F4"
                    },
                    4: {
                        name: "已完成",
                        color: "#56C36E"
                    },
                },
                status1: {
                    1: {
                        name: "低",
                        color: "#56C36E"
                    },
                    2: {
                        name: "一般",
                        color: "#4285F4"

                    },
                    3: {
                        name: "紧急",
                        color: "#FF8D1A"

                    },
                    4: {
                        name: "非常紧急",
                        color: "#F45961"
                    },
                },
                isLoading: false,
                tableData: [],
                total: 0,
                pageBean: {
                    content: "",
                    status: "",
                    priority: "",
                    chargePersonName: "",
                    acceptedPersonName: "",
                    pageNum: 1,
                    pageSize: 10,
                },
                idAndName:{},
                dialogRecordVisible:false
            }
        },
        created() {
            this.loadData();
        },
        mounted() {
            if (this.$route.query.id) {
                this.detailVisible = true;
                this.$refs.detailDrawer.loadInfo(this.$route.query.id);
            }
        },
        methods: {
            loadData() {
                this.isLoading = true;
                workorderList(this.pageBean).then((result) => {
                    this.tableData = result.data;
                    this.total = result.page.total;
                    this.isLoading = false;

                }).catch((err) => {
                    this.isLoading = false;
                });
            },
            searchAction() {
                this.loadData();
            },
            updateDetailVisible(data) {
                this.detailVisible = data;
            },
            submitDialog() {
                this.isLoading = true;
                deleteWorkorder({ id: this.deleteData.id }).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '删除成功'
                        })
                        this.loadData();
                    } else {
                        this.$message({
                            type: "error",
                            message: result.msg
                        })
                        this.isLoading = false;
                    }
                }).catch((err) => {
                    this.isLoading = false;
                });
                this.dialogVisible = false;
            },
            addWorkOrder() {
                this.$router.push({
                    path: "/workOrder/add"
                })
            },
            deleteAction(data) {
                if(this.level == 4 || this.userid == data.chargePerson){
                        this.deleteData = data;
                        this.dialogVisible = true;
                }else{
                        this.$message({
                            type: "error",
                            message: '您没有操作权限'
                        })
                }
            },
            onEdit(data) {
                if(this.level == 4 || this.userid == data.chargePerson){
                        this.$router.push({
                            path: '/workOrder/add',
                            query: {
                                id: data.id,
                            }
                        })
                }else{
                        this.$message({
                            type: "error",
                            message: '您没有操作权限'
                        })
                }

            },
            handleCommand(index, data) {
                switch (index) {
                    case 0:
                        this.updateData(3, data)
                        break;
                    case 1:
                        this.updateData(4, data)
                        break;
                    case 2:
                        {
                            this.detailVisible = true;
                            this.$refs.detailDrawer.loadInfo(data.id);
                        }
                        break;
                    case 3:
                        {
                            this.$router.push({
                              path:'/workOrder/timeManager',
                              query:{
                                workId:data.id
                              },
                            })
                        }
                        break;
                    case 4:
                        {
                            if(data.projectId == 0){
                                return this.$message.info('当前工单未关联项目，无法转为任务')
                            }
                            this.$refs.changeTask.show(data.id)
                        }
                        break;
                    default:
                        break;
                }
            },
            changeDone(){
                this.loadData()
            },
            updateData(status, data) {
                this.isLoading = true;
                updateWorkorder({ id: data.id, status: status }).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '更新成功'
                        })
                        this.loadData()
                    } else {
                        this.$message({
                            type: 'error',
                            message: result.msg
                        })
                        this.isLoading = false
                    }
                }).catch((err) => {
                    this.isLoading = false;
                });
            },
            handleCurrentChange(page) {
                this.pageBean.pageNum = page;
                this.loadData();
            },
            showDialog(row) {
                this.taskIdObj.taskId = row.id;
                this.taskIdObj.isEnable = row.isConvert;
                this.idAndName = null;
                this.dialogRecordVisible = true
                this.$nextTick(() => {
                    Bus.$emit('loadData')
                })
            },
            closeDialog(isUpdate) {
                if (isUpdate) {
                    this.$emit('flushData')
                }
                this.dialogRecordVisible = false
            },
        }
    }
</script>

<style scoped>
    .mainbg {
        padding: 20px;
        background-color: white;
        min-height:calc(100vh - 106px);
    }

    .pcc {
        margin: 0 auto;
        text-align: center;
    }

    .smtext {
        zoom: 0.8;
    }

    .fxcenter {
        width: 50px;
        height: 52px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .zhiding {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        /* background-color: red; */
    }

    .mt {
        margin-top: 4px;
    }

    .cusnamecss {
        display: flex;
    }

    .tagcss {
        font-size: .625em !important;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        height: 14px;
        min-width: 30px;
        line-height: 11px;
        border-radius: 2px;
        margin-right: 20px;
    }

    .genjin {
        color: #4285F4;
        background-color: #DFEAFD;
    }

    .fuze {
        color: #FEF2E7;
        background-color: #FF8D1A;
    }

    .xiezuo {
        color: #56C36E;
        background-color: #F3FEF6;
    }

    .tagcss:nth-child(2n+2) {
        margin-top: 4px;
    }

    .ltext {
        text-align: justify !important;
    }

    .mytable /deep/ .cell {
        height: auto !important;
        min-height: 52px !important;
        padding: 13px !important;
        line-height: 25px !important;
    }

    .mytable .el-button {
        padding: 2px;
    }

    .mr10 {
        margin-right: 10px;
    }

    .bbtn,
    .bbtn:hover,
    .bbtn:focus {
        color: #4285F4;
    }

    .rbtn,
    .rbtn:hover,
    .rbtn:focus {
        color: #F45961;
    }

    .right {
        text-align: right;
    }
</style>
