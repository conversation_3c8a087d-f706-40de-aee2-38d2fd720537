import request from '@/utils/request'


export function queryRoleResourceList(data) {
  return request({
    url: '/crm/controller/role/queryRoleResourceList',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}


export function updateRoleResource(data) {
  return request({
    url: '/crm/controller/role/updateRoleResource',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}






export function getRoleList(params) {
  return request({
    url: '/crm/controller/role/queryRoleList',
    method: 'get',
    params
  })
}

export function getRoleInfo(id) {
  return request({
    url: `/crm/controller/role/info/${id}`,
    method: 'get',
  })
}





export function deleteRole(params) {
  return request({
    url: '/crm/controller/role/deleteRole',
    method: 'get',
    params
  })
}




export function saveRole(data) {
  return request({
    url: '/crm/controller/role/saveRole',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

export function updateRole(data) {
  return request({
    url: '/crm/controller/role/updateRole',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}















