<template>
    <el-dialog class="unitdialog" title="选择项目" top="80px" :visible.sync="dialogTableVisible" width="70%" center>
        <el-form class="unitfcss" :inline="true">
            <el-form-item label="项目名称：">
                <el-input clearable="" class="definput" v-model="pageBean.projectName" placeholder="请输入项目名称"></el-input>
            </el-form-item>
            <el-form-item label="">
                <el-button class="defaultbtn ml20" icon="el-icon-search" type="primary" @click="handleClick">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table class="mytable" :data="dataList" >
            <el-table-column width="200" :show-overflow-tooltip="true" class-name="column_blue" property="projectName" label="项目名称" >
            </el-table-column>
            <el-table-column property="customerName" label="客户名称" ></el-table-column>
            <el-table-column property="unitName" label="客户单位" ></el-table-column>
            <el-table-column property="projectStatusName" label="项目状态" >
            </el-table-column>
            <el-table-column  property="beginTime" label="开始时间" >
            </el-table-column>
            <el-table-column  property="endTime" label="结束时间" >
            </el-table-column>
            <el-table-column property="projectAmount" label="项目金额(万元)" width="120px"></el-table-column>
            <el-table-column property="chargePersonName" label="负责人" ></el-table-column>
            <el-table-column property="edit" label="操作" align="center" fixed="right">
                <template slot-scope="scope" >
                    <div class="rbtn deffont" v-if="scope.row.id == projectData.id">取消选择</div>
                    <div class="bbtn deffont" v-else @click="chooseAction(scope.row)">选择</div>
                </template>
            </el-table-column>
        </el-table>
        <div class="center">
            <el-button class="defaultbtn" type="primary" @click="submitAction">提交</el-button>
        </div> 
        <page 
        :currentPage="pageBean.pageNum" 
        :pageSize="pageBean.pageSize" 
        :total="total"
        @updatePageNum="handleCurrentChange"
        ></page>
    </el-dialog>
</template>

<script>

import page  from './page.vue';
import {projecttList} from '@/api/wapi'
export default {
    components:{
        page
    },
    data(){
        return{
            activeName:"first",
            dataList:[],
            pageBean:{
                projectName:"",
                pageNum:1,
                pageSize:8,
            },
            total:13,
            projectData:{}
        }
    },
    props:{
        visible:{
            type:Boolean,
            default:false
        },
    },
    computed:{
        dialogTableVisible:{
            get(){
                return this.visible;
            },
            set(val){
                this.$emit('updateVisible',val);
            },

        }
    },
    methods:{
        selectProjectData(params){
            if(params){
              this.tempData = params
            }else{
                params = this.tempData
            }
                let sData = {...params,...this.pageBean}
                projecttList(sData).then(res=>{
                        if(res.status == 0){
                                this.dataList = res.data
                                this.total = res.page.total
                                this.dataList.forEach(item=>{
                                    this.$set(item,'isSelect',false)
                                })
                                if(this.projectData.id){
                                    this.selected(this.dataList)
                                }
                        }else{
                            this.msgError(res.msg)
                        }
                })
        },
        selected(dataList){
            dataList.forEach(item=>{
                if(item.id === this.projectData.id){
                    this.$set(item,'isSelect',true)
                }
            })
        },
        handleClick(){
            this.pageBean.pageNum = 1;
            this.selectProjectData()
        },
        submitAction(){
            this.$emit('updateData',this.projectData);
            this.dialogTableVisible = false;
            this.projectData = {}
        },
        handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.selectProjectData()
        },
        chooseAction(val){
            this.dataList.forEach(item=>{
                item.isSelect = false
            })
            this.projectData = val;
            val.isSelect = true
        }
    }
}
</script>
<style scoped>
.center{
    margin-top: 50px;
    text-align: center;
}
.bbtn{
    color: #4285F4;
    cursor: pointer;
}
.rbtn{
    color: #F45961;
    cursor: pointer;
}

.unitfcss{
    line-height: 34px;
}
.unitfcss /deep/.el-form-item__label{
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
}
.unitfcss /deep/.el-form-item{
    margin-bottom: 20px;
    /* padding-right: 60px; */
}
.ml20{
    margin-left: 20px;
}
.unitdialog /deep/.el-dialog__body{
    padding: 20px;
    padding-top: 0px;
}
.unitdialog /deep/.el-dialog__header{
    border: none;
}
</style>
<style scoped>
.tabscss /deep/.el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 40px;
  width: 240px !important;
  text-align: center;
  line-height: 40px;
  /* padding: 0 60px; */
}

</style>