<template>
  <div class="div100">
    <div class="logo">
      <div class="shuju">
        <img src="@/assets/Group3680.png" alt="" class="logotu" />
        <div class="wenzi1">业务大数据</div>
        <el-form :model="form" :rules="rules" ref="form" class="riqi xuanze">
          <el-form-item label="" prop="name" class="itemheight">
            <el-select
              ref="selectRef"
              v-model="form.name"
              placeholder="请选择筛选类型"
              clearable
              @change="handleChange"
              @visible-change="handleVisibleChange"
            >
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.value"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="year">
            <el-date-picker
              v-model="form.year"
              type="year"
              placeholder="请选择年份"
              format="yyyy年"
              value-format="yyyy"
              :picker-options="{ disabledDate: disabledDate }"
              class="xuanze1 itemheight"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" class="anniu" @click="submitForm('form')"
              >开始统计</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <deDialogHome
      ref="deRef"
      :dType="dType"
      :visible.sync="dialogDepartmentVisible"
      @updateVisible="updateDepartmentVisible"
      @submitData="submitData"
      :multiple="false"
    >
    </deDialogHome>
  </div>
</template>

<script>
import deDialogHome from '@/components/common/deDialogHome.vue';
import { checkPermission } from '@/utils/permission'
export default {
  components: {
    deDialogHome
  },
  data() {
    return {
      dialogDepartmentVisible:false,
      selDepartmentData:[],
      searchdepartmentNames:'',
      form: {
        userId: '',
        name: '',
        departmentId: '',
        year: new Date().getFullYear().toString(),
      },
      dType:'',
      options: [],
      levelTypeToList: {
        1: [
          {
            id: 4,
            value: '看自己',
          },
        ],
        2: [
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
          {
            id: 4,
            value: '看自己',
          },
        ],
        3: [
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
          {
            id: 4,
            value: '看自己',
          },
        ],
        4: [
          {
            id: 2,
            value: '选部门',
          },
          {
            id: 3,
            value: '选员工',
          },
          {
            id: 4,
            value: '看自己',
          },
        ],
      },
      dialogName: '请选择业务经理',
      dialogVisible: false,
      multipleNum: 1,
      rules: {
        name: [
          { required: true, message: '请选择业务经理', trigger: 'change' },
        ],
        year: [{ required: true, message: '请选择年份', trigger: 'change' }],
      },
    }
  },
  created(){
    this.options = this.levelTypeToList[sessionStorage.getItem('dataScope')] || []
  },
  methods: {
    disabledDate(time) {
      return time.getFullYear() > new Date().getFullYear()
    },
    handleChange(val){
      if (val == 2) {
        this.chooseDepartment('1') 
      }else if (val == 3){
        this.chooseDepartment('2') 
      }else if (val == 4) {
        this.form.name = sessionStorage.getItem('username')
        this.form.userId = sessionStorage.getItem('userid')
        this.form.departmentId = sessionStorage.getItem('departmentIds')
        console.log('this.form',this.form);
      }
    },
    handleVisibleChange(val) {
      console.log(val);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          
          if (this.form.name == 2 && !this.form.departmentId) {
            this.$message({
              type: 'info',
              message: '请选择要查看的部门',
            })
            return 
          }
          if (this.form.name == 3 && (!this.form.departmentId || !this.form.userId)) {
            this.$message({
              type: 'info',
              message: '请选择要查看的员工',
            })
            return 
          }
          if (checkPermission('kanban:detail')) {
            this.$router.push({
              path: '/kanban/salesman/detail',
              query: this.form,
            })
          } else {
            this.$message({
              type: 'info',
              message: '暂无查看权限，请联系管理员',
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    chooseDepartment(type) {
      this.dType = type
      this.dialogDepartmentVisible = true
      this.$refs.deRef.loadData()
      this.$refs.deRef.updateWorksId(this.selDepartmentData)
    },
    updateDepartmentVisible(val) {
      this.dialogDepartmentVisible = val
    },
    // 获取选择部门
    submitData(data) {
      if (this.dType == '1') {
        let departmentIds = data.map((item) => item.id)
        let departmentNames = data.map((item) => item.name)
        this.form.departmentId = departmentIds.join(',')
        this.form.name = departmentNames.join(',')
      }else if(this.dType == '2'){
        this.form.name = data[0].name
        this.form.userId = data[0].id
        this.form.departmentId = data[0].departmentId
      }
      this.selDepartmentData = data
      this.dialogDepartmentVisible = false
     
    },
  },
}
</script>

<style scoped lang="scss">
.itemheight /deep/.el-input__inner {
  height: 50px;
}
.div100 {
  height: 100%;
}
.logo {
  height: 100%;
  background: url(../../../assets/Group44216.png) no-repeat;
  background-size: 100% 100%;
}
.shuju {
  width: 478px;
  height: 618px;
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0px 20px 40px 0px #dee1ff;
  border-radius: 20px 20px 20px 20px;
  text-align: center;
  margin: auto 100px;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
.logotu {
  margin-top: 40px;
}
.wenzi1 {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 24px;
  color: #333333;
  margin-top: 60px;
  margin-right: 170px;
  margin-bottom: 32px;
}
.xuanze {
  width: 346px !important;
  margin: 0 auto;
  margin-bottom: 20px;
}
.xuanze1 {
  width: 346px !important;
}
.anniu {
  margin-top: 30px;
  height: 50px;
  width: 346px;
  background: #4285f4;
  border-radius: 4px 4px 4px 4px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
}
.xia {
  margin-bottom: 100px;
}
</style>