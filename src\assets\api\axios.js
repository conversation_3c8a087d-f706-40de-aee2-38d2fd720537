import axios from 'axios'
import { Message } from 'element-ui';
const api = {
    async get(url) {
        try {
            let res = await axios.get(url)
            res = res.data;
            return new Promise((resolve) => {
                if (res.status == 0) {
                    resolve(res)
                } else if (res.status == 403) {
                    window.location.href = ('/')
                    window.wm.$message.warning(res.msg)
                } else {
                    if (res.msg) {
                        Message.error(res.msg)
                    } else {
                        Message.error('系统错误')
                    }
                    reject(res)
                }
            })
        } catch (err) {

        }
    },
    async post(url, data) {
        try {
            let res = await axios.post(url, data)
            res = res.data
            return new Promise((resolve, reject) => {
                if (res.status == 0) {
                    resolve(res)
                } else if (res.code == 403) {
                    window.location.href = ('/')
                    Message.warning(res.msg)
                } else {
                    if (res.msg) {
                        Message({
                            showClose: true,
                            message: res.msg,
                            type: 'error'
                        });
                    } else {
                        Message.ererrorro('系统错误')
                    }
                    // reject(res)
                }
            })
        } catch (err) {

        }
    },
}
export { api }
