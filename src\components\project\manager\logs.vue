<template>
  <div class="maskcss" v-if="dataList.length>0">
    <logitem  v-for="(item,index) in dataList" :key="index" :isFirst="index == 0" :isLast="index == dataList.length-1" :itemData="item"></logitem>
    <div class="morebtn" v-if="!isLastPage">
      <span class="cur">加载更多</span> 
    </div>
  </div>
</template>

<script>
import logitem from '../common/logitem.vue';
import { projectlog } from '@/api/project/index'
export default {
  components:{
    logitem
  },
  data(){
    return{
      dataList:[],
      pageBean:{
        pageSize:10,
        pageNum:1,
        projectId:this.$route.query.id
      },
      isLastPage:false,
      isFirstPage:false,
    }
  },
  created(){
    
  },
  methods:{
    loadData(){
      projectlog(this.pageBean).then((result) => {
        this.dataList = result.data;
        this.isLastPage = result.page.isLastPage;
        this.isFirstPage = result.page.isFirstPage;
      }).catch((err) => {
        
      });
    },
  }
}
</script>

<style scoped>
.cur{
  cursor: pointer;
}
.morebtn{
  line-height: 30px;
  text-align: center;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #4285F4;
  margin-top: 20px;
  padding-bottom: 40px;
}
.maskcss{
  background-color: white;
  padding-top:20px;
  padding-left: 100px;
  padding-right: 100px;
  min-height: calc(100vh - 200px);
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
</style>