<template>
        <div class='mobanlist'>
          <el-form
            :model="ruleForm"
            ref="ruleForm"
            label-width="140px"
            class="ruleFormmo"
          >
            <template v-for="(item, index) in viewDataList">
              <el-form-item
                :prop="item.__vModel__"
                :show-message="true"
                :rules="[
                  {
                    required: item.isRequired == 1 ? true : false,
                    message: `${item.competItem}不能为空`,
                    trigger: 'blur',
                  },
                ]"
                :label="item.itemName"
                :key="index"
                v-if="item.itemType == 1"
              >
                <el-input v-model="ruleForm[item.__vModel__]"></el-input>
                {{ item.itemDescribe }}
              </el-form-item>
              <el-form-item
                :prop="item.__vModel__"
                :show-message="true"
                :rules="[
                  {
                    required: item.isRequired == 1 ? true : false,
                    message: `${item.competItem}不能为空`,
                    trigger: 'blur',
                  },
                ]"
                :label="item.itemName"
                :key="index"
                v-if="item.itemType == 2"
              >
                <el-input
                  type="textarea"
                  v-model="ruleForm[item.__vModel__]"
                ></el-input>
                {{ item.itemDescribe }}
              </el-form-item>
              <el-form-item
                :prop="item.__vModel__"
                :show-message="true"
                :rules="[
                  {
                    required: item.isRequired == 1 ? true : false,
                    message: `${item.competItem}不能为空`,
                    trigger: 'blur',
                  },
                ]"
                v-if="item.itemType == 3 || item.itemType == 4"
                :label="item.itemName"
                :key="index"
              >
                <el-upload
                  class="upload-demo"
                  action="https://jsonplaceholder.typicode.com/posts/"
                  multiple
                  :limit="3"
                  >
                  <div class="uploadcss">
                    <i class="el-icon-plus"></i>
                    <div class="uploadtext deffont">点击上传</div>
                  </div>
                  <div slot="tip" class="el-upload__tip">{{item.itemDescribe}}</div>
                </el-upload>
              </el-form-item>
            </template>
          </el-form>
          <el-button @click="goback" class="save" type="primary" 
            >返回</el-button
          >
        </div>
</template>

<script>
import {
  TemplateDetail,
} from '../../../api/index'
  export default {
      data() {
          return {
            viewDataList: [],
            ruleForm: {
              templateName: '',
            },
          }
      },
      created() {
        this.getisUp()
      },
      methods: {
        goback(){
          this.$router.back();
        },
        async getisUp() {
            let res = await TemplateDetail(this.$route.query.id)
            if (res.status != 0) return this.$message.error(res.msg)
            if (res.data) {
              let originData = res.data.templateItemVoList
              if (res.data.id == 0) {
                this.editOnOff = false
                originData.forEach((item) => {
                  item.__vModel__ = 'model_' + item.id
                })
                this.viewDataList = originData
              } else {
                originData.forEach((item) => {
                  item.__vModel__ = 'model_' + item.id
                  if (item.itemType == 1 || item.itemType == 2) {
                      this.$set(
                        this.ruleForm,
                        'model_' + item.id,
                        ''
                      )
                  } else if (item.itemType == 3 || item.itemType == 4) {
                    let fileListData = []
                    this.$set(this.ruleForm, 'model_' + item.id, fileListData)
                  }
                })
                this.viewDataList = originData
              }
            }
          },
      }
  }
</script>
<style scoped>
  .ruleFormmo .el-form-item {
      margin-bottom: 10px;
  }
  .uploadtext {
    color: #4285F4;
    cursor: pointer;
  }
  .el-icon-plus {
    font-size: 30px !important;
  }

  .uploadcss {
  width: 88px;
  height: 88px;
  line-height: 24px;
  background: #F5F5F5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #CCCCCC;
  display: inline-block;
}

.uploadcss i {
  margin-top: 20px;
  font-size: 24px;
  color: #4285F4;
}
  .mobanlist{
    width: 800px;
  }
  .blueline {
  border-left: 4px solid #2490ff;
  padding-left: 20px;
  margin-top: 0;
  margin-bottom: 20px;
}
  .mobantitlecss {
    height: 40px;
    background: #ffffff;
    border-radius: 10px;
    margin-bottom: 20px;
  }
  .save{
      display: block;
      margin: 0 auto;
  } 
</style>
