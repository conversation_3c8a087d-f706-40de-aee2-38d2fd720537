import service from '@/utils/request.js'
export function uploadFile(data) {
  return service.request({
    method: 'post',
    url: '/aliyun/oss/uploadFiles',
    data
  });
}


export function selectCustomer(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/customer/selectCustomer`,
    params
  });
}

export function queryVoListByCode(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/dicttype/queryVoListByCode`,
    params
  });
}



export function projectstage(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/projectstage/list`,
    params
  });
}

export function IsAddStage(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/projectstage/IsAddStage`,
    params
  });
}



// 获取素材资源列表
export function resMaterialList(data) {
  return service.request({
    method: 'get',
    url: `/resourceMaterial/pageResourceMaterialList?pageNo=${data.pageNum}&pageSize=${data.pageSize}&name=${data.name}`,
    data
  });
}
// 更新素材资源审核状态
export function resMaterialStatus(fid, status) {
  return service.request({
    method: 'get',
    url: `/resourceMaterial/updateStatus/${fid}/${status}`
  });
}

// /course/pageCourseList 分页查询资源库课程列表
export function resCourseList(data) {
  return service.request({
    method: 'get',
    url: `/goldcourse/manager/onlinelearningresource/queryList`,
    params: data
  });
}
//查询资源库课程小节列表
export function resSectionList(data) {
  return service.request({
    method: 'get',
    url: `/goldcourse/manager/onlinelearningresourcedetails/list`,
    params: data
  });
}

//查询资源库课程小节列表
export function queryRollList(data) {
  return service.request({
    method: 'get',
    url: `/goldcourse/manager/onlinelearningresource/queryRollList`,
    params: data
  });
}


// /course/updateStatus/{courseId}/{status}更新课程资源审核状态
export function resCourseStatus(fid, status) {
  console.log("zoulezouleozule");
  return service.request({
    method: 'get',
    url: `/course/updateStatus/${fid}/${status}`
  });
}


///course/pageCourseExamList/{courseId}查询资源库课程考试信息列表
export function resExamList(courseId) {
  return service.request({
    method: 'get',
    url: `/course/pageCourseExamList/${courseId}`
  });
}
///teachingMaterial/pageTeachingMaterialList分页查询数字化教材列表
export function resTeachingMaterialList(data) {
  return service.request({
    method: 'get',
    url: `/goldcourse/manager/digitizationtextbookinfo/list`,
    params: data
  });
}
///teachingMaterial/updateTeachingMaterial/{fid}/{status}跟新数字化教材
export function resTeachingMatStatus(fid, status) {
  return service.request({
    method: 'get',
    url: `/teachingMaterial/updateTeachingMaterial/${fid}/${status}`
  });
}

//goldcourse/manager/portaluserinfo/list分页查询pc端用户列表
export function fetchUserList(data) {
  return service.request({
    method: 'get',
    url: `/goldcourse/manager/portaluserinfo/list/?pageNum=${data.pageNum}&pageSize=${data.pageSize}&name=${data.name}&typeId=${data.typeId}`
  });
}



export function queryDictTypeList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/dicttype/queryDictTypeList`,
    params
  });
}



//机会列表
export function chanceList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/opportunity/selectOpportunity',
    params,
  });
}

//合同列表
export function contractList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/contract/selectContract',
    params,
  });
}


// 项目列表
export function projecttList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/project/selectProject',
    params,
  });
}

// 开设专业
export function listMajor(params) {
  return service.request({
    method: 'get',
    url: '/crm/business/unitspecialty/list',
    params,
  });
}
export function deleteMajor(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/unitspecialty/delete',
    data,
  });
}

export function nonSelectListAll(params) {
  return service.request({
    method: 'get',
    url: '/crm/business/unitspecialty/nonSelectListAll',
    params,
  });
}

export function addMajor(data) {
  return service.request({
    method: 'post',
    url: '/crm/business/unitspecialty/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 合同导出
export function contractExport(params) {
  return service.request({
    url: `/crm/controller/contract/exportExcel`,
    method: 'get',
    params
  })
}
