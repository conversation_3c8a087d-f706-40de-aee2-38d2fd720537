import service from '@/utils/request.js'
export function repositoryList(data) {
	return service.request({
		method: 'post',
		url: '/resource/selectAll',
		data
	});
}
export function getMenu() {
	return service.request({
		method: 'get',
		url: `/goldcourse/manager/resource/list`,
	});
}

export function deleteFile(params) {
	return service.request({
		method: 'get',
		url: '/aliyun/oss/deleteFile',
		params
	})
}

export function queryTreeByUserId(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/companydepartment/queryTreeByUserId',
		params
	})
}

export function queryAreaVoList(data) {
	return service.request({
		method: 'post',
		url: '/crm/controller/area/queryAreaVoList',
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
		data
	})
}

export function indexHe() {
	return service.request({
		method: 'post',
		url: '/crm/controller/homepage/index',
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
	})
}

export function hoemDetail(data) {
	return service.request({
		method: 'post',
		url: '/crm/controller/homepage/detail',
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
		data
	})
}

export function homeTaskList(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/task/queryOwnTaskList',
		params
	})
}


export function authCodeLogin(params) {
	return service.request({
		method: 'get',
		url: '/authCodeLogin',
		params
	})
}


// export function queryHomeList(params) {
// 	return service.request({
// 		method: 'get',
// 		url: '/crm/controller/remind/queryHomeList',
// 		params
// 	})
// }
export function queryHomeList(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/remind/queryListByPage',
		params
	})
}

export function remindSave(data) {
	return service.request({
		method: 'post',
		url: '/crm/controller/remind/save',
		data,
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
	})
}
//
export function remindDelete(data) {
	return service.request({
		method: 'post',
		url: '/crm/controller/remind/delete',
		data,
	})
}

export function remindInfo(id) {
	return service.request({
		method: 'get',
		url: `/crm/controller/remind/info/${id}`
	});
}







export function saveTemplate(data) {
	return service.request({
		method: 'post',
		url: `/crm/business/template/save`,
		data,
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
	});
}

export function updateTemplate(data) {
	return service.request({
		method: 'post',
		url: `/crm/business/template/update`,
		data,
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
	});
}




export function isExist(data) {
	return service.request({
		method: 'post',
		url: `/crm/controller/personalgoals/isExist  `,
		data,
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
	});
}

export function TemplateDetail(id) {
	return service.request({
		method: 'get',
		url: `/crm/business/template/info/${id}`,
	});
}


export function TemplateDetailRiZhi(params) {
	return service.request({
		method: 'get',
		url: `/crm/business/template/TemplateInfo`,
		params
	});
}

export function detailId(data) {
	return service.request({
		method: 'post',
		url: `/crm/business/template/deleteItem`,
		data
	});
}


export function deleteFileById(id) {
	return service.request({
		method: 'get',
		url: `/teaching/web/tcworksinfo/delete/${id}`,
	});
}


// /crm/controller/fileInfo/info

export function fileInfo(data) {
	return service.request({
		method: 'post',
		url: `/crm/controller/fileInfo/info`,
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
		data
	});
}

export function verifyDepartmentList(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/companydepartment/queryDepartmentList`,
	  params
	});
}

//首页待办接口
export function queryTodoNum(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/queryTodoNum`,
	  	params
	});
}
//首页消息接口
export function queryMessageNum(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/queryMessageNum`,
	  	params
	});
}
//首页待办、消息列表 （ruleType 1：合同开票；2：合同回款；3：合同退款；4：教材报订；5：项目成本）
export function todoList(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/processList`,
	  	params
	});
}
//首页待办、消息工单
export function workOrderList(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/workOrderList`,
	  	params
	});
}
//首页待办、消息 计划与总结
export function planList(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/planList`,
	  	params
	});
}
//首页消息：type    3：客户重要日期；7：机会阶段变更；8：合同状态变更
export function remindMessageList(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/remindMessageList`,
	  	params
	});
}

//首页消息 项目与任务
export function taskMessageList(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/taskMessageList`,
	  	params
	});
}
//首页消息 客户重要日期
export function remindCustomerCareList(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/remindCustomerCareList`,
	  	params
	});
}
//首页消息 跟进与拜访
export function remindVisitList(params) {
	return service.request({
		method: 'get',
		url: `/crm/controller/homepage/remindVisitList`,
	  	params
	});
}