<template>
    <el-dialog class="contractDialog" title="选择合同" top="80px" :visible.sync="dialogTableVisible" width="70%" center>
        <el-form class="unitfcss" :inline="true">
            <el-form-item label="合同名称：">
                <el-input clearable="" class="definput" v-model="pageBean.contractTitle" placeholder="请输入合同名称"></el-input>
            </el-form-item>
            <el-form-item label="">
                <el-button class="defaultbtn ml20" icon="el-icon-search" type="primary" @click="handleClick">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table class="mytable" :data="dataList" >
            <el-table-column width="200" :show-overflow-tooltip="true" class-name="column_blue" property="contractTitle" label="合同标题" ></el-table-column>
            <el-table-column property="contractNumber" label="合同编号" ></el-table-column>
            <el-table-column property="customerName" label="客户名称" ></el-table-column>
            <el-table-column property="unitName" label="客户单位" ></el-table-column>
            <el-table-column property="contractAmount" label="合同金额（万元）" ></el-table-column>
            <el-table-column property="discount" label="整单折扣" ></el-table-column>
            <el-table-column
                prop="returnStatus"
                label="回款状态"
                >
                <template slot-scope="scope">
                  {{ returnToName[scope.row.returnStatus] }}
                </template>
            </el-table-column>
            <el-table-column
        prop="invoicingStatus"
        label="开票状态"
        >
        <template slot-scope="scope">
          {{ invoicToName[scope.row.returnStatus] }}
        </template>
      </el-table-column>
            <el-table-column property="createTime" label="创建时间" width="160">
            </el-table-column>
            <el-table-column property="edit" label="操作" width="80" fixed="right">
                <template slot-scope="scope" >
                    <div class="rbtn deffont"  v-show="scope.row.isSelect" >取消选择</div>
                    <div class="bbtn deffont"  v-show="scope.row.isSelect == 0" @click="chooseAction(scope.row)">选择</div>
                </template>
            </el-table-column>
        </el-table>
        <div class="center">
            <el-button class="defaultbtn" type="primary" @click="submitAction">提交</el-button>
        </div> 
        <page 
        :currentPage="pageBean.pageNum" 
        :pageSize="pageBean.pageSize" 
        :total="total"
        @updatePageNum="handleCurrentChange"
        ></page>
    </el-dialog>
</template>

<script>


import {contractList} from '@/api/wapi'
import page  from './page.vue';

export default {
    components:{
        page
    },
    data(){
        return{
            activeName:"first",
            dataList:[],
            pageBean:{
                pageNum:1,
                pageSize:8,
                contractTitle:""
            },
            total:0,
            contractData:{},
            tempData:{},
            returnToName:{
                1:"未回款",
                2:'部分回款',
                3:'全部回款',
                4:'超额回款'
            },
            invoicToName:{
                1:"未开票",
                2:'部分开票',
                3:'全部开票',
                4:'超额开票'
            },
        }
    },
    props:{
        visible:{
            type:Boolean,
            default:false
        },
    },
    computed:{
        dialogTableVisible:{
            get(){
                return this.visible;
            },
            set(val){
                this.$emit('updateVisible',val);
            },
        }
    },
    methods:{
        selectContractData(params){
            if(params){
              this.tempData = params
            }else{
                params = this.tempData
            }
                let sData = {...params,...this.pageBean}
                contractList(sData).then(res=>{
                        if(res.status == 0){
                                this.dataList = res.data
                                this.total = res.page.total
                                this.dataList.forEach(item=>{
                                    this.$set(item,'isSelect',false)
                                })
                                if(this.contractData.id){
                                    this.selected(this.dataList)
                                }
                        }else{
                            this.msgError(res.msg)
                        }
                })
        },
        selected(dataList){
            dataList.forEach(item=>{
                if(item.id === this.contractData.id){
                    this.$set(item,'isSelect',true)
                }
            })
        },
        handleClick(){
            this.selectContractData()
        },
        submitAction(){
            this.$emit('updateData',this.contractData)
            this.dialogTableVisible = false;
            this.contractData = {}
        },
        handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.selectContractData()
        },
        chooseAction(val){
            this.dataList.forEach(item=>{
                item.isSelect = false
            })
            this.contractData = val;
            val.isSelect = true
        }
    }
}
</script>
<style scoped>
.center{
    margin-top: 50px;
    text-align: center;
}
.bbtn{
    color: #4285F4;
    cursor: pointer;
}
.rbtn{
    color: #F45961;
    cursor: pointer;
}

.unitfcss{
    line-height: 34px;
}
.unitfcss /deep/.el-form-item__label{
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
}
.unitfcss /deep/.el-form-item{
    margin-bottom: 20px;
    /* padding-right: 60px; */
}
.ml20{
    margin-left: 20px;
}
/* .contractDialog{
    z-index: 999999 !important;
} */
.contractDialog /deep/.el-dialog__body{
    padding: 20px;
    padding-top: 0px;
    
}
.contractDialog /deep/.el-dialog__header{
    border: none;
}
</style>
<style scoped>
.tabscss /deep/.el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 40px;
  width: 240px !important;
  text-align: center;
  line-height: 40px;
  /* padding: 0 60px; */
}

</style>