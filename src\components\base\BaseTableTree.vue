<!-- 表格 -->
<template>
  <div>
    <el-table
    :data="datalist11"
    style="width: 100%;"
    :row-key="getRowKeys">
    <el-table-column
      prop="name"
      label="资源名称"
      fixed="left"
      width="200"
      header-align="center">
    </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  data () {
    return {
      datalist11: [],
      search: {}
    }
  },
  methods: {
    getRowKeys (row) {
      // let num = Math.round(Math.random()*100)
      return row.id;
    },
    async init (obj) {
      let res = await this.$axios.get('/sf/business/companyposition/positionList', { params: obj })
      // let res = await this.$axios.get('/sf/business/resource/list')
      if (res.status === 0) {
        // let str = JSON.stringify(res.data)
        // let targetstr = str.replace(/childrenList/g, 'children')
        // this.datalist11 = JSON.parse(targetstr)
        this.datalist11 = res.data
      }
    }
  },
  created () {
    this.search.pageSize = '10';
    this.search.pageNum = '1';
    this.init(this.search)
  }
}
</script>
