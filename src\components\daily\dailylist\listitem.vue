<template>
  <div class="itemheader right_item">
    <div class="fixheader" ref="testRef" :class="{fixed: item.isFixed}">
      <div class="minheader">
        <div class="exportcss" v-if="isShowButton">
          <el-dropdown @command="commandClick" trigger="click">
            <i class="el-icon-more f16"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1" >查看详情</el-dropdown-item>
              <el-dropdown-item :command="2"  class="deletecss" v-if="level == 4 || userId == item.createBy"> 删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <headuser :url="item.logo" width="48" :username="item.createByName"></headuser>
        <div>
          <p class="pname">{{ item.createByName }}</p>
          <p class="">
            <span class="spantext">{{ item.createTime }}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="fixedcss" v-if="item.isFixed">
    </div>
    <div class="contentdiv">
      <div class="mlright">
        <slot></slot>
        <div class="modelList">
          <showmodel :viewDataList="item.templateItemVos"></showmodel>
        </div>
          <p class="linemar">关联客户:</p>
          <p class="textcss custext" v-for="(item,index) in item.dailyRecordCustomerVos" :key="index" @click="showCus(item.customerId)">
                <span>{{item.unitName}}</span>
                -
                <span>{{item.customerName}}</span>
          </p>
          <p v-if="item.dailyRecordCustomerVos==0" class="notext">未关联</p>
                <div class="flexd">
                      <span @click="showRead" v-if="item.allReadFlag==0 && item.readNum >0" class="readnum cuspor">{{item.readNum}}人已读</span>
                      <span @click="showRead" v-if="item.allReadFlag==1" class="readnum cuspor">
                        <img src="../../../assets/yesread.png" alt="">
                        全部已读
                      </span>
                      <span class="cuspor" @click="showRead" v-if="item.allReadFlag==0 && item.readNum == 0">
                        <div class="xmbook_red_point_4">
                        </div>
                        <span class="readnum noread">
                          未读
                        </span>
                      </span>
                      <div class="m10">
                          <img src="@/assets/commit.png" alt="" />
                          <span class="spantext commentnum">{{item.commentNumber}}</span>
                      </div>
                </div>
                <div class="buttoncursor" @click="expandOrShou()">
                  <img v-show="!expand &&item.commentNumber>0" src="@/assets/xia.png" alt="" />
                  <span v-show="!expand &&item.commentNumber>0" class="expand">展开</span>
                  <img v-show="expand && item.commentNumber>0" src="@/assets/shang.png" alt="" />
                  <span v-show="expand && item.commentNumber>0" class="expand">收起</span>
              </div>
                <el-collapse-transition>
                  <div v-if="expand">
                    <comment  @reply="reply" v-for="(item,index) in commentArr" :key="index" :item="item" @deleteAction="deleteAction" ></comment>
                  </div>
                </el-collapse-transition>
                <div class="savediv">
                  <el-input show-word-limit maxlength="400" clearable="" ref="elRef" class="inputcommont" v-model.trim="value" placeholder="请输入~"></el-input>
                  <el-button @click="commentButton()" class="savebutton" type="primary">发送</el-button>
                </div>
            </div>
    </div>
  
            <dc-dialog :iType="dialogType" title="温馨提示" width="500px" :showCancel="isShowCancel" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
              <p class="pcc">确定要删除么?</p>
            </dc-dialog>
            <replyDialog ref="replyDialog" :visible.sync="replayDialogVisible" :title="replayTitle" @updateVisible="updateReplayVisible" @replyData="replyData"></replyDialog>
            <div class="bline"> </div>
  </div>
</template>

<script>
import showmodel from '@/components/zongjie/temmodel/showmodel.vue';
import comment from './components/comment.vue'
import replyDialog from '@/components/common/replyDialog.vue'
import textBorder from '@/components/common/textBorder.vue'
import { commentList, sendSave, visitDelete, commentDelete } from '@/api/visit/index'
import { dailyDelete } from '@/api/daily/index'
import headuser from './components/headuser.vue'
import dataitem from './dataitem.vue';
export default {
  name: 'listItem',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    index: {
      type: Number,
      default: 0
    },
    isShowButton: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
            level: sessionStorage.getItem('dataScope'),
            userId: sessionStorage.getItem('userid'),
            dpsn:6,
            expand: false,
            value: '',
            commentArr: [],
            commentId: '',
            parentId: '',
            dialogType: 1,
            isShowCancel: true,
            dialogVisible: false,
            replayDialogVisible: false,
            replayTitle: '',
    }
  },
  components: {
    comment,
    replyDialog,
    headuser,
    showmodel
  },
  methods: {
    showRead(){
      this.$emit('showRead',this.item)
    },
    submitDialog() {
      let params = {
        id: this.item.id
      }
      dailyDelete(params).then(res => {
        if (res.status == 0) {
          this.msgSuccess('删除成功')
          this.dialogVisible = false
          this.$emit('dataListDelete',this.index)
        }
      })
    },
    commandClick(value) {
      if (value == 1) {
        this.$emit('viewDetail',this.item)
      }
      if (value == 2) {
        this.dialogVisible = true
      }
    },
    reply(data) {
      this.parentId = data.id,
        this.updateReplayVisible(true);
      this.replayTitle = `回复@${data.name}`
    },

    updateReplayVisible(val) {
      this.replayDialogVisible = val;
    },
    replyData(data) {
      this.save(data);
      this.$refs.replyDialog.closeAction();
    },
    save(comment) {
      this.commentId = this.item.id
      let params = {
        visitId: this.commentId,
        commentType:2,
        comment: comment,
        parentId: this.parentId || 0
      }
      sendSave(params).then(res => {
        if (res.status == 0) {
          this.commentListData(()=>{
            this.$nextTick(()=>{
                this.$emit('updateTopBottom')
            })
          })
          this.expand = true
          this.value = ''
        }
        if (!this.parentId) {
          this.$emit('updateCommont', this.item)
        }
        this.parentId = 0
      }).catch((err) => {
        this.parentId = 0;
      });
    },
    deleteAction(commentId) {
      commentDelete({ id: commentId }).then((result) => {
        if (result.data) {
          this.$message({
            type: 'success',
            message: '删除成功'
          })
          this.commentListData(()=>{
            this.$nextTick(()=>{
                this.$emit('updateTopBottom')
            })
          })
        } else {
          this.$message({
            type: 'error',
            message: '删除失败'
          })
        }
      }).catch((err) => {

      });
    },
    commentButton() {
      if (this.value == '') {
        this.msgError('请输入评论内容')
        return
      }
      this.save(this.value);
    },
    commentListData(callback) {
      commentList({ visitId: this.commentId }).then(res => {
        if (res.status == 0) {
          this.commentArr = res.data
          this.item.commentNumber = res.data.length
          this.$nextTick(()=>{
            callback()
          })
        }
      })
    },
  
    expandOrShou() {
      this.expand = !this.expand
      if (this.expand) {
        this.commentId = this.item.id
        this.commentListData(()=>{
                this.$emit('updateTopBottom')
        })
      }else{
        this.$nextTick(()=>{
          this.$emit('updateTopBottom')
        })
      }
    },
    showCus(cusId){
      this.$emit('showCustomer',cusId)
    },
  }
}
</script>

<style>
textarea {
  white-space: pre-wrap;
}
</style>

<style lang="scss" scoped>
  .minheader{
    width: 740px;
    margin: 0 auto;
  }
  .cuspor{
    cursor: pointer;
  }
  .deletecss{
    color: #f56c6c;
  }
  .notext{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #BAC9E3;
  }
  .noread{
    display: inline-block;
    vertical-align: middle;
    margin-left: 6px;
  }
  .fixheader{
    padding: 20px 0;
  }
  .modelList{
    margin-top: 20px;
  }
  .xmbook_red_point_4{
            display: inline-block;
            border-radius: 50%;
            border: 2px solid;
            width: 16px;
            height: 16px;
            border: 1px solid #768EB8;
            vertical-align: middle;
  }
  .fixedcss{
    height: 80px;
  }
  .bline{
    height: 1px;
    background:  #F0F0F0;
    margin-top: 40px;
  }
  .readnum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #BAC9E3;
  }
  .flexd{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .f16{
    cursor: pointer;
  }
    .fixed {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      z-index: 99;
      box-shadow: 0px 2px 12px 0px rgba(15,27,50,0.08);
      background: #fff;
  }

  .linemar{
    margin: 16px 0 8px 0;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
  }
  .textcss{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
  }
  .cname{
    color: #4285F4;
    cursor: pointer;
  }
.exportcss {
  float: right;
  margin-top: 10px;
}

.m10 {
  margin: 10px 0;
}

.labeltext {
  margin-bottom: 5px !important;
}
.divtext {
  width: 100px;
  height: 100px;
  background: #000;
  opacity: 0.5;
}

.custext{
    color: #4285F4;
    cursor: pointer;
  }

.savediv {
  margin-top: 20px;
}

.inputcommont {
  width: calc(100% - 88px);
  margin-right: 16px;
}
.inputcommont  /deep/ .el-input__inner {
  height: 34px !important;
  line-height: 34px !important;
}

.savebutton {
  width: 72px;
  padding: 9px 5px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #FFFFFF;
}

.itemheader {
  padding: 10px 0;
}

.buttoncursor {
  cursor: pointer;
  display: inline-block;
  margin: 6px 0;
}

.expand {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
}

.commentnum {
  margin-left: 6px;
}


.imgco {
  margin-right: 12px;
}

.mlright {
  margin-left: 57px;
}


.spantext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.pname {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-bottom: 8px;
}

.head {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 8px;
}
</style>
