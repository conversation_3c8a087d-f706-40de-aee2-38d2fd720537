<template>
  <div class='mobanlist'>
    <el-form
      ref="ruleForm"
      label-width="100%"
      class="ruleFormmo showmodel"
    >
      <template v-for="(item, index) in viewDataList">
        <el-form-item
          :label="item.itemName+':'"
          v-if="item.itemType == 1 || item.itemType == 2"
        >
          <p class="motext">
            {{item.itemDetailVos.length>0?item.itemDetailVos[0].templateItemValue:'暂无'}}
          </p>
        </el-form-item>
        <el-form-item
          v-if="item.itemType == 3"
          :label="item.itemName+':'"
        >
        <ul class="clearfix guancontent" v-if="item.itemDetailVos.length>0">
          <li :key="index" v-for="(item1,index) in item.itemDetailVos" @click="viewPdf(item1)" class="liitem left">{{ item1.templateItemFileName }}</li>
        </ul>
        <p v-else>暂无</p>
        </el-form-item>
      </template>
    </el-form>
    <fileview ref="fileview"></fileview>
  </div>
</template>

<script>
import {
   TemplateDetail,
} from '../../../api/index'

import {getUrlType} from '@/utils/tools.js'
import fileview from '../../common/fileview.vue';
export default {
  components:{
    fileview
  },
  props:{
        viewDataList:{
          type:Array,
          default:()=>[]
        },
    },
data() {
    return {
 
    }
},
created() {
},
methods: {
  viewPdf(item){
    let mediaType = getUrlType(item.templateItemUrl)
    console.log('sssss',mediaType,item.templateItemUrl)
    if(mediaType ==1){  //图片
      this.$maskV.show({
        type: 1,
        url: item.templateItemUrl,
        onClose: () => {
        }
      })
      return 
    }
    if(mediaType ==2){  //视频
      this.$maskV.show({
        type: 2,
        url: item.templateItemUrl,
        onClose: () => {
        }
      })
      return 
    }
    if(mediaType ==3){  //pdf
          this.$refs.fileview.show({
          url:item.templateItemUrl,
          fileName:item.templateItemFileName
         })
      return 
    }
  },
  async getisUp() {
      let res = await TemplateDetail(this.$route.query.id)
      if (res.status != 0) return this.$message.error(res.msg)
      if (res.data) {
        let originData = res.data.templateItemVoList
        if (res.data.id == 0) {
          this.editOnOff = false
          originData.forEach((item) => {
            item.__vModel__ = 'model_' + item.id
          })
          this.viewDataList = originData
        } else {
          originData.forEach((item) => {
            item.__vModel__ = 'model_' + item.id
            if (item.itemType == 1 || item.itemType == 2) {
                this.$set(
                  this.ruleForm,
                  'model_' + item.id,
                  ''
                )
            } else if (item.itemType == 3 || item.itemType == 4) {
              let fileListData = []
              this.$set(this.ruleForm, 'model_' + item.id, fileListData)
            }
          })
          this.viewDataList = originData
        }
      }
    },
}
}
</script>
<style>
  .showmodel .el-form-item__label{
      float: none;
      line-height: 24px;
      font-size: 20px;
      margin-bottom: 10px;
      display: block;
      text-align: left;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
  }
  .showmodel .el-form-item__content{
      margin-left: 0 !important;
      line-height: 26px;
  }
</style>
<style scoped>
.motext{
  word-wrap:break-word; 
    word-break:break-all;
}

.guancontent{
  margin: 10px 0;
  margin-left: 18px;
}
.liitem{
  border-radius: 4px 4px 4px 4px;
  border: 1px dashed #4285F4;
  padding: 5px 8px ;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  box-sizing: border-box;
  cursor: pointer;
  margin-right: 20px;
  line-height: 14px;
  box-sizing: border-box;
  margin-bottom: 10px;
}






</style>
