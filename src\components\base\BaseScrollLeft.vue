<!--
  @Desc 抽屉组件
  @params
  isShow(必填)：是否显示
  title(选填)：标题 默认'提示'
  width(选填)：宽度 默认'300px'
  placement(选填)：方向 默认'right'
-->
<template>
  <section class="drawer-wrap" v-show="showMask" @click.self="showMask = false">
    <div class="drawer-box" @click.self="showMask = false">
      <div class="drawer-content" :style="defaultSty" :class="placementDir">
        <div class="drawer-ivu">
          <div class="drawer-header"><span>{{title}}</span><i class="el-icon-close drawer-x" @click="showMask = false"></i></div>
          <div class="drawer-font">
            <slot></slot>
          </div>
          <slot name="footer"></slot>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    isShow: {
      type: Boolean
    },
    title: {
      type: String,
      default: '提示'
    },
    width: {
      type: String,
      default: '300px'
    },
    placement: {
      type: String,
      default: 'right'
    }
  },
  computed: {
    defaultSty () {
      return {
        width: this.width
      }
    },
    placementDir () {
      if (this.placement === 'right') {
        return 'drawer-diright'
      } else if (this.placement === 'left') {
        return 'drawer-dileft'
      }
    }
  },
  watch: {
    isShow (val) {
      this.showMask = val
    },
    showMask (val) {
      this.$emit('update:isShow', val)
    }
  },
  data () {
    return {
      showMask: false
    }
  }
}
</script>

<style scoped lang="scss">
.drawer-wrap{
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(55,55,55,.6);
  height: 100%;
  z-index: 10000;
  .drawer-box{
    position: fixed;
    overflow: auto;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    outline: 0;
    .drawer-diright{
      transition: width linear 3s;
      right: 0;
    }
    .drawer-dileft{
      left: 0;
    }
    .drawer-content{
      position: fixed;
      height: 100%;
      top: 0;
      width: 0;
      // animation: action_translateY 0.5s forwards ease-out;
      // @keyframes action_translateY {
      //   0% {
      //     opacity: 0;
      //   }
      //   100% {
      //     opacity: 1;
      //   }
      // }
    }
    .drawer-ivu{
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      bottom: 0;
      background-color: #fff;
      border: 0;
      background-clip: padding-box;
      box-shadow: 0 4px 12px rgba(0,0,0,.15);
    }
    .drawer-header{
      border-bottom: 1px solid #e8eaec;
      padding: 14px 16px;
      span{
        font-size: 14px;
        font-weight: bold;
      }
      .drawer-x{
        float: right;
        font-size: 20px;
        cursor: pointer;
      }
      .drawer-x:hover{
          animation: rotate1 0.5s linear;
        }
        @keyframes rotate1
        {
          from {
          transform:rotate(0deg);
          -ms-transform:rotate(0deg);
          -moz-transform:roate(0deg);
          -webkit-transform:rotate(0deg);
          -o-transform:rotate(0deg);
          }
          to {
          transform:rotate(180deg);
          -ms-transform:rotate(180deg);
          -moz-transform:rotate(180deg);
          -webkit-transform:rotate(180deg);
          -o-transform:rotate(180deg);
          }
        }
    }
    .drawer-font{
      width: 100%;
      padding-bottom: 53px;
      padding: 10px;
      box-sizing: border-box;
      word-wrap: break-word;
      position: static;
      height: calc(100% - 55px);
      overflow: auto
    }
    .footer{
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      border-top: 1px solid #e8e8e8;
      padding: 10px 16px;
      text-align: right;
      box-sizing: border-box;
      -webkit-tap-highlight-color: transparent;
    }
  }
}
</style>
