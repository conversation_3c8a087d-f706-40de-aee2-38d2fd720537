<template>
    <div>
        <div class="time">{{ currentTime }}</div>
    </div>
</template>    

<script>

export default {
    components:{

    },
    data() {
        return {
            currentTime: this.getCurrentTime(),
        }
    },
    mounted() {
        this.updateTime(); // 在组件挂载后开始定时更新时间
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer); // 组件销毁前清除定时器
        }
    },
    methods: {
        getCurrentTime() {
            const now = new Date();
            const year = now.getFullYear(); // 年
            const month = now.getMonth() + 1; // 月
            const day = now.getDate(); // 日
            const hours = now.getHours(); // 小时
            const minutes = now.getMinutes(); // 分钟
            const seconds = now.getSeconds(); // 秒
            // 格式化日期和时间
            return `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        },
        updateTime() {
            this.timer = setInterval(() => {
                this.currentTime = this.getCurrentTime(); // 每秒更新当前时间
            }, 1000); // 设置定时器，每1000毫秒更新一次时间
        }
    }
}
</script>
<style lang="scss" scoped>

.time {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    margin-top: 10px;
}

</style>

