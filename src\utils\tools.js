/**
 * 取窗口可视范围的宽度
 */
import store from "@/store";
import dayjs from "dayjs";
import { queryDictTypeList } from "@/api/wapi";
export let getClientWidth = function () {
  let clientWidth = 0;
  if (document.body.clientWidth && document.documentElement.clientWidth) {
    clientWidth =
      document.body.clientWidth < document.documentElement.clientWidth
        ? document.body.clientWidth
        : document.documentElement.clientWidth;
  } else {
    clientWidth =
      document.body.clientWidth > document.documentElement.clientWidth
        ? document.body.clientWidth
        : document.documentElement.clientHeclientWidthight;
  }
  return parseFloat(clientWidth);
};

/**
 * @param {str} str
 * @Desc 返回年月日时分秒
 */
export let ruleTime = function (str) {
  return dayjs(str).format("YYYY-MM-DD HH:mm:ss");
};
/**
 * @param {str} str
 * @Desc 返回年月日
 */
export let ruleTimeYear = function (str) {
  return dayjs(str).format("YYYY-MM-DD");
};
export let ruleTimeMonth = function (str) {
  return dayjs(str).format("hh:mm:ss");
};
export let ruleTimeSuccess = function (str) {
  return dayjs(str).format("YYYY-MM-DD hh:mm:ss");
};

export let downloadExcelFile = function (data, filename) {
  let blob = new Blob([data], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  });
  let href = window.URL.createObjectURL(blob);
  let a = document.createElement("a");
  a.setAttribute("href", href);
  a.setAttribute("download", filename);
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(href);
};

function base64ToUint8Array(base64String) {
  // 将base64字符串转换为二进制字符串
  const binaryString = atob(base64String);
  // 创建一个Uint8Array，每个字符代表一个字节
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

export let downloadExcelFileCommon = function (data, filename) {
  let blob = new Blob([base64ToUint8Array(data.bytes)], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  });
  let href = window.URL.createObjectURL(blob);
  let a = document.createElement("a");
  a.setAttribute("href", href);
  a.setAttribute("download", filename);
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(href);
};

export let downloadFileByUrl = function (url, filename) {
  let a = document.createElement("a");
  a.setAttribute("href", url);
  a.setAttribute("download", filename);
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(href);
};

/**
 * @desc 校验手机号是否合法
 * @param {String} phoneNum 手机号
 * @return {Boolean}
 */
export let validatePhoneNum = function (phoneNum) {
  let reg = /^1\d{10}$/;
  return reg.test(phoneNum);
};

export function validEmail(email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return reg.test(email);
}
export let validateWWw = function (url) {
  let reg = /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/;
  return reg.test(url);
};

export function ValidatePhone(val) {
  var isPhone = /^1[3456789]\d{9}$/; //手机号码
  var isMob = /^([0-9]{3,4}-)?[0-9]{7,8}$/;
  if (isMob.test(val) || isPhone.test(val)) {
    return true;
  } else {
    return false;
  }
}

export function ValidateFaxNumber(faxNumber) {
  var pattern = /^\d{3}-\d{7,8}$/;
  return pattern.test(faxNumber);
}

/**
 * @desc 合法的数字输入框
 * @param {String} phoneNum 手机号
 * @return {Boolean}
 */
export let validateInputNumber = function (numbers) {
  let reg = /^[0-9]*$/;
  return reg.test(numbers);
};

export function treeIterator(tree, func) {
  let node,
    curTree = [...tree];
  while ((node = curTree.shift())) {
    func(node);
    node.children && curTree.unshift(...node.children);
  }
}

/**
 * 根据树子节点ID查找所有父节点ID
 * @param {array} dataSource 树形结构数据源
 * @param {number} nodeId 子节点ID
 * @returns {array} 包含所有父节点ID的数组，按照从根节点到直接父节点的顺序排序
 */
export function findParentIds(dataSource, nodeId) {
  const parentIds = []; // 用于存储所有父节点ID的数组

  // 定义一个递归函数，用于遍历整棵树并查找子节点的所有父节点
  function traverse(node, nodeId) {
    if (node.resourceId === nodeId) {
      // 如果当前节点的ID等于子节点的ID，则表示已经找到了子节点，可以开始向上查找父节点
      return true; // 返回true表示已经找到了子节点
    }

    if (node.children) {
      // 如果当前节点有子节点，则继续遍历子节点
      for (const childNode of node.children) {
        if (traverse(childNode, nodeId)) {
          // 如果在子节点中找到了子节点的父节点，则将当前节点的ID添加到父节点ID数组中，并返回true表示已经找到了子节点
          parentIds.push(node);
          return true;
        }
      }
    }

    return false; // 如果当前节点不是子节点的父节点，则返回false
  }

  // 从根节点开始遍历整棵树，并调用递归函数查找子节点的所有父节点
  for (const node of dataSource) {
    if (traverse(node, nodeId)) {
      // 如果在当前节点的子树中找到了子节点的父节点，则直接退出循环
      break;
    }
  }

  return parentIds; // 返回所有父节点ID的数组
}

// const parentIds = findParentIds(dataSource, 10);
// console.log(parentIds); // [4, 1]

/* 建立缓存区，解决环形引用问题 */
let cache = new WeakMap(); // 使用 WeakMap 为了防止内存泄露

export function deepClone(value) {
  // 排除原始类型的情况，函数时也满足此条件
  if (typeof value !== "object" || value === null) return value;
  // 解决环形引用问题(即循环引用)
  // const cached = cache.get(value);
  // console.log(cached)
  // if (cached) return cached;
  // 克隆结果：1.数组 2.普通对象
  const result = Array.isArray(value) ? [] : {};
  // 设置克隆结果的原型链为 value 的原型链(即保持原型一致)
  Object.setPrototypeOf(result, Object.getPrototypeOf(value));
  // 环形引用时将克隆的值储存到缓存中
  cache.set(value, result);
  // 浅层克隆
  for (const key in value) {
    // 排除原型上的属性
    if (value.hasOwnProperty(key)) {
      result[key] = deepClone(value[key]); // 针对这个对象的每一个属性值进行克隆，则达到深度克隆效果
    }
  }
  return result;
}

export const getDict = async dictName => {
  if (!sessionStorage.getItem("dict")) {
    let res = await queryDictTypeList();
    if (res.status == 0) {
      sessionStorage.setItem("dict", JSON.stringify(res.data));
      return JSON.parse(sessionStorage.getItem("dict"))[dictName][0]
        .dictItemVoList;
    }
  } else {
    return JSON.parse(sessionStorage.getItem("dict"))[dictName][0]
      .dictItemVoList;
  }
};

export const fileNameToType = {
  image: 1,
  video: 2,
  radio: 3,
  file: 4
};

export const getFileTypeNum = url => {
  return fileNameToType[getFileType(url)];
};

/* 根据后缀判断文件类型 */
export const getFileType = fileName => {
  let suffix = ""; // 后缀获取
  let result = ""; // 获取类型结果
  if (fileName) {
    const flieArr = fileName.split("."); // 根据.分割数组
    suffix = flieArr[flieArr.length - 1]; // 取最后一个
  }
  if (!suffix) return false; // fileName无后缀返回false
  suffix = suffix.toLocaleLowerCase(); // 将后缀所有字母改为小写方便操作
  // 匹配图片
  const imgList = ["png", "jpg", "jpeg", "bmp", "gif"]; // 图片格式
  result = imgList.find(item => item === suffix);
  if (result) return "image";
  // 匹配excel
  const excelList = [
    "xls",
    "xlsx",
    "doc",
    "docx",
    "pdf",
    "ppt",
    "pptx",
    "rar",
    "zip"
  ];
  result = excelList.find(item => item === suffix);
  if (result) return "file";
  const videoList = [
    "mp4",
    "m2v",
    "mkv",
    "rmvb",
    "wmv",
    "avi",
    "flv",
    "mov",
    "m4v"
  ];
  result = videoList.find(item => item === suffix);
  if (result) return "video";
  // 匹配音频
  const radioList = ["mp3", "wav", "wmv"];
  result = radioList.find(item => item === suffix);
  if (result) return "radio";
  // 其他文件类型
  return "other";
};

export const addSnop = str => {
  return str + "?x-oss-process=video/snapshot,t_1000,m_fast";
};

function bfsTreeEach(tree, func) {
  let node,
    nodes = tree.slice();
  while ((node = nodes.shift())) {
    func(node);
    if (node.children && node.children.length) {
      nodes.push(...node.children);
    }
  }
}

export const valide = str => {
  let pathList = [];
  bfsTreeEach(store.getters.AllRouter, node => {
    pathList.push(node.path);
  });
  if (pathList.includes(str)) {
    return true;
  }
  return false;
};

export const getUrlType = url => {
  var suffixType = url.substring(url.lastIndexOf(".") + 1); //截取后缀名  输出：jpg
  let obj = {
    PNG: 1,
    png: 1,
    jpg: 1,
    JPG: 1,
    JPEG: 1,
    jpeg: 1,
    mp4: 2,
    pdf: 3,
    doc: 3,
    docx: 3,
    xlsx: 3,
  };
  return obj[suffixType];
};

export function removeById(arr, id) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].id === id) {
      arr.splice(i, 1);
      break;
    }
  }
  return arr;
}

export function contains(arr, val) {
  for (var i = 0; i < arr.length; i++) {
    if (arr[i].id === val) {
      return true;
    }
  }
  return false;
}

export function removeArrById(arr, id) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === id) {
      arr.splice(i, 1);
      break;
    }
  }
  return arr;
}

export function deepQuery(tree, id) {
  var isGet = false;
  var retNode = null;
  function deepSearch(tree, id) {
    for (var i = 0; i < tree.length; i++) {
      if (tree[i].children && tree[i].children.length > 0) {
        deepSearch(tree[i].children, id);
      }
      if (id === tree[i].id || isGet) {
        isGet || (retNode = tree[i]);
        isGet = true;
        break;
      }
    }
  }
  deepSearch(tree, id);
  return retNode;
}



export function getWeekDates(year, weekNumber) {
  // 一周的第一天是周一
  const startDay = 1;
  // 构建周一的日期
  const monday = new Date(year, 0, startDay + (weekNumber - 1) * 7);
  // 构建周日的日期
  const sunday = new Date(monday);
  sunday.setDate(sunday.getDate() + 6);
  // 将日期转换为字符串格式
  const mondayDate = monday.toISOString().split('T')[0];
  const sundayDate = sunday.toISOString().split('T')[0];
  var reg = /(\d{4})\-(\d{2})\-(\d{2})/;
  var startDate = mondayDate.replace(reg, "$1年$2月$3日");
  var endDate = sundayDate.replace(reg, "$1年$2月$3日");
  return {
    start: startDate.substring(5),
    end: endDate.substring(5),
  };
}

export function addDateText(timeStr) {
  var timeStr_ = timeStr.replace(/年|周/g, ",");
  var timeStrs = timeStr_.split(",");
  return {
    year: timeStrs[0],
    week: timeStrs[1]
  }
}

export function fileLinkToStreamDownload(url, fileName, type) {
  let xhr = new XMLHttpRequest();
  xhr.open('get', url, true);
  xhr.setRequestHeader('Content-Type', `application/${type}`);
  xhr.responseType = "blob";
  xhr.onload = function () {
    if (this.status == 200) {
      var blob = this.response;
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(blobUrl);
    }
  }
  xhr.send();
}

// 获取url中需要的数据  type  1: 获取文件名  2：获取后缀  3：获取文件名+后缀  4:获取文件前缀
export function urlFile(url, type) {
  let filename = url.substring(url.lastIndexOf('/') + 1)
  switch (type) {
    case 1: return filename; break;
    case 2: return filename.substring(filename.lastIndexOf(".") + 1); break;
    case 3: return filename.substring(0, filename.lastIndexOf(".")); break;
    case 4: return url.substring(0, url.lastIndexOf('/') + 1)
  }
}

//  将json参数转为键值对
export function getParStr(data) {
  var keys = Object.keys(data);
  let parArray = [];
  keys.forEach(element => {
    if (data[element] && element != 'applicationId') {
      parArray.push(`${element}=${data[element]}`)
    }
  });
  var str = parArray.join('&')
  return str
}
function getStyle(obj, attr) {
  if (obj.currentStyle) {
    return obj.currentStyle;
  } else {
    return getComputedStyle(obj);
  }
}

export function overflowhidden(id, rows, str) {
  var text = document.getElementById(id);
  var style = getStyle(text);
  var lineHeight = style["line-height"];   //获取到line-height样式设置的值 必须要有
  var at = rows * parseInt(lineHeight);      //计算包含文本的div应该有的高度
  var tempstr = str;                       //获取到所有文本
  text.innerHTML = tempstr;                //将所有文本写入html中
  var len = tempstr.length;
  var i = 0;
  if (text.offsetHeight <= at) {             //如果所有文本在写入html后文本没有溢出，那不需要做溢出处理
  }
  else {                                   //否则 一个一个字符添加写入 不断判断写入后是否溢出
    var temp = "";
    text.innerHTML = temp;
    while (text.offsetHeight <= at) {
      temp = tempstr.substring(0, i + 1);
      i++;
      text.innerHTML = temp;
    }
    var slen = temp.length;
    tempstr = temp.substring(0, slen - 1);
    len = tempstr.length
    text.innerHTML = tempstr.substring(0, len - 3) + "...";     //替换string后面三个字符
    text.height = at + "px";                                  //修改文本高度 为了让CSS样式overflow：hidden生效
  }

}

