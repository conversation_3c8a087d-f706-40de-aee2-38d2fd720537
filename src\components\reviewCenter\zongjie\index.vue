<template>
  <div class="maskbg">
    <el-tabs
    v-model="activeName"
    class="mytabs"
    tab-position="left"
    @tab-click="tabClick"
    style="height: 100%;">
      <el-tab-pane label="年总结" name="4">
        <reviewList goal-type="4" :review-type="'zongjie'" ></reviewList>
      </el-tab-pane>
      <el-tab-pane label="月总结" name="5">
        <reviewList goal-type="5" :review-type="'zongjie'" ></reviewList>
      </el-tab-pane>
      <el-tab-pane label="周总结" name="6">
        <reviewList goal-type="6" :review-type="'zongjie'" ></reviewList>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Bus from '@/utils/EventBus';
import reviewList from '../common/reviewList.vue';
export default {
  components:{
    reviewList
  },
  data(){
    return{
      activeName:'4',
    }
  },
  mounted(){
    this.activeName = this.$route.query.goalType || '4'
    this.$nextTick(()=>{
      Bus.$emit(`loadReview_${this.activeName}`)
    })
  },
  methods:{
    tabClick(tab){
      Bus.$emit(`loadReview_${tab.name}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.maskbg{
  height: calc(100%);
  padding: 20px 0;
  background-color: #FFFFFF;
}
.mytabs{
  &/deep/.el-tabs__header{
    width: 150px !important;
    text-align: center;
  }
  /deep/.el-tabs__item{
    text-align: center !important;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
