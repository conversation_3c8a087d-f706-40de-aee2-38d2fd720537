<template>
    <div class="page-controller" :class="{'alignRight':isRight,'alignLeft':isLeft}">
        <el-pagination
            layout="total,prev,pager, next,jumper"
            :total="total"
            @current-change="handleCurrentChange"
            :current-page="pageNum"
            :page-size="pageSize"
            >
        </el-pagination>
    </div>
</template>

<script>
export default {
    props:{
        align:{
            type:String,
            default:'center'
        },
        total:{
            type:Number,
            default:0,
        },
        currentPage:{
            type:Number,
            default:0,
        },
        pageSize:{
            type:Number,
            default:15,
        }
    },
    data(){
        return{
            isRight:false,
            isLeft:false,
        }
    },
    created(){
        switch (this.align) {
            case 'right':
                this.isRight = true;
                this.isLeft = false;
                break;
            case 'left':
                this.isRight = false;
                this.isLeft = true;
                break;
            default:
                this.isRight = false;
                this.isLeft= false;
                break;
        }
    },
    computed:{
        pageNum:{
            get(){
                return this.currentPage
            },
            set(val){
                this.$emit('updatePageNum',val)
            }
        }
    },
    methods:{
        handleCurrentChange(page){
            this.$emit('updatePageNum',page)
        }
    }
}
</script>

<style lang="scss" scoped>
.alignLeft{
    text-align: left;
}
.alignRight{
    text-align: right;
}
</style>