<!-- 添加文章 -->
<template>
  <div class="article app-container">
    <el-row>
      <el-col :span="6">
        <el-input
          placeholder="请输入关键字查询"
          v-model="filterText"
          prefix-icon="fa fa-search"
          style="width: 65%"
        ></el-input>
        <el-button
          type="primary"
          v-isShow="'sf:business:articlecategory:save'"
          @click="addOrUpdateTagTypeHandle()"
          >添加</el-button
        >
        <el-tree
          class="pt20"
          :data="tagTypeData"
          :props="defaultProps"
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          accordion
          :highlight-current="true"
          ref="tagTypeTree"
          :render-content="renderContent"
          @node-click="getTreeNode"
        >
        </el-tree>
      </el-col>
      <el-col :span="18" v-loading="articleloading">
        <div class="article-table" ref="configtypetable" v-show="tableList">
          <el-button
            type="primary"
            v-isShow="'sf:business:article:save'"
            style="float: right"
            class="mb20"
            @click="handleDialogArticle()"
            >添加文章</el-button
          >
          <el-table :data="dataList" border stripe>
            <el-table-column
              prop="id"
              header-align="center"
              align="center"
              label="标志ID"
            >
            </el-table-column>
            <el-table-column
              prop="title"
              header-align="center"
              align="center"
              label="文章标题"
            >
            </el-table-column>
            <el-table-column
              prop="author"
              header-align="center"
              align="center"
              label="作者"
            >
            </el-table-column>
            <el-table-column
              prop="summany"
              header-align="center"
              align="center"
              label="简介"
            >
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              label="创建时间"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.createTime">{{
                  ruleTime(scope.row.createTime)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              label="更新时间"
            >
              <template slot-scope="scope" v-if="scope.row.modifyTime">
                <span v-if="scope.row.modifyTime">{{
                  ruleTime(scope.row.modifyTime)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="sortNo"
              header-align="center"
              align="center"
              label="排序号"
            >
            </el-table-column>
            <el-table-column
              prop="isPublication"
              header-align="center"
              align="center"
              label="是否发布"
            >
              <template slot-scope="scope"
                ><span>{{
                  scope.row.isPublication === 1 ? '是' : '否'
                }}</span></template
              >
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="200px">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  v-isShow="'sf:business:article:update'"
                  @click="handleListEdit(scope.row)"
                  icon="el-icon-edit"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  v-isShow="'sf:business:article:delete'"
                  @click="handleListDelete(scope.row)"
                  icon="el-icon-delete"
                  type="text"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div
          class="article-table"
          ref="articleEdit"
          v-show="tableEdit"
          element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading"
        >
          <el-form
            :model="ruleForm"
            :rules="Rule.ARTICLE_ADD"
            ref="ruleForm"
            label-width="100px"
          >
            <el-form-item label="标志ID：" v-if="signID">
              <el-input v-model="signID" :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="文章标题：" prop="title">
              <el-input v-model="ruleForm.title"></el-input>
            </el-form-item>
            <el-form-item label="作者：" prop="author">
              <el-input v-model="ruleForm.author"></el-input>
            </el-form-item>
            <el-form-item label="简介：" prop="summany">
              <el-input v-model="ruleForm.summany"></el-input>
            </el-form-item>
            <el-form-item label="发布：" prop="isPublication">
              <el-radio-group v-model="ruleForm.isPublication">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="文章内容："
              prop="content"
              class="item-conent-edit"
            >
              <quill-editor
                ref="myTextEditor"
                class="conent-edit"
                v-model="ruleForm.content"
                @change="onChange"
                :options="editorOption"
              >
              </quill-editor>
            </el-form-item>
            <el-form-item>
              <el-button
                type="danger"
                v-isShow="'sf:business:article:save'"
                @click="handleSaveEdit('ruleForm')"
                >保 存</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
    <!-- 弹框, 新增/修改 -->
    <addOrDialog
      v-if="dialogVisible"
      ref="dialogTagType"
      @refreshTagTypeTree="getTagTypeData"
    ></addOrDialog>
    <!-- 弹框，富文本 -->
    <addOrQuilleditor
      v-if="dialogVisibleQuell"
      ref="addOrQuilleditor"
      @refreshTagEditorList="getTreeNode(saveClickData)"
    ></addOrQuilleditor>
  </div>
</template>

<script>
import { ruleTime } from '@/utils/tools'
import addOrDialog from './add-or-dialog.vue'
import addOrQuilleditor from './add-or-quilleditor.vue'
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
export default {
  components: {
    addOrDialog,
    quillEditor,
    addOrQuilleditor,
  },
  data() {
    return {
      articleloading: false,
      signID: '',
      filterText: '',
      tagTypeData: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      ruleForm: {
        title: '',
        author: '',
        summany: '',
        isPublication: 1,
        content: '',
        categoryType: '',
      },
      dialogVisible: false,
      dialogVisibleQuell: false,
      dataList: [],
      dataListLoading: false,
      tableList: false,
      tableEdit: false,
      editorOption: {
        content: '',
        placeholder: '请在这里输入',
        uploadUrl: '/sf/business/company/upload/file',
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline'],
            ['blockquote', 'code-block'],
            [{ header: 1 }, { header: 2 }],
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ script: 'sub' }, { script: 'super' }],
            [{ indent: '-1' }, { indent: '+1' }],
            [{ direction: 'rtl' }],
            [{ size: ['small', false, 'large', 'huge'] }],
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            [{ color: [] }, { background: [] }],
            [{ font: [] }],
            [{ align: [] }],
            ['image'],
          ],
        },
      },
      globId: '',
      saveClickData: {},
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tagTypeTree.filter(val)
    },
  },
  computed: {
    editor() {
      return this.$refs.myTextEditor.quill
    },
  },
  methods: {
    onChange() {
      this.$emit('input', this.ruleForm.content)
    },
    // 点击上传图片按钮
    imgClick() {
      if (!this.editorOption.uploadUrl) {
        console.log('no editor editorOption uploadUrl')
        return
      }
      /* 内存创建input file */
      let input = document.createElement('input')
      input.type = 'file'
      input.name = ''
      input.accept = 'image/jpeg,image/png,image/jpg,image/gif'
      input.onchange = this.onFileChange
      input.click()
    },
    onFileChange(e) {
      let self = this
      let fileInput = e.target
      if (fileInput.files.length === 0) {
        return
      }
      this.editor.focus()
      if (fileInput.files[0].size > 1024 * 1024) {
        self.$alert('图片不能大于1M', '图片尺寸过大', {
          confirmButtonText: '确定',
          type: 'warning',
        })
      }
      let data = new FormData()
      data.append('file', fileInput.files[0])
      this.$message('图片上传中...')
      this.$axios
        .post(this.editorOption.uploadUrl, data)
        .then((res) => {
          if (res.status === 0) {
            this.editor.insertEmbed(
              this.editor.getSelection().index,
              'image',
              res.data
            )
          } else {
            this.$message({
              message: '上传失败',
              type: 'warning',
            })
          }
        })
        .catch((err) => {
          this.$message({
            message: '上传失败',
            type: 'warning',
          })
        })
    },
    // 列表 编辑
    async handleListEdit(obj) {
      let res = await this.$axios.get(`sf/business/article/info/${obj.id}`)
      if (res.status === 0) {
        this.dialogVisibleQuell = true
        this.$nextTick(() => {
          this.$refs.addOrQuilleditor.init(
            this.ruleForm.categoryType,
            obj.categoryId,
            res.data
          )
        })
      }
    },
    // 列表 删除
    async handleListDelete(obj) {
      this.$confirm(`此操作将删除"${obj.title}", 是否继续？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let data = {}
          data.ids = obj.id
          data.categoryId = obj.categoryId
          let res = await this.$axios.post('/sf/business/article/delete', data)
          if (res.status === 0) {
            this.$message.success('删除成功')
            let data = {}
            data.type = 2
            data.categoryId = obj.categoryId
            this.getListData(data)
          }
        })
        .catch((e) => {
          console.log(e)
          return false
        })
    },
    // 时间戳转化为时分秒
    ruleTime(val) {
      return ruleTime(val)
    },
    // 添加文章 保存
    handleSaveEdit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.ruleForm.categoryId = this.globId
          let url = this.ruleForm.id
            ? '/sf/business/article/update'
            : '/sf/business/article/save'
          let res = await this.$axios.post(url, this.ruleForm)
          if (res.status === 0) {
            this.$message.success('操作成功')
          }
        } else {
          return false
        }
      })
    },
    append(data) {
      this.addOrUpdateTagTypeHandle(data.id)
    },
    edit(data) {
      this.addOrUpdateTagTypeHandle(data.id, data)
    },
    remove(node, data) {
      this.$confirm(`此操作将删除"${data.name}"该条数据, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let paramsData = {}
          paramsData.ids = data.id
          let res = await this.$axios.post(
            '/sf/business/articlecategory/delete',
            paramsData
          )
          if (res.status === 0) {
            this.$message.success('删除成功')
            setTimeout(() => {
              this.getTagTypeData()
              this.tableEdit = false
            }, 300)
          }
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    // tree 按钮
    renderContent(h, { node, data, store }) {
      return (
        <span
          class="renderFlex"
          style="flex: 1; display: flex; align-items: center; justify-content: space-between; font-size: 14px; padding-right: 8px;"
        >
          <span>{node.label}</span>
          <span>
            {data.type === 2 ? (
              <el-tooltip
                class="item"
                effect="dark"
                content="新增"
                placement="top-start"
              >
                <el-button
                  v-isShow={'sf:business:articlecategory:save'}
                  type="text"
                  size="medium"
                  icon="el-icon-plus"
                  on-click={() => this.append(data)}
                ></el-button>
              </el-tooltip>
            ) : (
              ''
            )}
            <el-tooltip
              class="item"
              effect="dark"
              content="编辑"
              placement="top-start"
            >
              <el-button
                v-isShow={'sf:business:articlecategory:update'}
                type="text"
                size="medium"
                icon="el-icon-edit-outline"
                on-click={() => this.edit(data)}
              ></el-button>
            </el-tooltip>
            <el-tooltip
              class="item"
              effect="dark"
              content="删除"
              placement="top-start"
            >
              <el-button
                v-isShow={'sf:business:articlecategory:delete'}
                type="text"
                size="medium"
                icon="el-icon-delete"
                on-click={() => this.remove(node, data)}
              ></el-button>
            </el-tooltip>
          </span>
        </span>
      )
    },
    // 点击tree 执行事件回调
    async getTreeNode(data) {
      this.articleloading = true
      this.saveClickData = data
      let ctx = {}
      this.globId = data.id
      this.ruleForm.categoryType = data.type
      ctx.categoryId = data.id
      if (data.type === 1) {
        // 文章
        this.tableList = false
        this.tableEdit = true
        ctx.type = 1
        let res = await this.$axios.get(
          '/sf/business/articlecategory/listAll',
          {
            params: ctx,
          }
        )
        if (res.status === 0) {
          let _data = res.data.articleVo
          if (!_data || _data === 'null' || _data === 'undefined') {
            this.signID = ''
            this.ruleForm.title = ''
            this.ruleForm.author =
              this.$store.state.user.username ||
              localStorage.getItem('username')
            this.ruleForm.summany = ''
            this.ruleForm.content = ''
            this.ruleForm.isPublication = 1
            this.ruleForm.id = ''
            this.articleloading = false
            return false
          }
          this.signID = _data.categoryId
          this.ruleForm.title = _data.title
          this.ruleForm.author = _data.author
          this.ruleForm.summany = _data.summany
          this.ruleForm.content = _data.content
          this.ruleForm.isPublication = _data.isPublication
          this.globId = _data.categoryId
          this.ruleForm.id = _data.id
          this.articleloading = false
        } else {
          this.$message.error(res.msg)
        }
      } else {
        // 列表
        this.tableEdit = false
        this.tableList = true
        ctx.type = 2
        this.getListData(ctx)
        this.articleloading = false
      }
    },
    // 列表数据
    async getListData(ctx) {
      let res = await this.$axios.get('/sf/business/articlecategory/listAll', {
        params: ctx,
      })
      if (res.status === 0) {
        let _data = res.data.articleVoList
        if (!_data || _data === 'null' || _data === 'undefined') return false
        this.dataList = _data
      }
    },
    // 添加文章
    handleDialogArticle() {
      this.dialogVisibleQuell = true
      this.$nextTick(() => {
        this.$refs.addOrQuilleditor.init(
          this.ruleForm.categoryType,
          this.globId
        )
      })
    },
    // 添加按钮
    addOrUpdateTagTypeHandle(id, backdata) {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dialogTagType.init(id, backdata)
      })
    },
    // 过滤函数
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 获取tree 树
    async getTagTypeData() {
      let res = await this.$axios.get(
        '/sf/business/articlecategory/articleCategoryTree'
      )
      if (res.status === 0) {
        this.tagTypeData = res.data
      }
    },
  },
  mounted() {
    this.$refs.myTextEditor.quill
      .getModule('toolbar')
      .addHandler('image', this.imgClick)
    this.$refs.tagTypeTree.$el.style.height =
      Number(window.innerHeight) - 300 + 'px'
    this.$refs.configtypetable.style.height =
      Number(window.innerHeight) - 200 + 'px'
  },
  created() {
    this.getTagTypeData()
  },
}
</script>

<style scoped lang="scss">
.item-conent-edit {
  height: 400px;
}
.conent-edit {
  height: 300px;
}
.renderFlex {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
