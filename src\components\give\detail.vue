<template>

  <div>
    <back>{{types[form.type]}}详情</back>
    <div class="mainbg" v-loading="isLoading">
        <el-form class="infoform" ref="form" :model="form" label-width="120px">
          <textBorder>基本信息</textBorder>
          <div class="mb30 bbline">
            <el-row :gutter="20" class="width100 mt10 pb5 " >
                <el-col :span="8">
                    <el-form-item label="事由:" class="labeltext">
                        <span>{{form.reason}}</span>
                    </el-form-item>
                    <el-form-item label="客户:" class="labeltext">
                        <span>{{form.customerName}}</span>
                    </el-form-item>
                     <el-form-item label="业务经理:" class="labeltext">
                        <span >{{form.operationName}}</span> 
                    </el-form-item>
                    <el-form-item label="业务经理部门:" class="labeltext">
                        <span >{{form.distributionDepartmentName}}</span> 
                    </el-form-item>
                </el-col>
                <el-col :span="8">  
                    <el-form-item label="时间:" class="labeltext">
                        <span>{{form.distributionTime}}</span>
                    </el-form-item>
                    <el-form-item label="客户单位:" class="labeltext">
                    <span>{{form.unitName}}</span>
                    </el-form-item>
                    <el-form-item v-if="form.type == 3" label="金额:" class="labeltext">
                    <span>{{form.price}}元</span>
                    </el-form-item>
                    <div v-else>
                        <el-form-item  label="回访提醒:" class="labeltext">
                            <span>{{form.revisitTime || '暂无'}}</span>
                        </el-form-item>
                        <el-form-item  label="是否回访:" class="labeltext">
                            <span>{{form.isRevisit == 1 ? '是' : "否"}}</span>
                        </el-form-item>
                    </div>
                    
                </el-col>
            </el-row>
          </div>

          <textBorder v-if="form.type != 3 ">{{types[form.type]}}信息</textBorder>
          <div v-show="form.type == 1 " class="mb30 bbline">
            <el-table class="mytable mtop20" :data="form.distributionDetailList" v-if="form.distributionDetailList.length>0">
                <el-table-column label="教材名称" prop="name" min-width="167px" align="center"></el-table-column>
                <el-table-column label="用书专业" prop="specialtyName" min-width="167px" align="center"></el-table-column>
                <el-table-column label="ISBN" prop="isbn" min-width="167px" align="center"></el-table-column>
                <el-table-column label="主编" prop="author" min-width="167px" align="center"></el-table-column>
                <el-table-column label="出版社" prop="platformName" min-width="167px" align="center"></el-table-column>
                <el-table-column label="出版时间" prop="publicationRevisionTime" min-width="167px" align="center"></el-table-column>
                <el-table-column label="价格" prop="price" fixed="right" min-width="167px" align="center"></el-table-column>
                <el-table-column label="数量" prop="number" fixed="right" min-width="167px" align="center"></el-table-column>
                
                <el-table-column label="总码洋" prop="all" fixed="right" min-width="167px" align="center">
                    <template slot-scope="scope">
                        <span>{{scope.row.number && (scope.row.price * scope.row.number).toFixed(2)}}</span>
                    </template>
                </el-table-column>
            </el-table>
            <el-row v-else  :gutter="20" class="width100 mt10 pb5 " >
                <el-col :span="8">
                    <el-form-item label="教材名称:" class="labeltext">
                        <span>{{form.goodsName}}</span>
                    </el-form-item>
                    <el-form-item label="isbns:" class="labeltext">
                    <span>{{form.isbn}}</span>
                    </el-form-item>
                    <el-form-item label="用书专业:" class="labeltext">
                        <span >{{form.specialtyName}}</span> 
                    </el-form-item>
                    <el-form-item label="数量:" class="labeltext">
                        <span >{{form.useBookNumber}}</span> 
                    </el-form-item>
                </el-col>
                <el-col :span="8">  
                    <el-form-item label="出版社:" class="labeltext">
                        <span>{{form.platformName}}</span>
                    </el-form-item>
                    <el-form-item label="价格:" class="labeltext">
                        <span >{{form.price}}</span> 
                    </el-form-item>
                    <el-form-item label="用书时间:" class="labeltext">
                        <span >{{form.useBookYear}}</span> 
                    </el-form-item>
                </el-col>
            </el-row>
          </div>
          <div v-show="form.type == 2" class="mb30 bbline">
            <el-table class="mytable mtop20" :data="form.distributionDetailList" v-if="form.distributionDetailList.length>0">
                <el-table-column label="礼品名称" prop="name" min-width="167px" align="center"></el-table-column>
                <el-table-column label="价格" prop="price" fixed="right" min-width="167px" align="center"></el-table-column>
                <el-table-column label="数量" prop="number" fixed="right" min-width="167px" align="center"></el-table-column>
                <el-table-column label="计量单位" prop="giftUnit" min-width="167px" align="center"></el-table-column>
                <el-table-column label="总金额" prop="all" fixed="right" min-width="167px" align="center">
                    <template slot-scope="scope">
                        <span>{{scope.row.number && (scope.row.price * scope.row.number).toFixed(2)}}</span>
                    </template>
                </el-table-column>
            </el-table>
            <el-row v-else :gutter="20" class="width100 mt10 pb5 " >
                <el-col :span="8">
                    <el-form-item label="礼品:" class="labeltext">
                        <span>{{form.reason}}</span>
                    </el-form-item>
                    <el-form-item label="单位:" class="labeltext">
                    <span>{{form.unitName}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">  
                    <el-form-item label="数量:" class="labeltext">
                        <span>{{form.useBookNumber}}</span>
                    </el-form-item>
                    <el-form-item label="价格:" class="labeltext">
                        <span >{{form.price}}</span> 
                    </el-form-item>
                </el-col>
            </el-row>
          </div>
          <textBorder>补充信息</textBorder>
          <div class="mb30 bbline">
            <el-row :gutter="20" class="width100 mt10  pb15" >
                <el-col :span="24">
                    <el-form-item label="备注:" class="labeltext">
                        <span>{{form.notes}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
          </div>

          <textBorder>系统信息</textBorder>
          <el-row :gutter="20" class="width100 mt10 pb15">
              <el-col :span="8">
                  <el-form-item label="创建人:" class="labeltext">
                      <span>{{form.createByName}}</span>
                  </el-form-item>
                  
                  <el-form-item label="创建时间:" class="labeltext">
                      <span>{{form.createTime}}</span>
                  </el-form-item>
              </el-col>
              <el-col :span="8">
                  <el-form-item label="最后修改人:" class="labeltext">
                      <span>{{form.modifyByName}}</span>
                  </el-form-item>
                  <el-form-item label="最后修改时间:" class="labeltext">
                      <span>{{form.modifyTime}}</span>
                  </el-form-item>
              </el-col>
              <el-col :span="8">
                  
              </el-col>
          </el-row>
      </el-form>
    </div>

  </div>
</template>

<script>
 import textBorder from '@/components/common/textBorder.vue'
 import back from '@/components/common/back.vue';
 import { distributionInfo } from '@/api/clientMaintenance/give';
  export default {
    components:{
      back,
      textBorder,
    },
    data(){
     return {
        colors:[],
        isLoading:false,
        form: {
            id:"",
            reason:"",
            type:"",
            customerName:"",
            unitName:"",
            orderNumber:"",
            distributionTime:"",
            isRevisit:false,
            notes:"",
            createByName:"",
            modifyByName:"",
            createTime:"",
            modifyTime:"",
            platformName:"",
            specialtyName:""
        },
        types:{
                1:'发放样书',
                2:"发放礼品",
                3:"商务"
            },
     }
    },
    created(){
        this.loadData();
    },
    methods:{
        loadData(){
            this.isLoading = true;
            distributionInfo(this.$route.query.id).then((result) => {
                this.form = result.data;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        }
    }

  }
</script>

<style lang="scss" scoped>
// .mytable{
//     width: 66%;
// }
.mtop20{
    margin-top: 20px !important;
}
.pb15{
    padding-bottom: 15px;
}
.filebg{
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  padding: 3px 10px;
  color: #4285F4;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #4285F4;
  cursor: pointer;

}
.mainbg{
    min-height: calc(100vh - 150px);
    margin: 16px 0px;
    padding: 20px 16px;
    padding-bottom: 0px;
    background-color: white;
    border-radius: 8px;
}
.colorcss{
    color:#F45961;
}
.tagitemcss{
    font-size: 12px;
    padding:4px 8px;
    margin: 0px 4px;
    background-color: #DFEAFD;
    border-radius: 2px;
    color: #4285F4;
}
.mtop20{
  margin-top: 20px;
}
.ml40{
    margin-left: 40px;
}
</style>
<style scoped>
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}
.mulitline /deep/.el-form-item__content{
    padding-top: 10px;
    line-height: 20px;
}

</style>