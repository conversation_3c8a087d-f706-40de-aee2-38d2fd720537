<template>
  <div class="drawer">
    <el-drawer  class="ed" center size="50%" title="阶段流程详情" :visible.sync="drawer_" :direction="direction" @close="handleClose">
      <el-row :gutter="10" class="cardcss">
          <el-col :lg="{span: '4-8'}"  v-for="(item,index) in dataViewList" :key="index">
            <div class="piececss">
                <img class="imgf left" :src="item.imgUrl" alt="">
                <div>
                    <p class="numcolor" :class="[`color-${index}`]">{{ item.numText }}</p>
                    <p class="numtext">{{ item.text }}</p>
                </div>
            </div>
          </el-col>
        </el-row>
      <el-tabs class="edt" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="阶段信息" name="first">
          <bacisInfo v-on="$listeners" :form="form"></bacisInfo>
        </el-tab-pane>
        <el-tab-pane label="成本记录" name="second">
          <costrecord ref="costRef" :stageId="stageId"  :taskId="stageId">
            <template slot-scope="{dataobj}">
              <p class="cbtext clearfix">本流程累计成本：<span class="yuan">{{dataobj.yuan}}元</span>  </p>
            </template>
          </costrecord>
        </el-tab-pane>
        <el-tab-pane label="工时与成果" name="third">
            <hourresult ref="houtRef" :taskId="stageId">
              <template slot-scope="scope">
                <p class="cbtext clearfix">
                  本流程累计工时：<span class="yuan">{{scope.row.accumulatedWorkHours}}人/天</span>
                  <span class="ml32"> 本流程累计成果(分钟)：</span>
                  <span class="yuan">{{scope.row.accumulatedDuration}}</span>
                </p>
              </template>
            </hourresult>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
import bacisInfo from './basicInfo.vue'
import costrecord from './costrecord'
import hourresult from './hourresult'
 import {stageSave,stageInfo,stageUpdate} from '@/api/stage/index'
export default {
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    direction: {
      type: String,
      default: 'rtl',
    },
  },
  components:{
    bacisInfo,
    costrecord,
    hourresult
  },
  created(){
  },
  data() {
    return {
      activeName: 'first',
      dataViewList:[
          {
            imgUrl:require('../../../assets/xiaoshou.png'),
            numText:'0',
            text:'任务总数'
          },
          {
            imgUrl:require('../../../assets/img2.png'),
            numText:'0',
            text:'未开始'
          },
          {
            imgUrl:require('../../../assets/img3.png'),
            numText:'0',
            text:'进行中'
          },
          {
            imgUrl:require('../../../assets/img4.png'),
            numText:'0',
            text:'已完成'
          },
          {
            imgUrl:require('../../../assets/img5.png'),
            numText:'0',
            text:'中止'
          },
        ],
        form:{},
        stageId:"",
    }
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
       
      },
    },
  },
  methods: {
    getStageInfo(stageId){
        this.stageId = stageId
        this.$refs.costRef.costListData(1,this.stageId)
        this.$refs.houtRef.hourListData(1,this.stageId)
        stageInfo(this.stageId).then(res=>{
            if(res.status == 0){
                this.form  = res.data
                this.dataViewList[0].numText = res.data.taskNum
                this.dataViewList[1].numText = res.data.notStartedNum
                this.dataViewList[2].numText = res.data.inProgressNum
                this.dataViewList[3].numText = res.data.completedNum
                this.dataViewList[4].numText = res.data.discontinueNum
            }
        })
    },
    handleClick(tab) {
      
    },
    handleClose() {
      this.$emit('changeDrawer', false)
    },
  },
}
</script>


<style lang="scss" scoped>

.color-0{
  color: #56C36E;
}
.color-1{
  color: #4A8BF6;
}
.color-2{
  color: #E85D5D;
}
.color-3{
  color: #EC9037;
}
.color-4{
  color: #F46D40;
}
.el-col-lg-4-8 {
		width: 20%;
	}
.numtext{
  font-size: 14px;
font-family: Microsoft YaHei-Regular, Microsoft YaHei;
font-weight: 400;
color: #999999;
}
.numcolor{
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
}
.imgf{
  margin-right: 5px;
  width: 40px;
  margin-top: 6px;
}
.piececss{
  position: relative;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  width: 100%;
  padding:26px  16px;
  box-sizing: border-box;
  box-shadow: 0px 2px 16px 0px rgba(15,27,50,0.08);

}
.cardcss{
  margin-bottom: 10px;
}
  .drawer{
    /deep/.el-drawer{
      padding: 20px;
      padding-top: 0;
    }
    /deep/ .el-drawer__header{
      padding-left: 0;
      text-align: center;
    }
  }
</style>>
