<!--会员管理-->
<template>
  <div>
    <template v-if="management">
      <div>
        <el-form :inline="true">
          <el-form-item>
            <el-input placeholder="账号/姓名" clearable v-model="search.nickname" class="input-230"></el-input>
          </el-form-item>
          <el-form-item>
            <SelectCascader v-model="area"></SelectCascader>
          </el-form-item>
          <el-form-item>
            <el-select v-model="search.currentMembership" clearable placeholder="类型" class="input-230">
              <el-option
                v-for="item in Dict.CHECKSTAND_TYPE"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input v-model="search.recommendName" clearable placeholder="邀请人" class="input-230"></el-input>
          </el-form-item>
          <el-form-item>
            <SelectDatePicker v-model="time"></SelectDatePicker>
          </el-form-item>
          <el-form-item>
            <el-button type="danger" @click="handleSearch" v-isShow="'business:member:list'"><i class="fa fa-search"></i>查询</el-button>
          </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table
          :data="tableData"
          border
          :empty-text="$emptyFont"
          style="width: 100%">
          <el-table-column
            prop="businessId"
            label="账号"
            width="140">
          </el-table-column>
          <el-table-column
            prop="nickname"
            label="姓名"
            width="160">
          </el-table-column>
          <el-table-column
            label="性别"
            width="80">
            <template slot-scope="scope">
              <span>{{scope.row.gender === 0 ? '女' : '男'}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="居住地"
            width="300">
            <template slot-scope="scope">
                <span>{{`${scope.row.provinceName} ${scope.row.cityName} ${scope.row.countyName}`}}</span>
              </template>
          </el-table-column>
          <el-table-column
            label="类型"
            width="160">
            <template slot-scope="scope">
              <span>{{scope.row.currentMembership | dict('CHECKSTAND')}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="recommendName"
            label="邀请人"
            width="160">
          </el-table-column>
          <el-table-column
            label="注册日期"
            width="200">
            <template slot-scope="scope">{{ruleTime(scope.row.createTime)}}</template>
          </el-table-column>
          <el-table-column
            label="婚姻状况"
            width="120">
            <template slot-scope="scope">
              <span>{{scope.row.marriageStatus | dict('MARATITAL_STATUS')}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="账号状态"
            width="120">
            <template slot-scope="scope">
              <span>{{handleStatus(scope.row.status)}}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="160">
            <template slot-scope="scope">
              <el-button v-isShow="'business:member:info'"  @click="viewDetail(scope.row)" type="text" size="small">查看</el-button>
              <el-button v-isShow="'business:member:updateUserStatBySys'" type="text" size="small" @click="handleFreeze(scope.row)">{{scope.row.status === 3 ? '解冻' : '冻结'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div>
          <el-pagination
            v-if="pagingObj.totalSum"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagingObj.currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagingObj.totalSum">
          </el-pagination>
        </div>
      </div>
    </template>
    <!-- 会员详情 -->
    <!-- <membership-details v-if="details" :isdetails="detailsMessage"></membership-details> -->
    <!-- 账号冻结弹出框 -->
    <!-- <freeze-dialog></freeze-dialog> -->
    <el-dialog
    title="账号冻结"
    :visible.sync="dialogVisible"
    width="30%"
    :close-on-click-modal="false"
    >
    <el-form :model="ruleForm" :rules="Rule.SUSPEND_ACCOUNT" ref="ruleForm">
      <el-form-item label="" prop="freezeType">
        <el-radio-group v-model="ruleForm.freezeType">
          <el-radio :label="0">永久冻结</el-radio><br />
          <el-radio :label="1" style="margin-top: 25px;">临时冻结</el-radio>
          <el-date-picker
          v-if="ruleForm.freezeType===1"
          v-model="unfreezeTime"
          type="datetime" placeholder="选择日期时间"
          value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="冻结原因：" prop="typeId" >
        <SelectFreezeOrigin v-model="ruleForm.typeId" style="display: inline-block; width:70%;"></SelectFreezeOrigin>
      </el-form-item>
      <el-form-item label="冻结描述：" prop="typeName">
        <el-input v-model="ruleForm.typeName" type="textarea" style="display: inline-block; width:70%;" placeholder="请输入冻结描述"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSureSubmit('ruleForm')">确 定</el-button>
    </span>
    </el-dialog>
    <!-- 查看弹出框 -->
    <viewDialog ref="addOrUpdate"></viewDialog>
  </div>
</template>

<script>
import SelectCascader from '@/components/select/SelectCascader.vue'
import SelectDatePicker from '@/components/select/SelectDatePicker.vue'
import SelectFreezeOrigin from '@/components/select/SelectFreezeOrigin.vue'
import viewDialog from './viewDialog'
import pagination from '@/mixin/pagination'
export default {
  mixins: [pagination],
  data() {
    return {
      time: [],
      area: [],
      unfreezeTime: '',
      dialogVisible: false,
      ruleForm: {},
      provinceData: [],
      cityData: [],
      countyData: [],
      search: {},
      tableData: [],
      detailsMessage: {},
      totalPages: {},
      dialogParameter: {},
      // 会员管理显示
      management: true,
      // 会员详情显示
      details: false
    }
  },
  components: {
    SelectCascader,
    SelectDatePicker,
    SelectFreezeOrigin,
    viewDialog
  },
  created () {
    // 默认先查询数据
    this.handleSearch();
  },
  methods: {
    handleFreezeTime () {
      if (this.ruleForm.freezeType === 1) {
        if (!this.unfreezeTime) { this.$message.error('临时冻结时间必填'); return false }
        this.ruleForm.unfreezeTime = this.unfreezeTime
      }
    },
    // 查看
    viewDetail (obj) {
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(obj)
      })
    },
    // 冻结弹框确定
    handleSureSubmit (formName) {
      this.handleFreezeTime()
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let res = await this.$axios.post('/sf/member/ocs/updateUserStatBySys', this.ruleForm);
          if (res.status === 0) {
            this.$message.success('操作成功');
            setTimeout(() => {
              this.dialogVisible = false
              this.getDataList(this.search)
            }, 300);
          }
        } else {
          return false;
        }
      })
    },
    // 查询
    handleSearch () {
      this.search.pageNum = '1';
      this.search.pageSize = '10';
      // 所有的用户
      this.search.currentMembership = 11;
      this.pagingObj.currentPage = 1;
      this.handleTimeParams(this.time, 'startTime', 'endTime');
      this.handleGetArea(this.area, 'provinceName', 'cityName', 'countyName');
      this.getDataList(this.search);
    },
    // 账号状态
    handleStatus (type) {
      let str = '';
      switch (type) {
        case 1:
          str = "正常";
          break;
        case 2:
          str = "用户自己紧急冻结";
          break;
        case 3:
          str = "违规后系统给冻结";
          break;
        case 4:
          str = "用户自己注销";
          break;
        case 5:
          str = "禁用";
          break;
        case 6:
          str = "已删除";
          break;
      }
      return str
    },
    // 是否展示
    // isShowTrip (status) {
    //   if (status === 3) {
    //     return false
    //   } else {
    //     return true
    //   }
    // },

    // 解冻/冻结
    handleFreeze (obj) {
      let num = '';
      let str = '';
      if (obj.status === 3) {
        str = '解冻';
        num = '1';
        this.$confirm(`此操作将${str}本条信息, 是否继续`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let data = {};
          data.userId = obj.id;
          data.stat = num;
          let res = await this.$axios.post('/sf/member/ocs/updateUserStatBySys', data)
          if (res.status === 0) {
            this.$message.success(`${str}-操作成功`)
            this.getDataList(this.search)
          } else {
            this.$message.error(`${res.msg}`)
          }
        }).catch(() => {
          this.ruleForm = {}
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      } else { // 冻结
        this.ruleForm.userId = obj.id;
        this.ruleForm.stat = 3;
        this.dialogVisible = true;
      }
    },
    // 列表请求参数
    async getDataList (obj) {
      let res = await this.$axios.get('sf/member/ocs/userList', { params: obj })
      if (res.status === 0) {
        this.tableData = res.data
        this.pagingObj.totalSum = res.page.total
      }
    }
  }
}
</script>
