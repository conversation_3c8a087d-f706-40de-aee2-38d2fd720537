<template>
    <div>
        <el-card class="niandu1">
            <div class="title">
                <textBorder class="biaoti">业绩变化</textBorder>
            </div>
            <el-row :gutter="12">
                <el-col :span="8" v-for="(item, index) in kuai1Items" :key="index">
                    <div class="kuai1">
                        <el-col :span="12">
                            <div class="content-wrapper">
                                <div class="toptitle">
                                    <textBorder class="wenzi">{{item.title}}</textBorder>
                                </div>
                                <div class="number numbers"><span class="number-styles">{{ item.amount }}</span>万元</div>
                                <div class="lastyears">
                                    <span class="lastyear">较上一年：</span><p class="number-style3">{{ item.lastYearAmount }}</p><span class="lastyear">万元</span>
                                </div>
                                <div class="zeng" v-if="item.amount || item.lastYearAmount" :class="typeClass[item.changeType]">
                                    <div class="zengzhang">{{ item.changeType }}：</div>
                                    <div class="baifenbi">{{ item.changePercent }}</div>
                                    <img :src="item.changeIcon" alt="" class="jiantou">
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12" class="juzhong">
                            <div class="image-wrapper">
                                <!-- 使用v-if判断是否渲染图表 -->
                                <div v-if="item.chart" :id="'yeji-' + item.chartIds" class="echarts-pie"></div>
                                <!-- 当不需要渲染图表时，显示图片 -->
                                <img v-else :src="item.image" alt="" class="tupian">
                            </div>
                        </el-col>
                    </div>
                </el-col>
                
            </el-row>
        </el-card>
    </div>
</template>    

<script>
import '../detail.css';
import textBorder from '@/components/common/textBorder.vue';
import * as echarts from 'echarts';
export default {
    components:{
        textBorder
    },
    data() {
        return {
            upicon: require('../../../assets/Group4189.png'),
            downicon:require('../../../assets/Group4190.png'),
            chipingicon:require('../../../assets/chiping.png'),
            kuai1Items:[],
            typeClass:{
                "增长":'upcss',
                "下降":'downcss',
                '持平':'chipingcss'
            },
        }
    },
    mounted() {
        
    },
    methods: {
        loadData(data){
            this.kuai1Items = this.getDataArray(data)
            this.$nextTick(() => {
            this.kuai1Items.forEach((item, index) => {
                if (item.chart && item.chartIds && item.chartOption) {
                    var chart = echarts.init(document.getElementById('yeji-' + item.chartIds));
                    chart.setOption(item.chartOption); 
                }
            });
        });
        },
        getImg(type){
            if (type === '增长') {
                return this.upicon
            }else if (type === '下降') {
                return this.downicon
            }else{
                return this.chipingicon
            }
        },
        getDataArray(data){
            var materialValues = this.getValue(data.materialProportion);
            var informationValues = this.getValue(data.informationProportion)
            console.log('materialValues=====',materialValues,data.materialProportion)
            return  [
                {
                    title: '年度业绩',
                    amount: data.thisYearPerformance,
                    lastYearAmount: data.lastYearPerformance,
                    changeType: data.performanceFluctuations,
                    changePercent: data.performanceRange,
                    changeIcon:this.getImg(data.performanceFluctuations),
                    chart: false,
                    image:require('../../../assets/Frame4244.png')
                },
                {
                    title: '教材类',
                    amount: data.thisYearMaterialPerformance,
                    lastYearAmount: data.lastYearMaterialPerformance,
                    changeType: data.materialFluctuations,
                    changePercent: data.materialRange,
                    changeIcon: this.getImg(data.materialFluctuations),
                    chart: true,
                    chartIds: 'pie-chart-1',
                    chartOption: { 
                        series: [
                            {
                                type: 'pie',
                                radius: ['68%', '90%'],
                                avoidLabelOverlap: false,
                                label: {
                                    show: true,
                                    position: 'center', 
                                    formatter: function(params) {
                                        if (params.name === '剩余') {

                                            return `{a|${(100.00 - params.value).toFixed(2)}}{percent|%}\n{b|${'业务占比'}}`;
                                        } else if (params.name === '业绩占比') {
                                            return `{a|${params.value}}{percent|%}\n{b|${params.name}}`;
                                        } else {
                                            return `{big|${params.value}}{percent|%}\n{big|${params.name}}`;
                                        }
                                    },
                                    rich: {
                                        a: {
                                            color: '#333', 
                                            fontSize: 32,
                                        },
                                        b: {
                                            color: '#999999', 
                                            fontSize: 12,
                                        },
                                        percent: {
                                            color: '#333',
                                            fontSize: 16, 
                                        }
                                    }
                                },

                                labelLine: {
                                    show: false
                                },
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowColor: 'rgba(197,237,244,0.8)',
                                    shadowOffsetX: 0,
                                    shadowOffsetY: 7,
                                },
                                data: [
                                    {
                                        value: materialValues[0],
                                        name: '业绩占比',
                                        itemStyle: {
                                            borderRadius: 60,
                                            borderWidth: 10,
                                            color: new echarts.graphic.LinearGradient(
                                                0, 0, 1, 0, 
                                                [
                                                    {offset: 0, color: '#45E1D8'}, 
                                                    {offset: 0.49, color: '#2FD6F3'}, 
                                                    {offset: 1, color: '#4186EE'} 
                                                ]
                                            )
                                        }
                                    },
                                    {
                                        value: materialValues[1],
                                        name: '剩余',
                                        itemStyle: {
                                            borderWidth: 10,
                                            color: 'rgba(223, 248, 253, 0.5)',
                                        }
                                    },
                                ]
                            }
                        ]
                    }
                },
                {
                    title: '信息化类',
                    amount: data.thisYearInformationPerformance,
                    lastYearAmount: data.lastYearInformationPerformance,
                    changeType: data.informationFluctuations,
                    changePercent: data.informationRange,
                    changeIcon: this.getImg(data.informationFluctuations),
                    chart: true,
                    chartIds: 'pie-chart-2',
                    chartOption: { 
                        series: [
                            {
                                type: 'pie',
                                radius: ['68%', '90%'],
                                avoidLabelOverlap: false,
                                label: {
                                    show: true,
                                    position: 'center', 
                                    formatter: function(params) {
                                        if (params.name === '剩余') {
                                            return `{a|${(100 - params.value).toFixed(2)}}{percent|%}\n{b|${'业务占比'}}`;
                                        } else if (params.name === '业绩占比') {
                                            return `{a|${params.value}}{percent|%}\n{b|${params.name}}`;
                                        } else {
                                            return `{big|${params.value}}{percent|%}\n{big|${params.name}}`;
                                        }
                                    },
                                    rich: {
                                        a: {
                                            color: '#333', 
                                            fontSize: 32,
                                        },
                                        b: {
                                            color: '#999999', 
                                            fontSize: 12,
                                        },
                                        percent: {
                                            color: '#333',
                                            fontSize: 16, 
                                        }
                                    }
                                },
                                labelLine: {
                                    show: false
                                },
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowColor: 'rgba(222,223,248,0.8)',
                                    shadowOffsetX: 0,
                                    shadowOffsetY: 7,
                                },
                                data: [
                                    {
                                        value: informationValues[0],
                                        name: '业绩占比',
                                        itemStyle: {
                                            borderRadius: 60,
                                            borderWidth: 10,
                                            color: new echarts.graphic.LinearGradient(
                                                0, 0, 1, 0, // 从左到右的渐变
                                                [
                                                    {offset: 0, color: '#4ED4FF'},
                                                    {offset: 0, color: '#5BD8FF'},
                                                    {offset: 0.47, color: '#5F6FFF'},
                                                    {offset: 1, color: '#A45CFF'}
                                                ]
                                            )
                                        }
                                    },
                                    {
                                        value: informationValues[1],
                                        name:'剩余',
                                        itemStyle: {
                                            borderWidth: 10,
                                            color: 'rgba(223, 248, 253, 0.5)',
                                        }
                                    },
                                ]
                            }
                        ]
                    }
                },
            ]
        },
        getValue(dataString){
            if (dataString.length>0) {
                var num = dataString.substring(0,dataString.length-1)
                var value = Number(num);
                var remValue = 100 - value
                return [value,remValue]
            }
            return [0,100]
        },
    }
}
</script>


