<template>
  <div class="concss">
    <el-radio-group
    class="myragroupcss"
    v-model="pageBean.handleType"
    style="margin-bottom: 30px;"
    @input="change">
      <el-radio-button :label="1">待处理</el-radio-button>
      <el-radio-button :label="2">已处理</el-radio-button>
    </el-radio-group>
    <el-form :inline="true" class="myform">
      <el-form-item label="">
        <el-input
          v-model="pageBean.createByName"
          class="definput"
          clearable
          placeholder="请输入提交人姓名"
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="`${types[goalType].label}：`">
        <el-date-picker
          class="definput"
          @change="changeDatePicker"
          v-model="searchValue"
          :type="types[goalType].type"
          :format="types[goalType].format"
          placeholder="请选择"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="部门：">
        <div class="unitbtn mt definput" @click="chooseDepartment">
          <span v-if="pageBean.departmentIds" class="deffont">{{
            searchdepartmentNames
          }}</span>
          <span v-else>请选择</span>
          <i
            v-if="searchdepartmentNames"
            @click.stop="cleardata"
            class="rcenter el-icon-circle-close"
          />
          <i v-else class="rcenter el-icon-arrow-down" />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          @click="searchAction"
          class="defaultbtn mt"
          icon="el-icon-search"
          type="primary"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        prop="year"
        :width="goalType == '3' || goalType == '6' ? 260 : 100"
        :label="types[goalType].label"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ getPlanTime(scope.row) }}</span>
          <span v-if="goalType == '3' || goalType == '6' "
            >({{
              scope.row.timeRange ? scope.row.timeRange.start + '-' + scope.row.timeRange.end :'--'
            }})</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="createByName" label="提交人" align="center">
        <template slot-scope="scope">
          <span
            >{{ scope.row.createByName }}
            <span class="noset">{{
              scope.row.logsStatus == 1 ? '(未处理)' : ''
            }}</span></span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="departmentName"
        label="部门"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column v-if="pageBean.handleType == 2" prop="year" label="审核状态" align="center">
        <template slot-scope="scope">
          <span class="circle" :class="{
              'c-four': scope.row.logsStatus == 3,
              'c-three': scope.row.logsStatus == 2,

            }"></span>
          {{
            typeMap[reviewType][scope.row.logsStatus] || '--'
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="提交时间"
        align="center"
        width="170"
      >
      </el-table-column>

      <el-table-column
        prop="edit"
        width="220"
        align="center"
        label="操作"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-isShow="'crm:controller:personalgoals:info'"
            class="bbtn"
            type="text"
            @click="todetail(scope.row)"
            v-if="pageBean.handleType == 2"
          >
            详情</el-button
          >
          <el-button
            v-isShow="'crm:controller:personalgoals:info'"
            class="bbtn"
            type="text"
            @click="todetail(scope.row)"
            v-else
          >
            审核</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>

    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <!-- 选择部门 -->
    <departmentDialog
      ref="deRef"
      dType="1"
      :visible.sync="dialogDepartmentVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </departmentDialog>
  </div>
</template>

<script>
import page from '@/components/common/page.vue'
import nolist from '@/components/common/nolist.vue'
import { findTemplate } from '@/api/common'
import {
  goalssummarylogs,
} from '@/api/goal/index'
import {
  getWeekDates,
} from '@/utils/tools'
import { getToken } from '@/utils/auth'
import { getWeek, getPlanTime } from '@/utils/index'
import departmentDialog from '@/components/common/departmentDialog.vue'
import { listDept } from '@/api/framework/dept'

import { isExist } from '@/api/index'
import Bus from '@/utils/EventBus'
export default {
  components: {
    page,
    nolist,
    departmentDialog,
  },
  props:{
    reviewType:{
      type:String,
      default:"plan"
    },
    goalType:{
      type:String,
      default:'1',
    },
  },
  data(){
    return{
      types: {
        '1': {
          label: '年份',
          type: 'year',
          format: 'yyyy',
        },
        '2': {
          label: '年-月',
          type: 'month',
          format: 'yyyy-M',
        },
        '3': {
          label: '年-周',
          type: 'week',
          format: 'yyyy 第 WW 周',
        },
        '4': {
          label: '年份',
          type: 'year',
          format: 'yyyy',
        },
        '5': {
          label: '年-月',
          type: 'month',
          format: 'yyyy-M',
        },
        '6': {
          label: '年-周',
          type: 'week',
          format: 'yyyy 第 WW 周',
        },
      },
      searchValue: '',
      addForm: {
        template: '',
        planTime: '',
        departmentId: '',
      },
      templateList: '',
      deleteData: {},
      draftId: '',
      detailVisible: false,
      dialogVisible: false,
      dialogPlanVisible: false,
      dialogDepartmentVisible: false,
      dialogDraftVisible: false,
      dialogFormVisible: false,
      dialogTitle: '',
      isSubmit: false,
      isLoading: false,
      searchdepartmentNames: '',
      returnStatusArr: [],
      options: [],
      tableData: [],
      total: 0,
      form: {
        name: '',
        dictTypeCode: 'Press',
        sortNo: '',
      },
      pickerOptions:{
        firstDayOfWeek:1,
      },
      makeplanTime: {
        year: '',
        month: '',
        week: '',
      },
      detailPath: {
        '1': '/plan/yearplan/yeardetail',
        '2': '/plan/monthplan/monthdetail',
        '3': '/plan/weekplan/weekdetail',
        '4': '/zongjie/yearzong/detail',
        '5': '/zongjie/monthzong/monthzdetail',
        '6': '/zongjie/weekzong/weekzdetail',
      },
      typeMap:{
        'plan':{
          2:'已通过',
          3: '驳回',
        },
        'zongjie':{
          2: '已查阅',
        },
      },
      pageBean: {
        createByName: '',
        departmentIds: '',
        pageNum: 1,
        pageSize: 10,
        year: '',
        month: '',
        week: '',
        goalsType: '',
        handleType:1,
      },
      selDepartmentData: [],
      departmentArr: [],
      numberToName: {
        1: '准时提交',
        2: '迟交',
        0: '--',
      },
    }
  },
  created(){
    this.pageBean.goalsType =  this.goalType
    this.pageBean.handleType = parseInt(this.$route.query.handleType) || 1
    Bus.$on(`loadReview_${this.goalType}`, (msg) => {
      this.pageBean.handleType = 1
      this.change()
    })
  },
  beforeDestroy(){
      Bus.$off(`loadReview_${this.goalType}`);
  },
  methods:{
    getPlanTime(data) {
      return getPlanTime(data, this.goalType)
    },
    cheangeDe(value) {
      this.addForm.template = ''
      this.loadTemplate()
    },
    loadTemplate() {
      findTemplate({
        methodName: 'save',
        className: 'PersonalGoalsController',
        templateType: this.goalType,
        departmentId: this.addForm.departmentId,
      })
        .then((result) => {
          this.templateList = result.data
        })
        .catch((err) => {})
    },
    querySearch(queryString, callback) {
      var list = [{}]
      if (queryString && queryString.length > 0) {
        let params = {
          dictTypeCode: 'Press',
          name: queryString,
        }
        queryList(params).then((res) => {
          list = res.data.map((item) => {
            return {
              id: `${item.id}`,
              value: `${item.name}`,
            }
          })
          callback(list)
        })
      }
    },
    handlename(item) {
      this.form.name = item.value
    },

    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?goalType=${this.goalType}`
      )
      this.isLoading = true
      goalssummarylogs(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          if (this.goalType == '3' || this.goalType == '6') {
            this.tableData.forEach((item) => {
              item.timeRange = item.year && item.week ? getWeekDates(item.year, item.week) : null
            })
          }
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    chooseDepartment(e) {
      this.dialogDepartmentVisible = true
      this.$refs.deRef.loadData()
      this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
    },
    updateSystemVisible(val) {
      this.dialogDepartmentVisible = val
    },
    submitData(data) {
      this.dialogDepartmentVisible = false
      this.selDepartmentData = data
      let departmentIds = data.map((item) => item.id)
      let departmentNames = data.map((item) => item.name)
      this.pageBean.departmentIds = departmentIds.join(',')
      this.searchdepartmentNames = departmentNames.join(',')
    },
    cleardata() {
      this.searchdepartmentNames = ''
      this.pageBean.departmentIds = ''
      this.selDepartmentData = []
    },
    todetail(data) {
      this.$router.push({
        path: this.detailPath[this.goalType],
        query: {
          id: data.relateId,
          type: this.goalType,
          createByName: data.createByName,
          timeRange: data.timeRange
            ? `${data.timeRange.start}-${data.timeRange.end}`
            : '',
        },
      })
    },
    changeDatePicker(date) {
      if (!date) {
        this.pageBean.year = ''
        this.pageBean.month = ''
        this.pageBean.week = ''
        return
      }
      var newDate = new Date(date)
      this.pageBean.year = newDate.getFullYear()
      if (this.goalType == '2' || this.goalType == '5') {
        this.pageBean.month = newDate.getMonth() + 1
      } else if (this.goalType == '3' || this.goalType == '6') {
        var time = getWeek(newDate)
        var times = time.split('-')
        this.pageBean.year = times[0]
        this.pageBean.week = times[1]
      }
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    change(){
      this.pageBean.pageNum = 1;
      this.pageBean.createByName = ''
      this.pageBean.week = ''
      this.pageBean.month = ''
      this.pageBean.year = ''
      this.pageBean.departmentIds = ''
      this.loadData()
    },
  },
}
</script>

<style lang="scss" scoped>
.concss{
  padding: 0px 10px;
}
.myragroupcss /deep/.el-radio-button__inner{
  padding: 12px 50px;
}
.noset {
  color: #e6a23c;
  margin-left: 8px;
}
.formw {
  width: 340px;
  margin: auto;
}
.mt30 {
  margin-top: 30px;
}
.el-auto {
  width: 280px;
}
.w1 {
  width: 110px;
}
.flexd {
  text-align: right;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}
.definput {
  width: 200px;
}
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: calc(100vh - 106px);
}
.pcc {
  margin: 0 auto;
  text-align: center;
}
.smtext {
  zoom: 0.8;
}
.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.mt {
  margin-top: 4px;
}
.ml {
  margin-right: 10px;
}
.cusnamecss {
  display: flex;
}
.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 20px;
}
.bcolor {
  color: #4285f4;
}
.icolor {
  color: #ff8d1a;
}
.scolor {
  color: #56c36e;
}
.ecolor {
  color: #f45961;
}
.fcolor {
  color: #333333;
}
.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}
.ltext {
  color: #4285f4;
  text-align: justify !important;
}
.mytable /deep/ .cell {
  height: auto !important;
  min-height: 52px !important;
  padding: 13px !important;
  line-height: 25px !important;
}
.mytable .el-button {
  padding: 2px;
}
.mr10 {
  margin-right: 10px;
}
.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.right {
  text-align: right;
}
.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}
</style>
