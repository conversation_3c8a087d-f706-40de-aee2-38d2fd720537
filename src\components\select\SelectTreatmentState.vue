<!--申诉管理处理状态下拉框-->
<template>
    <div>
        <el-select v-model="stateValue" placeholder="处理状态">
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
        </el-select>
    </div>
</template>

<script>
export default {
  data() {
    return {
      stateValue: '',
      options: []
    }
  }
}
</script>
