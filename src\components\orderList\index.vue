<template>
  <div class="maskview">
    <div class="headerdiv">
      <div
        class="hdiv"
        v-for="(item, index) in statusList"
        :key="index"
        @click="selectItem(item, index)"
      >
        <div class="textitem" :class="{ selectitem: sItem == index }">
          {{ item.name }}
        </div>
        <div class="hline" v-if="sItem == index"></div>
      </div>
    </div>
    <div class="searchdiv">
      <el-form :model="pageBean" :inline="true">
        <el-form-item label="订单编号：">
          <el-input
            v-model="pageBean.orderNo"
            clearable=""
            placeholder="请输入订单编号"
          ></el-input>
        </el-form-item>
        <el-form-item label="供应商：">
          <el-input
            v-model="pageBean.platformName"
            clearable=""
            placeholder="请输入供应商名称"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="商品名称：">
          <el-input
            v-model="pageBean.productName"
            clearable=""
            placeholder="请输入商品名称"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="下单时间：">
          <el-date-picker
            value-format="yyyy-MM-dd"
            v-model="value2"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="支付渠道：">
          <el-select
            clearable=""
            v-model="pageBean.paymentType"
            class="widthdiv"
            placeholder="支付方式"
          >
            <el-option label="支付宝" value="1"></el-option>
            <el-option label="微信" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="买家联系方式：">
          <el-input placeholder="请输入买家联系方式"> </el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="chaxun" class="btns" type="primary"
            >查询</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button class="btns" type="primary">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="orederlist">
      <div class="tableHeader">
        <el-row class="headerrow">
          <el-col :span="5">
            <div class="cloumntext leftspace">商品信息</div>
          </el-col>
          <el-col :span="2">
            <div class="cloumntext">购买数量</div>
          </el-col>
          <el-col :span="3">
            <div class="cloumntext">商品总价(元)</div>
          </el-col>
          <el-col :span="3">
            <div class="cloumntext">订单金额(元)</div>
          </el-col>
          <el-col :span="6">
            <div class="cloumntext">买家</div>
          </el-col>
          <el-col :span="2">
            <div class="cloumntext">订单状态</div>
          </el-col>
          <el-col :span="3">
            <div class="cloumntext">操作</div>
          </el-col>
          <el-col></el-col>
        </el-row>
      </div>
      <template v-if="orderList.length > 0">
        <div class="listitem" v-for="(item, index) in orderList" :key="index">
          <div class="orderHeader">
            <div class="vline"></div>
            <div class="ordermsg left1">订单编号：{{ item.orderNo }}</div>
            <div class="ordermsg left2">订单时间：{{ item.createTime }}</div>
            <div class="ordermsg left2">供应商：{{ item.platformName }}</div>
          </div>
          <div
            class="goodsitem"
            v-for="(item1, index) in item.orderDetailInfoVos"
            :key="index"
          >
            <el-row class="goodsrow">
              <el-col :span="5">
                <div class="goodsview">
                  <div class="goodsimg">
                    <!-- <img src="../../assets/loading.png" alt="" /> -->
                  </div>
                  <div class="rightview">
                    <div class="goodstext">{{ item1.productName }}</div>
                    <div class="goodstext">
                      {{ item1.parentName }}>{{ item1.name }}
                    </div>
                    <div class="goodstext">书号/ISBN：{{ item1.isbn }}</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="2">
                <div class="goodstext center">{{ item1.productNum }}</div>
              </el-col>
              <el-col :span="3">
                <div class="goodstext center">{{ item1.pricing }}</div>
              </el-col>
              <el-col :span="3">
                <template v-if="item.paymentType == 1 || item.paymentType == 2">
                  <div class="goodstext space">
                    订单金额：{{ item1.pricing }}
                  </div>
                  <div class="goodstext">
                    实际支付：{{ item1.paymentMoney }}
                  </div>
                  <div class="goodstext">
                    支付方式：{{
                      item.paymentType == 1 ? '支付宝支付' : '微信支付'
                    }}
                  </div>
                </template>
                <template v-if="item.paymentType == 0">
                  <div class="goodstext center">{{ item1.pricing }}</div>
                </template>
              </el-col>
              <el-col :span="6">
                <div class="goodstext topspace">
                  {{ item.userName }}-{{ item.userPhone }}
                </div>
                <div class="goodstext">收货地址：{{ item.address }}</div>
              </el-col>
              <el-col :span="2">
                <div class="goodstext center">
                  <!-- 1:"待支付", 2:"已支付", 3:"待发货", 4:"已发货", 5:"确认收货",
                  6:"取消交易", 7:"等待卖家同意", 8:"等待买家退货",
                  9:"买家已退货", 10:"退货成功" -->
                  <el-tag v-if="item.orderStatus == 3" type="danger"
                    >待发货</el-tag
                  >
                  <el-tag v-if="item.orderStatus == 4" type="danger"
                    >待收货</el-tag
                  >
                  <el-tag v-if="item.orderStatus == 5" type="success"
                    >已收货</el-tag
                  >
                  <el-tag v-if="item.orderStatus == 8" type="danger"
                    >退款待审核</el-tag
                  >
                  <el-tag v-if="item.orderStatus == 9" type="success"
                    >退款完成</el-tag
                  >
                  <el-tag v-if="item.orderStatus == 10" type="danger"
                    >退款成功</el-tag
                  >
                  <el-tag v-if="item.orderStatus == 6" type="success"
                    >订单结束</el-tag
                  >
                  <el-tag v-if="item.orderStatus == 7" type="success"
                    >退货</el-tag
                  >
                  <el-tag v-if="item.orderStatus == 1" type="danger"
                    >待支付</el-tag
                  >
                </div>
              </el-col>
              <el-col :span="3">
                <div class="editbtns">
                  <el-button
                    class="orderbtn"
                    size="small"
                    @click="goDetailAction(item)"
                    >订单详情</el-button
                  >
                </div>
              </el-col>
            </el-row>
            <div class="bottomLine" v-if="index != orderList.length - 1"></div>
          </div>
        </div>
      </template>
      <div v-else>
        <p class="zanwutext">暂无数据</p>
      </div>
    </div>
    <div class="bottomView">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        layout="prev, pager, next, jumper,total"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
// import { orderList } from '@/api/order/index.js'
export default {
  data() {
    return {
      sItem: 0,
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        platformName: '',
        orderNo: '',
        orderStatus: '',
        productName: '',
        paymentType: '',
        beginTime: '',
        endTime: '',
      },
      orderList: [],
      value2: '',
      statusList: [
        {
          id: '0',
          name: '全部',
        },
        {
          id: '1',
          name: '待支付',
        },
        {
          id: '3',
          name: '待发货',
        },
        {
          id: '4',
          name: '待收货',
        },
        {
          id: '6',
          name: '已完成/已结束',
        },
        {
          id: '7',
          name: '售后/退款',
        },
      ],
    }
  },
  created() {
    this.getorderList()
  },
  methods: {
    chaxun() {
      if (this.value2 != null && this.value2 != '') {
        this.pageBean.beginTime = this.value2[0]
        this.pageBean.endTime = this.value2[1]
      }
      this.pageBean.pageNum = 1
      this.getorderList()
    },
    getorderList() {
      orderList(this.pageBean).then((res) => {
        if (res.status == 0) {
          this.orderList = res.data
          this.total = res.page.total
        }
      })
    },
    selectItem(item, index) {
      this.pageBean.orderStatus = item.id
      this.sItem = index
      this.pageBean.pageNum = 1
      this.getorderList()
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.getorderList()
    },
    // 监听pagesize的变化
    handleSizeChange(page) {
      this.pageBean.pageNum = page
      this.getorderList()
    },
    goDetailAction(item) {
      this.$router.push({
        path: '/orderList/detail',
        query: {
          id: item.id,
        },
      })
    },
  },
}
</script>
<style scoped>
.juzhongcss {
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.zanwutext {
  height: 300px;
  text-align: center;
  line-height: 300px;
}
.maskview {
  /* position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px; */
}
.headerdiv {
  height: 80px;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  /* background-color: red; */
}
.textitem {
  padding: 0px 15px;
  font-size: 18px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #111111;
  cursor: pointer;
}
.hdiv {
  height: 30px;
  text-align: center;
}
.selectitem {
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #6672fc;
}
.hline {
  width: 26px;
  height: 4px;
  margin: 0 auto;
  margin-top: 5px;
  border-radius: 2px;
  background: #6672fc;
}
.searchdiv {
  margin-top: 20px;
  padding: 0px 30px;
  padding-top: 20px;
  background-color: white;
}
.btns {
  width: 80px;
  height: 40px;
  margin-left: 20px;
  background: #6672fc;
  border-radius: 4px;
}
.widthdiv {
  width: 180px;
}
.datewidth {
  width: 300px;
}
.headerrow {
  height: 50px;
  padding: 0 !important;
  background: #f6f9fe;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}
.leftspace {
  margin-left: 20px;
}
.cloumntext {
  padding-left: 10px;
  line-height: 50px;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #111111;
}
.listitem {
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}
.goodsrow {
  height: 134px;
  padding: 0 !important;
}
.orderHeader {
  margin-top: 10px;
  height: 40px;
  border-bottom: 1px solid #eceff8;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.orederlist {
  margin-top: 20px;
  padding: 20px 30px;
  background-color: white;
}
.vline {
  margin-left: 30px;
  width: 4px;
  height: 20px;
  background: #6672fc;
  border-radius: 2px 2px 2px 2px;
}
.ordermsg {
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #000;
}
.left1 {
  margin-left: 10px;
}
.left2 {
  margin-left: 30px;
}
.goodsview {
  height: 134px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.goodsimg {
  margin-left: 30px;
  width: 80px;
  height: 100px;
  background-color: #d6dcf0;
}
.goodsimg img {
  width: 100%;
  height: 100%;
}
.goodstext {
  margin-left: 10px;
  font-size: 12px;
  line-height: 30px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #333333;
}
.editbtns {
  height: 134px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
}
.el-button + .el-button {
  margin-left: 0px;
}
.center {
  line-height: 134px;
}
.space {
  margin-top: 25px;
}
.topspace {
  margin-top: 30px;
}
.orderbtn {
  width: 80px;
  background: #6672fc;
  color: white;
  border-radius: 16px;
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}
.bottomLine {
  margin: 0px 30px;
  height: 1px;
  background-color: #eceff8;
}
.bottomView {
  height: 80px;
  line-height: 80px;
  background-color: white;
  text-align: center;
}
</style>

<style>
</style>