<template>
  <div>
    <div>
      <back>返回</back>
    </div>
    <el-card v-loading="isLoading" class="mt10">
      <el-form label-width="85px" class="myform">
        <el-row :gutter="10" type="flex" justify="start">
          <el-col :span="6">
            <el-form-item label="客户名称：">
              <el-input
                class="definput"
                v-model="pageBean.customerName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产出比：" label-width="80px">
              <div class="numdiv">
                <el-input
                  @input="handleEdit"
                  class="wip1"
                  v-model="pageBean.searchMin"
                  type="number"
                >
                </el-input>
                <div class="form-center">--</div>
                <el-input
                  @input="handleEdit1"
                  class="wip1"
                  v-model="pageBean.searchMax"
                  type="number"
                >
                </el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button
              class="defaultbtn1 mt"
              icon="el-icon-search"
              type="primary"
              @click="searchAction"
              >搜索</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <el-table
        @sort-change="sortChange"
        class="customertable mytable tootiptable"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="customerName" label="客户名称" align="center">
        </el-table-column>
        <el-table-column prop="unitDepartment" label="部门" align="center">
        </el-table-column>
        <el-table-column prop="yongliangNum" label="负责专业" align="center">
          <template slot-scope="scope">
            <div>
              {{
                scope.row.specialtyName && scope.row.specialtyName.length > 0
                  ? scope.row.specialtyName.join('/')
                  : ''
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="inputPrice"
          :formatter="formatter"
          label="投入金额"
          align="center"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="outPrice"
          :formatter="formatter"
          label="产出金额"
          align="center"
        >
        </el-table-column>
        <el-table-column
          width="120"
          sortable="custom"
          prop="proportion"
          label="产出投入比"
          align="center"
        >
          <template slot-scope="scope">
            {{ calculate(scope.row.proportion) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="edit"
          width="80"
          align="center"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button class="bbtn" type="text" @click="toBook(scope.row)">
              明细
            </el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <nolist></nolist>
        </template>
      </el-table>
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </el-card>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import page from '@/components/common/page.vue'
import nolist from '@/components/common/nolist.vue'
import { queryInputOutInfo } from '@/api/businessdata/index'
import { getParStr } from '@/utils/tools'
export default {
  components: {
    page,
    nolist,
    back,
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        searchMin: '',
        searchMax: '',
        unitId: '',
        year: '',
        rankType: '',
        customerName: '',
      },
      updateRankType: {
        'ascending:inputPrice': 1,
        'descending:inputPrice': 2,
        'ascending:outPrice': 3,
        'descending:outPrice': 4,
        'ascending:proportion': 5,
        'descending:proportion': 6,
      },
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
    }
    this.loadData()
  },
  methods: {
    calculate(proportion) {
      if (proportion == 0) {
        return 0 + '%'
      } else {
        return (proportion * 100).toFixed(2) + '%'
      }
    },
    formatter(row, column, cellValue) {
      return cellValue + '万元'
    },
    toBook(data) {
      this.$router.push({
        path: '/businessdata/inputout/mingxi',
        query: {
          customerId: data.customerId,
          year: this.$route.query.year,
        },
      })
    },
    handleEdit(e) {
      let value = e.replace(/[^\d.]/g, '') // 只能输入数字
      value = value.replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
      this.pageBean.searchMin = value
    },
    handleEdit1(e) {
      let value = e.replace(/[^\d.]/g, '') // 只能输入数字
      value = value.replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
      this.pageBean.searchMax = value
    },
    sortChange(column) {
      let props = column.order + ':' + column.prop
      this.pageBean.pageNum = 1
      this.pageBean.rankType = this.updateRankType[props]
      this.loadData()
    },
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      queryInputOutInfo(this.pageBean)
        .then((result) => {
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    searchAction() {
      if (this.pageBean.searchMax == 0 && this.pageBean.searchMax != '') {
        this.msgError('最大值必须大于0')
        return
      }
      if (
        (+this.pageBean.searchMin >= 0 && this.pageBean.searchMin != '') ||
        (+this.pageBean.searchMax >= 0 && this.pageBean.searchMax != '')
      ) {
        if (
          +this.pageBean.searchMin >= 0 &&
          this.pageBean.searchMin != '' &&
          this.pageBean.searchMax == ''
        ) {
          this.msgError('请输入数量的最大值')
          return
        }
        if (
          +this.pageBean.searchMin >= 0 &&
          this.pageBean.searchMin != '' &&
          this.pageBean.searchMax != '' &&
          +this.pageBean.searchMax <= +this.pageBean.searchMin
        ) {
          this.msgError('最大值得大于最小值')
          return
        }
        if (
          this.pageBean.searchMin == '' &&
          this.pageBean.searchMax != '' &&
          this.pageBean.searchMax >= 0
        ) {
          this.msgError('请输入数量的最小值')
          return
        }
      }
      if (this.pageBean.platformName == '') {
        if (this.$route.query.platformName) {
          this.pageBean.platformName = this.$route.query.platformName
        }
      }
      this.pageBean.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>
<style scoped>
.yearcss /deep/.el-input__inner {
  height: 34px;
}
.wip1 {
  min-width: 40px;
  width: calc(50% - 10px);
  height: 34px;
}
.wip1 /deep/.el-input__inner {
  height: 28px;
  position: relative;
  top: -4px;
  line-height: 1px !important;
}
.wip1 /deep/.el-input__suffix {
  height: 28px;
  line-height: 34px;
}
.nums /deep/.el-input__inner {
  padding-right: 10px !important;
  padding-left: 6px !important;
}
.nums /deep/.el-input__suffix {
  height: 34px;
}
.definput /deep/.el-input__icon {
  line-height: 32px;
  height: 32px;
}
.usetimecss {
  width: 106px;
}
.nums {
  width: 80px;
  min-width: 80px;
  display: inline-block;
  vertical-align: top;
}

.numdiv {
  height: 34px;
  border: 1px solid #dcdfe6;
  padding: 0 4px;
  display: inline-block;
  margin-top: 3px;
  margin-left: 6px;
}

.form-center {
  display: inline-block;
  height: 34px;
  width: 20px;
  text-align: center;
  line-height: 34px;
  vertical-align: top;
}
.form-conten {
  position: relative;
}
.el-date-editor.el-input {
  width: 100%;
}
.useTime {
  width: 120px;
  display: inline-block;
  margin-right: 20px;
}
.defaultbtn1 {
  padding: 9px 10px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.mt {
  margin-top: 4px;
}
.customertable .el-button {
  padding: 2px;
}
.right {
  text-align: right;
}
</style>