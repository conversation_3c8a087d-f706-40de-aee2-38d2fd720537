<template>
  <div>
    <div v-if="!isControl" class="fl mb30 mtop20">
        <div class="tcss" v-if="viewdata.accumulatedWorkHours">总计工时：{{viewdata.accumulatedWorkHours}}</div>
        <div class="tcss">截止日期：{{viewdata.endTime}} <span v-if="viewdata.overdue" class="stacolor">已逾期</span></div>
        <div class="tcss">开始时间：{{viewdata.beginTime}}</div>
        <div class="tcss" v-if="viewdata.actualBeginTime">实际开始：{{viewdata.actualBeginTime}}</div>
    </div>
    <el-table
    :data="tableData"
    class="mytable"
    height="590px"
    style="width: 100%">
    <el-table-column
      prop="workTime"
      label="日期"
      width="120" align="center">
    </el-table-column>
    <el-table-column
      v-if="!isControl"
      prop="taskName"
      label="任务"
      width="180"
      align="center"
      show-overflow-tooltip>
    </el-table-column>
    <el-table-column
      prop="content"
      label="工作内容"
      min-width="180" align="center">
    </el-table-column>
    <el-table-column
      prop="workHours"
      label="消耗工时(小时)"
      width="120" align="center">
    </el-table-column>
    <el-table-column
      v-if="!isControl"
      prop="createByName"
      label="记录人"
      width="120" align="center">
    </el-table-column>
    <el-table-column
      v-if="isControl"
      prop="edit"
      label="操作"
      width="80" align="center">
      <template slot-scope="scope">
        <span  :class="{ 'notallowed': taskStatus == 3 || taskStatus == 5 }">
          <span :class="{ 'styled': taskStatus == 3 || taskStatus == 5 }" class="rbtn" @click="showDelete(scope.row)">删除</span>
        </span>
      </template>
    </el-table-column>
  </el-table>
  <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                @updatePageNum="handleCurrentChange"></page>
  </div>
</template>

<script>
import page from '@/components/common/page.vue';
import { queryWorkListForTask } from '@/api/project/index';
import { hourWorkList,taskworkhoursDelete } from '@/api/project'
import Bus from '@/utils/EventBus'
import { mapGetters } from 'vuex'
export default {
  props:{
    isControl:{
      type:Boolean,
      default:false,
    },
  },
  // inject: ['taskIdObj'],
  inject: {
    taskIdObj: {
      default: ()=>({})
    }
  },
  data(){
    return{
      tableData:[],
      viewdata:{
        actualBeginTime:'',
        beginTime:"",
        endTime:"",
        overdue:false
      },
      pageBean:{
        pageNum:1,
        pageSize:10,
        type:'1',
        taskId:'',
      },
      total:0,
    }
  },
  created(){
    if (this.isControl) {
      Bus.$on('loadData', (msg) => {
        this.pageBean.taskId = this.taskIdObj.taskId
        this.loadData()
      })
    }else{
      this.pageBean.taskId = this.$route.query.id
     
    }
    
  },
  components:{
    page
  },
  computed: {
        ...mapGetters(['taskStatus']),
      },
  methods:{
    showDelete(row){
      taskworkhoursDelete({ id: row.id }).then((result) => {
          if (result.data) {
              this.$message({
                  type: "success",
                  message: '删除成功'
              })
              this.loadData();
              this.$emit('deleteSubmit')
          } else {
              this.$message({
                  type: "error",
                  message: result.msg
              })
          }
      }).catch((err) => {
      });
    },
    loadData(){
      hourWorkList(this.pageBean).then(res=>{
        if(res.status == 0){
          this.tableData = res.data;
          this.total = res.page.total;
        }

      })
    },
    handleCurrentChange(page){
      this.pageBean.pageNum = page
      if (this.isControl) {
        this.loadData()
      }else{
        this.loadworks()
      }
      
    },
    updateTaskId(taskId){
      this.pageBean.taskId = taskId
    },
    loadworks(taskId){
      if (taskId) {
        this.pageBean.taskId = taskId
      }
      queryWorkListForTask(this.pageBean).then((result) => {
          this.viewdata.accumulatedWorkHours = result.accumulatedWorkHours
          this.tableData = result.data.taskWorkHoursVoList;
          this.total = result.page.total;
      }).catch((err) => {
          
      });
    },
    getTaskInfo(data){
      this.viewdata.beginTime = data.beginTime;
      this.viewdata.endTime = data.endTime;
      this.viewdata.actualBeginTime = data.actualBeginTime;
      this.viewdata.overdue = data.overdue
    },
  }
}
</script>

<style lang="scss" scoped>
  .styled{
    pointer-events:none;
  }
  .notallowed{
    cursor:not-allowed !important;
  }
.fl{
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.stacolor{
  line-height: 24px;
  background: #FFF5F6;
  border-radius: 12px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #F45961;
  padding: 4px 8px;
}
.mtop20{
  margin-top: 20px;
}
.tcss+.tcss{
  margin-left: 10px;
}
.tcss{
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}
.rbtn{
  color: #F45961;
  cursor: pointer;
}
.mytable{
  border: 1px solid #E6E6E6;
  border-radius: 8px;
}

</style>