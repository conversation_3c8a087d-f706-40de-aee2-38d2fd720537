// /crm/controller/distribution
import request from '@/utils/request'


export function distributionList(params) {
  return request({
    url: "/crm/controller/distribution/list",
    method: 'get',
    params
  })
}

export function addDistribution(data) {
  return request({
    url: '/crm/controller/distribution/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}



export function queryCustomerChargePersonDepartmentList(data) {
  return request({
    url: '/crm/controller/customer/queryCustomerChargePersonDepartmentList',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}

export function updateDistribution(data) {
  return request({
    url: '/crm/controller/distribution/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}

// 详情
export function distributionInfo(id) {
  return request({
    url: `/crm/controller/distribution/info/${id}`,
    method: 'get',
  })
}

//  删除发放
export function deleteDistribution(data) {
  return request({
    url: '/crm/controller/distribution/delete',
    method: 'post',
    data,

  })
}

// 发放明细  

export function distributionDetailList(params) {
  return request({
    url: '/crm/controller/distributiondetail/list',
    method: 'get',
    params
  })
}
// 新增发放明细
export function addDistributionDetail(data) {
  return request({
    url: '/crm/controller/distributiondetail/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}
// 删除发放明细
export function deleteDistributionDetail(data) {
  return request({
    url: '/crm/controller/distributiondetail/delete',
    method: 'post',
    data
  })
}
//  /crm/controller/distributiondetail/queryDetailOverview
//  发放明细列表中物品数量、总金额
export function queryDetailOverview(params) {
  return request({
    url: '/crm/controller/distributiondetail/queryDetailOverview',
    method: 'get',
    params
  })
}

// 发放回访列表 /crm/controller/distributionrevisit/list
export function distributionRevisitList(params) {
  return request({
    url: '/crm/controller/distributionrevisit/list',
    method: 'get',
    params
  })
}

// 新增回访
export function addDistributionRevisit(data) {
  return request({
    url: '/crm/controller/distributionrevisit/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,

  })
}
// 删除回访
export function deleteDistributionRevisit(data) {
  return request({
    url: '/crm/controller/distributionrevisit/delete',
    method: 'post',
    data
  })
}

// /crm/controller/distributiondetail/downloadTemplate
// 发放明细模板下载
export function downloadTemplate(params) {
  return request({
    url: '/crm/controller/distributiondetail/downloadTemplate',
    method: 'get',
    responseType: 'blob',
    params
  })
}
// 礼品发放数据下载
export function downloadTemplateGift(params) {
  return request({
    url: '/crm/controller/distribution/exportExcel',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// /crm/controller/distributiondetail/batchImportExcel

export function batchImportExcel(params) {
  return request({
    url: '/crm/controller/distributiondetail/batchImportExcel',
    method: 'get',
    params
  })
}



// 商务数据导出
// 
export function exportBusinessList(params) {
  return request({
    url: '/crm/controller/distribution/exportBusinessList',
    method: 'get',
    params
  })
}


/// /crm/controller/distribution/distributionDownload

export function distributionDownload(params) {
  return request({
    url: '/crm/controller/distribution/distributionDownload',
    method: 'get',
    params,
  })
}