<template>
  <div>
    <div>
      <back>权限设置</back>
    </div>
    <div class="card">
      <el-form label-position="top" ref="form" class="myform perset" :rules="rules" :model="form" label-width="100px" v-loading="isLoading">
            <textBorder class="pertext">权限设置</textBorder>
            <el-form-item  label="填写设置:" prop="allowSave">
              <el-radio-group class="radiog" v-model="form.allowSave">
                <el-radio :label="1">组织内成员均可填写（全公司）</el-radio>
                <el-radio :label="2" >组织内指定成员可填写（指定部门或者人）</el-radio>
                <div class="unitbtn mt definput" @click="chooseDepartment(3)" v-if="form.allowSave == 2">
                  <template v-if="departPeopleList.length>0">
                    <div class="pelist">
                      <span class="el-tag el-tag--info el-tag--small el-tag--light eltag" v-for="(item,index) in departPeopleList">
                        <span class="el-select__tags-text">{{item.name}}</span>
                        <i class="el-tag__close el-icon-close" @click.stop="closeTag(3,item.id)"></i>
                      </span>
                    </div>
                  </template>
                  <span v-else>请选择</span>
                  <i  class="rcenter1 el-icon-arrow-down" />
                </div>
              </el-radio-group>
            </el-form-item>
            <el-form-item  label="字数限制:" prop="wordsNum">
              <el-radio-group class="radiog" v-model="form.wordsNum">
                <el-radio :label="1">无字数限制</el-radio>
                <el-radio :label="2">填写字数限制</el-radio>
              </el-radio-group>
              <div v-if="form.wordsNum == 2">
                <span>
                  最少:
                </span>
                <el-input oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"  type="number" min="1" class="mininput" v-model="form.minNum"></el-input>
                <span class="maxtext">
                  最多:
                </span>
                <el-input oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" class="mininput" v-model="form.maxNum"></el-input>
              </div>
            </el-form-item>
          <el-form-item  label="抄送:" prop="allowCopy">
            <el-radio-group class="radiog" v-model="form.allowCopy">
              <el-radio :label="1">不许抄送</el-radio>
              <el-radio :label="2">允许抄送</el-radio>
            </el-radio-group>
          </el-form-item>
            <el-form-item label="发送范围:" prop="receivedPersons">
                      <div class="unitbtn mt definput" @click="chooseDepartment(2)">
                        <template v-if="peopleList.length>0">
                          <div class="pelist">
                            <span class="el-tag el-tag--info el-tag--small el-tag--light eltag" v-for="(item,index) in peopleList">
                              <span class="el-select__tags-text">{{item.name}}</span>
                              <i @click.stop="closeTag(2,item.id)" class="el-tag__close el-icon-close"></i>
                            </span>
                          </div>
                        </template>
                        <span v-else>请选择</span>
                        <i  class="rcenter1 el-icon-arrow-down" />
                      </div>
            </el-form-item>
          </el-col>
        <el-button type="primary" class="savecss btn_h42 w98" @click="submitForm">保存</el-button>
      </el-form>
    </div>
    <departmentDialogDaily ref="deRef" :dType="diaType" v-if="dialogDepartmentVisible" :dialogDepartmentVisible="dialogDepartmentVisible" @updateVisible="updateSystemVisible"
    @submitData="submitData">
    </departmentDialogDaily>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import textBorder from '../../common/textBorder.vue'
import { saveTemplatePermission,templatePermissionInfo } from '@/api/daily/index';
import departmentDialogDaily from '@/components/common/departmentDialogDaily.vue';
import { removeById } from '@/utils/tools.js'
export default {
  components: {
    back,
    textBorder,
    departmentDialogDaily
  },
  data() {
    return {
      dialogDepartmentVisible: false,
      isLoading: false,
      rules: {
        wordsNum: [
          { required: true, message: '请选择字数限制', trigger: 'change' }
        ],
        allowSave: [
          { required: true, message: '请选择填写设置', trigger: 'change' }
        ],
        allowCopy: [
          { required: true, message: '请选择是否抄送', trigger: 'change' }
        ],
        receivedPersons: [
            { required: true, message: '请选择发送范围', trigger: 'change' }
          ],
      },
      form: {
        minNum: '1',
        maxNum: '',
        templateId:'',
        allowSave:1,
        wordsNum:1,
        allowCopy:2,
        templateAllow:[],
        receivedPersons:[],
      },
      departPeopleList:[],
      diaType:'',
      peopleList:[],
    }
  },
  created() {
    if (this.$route.query.templateId) {
          this.form.templateId = this.$route.query.templateId
          this.loadInfo()
    }
  },
  methods: {
    closeTag(type,idtemId){
        if(type==3){
          this.departPeopleList = removeById(this.departPeopleList,idtemId)
        }
        if(type==2){
          this.peopleList = removeById(this.peopleList,idtemId)
        }
    },
    loadInfo() {
      templatePermissionInfo({ templateId: this.$route.query.templateId})
          .then(result => {
                 let resData
                 if(result.status ==0 && result.data){
                    resData = result.data
                    this.form = result.data
                    if(this.form.maxNum == 0){
                      this.form.maxNum = ''
                    }
                    if(resData.wordsNum == 2){
                        this.form.minNum = resData.minNum
                        if(resData.maxNum){
                          this.form.maxNum = resData.maxNum
                        }
                    }
                    if(resData.allowSave == 2){
                      this.departPeopleList = resData.templateAllow.map((item,index)=>{
                          return {
                            id:item.departmentUserId,
                            name:item.departmentUserName,
                            allowType:item.allowType
                          }
                      })
                    }
                    this.peopleList = resData.receivedPersons.map((item,index)=>{
                          return {
                            id:item.userId,
                            name:item.name,
                          }
                    })
                }
            
          })
          .catch(err => { });
      },
    submitData(data) {
      if(this.diaType == 3){
          this.departPeopleList = data
      }else if(this.diaType == 2){
          this.peopleList = data
          this.form.receivedPersons = this.peopleList.map((item,index)=>{
              return {
                userId:item.id,
                logsSort:index,
                disposeType:3
              }
          })
          this.$refs.form.validateField('receivedPersons');
      }
      this.dialogDepartmentVisible = false
    },
    updateSystemVisible(val){
      this.dialogDepartmentVisible = val
    },
    chooseDepartment(type){
        this.dialogDepartmentVisible = true
        this.diaType = type+''
        this.$nextTick(()=>{
          this.$refs.deRef.loadData()
          if(type == 3){
             this.$refs.deRef.updateWorksId(this.departPeopleList)
          }else if(type ==2){
            this.$refs.deRef.updateWorksId(this.peopleList)
          }
        })
    },
    submitForm() {
      if(this.form.allowSave == 2){
        if(this.departPeopleList.length==0){
              this.$message({
                type: "error",
                message: "请选择填写设置中指定部门或者人"
              })
              return 
          }
      }
      if(this.form.wordsNum == 2){
          if(this.form.minNum == ''){
              this.$message({
                type: "error",
                message: "请输入最少字数限制"
              })
              return 
          }
          if(this.form.minNum != '' && this.form.maxNum != ''){
              if(this.form.minNum>this.form.maxNum){
                this.$message({
                  type: "error",
                  message: "字数限制最大值必须大于最小值"
                })
                return 
              }
          }
      }
      if(this.form.allowSave == 1){
        this.form.templateAllow = []
      }else{
        this.form.templateAllow = this.departPeopleList.map((item,index)=>{
            return {
              departmentUserId:item.id,
              sort:index,
              allowType:item.allowType
            }
        })
      }
      this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          saveTemplatePermission(this.form).then((result) => {
            if (result.data) {
              this.$message({
                type: "success",
                message: "保存成功！"
              })
              this.$router.back();
            } else {
              this.$message({
                type: "error",
                message: "保存失败！"
              })
            }
          }).catch((err) => {

          })})
    },
  }
}
</script>

<style lang="scss" scoped>
  .unitbtn{
    height: auto !important;
    padding: 0 10px;
  }
  .eltag{
    margin-right: 10px;
    margin: 2px 0 2px 6px;
  }
  .pelist{
    width: 550px;
    white-space: normal;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .maxtext{
    margin-left: 20px;
  }
    .rcenter1{
      top: 0;
      position: absolute;
      right: 10px;
      font-size: 14px;
      color: #c0c4cc;
      height: 100%;
      line-height: 100%;
      display: flex;
      align-items: center;
    }
     .definput{
        width: 600px;
        position: relative;
        margin-left: 24px;
    }
  .mininput {
      display: inline-block;
      width: 200px;
  }
  .mininput  /deep/.el-input__inner {
      height: 34px;
  }
  .pertext{
    margin-bottom: 20px;
  }
  .perset /deep/.el-form-item__label{
      padding: 0 !important;
  }
  .radiog .el-radio{
      display: block;
      line-height: 26px;
  }
.w98 {
  width: 98px;
}
.savecss {
  display: block;
  margin: 0 auto;
}
.card {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
}
</style>