<template>
  <div>
    <div>
      <back :type="2"  @goback="back">{{ $route.query.id ? '修改总结' : '新增总结' }}</back>
    </div>
    <div class="mainbg">
      <div class="owntop" :class="{ w60: isShow }">
        <div class="plantitle">
          <div>{{ titleStr.mainTitle }}</div>
          <div v-if="titleStr.subTitle" class="subtitle">
            ({{ titleStr.subTitle }})
          </div>
        </div>
        <div class="titlefl" v-loading="isLoading">
          <div class="titlecss">个人总结</div>
        </div>
        <addzongcom
          :layouttype="isShow ? '2' : '1'"
          :zjtype="formLabelAlign.goalsType"
          :year="formLabelAlign.year"
          :month="formLabelAlign.month"
          :week="formLabelAlign.week"
          :zjid="$route.query.id"
        ></addzongcom>
        <addmodelcom ref="modelCom" @headdenForm="headdenForm"></addmodelcom>
        <div>
          <div class="pflex">
            <div class="pd10">抄送人：</div>
            <ul class="uflex">
              <li v-for="(item, index) in peopleList" :key="index" class="pli">
                <img
                  @click="closeTag(item.id)"
                  src="@/assets/close.png"
                  alt=""
                  class="closeimg"
                />
                <img v-if="item.logo" class="pimg" :src="item.logo" alt="" />
                <div class="noimg" v-else>
                  {{ item.name }}
                </div>
                <p class="pname">{{ item.name }}</p>
              </li>
              <li class="adddi" @click="clickXuan">
                <i class="el-icon-plus iadd"></i>
              </li>
            </ul>
          </div>
        </div>
        <div class="submitmask">
          <el-button
            class="defaultbtn"
            type="primary"
            @click="saveGoalAction(false)"
            v-dbClick
            >提交</el-button
          >
          <el-button
            class="defaultbtn"
            style="margin-left: 10px;"
            @click="saveGoalAction(true)"
            v-dbClick
            v-if="formLabelAlign.goalStatus === 0 || !formLabelAlign.id"
            >保存草稿</el-button
          >
        </div>
      </div>
      <div class="rightdiv" :class="{ w40: isShow }">
        <div v-if="isShow" class="reviewmask">
          <planreview
            ref="planreview"
            v-if="reviewType == 'planreview'"
            @closereview="closereview"
            :plantype="`${+form.goalsType - 3}`"
            :viewData="viewData"
            :date="planTime"
            :page="pageData"
          >
          </planreview>
          <summaryreview
            v-if="reviewType == 'summaryreview'"
            @closereview="closereview"
            :plantype="form.goalsType"
            :viewData="viewData"
            :date="planTime"
            :page="pageData"
          >
          </summaryreview>
        </div>
        <div v-else class="tright">
          <div class="bbtn" id="planreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            计划回顾
          </div>
          <div class="bbtn" id="summaryreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            总结回顾
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="选择回顾时间"
      :visible.sync="dialogPlanVisible"
      width="340px"
      center
      :before-close="handlePlanClose"
    >
      <span>
        <el-form class="mt30" label-width="85px">
          <el-form-item
            :label="`${
              types[
                reviewType == 'planreview'
                  ? +form.goalsType - 3
                  : form.goalsType
              ].label
            }：`"
          >
            <el-date-picker
              class="definput"
              :picker-options="pickerOptions"
              @change="changeDatePicker"
              v-model="planTime"
              :type="types[form.goalsType].type"
              :format="types[form.goalsType].format"
              placeholder="请选择"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextAction">下一步</el-button>
      </span>
    </el-dialog>
    <systemDialogZong
      ref="systemdialog"
      name="选择抄送人"
      :multipleNum="0"
      :visible.sync="dialogSystemVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialogZong>
  </div>
</template>
<script>
import planreview from '@/components/common/planreview.vue'
import summaryreview from '@/components/common/summaryreview.vue'
import addmodelcom from '@/components/zongjie/temmodel/addmodelcom.vue'
import systemDialogZong from '@/components/common/systemDialogZong.vue'
import { getWeek, getPlanTime } from '@/utils/index'
import { removeById } from '@/utils/tools'
import back from '../../common/back.vue'
import addzongcom from '../components/addzongcom.vue'
import {
  addpersonalgoals,
  queryReviewGoals,
  jiHuaInfo,
  updateSummarize,
} from '@/api/goal'

export default {
  components: {
    back,
    addzongcom,
    planreview,
    summaryreview,
    addmodelcom,
    systemDialogZong,
  },
  data() {
    return {
      titleStr: {
        mainTitle: '',
        subTitle: '',
      },
      pickerOptions: {
        disabledDate: this.endPickerTime,
      },
      activeType: '1',
      isShow: false,
      planTime: '',
      dialogPlanVisible: false,
      viewData: {},
      pageData: {},
      types: {
        1: {
          name: '年计划',
          label: '年度计划',
          type: 'year',
          format: 'yyyy',
        },
        2: {
          name: '月计划',
          label: '月度计划',
          type: 'month',
          format: 'yyyy-M',
        },
        3: {
          name: '周计划',
          label: '周计划',
          type: 'week',
          format: 'yyyy 第 W 周',
        },
        4: {
          name: '年总结',
          label: '年度总结',
          type: 'year',
          format: 'yyyy',
        },
        5: {
          name: '月总结',
          label: '月度总结',
          type: 'month',
          format: 'yyyy-M',
        },
        6: {
          name: '周总结',
          label: '周总结',
          type: 'week',
          format: 'yyyy 第 W 周',
        },
      },
      form: {
        createBy: sessionStorage.getItem('userid'),
        goalsType: this.$route.query.type,
        year: null,
        month: null,
        week: null,
        pageNum: 1,
        pageSize: 1,
      },
      reviewType: '',
      nowTime: '',
      isLoading: false,
      type: {
        1: '客户',
        2: '拜访与跟进',
        3: '机会',
        4: '合同',
      },
      list1: [
        {
          id: 1,
          name: '信息化业绩',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img5.png'),
          unit: '万元',
          color: '#F46D40',
        },
        {
          id: 2,
          name: '教材业绩',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img3.png'),
          unit: '万元',
          color: '#E85D5D',
        },
        {
          id: 3,
          name: '合同数量',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img/hetong_icon.png'),
          unit: '个',
          color: '#4A8BF6',
        },
        {
          id: 4,
          name: '新增拜访与跟进',
          goal: 0,
          finishGoal: 0,
          icon: require('../../../assets/img2.png'),
          unit: '次',
          color: '#4A8BF6',
        },
      ],
      formLabelAlign: {
        goalsType: this.$route.query.type,
        year: null,
        month: null,
        week: null,
        templateId: this.$route.query.templateId,
        templateItemList: [],
        departmentId: '',
        goalStatus: 0,
        draftId: null, // 添加草稿ID字段
      },
      unitGoalList: [],
      keyNames: {
        informationAmount: '信息化业绩（万元）',
        teachingMaterialAmount: '教材业绩金额（万元）',
        totalContract: '合同数量',
        totalVisit: '拜访次数',
      },
      keyDict: {
        1: 'newCustomers',
        2: 'newVisit',
        3: 'newOpportunity',
        4: 'newContract',
        5: 'contractAmount',
        6: 'contractReturnAmount',
      },
      peopleList: [],
      dialogSystemVisible: false,
    }
  },
  created() {
    if (this.$route.query.id) {
      this.formLabelAlign.draftId = this.$route.query.id // 设置草稿ID
      this.loadInfo()
    } else if (this.$route.query.draftId) {
      // 重新填写草稿，只设置草稿ID，不加载内容
      this.formLabelAlign.draftId = this.$route.query.draftId
      if (this.$route.query.departmentId) {
        this.formLabelAlign.departmentId = this.$route.query.departmentId
      }
      this.formLabelAlign.year = new Date(this.$route.query.time).getFullYear()
      this.formLabelAlign.goalsType = this.$route.query.type
      if (this.formLabelAlign.goalsType == 5) {
        this.formLabelAlign.month =
          new Date(this.$route.query.time).getMonth() + 1
      } else if (this.formLabelAlign.goalsType == 6) {
        var time = getWeek(this.$route.query.time)
        var times = time.split('-')
        this.formLabelAlign.week = parseInt(times[1])
      }
    } else {
      if (this.$route.query.departmentId) {
        this.formLabelAlign.departmentId = this.$route.query.departmentId
      }
      this.formLabelAlign.year = new Date(this.$route.query.time).getFullYear()
      this.formLabelAlign.goalsType = this.$route.query.type
      if (this.formLabelAlign.goalsType == 5) {
        this.formLabelAlign.month =
          new Date(this.$route.query.time).getMonth() + 1
      } else if (this.formLabelAlign.goalsType == 6) {
        var time = getWeek(this.$route.query.time)
        var times = time.split('-')
        this.formLabelAlign.week = parseInt(times[1])
      }
    }
  },
  methods: {
    getUnitGoalList(array) {
      this.unitGoalList = []
      this.unitIds = []
      var keys = [
        'informationAmount',
        'teachingMaterialAmount',
        'totalContract',
        'totalVisit',
      ]
      array.forEach((element, idx) => {
        var list = []
        this.unitIds.push(element.unitId)
        keys.forEach((key) => {
          var item = {
            id: element.id,
            index: idx,
            unitId: element.unitId,
            unitName: element.unitName,
            year: element.year,
            goalNum: element[key],
            unitgoalType: key,
            unitgoalTypeName: this.keyNames[key],
          }
          list.push(item)
        })
        this.unitGoalList.push(list)
      })
    },
    generateTitleStr() {
      const createByName = this.$route.query.createByName || ''
      const planTime = getPlanTime(this.formLabelAlign, this.form.goalsType)
      const planType = this.types[this.form.goalsType].name
      const timeRange = this.$route.query.timeRange || ''

      let mainTitle = `${createByName}${planTime}${planType}`
      let subTitle = timeRange

      return {
        mainTitle,
        subTitle,
      }
    },
    loadInfo() {
      jiHuaInfo({
        id: this.$route.query.id,
        type: 2,
      })
        .then((result) => {
          this.formLabelAlign = result.data
          // 保持草稿ID，用于重新编写时的更新操作
          this.formLabelAlign.draftId = this.$route.query.id
          this.formLabelAlign.goalsType = this.formLabelAlign.goalsType + ''
          this.getUnitGoalList(this.formLabelAlign.personalGoalsUnitList)
          this.titleStr = this.generateTitleStr()
          this.$refs.modelCom.setViewList(result.data.templateItemList)
          this.peopleList = result.data.copyPersons
        })
        .catch((err) => {})
    },
    loadOldData(data) {
      queryReviewGoals(data)
        .then((result) => {
          if (result.data && result.data.length > 0) {
            this.viewData = result.data
            this.pageData = result.page
            this.isShow = true
            this.activeType = '2'
            this.dialogPlanVisible = false
          } else {
            this.$message({
              type: 'error',
              message: '暂无相关回顾可查看',
            })
          }
        })
        .catch((err) => {})
    },
    endPickerTime(time) {
      if (this.reviewType == 'planreview') {
        return false
      }
      const today = new Date()
      return time > today
    },
    changeDatePicker(date) {
      var newDate = new Date(date)
      this.form.year = newDate.getFullYear()
      if (this.form.goalsType == 2 || this.form.goalsType == 5) {
        this.form.month = newDate.getMonth() + 1
      } else if (this.form.goalsType == 3 || this.form.goalsType == 6) {
        var time = getWeek(newDate)
        var times = time.split('-')
        this.form.year = Number(times[0])
        this.form.week = Number(times[1])
      }
    },
    handlePlanClose() {
      this.dialogPlanVisible = false
      this.planTime = ''
    },
    nextAction() {
      if (this.planTime) {
        if (this.reviewType == 'planreview') {
          var fdata = Object.assign({}, this.form)
          fdata.goalsType = +fdata.goalsType - 3
          this.loadOldData(fdata)
        } else {
          this.loadOldData(this.form)
        }
      } else {
        this.$message({
          type: 'info',
          message: `请选择${this.types[this.form.goalsType].label}`,
        })
      }
    },
    closereview() {
      this.isShow = false
      this.activeType = '1'
      this.reviewType = ''
    },
    getUnitGoalList(array) {
      this.unitGoalList = []
      this.unitIds = []
      var keys = [
        'informationAmount',
        'teachingMaterialAmount',
        'totalContract',
        'totalVisit',
      ]
      array.forEach((element, idx) => {
        var list = []
        this.unitIds.push(element.unitId)
        keys.forEach((key) => {
          var item = {
            id: element.id,
            index: idx,
            unitId: element.unitId,
            unitName: element.unitName,
            year: element.year,
            goalNum: element[key],
            unitgoalType: key,
            unitgoalTypeName: this.keyNames[key],
          }
          list.push(item)
        })
        this.unitGoalList.push(list)
      })
    },
    showView(e) {
      this.planTime = ''
      this.dialogPlanVisible = true
      this.reviewType = e.target.id
    },
    save() {
      this.isLoading = true
      addpersonalgoals(this.formLabelAlign)
        .then((result) => {
          this.isLoading = false
          if (result.data) {
            this.$message({
              type: 'success',
              message: this.formLabelAlign.goalStatus === 0 ? '草稿已保存' :'提交成功'
            })
            this.$router.go(-1)
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    update() {
      this.isLoading = true
      updateSummarize(this.formLabelAlign)
        .then((result) => {
          this.isLoading = false
          if (result.data) {
            this.$message({
              type: 'success',
              message: this.formLabelAlign.goalStatus === 0 ? '草稿已保存' :'修改成功',
            })
            this.$router.go(-1)
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    headdenForm() {
      let formData = this.$refs.modelCom.getViewData()
      this.formLabelAlign.templateItemList = formData
      this.formLabelAlign.copyPersons =
        this.peopleList.length > 0 ? this.peopleList.map((item) => item.id) : []

      if (this.$route.query.id) {
        delete this.formLabelAlign.createTime
        delete this.formLabelAlign.isChange
        this.update()
      } else {
        this.save()
      }
    },
    saveGoalAction(isDraft = false) {
      if (this.$route.query.id) {
      } else {
        if (this.$route.query.templateId) {
          let viewList = this.$refs.modelCom.getList()
          if (viewList.length == 0) {
            this.$message({
              type: 'error',
              message: '暂时没有模板,不能保存',
            })
            return
          }
        }
        if (this.$route.query.id) {
          if (this.formLabelAlign.templateItemList.length == 0) {
            this.$message({
              type: 'error',
              message: '暂时没有模板,不能保存',
            })
            return
          }
        }
      }

      // 设置目标状态
      if (isDraft) {
        this.formLabelAlign.goalStatus = 0  // 保存草稿
      } else {
        this.formLabelAlign.goalStatus = 1  // 正式保存
      }

      this.$refs.modelCom.submitForm()
    },
    closeTag(idtemId) {
      this.peopleList = removeById(this.peopleList, idtemId)
    },
    clickXuan() {
      this.$refs.systemdialog.loadData()
      this.peopleList
        ? this.$refs.systemdialog.updateWorksId(this.peopleList)
        : this.$refs.systemdialog.updateWorksId([])
      this.dialogSystemVisible = true
    },
    updateSystemVisible(val) {
      this.dialogSystemVisible = val
    },
    resArr(arr1, arr2) {
      const result = []
      const idSet = new Set()
      arr1.forEach((item) => {
        result.push(item)
        idSet.add(item.id)
      })
      arr2.forEach((item) => {
        if (!idSet.has(item.id)) {
          result.push(item)
        }
      })
      return result
    },
    submitData(data) {
      let newArr = this.resArr(this.peopleList, data)
      console.log(newArr, '---')
      if (this.$route.query.id) {
        this.peopleList = newArr
      } else {
        this.peopleList = data
      }
      this.dialogSystemVisible = false
    },
    back(){
      console.log("dsassssss");
      if (this.formLabelAlign.goalStatus == 1){
        this.$router.go(-1)
      }else{
        this.saveGoalAction(true)
      }
    },
  },
}
</script>
<style scoped>
.plantitle {
  height: 30px;
  width: 100%;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
  line-height: 23px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.pd10 {
  padding-top: 10px;
  color: #606266;
  vertical-align: middle;
  font-weight: 700;
  text-align: right;
  padding-right: 10px;
  width: 120px;
}
.pname {
  text-align: center;
}
.noimg {
  width: 48px;
  height: 48px;
  line-height: 48px;
  border-radius: 8px;
  background-color: #4285f4;
  text-align: center;
  color: #fff;
  font-size: 12px;
}
.uflex {
  display: flex;
  flex-wrap: wrap;
  width: calc(100% - 130px);
}
.closeimg {
  position: absolute;
  right: -8px;
  top: -8px;
  cursor: pointer;
}
.pli {
  margin-right: 24px;
  margin-bottom: 24px;
  position: relative;
}
.pimg {
  width: 48px;
  height: 48px;
  border-radius: 8px 8px 8px 8px;
}
.pflex {
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
}
.adddi {
  width: 44px;
  height: 44px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d6d6d6;
  text-align: center;
  line-height: 44px;
  font-size: 18px;
  color: #d6d6d6;
  cursor: pointer;
}
.submitmask {
  width: 100%;
  text-align: center;
  margin-top: 20px;
}
.maindiv {
  display: flex;
  padding-top: 20px;
}
.rightdiv {
  width: 20%;
  margin-left: 20px;
  border-radius: 10px;
}
.w40 {
  width: 40%;
  background-color: white;
}
.bbtn {
  height: 48px;
  line-height: 48px;
  background-color: #dfeafd;
  font-size: 16px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  letter-spacing: 1px;
  border-radius: 4px;
  cursor: pointer;
}
.bbtn:hover {
  background-color: #4285f4;
  color: white !important;
}
.bbtn + .bbtn {
  margin-top: 10px;
}
.owntop {
  width: 80%;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
}
.w60 {
  width: 60%;
}
.tright {
  text-align: center;
}
</style>
<style scoped>
.itemcss {
  width: 200px;
  margin-right: 20px;
}
.listcss {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}
.mainbg {
  height: 100%;
  overflow-y: auto;
  border-radius: 8px;
  margin: 16px 0px;
  padding: 20px 16px;
  display: flex;
}
.titlefl {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.w210 {
  width: 210px;
}
.width100 {
  width: 80%;
}
.textcss {
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
  margin: 15px 0px;
}
.progress /deep/.el-progress-bar__outer {
  height: 4px !important;
  background-color: #dfeafd;
}
.progress /deep/.el-progress-bar__inner {
  background-color: #5a98ff;
}
.progress /deep/.el-progress-bar {
  margin-right: 0px;
}
.inblock {
  display: inline-block;
}
.mt50 {
  margin-top: 50px;
}
.goalitem {
  height: 72px;
  line-height: 72px;
  margin-bottom: 20px;
}
.ml {
  margin-left: 3px;
}
.mt {
  margin-top: 5px;
}
.goalcss {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 16px;
}
.finishGoalcss {
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
  color: #f46d40;
  line-height: 28px;
}
.pl12 {
  padding-left: 12px;
}
.flex {
  display: flex;
  align-items: center;
}
.imgcss {
  width: 3em;
  height: 3em;
  margin-left: 1.5vw;
}
.typename {
  padding-top: 20px;
  margin-left: 20px;
  margin-bottom: 1.25em;
  font-size: 1em;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.dataitem {
  margin-top: 16px;
  height: 9.375em;
  background: #ffffff;
  box-shadow: 0px 2px 16px 0px rgba(15, 27, 50, 0.08);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}

.titlecss {
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  line-height: 23px;
}
.bg {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  background-color: white;
  border-radius: 8px;
}
</style>
