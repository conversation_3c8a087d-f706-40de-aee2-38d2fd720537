<template>
    <div>
        <back>机会详情</back>
        <div class="mainbg">
            <el-form class="infoform" ref="form" :model="form" label-width="120px">
                <textBorder>基本信息</textBorder>
                <div class="mb30 bbline">
                    <el-row :gutter="20" class="width100 mt10 pb5">
                        <el-col :span="8">
                            <el-form-item label="机会名称:" class="labeltext">
                                <span>{{form && form.opportunityName}}</span>
                            </el-form-item>
                            <el-form-item label="客户名称:" class="labeltext">
                                <span>{{form && form.customerName}}</span>
                            </el-form-item>
                            <el-form-item label="客户单位:" class="labeltext">
                                <span>{{form && form.unitName}}</span>
                            </el-form-item>
                            <el-form-item label="预计时间:" class="labeltext">
                                <span>{{ form && form.estimatedTime||'暂无'}}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="类型:" class="labeltext">
                                <span>{{form && form.type}}</span>
                            </el-form-item>
                            <el-form-item label="机会阶段:" class="labeltext">
                                <span :class="nameToCssColor[form.opportunityStage]">{{form && form.stage}}</span>
                            </el-form-item>
                            <el-form-item label="预计金额:" class="labeltext">
                                <span>¥{{form && form.estimatedAmount}}万元</span>
                                <!-- class="colorcss" -->
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div v-if="isTextbookType">
                    <textBorder>教材</textBorder>
                    <div class="mb30 bbline">
                        <el-table :data="form.distributionDetailList.length>0 ? form.distributionDetailList:[bookInfo]" style="width: 100%">
                            <el-table-column prop="materialName" label="教材名称" min-width="167px">
                                <template slot-scope="scope">
                                    {{(form.distributionDetailList.length>0 ? scope.row.name : form.materialName) || '暂无'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="specialtyName" label="用书专业" min-width="167px">
                                <template>
                                    {{ form.specialtyName || '暂无'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="useBookYear" label="用书时间" min-width="167px">
                                <template>
                                    {{form.useBookYear || '暂无'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="author" label="主编" min-width="167px">
                                <template slot-scope="scope">
                                    {{scope.row.author || '暂无'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="platformName" label="出版社" min-width="167px">
                                <template slot-scope="scope">
                                    {{ form.distributionDetailList.length>0 ? scope.row.platformName : bookInfo.isbn || '暂无'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="isbn" label="ISBN" min-width="167px">
                                <template slot-scope="scope">
                                    {{form.distributionDetailList.length>0 ? scope.row.isbn : bookInfo.isbn || '暂无'}}
                                </template>
                            </el-table-column>
                            
                            <!-- <el-table-column prop="operationName" label="业务经理" min-width="167px">
                                <template>
                                    {{scope.row.operationName || '暂无'}}
                                </template>
                            </el-table-column> -->
                            
                            <el-table-column prop="price" label="价格" fixed="right" align="center" min-width="100px">
                                <template slot-scope="scope">
                                    {{scope.row.price || '暂无'}}
                                </template>
                            </el-table-column>
                            <el-table-column prop="useBookNumber" label="用书数量" align="center" fixed="right" min-width="100px">
                                <template slot-scope="scope">
                                    {{form.distributionDetailList.length>0 ? scope.row.number :form.useBookNumber || '暂无'}}
                                </template>
                            </el-table-column>
                            <el-table-column label="总码洋" width="150px" fixed="right" align="center" min-width="100px">
                                <template slot-scope="scope">
                                    <span>{{scope.row.number && (scope.row.price * scope.row.number).toFixed(2)}}</span>
                                </template>
                            </el-table-column>
                            
                        </el-table>
                    </div>
                </div>
                <textBorder>负责与协作</textBorder>
                <div class=" mb30 bbline">
                    <el-row :gutter="20" class="width100 mt10">
                        <el-col :span="8" class="mb10">
                            <el-form-item label="负责人:" class="labeltext">
                                <span>{{form && form.chargePersonName}}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" class="mb10">
                            <el-form-item label="协作人:" class="labeltext">
                                <span>{{form && form.collaboratorName || '暂无' }}</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <textBorder>补充信息</textBorder>
                <div class="mb30 bbline">
                    <el-row :gutter="20" class="mt10 pb15 ">
                        <el-col :span="24">
                            <el-form-item label="备注:" class="labeltext">
                                <span>{{form && form.notes || '暂无'}}</span>
                            </el-form-item>
                            <el-form-item label="附件:" class="labeltext">
                                <pdfview class="mt10" type="1" :pdfArr="form.fileInfoList"></pdfview>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <textBorder>系统信息</textBorder>
                <el-row :gutter="20" class="mt10 pb15 ">
                    <el-col :span="8">
                        <el-form-item label="创建人:" class="labeltext">
                            <span>{{form && form.createByName}}</span>
                        </el-form-item>
                        <el-form-item label="最后修改时间:" class="labeltext">
                            <span>{{form && form.modifyTime}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="最后修改人:" class="labeltext">
                            <span>{{ form && form.modifyByName}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="创建时间:" class="labeltext">
                            <span>{{ form && form.createTime}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

    </div>
</template>

<script>
    import textBorder from '../../common/textBorder.vue'
    import back from '../../common/back.vue';
    import pdfview from '@/components/common/pdfview.vue'
    import { opportunityInfo } from "@/api/clientMaintenance/opportunity";
    import { queryBookInfo } from '@/api/product/index';
    import { nameToCssColor } from "@/utils/dict";
    export default {
        components: {
            back,
            textBorder,
            pdfview
        },
        data() {
            return {
                colors: [
                    {
                        color: '#FD7A41',
                        background: '#FFECE3'
                    },
                    {
                        color: '#4285F4',
                        background: '#DFEAFD'
                    },
                    {
                        color: '#AC6FFB',
                        background: '#F0E6FE'
                    },
                    {
                        color: '#FFA44C',
                        background: '#FFF2E5'
                    },
                ],
                nameToCssColor: nameToCssColor,
                form: {
                    distributionDetailList:[]
                },
                bookInfo: {},
            }
        },
        created() {
            this.loadInfo();
        },
        computed: {
            isTextbookType() {
                return this.form.type && this.form.type.includes('教材');
            }
        },
        methods: {
            loadInfo() {
                opportunityInfo(this.$route.query.id).then((result) => {
                    this.form = result.data;
                    if (this.form.materialId) {
                        this.lookBookInfo();
                    }
                }).catch((err) => {

                });
            },
            lookBookInfo() {
                queryBookInfo(this.form.materialId).then((result) => {
                    this.bookInfo = result.data;
                }).catch((err) => {

                });
            },
        }

    }
</script>

<style lang="scss" scoped>
    .pdfspan {
        margin-left: -18px !important;
    }

    /deep/ .guancontent {
        margin-left: -18px !important;
        margin: 0;
        // margin-top: 10px;
    }

    .pb12 {
        padding-bottom: 12px;
    }

    .pb15 {
        padding-bottom: 15px;
    }

    .filebg {
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        padding: 4px 10px;
        color: #4285F4;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px dashed #4285F4;
        cursor: pointer;
    }

    .mainbg {
        min-height: calc(100vh - 150px);
        margin: 16px 0px;
        padding: 20px;
        padding-bottom: 0px;
        background-color: white;
        border-radius: 8px;
    }

    .colorcss {
        color: #F45961;
    }

    .tagitemcss {
        font-size: 12px;
        padding: 4px 8px;
        margin: 0px 4px;
        background-color: #DFEAFD;
        border-radius: 2px;
        color: #4285F4;
    }

    .mtop20 {
        margin-top: 20px;
    }

    .ml40 {
        margin-left: 40px;
    }

    .color1 {
        color: #4285F4;
    }

    .color2 {
        color: #489DD2 !important;
    }

    .color3 {
        color: #4CAFB9 !important;
    }

    .color4 {
        color: #52BD97 !important;
    }

    .color5 {
        color: #56C36E !important;
    }

    .color6 {
        color: #7693C1 !important;
    }
</style>
<style scoped>
    .infoform /deep/.el-form-item {
        margin-bottom: 0px;
    }

    .mulitline /deep/.el-form-item__content {
        padding-top: 10px;
        line-height: 20px;
    }
</style>