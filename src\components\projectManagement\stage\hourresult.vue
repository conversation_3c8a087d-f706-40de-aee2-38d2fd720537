<template>
  <div>
    <slot :row="dataobj"></slot>
    <el-table  class="customertable mytable"  :data="tableData" style="width: 100%" v-loading="isLoading">
      <el-table-column
        prop="createByName"
        label="提交人"
        >
      </el-table-column>
      <el-table-column
        prop="taskDepartmentName"
        label="部门"
        >
      </el-table-column>
      <el-table-column
        prop="workHours"
        label="工时"
        >
      </el-table-column>
      <el-table-column
      width="120"
        prop="duration"
        label="成果时长(分钟)"
        >
      </el-table-column>
      <el-table-column
        width="120"
        show-overflow-tooltip
        prop="taskName"
        label="任务"
        >
      </el-table-column>
      <el-table-column
        width="160"
        prop="createTime"
        label="业务时间"
        >
      </el-table-column>
      <el-table-column
        width="60"
        prop="edit"
        label="操作">
        <template slot-scope="scope">
          <el-button :disabled="!scope.row.isOperate" class="rbtn "  type="text" @click="deleteAction(scope.row)"> 删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"></page>
    <dc-dialog :iType="dialogType" title="温馨提示" width="500px" :showCancel="isShowCancel" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
              <p class="pcc">确定要删除么?</p>
    </dc-dialog>
  </div>
</template>

<script>
  import {taskworkhoursList,queryTaskWorkHoursStatistics,hourDelete} from '@/api/stage/index'
  import page from '@/components/common/page.vue';
  export default {
    props: {
      taskId:{
        type: String,
        default: '',
      }
    },
       data(){
        return {
          dataobj:{
            accumulatedWorkHours:'0',
            accumulatedDuration:'0'
          },
          isLoading:false,
          tableData:[],
          dialogType:1,
              isShowCancel:true,
              dialogVisible:false,
              itemId:'',
              pageBean: {
                pageNum: 1,
                pageSize: 10,
            },
            stType:'',
            total:0,
      }
    },
    components:{
      page
    },
    methods:{
      handleCurrentChange(page){
            this.pageBean.pageNum = page;
            this.hourListData(this.stType,this.taskId)
        },
      deleteAction(row){
        this.itemId = row.id
        this.dialogVisible = true
    },
    submitDialog(){
            let params = {
                id:this.itemId
            }
            hourDelete(params).then(res=>{
                    if(res.status == 0){
                      this.msgSuccess('删除成功')
                      this.hourListApi(this.params)
                      this.queryTaskCostData(this.params)
                      this.dialogVisible = false
                    }
            })
          },
    hourListData(type,Id){
      this.stType = type
      this.params = type ==1?{
        projectStageId:Id
      }: {
        taskId:Id
      }
      this.params = {...this.params,...this.pageBean}
      this.hourListApi(this.params)
      this.queryTaskCostData(this.params)
    },
    hourListApi(params){
      taskworkhoursList(params).then(res=>{
            if(res.status == 0){
                this.tableData  = res.data
                this.total = res.page.total
            }
      })
    },
    queryTaskCostData(params){
      queryTaskWorkHoursStatistics(params).then(res=>{
            if(res.status == 0){
                this.dataobj  = res.data
            }
      })
    }
    }
  }
</script>

<style lang="scss" scoped>
.ml32{
  margin-left: 32px;
}
.cbtext{
  margin-bottom: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.yuan{
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
</style>