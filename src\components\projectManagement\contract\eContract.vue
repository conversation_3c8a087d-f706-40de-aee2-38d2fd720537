<template>
  <div class="pdfbg">
    <div class="bdiv" v-isShow="'download'">
      <el-button type="primary" @click="downLoad" v-if="eContractUrl"
        >下载电子合同</el-button
      >
    </div>
    <iframe
      :style="{ height: `${height + 100}px`, width: '100%' }"
      v-if="eContractUrl"
      :src="`https://wthedu.51suiyixue.com/PDF/web/viewer.html?file=${eContractUrl}`"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

import { fileLinkToStreamDownload, urlFile } from '@/utils/tools'

export default {
  computed: {
    ...mapGetters(['eContractUrl']),
  },
  data() {
    return {
      height: 0,
    }
  },
  created() {
    var width = window.screen.availWidth
    this.height = (width - 250) / 0.75
  },
  methods: {
    downLoad() {
      let fileName = urlFile(this.eContractUrl, 1)
      let fileExtension = urlFile(this.eContractUrl, 2)
      fileLinkToStreamDownload(this.eContractUrl, fileName, fileExtension)
    },
  },
}
</script>

<style scoped>
.bdiv {
  text-align: right;
  margin-bottom: 12px;
}
.pdfbg {
  text-align: center;
}
</style>