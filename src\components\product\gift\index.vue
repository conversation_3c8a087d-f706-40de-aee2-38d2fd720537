<template>
    <div class="mainbg fixpb">
        <el-form :inline="true" label-width="85px" class="myform ">
            <el-row  type="flex" justify="space-between">
                <el-col :span="18">
                    <el-form-item label="礼品名称：">
                        <el-input class="definput" placeholder="请输入礼品名称" v-model="pageBean.name" clearable>
                        </el-input>
                    </el-form-item>
                    <el-button class="defaultbtn mt" icon="el-icon-search" type="primary" @click="searchAction">搜索
                    </el-button>
                </el-col>
                <el-col :span="6" class="flexd">
                    <!-- <el-button  class="defaultbtn mt" icon="el-icon-upload2"
                        type="primary" @click="exportInfo">导入</el-button> -->
                  <el-button  class="defaultbtn mt" icon="el-icon-plus"
                        type="primary" @click="addInfo">新增</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-table class="mytable" :data="tableData" style="width: 100%;" v-loading="isLoading">
            <el-table-column prop="name" label="礼品名称" align="center">
            </el-table-column>
            <el-table-column prop="price" label="价格" align="center" >
            </el-table-column>
            <el-table-column prop="giftUnit" label="单位" align="center">
            </el-table-column>
            <el-table-column prop="edit" width="140" align="center" label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-button v-isShow="'crm:controller:workorder:update'" class="bbtn "
                        type="text" @click="onEdit(scope.row)"> 编辑</el-button>
                    <el-button v-isShow="'crm:controller:workorder:delete'" class="rbtn " type="text"
                        @click="deleteAction(scope.row)"> 删除</el-button>
                </template>
            </el-table-column>
            <template slot="empty">
                <nolist></nolist>
            </template>
        </el-table>
     


            <div class="fixpage">
                <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                @updatePageNum="handleCurrentChange"></page>
            </div>

              <el-dialog
                    :title="dialogTitle"
                    :visible.sync="dialogFormVisible"
                    width="500px"
                    center
                    
                    :before-close="handleClose">
                    <el-form ref="addform" label-width="100px" :model="form" :rules="rules" class="addform">
                        <el-form-item prop="name" label="礼品名称：" >
                            <el-input  v-model="form.name" maxlength="30" show-word-limit placeholder="请输入礼品名称"></el-input>
                        </el-form-item>
                        <el-form-item prop="price" label="价格：" label-width="100px">
                            <el-input type="number" @input="formatNum(form.price, 'price')" class="w100"  v-model="form.price"  placeholder="请输入价格（单位：元）"></el-input>
                        </el-form-item>
                        <el-form-item prop="giftUnit" label="单位：" label-width="100px">
                            <el-input  v-model="form.giftUnit" maxlength="10" placeholder="请输入礼品的计数单位"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                    <el-button @click="handleClose()">取 消</el-button>
                    <el-button type="primary" @click="submitAction()">确 定</el-button>
                    </span>
                </el-dialog>
                <dc-dialog iType="1" title="温馨提示" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
                    <template>
                    </template>
                    <p class="pcc">是否删除该礼品？</p>
                </dc-dialog>
    </div>
</template>

<script>
    import page from '../../common/page.vue';
    import nolist from '../../common/nolist.vue';
    import { giftList,addGift,updateGift,deleteGift,giftinfo } from "@/api/product/index";
    export default {
        components: {
            page,
            nolist,
        },
        data() {
            return {
                deleteData: {},
                detailVisible: false,
                dialogVisible: false,
                dialogFormVisible:false,
                dialogTitle:'',
                isLoading: false,
                tableData: [],
                total: 0,
                form:{
                    name:"",
                    price:'',
                    giftUnit:''
                },
                rules: {
                    name: [
                        { required: true, message: '请输入礼品名称', trigger: 'blur' },
                        { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' }
                    ],
                    price: [
                        { required: true, message: '请输入价格', trigger: 'blur' }
                    ],
                    giftUnit: [
                        { required: true, message: '请输入单位', trigger: 'blur' }
                    ]
                },
                pageBean: {
                    name: "",
                    pageNum: 1,
                    pageSize: 10,
                },
            }
        },
        created() {
            this.loadData();
        },
        methods: {
            formatNum(val, key) {
                let temp = val.toString();
                temp = temp.replace(/。/g, ".");
                temp = temp.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
                temp = temp.replace(/^\./g, ""); //验证第一个字符是数字
                temp = temp.replace(/\.{2,}/g, ""); //只保留第一个, 清除多余的
                temp = temp.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
                temp = temp.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数
                this.form[key] = temp;
            },
            loadData() {
                this.isLoading = true;
                giftList(this.pageBean).then((result) => {
                    this.tableData = result.data;
                    this.total = result.page.total;
                    this.isLoading = false;

                }).catch((err) => {
                    this.isLoading = false;
                });
            },
            searchAction() {
                this.loadData();
            },
            submitDialog() {
                this.isLoading = true;
                deleteGift({ id: this.deleteData.id }).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '删除成功'
                        })
                        this.loadData();
                    } else {
                        this.$message({
                            type: "error",
                            message: result.msg
                        })
                        this.isLoading = false;
                    }
                }).catch((err) => {
                    this.isLoading = false;
                });
                this.dialogVisible = false;
            },
            addInfo() {
               this.dialogFormVisible = true;
               this.dialogTitle = "新增礼品";
            },
            deleteAction(data) {
                this.deleteData = data;
                this.dialogVisible = true;
            },
            onEdit(data) {
                this.dialogFormVisible = true;
                this.dialogTitle = "编辑礼品";
                this.loadinfo(data.id);
            },
            loadinfo(id){
                giftinfo(id).then((result) => {
                    this.form = result.data;
                }).catch((err) => {
                    
                });
            },
            handleClose(){
                this.dialogFormVisible = false;
                Object.keys(this.form).forEach((item)=>{
                    if (item == 'price') {
                        this.form.price = '';
                    }else{
                        this.form[item] = ''
                    }
                    
                })
                
                this.$refs.addform.clearValidate();
            },
            submitAction(){
                this.$refs['addform'].validate((valid) => {
                    if (valid) {
                        if(this.form.price.length>9){
                                this.$message({
                                    type: "error",
                                    message: '价格长度超出限制'
                                })
                                return
                        }
                        if (this.form.id){
                            this.updateData()
                        }else{
                            this.add();
                        }

                    } else {
                        return false;
                    }
                });
            },
            add(){
                this.isLoading = true;
                addGift(this.form).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '添加成功成功'
                        })
                        this.handleClose();
                        this.loadData()
                    } else {
                        this.$message({
                            type: 'error',
                            message: result.msg
                        })
                        this.isLoading = false
                    }
                }).catch((err) => {
                    this.isLoading = false;
                });
            },
            updateData() {
                this.isLoading = true;
                updateGift(this.form).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '更新成功'
                        })
                        this.handleClose();
                        this.loadData()
                    } else {
                        this.$message({
                            type: 'error',
                            message: result.msg
                        })
                        this.isLoading = false
                    }
                }).catch((err) => {
                    this.isLoading = false;
                });
            },
            handleCurrentChange(page) {
                this.pageBean.pageNum = page;
                this.loadData();
            }
        }
    }
</script>

<style scoped>
    .w100 /deep/ .el-input__inner{
        text-align: left;
    }
    .w100{

    }
    .flexd{
            text-align: right;
    }
    .definput{
        width: 200px;
    }
    .mainbg {
        padding: 20px;
        background-color: white;
        min-height:calc(100vh - 106px);
    }
    .pcc {
        margin: 0 auto;
        text-align: center;
    }
    .smtext {
        zoom: 0.8;
    }
    .fxcenter {
        width: 50px;
        height: 52px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .zhiding {
        width: 16px;
        height: 16px;
        margin-right: 4px;
    }
    .mt {
        margin-top: 4px;
    }
    .cusnamecss {
        display: flex;
    }
    .tagcss {
        font-size: .625em !important;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        height: 14px;
        min-width: 30px;
        line-height: 11px;
        border-radius: 2px;
        margin-right: 20px;
    }
    .genjin {
        color: #4285F4;
        background-color: #DFEAFD;
    }
    .fuze {
        color: #FEF2E7;
        background-color: #FF8D1A;
    }
    .xiezuo {
        color: #56C36E;
        background-color: #F3FEF6;
    }
    .tagcss:nth-child(2n+2) {
        margin-top: 4px;
    }
    .ltext {
        color: #4285F4;
        text-align: justify !important;
    }
    .mytable /deep/ .cell {
        height: auto !important;
        min-height: 52px !important;
        padding: 13px !important;
        line-height: 25px !important;
    }
    .mytable .el-button {
        padding: 2px;
    }
    .mr10 {
        margin-right: 10px;
    }
    .bbtn,
    .bbtn:hover,
    .bbtn:focus {
        color: #4285F4;
    }

    .rbtn,
    .rbtn:hover,
    .rbtn:focus {
        color: #F45961;
    }
    .right {
        text-align: right;
    }
</style>