<template>
  <div v-loading="isLoading">
    <el-form class="infoform" ref="form" :model="form" label-width="140px">
      <textBorder>基本信息</textBorder>
      <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
        <el-col :span="8">
          <el-form-item label="合同标题：" class="labeltext">
            <span>{{ form.contractTitle }}</span>
          </el-form-item>
          <el-form-item label="客户单位：" class="labeltext">
            <span>{{ form.unitName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="合同编号：" class="labeltext">
            <span>{{ form.contractNumber }}</span>
          </el-form-item>
          <el-form-item label="关联机会：" class="labeltext">
            <span class="column_blue">{{
              form.opportunityName || '暂无'
            }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户姓名：" class="labeltext">
            <span>{{ form.customerName }}</span>
          </el-form-item>
          <el-form-item label="到期时间：" class="labeltext">
            <span>{{ form.expireTime || '暂无' }}</span>
          </el-form-item>
        </el-col>
        <el-row>
          <el-col :span="24">
            <el-form-item label="合同类型：" class="labeltext">
              <span>{{ form.contractDictType }}</span>
              --
              <span>{{ form.contractDictItem }}</span>
            </el-form-item></el-col
          >
        </el-row>
      </el-row>

      <textBorder>合同信息</textBorder>

      <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
        <el-col :span="8">
          <el-form-item label="合同金额(万元)：" class="labeltext">
            <span>{{
              form.contractAmount == 0 ? '暂无' : form.contractAmount + '万元'
            }}</span>
          </el-form-item>
          <el-form-item label="已回款金额(万元)：" class="labeltext">
            <span>{{ form.returnTotalAmount || '暂无' }}</span>
          </el-form-item>
          <el-form-item label="开票状态：" class="labeltext">
            <span>{{ invoicToName[form.invoicingStatus] }}</span>
          </el-form-item>
          <el-form-item label="收货人：" class="labeltext">
            <span>{{ form.receiver || '暂无' }}</span>
          </el-form-item>
          <el-form-item label="开始时间：" class="labeltext">
            <span>{{ form.beginTime || '暂无' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="折扣：" class="labeltext">
            <span>{{ form.discount || '暂无' }}</span>
          </el-form-item>
          <el-form-item label="退款状态：" class="labeltext">
            <span>{{ refundStatus[form.refundStatus] }}</span>
          </el-form-item>
          <el-form-item label="已开票金额(万元)：" class="labeltext">
            <span>{{ form.invoicingTotalAmount || '暂无' }}</span>
          </el-form-item>
          <el-form-item label="收货人地址：" class="labeltext lh">
            <span>{{ form.deliveryAddress || '暂无' }}</span>
          </el-form-item>
          <el-form-item label="结束时间：" class="labeltext">
            <span>{{ form.endTime || '暂无' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="回款状态：" class="labeltext">
            <span>{{ returnToName[form.returnStatus] }}</span>
          </el-form-item>
          <el-form-item label="退款金额(万元)：" class="labeltext">
            <span>{{ form.refundTotalAmount || '暂无' }}</span>
          </el-form-item>
          <el-form-item label="发货状态：" class="labeltext">
            <span>{{ invoiceStaName[form.invoiceStatus] }}</span>
          </el-form-item>
          <el-form-item label="仓库：" class="labeltext">
            <span>{{ form.warehouseName || '暂无' }}</span>
          </el-form-item>
          <el-form-item label="质保金年限：" class="labeltext">
            <span>{{
              form.warrantyPeriod ? form.warrantyPeriod + '年' : '暂无'
            }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <textBorder>系统信息</textBorder>
      <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
        <el-col :span="8">
          <el-form-item label="创建人：" class="labeltext">
            <span>{{ form.createByName }}</span>
          </el-form-item>
          <el-form-item label="最后修改时间：" class="labeltext">
            <span>{{ form.modifyTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最后修改人：" class="labeltext">
            <span>{{ form.modifyByName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建时间：" class="labeltext">
            <span>{{ form.createTime }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import textBorder from '../../common/textBorder.vue'
import { contractInfo } from '@/api/contract'
export default {
  data() {
    return {
      form: {
        id: '',
        contractTitle: '',
        contractNumber: '',
        customerId: '',
        customerName: '',
        unitName: '',
        expireTime: '',
        contractType: '',
        contractTypeSub: '',
        contractAmount: '',
        discount: '',
        receiver: '',
        deliveryAddress: '',
        warehouseId: '',
        beginTime: '',
        endTime: '',
        warrantyPeriod: '',
        chargePerson: '',
        collaborator: '',
        notes: '',
        fileInfoList: [],
      },
      isLoading: false,
      invoiceStaName: {
        0: '未发货',
        1: '已发货',
      },
      returnToName: {
        1: '未回款',
        2: '部分回款',
        3: '全部回款',
        4: '超额回款',
      },
      refundStatus: {
        1: '未退款',
        2: '部分退款',
        3: '全部退款',
        4: '超额退款',
      },
      invoicToName: {
        1: '未开票',
        2: '部分开票',
        3: '全部开票',
        4: '超额开票',
      },
    }
  },
  components: {
    textBorder,
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.isLoading = true
      contractInfo(this.$route.query.id)
        .then((result) => {
          this.form = result.data
          this.$store.commit('cus/CHANGE_KEY', {
            key: 'isShow',
            value: result.data.isShow == 0 ? false : true,
          })
          this.isLoading = false
          var fileData =
            result.data.fileInfoList.length > 0
              ? result.data.fileInfoList[0]
              : {}
          this.$store.commit('cus/CHANGE_KEY', {
            key: 'eContractUrl',
            value: fileData.url,
          })
          this.$store.commit('cus/CHANGE_KEY', {
            key: 'contractAmount',
            value: result.data.contractAmount,
          })
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.pb12 {
  padding-bottom: 12px;
}
</style>
<style scoped>
.lh /deep/.el-form-item__content {
  line-height: 18px !important;
  padding: 0;
  padding-top: 12px;
}
.infoform /deep/.el-form-item {
  margin-bottom: 0px;
}
</style>
