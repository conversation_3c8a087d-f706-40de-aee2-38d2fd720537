<template>
  <el-dialog width="500px" :visible="dialogVisible" title="工单转任务" @close="close">
    <el-form ref="myform" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="任务名称：" prop="taskName">
            <el-input maxlength="50" show-word-limit clearable="" v-model="form.taskName"  class="definput"
                placeholder="请输入任务名称"></el-input>
        </el-form-item>
        <el-form-item prop="beginTime" label="开始时间：">
            <el-date-picker value-format="yyyy-MM-dd" class="definput" v-model="form.beginTime"
                type="date" placeholder="选择日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime" label="结束时间：">
            <el-date-picker value-format="yyyy-MM-dd" class="definput" v-model="form.endTime"
                type="date" placeholder="选择日期">
            </el-date-picker>
        </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="submitAction">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { changeToTask } from '@/api/workorder/index';
export default {
    data(){
        return{
            dialogVisible:false,
            form:{
                workOrderId:'',
                beginTime:'',
                endTime:'',
                taskName:""
            },
            rules:{
                beginTime: [
                    { required: true, message: '请选择开始时间', trigger: 'change' }
                ],
                endTime: [
                    { required: true, message: '请选择结束时间', trigger: 'change' }
                ],
                taskName: [
                    { required: true, message: '请填写任务名称', trigger: 'blur' }
                ]
            },
            isSubmit:false,
        }
    },
    methods:{
        show(workId){
            this.dialogVisible = true;
            this.form.workOrderId = workId;
        },
        close(){
            Object.keys(this.form).forEach(key =>{
                this.form[key] = ''
            })
            this.$refs.myform.clearValidate();
            this.dialogVisible = false;
        },
        change(){
            this.isSubmit = true;
            changeToTask(this.form).then((result) => {
                this.isSubmit = false;
                if (result.data) {
                    this.$message({
                        type:'success',
                        message:result.msg
                    })
                    this.close()
                    this.$emit('doneAction')
                }else{
                    this.$message({
                        type:'error',
                        message:result.msg
                    })
                }
            }).catch((err) => {
                this.isSubmit = false;
            });
        },
        submitAction(){
            this.$refs.myform.validate((valid) => {
                if (valid) {
                    this.change()
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.el-date-editor.el-input{
    width: 100% !important;
}
</style>