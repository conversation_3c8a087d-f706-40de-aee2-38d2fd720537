<template>
    <el-dialog 
    class="ml_sign" 
    
    title="电子签名"
    :visible.sync="dialogVisible"
    width="500px"
    center
    :before-close="handleClose"
    >
    <vue-esign id='canvas' ref="esign" :width='1000' :height="500" style="border: 1px dashed #c2c1c1;" :isCrop="isCrop" :lineWidth="lineWidth" :lineColor="lineColor" :bgColor.sync="bgColor" />
    <div class="bottomdiv">
        <el-button type="primary" v-if="hasLastSign" @click="save" size="small">使用上次签名</el-button>
        <div class="right">
          <el-button style="margin-right: 20px;" @click="handleReset" size="small">清空</el-button>
          <el-button type="primary" @click="handleGenerate" v-dbClick size="small">保存</el-button>
        </div>
        
    </div>
    </el-dialog>
  </template>
  
  <script>
import vueEsign from 'vue-esign'
import {uploadFile } from '@/api/wapi';
import { base64toFile } from '@/utils/index';
  export default {
    components:{
      vueEsign
    },

    props:{
      dialogVisible:{
        type:Boolean,
        default:false
      },
      hasLastSign:{
        type:Boolean,
        default:false,
      }
    },
    name: 'esign',
    data () {
      return {
        lineWidth: 6,
        lineColor: '#000000',
        bgColor: '',
        isCrop: false,
      }
    },
    computed: {
    },
    mounted () {
      
    },
    created(){
    
    },
    methods: {
      handleClose(){
        this.$refs.esign.reset()
        this.$emit('updateVisible',false)
      },
      handleReset () {
        this.$refs.esign.reset()
      },
      save(){
        // 使用上次签名
        this.$emit('submitAction')
      },
      handleGenerate () {
        this.$refs.esign.generate().then(res => {
          const file =  base64toFile(res,'电子签名.png')
          if (file) {
            let formData = new FormData();
            formData.append("file",file);
            formData.append("serviceName",'esign')
            formData.append("applicationId",sessionStorage.getItem('applicationId'))
            uploadFile(formData).then((result) => {
              console.log("result==>",result);
              if (result.data && result.data.url) {
                this.$emit('submitAction',result.data)
              }else{
                this.$message({
                  type:"error",
                  message:"签名上传失败"
                })
              }
              
            }).catch((err) => {
              
            });
          }else{
            this.$message({
              type:'success',
              message:"签名生成错误"
            })
          }
          
          
        }).catch(err => {
          alert(err) // 画布没有签字时会执行这里 'Not Signned'
        })
      }

    }
  }
  </script>

<style scoped>
  /* #signature{
    width: 400px;
    height: 300px;
  } */
  .bottomdiv{
    margin-top: 10px;
    line-height: 50px;
    position: relative;
  }
</style>