<template>
    <el-drawer
    :append-to-body="true"
    center
    close-on-press-escape
    class="mydrawer"
    title="发货单"
    size="50%"
    :before-close="beforeClose"
    :visible.sync="drawer"
    direction="rtl">
    <div class="concss">
        <el-tabs v-model="activeName">
            <el-tab-pane label="发货单详情" name="first">
                 <el-form class="infoform" ref="form" label-width="120px">
                    <el-table class="mytable" v-if="form.distributionDetailList.length>0" :data="form.distributionDetailList">
                        <el-table-column label="教材名称" min-width="167px" prop="name"></el-table-column>
                        <el-table-column label="用书专业" min-width="167px" prop="specialtyName"></el-table-column>
                        <el-table-column label="isbn" min-width="167px" prop="isbn"></el-table-column>
                        <el-table-column label="出版社" min-width="167px" prop="platformName"></el-table-column>
                        <el-table-column label="出版时间" min-width="167px" prop="publicationRevisionTime"></el-table-column>
                        <el-table-column label="主编" min-width="167px" prop="author"></el-table-column>
                        <el-table-column label="价格" width="100px" prop="price" align="center" fixed="right"></el-table-column>
                        <el-table-column label="发货数量" width="100px" prop="number" align="center" fixed="right"></el-table-column>
                        <el-table-column label="总码洋" width="100px" prop="all" align="center" fixed="right">
                            <template slot-scope="scope">
                                <span>{{
                                    scope.row.number &&
                                    (scope.row.price * scope.row.number).toFixed(2)
                                }}</span>
                            </template>
                        </el-table-column>
                    </el-table> 
                    <el-row v-else :gutter="0" class="width100 mt10 pb12 mb30 bbline" type="flex" justify="center">
                        <el-col :span="11">
                            <el-form-item label="教材名称：" class="labeltext lh">
                                <span>{{form.materialName}}</span>
                            </el-form-item>
                            <el-form-item label="ISBN：" class="labeltext">
                                <span>{{form.isbn}}</span>
                            </el-form-item>
                        </el-col>
                        <el-col :span="11">  
                            <el-form-item label="出版社：" class="labeltext">
                                <span>{{form.platformName}}</span>
                            </el-form-item>
                            <el-form-item label="价格：" class="labeltext">
                                <span>{{form.price}}元</span>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline"  type="flex" justify="center">
                        <el-col :span="11">
                        <el-form-item label="发货量：" v-if="form.distributionDetailList.length<=0" class="labeltext">
                            <span>{{form.sendNumber}}</span>
                        </el-form-item>
                        <el-form-item label="发货负责人：" class="labeltext">
                            <span>{{form.chargeName}}</span>
                        </el-form-item>
                        <el-form-item v-if="form.distributionDetailList.length<=0" label="用书专业：" class="labeltext lh">
                            <span>{{form.specialtyName}}</span>
                        </el-form-item>
                        <el-form-item label="物流单：" class="labeltext">
                            <span>{{form.trackingNumber}}</span>
                        </el-form-item>
                        </el-col>
                        <el-col :span="11">  
                        <el-form-item label="折扣：" class="labeltext">
                            <span>{{form.discount}}折</span>
                        </el-form-item>
                        <el-form-item label="用书时间：" class="labeltext">
                            <span>{{form.useBookYear}}</span>
                        </el-form-item>
                        <el-form-item label="发货时间：" class="labeltext">
                            <span>{{form.deliveryTime}}</span>
                        </el-form-item>
                        
                        </el-col>
                    </el-row>
                    <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline"  type="flex" justify="center">
                        <el-col :span="11">
                        <el-form-item label="用书单位：" class="labeltext">
                            <span>{{form.unitName}}</span>
                        </el-form-item>
                        <el-form-item label="确认人：" class="labeltext">
                            <span>{{form.confirmName}}</span>
                        </el-form-item>
                        </el-col>
                        <el-col :span="11">  
                        <el-form-item label="收货人：" class="labeltext">
                            <span>{{form.receiveName}}</span>
                        </el-form-item>
                        <el-form-item label="确认状态：" class="labeltext">
                            <span>{{isConfirms[form.isConfirm]}}</span>
                        </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline "  type="flex" justify="center">
                        <el-col :span="22">
                        <el-form-item label="备注：" class="labeltext lh">
                            <span>{{form.notes}}</span>
                        </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="0" class="width100 pb12 mb30 "  type="flex" justify="center">
                        <el-col :span="11">
                        <el-form-item label="操作人：" class="labeltext">
                            <span>{{form.createName}}</span>
                        </el-form-item>
                        </el-col>
                        <el-col :span="11">  
                        <el-form-item label="业务时间：" class="labeltext">
                            <span>{{form.createTime}}</span>
                        </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="发货跟踪" name="second">
                <el-timeline>
                    <el-timeline-item v-for="item in this.form.deliverTrackVoList" :key="item.id" :timestamp="item.createTime" placement="top">
                        <h4>{{item.createName}}</h4>
                        <el-card class="mt10">
                            <p>{{item.notes}}</p>
                        </el-card>
                    </el-timeline-item>
                </el-timeline>
            </el-tab-pane>
        </el-tabs>
    </div>
    </el-drawer>
</template>

<script>
import { deliverInfo  } from "@/api/materialdeliver/index";
export default {
    props:{
        visible:{
            type:Boolean,
            default:false
        }
    },
    data(){
        return{
            activeName:"first",
            paymentWays:{
                1:"现金",
                2:"银行转账",
                3:'微信转账',
                4:'支付宝转账',
                5:'其他'
            },
            isConfirms:{
              2:'已确认',
              1:'未确认'
            },
            form:{
                id:'',
                chargeName:"",
                platformName:"",
                receiveName:"",
                confirmName:"",
                sendNumber:"",
                createName:"",
                createTime:"",
                deliverTrackVoList:[],
                deliveryTime:"",
                discount:"",
                isConfirm:"",
                isbn:"",
                materialName:"",
                price:"",
                notes:"",
                specialtyName:'',
                trackingNumber:"",
                unitName:"",
                useBookYear:'',
                distributionDetailList:[]

            },
        }
    },
    computed:{
        drawer:{
            get(){
                return this.visible
            },
            set(val){
                this.$emit('updateVisible',val)
            }
        }
    },
    created(){
    },
    methods:{
        loadData(id){
            deliverInfo(id).then((result) => {
                this.form = result.data;
            }).catch((err) => {
                
            });
        },
        beforeClose(){
            this.drawer = false;
        },
    }
}
</script>

<style scoped>
.pb12{
    padding-bottom: 12px;
}
.lh /deep/.el-form-item__content{
    line-height: 18px !important;
    padding: 0;
    padding-top: 12px;
}
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}
.concss{
    padding: 0px 20px;
    padding-top: 30px;
    height: calc(100% - 30px);
    overflow-y: auto;
}
.mydrawer /deep/.el-drawer__header{
    text-align: center;
    color: #333333;
}
</style>