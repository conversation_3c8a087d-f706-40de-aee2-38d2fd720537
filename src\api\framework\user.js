import request from '@/utils/request'


export function treeDepartmentList(query) {
  return request({
    url: '/crm/controller/userinfodetail/treeDepartmentList',
    method: 'get',
    params: query
  })
}

export function userList(query) {
  return request({
    url: '/crm/controller/userinfodetail/list',
    method: 'get',
    params: query
  })
}




export function queryRoleList(query) {
  return request({
    url: '/crm/controller/userinfodetail/queryRoleList',
    method: 'get',
    params: query
  })
}


export function updateUserRole(data) {
  return request({
    url: '/crm/controller/userinfodetail/updateUserRole',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

export function userRelateMove(data) {
  return request({
    url: '/crm/controller/userinfodetail/userRelateMove',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

export function synchronizeUpdateDepartments() {
  return request({
    url: '/crm/controller/companydepartment/synchronizeUpdateDepartments',
    method: 'post',
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

export function refreshUserInfo() {
  return request({
    url: '/crm/controller/userinfodetail/refreshUserInfo',
    method: 'get',
  })
}
//获取部门详情
export function findDepartmentInfo(data) {
  return request({
    url: '/crm/controller/companydepartment/findDepartmentInfo',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

// 新增子部门
export function saveDepartment(data) {
  return request({
    url: '/crm/controller/companydepartment/saveDepartment',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}
// 更新部门信息
export function updateDepartment(data) {
  return request({
    url: '/crm/controller/companydepartment/updateDepartment',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}
// 查询部门操作记录
export function queryDepartmentLog(data) {
  return request({
    url: '/crm/controller/companydepartment/queryDepartmentLog',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

// 查询数据迁移信息
export function queryMigrateData(data) {
  return request({
    url: '/crm/controller/dataMigration/queryMigrateData',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

// 执行数据迁移
export function dataMigration(data) {
  return request({
    url: '/crm/controller/dataMigration/dataMigration',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

// 查询迁移进度
export function queryProgress(data) {
  return request({
      url: '/crm/controller/dataMigration/queryProgress',
      method: 'post',
      data,
      headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

// 取消数据迁移
export function cancelMigration() {
  return request({
    url: '/crm/controller/dataMigration/cancelMigration',
    method: 'post',
    data: {},
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

// 更新员工启用/禁用状态
export function updateEnable(data) {
  return request({
    url: '/crm/controller/userinfo/updateEnable',
    method: 'post',
    data,
  })
}

// 删除员工
export function deleteUser(data) {
  return request({
    url: '/crm/controller/userinfodetail/delete',
    method: 'post',
    data
  })
}

// 同步员工
export function synchronizeEmployees() {
  return request({
    url: '/crm/controller/userinfodetail/synchronizeEmployees',
    method: 'post',
    data: {},
  })
}

// 部门迁移
export function migrateDepartment(data) {
  return request({
    url: '/crm/controller/companydepartment/migrateDepartment',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

// 获取用户部门列表
export function queryUserDepartmentList(query) {
  return request({
    url: '/crm/controller/companydepartment/queryUserDepartmentList',
    method: 'get',
    params: query
  })
}

// 员工部门迁移
export function migrateDepartmentEmployees(data) {
  return request({
    url: '/crm/controller/userinfodetail/userDepartmentMove',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

// 删除部门
export function deleteDepartment(data) {
  return request({
    url: '/crm/controller/companydepartment/deleteDepartment',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
  })
}

