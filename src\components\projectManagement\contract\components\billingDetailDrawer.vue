<template>
  <el-drawer
    center
    class="mydrawer"
    title="开票详情"
    size="45%"
    :visible.sync="drawer"
    direction="rtl"
  >
    <div class="concss">
      <el-form class="infoform" ref="form" label-width="110px">
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb20 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="合同金额：" class="labeltext">
              <span>{{ form.contractAmount }}万元</span>
            </el-form-item>
            <el-form-item label="未开票金额：" class="labeltext">
              <span>{{ form.noInvoicingTotalAmount }}万元</span>
            </el-form-item>
            <el-form-item label="币种：" class="labeltext">
              <span>{{ form.currencyName }}</span>
            </el-form-item>
            <el-form-item label="开票日期：" class="labeltext">
              <span>{{ form.invoicingDate }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="开票金额：" class="labeltext">
              <span>{{ form.invoicingAmount }}万元</span>
            </el-form-item>
            <el-form-item label="开票类型：" class="labeltext">
              <span>{{ invoicingTypes[form.invoicingType] }}</span>
            </el-form-item>
            <el-form-item label="单位税号：" class="labeltext">
              <span>{{ form.taxIdentificationNumber }}</span>
            </el-form-item>
            <el-form-item label="抬头名称：" class="labeltext">
              <span>{{ form.invoiceHeader }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb20 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="开户银行：" class="labeltext">
              <span>{{ form.openingBank }}</span>
            </el-form-item>
            <el-form-item label="单位地址：" class="labeltext">
              <span>{{ form.unitAddress }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="开户地址：" class="labeltext">
              <span>{{ form.openingAddress }}</span>
            </el-form-item>
            <el-form-item label="联系电话：" class="labeltext">
              <span>{{ form.contactsPhone }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row :gutter="0" class="width100 mt10 pb12 mb20 bbline" type="flex" justify="center">
                <el-col :span="11">
                    <el-form-item label="抬头类型：" class="labeltext">
                        <span>{{invoiceHeaderTypes[form.invoiceHeaderType]}}</span>
                    </el-form-item>
                    <el-form-item label="纳税识别号：" class="labeltext">
                        <span>{{form.taxIdentificationNumber}}</span>
                    </el-form-item>
                    <el-form-item label="开户账号：" class="labeltext">
                        <span>{{form.openingAccount}}</span>
                    </el-form-item>
                    <el-form-item label="联系人：" class="labeltext">
                        <span>{{form.contacts}}</span>
                    </el-form-item>
                    <el-form-item label="寄送人：" class="labeltext">
                        <span>{{form.sender}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="11">
                    <el-form-item label="发票抬头：" class="labeltext lh">
                        <span>{{form.invoiceHeader}}</span>
                    </el-form-item>
                    <el-form-item label="开户行：" class="labeltext lh">
                        <span>{{form.openingBank}}</span>
                    </el-form-item>
                    <el-form-item label="开票地址：" class="labeltext lh">
                        <span>{{form.openingAddress}}</span>
                    </el-form-item>
                    <el-form-item label="联系电话：" class="labeltext">
                        <span>{{form.contactsPhone}}</span>
                    </el-form-item>
                    <el-form-item label="寄送地址：" class="labeltext lh">
                        <span>{{form.mailingAddress}}</span>
                    </el-form-item>
                </el-col>
            </el-row> -->
        <!-- <el-row
          :gutter="0"
          class="mt10 pb20 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="22">
            <el-form-item label="附件：" class="labeltext lh24">
              <pdfview type="1" :pdfArr="form.fileInfoList"></pdfview>
            </el-form-item>
            <el-form-item label="备注：" class="labeltext lh">
              <span>{{ form.notes }}</span>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="申请人：" class="labeltext">
              <span>{{ form.createByName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="申请时间：" class="labeltext">
              <span>{{ form.createTime }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="checkpeople">
          <span class="spanname">审核人：</span>
          <eltimeline :businessId="businessId" :status="status" v-if="drawer"></eltimeline>
        </div>
      </el-form>
    </div>
  </el-drawer>
</template>

<script>
import {
  contractinvoicingInfo,
  selectReturnOrRefund,
} from '@/api/contract/index'
import pdfview from '@/components/common/pdfview.vue'
import eltimeline from '@/components/common/eltimeline.vue'
export default {
  components: {
    pdfview,
    eltimeline,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    drawer: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('updateVisible', val)
      },
    },
  },
  /**
     *
开票类型（1：增值税专用发票；2：增值税普通发票；3：电子普通发票；4：收据）
     */
  data() {
    return {
      noInvoicingTotalAmount: 0,
      currencys: {
        1: '人民币',
      },
      invoiceHeaderTypes: {
        1: '企业',
      },
      invoicingTypes: {
        1: '增值税专用发票',
        2: '增值税普通发票',
        3: '电子普通发票',
        4: '收据',
      },
      form: {
        invoicingAmount: '',
        currency: '',
        invoicingDate: '',
        invoicingType: '',
        invoicingNumber: '',
        invoiceHeaderType: '',
        invoiceHeader: '',
        taxIdentificationNumber: '',
        openingBank: '',
        openingAccount: '',
        openingAddress: '',
        contacts: '',
        contactsPhone: '',
        sender: '',
        mailingAddress: '',
        notes: '',
        createByName: '',
        createTime: '',
        fileInfoList: [],
      },
      businessId: '',
      status:''
    }
  },
  methods: {
    beforeClose() {
      this.drawer = false
      var keys = Object.keys(this.form)
      keys.forEach((element) => {
        this.form[element] = ''
      })
    },
    loadData(id,status) {
      this.businessId = id
      this.status = status
      contractinvoicingInfo(id).then((result) => {
        this.form = result.data
        this.loadAmount(this.form.contractId)
      })
    },
    loadAmount(id) {
      selectReturnOrRefund({ id: id }).then((result) => {
        this.noInvoicingTotalAmount = result.data.noInvoicingTotalAmount
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.spanname {
  width: 110px;
  padding-right: 8px;
  text-align: right;
  flex-shrink: 0;
}
.checkpeople {
  display: flex;
  margin-left: 20px;
}
.pdfspan {
  margin-left: -18px !important;
}
/deep/ .guancontent {
  margin-left: -18px !important;
  margin: 0;
  // margin-top: 10px;
}
.pb12 {
  padding-bottom: 12px;
}
.lh24 /deep/.el-form-item__content {
  line-height: 24px !important;
  padding: 0;
  padding-top: 8px;
}
.filebg {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  padding: 3px 10px;
  color: #4285f4;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #4285f4;
  cursor: pointer;
}
.pb12 {
  padding-bottom: 12px;
}
.lh /deep/.el-form-item__content {
  line-height: 18px !important;
  padding: 0;
  padding-top: 12px;
}
.infoform /deep/.el-form-item {
  margin-bottom: 0px;
}
.concss {
  padding: 0px 20px;
  padding-top: 30px;
  overflow-y: auto;
  /* background-color: red; */
  height: calc(100vh - 75px);
}
.mydrawer /deep/.el-drawer__header {
  text-align: center;
  color: #333333;
}
</style>
