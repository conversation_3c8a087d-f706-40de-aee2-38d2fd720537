<template>
    <div>
        <el-card class="mokuai">
            <div class="title">
                <textBorder class="biaoti">客户信息完整度分析</textBorder>
                <div class="circles">
                    <div class="sanxing">
                        <div class="circle"></div>
                        <div class="zi">三星客户</div>

                        <div class="circle1"></div>
                        <div class="zi">二星客户</div>
                
                        <div class="circle2"></div>
                        <div class="zi">一星客户</div>
                    </div>


                </div>
            </div>
            <el-row :gutter="12">
                <el-col :span="8">
                    <div class="bingtu">
                        <textBorder class="wenzi1">客户星级占比</textBorder>
                        <div class="tubiao" ref="pieChartContainer"></div>
                    </div>
                </el-col>
                <el-col :span="16">
                    <div class="bingtu">
                        <textBorder class="wenzi1">各级客户信息完整度分析</textBorder>
                        <div class="tubiao1" ref="chartContainer"></div>
                    </div>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
import '../detail.css';
import * as echarts from 'echarts';
import textBorder from '@/components/common/textBorder.vue';
export default {
    components:{
        textBorder
    },
    data() {
        return {

            resizeHandler: null,

        }
    },
    mounted() {
        this.setupResizeEvent();
    },
    beforeDestroy() {
        // 在组件销毁前移除事件监听器
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
    },
    methods: {
        // 柱状图
        initChart(data) {
            this.chart = echarts.init(this.$refs.chartContainer);
            const option = {
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(function (item, index) {
                            // 根据索引匹配星级
                            let starLevel = '';
                            switch (index) {
                                case 0:
                                    starLevel = '完成度一星';
                                    break;
                                case 1:
                                    starLevel = '完成度二星';
                                    break;
                                case 2:
                                    starLevel = '完成度三星';
                                    break;
                            }
                            result += item.marker + starLevel + ' : ' + '<span style="font-weight: bold; margin-left: 20px;">' + item.value + '</span><span style="color: #999999; ">人</span><br/>';
                        });
                        return result;
                    }
                },
                xAxis: {
                    type: 'category',
                    axisTick: { // 坐标轴刻度
                    show: false
                    },
                    splitLine: { // 坐标轴在 grid 区域中的分隔线。
                    show: false
                    },

                    data: ['S级客户', 'A级客户', 'B级客户', 'C级客户', 'D级客户'],
                    axisPointer: {
                        type: 'shadow', // 使用阴影效果
                        shadowStyle: {
                            color: 'rgba(237, 238, 252,0.3)', // 阴影颜色，这里使用半透明的灰色
                            width: 'auto' // 自动匹配数据点的宽度
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    splitLine: {  // 坐标轴在 grid 区域中的分隔线
                    lineStyle: { // 分隔线
                        type: 'dashed', // 线的类型
                        color: '#e8e8e8' // 分隔线颜色
                    }
                    },

                },
                legend: {
                    top: '0', 
                    right: '0', 
                },
                series: [            
                    {
                        name: '完成度1星',
                        data: data.oneStarCustomerList,
                        type: 'bar',
                        color: '#2AD0D0',
                    },
                    {
                        name: '完成度2星',
                        data: data.twoStarCustomerList,
                        type: 'bar',
                        color: '#3782FF',
                    },
                    {
                        name: '完成度3星',
                        data: data.threeStarCustomerList,
                        type: 'bar',
                        color: '#FF9F3E',
                    }
                ]
            };
            this.chart.setOption(option);
            
            window.addEventListener('resize',function(){
                this.chart.resize();
            }.bind(this)) 
        },
        // 饼状图
        initPieChart(data) {
            const pieChart = echarts.init(this.$refs.pieChartContainer);
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                    return `<div style="display: flex; align-items: center;">
                                <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ${params.color}; margin-right: 5px;"></span>
                                ${params.name} : <span style="font-size: 20px; font-weight: bold; margin-left: 20px;">${params.value}</span>人
                            </div>`;
                }
                },
                    series: [
                        // 画外部描述的饼图
                        {
                        type: "pie",
                        radius: "70%",
                        center: ["50%", "50%"],
                        data: [

                            {
                            name: "一星客户",
                            value: data[0],
                            itemStyle: {
                                color: '#2AD0D0'
                            }
                            },
                            {
                            name: "二星客户",
                            value: data[1],
                            itemStyle: {
                                color: '#3782FF'
                            }
                            },
                            {
                            name: "三星客户",
                            value: data[2],
                            itemStyle: {
                                color: '#FF9F3E'
                            }
                            },
                        ],
                        label: {
                            show: true,
                            position: "outside",
                            color: "#7F8FA4",
                            fontSize: 12,
                        },
                        labelLine: {
                            show: true,
                            length: 20, // 可以调整标签线的长度
                            length2: 20 // 标签线的二段长度
                        },
                        },
                        // // 画内部百分比的饼图
                        // {
                        // type: "pie",
                        // data: [
                        // {
                        //     name: "一星客户",
                        //     value: data[0],
                        //     itemStyle: {
                        //         color: '#2AD0D0'
                        //     }
                        //     },
                        //     {
                        //     name: "二星客户",
                        //     value: data[1],
                        //     itemStyle: {
                        //         color: '#3782FF'
                        //     }
                        //     },
                        //     {
                        //     name: "三星客户",
                        //     value: data[2],
                        //     itemStyle: {
                        //         color: '#FF9F3E'
                        //     }
                        //     },
                        // ],
                        // label: {
                        //     show: true,
                        //     position: "inside",
                        //     formatter: `{c}`,
                        // },
                        // },
                    ],
            };
            this.pieChart = echarts.init(this.$refs.pieChartContainer);
            this.pieChart.setOption(option);
        },
        setupResizeEvent() {
            this.resizeHandler = () => {
                if (this.chart) {
                    this.chart.resize();
                }
                if (this.pieChart) {
                    this.pieChart.resize();
                }
            };
            window.addEventListener('resize', this.resizeHandler);
        }
    }
}
</script>
<style lang="scss" scoped>

.mokuai {
    height: 461px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    margin-bottom: 12px;
}
.title {
    display: flex; 
    justify-content: space-between; 
    align-items: flex-start; 
}
.left-container {
    display: flex;
}

.bingtu {
    height: 360px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E6E6E6;
    padding: 16px;
}
.tubiao {
    width: 100%;
    height: 300px;
}
.tubiao1 {
    width: 100%;
    height: 350px;
}
.sanxing {
    display: flex;
}
.circle {
    width: 8px;
    height: 8px;
    background: #FF9F3E;
    border-radius: 99px 99px 99px 99px;
    margin-top: 5px;
    margin-right: 8px;
}
.circle1 {
    width: 8px;
    height: 8px;
    background: #3782FF;
    border-radius: 99px 99px 99px 99px;
    margin-top: 5px;
    margin-right: 8px;
}
.circle2 {
    width: 8px;
    height: 8px;
    background: #2AD0D0;
    border-radius: 99px 99px 99px 99px;
    margin-top: 5px;
    margin-right: 8px;
}
.zi{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    margin-right: 16px;
}
</style>