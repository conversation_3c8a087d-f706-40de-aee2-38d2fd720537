<template>
  <div class="mainbg fixpb">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="单位回收站" name="first">
        <div class="right">
          <el-button
            size="small"
            type="danger"
            @click="deleteCurrentPage"
            :disabled="!tableDataUnit.length"
            >删除本页数据</el-button
          >
        </div>
        <el-table class="mytable" :data="tableDataUnit" v-loading="isLoading">
          <el-table-column prop="unitName" label="单位名称" align="center">
          </el-table-column>
          <el-table-column
            prop="customerNumber"
            label="下属客户数量"
            align="center"
          >
          </el-table-column>
          <el-table-column prop="unitTypeName" label="单位类型" align="center">
          </el-table-column>
          <el-table-column
            prop="unitCharacterName"
            label="单位特点"
            align="center"
          >
          </el-table-column>
          <el-table-column label="来源" align="center">
            <template slot-scope="scope">
              {{ sourceName[scope.row.source] }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" align="center" label="联系电话">
          </el-table-column>
          <el-table-column prop="edit" width="100" fixed="right" label="操作">
            <!-- v-isShow="'crm:controller:unit:restore'" -->
            <template slot-scope="scope">
              <el-button
                class="bbtn"
                type="text"
                @click="restoreUnit(scope.row)"
              >
                还原</el-button
              >
              <el-button
                class="rbtn"
                type="text"
                @click="deleteActionUnit(scope.row)"
              >
                删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <nolist></nolist>
          </template>
        </el-table>
        <div class="fixpage">
          <page
            :currentPage="pageBeanUnit.pageNum"
            :total="totalUnit"
            :pageSize="pageBeanUnit.pageSize"
            @updatePageNum="handleCurrentChangeUnit"
          ></page>
        </div>
      </el-tab-pane>
      <el-tab-pane label="客户回收站" name="second">
        <div class="right">
          <el-button
            size="small"
            type="danger"
            @click="deleteCurrentPage"
            :disabled="!tableData.length"
            >删除本页数据</el-button
          >
        </div>
        <el-table class="mytable" :data="tableData" style="width: 100%">
          <el-table-column
            prop="customerName"
            label="客户名称"
            align="center"
            class-name="column_blue"
            width="167px"
          >
          </el-table-column>
          <el-table-column
            prop="unitName"
            label="客户单位"
            align="center"
            min-width="150px"
          >
          </el-table-column>
          <el-table-column prop="unitDepartment" label="部门" align="center">
          </el-table-column>
          <el-table-column prop="duties" label="职务" align="center">
          </el-table-column>
          <el-table-column
            prop="chargePersonName"
            label="负责人"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="collaboratorName"
            label="协作人"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="customerLevelName"
            label="客户级别"
            align="center"
            width="120px"
          >
            <template slot-scope="scope">
              <span :class="levelColor[scope.row.customerLevelName]">{{
                scope.row.customerLevelName
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="phone"
            label="联系方式"
            align="center"
            width="120px"
          >
          </el-table-column>
          <el-table-column
            prop="edit"
            width="100"
            align="center"
            fixed="right"
            label="操作"
          >
            <template slot-scope="scope">
              <el-button
                class="bbtn"
                v-isShow="'crm:controller:customer:restore'"
                type="text"
                @click="restore(scope.row)"
              >
                还原</el-button
              >
              <el-button
                class="rbtn"
                v-isShow="'crm:controller:customer:physicalDelete'"
                type="text"
                @click="deleteAction(scope.row)"
              >
                删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <nolist></nolist>
          </template>
        </el-table>
        <div class="fixpage">
          <page
            :currentPage="pageBean.pageNum"
            :total="total"
            :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"
          ></page>
        </div>
      </el-tab-pane>
    </el-tabs>
    <dc-dialog
      iType="1"
      title="确定删除吗？"
      width="500px"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <template> </template>
      <p class="pcc">
        {{
          deleteType == 2
            ? '回收站删除该单位后将无法恢复，是否继续删除？'
            : ' 回收站删除该客户后将无法恢复，是否继续删除？'
        }}
      </p>
    </dc-dialog>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import {
  recycleCustomerList,
  physicalDelete,
  restoreCustomer,
  restoreUnit,
  physicalDeleteUnit,
  batchDeleteUnit,
  batchDeleteCustomer,
} from '@/api/clientmanagement/customer'
import { listUnit } from '@/api/clientmanagement/unit'
import { customerLevelColors } from '@/utils/dict'
export default {
  components: {
    page,
    nolist,
  },
  data() {
    return {
      levelColor: customerLevelColors,
      totalUnit: 0,
      sourceName: {
        1: '公司资源',
        2: '自主开拓',
      },
      dialogVisible: false,
      deleteData: {},
      isLoading: false,
      tableData: [],
      tableDataUnit: [],
      total: 0,
      pageBean: {
        isDeleted: 1,
        pageNum: 1,
        pageSize: 10,
      },
      pageBeanUnit: {
        isDeleted: 1,
        pageNum: 1,
        pageSize: 10,
      },
      activeName: 'first',
      deleteType: 2,
    }
  },
  created() {
    this.loadData()
    this.loadDataUnit()
  },
  methods: {
    loadDataUnit() {
      this.isLoading = true
      listUnit(this.pageBeanUnit)
        .then((result) => {
          this.tableDataUnit = result.data
          this.totalUnit = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    handleClick(tab, event) {
      this.activeName = tab.name
      if (tab.name == 'first') {
        this.pageBeanUnit.pageNum = 1
        this.loadDataUnit()
        this.deleteType = 2
      } else {
        this.pageBean.pageNum = 1
        this.loadData()
        this.deleteType = 1
      }
    },
    loadData() {
      this.isLoading = true
      recycleCustomerList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    submitDialog() {
      let res
      if (this.deleteType == 2) {
        physicalDeleteUnit({ id: this.deleteData.id }).then((res) => {
          this.$message({
            type: 'success',
            message: '已删除',
          })
          this.loadDataUnit()
        })
      } else {
        physicalDelete({ id: this.deleteData.id }).then((res) => {
          this.$message({
            type: 'success',
            message: '已删除',
          })
          this.loadData()
        })
      }
      this.dialogVisible = false
    },
    deleteAction(data) {
      this.deleteData = data
      this.dialogVisible = true
    },
    deleteActionUnit(data) {
      const dataScope = sessionStorage.getItem('dataScope')
      if (dataScope != 4) {
        this.$message({
          type: 'error',
          message: '暂无权限，无法删除',
        })
        return
      }

      this.deleteData = data
      this.dialogVisible = true
    },
    restore(data) {
      restoreCustomer({ id: data.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '已还原，请到客户管理进行查看。',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    restoreUnit(data) {
      restoreUnit({ id: data.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '已还原，请到单位管理进行查看。',
            })
            this.loadDataUnit()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    handleCurrentChangeUnit(page) {
      this.pageBeanUnit.pageNum = page
      this.loadDataUnit()
    },
    deleteCurrentPage() {
      const dataScope = sessionStorage.getItem('dataScope')
      if (dataScope != 4) {
        this.$message({
          type: 'error',
          message: '暂无权限，无法删除',
        })
        return
      }

      const isUnit = this.deleteType == 2
      const data = isUnit ? this.tableDataUnit : this.tableData
      const message = isUnit ? '单位' : '客户'
      const deleteFunc = isUnit ? batchDeleteUnit : batchDeleteCustomer
      const loadDataFunc = isUnit ? this.loadDataUnit : this.loadData

      if (!data.length) return

      this.$confirm(`确定要删除本页所有${message}数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const ids = data.map((item) => item.id)
          deleteFunc(ids).then((res) => {
            if (res.status == 0 && res.data) {
              this.$message({
                type: 'success',
                message: '批量删除成功',
              })
              loadDataFunc()
            } else {
              loadDataFunc()
              this.$message({
                type: 'error',
                message: res.msg,
              })
            }
          })
        })
        .catch(() => {})
    },
  },
}
</script>
<style lang="scss" scoped>
.right {
  text-align: right;
  margin-bottom: 10px;
}
.pcc {
  margin: 0 auto;
  text-align: center;
}
.smtext {
  zoom: 0.8;
}
.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.mt {
  margin-top: 4px;
}
.cusnamecss {
  display: flex;
}
.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 20px;
}
.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}
.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}
.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}
.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}
.customertable {
  margin-top: 10px !important;
}
.customertable .el-button + .el-button {
  margin-left: 40px;
}
.customertable .el-button {
  padding: 2px;
}
.mr10 {
  margin-right: 10px;
}
.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}
.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}

.mainbg {
  min-height: 100%;
  padding: 20px;
  background-color: white;
}
.tabscss /deep/.el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 56px;
  width: 240px !important;
  text-align: center;
  line-height: 56px;
  /* padding: 0 60px; */
}
</style>
