<template>
  <div class="mainbg fixpb">
    <el-form :inline="true" class="myform clearfix">
      <el-form-item label="" class="flex">
        <el-input class="definput wid" v-model="pageBean.opportunityName" clearable placeholder="机会名称"></el-input>
      </el-form-item>
      <el-form-item label="" class="flex">
        <el-input class="definput wid yong" v-model="pageBean.materialName" clearable
          placeholder="教材名称、出版社、用书专业"></el-input>
      </el-form-item>
      <el-form-item label="" class="flex">
        <el-input class="definput wid inputWid150" v-model="pageBean.customerName" clearable
          placeholder="客户名称"></el-input>
      </el-form-item>
      <el-form-item label="" class="flex">
        <el-input class="definput wid inputWid150" v-model="pageBean.unitName" clearable placeholder="客户单位"></el-input>
      </el-form-item>
      <el-form-item label="" class="flex">
        <el-input class="definput inputWid150 fuw" v-model="pageBean.chargePersonName" clearable
          placeholder="负责人"></el-input>
      </el-form-item>
      <el-form-item label="" class="flex">
        <el-input class="definput inputWid150 fuw" v-model="pageBean.chargePersonDepartment" clearable
          placeholder="负责人部门"></el-input>
      </el-form-item>
      <el-form-item label="" class="flex">
        <el-input class="definput inputWid150 fuw" v-model="pageBean.createByName" clearable
          placeholder="创建人"></el-input>
      </el-form-item>
      <el-form-item label="" class="flex">
        <el-input class="definput inputWid150 fuw" v-model="pageBean.createByDepartment" clearable
          placeholder="创建人部门"></el-input>
      </el-form-item>
      <el-form-item label="协作负责：" label-width="85px">
        <el-select class="definput w100" popper-class="removescrollbar" v-model="typename" @change="changeOption"
          clearable placeholder="请选择">
          <el-option key="" label="全部" value=""> </el-option>
          <el-option key="1" label="我负责的" value="1"> </el-option>
          <el-option key="2" label="我协作的" value="2"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="机会阶段：" label-width="85px">
        <el-select class="definput w100" popper-class="removescrollbar" clearable v-model="pageBean.opportunityStage"
          placeholder="请选择">
          <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型：" prop="opportunityType">
        <el-select @change="changeValue" class="definput w100 ellipsis" popper-class="removescrollbar"
          v-model="pageBean.opportunityType" clearable placeholder="请选择">
          <el-option v-for="item in optionsContract" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>

        <el-select class="definput w150 ml10" popper-class="removescrollbar" v-model="pageBean.opportunityTypeSub"
          clearable placeholder="请选择">
          <el-option v-for="item in optionsContractDetail" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间：">
        <datepicker @submitAction="submitAction"></datepicker>
      </el-form-item>
      <el-form-item>
        <el-button class="defaultbtn mt" icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
      </el-form-item>
      <el-form-item class="fr">
        <el-button class="defaultbtn mt" v-isShow="'crm:controller:opportunity:save'" icon="el-icon-plus" type="primary"
          @click="addChance">新建机会</el-button>
      </el-form-item>
    </el-form>
    <el-table class="jhtable mytable1" :data="tableData" style="width: 100%" v-loading="isLoading">
      <el-table-column prop="opportunityName" label="销售机会" align="center" min-width="200px">
      </el-table-column>
      <el-table-column prop="type" label="类型" align="center" width="200">
      </el-table-column>
      <el-table-column prop="customerName" label="客户" align="center">
      </el-table-column>
      <el-table-column prop="unitName" label="客户单位" align="center" width="200" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="estimatedAmount" label="预计金额" align="center">
        <template slot-scope="scope">
          <span>
            {{
              scope.row.estimatedAmount > 0
                ? scope.row.estimatedAmount + '万元'
                : '—'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="stage" label="机会阶段" align="center">
        <template slot-scope="scope">
          <span :class="nameToCssColor[scope.row.opportunityStage]">{{
            scope.row.stage
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="chargePersonName" label="负责人" align="center">
      </el-table-column>
      <el-table-column prop="chargePersonDepartment" label="负责人部门" width="150px" align="center">
      </el-table-column>
      <!-- <el-table-column
        prop="materialName"
        label="教材名称"
        align="center"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.materialName || "—" }}
          </span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        prop="platformName"
        label="出版社"
        align="center"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.platformName || "—" }}
          </span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column prop="specialtyName" label="用书专业" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.specialtyName || "—" }}
          </span>
        </template>
      </el-table-column> -->
      <el-table-column prop="specialtyName" label="跟进拜访次数" align="center" width="110px">
        <template slot-scope="scope">
          <span class="numtime" @click="gotoDetail(scope.row)">
            {{ scope.row.visitCount + '次' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="createByName" width="100px" align="center" label="创建人"></el-table-column>
      <el-table-column prop="createByDepartment" min-width="160px" align="center" label="创建人部门"></el-table-column>
      <el-table-column prop="edit" width="180" align="center" fixed="right" label="操作">
        <template slot-scope="scope">
          <el-dropdown placement="bottom-end" @command="(e) => handleCommand(e, scope.row)" trigger="click">
            <el-button class="bbtn mr40" type="text"> 更多 </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="shouldShowStageButton(scope.row.stage)"
                v-isShow="'crm:controller:opportunity:update'"
                :command="1">{{ getStageButtonText(scope.row.stage) }}</el-dropdown-item>
              <el-dropdown-item v-isShow="'crm:controller:opportunity:info'" :command="2">详情</el-dropdown-item>
              <el-dropdown-item v-isShow="'crm:controller:opportunity:addContract'" :command="3">创建合同</el-dropdown-item>
              <el-dropdown-item v-isShow="'crm:controller:opportunity:update'" :command="4">快捷跟进</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button class="bbtn" type="text" v-isShow="'crm:controller:opportunity:update'"
            @click="onEdit(scope.row)">
            编辑</el-button>
          <el-button class="rbtn" type="text" v-isShow="'crm:controller:opportunity:delete'"
            @click="deleteAction(scope.row)">
            删除</el-button>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"></page>
    </div>

    <dc-dialog :iType="dialogType" title="确定删除吗？" width="500px" :dialogVisible.sync="dialogVisible"
      @submit="submitDialog">
      <template> </template>
      <p class="pcc">{{ dialogMessage }}</p>
    </dc-dialog>
    <stageDialog ref="stage" :visible="stageVisible" @updateVisible="updateStageVisible" @successBlock="successBlock">
    </stageDialog>
  </div>
</template>

<script>
  import page from '../../common/page.vue'
  import stageDialog from './components/stageDialog.vue'
  import nolist from '../../common/nolist.vue'
  import {
    getDict,
    valide,
    getParStr
  } from '@/utils/tools'
  import {
    nameToCssColor
  } from '@/utils/dict'
  import {
    opportunityList,
    deleteOpportunity,
    addContract,
  } from '@/api/clientMaintenance/opportunity'
  import datepicker from '@/components/common/datepicker.vue'
  import {
    queryVoListByCode
  } from '@/api/wapi.js'
  export default {
    components: {
      page,
      stageDialog,
      nolist,
      datepicker,
    },
    data() {
      return {
        nameToCssColor: nameToCssColor,
        typename: '',
        dialogType: 1,
        dialogMessage: '确认删除该机会？',
        dialogVisible: false,
        stageVisible: false,
        options: [],
        isLoading: false,
        deleteData: {},
        tableData: [],
        total: 0,
        datarange: [],
        pageBean: {
          opportunityStage: '',
          opportunityName: '',
          customerName: '',
          unitName: '',
          chargePersonName: '',
          createByName: '',
          createByDepartment: '',
          chargePerson: '',
          chargePersonDepartment: '',
          collaborator: '',
          opportunityTypeSub: '',
          opportunityType: '',
          startTime: '',
          endTime: '',
          time: '',
          pageNum: 1,
          pageSize: 10,
        },
        types: [],
        type: 'opportunityName',
        optionsContract: [],
        optionsContractDetail: [],
      }
    },
    created() {
      if (this.$route.query.unitId) {
        this.pageBean.unitId = this.$route.query.unitId
      }
      if (Object.keys(this.$route.query).length > 0) {
        this.pageBean = Object.assign(this.pageBean, this.$route.query)
        this.pageBean.pageNum = Number(this.pageBean.pageNum)
        this.pageBean.pageSize = Number(this.pageBean.pageSize)
      }
      this.getTypeList()
      this.loadData()
      this.getDictApi()
    },
    methods: {
      getStageDisable(name) {
        if (name == '赢单' || name == '输单') {
          return true
        }
        return false
      },
      // 判断是否显示阶段按钮
      shouldShowStageButton(stageName) {
        return stageName !== '赢单' && stageName !== '输单'
      },
      getStageButtonText(stageName) {
        const stageMap = {
          '初步接洽': '完成接洽',
          '需求确认': '完成确认',
          '方案报价': '完成报价',
          '谈判审核': '完成审核'
        }
        return stageMap[stageName] || '完成接洽'
      },
      handleStageProgress(data) {
        this.stageVisible = true
        this.$refs.stage.getTypeList()
        this.$refs.stage.updateData(data)
      },
      setValue(val) {
        let item = this.optionsContract.filter((item) => item.id === val)
        this.optionsContractDetail = item[0].children
      },
      getDictApi() {
        queryVoListByCode({
          code: 'ContractType'
        }).then((res) => {
          if (res.status == 0) {
            this.optionsContract = res.data
          }
        })
      },
      changeValue(val) {
        if (val == '') {
          this.pageBean.opportunityTypeSub = ''
          this.optionsContractDetail = []
        } else {
          this.pageBean.opportunityTypeSub = ''
          let item = this.optionsContract.filter((item) => item.id === val)
          this.optionsContractDetail = item[0].children
        }
      },
      gotoDetail(row) {
        if (!valide('/clientMaintenance/salesLead/oppvisitlist')) {
          this.$message({
            type: 'error',
            message: '您没有访问权限,请联系管理员',
          })
          return false
        }
        this.$router.push({
          path: '/clientMaintenance/salesLead/oppvisitlist',
          query: {
            id: row.id,
          },
        })
      },
      changeDateRange(date) {
        if (date) {
          this.pageBean.startTime = date[0]
          this.pageBean.endTime = date[1]
        } else {
          this.pageBean.startTime = ''
          this.pageBean.endTime = ''
        }
      },
      loadData() {
        history.replaceState(
          null,
          null,
          `#${this.$route.path}?${getParStr(this.pageBean)}`
        )
        this.isLoading = true
        opportunityList(this.pageBean)
          .then((result) => {
            this.tableData = result.data
            this.total = result.page.total
            this.isLoading = false
          })
          .catch((err) => {
            this.isLoading = false
          })
      },
      getTypeList() {
        getDict('OpportunityStage')
          .then((result) => {
            this.options = result
          })
          .catch((err) => {})
      },
      searchAction() {
        this.pageBean.pageNum = 1
        this.loadData()
      },
      changeOption(data) {
        if (data == '1') {
          this.pageBean.chargePerson = window.sessionStorage.getItem('userid')
          this.pageBean.collaborator = ''
        } else if (data == '2') {
          this.pageBean.chargePerson = ''
          this.pageBean.collaborator = window.sessionStorage.getItem('userid')
        } else {
          this.pageBean.chargePerson = ''
          this.pageBean.collaborator = ''
        }
      },
      updateStageVisible(val) {
        this.stageVisible = val
      },
      successBlock() {
        this.stageVisible = false
        this.loadData()
      },
      submitDialog() {
        this.dialogVisible = false
        if (this.dialogType == 1) {
          deleteOpportunity({
              id: this.deleteData.id
            })
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '删除成功',
                })
                this.loadData()
              } else {
                this.dialogMessage = result.msg
                this.dialogType = 2
                this.dialogVisible = true
              }
            })
            .catch((err) => {})
        } else {}
      },
      addChance() {
        this.$router.push({
          path: '/clientMaintenance/salesLead/add',
        })
      },
      deleteAction(data) {
        this.dialogType = 1
        this.dialogMessage = '确认删除该销售机会？'
        this.dialogVisible = true
        this.deleteData = data
      },
      onEdit(data) {
        this.$router.push({
          path: '/clientMaintenance/salesLead/add',
          query: {
            id: data.id,
          },
        })
      },
      handleCommand(index, data) {
        switch (index) {
          case 1: // 阶段推进
          {
            this.handleStageProgress(data)
          }
          break
          case 2: // 详情
          {
            this.$router.push({
              path: '/clientMaintenance/salesLead/detail',
              query: {
                id: data.id
              }, // 机会id
            })
          }
          break
          case 3: // 创建合同
          {
            this.$confirm('是否生成该机会的合同信息？', '确认信息', {
                distinguishCancelAndClose: true,
                confirmButtonText: '创建',
                cancelButtonText: '取消',
              })
              .then(() => {
                this.$router.push({
                  path: '/projectManagement/contract/add',
                  query: {
                    opportunityId: data.id
                  },
                })
              })
          }
          break
          case 4: // 快捷跟进
          {
            this.$router.push({
              path: '/clientMaintenance/followVisit/add',
              query: {
                opportunityId: data.id
              }, // 机会id，添加opportunityId标识
            })
          }
          break
          default:
            break
        }
      },
      saveContract(id) {
        console.log('生成合同id', id)
        addContract(id)
          .then((result) => {
            if (result.data) {
              this.$confirm('该机会的合同内容已生成，是否查看', '提示', {
                  distinguishCancelAndClose: true,
                  confirmButtonText: '查看合同',
                  cancelButtonText: '取消',
                })
                .then(() => {
                  this.$router.push({
                    path: '/projectManagement/contract/index',
                  })
                })
                .catch((action) => {})
            } else {
              this.$message({
                type: 'error',
                message: result.msg,
              })
            }
          })
          .catch((err) => {})
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page
        this.loadData()
      },
      submitAction(type, dateRange) {
        if (type == 11) {
          if (dateRange) {
            this.pageBean.startTime = dateRange[0]
            this.pageBean.endTime = dateRange[1]
          } else {
            this.pageBean.startTime = ''
            this.pageBean.endTime = ''
          }
          this.pageBean.time = ''
        } else {
          this.pageBean.time = type
          this.pageBean.startTime = ''
          this.pageBean.endTime = ''
        }
      },
    },
  }
</script>
<style>
  .mytable1 td,
  th {
    padding: 14px 0;
  }
</style>

<style scoped lang="scss">
  .ml10 {
    margin-left: 10px;
  }

  .numtime {
    color: #4285f4;
    cursor: pointer;
  }

  .yong {
    width: 240px !important;
  }

  .dan {
    width: 220px !important;
  }

  .w100 {
    width: 110px;
  }

  .w150 {
    width: 150px;
  }

  .wid {
    width: 200px;
  }

  .mr40 {
    margin-right: 16px;
  }

  .jhtable .el-button+.el-button {
    margin-left: 16px;
  }

  .mainbg {
    background-color: white;
    padding: 20px;
    min-height: 100%;
  }

  .pcc {
    margin: 0 auto;
    text-align: center;
  }

  .smtext {
    zoom: 0.8;
  }

  .fxcenter {
    width: 50px;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .zhiding {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .mt {
    margin-top: 3px;
  }

  .cusnamecss {
    display: flex;
  }

  .tagcss {
    font-size: 0.625em !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 14px;
    min-width: 30px;
    line-height: 11px;
    border-radius: 2px;
    margin-right: 20px;
  }

  .genjin {
    color: #4285f4;
    background-color: #dfeafd;
  }

  .fuze {
    color: #fef2e7;
    background-color: #ff8d1a;
  }

  .xiezuo {
    color: #56c36e;
    background-color: #f3fef6;
  }

  .tagcss:nth-child(2n + 2) {
    margin-top: 4px;
  }

  .jhtable .el-button {
    padding: 2px;
  }

  .mr10 {
    margin-right: 10px;
  }

  .bbtn,
  .bbtn:hover,
  .bbtn:focus {
    color: #4285f4;
  }

  .rbtn,
  .rbtn:hover,
  .rbtn:focus {
    color: #f45961;
  }

  .right {
    text-align: justify;
  }

  .color1 {
    color: #4285f4;
  }

  .color2 {
    color: #489dd2 !important;
  }

  .color3 {
    color: #4cafb9 !important;
  }

  .color4 {
    color: #52bd97 !important;
  }

  .color5 {
    color: #56c36e !important;
  }

  .color6 {
    color: #7693c1 !important;
  }
</style>
