<template>
  <el-drawer
    class="mydrawer"
    :visible.sync="visible"
    direction="rtl"
    size="45%"
    title="成本详情"
    @close="handleClose"
    center
  >
    <div class="projectTitle">{{ form.projectName }}</div>
    <div class="bgc">ID:{{form.taskNum}} {{form.taskName}}</div>
    <div class="drawer-content">
      <el-form class="infoform" ref="form" label-width="120px">
        <el-row
          :gutter="0"
          class="width100 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="12">
            <el-form-item label="成本金额：" class="labeltext">
              <span>{{ form.costAmount || '--' }}元</span>
            </el-form-item>
            <el-form-item label="申请人：" class="labeltext">
              <span>{{ form.createByName || '--' }}</span>
            </el-form-item>
            <el-form-item label="申请时间：" class="labeltext">
              <span>{{ form.createTime || '--' }}</span>
            </el-form-item>
            <el-form-item label="成本事项：" class="labeltext">
              <span>{{ form.reason || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成本类型：" class="labeltext">
              <span>{{ form.costTypeName || '--' }}</span>
            </el-form-item>

            <el-form-item label="成本日期：" class="labeltext">
              <span>{{ form.costDate || '--' }}</span>
            </el-form-item>
            <el-form-item label="审批状态：" class="labeltext">
              <span>{{ reviewStatusMap[form.status] || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="12">
            <el-form-item label="客户:" class="labeltext">
              <span>{{ form.customerName }}</span>
            </el-form-item>
            <el-form-item label="业务负责人:" class="labeltext">
              <span>{{ form.businessOwnerName }}</span>
            </el-form-item>
            <el-form-item label="项目类型：" class="labeltext">
              <span>{{ form.projectTypeName + '-' + form.projectTypeSubName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户单位：" class="labeltext">
              <span>{{ form.unitName || '--' }}</span>
            </el-form-item>
            <el-form-item label="项目负责人：" class="labeltext">
              <span>{{ form.chargePersonName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="checkpeople">
          <span class="spanname">审核人：</span>
          <eltimeline :businessId="data.businessId" :status="data.status" v-if="visible"></eltimeline>
        </div>
      </el-form>
      <div class="btns" v-if="handleType == 1">
        <el-button @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="handleApprove">通过</el-button>
      </div>
    </div>

    <!-- 通过对话框 -->
    <approve-dialog
      :visible.sync="approveDialogVisible"
      @submit="onApproveSubmit"
    ></approve-dialog>

    <!-- 驳回对话框 -->
    <reject-dialog
      :visible.sync="rejectDialogVisible"
      @submit="onRejectSubmit"
    ></reject-dialog>
  </el-drawer>
</template>

<script>
import ApproveDialog from '../../common/approveDialog.vue'
import RejectDialog from '../../common/rejectDialog.vue'
import { approve, projectCostInfo } from '@/api/reviewCenter'
import eltimeline from '@/components/common/eltimeline.vue'
import { reviewStatusMap } from "@/utils/dict";
export default {
  name: 'ReviewDrawer',
  components: {
    ApproveDialog,
    RejectDialog,
    eltimeline,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    handleType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: () => {
        return new Object()
      },
    },
  },
  data() {
    return {
      approveDialogVisible: false,
      rejectDialogVisible: false,
      reviewStatusMap,
      form: {},
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  methods: {
    loadinfo() {
      projectCostInfo(this.data.businessId)
        .then((result) => {
          console.log('项目成本详情=====', result)
          this.form = result.data
        })
        .catch((err) => {})
    },
    handleClose() {
      this.visible = false
    },
    handleApprove() {
      this.approveDialogVisible = true
    },
    handleReject() {
      this.rejectDialogVisible = true
    },
    onApproveSubmit() {
      this.submit({
        id: this.data.id,
        status: 3,
      })
    },
    onRejectSubmit(data) {
      console.log('驳回提交的数据', data)
      this.submit({
        id: this.data.id,
        status: 4,
        remark: data,
      })
    },
    submit(par) {
      approve(par)
        .then((result) => {
          if (result.data) {
            this.visible = false
            if (par.status == 3) {
              this.msgSuccess('审核通过')
            } else {
              this.msgSuccess('已驳回')
            }
            this.$emit('reload')
          } else {
            this.$message.error(result.msg)
          }
        })
        .catch((err) => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
}
.bgc{
  margin: 0 56px !important;
  padding:12px;
  border-radius: 8px;
  background-color: #4285f4;
  color: #FFFFFF;
  margin: 0;
}
.boxItem {
  display: flex;
  align-items: center;
  line-height: 18px;
}
/deep/ .el-drawer {
  .el-drawer__header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
}
.mydrawer /deep/.el-drawer__header {
  text-align: center;
  color: #333333;
}
.btns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.mb20 {
  margin-bottom: 20px;
}

.spanname {
  width: 110px;
  padding-right: 8px;
  text-align: right;
  flex-shrink: 0;
}
.checkpeople {
  display: flex;
  margin-left: 6px;
}
:deep() {
  .el-drawer__body {
    .projectTitle {
      padding-left: 58px;
      font-size: 48x;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      margin-bottom: 20px;
    }
  }
}
</style>
