import service from '@/utils/request.js'

// 新增模版
export function addProjectstagetemplate(data) {
    return service.request({
      method: 'post',
      url: '/crm/controller/projectstagetemplate/save',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    });
  }
  // 编辑模版
export function updateProjectstagetemplate(data) {
    return service.request({
      method: 'post',
      url: '/crm/controller/projectstagetemplate/update',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
    });
  }
// 模版列表
export function projectstagetemplateList(params) {
    return service.request({
        method: 'get',
        url: '/crm/controller/projectstagetemplate/list',
        params,
    });
}

// 模版详情
export function projectstagetemplateInfo(id) {
    return service.request({
        method: 'get',
        url: `/crm/controller/projectstagetemplate/info/${id}`,
    });
}

// 
// 删除模版
export function deleteProjectstagetemplate(data) {
    return service.request({
        method: 'post',
        url: '/crm/controller/projectstagetemplate/delete',
        data,
    });
}
// 模板下保存阶段
export function saveTemplateStage(data) {
    return service.request({
        method: 'post',
        url: '/crm/controller/projectstagetemplate/saveTemplateStage',
        data,
    });
}
// 模板下点击新增阶段按钮
export function newAddTemplateStage(data) {
    return service.request({
        method: 'post',
        url: '/crm/controller/projectstagetemplate/newAddTemplateStage',
        data,
    });
}
// 模板下删除阶段
export function deleteTemplateStage(data) {
    return service.request({
        method: 'post',
        url: '/crm/controller/projectstagetemplate/deleteTemplateStage',
        data,
    });
}