import service from '@/utils/request.js'
export function queryInputOutData(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/unit/queryInputOutData',
		params
	});
}

export function queryInputOutInfo(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/unit/queryInputOutInfo',
		params
	});
}


export function queryDistributionDate(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/distribution/queryDistributionDate',
		params
	})
}

export function queryContractOutDate(params) {
	return service.request({
		method: 'get',
		url: '/crm/controller/distribution/queryContractOutDate',
		params
	})
}

