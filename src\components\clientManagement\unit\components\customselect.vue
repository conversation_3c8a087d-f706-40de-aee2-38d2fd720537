<template>
  <div>
    <el-dialog width="70%" class="customD" title="收货地址" :visible.sync="dialogTableVisible">
      <div class="page-header" slot="title">
        <el-form ref="form" :model="pageBean" :inline="true">
          <el-form-item class="cbottom" label="客户名称：">
            <el-input class="definput" v-model="pageBean.customerName" clearable placeholder="请输入客户名称"></el-input>
          </el-form-item>
          <el-form-item class="cbottom" label="部门：">
            <el-input class="definput" v-model="pageBean.unitDepartment" clearable placeholder="请输入部门"></el-input>
          </el-form-item>
          <el-form-item class="cbottom">
            <el-button class="search btn defaultbtn" type="primary" icon="el-icon-search" @click="onSearch">搜索
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table class="mytable" :cell-style="cellStyle" :data="dataList" style="width: 100%" v-loading="isLoading">
        <el-table-column prop="customerName" label="客户名称">
        </el-table-column>
        <el-table-column prop="unitDepartment" label="部门">
        </el-table-column>
        <el-table-column prop="duties" label="职务">
        </el-table-column>
        <el-table-column prop="customerLevelName" label="客户级别">
          <template slot-scope="scope">
              <span :class="customerLevelColors[scope.row.customerLevelName]">{{ scope.row.customerLevelName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系方式" width="120">
        </el-table-column>
        <el-table-column  label="负责人" align="center" width="160px">
            <template slot-scope="scope">
                <div v-if="scope.row.chargePersonNames.length>0">
                    <el-tag class="cuscss" v-for="(item,index) in scope.row.chargePersonNames" :key="index">{{item}}</el-tag>
                </div>
                <div v-else>
                    —
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="edit" width="80" label="操作">
          <template slot-scope="scope">
            <el-button class="bBtn" type="text" @click="onSelected(scope.row)" v-show="scope.row.isSelect == 0"> 选择
            </el-button>
            <div class="rbtn deffont" v-show="scope.row.isSelect" @click="cancleS(scope.row)">取消选择</div>
          </template>

        </el-table-column>
      </el-table>
      <div class="center">
        <el-button class="defaultbtn " type="primary" @click="submitAction">提交</el-button>
      </div>
      <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"></page>
    </el-dialog>
  </div>
</template>

<script>
  import page from '../../../common/page.vue';
  import { selectCustomer } from '@/api/wapi'
  import { unitCusSave } from '@/api/unit'
  import { customerLevelColors } from "@/utils/dict";
  export default {
    components: {
      page,
    },
    props: {
      customVisible: {
        default: false,
      },
      coId: {
        default: ''
      }
    },
    data() {
      return {
        customerLevelColors:customerLevelColors,
        dialogTableVisible: this.customVisible,
        dataList: [],
        pageBean: {
          customerName: '',
          pageNum: 1,
          pageSize: 8,
          unitDepartment: '', 
        },
        isLoading: false,
        total: 0,
        selectedArr: [],
        idToStatus: {},
        tempData: {},
        customerDataS: {},
      }
    },
    watch: {
      customVisible: function (boo) {
        this.dialogTableVisible = boo;
      },
      dialogTableVisible(boo) {
        if (!boo) {
          this.selectedArr = []
        }
        this.$emit('update:customVisible', boo)
      }
    },
    methods: {
      valueFormatter(row, column, cellValue, index){
            if (cellValue) {
                return cellValue
            }
            return '—'
        },
      onSelected(row) {
        row.isSelect = true
        this.selectedArr.push(row.id)
      },
      cancleS(row) {
        row.isSelect = false
        this.selectedArr = this.selectedArr.filter(function (item) {
          return item.id !== row.id
        });
      },
      submitAction() {
        if (this.selectedArr.length == 0) {
          this.msgError('请先选择客户')
          return
        }
        let params = {
          structureId: this.coId,
          customerIds: this.selectedArr
        }
        unitCusSave(params).then(res => {
          if (res.status == 0) {
            this.$emit('update:customVisible', false)
            this.$emit('getCusList')
            this.selectedArr = []
          } else {
            this.msgError(res.msg)
          }
        })
      },
      cellStyle({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
          return "color:#4285F4 ";
        }
        return "";
      },

      onSearch() {
        this.selectCustomerData()
      },
      selectCustomerData(params) {
        if (params) {
          this.tempData = params
        } else {
          params = this.tempData
        }
        let sData = { ...params, ...this.pageBean }
        selectCustomer(sData).then(res => {
          if (res.status == 0) {
            this.dataList = res.data
            this.total = res.page.total
            this.dataList.forEach(item => {
              this.$set(item, 'isSelect', false)
            })
            // this.addSelectedItem(this.dataList)
          } else {
            this.msgError(res.msg)
          }
        })
      },
      addSelectedItem(dataList) {
        dataList.forEach(itemc => {
          if (this.selectedArr.includes(itemc.id)) {
            itemc.isSelect = true
          }
        })
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.selectCustomerData()
      }
    }

  }
</script>

<style lang="scss" scoped>
  .cl {
    margin-left: 0 !important;
  }

  .center {
    margin-top: 10px;
    text-align: center;
  }

  .customD {
    /deep/ .el-dialog__header {
      border-bottom: none;
      padding-bottom: 0 !important;
    }

    .cbottom {
      margin-bottom: 0 !important;
    }

    /deep/ .el-dialog__body {
      padding: 20px;
    }
  }
</style>