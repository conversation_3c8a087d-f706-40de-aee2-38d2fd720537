<template>
  <div class="mainbg">
      <modelCom :typeModel="typeModel"></modelCom>
  </div>
</template>
<script>
import modelCom from '../../zongjie/temmodel/modellistcom.vue';
  export default {
      components: {
        modelCom,
      },
      data() {
          return {
            typeModel:'2'
          }
      },
      created() {
        
      },
      mounted() {
         
      },
      methods: {
   
      }
  }
</script>
<style scoped>
  .mainbg{
    transform: none;
  }
</style>