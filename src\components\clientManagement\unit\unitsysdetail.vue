
<template>
  <div class="mainbg" v-loading="isLoading">
    <div class="backdiv">
      <back>单位结构详情</back>
    </div>
    <div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="grid-content bg-purple ">
            <el-card class="box-card lefttable sleft">
                <div class="dtop">
                    <p class="pti">结构详情</p>
                    <div>
                      <el-row>
                        <el-col :span="12">
                          <div class="spanc">
                            <span class="lle">结构名称: </span>
                            <span>{{sData.name}}</span>
                          </div>
                        </el-col>
                        <el-col :span="12"> 
                          <div class="spanc">
                            <span class="lle">下属人员: </span>
                            <span>{{sData.structureCustomerNum}}</span>
                          </div>
                      </el-col>
                      </el-row>
                    </div>
                </div>
                <div class="dbott">
                  <p class="pti ren">人员信息</p>
                  <el-table  :row-style="isRed" @row-click="rowClickEv"
                  class="unittable  table-style" :data="dataList" >
                    <el-table-column min-width="120" prop="customerName" label="客户名称" align="center">
                    </el-table-column>
                    <el-table-column prop="unitDepartment"  label="部门" align="center" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="duties" align="center" label="职务" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="customerLevelName" align="center" width="120" label="客户级别">
                      <template slot-scope="scope">
                          <span>{{ scope.row.customerLevelName }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="phone" align="center" width="120" label="联系方式">
                    </el-table-column>
                    <el-table-column prop="chargePersonName" align="center" width="120"  label="负责人">
                    </el-table-column>
                    <template slot="empty">
                      <nolist></nolist>
                    </template>
                  </el-table>
                  <!-- <page :currentPage="pageBeanC.pageNum" :total="totalC" :pageSize="pageBeanC.pageSize"
                  @updatePageNum="handleCurrentChangeC"></page> -->
                </div>
            </el-card>
          </div>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card lefttable">
            <el-tabs v-model="activeName" @tab-click="handleClick"  class="tabscss">
              <el-tab-pane label="详情" name="first">
                <el-form class="infoform" ref="form" :model="formInfo" label-width="80px">
                  <textBorder>客户信息</textBorder>
                  <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline" >
                      <el-col :span="12">
                          <el-form-item label="客户名称:" class="labeltext mulitline">
                              <span>{{formInfo.customerName}}</span>
                          </el-form-item>
                          <el-form-item label="部门:" class="labeltext">
                            <span>{{formInfo.unitDepartment}}</span>
                          </el-form-item>
                          <el-form-item label="客户级别:" class="labeltext mulitline">
                            <span :class="levelColor[formInfo.customerLevelName]">{{formInfo.customerLevelName}}</span>
                            </el-form-item>
                            <el-form-item label="负责课程:" class="labeltext">
                              <span>{{formInfo.responsibleCourse}}</span>
                          </el-form-item>
                      </el-col>
                      <el-col :span="12">  
                          <el-form-item label="客户单位:" class="labeltext">
                                <span>{{formInfo.unitName}}</span>
                          </el-form-item>
                          <el-form-item label="职务:" class="labeltext">
                              <span class="colorcss">{{formInfo.duties}}</span>
                          </el-form-item>
                          <el-form-item label="负责专业:" class="labeltext">
                            <span>{{formInfo.specialtyName}}</span>
                        </el-form-item>
                      </el-col>
                  </el-row>
                  <textBorder>客户标签</textBorder>
                  <el-row :gutter="20" class="width100 mtop20 bbline mb30" >
                      <el-col :span="24" class="mb20 ml40">
                          <span class="tagitemcss" :style="colors[index]" :key="index" v-for="(item,index) in formInfo.tags">{{item}}</span>
                      </el-col>
                  </el-row>
                  <textBorder>联系信息</textBorder>
                  <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
                      <el-col :span="12" class="mb10">
                          <el-form-item label="电话:" class="labeltext mulitline">
                              <span>{{formInfo.phone}}</span>
                          </el-form-item>
                          <el-form-item label="微信:" class="labeltext mulitline">
                            <span>{{formInfo.wechat}}</span>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12" class="mb10">  
                          <el-form-item label="邮箱:" class="labeltext mulitline">
                          <span>{{formInfo.mailbox}}</span>
                          </el-form-item>
                      </el-col>
                  </el-row>
                  <textBorder>负责与协作</textBorder>
                  <el-row :gutter="20" class="width100 mt10 mb30 bbline">
                      <el-col :span="8" class="mb10">
                          <el-form-item label="负责人:" class="labeltext">
                              <span>{{formInfo.chargePersonName}}</span>
                          </el-form-item>
                      </el-col>
                      <el-col :span="8"  class="mb10">
                          <el-form-item label="协作人:" class="labeltext">
                              <span>{{formInfo.collaboratorName}}</span>
                          </el-form-item>
                      </el-col>
                  </el-row>
              </el-form>
              </el-tab-pane>
              <el-tab-pane  label="历史用书情况" name="second">
                <div class="grid-content bg-purple">
                  <el-table class="unittable mytable" :data="tableData" >
                    <el-table-column prop="materialName" label="教材名称" align="center">
                    </el-table-column>
                    <el-table-column prop="platformName" label="出版社" align="center">
                    </el-table-column>
                    <el-table-column prop="author" label="主编" align="center">
                    </el-table-column>
                    <el-table-column prop="price" label="价格" align="center">
                    </el-table-column>
                    <el-table-column prop="bookNumber" label="用书量" align="center">
                    </el-table-column>
                    <el-table-column prop="useBookYear" label="用书时间" align="center">
                    </el-table-column>
                    <el-table-column prop="bookSpecialtyName" label="用书专业" align="center">
                    </el-table-column>
                    <el-table-column prop="isJoin" label="类型" align="center" >
                      <template slot-scope="scope">
                          <span>{{typeObj[scope.row.isJoin]}}</span>
                      </template>
                    </el-table-column>
                    <template slot="empty">
                      <nolist></nolist>
                    </template>
                  </el-table>
                  <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                    @updatePageNum="handleCurrentChange"></page>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
      </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>

  import page from '../../common/page.vue';
  import nolist from '../../common/nolist.vue';
  import { unitSD,customerbook} from '@/api/clientmanagement/unit'
  import back from '../../common/back.vue'
  import textBorder from '../../common/textBorder.vue'
  import {cusListAll } from '@/api/unit'
  import {customerInfo } from "@/api/clientmanagement/customer";
  import { customerLevelColors } from "@/utils/dict";
  export default {
    components: {
      page,
      nolist,
      back,
      textBorder,
    },
    data() {
      return {
        levelColor:customerLevelColors,
        formInfo:{},
        isLoading: false,
        tableData: [],
        total: 0,
        totalC:0,
        pageBean: {
          unitName: '',
          pageNum: 1,
          pageSize: 6,
        },
        pageBeanC:{
          structureId: '',
          customerName: '',
          customerLevel: ''
        },
        gender:{
          1:'男',
          2:'女'
        },
        activeName:'first',
        dataList: [],
        sData:{},
        colors:[
            {
                color:'#FD7A41',
                background:'#FFECE3'
            },
            {
                color:'#4285F4',
                background:'#DFEAFD'
            },
            {
                color:'#FFA44C',
                background:'#FFF2E5'
            },
            {
                color:'#AC6FFB',
                background:'#F0E6FE'
            },
            {
                color:'#67C23A',
                background:'#E6F3D8'
            },
          ],
          selectedArrData: [], 
          pageR:{
            pageNum: 1,
            pageSize: 6,
            customerId: '',
          },
          typeObj:{
                    1:'过往合作',
                    3:'历史发货',
                    2:'非合作'
                },
      }
    },
    created() {
      this.unitSDAPI()
      this.pageBeanC.structureId = this.$route.query.sId
      this.loadData();
      this.loadDataInfo(this.$route.query.id)
      this.pageR.customerId = this.$route.query.id
      this.customerbookApi()

    },
    methods: {
      customerbookApi(){
        customerbook(this.pageR).then(res=>{
          if(res.status == 0){
                this.tableData = res.data
                this.total = res.page.total
          }
        })
      },
      isRed({ row }) {
          const checkIdList = this.selectedArrData.map((item) => item.id);
          if (checkIdList.includes(row.id)) {
            return {
              backgroundColor: "#4285F4",
              color: "#fff",
            };
          }
    },
    rowClickEv(row) {
      this.selectedArrData = [row];
      this.loadDataInfo(row.id)
      this.pageR.customerId = row.id
      this.pageR.pageNum = 1
      this.pageBeanC.pageNum = 1
      this.customerbookApi()
    },
      loadDataInfo(cid){
            customerInfo(cid).then((result) => {
                this.formInfo = result.data;
                this.formInfo.specialtyName =this.formInfo.specialtyName.join(',')
                this.formInfo.tags = this.formInfo.label && this.formInfo.label.split(',')
            }).catch((err) => {
                
            });
        },
      unitSDAPI(){
        unitSD(this.$route.query.sId).then(res=>{
            if(res.status == 0){
                this.sData = res.data
            }
        })
      },
      handleClick(tab, event) {
                this.activeName = tab.name;
        },
      async loadData() {
        this.isLoading = true;
        let res = await cusListAll(this.pageBeanC)
        if (res.status === 0) {
          this.dataList = res.data
          this.dataList.forEach(item=>{
            if(item.id==this.$route.query.id){
              this.selectedArrData = [item];
            }
          })
          this.isLoading = false
        } else {
          this.$message.error(`${res.msg}`)
          this.isLoading = false
        }
      },
      handleCurrentChange(page) {
        this.pageR.pageNum = page;
        this.customerbookApi()
      },
      handleCurrentChangeC(page){
        this.pageBeanC.pageNum = page;
        this.loadData();
      }
    }
  }

</script>

<style scoped>
  .sleft{
  }
  .infoform{
    height: 654px;
    overflow-y: auto;
  }
  .tagitemcss{
    font-size: 12px;
    padding:4px 8px;
    margin: 0px 4px;
    background-color: #DFEAFD;
    border-radius: 2px;
    color: #4285F4;
}
  .lefttable{
    height: 743px;
    overflow-y: auto;
    overflow-x: hidden;
  }
   .table-style >>> .el-table__body tr:hover > td {
      background-color: #4285F4 !important;
      color: #fff;
    }
  .ml40{
    margin-left: 40px;
  }
  .mb20{
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .lle{
    margin-left: 20px;
  }
  .ren{
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .spanc{
    height: 40px;
    line-height: 40px;
  }
  .pti{
    line-height: 20px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 12px;
  }
  .backdiv{
    margin-bottom: 12px;
  }
  .definput{
    width: 180px;
  }
  .page-header {
    display: flex;
    justify-content: space-between;
  }

  .unittable .el-button+.el-button {
    margin-left: 40px;
  }

  .tabbtn {
    font-size: 14px;
    cursor: pointer;
    padding: 2px;
  }

  .mbtn {
    margin-right: 40px;
  }

  .mbtn,
  .mbtn:hover,
  .mbtn:focus {
    color: #FF8D1A !important;
  }

  .bbtn,
  .bbtn:hover,
  .bbtn:focus {
    color: #4285F4;
  }

  .rbtn,
  .rbtn:hover,
  .rbtn:focus {
    color: #F45961;
  }

  .btn {
    height: 34px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FFFFFF;
    letter-spacing: 1px;
  }

  .search {
    width: 96px;
  }

  .mainbg {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
  }
  .el-form-item {
    margin-right: 16px !important;
    margin-bottom: 0px  !important;
  }

</style>