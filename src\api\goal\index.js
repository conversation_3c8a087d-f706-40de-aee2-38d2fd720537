import service from '@/utils/request.js'

//  获取年度列表
export function getYearList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/personalgoals/getYearList',
    params
  })
}


export function personSummary(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/personalgoals/personSummary',
    params
  })
}

// 新增目标
export function addpersonalgoals(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/personalgoals/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 编辑目标
export function updatepersonalgoals(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/personalgoals/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}



export function updateSummarize(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/personalgoals/updateSummarize',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 目标详情
export function personalgoalsInfo(data) {
  return service.request({
    method: 'post',
    url: `/crm/controller/personalgoals/detail`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


// 计划详情
export function jiHuaInfo(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/personalgoals/info`,
    params
  });
}



// 删除  /crm/controller/personalgoalsunit/delete
export function personalgoalsunitDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/personalgoalsunit/delete',
    data,
  });
}

// 目标排行
export function achieveGoalVoList(data) {
  return service.request({
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    url: `/crm/controller/personalgoalsdetail/achieveGoalVoList`,
    data
  });
}
// 汇总
export function achieveGoalsSummary(data) {
  return service.request({
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    url: `/crm/controller/personalgoalsdetail/achieveGoalsSummary`,
    data
  });
}
// 个人目标
export function personGoals(data) {
  return service.request({
    method: 'post',
    url: `/crm/controller/personalgoalsdetail/personGoals`,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
}

export function goalSetView(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/personalgoalsunit/queryVoList`,
    params
  });
}

export function departmentGoals(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/personalgoalsdetail/departmentGoals`,
    params
  });
}


// /crm/controller/personalgoals/list
/**
 * 计划列表
 * @param {*} params
 * @returns
 */
export function personalgoalsList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/personalgoals/list`,
    params
  });
}


export function queryCompanyDepartment(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/personalgoals/queryCompanyDepartment`,
    params
  });
}

// /crm/controller/personalgoals/delete
/**
 * 计划删除
 * @param {*} data
 * @returns
 */
export function personalgoalsdelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/personalgoals/delete',
    data,
  });
}

//  审核列表
export function toCheckList(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/personalgoals/toCheckList`,
    params
  });
}

/**
 * 审核
 * @param {} data
 * @returns
 */
export function check(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/personalgoals/check',
    data,
    // headers: {
    //   'Content-Type': 'application/json;charset=UTF-8'
    // },
  });
}
export function queryReviewGoals(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/personalgoals/queryReviewGoals`,
    params
  });
}

export function goalssummarylogs(params) {
  return service.request({
    url: `/crm/business/goalssummarylogs/list`,
    method: 'get',
    params
  })
}
