<template>
  <div>
    <el-form class="infoform" ref="form" :model="form" label-width="120px">
      <div class="comdegree">
        信息完成度：
        <el-rate
          :max="3"
          class="derate"
          v-model="form.starLevel"
          disabled
          text-color="#ff9900"
          score-template="{value}"
        >
        </el-rate>
      </div>
      <textBorder>客户信息</textBorder>
      <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
        <el-col :span="12">
          <el-form-item label="客户名称:" class="labeltext mulitline">
            <span>{{ form.customerName }}</span>
          </el-form-item>
          <el-form-item label="部门:" class="labeltext">
            <span>{{ form.unitDepartment }}</span>
          </el-form-item>
          <el-form-item label="客户级别:" class="labeltext">
            <span :class="customerLevelColors[form.customerLevelName]">{{
              form.customerLevelName
            }}</span>
          </el-form-item>
          <el-form-item label="负责课程:" class="labeltext">
            <span>{{ form.responsibleCourse }}</span>
          </el-form-item>
          <el-form-item label="客户照片:" class="labeltext">
            <span>
              <el-image
                class="customerImg"
                :src="form.imageUrl"
                :preview-src-list="srcList"
              >
              </el-image>
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户单位:" class="labeltext">
            <span>{{ form.unitName }}</span>
          </el-form-item>
          <el-form-item label="职务:" class="labeltext">
            <span>{{ form.duties }}</span>
          </el-form-item>
          <el-form-item label="负责专业:" class="labeltext">
            <span>{{ form.specialtyName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <textBorder>联系信息</textBorder>
      <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
        <el-col :span="12" class="mb10">
          <el-form-item label="电话:" class="labeltext">
            <span>{{ form.phone }}</span>
          </el-form-item>
          <el-form-item label="邮箱:" class="labeltext">
            <span>{{ form.mailbox }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="mb10">
          <el-form-item label="微信:" class="labeltext">
            <span>{{ form.wechat }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <textBorder>客户标签</textBorder>
      <div class="width100 mtop30 bbline mb30 tagcss">
        <span
          class="tagitemcss"
          :style="colors[index]"
          :key="index"
          v-for="(item, index) in form.tags"
          >{{ item }}</span
        >
      </div>
      <textBorder>数据画像</textBorder>
      <!-- <el-row :gutter="20" class="width100 mt10 pb5">
            <el-col :span="12" class="">
                <el-form-item label="性别:" class="labeltext">
                    <span>{{gender[form.sex]}}</span>
                </el-form-item>
                <el-form-item label="年龄:" class="labeltext">
                    <span>{{form.age}}</span>
                </el-form-item>
                <el-form-item label="民族:" class="labeltext">
                    <span>{{form.nationName}}</span>
                </el-form-item>
                <el-form-item label="职称:" class="labeltext">
                    <span>{{form.professionalTitles}}</span>
                </el-form-item>
                <el-form-item label="工作地址:" class="labeltext mulitline">
                    <span>{{form.address}}</span>
                </el-form-item>
                <el-form-item label="家庭地址:" class="labeltext mulitline">
                <span>{{form.homeAddress}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="mb10">
                <el-form-item label="身份证号:" class="labeltext mulitline">
                    <span>{{form.cardNumber}}</span>
                </el-form-item>
                <el-form-item label="籍贯:" class="labeltext mulitline">
                    <span>{{form.nativePlace}}</span>
                </el-form-item>
                <el-form-item label="生日:" class="labeltext">
                    <span>{{form.birthday}}</span>
                </el-form-item>
                <el-form-item label="职称时间:" class="labeltext">
                    <span>{{form.professionalTime}}</span>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="width100 mt10 mb30 bbline">
            <el-col :span="24">
                <el-form-item label="爱好情况:" class="labeltext mulitline">
                    <span>{{form.hobby}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="论文课题:" class="labeltext mulitline">
                    <span>{{form.thesisTopic}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="研究方向:" class="labeltext mulitline">
                    <span>{{form.researchDirection}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="车辆信息:" class="labeltext mulitline">
                            <span>{{form.carInfo}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="车牌号:" class="labeltext mulitline">
                            <span>{{form.carNumber}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-col>
            <el-col :span="24" >
                 <el-form-item label="婚恋情况:" class="labeltext mulitline">
                    <span>{{form.marriage}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item label="在意的人:" class="labeltext mulitline">
                    <p v-for="(item,index) in inputTags1" :key="index">{{item}}</p>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="mb10">
                <el-form-item label="子女情况:" class="labeltext mulitline">
                    <span>{{form.children}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="mb10">
                <el-form-item label="备注:" class="labeltext mulitline">
                    <span>{{form.notes}}</span>
                </el-form-item>
            </el-col>
            <el-col :span="24" class="
            ">
                <el-form-item label="附件:" class="labeltext mulitline">
                    <div class="filescss">
                        <div class="fileitem" v-for="item in form.fileInfoEntities" :key="item.id" @click="viewPdf(item)">{{item.fileName}}</div>
                    </div>
                </el-form-item>
            </el-col>
        </el-row> -->
      <div class="box">
        <img
          src="@/assets/girl.png"
          v-if="form.sex === 2"
          alt=""
          class="boyimg"
        />
        <img src="@/assets/boy.png" v-else alt="" class="boyimg" />

        <div class="infodiv">
          <div class="tagdiv">个人信息</div>
          <div class="dashed-line bluecolor"></div>
          <div class="mt20px">
            <div class="spanitem">
              <span class="spanlabel"> 性别: </span>
              <span>{{ gender[form.sex] || '--' }}</span>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 籍贯: </span>
              <span>{{ form.nativePlace || '--' }}</span>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 民族: </span>
              <span>{{ form.nationName || '--' }}</span>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 出生年月：</span>
              <span>{{ form.birthday || '--' }}</span>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 年龄:</span>
              <span>{{ form.age || '--' }}</span>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 婚恋情况:</span>
              <el-tooltip
                popper-class="maxWidth"
                effect="dark"
                :content="form.marriage"
                placement="top-start"
              >
                <span class="w250" id="overflowhidden">{{
                  form.marriage || '--'
                }}</span>
              </el-tooltip>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 爱好</span>
              <el-tooltip
                popper-class="maxWidth"
                effect="dark"
                :content="form.hobby"
                placement="top-start"
              >
                <span class="w250" id="overflowhidden2">{{ form.hobby || '--' }}</span>
              </el-tooltip>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 身份证号:</span>
              <span>{{ form.cardNumber || '--' }}</span>
            </div>
          </div>
        </div>

        <div class="infodiv career">
          <div class="tagdiv tagyellow">职业信息</div>
          <div class="dashed-line yellowcolor"></div>
          <div class="mt20px">
            <div class="spanitem">
              <span class="spanlabel"> 职称: </span>
              <span>{{ form.professionalTitles || '--' }}</span>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 职称时间: </span>
              <span>{{ form.professionalTime || '--' }}</span>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 论文课题: </span>
              <el-tooltip
                popper-class="maxWidth"
                effect="dark"
                :content="form.thesisTopic"
                placement="top-start"
              >
                <span class="valuespan" id="overflowhidden3">{{
                  form.thesisTopic || '--'
                }}</span>
              </el-tooltip>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 工作地址:</span>
              <!-- <span>{{ form.address }}</span> -->
              <el-tooltip
                popper-class="maxWidth"
                effect="dark"
                :content="form.address"
                placement="top-start"
              >
                <span class="valuespan" id="overflowhidden4">{{
                  form.address || '--'
                }}</span>
              </el-tooltip>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 研究方向:</span>
              <el-tooltip
                popper-class="maxWidth"
                effect="dark"
                :content="form.researchDirection"
                placement="top-start"
              >
                <span class="valuespan" id="overflowhidden5">{{
                  form.researchDirection || '--'
                }}</span>
              </el-tooltip>
            </div>
          </div>
        </div>

        <div class="infodiv family">
          <div class="tagdiv taggreen">家庭信息</div>
          <div class="dashed-line greencolor"></div>
          <div class="mt20px">
            <div class="spanitem">
              <span class="spanlabel"> 车辆信息: </span>
              <el-tooltip
                popper-class="maxWidth"
                effect="dark"
                :content="form.carInfo"
                placement="top-start"
              >
                <span class="valuespan" id="overflowhidden7">{{
                  form.carInfo || '--'
                }}</span>
              </el-tooltip>
            </div>
            <div class="spanitem">
              <span class="spanlabel"> 车牌号: </span>
              <el-tooltip
                popper-class="maxWidth"
                effect="dark"
                :content="form.carNumber"
                placement="top-start"
              >
                <span class="valuespan" id="overflowhidden8">{{
                  form.carNumber || '--'
                }}</span>
              </el-tooltip>
            </div>

            <div class="spanitem">
              <span class="spanlabel"> 家庭住址:</span>
              <el-tooltip
                popper-class="maxWidth"
                effect="dark"
                :content="form.homeAddress"
                placement="top-start"
              >
                <span class="valuespan" id="overflowhidden6">{{
                  form.homeAddress || '--'
                }}</span>
              </el-tooltip>
            </div>
            <div class="spanitem2">
              <span class="spanlabel"> 在意的人: </span>
              <span>
                <p v-for="(item, index) in inputTags1" :key="index">
                  {{ item }}
                </p></span
              >
            </div>
          </div>
        </div>
      </div>

      <el-row class="clearfix">
        <el-col :span="24" class="mb10">
          <el-form-item label="备注:" class="labeltext mulitline">
            <span>{{ form.notes }}</span>
          </el-form-item>
        </el-col>
        <el-col
          :span="24"
          class="
            "
        >
          <el-form-item label="附件:" class="labeltext mulitline">
            <div class="filescss">
              <div
                class="fileitem"
                v-for="item in form.fileInfoEntities"
                :key="item.id"
                @click="viewPdf(item)"
              >
                {{ item.fileName }}
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <textBorder>负责与协作</textBorder>
      <el-row :gutter="20" class="width100 mt10 mb30 bbline">
        <el-col :span="24" class="mb10">
          <el-form-item label="负责人:" class="labeltext">
            <el-tag
              class="cuscss"
              v-for="(item, index) in form.chargePersonNames"
              :key="index"
              >{{ item }}</el-tag
            >
          </el-form-item>
        </el-col>
      </el-row>
      <textBorder>系统信息</textBorder>
      <el-row :gutter="20" class="mt10">
        <el-col :span="12">
          <el-form-item label="创建人:" class="labeltext">
            <span>{{ form.createByName || '--'}}</span>
          </el-form-item>
          <el-form-item label="创建时间:" class="labeltext">
            <span>{{ form.createTime || '--'}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最后修改人:" class="labeltext">
            <span>{{ form.modifyByName || '--'}}</span>
          </el-form-item>
          <el-form-item label="最后修改时间:" class="labeltext">
            <span>{{ form.modifyTime || '--' }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <fileview ref="fileview"></fileview>
  </div>
</template>

<script>
import textBorder from '../../common/textBorder.vue'
import { customerInfo } from '@/api/clientmanagement/customer'
import { customerLevelColors } from '@/utils/dict'
import fileview from '../../common/fileview.vue'
import { overflowhidden } from '@/utils/tools'
export default {
  data() {
    return {
      value: 1,
      customerLevelColors: customerLevelColors,
      colors: [
        {
          color: '#FD7A41',
          background: '#FFECE3',
        },
        {
          color: '#4285F4',
          background: '#DFEAFD',
        },
        {
          color: '#FFA44C',
          background: '#FFF2E5',
        },
        {
          color: '#AC6FFB',
          background: '#F0E6FE',
        },
        {
          color: '#67C23A',
          background: '#E6F3D8',
        },
      ],
      form: {},
      gender: {
        1: '男',
        2: '女',
      },
      inputTags1: [],
      srcList: [],
    }
  },
  components: {
    textBorder,
    fileview,
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      customerInfo(this.$route.query.id)
        .then((result) => {
          this.form = result.data
          this.form.sex = this.form.sex == 0 ? '' : this.form.sex
          this.form.age = this.form.age == 0 ? '' : this.form.age
          this.form.tags =
            this.form.label.length > 0 ? this.form.label.split(',') : []
          this.inputTags1 =
            result.data.importPeople && result.data.importPeople.split(',')
          this.form.specialtyName = this.form.specialtyName.join(',')
          this.$store.commit('cus/CHANGE_KEY', {
            key: 'isTop',
            value: result.data.top,
          })
          this.overflowH('overflowhidden')
          this.overflowH('overflowhidden2')
          this.overflowH('overflowhidden3')
          this.overflowH('overflowhidden4')
          this.overflowH('overflowhidden5')
          this.overflowH('overflowhidden6')
          this.overflowH('overflowhidden7')
          this.overflowH('overflowhidden8')
          this.$nextTick(()=>{
            document.querySelector('.box').style.height = `${document.querySelector('.family').offsetHeight +  350}px`
          })
          this.srcList = [this.form.imageUrl]
        })
        .catch((err) => {})
    },
    overflowH(elid) {
      this.$nextTick(() => {
        var text = document.getElementById(elid)
        var str = text.innerHTML
        overflowhidden(elid, 2, str)
      })
    },
    viewPdf(item) {
      this.$refs.fileview.show(item)
    },
  },
}
</script>
<style>
.maxWidth {
  max-width: 400px;
}
</style>

<style lang="scss" scoped>
.clearfix{
  clear: both;
}
.customerImg {
  width: 120px;
}
.mtop30 {
  margin-top: 30px;
}
.tagcss {
  height: 50px;
  margin-left: 30px;
}
.w250 {
  display: inline-block;
  width: 250px;
  vertical-align: top;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 18px;
}
.valuespan {
  display: inline-block;
  width: 360px;
  vertical-align: top;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 18px;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 限制文本为两行 */
  overflow: hidden;
}
.mt20px {
  margin-top: 20px;
}
.spanlabel {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  margin-right: 12px;
  min-width: 60px;
}
.mt20 {
  margin-top: 20px;
}
.spanitem {
  padding-left: 10px;
  display: flex;
  min-height: 18px;
  max-height: 42px;
  line-height: 18px;
  margin-bottom: 16px;
}
.spanitem2{
  padding-left: 10px;
  display: flex;
  min-height: 18px;
  line-height: 18px;
  margin-bottom: 16px;
}
.infodiv {
  position: absolute;
  left: calc(50% - 400px);
  top: 70px;
}
.career {
  position: absolute;
  left: calc(50% + 220px);
  top: 40px;
  width: calc(50% - 220px);
}
.family {
  position: absolute;
  left: calc(50% + 220px);
  top: 350px;
  width: calc(50% - 220px);
}
.dashed-line {
  position: relative;
  width: 202px;
  height: 1px;
  background: linear-gradient(
    to left,
    transparent 0%,
    transparent 50%,
    #4285f4 50%,
    #4285f4 100%
  );
  background-size: 10px 1px;
  background-repeat: repeat-x;
  padding-left: 6px;
  left: 110px;
  top: -15px;
}

.dashed-line::before {
  content: '';
  position: absolute;
  right: 208px;
  top: -2px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #4285f4;
}

.yellowcolor::before {
  background-color: #fd7a41;
  right: -10px;
  top: -2px;
}
.yellowcolor {
  background: linear-gradient(
    to right,
    transparent 0%,
    transparent 50%,
    #fd7a41 50%,
    #fd7a41 100%
  );
  background-size: 10px 1px;
  background-repeat: repeat-x;
  left: -144px;
  top: -15px;
  width: 121px;
}
.tagyellow {
  background-color: #fd7a41 !important;
}
.taggreen {
  background-color: #44ce62 !important;
}

.greencolor::before {
  background-color: #44ce62;
  right: -10px;
  top: -2px;
}
.greencolor {
  background: linear-gradient(
    to right,
    transparent 0%,
    transparent 50%,
    #44ce62 50%,
    #44ce62 100%
  );
  background-size: 10px 1px;
  background-repeat: repeat-x;
  left: -144px;
  top: -15px;
  width: 121px;
}
.tagdiv {
  width: 88px;
  height: 30px;
  background: #4285f4;
  border-radius: 99px 99px 99px 99px;
  text-align: center;
  line-height: 30px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}

.filescss {
  display: flex;
  margin-bottom: 20px;
}
.fileitem + .fileitem {
  margin-left: 20px;
}
.fileitem {
  padding: 0 16px;
  height: 32px;
  line-height: 30px;
  font-size: 12px;
  color: #4285f4;
  border-radius: 15px;
  border: #4285f4 dashed 1px;
  cursor: pointer;
}
.fujian {
  width: 100px;
  margin-right: 10px;
}
.derate /deep/ .el-rate__icon {
  font-size: 25px;
}

.comdegree {
  position: absolute;
  right: 0;
  top: 0;
  width: 182px;
}
.derate {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  top: -4px;
}
.lh {
  width: 100%;
  line-height: 23px;
}
.colorcss {
  color: #f45961;
}
.tagitemcss {
  font-size: 16px;
  padding: 14px 24px;
  margin: 0px 8px;
  background-color: #dfeafd;
  border-radius: 2px;
  color: #4285f4;
  border-radius: 8px 8px 8px 8px;
}
.mtop20 {
  margin-top: 20px;
}
.ml40 {
  margin-left: 40px;
}
.box {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  min-height: 550px;
}
.boyimg {
  height: 438px;
}
.leftInfo {
  position: absolute;
  left: 150px;
  top: 0;
}
.leftInfo2 {
  position: absolute;
  left: 150px;
  top: 35%;
}
.leftInfo3 {
  position: absolute;
  left: 150px;
  top: 65%;
}
.rightInfo {
  position: absolute;
  right: 150px;
  top: 0;
}
.rightInfo2 {
  position: absolute;
  right: 150px;
  top: 25%;
}
.rightInfo3 {
  position: absolute;
  right: 150px;
  top: 45%;
}
.rightInfo4 {
  position: absolute;
  right: 150px;
  top: 65%;
}
.notes {
  position: absolute;
  bottom: 10px;
  left: 150px;
}
.leftItem {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #333;
}
.rightItem {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #333;
}
.itemBox {
  height: 50px;
  background-color: #50d0e0;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  padding: 0 10px;
  box-sizing: border-box;
}
.line-container {
  position: relative;
  height: 200px;
  width: 300px;
}
.line {
  position: absolute;
  top: 50px;
  left: 50px;
  width: 200px;
  height: 100px;
  border-bottom: 2px solid blue; /* 水平线段 */
  border-left: 2px solid blue; /* 垂直线段 */
}
</style>
<style scoped>
.infoform /deep/.el-form-item {
  margin-bottom: 0px;
}
.mulitline /deep/.el-form-item__content {
  padding-top: 10px;
  line-height: 20px;
}
.infoform {
  position: relative;
}
</style>
