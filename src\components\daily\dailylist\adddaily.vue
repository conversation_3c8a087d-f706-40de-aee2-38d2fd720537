<template>
  <div>
    <back>写日报</back>
    <div class="maindiv">
      <div class="owntop" :class="{ w60: isShow }">
        <div class="personalplan">
          <el-form
             label-position="top"
             class="mt elform myform"
            :model="formLabelAlign"
          >
           <addmodelcom ref="modelCom" @headdenForm="headdenForm"  @setPeople="setPeople"></addmodelcom>
           <el-form-item
           label="关联客户："
           >
           <div class="pflex">
              <ul class="uflex">
                <li class="cli" v-for="(item,index) in customerList" :key="index">
                  <p>
                    <span>{{item.unitName}}-</span>
                    <span>{{item.customerName}}</span>
                    <i class="el-icon-close cclose" @click="deleteCustomer(index)"></i>
                  </p>
                </li>
                <li class="adddi" @click="chooseCustomer">
                  <i class="el-icon-plus iadd"></i>
                </li>
              </ul>
           </div>
           </el-form-item>
           <el-form-item
           v-if="allowCopy==2"
           label="抄送人："
           >
              <div class="pflex">
                <ul class="uflex">
                  <li v-for="(item,index) in peopleList" :key="index" class="pli">
                     <img @click="closeTag(item.id)" src="../../../assets/close.png" alt="" class="closeimg">
                     <img v-if="item.logo" class="pimg" :src="item.logo" alt="">
                     <div class="noimg" v-else>
                        {{item.name}}
                     </div>
                     <p class="pname">{{item.name}}</p>
                  </li>
                  <li class="adddi" @click="clickXuan">
                    <i class="el-icon-plus iadd"></i>
                  </li>
                </ul>
              </div>
           </el-form-item>
          </el-form>
        </div>
        <div class="submitmask">
          <el-button
            class="defaultbtn"
            type="primary"
            @click="saveGoalAction"
            v-dbClick
            >保存</el-button
          >
        </div>
      </div>
      <div class="rightdiv " :class="{ w40: isShow }">
        <div class="repadd" v-if="isReview">
          <div class="closediv">
            <img @click="closeC" src="../../../assets/close.png" alt="">
          </div>
          <p class="rti">今日工作回顾</p>
          <el-row :gutter="20">
            <el-col :span="8"  v-for="(item,index) in list" :key="index">
              <dataitem :item="item"></dataitem>
            </el-col>
          </el-row>
          <!-- 1，跟进拜访；2客户。3合同；4回款；5发放 -->
          <div class="con" v-if="timeData.length>0">
            <h2 class="title">今日时间轴</h2>
            <div class="timeline-con" >
              <div class="timeline-post" v-for="(item,index) in timeData" :key="index">
                <div class="timeline-icon-con">
                </div>
                <div class="timeline-content">
                  <h3 class="ititle">{{typeToName[item.type]['title']}}</h3>
                  <p class="vcustomer" v-if="item.type !=4">{{typeToName[item.type]['label']}}-{{item.customerName}}</p>
                  <p class="vcustomer" v-if="item.type ==4">{{typeToName[item.type]['label']}}-{{item.contractName}}</p>
                  <p class="vcustomer" v-if="item.type ==3">{{typeToName[item.type]['label1']}}-{{item.contractName}}</p>
                  <p class="vcustomer" v-if="item.type ==4">{{typeToName[item.type]['label1']}}-{{item.amount}}万元</p>
                  <p class="vtime">{{item.createTime}}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="isShow" class="reviewmask">
          <planreview ref="planreview" v-if="reviewType == 'planreview'" @closereview="closereview"
            :plantype="form.goalsType" :viewData="viewData" :page="pageData" :date="planTime">
          </planreview>
          <summaryreview v-if="reviewType == 'summaryreview'" @closereview="closereview"
            :plantype="form.goalsType" :viewData="viewData" :page="pageData" :date="planTime">
          </summaryreview>
        </div>
        <div v-else class="tright">
          <div class="bbtn" id="planreview" @click="toReview" >
            <i class="el-icon-notebook-2" alt="" />
            今日工作回顾
          </div>
          <div class="bbtn" id="planreview" @click="showView" >
            <i class="el-icon-notebook-2" alt="" />
            计划回顾
          </div>
          <div class="bbtn" id="summaryreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            总结回顾
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="选择回顾时间" :visible.sync="dialogPlanVisible" append-to-body width="340px" center :before-close="handlePlanClose">
      <span>
        <el-form class="mt30" label-width="85px">
          <el-form-item :label="
          `${reviewType == 'summaryreview' ?'总结':'计划'}类型：`">
            <el-select class="definput width200"  v-model="form.goalsType" placeholder="请选择" @change="changeType">
            <el-option
              v-for="item in options[reviewType]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          </el-form-item>
          <el-form-item v-if="form.goalsType" :label="
              `${
                types[form.goalsType].label
              }：`
            ">
            <el-date-picker class="definput width200" :picker-options="pickerOptions" @change="changeDatePicker"
              v-model="planTime" :type="types[form.goalsType].type" :format="types[form.goalsType].format"
              placeholder="请选择">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextAction">下一步</el-button>
      </span>
    </el-dialog>
        <departmentDialogDaily  ref="deRef" dType="2" v-if="dialogDepartmentVisible" :dialogDepartmentVisible="dialogDepartmentVisible" @updateVisible="updateSystemVisible"
        @submitData="submitData">
        </departmentDialogDaily>
            <customerDialogMultiple ref="customer" :visible.sync="customerDialogVisible" @updateVisible="updateVisible"
            @updateCustomer="updateCustomer">
        </customerDialogMultiple>
  </div>
</template>

<script>
import back from "@/components/common/back.vue";
import planreview from "@/components/common/planreview.vue";
import summaryreview from "@/components/common/summaryreview.vue";
import departmentDialogDaily from '@/components/common/departmentDialogDaily.vue';
import {
  saveDailyRecord,
  queryDailyReview
} from "@/api/daily";
import dataitem from './dataitem.vue';
import {
    queryReviewGoals,
  } from "@/api/goal";
import { removeById } from '@/utils/tools.js'
import { getWeek } from '@/utils/index.js'
import addmodelcom from "./addmodelcom.vue";
import customerDialogMultiple from '../../common/customerDialogMultiple.vue';
export default {
  components: {
    back,
    planreview,
    summaryreview,
    addmodelcom,
    departmentDialogDaily,
    customerDialogMultiple,
    dataitem
  },
  data() {
    return {
      typeToName:{
            0:{
              title:'',
              label:'',
              label1:'',
            }, 
            1:{
              title:'跟进拜访',
              label:'拜访客户',
              label1:'',
            }, 
            2:{
              title:'新增客户',
              label:'新增客户',
              label1:'',
            }, 
            3:{
              title:'合同新增',
              label:'客户',
              label1:'合同',
            }, 
            4:{
              title:'回款',
              label:'回款合同',
              label1:'金额',
            }, 
            5:{
              title:'样书发放',
              label:'发放客户',
              label1:'',
            }, 
      },
      toObj:{
        0:{
          finishGoal:'visit'
        },
        1:{
          finishGoal:'customer'
        },
        2:{
          finishGoal:'deliverNum'
        },
        3:{
          finishGoal:'contract'
        },
        4:{
          finishGoal:'returnAmount'
        },
        5:{
          finishGoal:'contractAmount'
        },
      },
      list:[
              {id:1,name:"拜访客户",finishGoal:0,icon:require('../../../assets/img2.png'),unit:"次",color:"#4A8BF6",},
              {id:2,name:"新增客户",finishGoal:0,icon:require('../../../assets/img5.png'),unit:"个",color:"#F46D40",},
              {id:3,name:"样书发放",finishGoal:0,icon:require('../../../assets/yang.png'),unit:"次",color:"#56C36E",},
              {id:4,name:"合同新增",finishGoal:0,icon:require('../../../assets/img/hetong_icon.png'),unit:"个",color:"#FF8D1A",},
              {id:5,name:"回款金额",finishGoal:0,icon:require('../../../assets/img4.png'),unit:"万元",color:"#4A8BF6",},
              {id:6,name:"业绩",finishGoal:0,icon:require('../../../assets/img3.png'),unit:"万元",color:"#E85D5D",},
            ],
      customerList:[],
      customerDialogVisible:false,
      dialogDepartmentVisible:false,
      pickerOptions: {
        disabledDate: this.endPickerTime
      },
      isShow: false,
      isLoading: false,
      dialogPlanVisible: false,
      year: new Date(this.$route.query.time).getFullYear(),
      tableData: [],
      planTime: "",
      options:{
          'planreview':[
            {label:'年计划',value:'1'},
            {label:'月计划',value:'2'},
            {label:'周计划',value:'3'}
          ],
          'summaryreview':[
            {label:'年总结',value:'4'},
            {label:'月总结',value:'5'},
            {label:'周总结',value:'6'}
          ]
        },
      types: {
        "1": {
          name: "年计划",
          label: "年度计划",
          type: "year",
          format: "yyyy"
        },
        "2": {
          name: "月计划",
          label: "月度计划",
          type: "month",
          format: "yyyy-M"
        },
        "3": {
          name: "周计划",
          label: "周计划",
          type: "week",
          format: "yyyy 第 W 周"
        },
        "4": {
          name: "年总结",
          label: "年度总结",
          type: "year",
          format: "yyyy"
        },
        "5": {
          name: "月总结",
          label: "月度总结",
          type: "month",
          format: "yyyy-M"
        },
        "6": {
          name: "周总结",
          label: "周总结",
          type: "week",
          format: "yyyy 第 W 周"
        }
      },
      form: {
          goalsType: '',
          createBy:sessionStorage.getItem('userid'),
          year: "",
          month: "",
          week: "",
          pageSize:1,
          pageNum:1
      },
      reviewType: "",
      labelPosition: "right",
      formLabelAlign: {
        goalsType:7,
        templateId:'',
        customerIds:[],
        copyPersons:[],
        templateItemList:[],
      },
      viewData:{},
      pageData:{},
      peopleList:[],
      allowCopy:'',
      isReview:false,
      sendPeopleList:[],
      timeData:[],
    };
  },
  mounted: function() {},
  created() {
    if(this.$route.query.templateId){
      this.formLabelAlign.templateId = this.$route.query.templateId;
    }
    if(this.$route.query.allowCopy){
      this.allowCopy = this.$route.query.allowCopy;
    }
  },

  methods: {
    setPeople(data){
      this.sendPeopleList = data
    },
    closeC(){
        this.isShow = false
        this.isReview = false
    },
    getArrDifference(arr1, arr2) {
        return arr1.filter(item =>{
            return arr2.some(i=>item.id==i.userId)
        });
    },
    toReview(){
        this.isShow = true
        this.isReview = true
        queryDailyReview().then(res=>{
          if(res.status == 0){
                let resultData = res.data
                this.list.forEach((item,index) => {
                  item.finishGoal = resultData.dailyReviewVo[this.toObj[index]['finishGoal']];
                });
                this.timeData = resultData.timerList
                this.timeData.sort(function(a, b) {
                  var x = a['createTime'];
                  var y = b['createTime'];
                  return x > y ? -1 : x < y ? 1 : 0;
                });
                console.log(this.timeData)
          }else{
              this.$message({
                type: "error",
                message: result.msg
              })
          }
        })
    },
    deleteCustomer(index){
      this.customerList.splice(index,1);
    },
    closeTag(idtemId){
          this.peopleList = removeById(this.peopleList,idtemId)
    },
    updateCustomer(data) {
        this.customerList = data
        this.customerDialogVisible = false
    },
    chooseCustomer() {
        this.customerDialogVisible = true;
        this.$refs.customer.selectCustomerData({ visitId: '', id: '', className: 'VisitController' },this.customerList)
    },
    updateVisible(val) {
        this.customerDialogVisible = val;
    },
    submitData(data) {
      let repearArr = this.getArrDifference(data,this.sendPeopleList)
      if(repearArr.length>0){
        let tipName = repearArr.map(item=>item.name).join(',')
        this.$message({
          type: "error",
          message: `${tipName}已经是日志的接收人,请选择其他抄送人`
        });
        return 
      }
      this.peopleList = data
      this.dialogDepartmentVisible = false
    },
    updateSystemVisible(val){
      this.dialogDepartmentVisible = val
    },
    clickXuan() {
        this.dialogDepartmentVisible = true
        this.$nextTick(()=>{
          this.$refs.deRef.loadData()
          this.$refs.deRef.updateWorksId(this.peopleList)
        })
    },
    loadReviewData() {
      queryReviewGoals(this.form).then((result) => {
        if (result.data && result.data.length>0) {
          this.viewData = result.data;
          this.pageData = result.page;
          this.isShow = true;
          this.dialogPlanVisible = false;
        } else {
          this.$message({
            type: "error",
            message: '暂无相关回顾可查看'
          });
        }
      }).catch((err) => {

      });
    },
    endPickerTime(time) {
      if (this.reviewType == "planreview") {
        return false;
      }
      const today = new Date();
      return time > today;
    },
    changeDatePicker(date) {
      var newDate = new Date(date);
      this.form.year = newDate.getFullYear();
      if (this.form.goalsType == 2 || this.form.goalsType == 5) {
        this.form.month = newDate.getMonth() + 1;
      } else if (this.form.goalsType == 3 || this.form.goalsType == 6) {
        var time = getWeek(newDate);
        var times = time.split("-");
        this.form.year = times[0];
        this.form.week = times[1];
      }
    },
    changeType(){
        if (this.planTime) {
          this.planTime = ''
        }
      },
      handlePlanClose() {
        this.dialogPlanVisible = false;
        this.planTime = "";
        this.form.goalsType = ''
      },
      nextAction() {
        if (!this.form.goalsType) {
            this.$message({
              type: "info",
              message: `请选择${this.reviewType == 'planreview' ? '计划类型' : '总结类型'}`
            });
            return 
          }
        if (this.planTime) {
          
          if (this.reviewType == 'planreview') {
              var fdata = Object.assign({},this.form)
              this.loadReviewData(fdata);
            }else{
              this.loadReviewData(this.form);
            }
        } else {
          this.$message({
            type: "info",
            message: `请选择${this.types[this.form.goalsType].label}`
          });
        }
      },
      closereview() {
        this.isShow = false;
        this.reviewType = "";
        this.planTime = "";
        this.form.goalsType = ''
      },
    showView(e) {
      this.planTime = "";
      this.reviewType = e.target.id;
      this.dialogPlanVisible = true;
    },
    save() {
      this.isLoading = true;
      saveDailyRecord(this.formLabelAlign)
        .then(result => {
          this.isLoading = false;
          if (result.data) {
            this.$message({
              type: "success",
              message: "保存成功"
            });
            this.$router.go(-1)
          } else {
            this.$message({
              type: "error",
              message: result.msg
            });
          }
        })
        .catch(err => {
          this.isLoading = false;
        });
    },
    headdenForm() {
        let formData = this.$refs.modelCom.getViewData();
        this.formLabelAlign.templateItemList = formData;
        if(this.customerList.length>0){
            let cArr = this.customerList.map(item=>item.id)
            this.formLabelAlign.customerIds = cArr
        }
        if(this.allowCopy == 2){
          if(this.peopleList.length>0){
            this.formLabelAlign.copyPersons = this.peopleList.map(item=>item.id)
          }
        }
        this.save();
    },
    saveGoalAction() {
        this.$refs.modelCom.submitForm();
    },
  }
};
</script>

<style lang="scss" scoped>
  .ititle{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #333333;
  }
  .vtime{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #999999;
  }
  .vcustomer{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
  }
  .rti{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 30px;
    color: #333333;
  }
  .repadd{
      padding: 20px;
  }
  .closediv{
    text-align: right;
  }
  .closediv img{
    cursor: pointer;
  }
  .cclose{
    display: inline-block;
    vertical-align: middle; 
    margin-left: 18px;
    color: #4285F4;
    cursor: pointer;
 }
  .cli{
    padding: 0 16px;
    height: 44px;
    border-radius: 200px 200px 200px 200px;
    border: 1px solid #4285F4;
    margin-right: 16px;
    line-height: 44px;
    color: #4285F4;
    margin-bottom: 10px;
  }
  .pname{
    text-align: center;
  }
  .noimg{
    width: 48px;
    height: 48px;
    line-height: 48px;
    border-radius: 8px;
    background-color: #4285F4;
    text-align: center;
    color: #fff;
    font-size: 12px;
  }
  .uflex{
    display: flex;
    flex-wrap: wrap;
  }
  .closeimg{
    position: absolute;
    right:-8px;
    top: -8px;
    cursor: pointer;
  }
  .pli{
    margin-right: 24px;
    position: relative;
  }
  .pimg{
    width: 44px;
    height: 44px;
    border-radius: 8px 8px 8px 8px;
    
  }
  .pflex{
    display: flex;
    margin-top: 10px;
    flex-wrap: wrap;
  }
  .adddi{
    width: 44px;
    height: 44px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #D6D6D6;
    text-align: center;
    line-height: 44px;
    font-size: 18px;
    color: #D6D6D6;
    cursor: pointer;
  }
  .zhu{
    font-family: SourceHanSansSC;
    font-weight: 300;
    font-size: 14px;
    color: rgb(173, 173, 173);
  }
  .bposi{
    position: relative;
    height: 34px;
    margin-bottom: 10px;
  }
  .borderdi{
    border:1px solid #DCDFE6;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
  }
.mr {
  margin-left: 20px;
}
.upload-demo {
  margin-top: 20px;
}
.pd20 {
  padding: 20px 0;
}
.tright {
  text-align: center;
}

.maindiv {
  display: flex;
  padding-top: 20px;
}
.rightdiv {
  width: 20%;
  margin-left: 20px;
  border-radius: 10px;
}
.w40 {
  width: 40%;
  width: 50%;
  background-color: white;
}
.submitmask {
  width: 100%;
  text-align: center;
  margin-top: 20px;
}
.textAlign /deep/.el-input__inner {
  text-align: center !important;
}
.flexcss {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.textAlignRight {
  text-align: right;
}
.width200 {
  width: 200px;
  margin-bottom: 16px;
}
.definput /deep/.el-input-group__append {
  background-color: #f6f7fb !important;
  color: #333333 !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.rtextcss {
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  color: #999999;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.owntable /deep/ td,
th {
  padding: 8px 0 !important;
}
.owntable /deep/ th {
  background-color: #f6f7fb !important;
}
.owntable /deep/ .cell {
  font-size: 14px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400 !important;
  color: #333333 !important;
}
.mubiao {
  background: #fff;
  margin-top: 10px;
  padding: 24px 20px;
  min-height: 500px;
  box-sizing: border-box;
}
.inputcss {
  height: 36px !important;
}
.elform /deep/ .el-input__inner {
  line-height: 36px !important;
  height: 36px;
}
.deleteimg {
  position: absolute;
  right: 12px;
  bottom: 12px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.tablebox {
  height: 337px;
  overflow-x: hidden;
}
.asbu {
  position: absolute;
  right: 0;
  top: 0;
}
.rela {
  position: relative;
  margin-bottom: 24px;
}
.elform /deep/.el-input-group__append {
  padding: 0 12px !important;
}
.mt {
  margin-top: 20px;
}
.elform {
  width: 100%;
}
.elform  .el-form-item {
    margin-bottom: 20px !important;
}
.bbtn {
  height: 48px;
  line-height: 48px;
  background-color: #dfeafd;
  font-size: 16px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  letter-spacing: 1px;
  border-radius: 4px;
  cursor: pointer;
}
.bbtn:hover {
  background-color: #4285f4;
  color: white !important;
}
.bbtn + .bbtn {
  margin-top: 10px;
}
.owntop {
  width: 80%;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
}
.w60 {
  width: 60%;
  width: 50%;
}
.personalplan {
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  box-sizing: border-box;
}
.unitplan {
  flex: 1;
  background: #fff;
  border-radius: 8px 8px 8px 8px;
  box-sizing: border-box;
}
.myform /deep/.el-form-item__label{
    padding: 0 !important;
    line-height: 36px;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
}
.con {
				width: 100%;
				margin: 0 auto;
			}

			.title {
				text-align: center;
				font-size: 24px;
				color: #333;
				margin-top: 50px;
				text-align: center;
			}

			.timeline-con {
				position: relative;
        margin-top: 80px;
				position: relative;
			}
			.timeline-con::before {
				content: '';
				display: block;
				width: 1px;
				height: 88%;
				position: absolute;
				left: 50%;
				top: 0;
				background: linear-gradient(transparent 0%,
						transparent 50%,
						#E6E6E6 50%,
						#E6E6E6 100%);
				background-size: 1px 10px;
				background-repeat: repeat-y;
				background-position: left;
			}
			.timeline-post {
				width: 50%;
				margin-left: 50%;
				margin-bottom: 20px;
        min-height: 86px;
			}
			.timeline-post:nth-child(even) {
				margin-left: 0;
			}

			.timeline-date {
				font-size: 20px;
				font-weight: bold;
				color: #666;
				position: absolute;
				right: 50%;
				margin-top: 15px;
				margin-right: 24px;
			}
			.timeline-post:nth-child(even) .timeline-date {
				left: 50%;
				margin-left: 24px;
			}
			.timeline-icon-con {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				left: 50%;
				width: 10px;
				height: 10px;
				background: #D9D9D9;
				border-radius: 50%;
				margin-left: -5px;
			}
      .timeline-post:nth-child(even) .timeline-content {
        position: relative;
				font-size: 18px;
				color: #666;
				line-height: 24px;
				margin: 0 0 0 44px;
				padding: 15px 30px;
				background: url(../../../assets/leftr.png) no-repeat;
				background-size: 100% 100%;
        transform: translateY(-50%);
        top: 5px;
			}
			.timeline-content {
				position: relative;
				font-size: 18px;
				color: #666;
				line-height: 24px;
				margin: 0 0 0 44px;
				padding: 15px 30px;
				background: url(../../../assets/rbg.png) no-repeat;
				background-size: 100% 100%;
        transform: translateY(-50%);
        top: 5px;
			}
			.timeline-post:nth-child(even) .timeline-content {
				margin: 0 44px 0 0;
				text-align: right;
			}
			
</style>
