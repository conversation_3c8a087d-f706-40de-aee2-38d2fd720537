<template>
  <div class="flexcss">
    <div class="head-container">
      <ul class="rolelist">
        <li @click="roleHandle(item,index)" class="roleli" v-for="(item,index) in roleList" :key="index">
          <el-button class="rolebutton" :type="index === activeIndex ?'primary':'text'"> {{item.name}}</el-button>
        </li>
      </ul>
      <div class="roleButton">
        <el-button v-isShow="'crm:controller:role:saveRole'" type="primary" @click="addOrUpdateHandle()">新建角色
        </el-button>
        <el-button v-isShow="'crm:controller:role:deleteRole'" type="danger" @click="deleteHandle()">删除</el-button>
      </div>
    </div>
    <div class=" rolediv">
      <div class="roleheader">
        <div class="roleName">
          <span>{{currentRole.name}}</span>
          <i v-isShow="'crm:controller:role:updateRole'" @click="addOrUpdateHandle(currentRole)"
            class="el-icon-edit editIcon"></i>
        </div>
      </div>

      <div class="authority">
        <span class="title">数据权限</span>
        <el-radio-group v-model="modelValue" @change="radioChange">
          <el-radio label="1">
            本人
            <el-tooltip class="item" effect="dark" content="看到自己的数据" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-radio>
          <el-radio label="2">所在部门 <el-tooltip class="item" effect="dark" content="看所在部门所有人的数据" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-radio>
          <el-radio label="3">所在部门及下级部门<el-tooltip class="item" effect="dark" content="看所在部门及下级部门所有人的数据"
              placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-radio>
          <el-radio label="4">全公司<el-tooltip class="item" effect="dark" content="看所有人的数据" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </div>
      <div class="authSet">
        <div :key="index" class="authList" v-for="(item,index) in authList">
          <div class="directory">
            <el-checkbox v-model="item.isSelected" @change="checkAll(item)">{{ item.resourceName }}</el-checkbox>
          </div>
          <div class="authAll">
            <div class="authlist" v-for="(citem,cindex) in item.children" :key="cindex">
              <div class="itemheader">
                <el-checkbox v-model="citem.isSelected" @change="checkSecAll(citem,item)">{{ citem.resourceName }}
                </el-checkbox>
              </div>
              <div class="itemlist mt_20">
                <el-checkbox @change="checkThree(ccitem,citem)" v-model="ccitem.isSelected"
                  v-for="(ccitem,ccindex) in citem.children" :key="ccindex">{{ ccitem.resourceName }}</el-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="roleButton">
        <el-button v-isShow="'crm:controller:role:updateRoleResource'" type="primary" @click="saveAuth()">保存</el-button>
      </div>
    </div>
    <AddOrUpdate v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataRoleList()"></AddOrUpdate>
  </div>
</template>

<script>
  import { getRoleList, deleteRole, queryRoleResourceList, getRoleInfo, updateRole, updateRoleResource } from '@/api/framework/roleposition'
  import AddOrUpdate from './add-or-update'
  import { treeIterator, findParentIds } from '@/utils/tools'
  export default {
    data() {
      return {
        radioAuth: '',
        currentRole: {
        },
        addOrUpdateVisible: false,
        roleList: [],
        authList: [],
        modelValue: '',
        activeIndex: 0,
        isIndeterminate: false
      }
    },
    components: {
      AddOrUpdate,
    },
    methods: {
      checkParentChildren(parentItem, arr = []) {
        let mapTree = (parentItem) => {
          if (parentItem.children && parentItem.children.length > 0) {
            for (let i = 0; i < parentItem.children.length; i++) {
              let node = parentItem.children[i]
              if (!node.isSelected) {
                arr.push(false)
              } else {
                arr.push(true)
              }
              if (node.children && node.children.length) {
                mapTree(node)
              }
            }
            return arr.some(item => item === true)
          }
        }
        let resutnBoo = mapTree(parentItem)
        return resutnBoo
      },
      checkThree(ccitem, parentItem) {
        // let boolean = this.checkParentChildren(parentItem)
        // let parentArr = findParentIds(this.authList, ccitem.resourceId)
        // if (boolean) {
        //   parentArr.forEach(item => {
        //     item.isSelected = true
        //   })
        // } else {
        //   parentArr[0].isSelected = false
        //   let pBoo = this.checkParentChildren(parentArr[parentArr.length - 1])
        //   if (pBoo) {
        //     parentArr[parentArr.length - 1].isSelected = true
        //   } else {
        //     parentArr[parentArr.length - 1].isSelected = false
        //   }
        // }
      },
      checkSecAll(citem, parentItem) {
        // if (citem.children.length > 0) {
        //   this.treeCheckOrNot(citem)
        // }
        let boolean = this.checkParentChildren(parentItem)
        if (!boolean) {
          parentItem.isSelected = false
        } else {
          parentItem.isSelected = true
        }
      },
      treeCheckOrNot(item) {
        treeIterator(item.children, (node) => {
          if (item.isSelected) {
            node.isSelected = true
          } else {
            node.isSelected = false
          }
        })
      },
      checkAll(item) {
        if (item.children.length > 0) {
          this.treeCheckOrNot(item)
        }
      },
      saveAuth() {
        let resourceIds = []
        treeIterator(this.authList, (node) => {
          if (node.isSelected) {
            resourceIds.push(node.resourceId)
          }
        })
        let params = {
          roleId: this.currentRole.id,
          resourceIds
        }
        updateRoleResource(params).then(res => {
          if (res.status === 0) {
            this.msgSuccess('保存成功')
          }
        })
      },
      radioChange(value) {
        let params = {
          dataScope: value,
          id: this.currentRole.id
        }
        updateRole(params).then(res => {
          if (res.status === 0) {
            this.msgSuccess('保存成功')
          }
        })
      },
      roleHandle(item, index) {
        this.currentRole = item
        this.activeIndex = index
        this.queryRoleResourceListData({ roleId: this.currentRole.id })
        this.getRoleInfoData(this.currentRole.id)
      },

      getRoleInfoData(id) {
        getRoleInfo(id).then(res => {
          if (res.status === 0) {
            this.modelValue = res.data.dataScope + ''
          }
        })
      },
      queryRoleResourceListData(data) {
        queryRoleResourceList(data).then(res => {
          if (res.status === 0) {
            this.authList = res.data
          }
        })
      },
      getDataRoleList() {
        getRoleList().then((res) => {
          if (res.status === 0) {
            this.roleList = res.data
            this.currentRole = res.data[0]
            this.queryRoleResourceListData({ roleId: this.currentRole.id })
            this.getRoleInfoData(this.currentRole.id)
          }
        })
      },
      addOrUpdateHandle(role) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(role)
        })
      },
      deleteHandle() {
        this.$confirm(
          `确定对进行删除操作?`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(async () => {
          let res = await deleteRole({ id: this.currentRole.id })
          if (res.status === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
            })
            this.getDataRoleList()
            this.activeIndex = 0
          } else {
            this.$message.error(res.msg)
          }
        })
      },
    },
    created() {
      this.getDataRoleList()
    },
  }
</script>

<style lang="scss" scoped>
  .authlist {
    padding-bottom: 10px;
    border-bottom: 1px solid #f2f2f2;
    margin-bottom: 20px;
  }

  .editIcon {
    cursor: pointer;
    float: right;
    margin-top: 15px;
  }

  .roleButton {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 55px;
    border-top: 1px solid #d4d8db;
    text-align: center;
    line-height: 55px;
  }

  .mt_20 {
    margin-top: 20px;
  }

  .authSet {
    padding: 0 16px;
    flex: 1;
    overflow-x: hidden;
    position: relative;
  }

  .directory {
    padding-bottom: 10px;
    width: 170px;

    /deep/ {
      .el-checkbox__label {
        font-size: 14px;
      }
    }
  }

  .authList {
    padding: 30px 0;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
  }

  .authAll {
    flex: 1;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #292e33;
    display: inline-block;
    width: 170px;
  }

  .authority {
    height: 70px;
    line-height: 70px;
    overflow: hidden;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #f2f2f2;
  }

  .roleheader {
    align-items: center;
    padding: 0 16px;
    height: 50px;
    min-height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #f2f2f2;
  }

  .rolebutton {
    padding: 4px !important;
  }

  .roleName {
    font-size: 16px;
    font-weight: 500;
    color: #292e33;
    margin-right: 0;
    line-height: 50px;
  }

  .roleli {
    margin-bottom: 20px;

    /deep/ .el-button--text {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .rolediv {
    flex: 1;
    background: #fff;
    display: flex;
    flex-direction: column;
    padding-bottom: 55px;
    position: relative;
  }

  .flexcss {
    display: flex;
    height: 100%;
  }

  .rolelist {
    padding: 0 20px;
    flex: 1;
    overflow-x: hidden;
  }

  .head-container {
    width: 260px;
    padding: 20px 0 55px 0;
    background-color: #fff;
    margin-right: 10px;
    position: relative;
    display: flex;
  }
</style>