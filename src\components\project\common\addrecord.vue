<template>
  <div class="fdiv">
    <el-form :model="form" ref="form" label-width="120px" :rules="rules">
        <el-form-item prop="workTime" label="日期：" v-if="recordType == 'work' || recordType == 'out'">
            <el-date-picker
            value-format="yyyy-MM-dd"
            class="definput w100"
            v-model="form.workTime"
            type="date"
            placeholder="请选择日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item prop="costDate" label="日期：" v-if="recordType == 'cost'">
            <el-date-picker
            value-format="yyyy-MM-dd"
            class="definput w100"
            v-model="form.costDate"
            type="date"
            placeholder="请选择日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item prop="content" :label="`${getText[recordType].label}：`" v-if="recordType == 'work' || recordType == 'out'">
            <el-input   maxlength="200"
            show-word-limit type="textarea" v-model="form.content" :rows="6" :placeholder="getText[recordType].pltext" ></el-input>
        </el-form-item>
        <el-form-item prop="reason" :label="`${getText[recordType].label}：`" v-if="recordType == 'cost'">
            <el-input   maxlength="200"
            show-word-limit type="textarea" v-model="form.reason" :rows="6" :placeholder="getText[recordType].pltext" ></el-input>
        </el-form-item>
        <el-form-item prop="workHours" v-if="recordType == 'work'" label="消耗工时：">
            <el-input  @input="limitWorkHoursNum"   type="number" v-model="form.workHours" placeholder="请输入"><template slot="append">小时</template></el-input>
        </el-form-item>
        <el-form-item   v-if="recordType == 'out'" label="时长：">
            <el-input @input="limitPhoneNum" class="minute" type="number" v-model="minuteValue" placeholder="请输入分钟"></el-input>
            <span class="linecss">--</span>
            <el-input  @input="limitToSeconds" class="second"  v-model="secondValue" placeholder="请输入秒(00)"></el-input>
        </el-form-item>
        <el-form-item prop="costAmount" v-if="recordType == 'cost'" label="金额：">
            <el-input-number clearable  :controls="false"  :min="0" :precision="2"  v-model="form.costAmount" class="definput widm textleft" placeholder="请输入金额（单位：元）"></el-input-number>
        </el-form-item>
    </el-form>
        <el-button :disabled="taskStatus == 3 || taskStatus == 5" class="submitBtn" type="primary" @click="submit">提交</el-button>
  </div>
</template>

<script>
      import { mapGetters } from 'vuex'
export default {
    props:{
        recordType:{
            type:String,
            default:'work'
        }
    },
    data(){
        return{
            form:{
                taskId:'',
                type:'',
                workHours:'',
                duration:'',
                workTime:'',
                content:'',
                costDate:'',
                reason:'',
            },
            secondValue:'',
            minuteValue:'',
            rules: {
                content: [
                    { required: true, message: '请输入本次工作内容', trigger: 'blur' },
                ],
                workTime: [
                    {  required: true, message: '请选择日期', trigger: 'change' }
                ],
                costDate: [
                    {  required: true, message: '请选择日期', trigger: 'change' }
                ],
                reason: [
                    { required: true, message: '请输入成本事项', trigger: 'blur' },
                ],
                workHours: [
                    { required: true, message: '请输入消耗工时', trigger: 'blur' },
                ],
                costAmount: [
                    { required: true, message: '请输入金额', trigger: 'blur' },
                ],
            },
            getText:{
                "work":{
                    label:"工作内容",
                    pltext:"请输入本次工作内容"
                },
                "out":{
                    label:"内容",
                    pltext:"请输入本次工作内容"
                },
                "cost":{
                    label:"事项",
                    pltext:"请输入成本事项"
                },
            },
        }
    },
    computed: {
        ...mapGetters(['taskStatus']),
      },
    methods:{
        limitPhoneNum(value) {
            if (value.toString().length > 4) {
                this.minuteValue = this.minuteValue.toString().slice(0, 4)
            }
        },
        limitWorkHoursNum(value){
            if (value.toString().length > 4) {
                this.form.workHours = value.toString().slice(0, 4)
            }
        },
        limitToSeconds(value) {
            value=value.replace('.','');
            const regex = /^[0-5][0-9]?$/;
            if (!regex.test(value)) {
                this.secondValue = ''
            }else{
                this.secondValue = value
            }
        },
        resetForm(){
            this.$refs.form.resetFields();
            for (let key in this.form) {
                    this.form[key] = ''
            }
            this.minuteValue = ''
            this.secondValue = ''
        },
        submit(){
                if(this.recordType=='out'){
                    if(!(this.minuteValue==='' && this.secondValue==='') ){
                        if(this.minuteValue!=''){
                            if(this.minuteValue.length==1){
                                this.form.duration = '0'+this.minuteValue+':'
                            }else{
                                this.form.duration = this.minuteValue+':'
                            }
                        }else{
                            this.form.duration = '00'+':'
                        }
                        if(this.secondValue!=''){
                            if(this.secondValue.length==1){
                                this.form.duration = this.form.duration+ '0'+this.secondValue
                            }else{
                                this.form.duration = this.form.duration+this.secondValue
                            }
                        }else{
                            this.form.duration = this.form.duration+'00'
                        }
                    }
                }
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        this.$emit('submitData',{
                            form :this.form,
                            recordType:this.recordType
                        })
                    } else {
                        return false;
                    }
                });
        },
    }
}
</script>

<style lang="scss" scoped>
.widm{
    width: 100%;
}
 .textleft /deep/.el-input__inner {
    text-align: left !important;
    }
    .linecss{
        display: inline-block;
        text-align: center;
        vertical-align: middle;
        width: 10%;
    }
    .minute{
        width: 45%;
    }
    .second{
        width: 45%;
        float:right;
    }
.submitBtn{
    width: 80px;
    padding: 10px 0 !important;
    display: block;
    margin: auto;
    margin-top: 60px;
}
.bottom{
   
}
.fdiv{
    height: 100%;
    position: relative;
}
.w100.el-input{
    width: 100% !important;
}
</style>