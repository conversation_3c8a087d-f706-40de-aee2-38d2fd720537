<template>
  <div class="itemheader">
    <div class="exportcss">
      <el-dropdown @command="commandClick" trigger="click">
        <i class="el-icon-arrow-down f16"></i>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="1" v-isShow="'crm:controller:visit:info'"
            >编辑</el-dropdown-item
          >
          <el-dropdown-item
            :command="2"
            v-isShow="'crm:controller:visit:delete'"
          >
            删除</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <headuser
      :url="item.creatByLogo"
      width="48"
      :username="item.createByName"
    ></headuser>
    <div>
      <p class="pname">{{ item.createByName }}</p>
      <p class="">
        <span class="spantext">{{ typeToName[item.assistType] }}</span>
        <span class="spantext"
          >&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;</span
        >
        <span class="spantext">{{ item.createTime }}</span>
        <span class="spantextcolor" v-if="item.remindTime"
          >下次跟进时间：{{ item.remindTime }}</span
        >
      </p>
    </div>
    <div class="mlright">
      <p
        class="visitText"
        v-html="
          item.description.replace(/\n|\r\n/g, '<br>').replace(/ /g, '&nbsp')
        "
      ></p>
      <div class="clearfix">
        <img
          :key="`pic${index}`"
          v-for="(item, index) in item.picList"
          @click="showImg(item)"
          class="imgitem left imgco"
          :src="item.url"
          alt=""
        />
        <div
          :key="index"
          v-for="(item, index) in item.videoList"
          class="left videodiv imgco"
          @click="showvideo(item)"
        >
          <img
            class="imgitem"
            :src="`${item.url}?x-oss-process=video/snapshot,t_1000,m_fast`"
            alt=""
          />
          <img class="playimg" src="@/assets/playsmall.png" alt="" />
        </div>
      </div>
      <div class="yuyin">
        <span class="spantext">语音备忘：</span>
        <audioV
          v-if="item"
          :item="item"
          :url="item.soundList.length > 0 ? item.soundList[0].url : ''"
        ></audioV>
        <span v-else>暂无</span>
      </div>
      <div class="yuyin" v-if="item.isAssist">
        <img src="@/assets/guan.png" alt="" />
        <span class="guantext">关键信息：</span>
        <p class="guancontent" v-if="item.assistContent">
          {{ item.assistContent }}
        </p>
        <span v-else>暂无</span>
      </div>
      <div class="yuyin">
        <img src="@/assets/file.png" alt="" />
        <span class="guantext">附件：</span>
        <pdfview
          :pdfArr="item.fileList"
          v-if="item.fileList.length > 0"
        ></pdfview>
        <span v-else>暂无</span>
      </div>

      <el-form>
        <textBorder>基本信息</textBorder>
        <el-row :gutter="20" class="width100 mt10 pb5">
          <el-col :span="cCol">
            <el-form-item label="跟进客户：" class="labeltext">
              <span class="cname" @click="openC(item.customerId)">{{
                item.customerName
              }}</span>
            </el-form-item>
            <!-- <el-form-item label="部门：" class="labeltext">
              <span>{{ item.unitDepartment }}</span>
            </el-form-item> -->
          </el-col>
          <el-col :span="cCol">
            <el-form-item label="客户单位：" class="labeltext mulitline">
              <span>{{ item.unitName }}</span>
            </el-form-item>
            <!-- <el-form-item label="职务：" class="labeltext">
              <span class="colorcss">{{ item.duties }}</span>
            </el-form-item> -->
          </el-col>
          <!-- <el-col :span="cCol">
            <el-form-item label="联系电话：" class="labeltext">
              <span>{{ item.phone }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="cCol">
            <el-form-item label="客户级别：" class="labeltext">
              <span :class="customerLevelColors[item.level]">{{ item.level }}</span>
            </el-form-item>
          </el-col> -->
        </el-row>

        <textBorder>关联信息</textBorder>
        <el-row :gutter="20" class="width100 mt10 pb5">
          <el-col :span="cCol">
            <el-form-item label="关联合同：" class="labeltext mulitline">
              <span class="szcss" @click="toDetail(item.contractId, '合同')">{{
                item.contractName
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="cCol">
            <el-form-item label="关联机会：" class="labeltext mulitline">
              <span>{{ item.opportunityName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="cCol">
            <el-form-item label="关联项目：" class="labeltext mulitline">
              <span>{{ item.projectName }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <textBorder>系统信息</textBorder>
        <el-row :gutter="20" class="width100 mt10 pb5">
          <el-col :span="cCol">
            <el-form-item label="最后修改人：" class="labeltext">
              <span>{{ item.modifyName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="cCol">
            <el-form-item label="最后修改时间：" class="labeltext">
              <span>{{ item.modifyTime }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="cCol">
            <el-form-item label="创建人：" class="labeltext">
              <span>{{ item.createByName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="cCol">
            <el-form-item label="创建时间：" class="labeltext">
              <span>{{ item.createTime }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <textBorder>已查看人员</textBorder>
        <p class="peocss">{{ item.viewedPersonNameList.join('、') }}</p>
      </el-form>
      <div class="m10">
        <img src="@/assets/commit.png" alt="" />
        <span class="spantext commentnum">{{ item.visitCommentNum }}</span>
      </div>
      <el-collapse-transition v-if="commentArr.length > 0">
        <div v-show="expand">
          <comment
            @reply="reply"
            v-for="(item, index) in commentArr"
            :key="index"
            :item="item"
            @deleteAction="deleteAction"
          ></comment>
        </div>
      </el-collapse-transition>

      <div class="buttoncursor" @click="expandOrShou()">
        <img
          v-show="!expand && item.visitCommentNum > 0"
          src="@/assets/xia.png"
          alt=""
        />
        <span v-show="!expand && item.visitCommentNum > 0" class="expand"
          >展开</span
        >

        <img
          v-show="expand && item.visitCommentNum > 0"
          src="@/assets/shang.png"
          alt=""
        />
        <span v-show="expand && item.visitCommentNum > 0" class="expand"
          >收起</span
        >
      </div>
      <div class="savediv">
        <el-input
          show-word-limit
          maxlength="400"
          clearable=""
          ref="elRef"
          class="inputcommont"
          v-model.trim="value"
          placeholder="说点什么，鼓励一下你的队友吧~"
        ></el-input>
        <el-button @click="commentButton()" class="savebutton" type="primary"
          >提交</el-button
        >
      </div>
    </div>
    <dc-dialog
      :iType="dialogType"
      title="温馨提示"
      width="500px"
      :showCancel="isShowCancel"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <p class="pcc">确定要删除么?</p>
    </dc-dialog>
    <replyDialog
      ref="replyDialog"
      :visible.sync="replayDialogVisible"
      :title="replayTitle"
      @updateVisible="updateReplayVisible"
      @replyData="replyData"
    ></replyDialog>
  </div>
</template>

<script>
import pdfview from './components/pdfview.vue'
import comment from './components/comment.vue'
import audioV from '@/components/common/audio.vue'
import replyDialog from '@/components/common/replyDialog.vue'
import textBorder from '@/components/common/textBorder.vue'
import {
  commentList,
  sendSave,
  visitDelete,
  commentDelete,
  isShowDetail,
} from '@/api/visit/index'
import { customerLevelColors } from '@/utils/dict'
import headuser from './components/headuser.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'listItem',
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dpsn: 6,
      typeToName: {
        1: '电话拜访',
        2: '线上拜访',
        3: '实地拜访',
      },
      customerLevelColors: customerLevelColors,
      expand: false,
      value: '',
      commentArr: [],
      commentId: '',
      parentId: '',
      dialogType: 1,
      isShowCancel: true,
      dialogVisible: false,
      replayDialogVisible: false,
      replayTitle: '',
    }
  },
  components: {
    comment,
    pdfview,
    audioV,
    textBorder,
    replyDialog,
    headuser,
  },
  created() {},
  computed: {
    ...mapGetters(['cCol']),
  },
  methods: {
    clearData() {
      this.commentId = ''
      this.expand = false
      this.commentArr = []
    },
    getPremission(customerId) {
      isShowDetail({
        id: customerId,
      })
        .then((result) => {
          if (result.data) {
            this.$store.commit('cus/CHANGECOL', 12)
            this.$emit('setWidth', customerId)
          } else {
            this.$message({
              type: 'error',
              message: '暂无权限，无法查看',
            })
          }
        })
        .catch((err) => {})
    },
    openC(customerId) {
      this.getPremission(customerId)
    },
    toDetail(id, type) {
      if (!id) {
        return
      }
      var path = ''
      switch (type) {
        case '合同':
          path = '/projectManagement/contract/detail'
          break
        case '机会':
          path = '/clientMaintenance/salesLead/detail'
          break
        case '项目':
          path = ''
          break

        default:
          break
      }
      this.$router.push({
        path: path,
        query: {
          id: id,
        },
      })
    },
    submitDialog() {
      let params = {
        id: this.item.id,
      }
      visitDelete(params).then((res) => {
        if (res.status == 0) {
          this.msgSuccess('删除成功')
          this.$emit('loadData')
          this.dialogVisible = false
        }
      })
    },
    commandClick(value) {
      if (value == 1) {
        this.$router.push({
          path: '/clientMaintenance/followVisit/add',
          query: {
            id: this.item.id,
          },
        })
      }
      if (value == 2) {
        this.dialogVisible = true
      }
    },
    reply(data) {
      ;(this.parentId = data.id), this.updateReplayVisible(true)
      this.replayTitle = `回复@${data.name}`
    },

    updateReplayVisible(val) {
      this.replayDialogVisible = val
    },
    replyData(data) {
      this.save(data)
      this.$refs.replyDialog.closeAction()
    },
    save(comment) {
      this.commentId = this.item.id
      let params = {
        visitId: this.commentId,
        comment: comment,
        commentType: 1,
        parentId: this.parentId || 0,
      }
      sendSave(params)
        .then((res) => {
          if (res.status == 0) {
            this.commentListData()
            this.expand = true
            this.value = ''
          }
          if (!this.parentId) {
            this.$emit('updateCommont', this.item)
          }
          this.parentId = 0
        })
        .catch((err) => {
          this.parentId = 0
        })
    },
    deleteAction(commentId) {
      commentDelete({ id: commentId })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.commentListData()
          } else {
            this.$message({
              type: 'error',
              message: '删除失败',
            })
          }
        })
        .catch((err) => {})
    },
    commentButton() {
      if (this.value == '') {
        this.msgError('请输入评论内容')
        return
      }
      this.save(this.value)
    },
    commentListData() {
      commentList({ visitId: this.commentId }).then((res) => {
        if (res.status == 0) {
          this.commentArr = res.data
        }
      })
    },
    expandOrShou() {
      this.expand = !this.expand
      if (this.expand) {
        if (this.commentId) {
          return
        }
        this.commentId = this.item.id
        this.commentListData()
      }
    },
    showImg(item) {
      this.$maskV.show({
        type: 1,
        url: item.url,
        onClose: () => {},
      })
    },
    showvideo(item) {
      this.$maskV.show({
        type: 2,
        url: item.url,
        onClose: () => {},
      })
    },
  },
}
</script>

<style>
textarea {
  white-space: pre-wrap;
}
</style>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 20px !important;
}
.peocss {
  margin: 16px 0;
  line-height: 20px;
}
.cname {
  color: #4285f4;
  cursor: pointer;
}
.exportcss {
  float: right;
  margin-top: 30px;
}

.m10 {
  margin: 10px 0;
}

.labeltext {
  margin-bottom: 5px !important;
}
.divtext {
  width: 100px;
  height: 100px;
  background: #000;
  opacity: 0.5;
}

.savediv {
}

.inputcommont {
  width: calc(100% - 88px);
  margin-right: 16px;
}
.inputcommont /deep/ .el-input__inner {
  height: 34px !important;
  line-height: 34px !important;
}

.savebutton {
  width: 72px;
  padding: 9px 5px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
}

.itemheader {
  border-bottom: 1px solid #f0f0f0;
  padding: 30px 0;
}

.buttoncursor {
  cursor: pointer;
  display: inline-block;
  margin-bottom: 20px;
}

.expand {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
}

.commentnum {
  margin-left: 6px;
}

.guancontent {
  margin: 10px 0;
  margin-left: 18px;
}

.guantext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-left: 2px;
}

.liitem {
  border-radius: 4px 4px 4px 4px;
  border: 1px dashed #4285f4;
  padding: 5px 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  box-sizing: border-box;
  cursor: pointer;
}

.yuplay {
  margin-left: 6px;
  margin-right: 10px;
}

.yutext {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  margin-left: 17px;
}

.yuyindiv {
  width: 175px;
  height: 24px;
  background: #dfeafd;
  border-radius: 17px 17px 17px 17px;
  display: inline-block;
  line-height: 21px;
}

.yuyin {
  margin: 10px 0;
  margin-bottom: 20px;
}

.imgco {
  margin-right: 12px;
}

.playimg {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  cursor: pointer;
}

.videodiv {
  position: relative;
}

.imgitem {
  width: 98px;
  height: 98px;
  border-radius: 8px 8px 8px 8px;
  cursor: pointer;
}

.mlright {
  margin-left: 57px;
}

.visitText {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin: 10px 0;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 28px;
}

.spantextcolor {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #f45961;
  margin-left: 20px;
}

.spantext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.pname {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-bottom: 8px;
}

.head {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 8px;
}
.mulitline /deep/.el-form-item__content {
  padding-top: 10px;
  line-height: 20px;
  overflow-wrap: break-word;
}
</style>
