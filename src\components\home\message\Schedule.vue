<template>
  <div class="box" ref="scrollContainer" @scroll="handleScroll">
    <div class="boxItem" v-for="(item,index) in boxList" :key="index">
      <div class="title">{{ item.title }}</div>
      <div class="container">
        <div class="content">{{ item.content }}</div>
      </div>
      <div class="justBetween">
        <div class="time">提醒时间：{{ item.remindTime }}</div>
        <div class="btn" @click="deleteAction(item)">
          <img src="@/assets/delete.png" alt="" class="btnImg">
        </div>
      </div>
    </div>
    <div v-if="loading" class="loading-text">加载中...</div>
    <div v-if="noMore && boxList.length > 0" class="no-more-text">没有更多数据了</div>
    <el-empty v-if="boxList.length == 0 && !loading" description="暂无日程消息"></el-empty>
  </div>
</template>

<script>
import {queryHomeList, remindDelete} from '@/api/index'
import {myMinxin} from '../common/handleScroll.js'
import Bus from "@/utils/EventBus"

export default {
  mixins:[myMinxin],
  data() {
    return {
      pageBean:{
        pageNum: 1,
        pageSize: 10,
        taskType:1
      },
      boxList:[]
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    getList(isLoadMore = false){
      queryHomeList(this.pageBean).then((res) => {
        if (isLoadMore) {
          this.boxList = [...this.boxList, ...res.data]
        } else {
          this.boxList = res.data
        }

        this.noMore = !res.page.hasNextPage
      }).finally(() => {
        this.loading = false
        this.isScrollLoading = false
      })
    },
    deleteAction(item){
      this.$confirm('此操作将删除该日程信息, 是否继续?', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          remindDelete({ id: item.id })
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '删除成功',
                })
                this.loadData()
                // 通知index组件刷新消息数量
                Bus.$emit('refreshMessageNum')
              }
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
  }
}
</script>

<style lang="scss" scoped>
.boxItem{
  min-height: 137px;
  padding: 12px 20px;
  background: #F6F7FB;
  border-radius: 8px 8px 8px 8px;
  margin-bottom: 16px;
}
.boxItem:last-child{
  margin-bottom: 0;
}
.title{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 14px;
  color: #333333;
}
.container{
  border-bottom: 1px solid #E6E9F5;
  padding-bottom: 12px;
  margin-top: 12px;
  margin-bottom: 12px;
}
.content{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}
.content:last-child{
  margin-bottom: 0;
}
.justBetween{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.btn{
  display: flex;
  align-items: center;
  cursor: pointer;
}
.btnText{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #4285F4;
  margin-right: 2px;
}
.btnImg{
  width: 16px;
  height: 17px;
}
.time{
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}
.box {
  max-height: 100%;
  overflow-y: auto;
}

.loading-text, .no-more-text {
  text-align: center;
  padding: 20px;
  font-size: 14px;
  color: #999;
}
</style>
