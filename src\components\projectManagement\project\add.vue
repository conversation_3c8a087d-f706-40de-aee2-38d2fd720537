<template>
    <div class="flex">
        <div>
            <back>{{titleName}}</back>
        </div>
        <div class="mainbg">
            <el-form :rules="rules" :model="form" ref="addform" class="addfcss" label-width="130px">
                <textBorder>基础信息</textBorder>
                <div class="pt20  bbline">
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item label="项目名称：" prop="projectName">
                                <el-input clearable="" v-model="form.projectName" class="definput"
                                    placeholder="请输入项目名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="项目状态：" prop="projectStatus">
                                <el-select clearable class="definput" popper-class="removescrollbar"
                                    v-model="form.projectStatus" placeholder="请选择">
                                    <el-option v-for="item in optionsStatus" :key="item.value" :label="item.name"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="客户：" prop="customerId">
                                <div class="unitbtn" :class="{selectedCss:form.customerName}" @click="chooseCustomer">
                                    {{form.customerName ? form.customerName :'请选择客户'}} <i
                                        class=" rcenter el-icon-arrow-down" /></div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item label="客户单位：">
                                <div class="unitbtn selectedCss"> {{ form.unitName}}</div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="beginTime" label="开始时间：">
                                <el-date-picker value-format="yyyy-MM-dd" class="eldate" v-model="form.beginTime"
                                    type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item prop="endTime" label="结束时间：">
                                <el-date-picker value-format="yyyy-MM-dd" class="eldate" v-model="form.endTime"
                                    type="date" placeholder="选择日期">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>

                    </el-row>


                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item label="关联合同：">
                                <div class="unitbtn sw" :class="{selectedCss:form.contractName}"
                                    @click="chooseContract">{{form.contractName ? form.contractName :'请选择'}}
                                    <i @click.stop="closeCon" v-show="form.contractName"
                                        class="rcenter el-icon-close"></i>
                                    <i v-show="!form.contractName" class=" rcenter el-icon-arrow-down" />
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="合同金额(万元)：">
                                <div class="unitbtn selectedCss discss"> {{ contractAmount}}</div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="项目类型：" prop="projectType">
                                <el-select clearable="" @change="changeValue" class="definput els40 ellipsis"
                                    popper-class="removescrollbar" v-model="form.projectType" placeholder="请选择">
                                    <el-option v-for="item in optionsContract" :key="item.id" :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                                <el-select clearable="" class="definput els50 ellipsis" popper-class="removescrollbar"
                                    v-model="form.projectTypeSub" placeholder="请选择">
                                    <el-option v-for="item in optionsContractDetail" :key="item.id" :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>


                            </el-form-item>
                        </el-col>

                    </el-row>


                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item label="项目金额(万元)：" prop="projectAmount">
                                <el-input clearable="" v-model="form.projectAmount" class="definput"
                                    placeholder="请输入项目金额"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <textBorder v-if="showTem" class="mt30">项目模板</textBorder>
                <div class="pt20  bbline" v-if="showTem">
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item prop="isTemplate" label-width="150px" class="" label="是否使用模板创建：">
                                <el-radio :disabled="disabled" v-model="form.isTemplate" :label="1">是</el-radio>
                                <el-radio :disabled="disabled" v-model="form.isTemplate" :label="0">否</el-radio>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="form.isTemplate">
                            <el-form-item class="quanxiancss" prop="templateId" label="选择模板：">
                                <el-select :disabled="disabled" ref="country_select" clearable="" class="definput"
                                    popper-class="removescrollbar" v-model="form.templateId" placeholder="请选择">
                                    <el-option v-for="item in options" :key="item.id" :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </div>
                <textBorder class="mt30">负责与协作</textBorder>
                <div class="pt20  bbline">
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item label="负责人：" prop="chargePerson">
                                <span v-if="form.chargePerson" class="mr10">{{form.chargePersonName}}</span>
                                <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择负责人',1)">
                                    点击选择负责人</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="协作人：" prop="collaborator">
                                <el-tag class="tagcss" size="small" v-for="item in xiezuolist" closable
                                    @close="handleTagClose(item)" :key="item.id">{{item.name}}</el-tag>
                                <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择协作人',5)">
                                    点击选择协作人</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </div>
                <textBorder class="mt30">补充信息</textBorder>
                <div class="pt20  bbline">
                    <el-form-item label="附件：">
                        <upload2 ref="upload2" @submitImg="submitPdf" accept=".pdf" :fileList="fileListPdf">
                            <span class="studiocss">
                                <img src="../../../assets/img/file_icon.png">
                                <span class="uploadtext  deffont">点击上传附件</span>
                            </span>
                            <template slot="ptip">
                                <p>只能上传pdf文件</p>
                            </template>
                        </upload2>
                    </el-form-item>

                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="12">
                            <el-form-item label="备注：">
                                <el-input maxlength="300" show-word-limit v-model="form.notes" class="definput"
                                    type="textarea" rows="4" placeholder="请输入备注"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="pt20 btncenter">
                    <el-form-item>
                        <el-button :disabled="disabled" class="w98 btn_h42" type="primary" @click="saveButton">保存
                        </el-button>
                    </el-form-item>
                </div>
            </el-form>
        </div>
        <systemDialog ref="systemdialog" :name="dialogName" :multipleNum="multipleNum" :visible.sync="dialogVisible"
            @updateVisible="updateSystemVisible" @submitData="submitData">
        </systemDialog>


        <!-- 客户选择 -->
        <cuatomerDialog ref="customer" :visible.sync="customerDialogVisible" @updateVisible="updateVisible"
            @updateCustomer="updateCustomer"></cuatomerDialog>

        <!-- 合同选择 -->
        <contractDialog ref="contractRef" :visible.sync="contractVisible" @updateVisible="updateContractVisible"
            @updateData="updateContractData"></contractDialog>




    </div>
</template>

<script>
    import back from '../../common/back.vue';
    import textBorder from '../../common/textBorder.vue';
    import systemDialog from '../../common/systemDialog.vue';
    import cuatomerDialog from '../../common/customerDialog.vue';
    import contractDialog from '../../common/contractDialog.vue'
    import { getDict } from '@/utils/tools.js'
    import { projectSave, projectUpdate, proInfo } from '@/api/project/index'
    import { contractInfo } from '@/api/contract'
    import upload2 from '@/components/common/upload2.vue'
    import { getFileTypeNum } from '@/utils/tools'

    import { selectTemplate } from '@/api/project/index.js'
    export default {
        components: {
            back,
            textBorder,
            systemDialog,
            cuatomerDialog,
            contractDialog,
            upload2
        },
        data() {
            return {
                rules: {
                    projectName: [
                        { required: true, message: '请输入项目名称', trigger: 'blur' },
                    ],
                    customerId: [
                        { required: true, message: '请选择客户', trigger: 'change' },
                    ],
                    chargePerson: [
                        { required: true, message: '请选择负责人', trigger: 'change' },
                    ],
                    projectAmount: [
                        { required: true, message: '请输入项目金额', trigger: 'blur' },
                    ],
                    projectStatus: [
                        { required: true, message: '请选择项目状态', trigger: 'change' }
                    ],
                    projectType: [
                        { required: true, message: '请选择项目类型', trigger: 'change' }
                    ],
                    beginTime: [
                        { required: true, message: '请选择开始时间', trigger: 'change' }
                    ],
                    endTime: [
                        { required: true, message: '请选择结束时间', trigger: 'change' }
                    ],
                    isTemplate: [
                        { required: true, message: '请选择活动资源', trigger: 'change' }
                    ],
                    templateId: [
                        { required: true, message: '请选择模版', trigger: 'change' }
                    ]
                },
                optionsStatus: [
                    {
                        value: 1,
                        name: '未开始',
                    },
                    {
                        value: 2,
                        name: '进行中',
                    },
                    {
                        value: 3,
                        name: '已完成',
                    },
                ],
                pStatus: {
                    1: [
                        {
                            value: 1,
                            name: '未开始',
                        },
                        {
                            value: 2,
                            name: '进行中',
                        },
                        {
                            value: 3,
                            name: '已完成',
                        },
                    ],
                    2: [
                        {
                            value: 2,
                            name: '进行中',
                        },
                        {
                            value: 3,
                            name: '已完成',
                        },
                    ],
                    3: [
                        {
                            value: 3,
                            name: '已完成',
                        },
                    ],
                },
                optionsContract: [],
                optionsContractDetail: [],
                unitDialogVisible: false,
                fileList: [],
                dialogVisible: false,
                dialogName: '',
                options: [],
                customerDialogVisible: false,
                form: {
                    projectName: '',
                    projectStatus: '',
                    customerId: '',
                    contractId: '',
                    contractName: '',
                    beginTime: '',
                    endTime: '',
                    projectType: '',
                    projectTypeSub: "",
                    projectAmount: "",
                    isTemplate: 0,
                    templateId: '',
                    chargePerson: '',
                    collaborator: '',
                    notes: '',
                    fileInfoList: [],
                },
                contractAmount: '',
                contractVisible: false,
                multipleNum: 1,
                fileListPdf: [],
                proId: '',
                xiezuolist: [],
                titleName: '新建项目',
                showTem: false,
                disabled: false,
            }
        },
        created() {
            if (this.$route.query.contractId) {
                this.form.contractId = this.$route.query.contractId;
                this.loadContract(this.form.contractId);
            }
            this.projectstagetemplateListData()
            if (this.$route.query.id) {
                this.proId = this.$route.query.id
                this.proInfoData()
                this.titleName = '编辑项目'
            } else {
                this.showTem = true
                this.getDictApi()
            }
        },
        methods: {
            closeCon() {
                this.form.contractName = ''
                this.form.contractId = ''
                this.contractAmount = ''
            },
            getDictApi() {
                getDict('ContractType').then(res => {
                    this.optionsContract = res
                    if (this.form.projectType) {
                        this.setValue(this.form.projectType)
                    }
                })
            },
            proInfoData() {
                proInfo(this.proId).then(res => {
                    if (res.status == 0) {
                        this.form = res.data
                        this.contractAmount = res.data.contractAmount == 0 ? "" : res.data.contractAmount;
                        this.form.templateId = res.data.templateId == 0 ? '' : res.data.templateId
                        if (res.data.projectStatus == 2 || res.data.projectStatus == 3) {
                            this.optionsStatus = this.pStatus[res.data.projectStatus]
                        }
                        if (res.data.projectStatus == 3) {
                            this.disabled = true
                        }
                        var ids = res.data.collaborator && res.data.collaborator.split(',');
                        let names = res.data.collaboratorName && res.data.collaboratorName.split(',');
                        ids && ids.forEach((item, index) => {
                            var data = {};
                            data.id = item;
                            data.name = names[index];
                            this.xiezuolist.push(data)
                        });
                        res.data.fileInfoList.forEach(item => {
                            item.name = item.fileName
                        })
                        this.fileListPdf = res.data.fileInfoList
                        this.$refs.upload2.setFileList(this.fileListPdf)
                        this.getDictApi()
                    }
                })
            },
            projectstagetemplateListData() {
                selectTemplate().then(res => {
                    if (res.status == 0) {
                        this.options = res.data
                    }
                })
            },
            submitPdf(fileList) {
                this.fileListPdf = fileList
            },
            loadContract(contractId) {
                contractInfo(contractId).then((result) => {
                    this.form.contractName = result.data.contractTitle;
                    this.contractAmount = result.data.contractAmount;

                }).catch((err) => {

                });
            },
            changeValue(val) {
                if (val == '') {
                    this.form.projectTypeSub = ''
                    this.optionsContractDetail = []
                } else {
                    this.form.projectTypeSub = ''
                    let item = this.optionsContract.filter(item => item.id === val)
                    this.optionsContractDetail = item[0].children
                }
            },
            setValue(val) {
                let item = this.optionsContract.filter(item => item.id === val)
                this.optionsContractDetail = item[0].children
            },
            arrForEach(arrFileList) {
                arrFileList.forEach(item => {
                    this.form.fileInfoList.push({
                        ...item,
                        fileType: getFileTypeNum(item.url)
                    })
                })
            },
            saveButton() {
                this.$refs['addform'].validate((valid) => {
                    if (valid) {
                        if (this.form.customerId == '') {
                            this.msgError('请选择客户')
                            return
                        }
                        if (this.form.projectType == '' || this.form.projectTypeSub == '') {
                            this.msgError('请选择项目类型')
                            return
                        }
                        if (this.form.isTemplate) {
                            if (this.form.templateId == '') {
                                this.msgError('请选择模板')
                                return
                            }
                        }
                        if (this.form.chargePerson == '') {
                            this.msgError('请选择负责人')
                            return
                        }
                        this.form.fileInfoList = []
                        if (this.fileListPdf.length > 0) {
                            this.arrForEach(this.fileListPdf)
                        }
                        if (this.proId) {
                            projectUpdate(this.form).then(res => {
                                if (res.status == 0) {
                                    this.msgSuccess('更新成功')
                                    this.$router.back();
                                }
                            })
                        } else {
                            projectSave(this.form).then(res => {
                                if (res.status == 0) {
                                    this.msgSuccess('添加成功')
                                    this.$router.back();
                                }
                            })
                        }

                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            chooseContract() {
                if (this.$route.query.contractId) {
                    return
                }
                if (this.form.contractId == 0) {
                    this.form.contractId = ''
                }
                let params = {
                    id: this.form.contractId || '',
                    methodName: 'list',
                    className: 'ProjectController',
                    opportunityId: ''
                }
                this.$refs.contractRef.selectContractData(params)
                this.contractVisible = true;
            },
            updateContractData(data) {
                this.form.contractName = data.contractTitle;
                this.form.contractId = data.id;
                this.contractAmount = data.contractAmount;
            },
            updateContractVisible(val) {
                this.contractVisible = val;
            },
            updateCustomer(data) {
                this.form.customerName = data.customerName;
                this.form.customerId = data.id;
                this.form.unitId = data.unitId
                this.form.unitName = data.unitName
                this.$refs.addform.validateField(['customerId'])
            },
            updateVisible(val) {
                this.customerDialogVisible = val;
            },
            chooseCustomer() {
                this.customerDialogVisible = true;
                this.$refs.customer.selectCustomerData({ id: this.form.customerId, className: 'ProjectController' })
            },
            // 选择协作人
            clickXuan(name, multipleNum) {
                this.dialogName = name;
                this.multipleNum = multipleNum;
                this.$refs.systemdialog.loadData();
                if (name == '选择负责人') {
                    this.form.chargePerson ? this.$refs.systemdialog.updateWorksId([{ id: this.form.chargePerson, name: this.form.chargePersonName,departmentId:this.form.customerDepartmentId }]) :this.$refs.systemdialog.updateWorksId([]) ;
                } else if (name == '选择协作人') {
                    this.form.collaborator ? this.$refs.systemdialog.updateWorksId(this.xiezuolist) : this.$refs.systemdialog.updateWorksId([]);
                }
                this.dialogVisible = true;
            },
            updateSystemVisible(value) {
                this.dialogVisible = value;
            },
            handleTagClose(tag) {
                this.xiezuolist.splice(this.xiezuolist.indexOf(tag), 1);
                this.xiezuoData(this.xiezuolist)
            },
            xiezuoData(list) {
                var ids = [];
                var names = [];
                list.forEach(item => {
                    ids.push(item.id);
                    names.push(item.name);
                });
                this.form.collaborator = ids.join(',');
                this.form.collaboratorName = names.join(',');
            },
            submitData(data, type, departmentId) {
                if (type == '选择负责人') {
                    this.form.chargePerson = data.length > 0 ? data[0].id : '';
                    this.form.chargePersonName = data.length > 0 ? data[0].name : '';
                    this.form.customerDepartmentId = departmentId;
                    this.$refs.addform.validateField(['chargePerson'])
                } else if (type == '选择协作人') {
                    this.xiezuoData(data)
                    this.xiezuolist = data;
                }
                this.updateSystemVisible(false)
            },
        }
    }
</script>
<style scoped lang="scss">
    .discss {
        background-color: #f5f7fa;
    }

    .tagcss /deep/ .el-icon-close {
        width: 12px;
        height: 12px;
        line-height: 12px;
        background-color: #4285F4;
        color: white;
    }

    .tagcss {
        margin-left: 8px;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #4285F4;
        background: #DFEAFD;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
    }

    .ellipsis /deep/.el-input__inner {
        padding: 0 8px !important;
    }

    /deep/ .el-input__suffix {
        right: 0 !important;
    }

    .studiocss img {
        width: 16px;
        height: 16px;
        margin-right: 3px;
        margin-top: -3px;
    }

    .eldate {
        width: 100% !important;
        padding: 0;

        /deep/.el-input__inner {
            height: 34px;
            line-height: 34px;
        }

        /deep/.el-input__icon {
            line-height: 34px;
        }
    }

    .els50 {
        width: 58%;
    }

    .els40 {
        width: 40%;
        margin-right: 1%;
    }

    .els50:last-child {
        margin-right: 0;
        margin-left: 1%;
        float: right;
    }

    .elflex {
        display: flex;
    }

    .w98 {
        width: 98px;
    }

    .flex {
        display: flex;
        flex-direction: column;
    }

    .rcenter {
        position: absolute;
        right: 10px;
        line-height: 34px;
        font-size: 14px;
        color: #c0c4cc;
    }

    .btncenter {
        text-align: center;
    }

    .quanxiancss /deep/.el-form-item__label {
        margin-left: -7px;
        width: 125px !important;
    }

    .addresscss {
        max-height: 68px;
    }

    .input-new-tag {
        width: 200px;
        margin-left: -8px;

    }

    .input-new-tag /deep/.el-input__inner {
        border: none;
        background-color: rgba(0, 0, 0, 0);
    }

    .tagcol /deep/.el-form-item__label {
        height: 34px;
        line-height: 34px;
    }

    .tagcol /deep/.el-form-item__content {
        background: #F5F5F5;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        min-height: 34px;
        line-height: 34px;
    }

    .mainbg {
        margin: 16px 0px;
        padding: 20px 16px;
        background-color: white;
        border-radius: 8px;
    }

    .pd0 {
        padding-right: 0px !important;
    }
</style>
<style>

</style>