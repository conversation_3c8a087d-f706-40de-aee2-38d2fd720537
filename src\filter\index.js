/**
 * @Desc 全局过滤器
 */
import Dict from '@/config/dict.js'

//时间过滤成  2019/9/2  
const dateFormat = function (value) {
  if (value) {
    return new Date(value).toLocaleDateString()
  }
  return ''
}
/*//时间过滤成  2019-09-02  
const dateFormat1 = function (value) {
  if (value) {
    function addDateZero(num) {
        return (num < 10 ? "0" + num : num);
    }
    let d = new Date(datetime);
    
                case 'mm':
                    return time.getMinutes();
                    break;
                case 'dd':
                    return time.getDate();
                    break;
                case 'HH':
                    return time.getHours();
                    break;
                case 'ss':
                    return time.getSeconds();
                    break;
        }
      });
    }
    let formatdatetime = format(value.getFullYear()) + '-' + addDateZero(value.getMonth() + 1) + '-' + addDateZero(value.getDate()) + ' ' + addDateZero(value.getHours()) + ':' + addDateZero(value.getMinutes()) + ':' + addDateZero(value.getSeconds());
    formatdatetime=
    
    return formatdatetime;
  }
  return ''
}*/

const dict = function (value, dictKey) {
  if (!value && value !== 0) return '';
  if (Dict.hasOwnProperty(dictKey)) {
    let ary = Dict[dictKey];
    try {
      for (let i = 0; i < ary.length; i++) {
        if (ary[i].value === value.toString()) {
          return ary[i].label
        }
      }
    } catch (e) {
      console.log(e)
    }
  }
}

export default {
  dateFormat,
  dict
}
