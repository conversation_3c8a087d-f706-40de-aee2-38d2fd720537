<template>
  <div class="echart-page">
    <back>返回</back>
    <div class="title-container">
      <h3>{{dtitle}}</h3>
    </div>
    <div id="unitchart"></div>
    
    <el-dialog 
      :visible.sync="nodeDialogVisible" 
      :title="title"
      width="80%" 
      append-to-body
      class="node-dialog">
      <div class="node-detail">
        <div class="page-header">
            <el-table class="customertable mytable tootiptable" height="590px" v-loading="isLoading"  :data="tableData" style="width: 100%">
                <el-table-column prop="customerName" label="客户名称" align="center" width="90px">
                    <template slot-scope="scope">
                        <span class="bbtn" @click="toDetail(scope.row)">{{ scope.row.customerName }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="unitDepartment" label="部门" align="center" width="120px">
                </el-table-column>
                <el-table-column prop="duties" label="职务" align="center" width="120px">
                </el-table-column>
                
                <el-table-column prop="customerLevelName" label="客户级别" align="center" width="100px">
                    <template slot-scope="scope">
                        <span :class="levelColor[scope.row.customerLevelName]">{{ scope.row.customerLevelName }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="phone" align="center" label="联系电话" width="120px">
                </el-table-column>
                <el-table-column prop="majorNames" label="负责专业" align="center"  min-width="200px">
                    <template slot-scope="scope">
                                {{scope.row.specialtyName.length>0 ? scope.row.specialtyName.join(',') :'—'}}
                    </template>
                </el-table-column>
                <el-table-column  label="负责人" align="center" width="160px">
                    <template slot-scope="scope">
                        <div v-if="scope.row.chargePersonNames.length>0">
                            <el-tag class="cuscss" v-for="(item,index) in scope.row.chargePersonNames" :key="index">{{item}}</el-tag>
                        </div>
                        <div v-else>
                            —
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" align="center" width="160px">
                </el-table-column>
                <template slot="empty">
                    <nolist></nolist>
                </template>
            </el-table>
            <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                @updatePageNum="handleCurrentChange"></page>
            </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import page from '@/components/common/page.vue';
import nolist from '@/components/common/nolist.vue';
import back from '@/components/common/back.vue';
import {
  queryStructureTreeList,
  cusList,
} from '@/api/unit'

import { downloadExcelFile } from '@/utils/tools'
import { customerLevelColors } from  '@/utils/dict.js'
export default {
    components: {
        page,
        nolist,
        back
    },
    data(){
        return{
            visible:false,
            dtitle:'',
            nodeDialogVisible: false,
            nodeInfo: {},
            title:'下属客户',
            pageBean: {
                pageNum: 1,
                pageSize: 10,
            },
            isLoading: false,
            tableData: [],
            total: 0,
            levelColor:customerLevelColors,
            option:{
                tooltip: {
                    trigger: 'item',
                    triggerOn: 'mousemove'
                },
                    toolbox: {
                        feature: {
                            saveAsImage: {
                                title:'保存为图片',
                                name:""
                            }
                        }
                    },
                    series: [
                    {
                    type: 'tree',
                    data: [],
                    left: '2%',
                    right: '2%',
                    top: '5%',
                    bottom: '20%',
                    symbol: 'polyline',
                    avoidLabelOverlap: true,//防止标签重叠
                    // roam: true, //移动+缩放  'scale' 或 'zoom'：只能够缩放。 'move' 或 'pan'：只能够平移。
                    // scaleLimit:{ //缩放比例
                    //     min:0.7,//最小的缩放值
                    //     max:4,//最大的缩放值  
                    // },
                    layout: 'orthogonal',//树图布局，orthogonal水平垂直方向，radial径向布局 是指以根节点为圆心，每一层节点为环，一层层向外
			        orient: 'TB', //树形方向  TB为上下结构  LR为左右结构
                    initialTreeDepth: 3, //初始展开的层级
                    edgeShape: 'polyline',
                    expandAndCollapse: false,//设为false禁用折叠展开交互
                    lineStyle: {//结构线条样式
                        width: 0.7,
                        color: '#4285F4',
                        type: 'broken'
                    },
                    label: {//节点文本样式
                    normal: {
                        backgroundColor: '#4285F4',
                        position: 'top',
                        verticalAlign: 'middle', //文字垂直对齐方式
                        align: 'center',
                        borderColor: '#4285F4',
                        color:'#fff',
                        borderWidth: 1,
                        borderRadius: 5,
                        padding: [0,10,0,10],
                        height: 40,
                        width:100,
                        offset: [0,0],//节点文字与圆圈之间的距离
                        fontSize: 14 ,
                        // 节点文本阴影
                        // shadowBlur: 10,
                        // shadowColor: 'rgba(0,0,0,0.25)', 
                        // shadowOffsetX: 0,
                        // shadowOffsetY: 2,
                        }
                    },
                    leaves: { //叶子节点文本样式
                        label: {
                            backgroundColor: 'rgba(0,0,0,0)',
                            position: 'top',
                            verticalAlign: 'middle', //文字垂直对齐方式
                            align: 'center',
                            borderColor:'rgba(0,0,0,0)',
                            color:'#333333',
                            borderWidth: 1,
                            borderRadius: 5,
                            padding:[10,0,0,10],
                            height:130,
                            width:20,
                            offset: [0,80],//节点文字与圆圈之间的距离
                            fontSize: 13 ,
                            // 节点文本阴影
                            // shadowBlur: 10,
                            // shadowColor: 'rgba(0,0,0,0.25)', 
                            // shadowOffsetX: 0,
                            // shadowOffsetY: 2,
                            overflow:'break',//break为文字折行，  truncate为文字超出部分省略号显示
                            lineOverflow:'truncate',//文字超出高度后 直接截取
                        }
                    },
                    // 改为false，禁止树结构的展开折叠行为
                    expandAndCollapse: false,
                    animationDuration: 550,
                    animationDurationUpdate: 750
                    }
                ]
            }
        }
    },
    created() {
        const unitId = this.$route.query.id
        const unitName = this.$route.query.unitName
        if (unitId && unitName) {
            this.dtitle = unitName + '架构图'
            this.getTagTypeData(unitId, unitName)
        }
    },
    methods:{
        getTagTypeData(id, name) {
            queryStructureTreeList({ unitId: id }).then(res => {
                if (res.status === 0) {
                    if (res.data.length > 0) {
                        this.$nextTick(() => {
                            var chartDom = document.getElementById('unitchart');
                            var myChart = echarts.init(chartDom);
                            this.option.series[0].data = [
                                {
                                    name: name,
                                    children: this.changeDataFiled(res.data),
                                }
                            ]
                            this.option.toolbox.feature.saveAsImage.name = this.dtitle
                            myChart.setOption(this.option)
                            
                            // 添加点击事件
                            myChart.off('click');
                            myChart.on('click', 'series', (params) => {
                                this.handleNodeClick(params);
                            });
                        })
                    } else {
                        this.$message.error('该单位暂无架构信息可以查看')
                    }
                }
            })
        },
        changeDataFiled(array){
            array.forEach(element => {
                element.children = element.childList
                if (element.childList.length>0) {
                   return this.changeDataFiled(element.childList)
                }else{
                    console.log('element.name=====',element.name)
                }
            });
            return array
        },
        handleNodeClick(params) {
            if (params.data) {
                this.nodeInfo = params.data;
                this.nodeDialogVisible = true;
                this.pageBean.structureId = params.data.id;
                this.pageBean.pageNum = 1;
                this.loadData();
            }
        },
        loadData() {
            this.isLoading = true;
            cusList(this.pageBean)
                .then(res => {
                    if (res.status == 0) {
                        this.tableData = res.data;
                        this.total = res.page.total;
                    } else {
                        this.$message.error(res.msg);
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        toDetail(row) {
            // 跳转到客户详情页
            this.$router.push({
                path: '/clientManagement/unit/unitsysdetail',
                query: {
                    id: row.id,
                    sId: this.pageBean.structureId,
                }
            });
        },
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.loadData();
        },
    }
}
</script>

<style lang="scss" scoped>
.echart-page {
    padding: 20px;
    background-color: #fff;
}

.title-container {
    margin: 10px 0;
    text-align: center;
}

#unitchart{
    width: 100%;
    height:85vh;
    overflow: auto;
}

.node-detail {
    padding: 10px;
    line-height: 24px;
}
.bbtn{
    cursor: pointer;
}
</style>