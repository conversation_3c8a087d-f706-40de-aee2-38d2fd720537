<template>
  <div>
    <el-table class="detailList mytable" border  :data="tableData" style="width: 100%"  v-loading="isLoading">
      <el-table-column
        prop="reason"
        label="发放事由"
        align="center"
        class-name="column_blue"
        show-overflow-tooltip
        min-width="200px"
        >
      </el-table-column>
      <el-table-column prop="giveType" label="发放方式" align="center" width="100px">
          <template slot-scope="scope">
              <span>{{ giveTypeMap[scope.row.distributionForm] }}</span>
          </template>
      </el-table-column>
      <el-table-column prop="name"  label="样书" align="center" min-width="167px">
          <template slot-scope="scope" >
              <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                  <el-tooltip :content="item.name" placement="left" :disabled="item.isShow"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                      <div class="itemlinecss">{{ item.name}}</div>
                  </el-tooltip>
              </div>
              <div v-else>{{ scope.row.goodsName }}</div>
          </template>
      </el-table-column>
      <el-table-column prop="platformName" label="出版社" align="center" min-width="167px">
          <template slot-scope="scope">
              <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                  <el-tooltip :content="item.platformName" placement="left" :disabled="item.isShow2"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                      <div class="itemlinecss">{{item.platformName }}</div>
                  </el-tooltip>
              </div>
              <div v-else>
                  {{scope.row.platformName}}
              </div>
              
          </template>
      </el-table-column>
      <el-table-column prop="specialtyName" label="用书专业" align="center" min-width="167px">
          <template slot-scope="scope">
              <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                  <el-tooltip :content="item.specialtyName" placement="left" :disabled="item.isShow3"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                      <div class="itemlinecss">{{item.specialtyName }}</div>
                  </el-tooltip>
              </div>
              <div v-else>
                  {{scope.row.specialtyName}}
              </div>
          </template>
      </el-table-column>
      <el-table-column prop="useBookNumber" label="数量" align="center" min-width="167px">
          <template slot-scope="scope">
              <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                  <div class="itemlinecss" v-for="item in scope.row.distributionDetailList" :key="item.id">
                      {{ 
                          item.number
                      }}
                  </div>
              </div>
              <div v-else>
                  {{scope.row.useBookNumber}}
              </div>
          </template>
      </el-table-column>
      <el-table-column prop="useBookYear" label="用书时间" align="center" min-width="167px">
      </el-table-column>
      <el-table-column prop="useBookNumber" label="数量" align="center" min-width="167px">
      </el-table-column>
      <el-table-column prop="customerName" label="客户" align="center"  show-overflow-tooltip min-width="167px">
      </el-table-column>
      <!-- <el-table-column prop="unitName" label="单位" align="center" min-width="167px">
      </el-table-column> -->
      <el-table-column prop="createByName" label="发放人" align="center" min-width="167px">
      </el-table-column>
      <el-table-column prop="distributionTime" label="发放时间" align="center" min-width="167px">
      </el-table-column>
      <el-table-column prop="edit" label="是否回访" align="center" min-width="167px">
          <template slot-scope="scope">
              <div v-if="scope.row.isRevisit" class="yesc">是</div>
              <div v-else class="noc">否</div>
          </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page 
    :currentPage="pageBean.pageNum" 
    :total="total" 
    :pageSize="pageBean.pageSize" 
    @updatePageNum="handleCurrentChange"
    ></page>
  </div>
</template>

<script>

import page from '../../common/page.vue';
import { distributionList } from "@/api/clientMaintenance/give";
import nolist from '../../common/nolist.vue';
import { giveTypes, giveTypeMap } from '../../../utils/dict'
  export default {
    components:{
      page,
      nolist
    },
   
     data(){
      return{
        giveTypes,
        giveTypeMap,
        isLoading:false,
        tableData:[],
        status:{
          1:"样书",
          2:"礼品",
        },
        total:0,
        pageBean:{
          unitId:this.$route.query.id,
          pageNum:1,
          pageSize:10,
          type: 1,
        }
      }
    },
    created(){
    },
    methods:{
      loadData(){
        this.isLoading = true;
        distributionList(this.pageBean).then((result) => {
          this.tableData = result.data;
          this.total = result.page.total;
          this.isLoading = false;
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      onDetail(type,data){
        switch (type) {
          case 1:
            {
              console.log('明细')
            }
            break;
          case 2:// 详情
            this.$router.push({
              path:"/projectManagement/project/detail",
              query:{
                id:data.name
              }
            })
            break;
          default:
            break;
        }
        
      },
      handleCurrentChange(page){
        this.pageBean.pageNum = page;
        this.loadData();
      }
    }
  }
</script>

<style lang="scss" scoped>
.detailList{
  min-height: 590px;
}
.itemlinecss{
    margin-left: -10px !important;
    margin-right: -10px !important;
    border-bottom: 1px solid #F0F0F0;
    padding: 14px 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center !important;
}
.itemlinecss:first-child{
    padding-top: 0px;
}
.itemlinecss:last-child{
    border-bottom: none;
    padding-bottom: 0px;
}
.tabbtn{
  font-size: 14px;
}
.bbtn,.bbtn:hover,.bbtn:focus{
  color: #4285F4;
}
.yescss{
    color: #56C36E;
}
.nocss{
    color: #F45961;
}

</style>
<style>