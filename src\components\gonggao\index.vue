<template>
    <div>
      <el-card>
          <div class="topContent">
              <div class="leftBtn">
                  <el-input placeholder="请输入公告标题" v-model="input" clearable class="input" size="medium"></el-input>
                  <el-button type="primary" icon="el-icon-search" size="medium" @click="fetchData">搜索</el-button>
              </div>
              <div class="rightBtn">
                <el-button type="primary" size="medium" @click="goToAdd">新增</el-button>
              </div>
          </div>
          <div class="middle">
              <el-table
              :data="tableData"
              style="width: 100%">
              <el-table-column
                prop="version"
                label="公告标题"
                align="center">
              </el-table-column>
              <el-table-column
                prop="createTime"
                label="推送时间"
                align="center">
              </el-table-column>
              <el-table-column
                  fixed="right"
                  label="操作"
                  width="200"
                  align="center">
                  <template slot-scope="scope">
                    <el-button type="text" @click="goToEdit(scope.row.id)">编辑</el-button>
                  <el-button type="text" class="deleteBtn"  @click="openDeleteDialog(scope.$index, scope.row)">删除</el-button>
                  </template>
              </el-table-column>
            </el-table>
          </div>

          <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"></page>

          <el-dialog title="温馨提示" :visible.sync="deleteDialogVisible" width="30%" center>
              <span class="text">是否删除该公告？</span>
              <span slot="footer" class="dialog-footer">
                  <el-button @click="deleteDialogVisible = false">取消</el-button>
                  <el-button type="danger" @click="confirmDelete">确定</el-button>
              </span>
          </el-dialog>
    </el-card>
    </div>
  </template>
  
<script>
import page from '../common/page.vue';
import { fetchGonggaoList,deleteGonggao, } from '@/api/gongao/index.js';

    export default {
    components:{
        page
    },
    data() {
        return {
          input:'',
          tableData: [],
          total: 0,
          pageBean: {
                pageNum: 1,
                pageSize: 10,
                version:''
            },
        deleteDialogVisible: false,
        currentDeleteId: null 
        }
    },
    created() {
        this.fetchData();
    },
    methods:{
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.fetchData();
        },
        goToAdd() {
            this.$router.push({ path: '/gonggao/add' });
        },
        goToEdit(id) {
            this.$router.push({ path: '/gonggao/add', query: { id } });
        },
        openDeleteDialog(index, row) {
            this.deleteDialogVisible = true;
            this.currentDeleteId = row.id;
        },
        confirmDelete() {
            deleteGonggao(this.currentDeleteId).then(() => {
                this.deleteDialogVisible = false;
                this.$message.success('删除成功');
                this.fetchData();
            }).catch(error => {
                console.error('删除失败:', error);
            });
        },
        fetchData() {
          this.pageBean.version = this.input;
          fetchGonggaoList(this.pageBean).then(response => {
              this.tableData = response.data; 
              this.total = response.page.total;
          }).catch(error => {
              console.log('=====');
          });
      }
    }
}
  </script>
  
  <style scoped>
  .topContent{
    display: flex;
    justify-content: space-between;
  }
  .input{
    width: 200px;
    margin-right: 20px;
  }
  .text{
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .deleteBtn{
    color: red;
  }
  </style>