<template>
  <div class="mainbg fixpb">
    <div v-loading="isLoading">
      <el-form :inline="true" class="myform">
        <el-form-item label="">
          <el-input
            class="definput iw"
            v-model="pageBean.materialName"
            clearable
            placeholder="教材名称或出版社名称或ISBN"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            class="definput wfi"
            v-model="pageBean.unitName"
            clearable
            placeholder="请输入收货单位"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            class="definput wfi"
            v-model="pageBean.receiveName"
            clearable
            placeholder="请输入收货人"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认状态：" label-width="85px">
          <el-select
            class="definput"
            popper-class="removescrollbar"
            clearable
            v-model="pageBean.isConfirm"
            placeholder="请选择"
          >
            <el-option key="2" label="已确认" value="2"> </el-option>
            <el-option key="1" label="未确认" value="1"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数量范围：" label-width="85px">
          <el-select
            class="definput wfi"
            popper-class="removescrollbar"
            clearable
            v-model="pageBean.isSendNumber"
            placeholder="请选择"
          >
            <el-option
              v-for="item in famweis"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="fr">
          <el-button
            v-isShow="'crm:business:materialdeliver:save'"
            class="defaultbtn"
            icon="el-icon-plus"
            type="primary"
            @click="addAction"
            >新增</el-button
          >
        </el-form-item>
        <el-form-item label="">
          <el-input
            class="definput iw"
            v-model="pageBean.chargeName"
            clearable
            placeholder="业务负责人"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            class="definput wfi"
            v-model="pageBean.confirmName"
            clearable
            placeholder="仓库负责人"
          ></el-input>
        </el-form-item>
        <el-button
          class="defaultbtn mt"
          icon="el-icon-search"
          type="primary"
          @click="searchAction"
          >搜索
        </el-button>
      </el-form>
      <el-table class="customertable mytable tootiptable" :data="tableData" border>
        <el-table-column
          prop="name"
          label="教材名称"
          align="center"
          min-width="250px"
        >
          <template slot-scope="scope" >
              <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                  <el-tooltip :content="item.name" placement="left" :disabled="item.isShow"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                      <div class="itemlinecss bbtn">{{ item.name}}</div>
                  </el-tooltip>
              </div>
              <div class="itemlinecss bbtn" v-else>{{ scope.row.materialName }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="platformName"
          label="出版社"
          align="center"
          width="200px"
        >
        <template slot-scope="scope">
            <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                <el-tooltip :content="item.platformName" placement="left" :disabled="item.isShow2"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                    <div class="itemlinecss">{{item.platformName }}</div>
                </el-tooltip>
            </div>
            <div class="itemlinecss" v-else>
                {{scope.row.platformName}}
            </div>
            
        </template>
        </el-table-column>
        <el-table-column prop="isbn" label="ISBN" align="center" width="150px">
          <template slot-scope="scope">
              <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                  <div  v-for="item in scope.row.distributionDetailList" :key="item.id" class="itemlinecss">{{item.isbn }}</div>
              </div>
              <div class="itemlinecss" v-else>
                  {{scope.row.isbn}}
              </div>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" align="center" width="100px">
          <template slot-scope="scope">
              <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                  <div v-for="item in scope.row.distributionDetailList" :key="item.id" class="itemlinecss">{{item.price }}</div>
              </div>
              <div v-else>
                  {{scope.row.price}}
              </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sendNumber"
          label="发货量"
          align="center"
          width="100px"
        >
        <template slot-scope="scope">
            <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                <div class="itemlinecss" v-for="item in scope.row.distributionDetailList" :key="item.id">{{item.number }}</div>
            </div>
            <div v-else>
                {{scope.row.sendNumber}}
            </div>
        </template>
        </el-table-column>
        <el-table-column
          prop="discount"
          label="折扣"
          align="center"
          width="100px"
        >
        </el-table-column>
        <el-table-column
          prop="chargeName"
          label="业务负责人"
          align="center"
          width="120px"
        >
        </el-table-column>
        <el-table-column
          prop="useBookYear"
          label="用书时间"
          align="center"
          width="120px"
        >
        </el-table-column>
        <el-table-column
          prop="specialtyName"
          label="用书专业"
          align="center"
          width="150px"
        >
        <template slot-scope="scope">
            <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                <el-tooltip :content="item.specialtyName" placement="left" :disabled="item.isShow3"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                    <div class="itemlinecss">{{item.specialtyName }}</div>
                </el-tooltip>
            </div>
            <div v-else>
                {{scope.row.specialtyName}}
            </div>
        </template>
        </el-table-column>
        <el-table-column
          prop="deliveryTime"
          label="发货时间"
          align="center"
          width="150px"
        >
        </el-table-column>
        <el-table-column
          prop="trackingNumber"
          label="物流单"
          align="center"
          width="150px"
        >
        </el-table-column>
        <el-table-column
          prop="unitName"
          label="收货单位"
          align="center"
          width="200px"
        >
        </el-table-column>
        <el-table-column
          prop="receiveName"
          label="收货人"
          align="center"
          width="120px"
        >
        </el-table-column>
        <el-table-column
          prop="confirmName"
          label="仓库负责人"
          align="center"
          width="120px"
        >
        </el-table-column>
        <el-table-column
          prop="isConfirm"
          label="是否确认"
          align="center"
          width="100px"
        >
          <template slot-scope="scope">
            <span :class="{ gbtn: scope.row.isConfirm == 2 }">
              {{ isConfirms[scope.row.isConfirm] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="edit"
          width="200"
          align="right"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              v-show="
                scope.row.isConfirm != 2 && userid == scope.row.confirmPerson
              "
              class="bbtn"
              type="text"
              @click="confirmAction(scope.row)"
            >
              确认
            </el-button>
            <el-button class="bbtn" type="text" @click="toDetail(scope.row)">
              详情
            </el-button>
            <el-button
              :disabled="userid != scope.row.chargePerson"
              class="bbtn"
              type="text"
              @click="addgz(scope.row)"
            >
              跟踪
            </el-button>
            <el-button v-isShow="'crm:business:materialdeliver:update'" class="ebtn" type="text" @click="onEdit(scope.row)">
              编辑</el-button
            >
            <el-button
              class="rbtn"
              type="text"
              @click="deleteAction(scope.row)"
            >
              删除</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <nolist></nolist>
        </template>
      </el-table>

      <div class="fixpage">
        <page
          :currentPage="pageBean.pageNum"
          :total="total"
          :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"
        ></page>
      </div>

      <dc-dialog
        iType="1"
        title="温馨提示"
        width="500px"
        :dialogVisible.sync="dialogDeleteVisible"
        @submit="deleteButton"
      >
        <template> </template>
        <p class="pcc">是否删除该教材发货记录？删除后将无法恢复。</p>
      </dc-dialog>
    </div>
    <!-- 导入提示 -->
    <dc-dialog
      iType="2"
      title="导入信息提示"
      width="500px"
      :showCancel="false"
      :dialogVisible.sync="dialogMsgVisible"
      @submit="submitMsgDialog"
      :appendToBody="true"
    >
      <template> </template>
      <p class="pcc" v-for="(item, index) in errorData" :key="index">
        {{ item }}
      </p>
    </dc-dialog>
    <el-dialog
      title="跟踪"
      :visible.sync="gzDialogVisible"
      width="500px"
      center
    >
      <el-input
        type="textarea"
        v-model="gzpar.notes"
        :rows="5"
        placeholder="请输入跟踪信息"
      ></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGZData" :loading="isSubmit"
          >提 交</el-button
        >
      </span>
    </el-dialog>
    <detailDrawer
      ref="deliverDetailDrawer"
      :visible.sync="detailDetailVisible"
      @updateVisible="updateDetailVisible"
    />
  </div>
</template>
<script>
import page from '@/components/common/page.vue'
import nolist from '@/components/common/nolist.vue'
import {
  deliverList,
  deletedeliver,
  addtrack,
  verify,
} from '@/api/materialdeliver/index'
import {
  getDict,
  downloadFileByUrl,
  downloadExcelFile,
  getParStr,
} from '@/utils/tools'
import { getToken } from '@/utils/auth'
import detailDrawer from './common/detailDrawer.vue'
export default {
  components: {
    page,
    nolist,
    detailDrawer,
  },
  data() {
    return {
      isSubmit: false,
      gzDialogVisible: false,
      detailDetailVisible: false,
      gzpar: {
        deliverId: '',
        notes: '',
      },
      dialogTitle: '',
      dialogName: '选择业务经理',
      multipleNum: 1,
      dialogVisible: false,
      dType: '2',
      userid: window.sessionStorage.getItem('userid'),
      famweis: [
        { id: '1', name: '1000册以内' },
        { id: '2', name: '1000册~3000册' },
        { id: '3', name: '3000册～5000册' },
        { id: '4', name: '5000册以上' },
      ],
      isConfirms: {
        2: '已确认',
        1: '未确认',
      },
      optionsPress: [],
      dialogTableVisible: false,
      dialogDeleteVisible: false,
      dialogType: '1',
      deleteBookId: '',
      isShowCancel: true,
      typename: '',
      levels: [],
      isLoading: false,
      tableData: [],
      total: 0,
      deletemsg: '',
      pageBean: {
        materialName: '',
        unitName: '',
        receiveName: '',
        confirmName: '',
        chargeName: '',
        isConfirm: '',
        isSendNumber: '',
        pageNum: 1,
        pageSize: 10,
      },
      ruleForm: {
        name: '',
        author: '',
        isJoin: undefined,
        publicationRevisionTime: '',
        isbn: '',
        price: undefined,
        platformId: '',
        operationManager: '',
      },
      rules: {
        name: [{ required: true, message: '请输入教材名称', trigger: 'blur' }],
        platformId: [
          { required: true, message: '请选择出版社', trigger: 'change' },
        ],
        isJoin: [
          { required: true, message: '请选择是否合作', trigger: 'change' },
        ],
        author: [{ required: true, message: '请输入主编', trigger: 'blur' }],
        price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
      },
      errorData: [],
      isDownload: false,
      dialogMsgVisible: false,
      isImport: false,
      getUrl: `${process.env.VUE_APP_BASE_API}crm/business/teachingmaterial/batchImportExcel`,
      headers: { Authorization: getToken() },
      fileData: {
        applicationId: sessionStorage.getItem('applicationId'),
      },
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
    }
    this.loadData()
  },
  methods: {
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      deliverList(this.pageBean)
        .then((result) => {
          result.data.map(item =>{
              item.distributionDetailList && item.distributionDetailList.forEach(element => {
                  element.isShow = element.name.length>15 ? false : true
                  element.isShow2 = element.platformName.length>12 ? false : true
                  element.isShow3 = element.specialtyName.length>10 ? false : true
              });
          })
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    onEdit(data) {
      if (data.isConfirm == 2) {
        return this.$message({
          type: 'warning',
          message: '该教材发货信息已进行确认，无法编辑',
        })
      }
      this.$router.push({
        path: '/materialdeliver/add',
        query: {
          id: data.id,
        },
      })
    },
    handleClose() {
      Object.keys(this.ruleForm).forEach((item) => {
        this.ruleForm[item] = ''
      })
      this.$refs.ruleForm.resetFields()
      this.dialogTableVisible = false
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    deleteButton() {
      deletedeliver({ id: this.deleteBookId }).then((result) => {
        if (result.data) {
          this.loadData()
          this.deleteBookId = ''
          this.dialogDeleteVisible = false
          this.$message({
            type: 'success',
            message: '删除成功',
          })
        } else {
          this.$message({
            type: 'error',
            message: result.msg,
          })
          this.dialogDeleteVisible = false
        }
      })
    },
    addAction() {
      this.$router.push({
        path: '/materialdeliver/add',
      })
    },
    toBook(data) {
      this.$router.push({
        path: '/clientManagement/customer/bookinfo',
        query: { id: data.id },
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    deleteAction(data) {
      ;(this.deleteBookId = data.id), (this.dialogDeleteVisible = true)
    },
    download() {
      this.isDownload = true
      pressImportTemplate()
        .then((result) => {
          downloadFileByUrl(result.data.url, '教材导入模版')
          this.isDownload = false
        })
        .catch((err) => {
          this.isDownload = false
        })
    },
    submitMsgDialog() {
      this.errorData = []
      this.dialogMsgVisible = false
    },
    beforeUpload(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['xlsx']
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error('导入明细仅支持 .xlsx 格式!')
        return false
      }
      this.isImport = true
    },
    handleError(file, res) {
      this.isImport = false
    },
    handleAvatarSuccess(res, file) {
      if (res.status == 0 && res.data.errorCount <= 0) {
        this.$message({
          type: 'success',
          message: res.msg,
        })
      } else {
        this.$message({
          type: 'error',
          message: res.msg,
        })
        // 显示错误提示的弹框
        this.dialogMsgVisible = true
        this.errorData = res.data.errorData
      }
      // 刷新数据
      this.loadData()
      this.isImport = false
    },
    addgz(data) {
      this.gzpar.deliverId = data.id
      this.gzpar.notes = ''
      this.gzDialogVisible = true
    },
    submitGZData() {
      if (this.gzpar.notes) {
        this.isSubmit = true
        addtrack(this.gzpar)
          .then((result) => {
            this.isSubmit = false
            if (result.data) {
              this.$message({
                type: 'success',
                message: '添加成功',
              })
              this.gzDialogVisible = false
            } else {
              this.$message({
                type: 'error',
                message: result.msg,
              })
            }
          })
          .catch((err) => {
            this.isSubmit = false
          })
      } else {
        this.$message({
          type: 'info',
          message: '请输入跟踪信息',
        })
      }
    },
    confirmAction(data) {
      this.$confirm('是否确认发货', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
      })
        .then(() => {
          verify({ id: data.id })
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '确认成功!',
                })
                this.loadData()
              } else {
                this.$message({
                  type: 'error',
                  message: result.msg,
                })
              }
            })
            .catch((err) => {})
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    toDetail(data) {
      this.detailDetailVisible = true
      this.$refs.deliverDetailDrawer.loadData(data.id)
    },
    updateDetailVisible(val) {
      this.detailDetailVisible = val
    },
  },
}
</script>
<style scoped>
.itemlinecss{
    margin-left: -10px !important;
    margin-right: -10px !important;
    border-bottom: 1px solid #F0F0F0;
    padding: 14px 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center !important;
}
.itemlinecss:first-child{
    padding-top: 0px;
}
.itemlinecss:last-child{
    border-bottom: none;
    padding-bottom: 0px;
}
.mainbg {
  background-color: white;
  padding: 20px;
  min-height: calc(100vh - 106px);
}
.pscss {
  position: absolute;
  right: 9px;
  top: 10px;
  color: #c0c4cc;
}
.guang /deep/.el-input__inner {
  line-height: 1px !important;
}
.wcss {
  width: 280px;
}
.cDialog /deep/.el-dialog__body {
  display: flex;
  justify-content: center;
  align-content: center;
}
.cDialog /deep/.el-dialog {
  width: 580px !important;
}
.iw {
  width: 230px;
  height: 34px;
}
.iw /deep/.el-input__inner {
  padding-right: 6px;
}
.w100 {
  width: 100px;
}
.wfi {
  width: 170px;
}
.mt {
  margin-top: 4px;
}
.mr10 {
  margin-right: 10px;
}
.right {
  text-align: right;
}
.defaultbtn + .defaultbtn {
  margin-right: 10px;
}
.ml {
  margin-left: 10px;
}
.myform /deep/.el-form-item {
  margin-bottom: 5px;
}
.myform {
  margin-bottom: 15px;
}
.ebtn {
  color: #e6a23c;
}
.gbtn {
  color: green;
}
</style>