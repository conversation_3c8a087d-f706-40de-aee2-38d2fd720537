<template>
  <div class="mainbg" ref="box">
    <div>
      <el-form inline class="myform bbline clearfix">
        <el-form-item>
          <el-input
            clearable
            v-model="pageBean.customerName"
            class="definput inputWid150"
            placeholder="客户名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            clearable=""
            v-model="pageBean.unitName"
            class="definput inputWid150"
            placeholder="客户单位"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            clearable
            v-model="pageBean.createByName"
            class="definput inputWid150"
            placeholder="创建人"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="pageBean.createByDepartment"
            class="definput inputWid150"
            clearable
            placeholder="创建人部门"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            clearable
            v-model="pageBean.keyword"
            class="definput inputWid150"
            placeholder="关键字"
          ></el-input>
        </el-form-item>
        <el-form-item label="跟进类型：">
          <el-select
            clearable
            class="definput width120"
            popper-class="removescrollbar"
            v-model="pageBean.assistType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in types"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间：">
          <datepicker @submitAction="submitAction"></datepicker>
        </el-form-item>

        <el-form-item>
          <el-button
            @click="search"
            class="defaultbtn"
            icon="el-icon-search"
            type="primary"
            >搜索</el-button
          >
        </el-form-item>
        <el-form-item class="">
          <el-button
            v-isShow="'crm:controller:visit:list'"
            class="defaultbtn mt3"
            icon="el-icon-download"
            :loading="isDownload"
            type="primary"
            @click="exportVisit"
            >数据导出</el-button
          >
        </el-form-item>
        <el-form-item class="fr mr16">
          <el-button
            v-isShow="'crm:controller:visit:save'"
            class="defaultbtn mt3"
            icon="el-icon-plus"
            type="primary"
            @click="addVisit"
            >新建拜访</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div v-if="dataList.length == 0 && loadBoo" class="nolist">
      <img class="noimg" src="../../../assets/no.png" alt="" />
      <p>暂无记录~</p>
    </div>
    <div class="scrolldiv" ref="sonRef">
      <el-row :gutter="20">
        <el-col :span="ccol">
          <listitem
            @setWidth="setWidth"
            @updateCommont="updateCommont"
            @loadData="loadData"
            @audioPlay="audioPlay"
            :key="item.id"
            v-for="(item, index) in dataList"
            :item="item"
            ref="commentRef"
          ></listitem>
        </el-col>
        <el-col :span="rcol" class="">
          <el-card :style="{ width: divWidth }" class="box-card lefttable rdiv">
            <i class="el-icon-close closeicon" @click="closeR"></i>
            <el-tabs
              v-model="activeName"
              @tab-click="handleClick"
              class="tabscss vili"
            >
              <el-tab-pane label="详情" name="first">
                <el-form
                  class="infoform"
                  ref="form"
                  :model="formInfo"
                  label-width="80px"
                >
                  <textBorder>基本信息</textBorder>
                  <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
                    <el-col :span="12">
                      <el-form-item
                        label="客户名称:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.customerName }}</span>
                      </el-form-item>
                      <el-form-item label="部门:" class="labeltext">
                        <span>{{ formInfo.unitDepartment }}</span>
                      </el-form-item>
                      <el-form-item
                        label="客户级别:"
                        class="labeltext mulitline"
                      >
                        <span
                          :class="
                            customerLevelColors[formInfo.customerLevelName]
                          "
                          >{{ formInfo.customerLevelName }}</span
                        >
                      </el-form-item>
                      <el-form-item label="负责课程:" class="labeltext">
                        <span>{{ formInfo.responsibleCourse }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="客户单位:" class="labeltext">
                        <span>{{ formInfo.unitName }}</span>
                      </el-form-item>
                      <el-form-item label="职务:" class="labeltext">
                        <span class="colorcss">{{ formInfo.duties }}</span>
                      </el-form-item>
                      <el-form-item label="负责专业:" class="labeltext">
                        <span>{{
                          formInfo.specialtyName
                            ? formInfo.specialtyName.join(',')
                            : ''
                        }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <textBorder>客户标签</textBorder>
                  <el-row :gutter="20" class="width100 mtop20 bbline mb30">
                    <el-col :span="24" class="mb20 ml40">
                      <span
                        class="tagitemcss"
                        :style="colors[index]"
                        :key="index"
                        v-for="(item, index) in formInfo.tags"
                        >{{ item }}</span
                      >
                    </el-col>
                  </el-row>
                  <textBorder>联系信息</textBorder>
                  <el-row :gutter="20" class="width100 mt10 pb5 mb30 bbline">
                    <el-col :span="12" class="mb10">
                      <el-form-item label="电话:" class="labeltext mulitline">
                        <span>{{ formInfo.phone }}</span>
                      </el-form-item>
                      <el-form-item label="微信:" class="labeltext mulitline">
                        <span>{{ formInfo.wechat }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" class="mb10">
                      <el-form-item label="邮箱:" class="labeltext mulitline">
                        <span>{{ formInfo.mailbox }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <textBorder>数据画像</textBorder>
                  <el-row :gutter="20" class="width100 mt10 pb5">
                    <el-col :span="12" class="">
                      <el-form-item label="性别:" class="labeltext">
                        <span>{{ gender[formInfo.sex] }}</span>
                      </el-form-item>
                      <el-form-item label="年龄:" class="labeltext">
                        <span>{{ formInfo.age }}</span>
                      </el-form-item>
                      <el-form-item label="民族:" class="labeltext">
                        <span>{{ formInfo.nationName }}</span>
                      </el-form-item>
                      <el-form-item label="职称:" class="labeltext">
                        <span>{{ formInfo.professionalTitles }}</span>
                      </el-form-item>
                      <el-form-item
                        label="工作地址:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.address }}</span>
                      </el-form-item>
                      <el-form-item
                        label="家庭地址:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.homeAddress }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" class="mb10">
                      <el-form-item
                        label="身份证号:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.cardNumber }}</span>
                      </el-form-item>
                      <el-form-item label="籍贯:" class="labeltext mulitline">
                        <span>{{ formInfo.nativePlace }}</span>
                      </el-form-item>
                      <el-form-item label="生日:" class="labeltext">
                        <span>{{ formInfo.birthday }}</span>
                      </el-form-item>
                      <el-form-item label="职称时间:" class="labeltext">
                        <span>{{ formInfo.professionalTime }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20" class="width100 mt10 mb50 bbline">
                    <el-col :span="24">
                      <el-form-item
                        label="爱好情况:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.hobby }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item
                        label="论文课题:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.thesisTopic }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item
                        label="研究方向:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.researchDirection }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-row>
                        <el-col :span="12">
                          <el-form-item
                            label="车辆信息:"
                            class="labeltext mulitline"
                          >
                            <span>{{ formInfo.carInfo }}</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item
                            label="车牌号:"
                            class="labeltext mulitline"
                          >
                            <span>{{ formInfo.carNumber }}</span>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item
                        label="婚恋情况:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.marriage }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item
                        label="在意的人:"
                        class="labeltext mulitline"
                      >
                        <p
                          class="carep"
                          v-for="(item, index) in inputTags1"
                          :key="index"
                        >
                          {{ item }}
                        </p>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24" class="mb10">
                      <el-form-item
                        label="子女情况:"
                        class="labeltext mulitline"
                      >
                        <span>{{ formInfo.children }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="历史用书情况" name="second">
                <div class="">
                  <el-table class="unittable mytable" :data="tableData">
                    <el-table-column
                      prop="materialName"
                      label="教材名称"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="platformName"
                      label="出版社"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column prop="author" label="主编" align="center">
                    </el-table-column>
                    <el-table-column prop="price" label="价格" align="center">
                    </el-table-column>
                    <el-table-column
                      prop="bookNumber"
                      label="用书量"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="useBookYear"
                      label="用书时间"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="bookSpecialtyName"
                      label="用书专业"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column prop="isJoin" label="类型" align="center">
                      <template slot-scope="scope">
                        <span>{{ typeObj[scope.row.isJoin] }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                  <page
                    :currentPage="pageBeanLi.pageNum"
                    :total="totalLi"
                    :pageSize="pageBeanLi.pageSize"
                    @updatePageNum="handleCurrentChange"
                  ></page>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="loading" v-loading="loading"></div>

    <div class="fixbottom">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChangeList"
      ></page>
    </div>
  </div>
</template>

<script>
import listitem from './listitem.vue'
import { visitList, visitDownloadExcel } from '@/api/visit/index'
import textBorder from '@/components/common/textBorder.vue'
import page from '@/components/common/page.vue'
import {
  customerInfo,
  workReportSetRead,
} from '@/api/clientmanagement/customer'
import { customerbook } from '@/api/clientmanagement/unit'
import { customerLevelColors } from '@/utils/dict'
import { downloadFileByUrl, getParStr } from '@/utils/tools'
import datepicker from '@/components/common/datepicker.vue'
export default {
  components: {
    listitem,
    textBorder,
    datepicker,
    page,
  },
  data() {
    return {
      customerLevelColors: customerLevelColors,
      ccol: 24,
      rcol: 0,
      loading: true,
      dataList: [],
      datarange: [],
      isDownload: false,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        assistType: '',
        customerName: '',
        unitName: '',
        createByName: '',
        createByDepartment: '',
        startTime: '',
        endTime: '',
        time: '',
        keyword: '',
      },
      total: 0,
      types: [
        {
          label: '电话拜访',
          value: '1',
        },
        {
          label: '线上拜访',
          value: '2',
        },
        {
          label: '实地拜访',
          value: '3',
        },
      ],
      onOff: false,
      loadBoo: false,
      divWidth: 0,
      activeName: 'first',
      formInfo: {},
      colors: [
        {
          color: '#FD7A41',
          background: '#FFECE3',
        },
        {
          color: '#4285F4',
          background: '#DFEAFD',
        },
        {
          color: '#FFA44C',
          background: '#FFF2E5',
        },
        {
          color: '#AC6FFB',
          background: '#F0E6FE',
        },
        {
          color: '#67C23A',
          background: '#E6F3D8',
        },
      ],
      tableData: [],
      totalLi: 0,
      inputTags1: [],
      gender: {
        1: '男',
        2: '女',
      },
      pageBeanLi: {
        pageNum: 1,
        pageSize: 6,
        customerId: '',
      },
      typeObj: {
        1: '过往合作',
        3: '历史发货',
        2: '非合作',
      },
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
    }
    this.getData()
  },
  mounted() {
    // document
    //   .getElementById('section')
    //   .addEventListener('scroll', this.isAddScrollListen)
  },
  beforeDestroy() {},
  methods: {
    // 监听滚动，出现滑块的情况
    boxScroll() {
      const items = this.$refs.commentRef || []
      Array.from(items).forEach((item, index) => {
        // 只考虑未读状态，把未读状态标记为已读状态，
        const itemData = this.dataList[index]
        if (itemData.isViewed == 1) {
          const rect = item.$el.getBoundingClientRect()
          // 计算某项在可视区域的top值 + 该项的高度 < 窗口可视区域的高度 - 56
          // 此处根据具体需要修改值
          if (rect.top + rect.height < window.innerHeight - 56) {
            this.dataList[index].isViewed = 2
            workReportSetRead({
              visitId: itemData.id,
            }).then((res) => {
              console.log(`第${index + 1}条已读`)
            })
          }
        }
      })
    },
    isAddScrollListen() {
      const parentHeight = document.getElementById('section').offsetHeight
      const sonHeight = this.$refs.sonRef.offsetHeight
      // 子级高度大于父级高度，说明有滚动条，添加滚动监听
      if (sonHeight > parentHeight) {
        // 添加滚动监听（滚动时触发的事件）
        document
          .getElementById('section')
          .addEventListener('scroll', this.boxScroll)
        // 默认把可视区域内的内容自动设置为已读状态
        this.$nextTick(() => {
          this.boxScroll()
        })
      } else {
        // 否则，直接设置为已读状态
        for (let i = 0; i < this.dataList.length; i++) {
          if (this.dataList[i].isViewed == 1) {
            this.dataList[i].isViewed = 2
            workReportSetRead({
              visitId: this.dataList[i].id,
            }).then((res) => {})
          }
        }
      }
    },
    handleCurrentChangeList(page) {
      document.getElementById('section').scrollTop = 0
      this.pageBean.pageNum = page
      this.getData()
    },
    customerbookApi() {
      customerbook(this.pageBeanLi).then((res) => {
        if (res.status == 0) {
          this.tableData = res.data
          this.totalLi = res.page.total
        }
      })
    },
    loadDataInfo(customerId) {
      customerInfo(customerId)
        .then((result) => {
          this.formInfo = result.data
          this.formInfo.tags =
            this.formInfo.label && this.formInfo.label.split(',')
          this.inputTags1 =
            result.data.importPeople && result.data.importPeople.split(',')
          this.formInfo.sex = this.formInfo.sex == 0 ? '' : this.formInfo.sex
          this.formInfo.age = this.formInfo.age == 0 ? '' : this.formInfo.age
        })
        .catch((err) => {})
    },
    handleCurrentChange(page) {
      this.pageBeanLi.pageNum = page
      this.customerbookApi()
    },
    handleClick(tab, event) {
      this.activeName = tab.name
    },
    setWidth(customerId) {
      this.ccol = 12
      this.rcol = 12
      this.$nextTick(() => {
        this.divWidth =
          document.getElementsByClassName('itemheader')[0].clientWidth + 'px'
      })
      this.loadDataInfo(customerId)
      this.pageBeanLi.customerId = customerId
      this.pageBeanLi.pageNum = 1
      this.customerbookApi()
    },
    closeR() {
      this.ccol = 24
      this.rcol = 0
      this.$nextTick(() => {
        this.divWidth = 0 + 'px'
      })
      this.$store.commit('cus/CHANGECOL', 6)
    },
    updateCommont(item) {},
    search() {
      this.$refs.commentRef.forEach((item) => {
        item.clearData()
      })
      this.pageBean.pageNum = 1
      this.getData()
    },
    getData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.loading = true
      visitList(this.pageBean).then((res) => {
        if (res.status == 0) {
          this.dataList = res.data
          this.total = res.page.total
          this.dataList.forEach((item) => {
            if (item.soundList.length > 0) {
              this.$set(item, 'isPlay', false)
            }
          })
          this.loading = false
          this.loadBoo = true
          // 列表有值时添加监听事件
          if (this.dataList.length > 0) {
            this.$nextTick(() => {
              this.isAddScrollListen()
            })
          }
        }
      })
    },
    changeDateRange(date) {
      if (date) {
        this.pageBean.startTime = date[0]
        this.pageBean.endTime = date[1]
      } else {
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
    },
    loadData() {
      this.getData()
    },
    audioPlay(item, boolean) {
      this.dataList.forEach((item1) => {
        if (item1.soundList.length > 0) {
          item1.isPlay = false
        }
      })
      item.isPlay = boolean
    },
    addVisit() {
      this.$router.push('/clientMaintenance/followVisit/add')
    },
    exportVisit() {
      var exportPar = Object.assign({}, this.pageBean)
      delete exportPar.pageNum
      delete exportPar.pageSize
      this.isDownload = true
      visitDownloadExcel(exportPar)
        .then((result) => {
          downloadFileByUrl(result.data.url, result.data.fileName)
          this.isDownload = false
        })
        .catch((err) => {
          this.isDownload = false
        })
    },
    submitAction(type, dateRange) {
      if (type == 11) {
        if (dateRange) {
          this.pageBean.startTime = dateRange[0]
          this.pageBean.endTime = dateRange[1]
        } else {
          this.pageBean.startTime = ''
          this.pageBean.endTime = ''
        }
        this.pageBean.time = ''
      } else {
        this.pageBean.time = type
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
    },
  },
}
</script>
<style>
.vili .el-tabs__header {
}
</style>
<style scoped>
.mb50 {
  margin-bottom: 50px;
}
.fixbottom {
  position: fixed;
  right: 25px;
  bottom: 0;
  left: 190px;
  z-index: 10;
  padding-bottom: 20px;
  background: #fff;
  box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.16);
}
.width120 {
  width: 120px;
}
.carep {
  line-height: 30px;
}
.carep:first-child {
  margin-top: 5px;
}
.labeltext {
  margin-bottom: 5px !important;
}
.mtop20 {
  margin-top: 20px;
}
.closeicon {
  position: absolute;
  right: 10px;
  cursor: pointer;
}
.lefttable {
  position: relative;
}
.tagitemcss {
  font-size: 12px;
  padding: 4px 8px;
  margin: 0px 4px;
  background-color: #dfeafd;
  border-radius: 2px;
  color: #4285f4;
}
.rdiv {
  position: fixed;
  top: 250px;
  right: 34px;
  bottom: 20px;
  overflow-y: auto;
}
.nolist {
  height: calc(100vh - 283px);
  text-align: center;
  overflow: hidden;
}
.noimg {
  margin-top: 180px;
}
.loading {
  width: 100px;
  height: 60px;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.f16 {
  font-size: 16px;
  color: #333333;
}

.pb15 {
  padding-bottom: 15px;
}
.mt3 {
  margin-top: 3px;
}
.ml {
  margin-left: -80px;
}
.mainbg {
  background-color: white;
  padding: 20px;
  position: relative;
  padding-bottom: 86px;
}

.right {
  text-align: right;
}
</style>
