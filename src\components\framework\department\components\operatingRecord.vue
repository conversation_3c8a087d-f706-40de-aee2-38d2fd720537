<template>
    <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="uName" label="操作人"></el-table-column>
        <el-table-column prop="createTime" label="操作时间"></el-table-column>
        <el-table-column prop="context" label="记录"></el-table-column>
    </el-table>
</template>
<script>
import { queryDepartmentLog } from '@/api/framework/user'
  
export default {
      components: { 
      },
      props: {
        departmentId: {
          type: [String, Number],
          default: ''
        }
      },
      data() {
          return {
            tableData: [],
            loading: false
          };
      },
      watch: {
        departmentId: {
          handler(val) {
            if (val) {
              this.getOperationRecords();
            }
          },
          immediate: true
        }
      },
      methods: {
        getOperationRecords() {
          if (!this.departmentId) return;
          queryDepartmentLog({ id: this.departmentId })
            .then(res => {
                this.tableData = res.data;
            })
        }
      }
  };
</script>
  
<style lang="scss" scoped>


</style> 