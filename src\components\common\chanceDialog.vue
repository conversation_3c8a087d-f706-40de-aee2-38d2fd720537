<template>
    <el-dialog class="unitdialog" title="选择机会" top="80px" :visible.sync="dialogTableVisible" width="70%" center>
        <el-form class="unitfcss" :inline="true">
            <el-form-item label="机会名称：">
                <el-input clearable="" class="definput" v-model="pageBean.opportunityName" placeholder="请输入机会名称"></el-input>
            </el-form-item>
            <el-form-item label="">
                <el-button class="defaultbtn ml20" icon="el-icon-search" type="primary" @click="handleClick">搜索</el-button>
            </el-form-item>
        </el-form>
        <el-table class="mytable" :data="dataList" :height="478" v-loading="isLoading">
            <el-table-column class-name="column_blue" property="opportunityName" label="机会名称" align="center"
                min-width="200px"></el-table-column>
            <el-table-column property="customerName" label="客户" align="center" min-width="200px"></el-table-column>
            <el-table-column property="unitName" label="客户单位" align="center" min-width="200px"></el-table-column>
            <el-table-column property="estimatedAmount" label="预算金额" align="center" width="150px"></el-table-column>
            <el-table-column property="stage" label="机会阶段" align="center" width="150px">
            </el-table-column>
            <el-table-column property="edit" label="操作" align="center" fixed="right">
                <template slot-scope="scope">
                    <div class="rbtn deffont" v-show="scope.row.isSelect">取消选择</div>
                    <div class="bbtn deffont" v-show="scope.row.isSelect == 0" @click="chooseAction(scope.row)">选择</div>
                </template>
            </el-table-column>
        </el-table>
        <div class="center">
            <el-button class="defaultbtn" type="primary" @click="submitAction">提交</el-button>
        </div>
        <page :currentPage="pageBean.pageNum" :pageSize="pageBean.pageSize" :total="total"
            @updatePageNum="handleCurrentChange"></page>
    </el-dialog>
</template>

<script>

import page from './page.vue';

import { chanceList } from '@/api/wapi'

export default {
    components: {
        page
    },
    data() {
        return {
            isLoading: false,
            dataList: [],
            pageBean: {
                opportunityName: "",
                pageNum: 1,
                pageSize: 8,
            },
            total: 0,
            tempData: {},
            chanceData: {}
        }
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
    },
    computed: {
        dialogTableVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('updateVisible', val);
            },
        }
    },
    methods: {
        selectChanceData(params) {
            if (params) {
                this.tempData = params
            } else {
                params = this.tempData
            }
            this.isLoading = true;
            let sData = { ...params, ...this.pageBean }
            chanceList(sData).then(res => {
                this.isLoading = false;
                if (res.status == 0) {
                    this.dataList = res.data
                    this.total = res.page.total
                    this.dataList.forEach(item => {
                        this.$set(item, 'isSelect', false)
                    })
                    if (this.chanceData.id) {
                        this.selected(this.dataList)
                    }
                } else {
                    this.msgError(res.msg)
                }
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        selected(dataList) {
            dataList.forEach(item => {
                if (item.id === this.chanceData.id) {
                    this.$set(item, 'isSelect', true)
                }
            })
        },
        handleClick() {
            this.pageBean.pageNum = 1;
            this.selectChanceData()
        },
        submitAction() {
            this.$emit('updateData', this.chanceData)
            this.dialogTableVisible = false;
            this.chanceData = {}
        },
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.selectChanceData()
        },
        chooseAction(val) {
            this.dataList.forEach(item => {
                item.isSelect = false
            })
            this.chanceData = val;
            val.isSelect = true
        }
    }
}
</script>
<style scoped>
.center {
    margin-top: 50px;
    text-align: center;
}

.bbtn {
    color: #4285F4;
    cursor: pointer;
}

.rbtn {
    color: #F45961;
    cursor: pointer;
}

.unitfcss {
    line-height: 34px;
}

.unitfcss /deep/.el-form-item__label {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
}

.unitfcss /deep/.el-form-item {
    margin-bottom: 20px;
}

.ml20 {
    margin-left: 20px;
}

.unitdialog /deep/.el-dialog__body {
    padding: 20px;
    padding-top: 0px;
}

.unitdialog /deep/.el-dialog__header {
    border: none;
}
</style>
<style scoped>
.tabscss /deep/.el-tabs__item {
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 40px;
    width: 240px !important;
    text-align: center;
    line-height: 40px;
}
</style>