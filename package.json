{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "axios": "^0.19.0", "browserslist": "^4.16.1", "caniuse-lite": "^1.0.30001173", "clipboard": "^2.0.6", "core-js": "^3.4.4", "dayjs": "^1.8.25", "dingtalk-jsapi": "^3.0.25", "echarts": "^5.4.2", "element-tree-grid": "^0.1.3", "element-ui": "^2.13.0", "file-saver": "^2.0.2", "font-awesome": "^4.7.0", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "md5": "^2.3.0", "normalize.css": "7.0.0", "path-to-regexp": "2.4.0", "screenfull": "^5.1.0", "vue": "^2.6.10", "vue-cropper": "^0.5.6", "vue-esign": "^1.1.4", "vue-image-crop-upload": "^2.5.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.3", "vue-tree-color": "^2.3.3", "vue-video-player": "^5.0.2", "vuex": "^3.1.2", "wangeditor": "^4.7.15", "xlsx": "^0.15.6"}, "devDependencies": {"@riophae/vue-treeselect": "^0.4.0", "@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.3", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "html-webpack-plugin": "3.2.0", "less": "^3.9.0", "less-loader": "^4.1.0", "mockjs": "1.0.1-beta3", "node-sass": "^4.14.1", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "^2.6.10", "vue-ueditor-wrap": "^2.4.1"}}