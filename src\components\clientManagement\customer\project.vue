<template>
  <div>
    <el-table class="detailList mytable"  :data="tableData" style="width: 100%" height="590px" v-loading="isLoading">
      <el-table-column
        prop="projectName"
        label="项目名称"
        align="center"
        class-name="column_blue"
        show-overflow-tooltip
        min-width="250px"
        >
      </el-table-column>
      <el-table-column
        prop="customerName"
        label="客户姓名"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="unitName"
        label="客户单位"
        show-overflow-tooltip
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="projectStatusName"
        align="center"
        label="项目状态"
        width="167px">
      </el-table-column>
      <el-table-column
        prop="beginTime"
        label="开始时间"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="endTime"
        label="结束时间"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="projectAmount"
        label="项目金额（万元）"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="chargePersonName"
        align="center"
        width="167px"
        label="项目负责人">
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <page 
    :currentPage="pageBean.pageNum" 
    :total="total" 
    :pageSize="pageBean.pageSize" 
    @updatePageNum="handleCurrentChange"
    ></page>
  </div>
</template>

<script>

import page from '../../common/page.vue';
import nolist from '../../common/nolist.vue';
import { projectList } from "@/api/project";

  export default {
    components:{
      page,
      nolist
    },
    data(){
      return{
        isLoading:false,
        tableData:[],
        status:{
          1:"未开始",
          2:"进行中",
          3:"已完成"
        },
        total:0,
        pageBean:{
          customerId:this.$route.query.id,
          pageNum:1,
          pageSize:10,
        }
      }
    },
    created(){
      console.log("sssss",this.$route.query.id);
    },
    methods:{
      loadData(){
        this.isLoading = true;
        projectList(this.pageBean).then((result) => {
          this.tableData = result.data;
          this.total = result.page.total;
          this.isLoading = false;
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      onDetail(data){
        console.log(data)
        this.$router.push({
          path:"/projectManagement/project/detail",
          query:{
            id:data.name
          }
        })
      },
      // 项目名称列
      // columnFormatter(row,column,cellValue,index){

      // },
      handleCurrentChange(page){
        this.pageBean.pageNum = page;
        this.loadData();
      }
    }
  }
</script>

<style lang="scss" scoped>
.tabbtn{
  font-size: 14px;
}
.bbtn,.bbtn:hover,.bbtn:focus{
  color: #4285F4;
}

</style>