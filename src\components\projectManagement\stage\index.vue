<template>
  <div class="con">
      <back>阶段流程管理</back>
      <ul class="stagediv">  
        <li class="licss" v-for="(item,index) in stageArr" :key='index'>
          <div v-for="(citem,cindex) in item" :key="cindex" class="yjie">
              <div @click="handleStage(citem)" class="box1 " :class="{activecss:citem.id === itemId}">
                {{ citem.name }}
              </div>
              <template v-if="citem.id === itemId">
                <div class="clearfix" >
                    <div   class="button1 bc left cp " @click="stageInfo(citem)">
                      <img src="../../../assets/de.png" alt="">
                      流程详情
                    </div>
                    <div :class="{noallow:!citem.isOperate}" class="button2 bc left cp" @click="editStage(citem)">
                      <img src="../../../assets/edit.png" alt="">
                      编辑
                    </div>
                    <div :class="{noallow:!citem.isOperate}" class="button3 bc left cp" @click="addStage(item,1,citem)">
                      <i class="el-icon-plus addi"></i>
                      添加同级别流程
                    </div>
                  </div>
                  <div :class="{noallow:!citem.isOperate}" class="button4 bc cp" @click="addTask(citem)">
                    <i class="el-icon-plus addi"></i>
                      添加任务
                  </div>
              </template>
              
              <div :class="comCss[ccitem.taskStatus]" class="taskitem" v-for="(ccitem,ccindex) in citem.taskVoList" :key="ccindex">
                  <span class="cp" @click="taskInfo(ccitem)">
                  <p class="tasktitle">{{ ccitem.taskName }}</p>
                  <p class="taskkey">任务内容：</p>
                  <p class="taskkey">{{ ccitem.taskContent }}</p>
                  <p class="taskkey">任务状态：
                    <!-- <span class="nostart">未开始</span> -->
                    <!-- <span class="doing">进行中</span> -->
                    <!-- <span class="Completed">已完成</span> -->
                    <!-- <span class="ending">已结束</span> -->
                    <span :class="numToClass[ccitem.taskStatus]">{{ ccitem.taskStatusName }}</span>
                  </p>
                  <p class="taskkey">结束日期：<span>{{ ccitem.endTime }}</span></p>
                  <p class="taskkey">任务负责人：<span>{{ ccitem.chargePersonName }}</span></p>
                </span>
                  <p v-if="auth(ccitem) && (ccitem.taskStatus==1||ccitem.taskStatus==2)" :class="num2css[ccitem.taskStatus]" class="taskf" @click="completeTask(ccitem)">{{numToText[ccitem.taskStatus].activeText}}</p>
                  <p v-else :class="greyToCss[ccitem.taskStatus]" class="taskf "  >{{numToText[ccitem.taskStatus].text}}</p>
              </div>
        </div>
        </li>
        <img @click="addStage" class="add" src="../../../assets/add.png" alt="">

      </ul>
      <drawer @loadData="loadData" ref="stageRef"  :xOrder="xOrder" :yOrder="yOrder" @changeDrawer="changeDrawer" :drawer="drawer"   @saveData="saveData"></drawer>
      <drawerstage :stageId="stageId" @loadData="loadData"  ref="stageDetail" @changeDrawer="changeDrawerStage" :drawer="drawerstage" ></drawerstage>


      <drawerpay @loadData="loadData" ref="payRef"  @changeDrawer="changeDrawerPay" :drawer="drawerpay" ></drawerpay>

      <drawertask @loadData="loadData" ref="taskRef" @changeDrawer="changeDrawerTask" :drawer="drawertaskB" ></drawertask>
      <dc-dialog iType="2" title="温馨提示" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
        <p class="pcc">        {{ typeToText[textKey] }}</p>
      </dc-dialog>
  </div>
</template>

<script>
import {projectstage,IsAddStage} from '@/api/wapi.js'
import drawer from './drawer.vue'
import drawerstage from './drawerstage.vue'
import drawerpay from './drawerpay'
import drawertask from './components/drawertask'
import {taskUpdate} from '@/api/stage/index'
import back from '../../common/back.vue';
  export default {
      data(){
        return {
          itemId:1,
          stageArr:[],
          drawer: false,
          drawerstage:false,
          drawerpay:false,
          drawertaskB:false,
          dialogVisible:false,
          typeToText:{
            1:'是否确认任务？确认后任务将变更为进行中状态，系统将同步进行时间记录',
            2:'是否确认完成任务？确认后任务将变更为已完成状态',
          },
          textKey:'0',
          xOrder:1,
          yOrder:1,
          stageId:'',
          numToClass:{
            1:'nostart',
            2:'doing',
            3:'Completed',
            4:'ending',
            5:'termination'
          },
          num2css:{
            1:'blue',
            2:'blue',
            3:"Completed",
            4:'grey',
            5:"grey"
          },
          greyToCss:{
            1:'grey',
            2:'grey',
            3:"Completed",
            4:'grey',
            5:"grey"
          },
          comCss:{
            1:'',
            2:'',
            3:'fa11',
            4:'fa11',
            5:'fa11'
          },
          numToText:{
              1:{
                activeText:"任务确认",
                text:"任务待确认"
              },
              2:{
                activeText:"任务完成",
                text:"任务进行中"
              },
              3:{
                activeText:"任务已完成",
                text:"任务已完成"
              },
              4:{
                activeText:"任务已结束",
                text:"任务已结束"
              },
              5:{
                activeText:"任务已暂停",
                text:"任务已暂停"
              },

          },
          updateTaskId:'',
          updataTaskStatus:'',
        }
      },
      components:{
        drawer,
        drawerstage,
        drawerpay,
        drawertask,
        back,
      },
      computed: {
        auth: () => {
            return (ccitem) => {
                let authArr = [ccitem.chargePerson]
                let newArr = ccitem.collaborator.split(',')
                let lastArr = [...authArr,...newArr]
                let uId = sessionStorage.getItem('userid')
                return lastArr.includes(uId)
            };
        },
      },
      methods:{
        loadData(){
          this.getStageData()
        },
        submitDialog(){
          let params ={
            taskStatus:(+this.updataTaskStatus)+1,
            id:this.updateTaskId
          }
          taskUpdate(params).then(res=>{
              if(res.status == 0){
                this.msgSuccess('操作成功')
                this.dialogVisible = false
                this.getStageData()
              }
          })
        },
        completeTask(ccitem){
            if(ccitem.taskStatus ==1){
                this.textKey = '1'
            }
            if(ccitem.taskStatus ==2){
                this.textKey = '2'
            }
            this.updateTaskId = ccitem.id
            this.updataTaskStatus = ccitem.taskStatus
            this.dialogVisible = true
        },
        addTask(item){
              if(!item.isOperate){
                this.msgError('没有操作权限')
                return 
              }
            this.$router.push({
              path:'/projectManagement/stage/taskadd',
              query:{
                id:item.id
              }
            })
        },
        saveData(){

        },
        taskInfo(ccitem){
            let taskId = ccitem.id
            this.drawertaskB = true
            this.$nextTick(()=>{
              this.$refs.taskRef.getTaskInfo(taskId)
            })
        },
        handleStage(item){
            if(item.id === this.itemId){
                this.itemId = -1
            }else{
              this.itemId = item.id
            }
        },
        stageInfo(citem){
        
          this.stageId = citem.id
          if(citem.stageType === 1){
            this.drawerstage = true
            this.$nextTick(()=>{
              this.$refs.stageDetail.getStageInfo(this.stageId)
            })
          }else{
             this.drawerpay = true
             this.$nextTick(()=>{
                this.$refs.payRef.getStageInfo(this.stageId)
              })
          }
        },
        changeDrawer(v) {
                this.drawer = v
        },
        changeDrawerTask(v){
          this.drawertaskB = v
        },
        changeDrawerStage(v){
          this.drawerstage = v
        },
        changeDrawerPay(v){
          this.drawerpay = v
        },
        async getIsAddStage(){
          let res = IsAddStage({projectId:this.$route.query.id})
          return res
        },
        async addStage(item,type,citem){
          if(type){
              if(!citem.isOperate){
                this.msgError('没有操作权限')
                return 
              }
          }else{
            let res = await IsAddStage({projectId:this.$route.query.id})
            if(res.status == 0){
                if(!res.data){
                   this.msgError('没有操作权限')
                   return 
                }
            }
          }
          this.stageId =''
          this.drawer = true
          if(type){
              this.xOrder = item[item.length-1].xOrder
              this.yOrder = item[item.length-1].yOrder + 1
          }else{
            if(this.stageArr.length>0){
              this.xOrder = this.stageArr[this.stageArr.length-1][0].xOrder + 1
              this.yOrder = this.stageArr[this.stageArr.length-1][0].yOrder
            }
          }
        },
        editStage(citem){
              if(!citem.isOperate){
                this.msgError('没有操作权限')
                return 
              }
              this.drawer = true
              this.xOrder = citem.xOrder
              this.yOrder = citem.yOrder 
              this.stageId = citem.id
              this.$refs.stageRef.getStageInfo(this.stageId)
        },
        getStageData(){
          projectstage({projectId:this.$route.query.id}).then(res=>{
            if(res.status == 0){
                let oldData= res.data
                var index = oldData.length;
                while(index--){
                  let filterData = oldData[index]
                  let queryList = filterData.filter((item) => {
                    return item !== null
                  })
                  oldData[index] = queryList
                  if(queryList.length==0){
                    oldData.splice(index,1)
                  }
                }
                this.stageArr = oldData
            }
          })
        }
      },
      created(){
        this.getStageData()
      }
  }
</script>

<style lang="scss" scoped>
.noallow{
  cursor: not-allowed !important;
  background-color: #f4f4f5 !important;
}


.doing{
    margin-top: -3px;
}
.cp{
  cursor: pointer;
}
.yjie{
  margin-bottom: 30px;
}
.yjie:last-child{
  margin-bottom: 0;
}

.taskkey{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #444444;
  line-height: 20px;
  margin: 10px 0;
  word-wrap:break-word;
}
.grey{
  color: #999999;
}
.blue{
  color: #4285F4;
  cursor: pointer;
}
.taskitem{
  padding: 0 12px;
  box-sizing: border-box;
  background: #F4F8FF;
  border-radius: 8px 8px 8px 8px;
  margin-top: 16px;
}
.fa11{
  background: #FAFAFA;
}
.tasktitle{
  min-height: 42px;
  padding:10px 0px ;
  line-height: 22px;
  border-bottom: 1px solid #B5C6DB;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  word-wrap:break-word;
}
.taskf{
  height: 42px;
  line-height: 42px;
  border-top: 1px solid #B5C6DB;
  text-align: center;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
}
.ending,.termination{
  color: #F45961;
}
.Completed{
  color: #56C36E;
}
.nostart{
  color: #FF8D1A;
}
.doing{
  color: #4285F4;
}
.addi{
  font-size: 16px;
}
.grey{
  color: #999999;
}
.bc{
  height: 40px;
  background: #F4F8FF;
  border-radius: 4px 4px 4px 4px;
  line-height: 40px;
  text-align: center;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
}
.button1{
  width: 88px;
  margin-right: 8px;
}
.button2{
  width: 62px;
  margin-right: 8px;
}
.button3{
  width: 127px;
}
.button4{
  margin-top: 8px;
}
.add{
  width: 40px;
  height: 40px;
  margin-left: 34px;
  cursor: pointer;
}
.box1 {
    height: 40px;
    background: #F4F8FF;
    position: relative;
    width: 280px;
    margin-bottom: 8px;
    text-align: center;
    line-height: 40px;
    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    cursor: pointer;
  }
  .box1:before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    left: 100%;
    top: 0;
    border: solid transparent;
    border-width: 20px;
    border-left-color: #F4F8FF;
  }
  .activecss{
    background: #4285F4;
    color: #fff;
  }
  .activecss::before
  {
    border-left-color: #4285F4;
  }
.stagediv{
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  height: calc(100vh - 132px);
  overflow-y: auto;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  align-items: flex-start;
  margin-top: 16px;
}
.licss{
  width: 293px;
  flex-shrink: 0;
  margin-right: 30px;
}

</style>