/* 全局样式表 */

html,
body,
#app {
    height: 100%;
    margin: 0;
    padding: 0;
    min-width: 1366px;
    min-width: 1286px;
}

.el-breadcrumb {
    margin-bottom: 15px;
    font-size: 12px;
}

.el-card {
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.15) !important;
}

.el-table {
    margin-top: 15px;
    font-size: 14px;
    color: #333333;
    margin-bottom: 15px;
}

.el-cascader-panel {
    height: 200px;
}

.el-steps {
    margin: 25px 0;
}

.ql-editor {
    min-height: 200px !important;
}

/*公共样式--开始*/

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    min-height: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
}

ul,
li {
    list-style: none;
}

a {
    text-decoration: none;
    color: #232323;
}

body {
    font-family: "Microsoft YaHei";
    font-size: 14px;
    color: #333;
}

img {
    border: none;
    vertical-align: middle;
}



table {
    border-collapse: collapse;
    table-layout: fixed;
}

input,
textarea {
    outline: none;
    border: none;
}

textarea {
    resize: none;
    overflow: auto;
}

.clearfix {
    zoom: 1;
}

.clearfix:after {
    content: ".";
    width: 0;
    height: 0;
    visibility: hidden;
    display: block;
    clear: both;
    overflow: hidden;
}

.fl {
    float: left
}

.fr {
    float: right
}

.tl {
    text-align: left;
}

.tc {
    text-align: center
}

.tr {
    text-align: right;
}

.ellipse {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.inline {
    display: inline-block;
    *display: inline;
    *zoom: 1;
}
