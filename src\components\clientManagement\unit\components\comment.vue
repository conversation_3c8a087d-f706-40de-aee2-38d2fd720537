<template>
  <div class="commentdiv" :class="{secondcss:!item.reply_comment}">
      <ul>
        <li class="commentli">
          <div>
                <img class="head left" src="@/assets/touxiang.png" alt="">
                <p class="">
                  <span class="pname">{{ item.name }}</span>
                  <span class="spantext">2023-02-02 11:21</span>
                  <span class="reply" v-if="item.reply_comment">回复</span>
                </p>
                <p class="ptext">1丰访商学院学前腾主任，今天不在学校，把文件放到了办公室，没办法安排老师去参会</p>
            </div>
            <template v-if="item.reply_comment" >
              <commentitem :item="item" v-for="(item,index) in item.children" :key="index"></commentitem>
            </template>
        </li>
      </ul>
  </div>
</template>

<script>
  export default {
    name:'commentitem',
    props:{
      item:{
        type:Object,
        default:()=>{}
      }
    },
    created(){
        console.log(this.item)
    },
    data(){
      return {
       
      }
    }
    
  }
</script>

<style lang="scss" scoped>
.pname{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-right: 12px;
}
.secondcss{
  margin-left: 47px !important;
  margin-top: 20px !important;
}
.commentli{
  margin-bottom: 20px;
}
.ptext{
  margin-top: 8px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-left: 48px;
}
.reply{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  margin-left: 16px;
}
.spantext{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.head{
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 8px;
}
.commentdiv{
  margin-left: 14px;
  margin-top: 10px;
}

</style>