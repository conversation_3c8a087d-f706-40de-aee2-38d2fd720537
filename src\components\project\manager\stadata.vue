<template>
  <div class="maskcss">
    <el-row class="c1"   type="flex"  justify="space-between">
      <el-col :span="8" class= "pd br">
        <textBorder class="ttitle">任务概况</textBorder>
        <el-row class="rowheight" type="flex"  justify="space-between">
          <el-col :span="9" class= "br">
            <ditem label="总任务" :value="`${taskStatusData.totalNum}`" :valueColor="'#4285F4'"></ditem>
          </el-col>
          <el-col :span="9" class="br pr">
            <ditem label="项目进度" :value="taskStatusData.progress" :valueColor="'#4285F4'">
            </ditem>
            <el-progress class="presscss" :show-text="false" :percentage="getProgress(progressPercentage)"></el-progress>
          </el-col>
          <el-col :span="6" class="br">
            <ditem label="已完成" :value="`${taskStatusData.completedNum}`" :valueColor="'#55C36E'"></ditem>
          </el-col>
        </el-row>
        <el-row class="rowheight2" type="flex"  justify="space-between">
          <el-col :span="6" class="br">
            <ditem label="未开始" :value="`${taskStatusData.notStartedNum}`" :ptop="10" :valueColor="'#FF8D1A'"></ditem>
          </el-col>
          <el-col :span="6" class="br">
            <ditem label="进行中" :value="`${taskStatusData.inprogressNum}`" :ptop="10"  :valueColor="'#4285F4'"></ditem>
          </el-col>
          <el-col :span="6" class="br">
            <ditem label="暂停" :value="`${taskStatusData.pauseNum}`" :ptop="10"  :valueColor="'#A25EFA'" ></ditem>
          </el-col>
          <el-col :span="6" class="br">
            <ditem label="停止" :value="`${taskStatusData.stopNum}`" :ptop="10"  :valueColor="'#F45961'"></ditem>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8" class="pd br pr">
        <textBorder class="ttitle">相关人员</textBorder>
        <!-- v-if="projectMemberData.totalNum >8" -->
        <span class="mbtn" @click="showMember">更多<i class="el-icon-arrow-right"/></span>
        <div class="countcss">项目成员：{{projectMemberData.totalNum}}人</div>
        <div class="clist">
          <div class="citem" v-for="(item,index) in projectMemberData.projectMemberVoList" :key="index">
            <i class="el-icon-user-solid crc"></i>
            {{item.name}}
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <el-row class="rh"  type="flex"  justify="space-between">
          <el-col :span="12">
            <ditem :ptop="35" :label="'项目工时(小时)'"  :value="`${projectEfficiency.workHours}`" :valueColor="'#4285F4'"  @click.native="showOutputDialog"  class="clickable-ditem"></ditem>
          </el-col>
          <el-col :span="12">
            <ditem :ptop="35" :label="'效率比'" :value="`${projectEfficiency.efficiency}`" :valueColor="'#55C36E'"></ditem>
          </el-col>
        </el-row>
        <el-row class="rh mt12"  type="flex"  justify="space-between">
          <el-col :span="12">
            <ditem :ptop="35" :label="'项目消耗 (万元)'" :value="`${projectEfficiency.costAmount}`" :valueColor="'#FF8D1A'"></ditem>
          </el-col>
          <el-col :span="12">
            <ditem :ptop="35" :label="'逾期率 (%)'" :value="`${projectEfficiency.overdueRate}`" :valueColor="'#E85D5D'"></ditem>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row class="mt12"   type="flex"  justify="space-between">
      <el-col class="pd br" :span="24">
        <div class="sdiv">
          <textBorder class="ttitle">工时和产出</textBorder>
          <el-button type="text" class="bbtn" @click="showOutputDialog">查看更多
            <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
        <el-table
          class="mtable mt12"
          size="small"
          :data="hoursData"
          height="600px"
          default-expand-all
          :tree-props="{children: 'childrenList'}"
          border>
          <el-table-column
            prop="taskName"
            label="任务名称"
            width="180px"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            prop="workTime"
            label="日期"
            width="120">
          </el-table-column>
          <el-table-column
            prop="content"
            label="内容"
            min-width="200px"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            prop="workHours"
            label="消耗工时"
            width="100">
          </el-table-column>
          <el-table-column
            prop="workType"
            label="产出类型"
            width="100">
            <template slot-scope="scope">
              {{workTypeOptionMap[scope.row.workType]}}
            </template>
          </el-table-column>
          <el-table-column
            prop="duration"
            label="产出时长"
            width="100">
          </el-table-column>
          <el-table-column
            prop="createByName"
            label="创建人"
            width="100">
          </el-table-column>
          <el-table-column
            prop="departmentName"
            label="部门"
            width="150">
          </el-table-column>
        </el-table>
        <div class="tjcss" v-if="hoursData.totalWorkHours">
          总计：<span class="tlcss">{{hoursData.totalWorkHours}}</span>
        </div>
      </el-col>

    </el-row>
    <el-row class="mt12"   type="flex"  justify="space-between">
      <el-col class="pd br " :span="12">
        <textBorder class="ttitle">成本</textBorder>
        <el-table
          class="mtable mt12"
          size="small"
          :data="costData.list"
          height="300px"
          row-key="taskId"
          border
          default-expand-all
          :tree-props="{children: 'childrenList'}">
          <el-table-column
            prop="taskName"
            label="任务名称"
            show-overflow-tooltip>
          </el-table-column>
          <el-table-column
            prop="costAmount"
            label="成本金额"
            width="150">
          </el-table-column>
        </el-table>
        <div class="tjcss" v-if="costData.totalCostAmount">
          总计：<span class="tlcss">{{costData.totalCostAmount}}</span> 
        </div>
      </el-col>
      <el-col class="pd br ovdiv" :span="12">
        <textBorder class="ttitle">项目时间</textBorder>

        <div class='fu'>
          <el-date-picker v-if="value1.length>0" @change="chooseDate" @blur="chooseDate" :append-to-body="false" popper-class='topPicker'
            class='hidepicker' ref='datePick' :picker-options="pickerOptions" v-model="value1" type="daterange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </div>
      </el-col>
    </el-row>
    <moreoutputdialog ref="moreoutputdialog"  :projectId="$route.query.id" ></moreoutputdialog>
    <memberdialog ref="memberdialog" :dialogVisible="dialogVisible" :projectId="$route.query.id" @handleClose="handleClose"></memberdialog>
  </div>
</template>

<script>
import textBorder from '@/components/common/textBorder.vue';
import ditem from '../common/ditem.vue';
import memberdialog from '../common/memberdialog.vue';
import moreoutputdialog from '../common/moreoutputdialog.vue';

import { queryTaskStatusCount,queryProjectMembers,queryProjectEfficiency,queryProjectCostById,queryOutputByProjectId,queryProjectWorkHoursList } from '@/api/project/index'
import { workTypeOptionMap } from "@/utils/dict";
export default {
  components:{
    textBorder,
    memberdialog,
    ditem,
    moreoutputdialog
  },
  data(){
    return{
      // value1: ['2024-04-20', '2024-04-30'],
      workTypeOptionMap,
      value1: [],
      costData: [],
      hoursData:[],
        taskStatusData:{
          totalNum:'0',
          notStartedNum:'0',
          inprogressNum:'0',
          completedNum:'0',
          pauseNum:'0',
          stopNum:'',
          progress:'0%',
        },
        progressPercentage:0,
        projectMemberData:{
          totalNum:0,
          projectMemberVoList:[]
        },
        dialogVisible:false,
        outputDialogVisible: false,
        proMemberList:[],
        projectEfficiency:{
          workHours:'0',
          efficiency:'0',
          costAmount:'0',
          overdueRate:'0',
          actualBeginTime:'',
          actualEndTime:'',
          beginTime:'',
          endTime:'',
        },
        // stopTime: [
				// 	"2023-07-29",
				// 	"2023-07-30",
				// 	"2023-07-31",
				// 	"2023-08-01",
				// 	"2023-08-02",
				// 	"2023-08-03",
				// 	"2023-08-04",
				// 	"2023-08-10",
				// 	"2023-08-11",
				// 	"2023-08-12",
				// ],
        stopTime: [],
				pickerOptions: this.customDateStyle(),
        
    }
  },
  mounted() {
		},
  methods:{
    chooseDate() {
				this.$refs.datePick.focus(); // 选择日期后依旧触发焦点
			},
			customDateStyle() {
				let self = this;
				return {
					cellClassName(time) {
						let day = time.getDate();
						if (day < 10) {
							day = "0" + day;
						};
						let month = time.getMonth() + 1
						if (month < 10) {
							month = "0" + month;
						};
						let year = time.getFullYear();
						let res = year + "-" + month + "-" + day;
						for (let i = 0; i < self.stopTime.length; i++) {
							let _time = self.stopTime[i];
							if (res == _time) {
								if (i == 0) {
									return 'stopColor_start_date stopColor_in_range  half-bg';
								} else if (i == self.stopTime.length - 1) {
									return 'stopColor_end_date stopColor_in_range  half-bg';
								} else {
									return 'stopColor_in_range';
								}
							}
						}
					}
				}
			},
    getProgress(str){
      if(str == 0){
        return 0
      }
      var data = str.substring(0, str.length-1)
      return parseInt(data)
    },
    loadData(){
      this.loadStatusCount()
      this.loadProjectMembers()
      this.loadProjectEfficiency()
      this.loadHoursData();
      this.loadCostData();
    },
    loadStatusCount(){
      queryTaskStatusCount(this.$route.query.id).then((result) => {
        let resData =  result.data;
        for (let key in resData) {
          if (resData.hasOwnProperty(key)) {
            if(key!='progress'){
              resData[key] = resData[key] ==0? '0' : resData[key] +''
            }
          }
        }
        this.taskStatusData = resData
        console.log(this.taskStatusData.progress,'----')
        this.taskStatusData.progress = this.taskStatusData.progress == '' ? '0%' : this.taskStatusData.progress
        this.progressPercentage = this.taskStatusData.progress
      }).catch((err) => {
        
      });
    },
    loadProjectMembers(){
      queryProjectMembers({
        id:this.$route.query.id,
        isAll:0
      }).then((result) => {
        this.projectMemberData = result.data;
      }).catch((err) => {
        
      });
    },
     generateDateRange(startDate, endDate, granularity) {
				const dateRange = [];
				let currentDate = new Date(startDate);
				while (currentDate <= new Date(endDate)) {
					dateRange.push(currentDate.toISOString().split('T')[0]);
					currentDate.setDate(currentDate.getDate() + 1);
				}
				return dateRange;
			},
     getNowFormatDate() {
				let date = new Date(),
					year = date.getFullYear(), //获取完整的年份(4位)
					month = date.getMonth() + 1, //获取当前月份(0-11,0代表1月)
					strDate = date.getDate() // 获取当前日(1-31)
				if (month < 10) month = `0${month}` // 如果月份是个位数，在前面补0
				if (strDate < 10) strDate = `0${strDate}` // 如果日是个位数，在前面补0

				return `${year}-${month}-${strDate}`
			},
    loadProjectEfficiency(){
      queryProjectEfficiency(this.$route.query.id).then((result) => {
        this.projectEfficiency = result.data;
        this.value1[0] = this.projectEfficiency.beginTime
        this.value1[1] = this.projectEfficiency.endTime
        if(this.projectEfficiency.actualBeginTime && this.projectEfficiency.actualEndTime){
          let startDate = this.projectEfficiency.actualBeginTime
          let endDate = this.projectEfficiency.actualEndTime
          let dailyRange = this.generateDateRange(startDate, endDate);
          this.stopTime = dailyRange
          this.pickerOptions = this.customDateStyle()
        }else if(this.projectEfficiency.actualBeginTime && !this.projectEfficiency.actualEndTime){
          let startDate = this.projectEfficiency.actualBeginTime
          let endDate = this.getNowFormatDate()
          // let endDate = '2024-04-29'
          this.projectEfficiency.actualEndTime = endDate
          let dailyRange = this.generateDateRange(startDate, endDate);
          this.stopTime = dailyRange
          this.pickerOptions = this.customDateStyle()
        }
        this.$nextTick(()=>{
          this.$refs.datePick.focus(); // 让日历组件默认触发焦点事件
        })
        setTimeout(()=>{
          if(this.projectEfficiency.actualBeginTime<this.projectEfficiency.beginTime && this.projectEfficiency.beginTime<this.projectEfficiency.actualEndTime){
            let item_list = document.querySelectorAll('.start-date');
            item_list[0].className += " canclecss";
          }
        },100)
      }).catch((err) => {
        
      });
    },
    loadHoursData(){
      var par = {
        pageNum:1,
        pageSize:20,
        projectId:this.$route.query.id
      }
      queryProjectWorkHoursList(par).then((result) => {
        this.hoursData = result.data;
      }).catch((err) => {
        
      });
    },
    // 成本
    loadCostData(){
      var par = {
        type:2
      }
      queryProjectCostById(this.$route.query.id,par).then((result) => {
        this.costData = result.data;
      }).catch((err) => {
        
      });
    },

    showMember(){
      this.dialogVisible = true;
      this.$refs.memberdialog.loadProjectMembers();
    },
    handleClose(){
      this.dialogVisible = false;
    },
    showOutputDialog() {
      this.$refs.moreoutputdialog.show();
    },
    handleOutputDialogClose() {
      this.outputDialogVisible = false;
    },
  }
}
</script>
<style>
  .el-date-table td.canclecss div {
      margin-left: 0;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
  }
	.topPicker {
		top: -1px !important;
		left: -1px !important;
		margin-top: 0 !important;
		margin-left: 0 !important;
    width: 577px;
	}
  .fu {
    margin-top: 20px;
  }
  .stopColor_start_date div span {
		color: #fff;
		background-color: #30B64F;
		z-index: 10;
	}

	.stopColor_end_date div span {
		color: #fff;
		background-color: #30B64F;
		z-index: 10;
	}

	.stopColor_in_range div {
		/* background-color: #E3FCE9 !important; */


    background-color: #CDF6D7 !important;
	}
  .fu {
		position: relative;
		width: 648px;
    width: 100%;
		height: 331px;
	}

	.fu .el-date-table td div {
		height: 30px;
		padding: 0 !important;
		box-sizing: border-box;
	}

	.fu .el-date-table td span {
		width: 30px;
		height: 30px;
		display: block;
		margin: 0 auto;
		line-height: 30px;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		border-radius: 50%;
	}


	.fu .el-date-table {
		pointer-events: none;
	}
  .fu .el-date-table td.stopColor_start_date div {
		/* margin-left: 5px; */
		border-top-left-radius: 15px;
		border-bottom-left-radius: 15px;
	}

	.fu .el-date-table td.in-range div {
		background-color: #DFEAFD;
	}

	.fu .popper__arrow {
		display: none !important;
	}
</style>

<style scoped lang="scss">
  .clickable-ditem{
    cursor: pointer;
  }
  .sdiv{
    display: flex;
    justify-content: space-between;
  }
  .ovdiv{
    overflow-x: auto;
  }
.overdue{
  color:'#F45961'
}
.bbtn{
  color:'#55C36E'
}
.presscss{
  position: absolute;
  bottom: 8px;
  left: 10px ;
  right: 10px ;
  z-index: 1;
}
.pr{
  position: relative;
}
.textcss{
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 14px;
  color: #000000;
}
.msgcss{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tlcss{
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
}
.tjcss{
  height: 42px;
  line-height: 42px;
  border-radius: 0px 0px 8px 8px;
  border: 1px solid #E6E6E6;
  border-top: none;
  text-align: center;

  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #666666;

}
.rh{
  height: calc(50% - 6px);
}
.rh .el-col{
  border: 1px solid #e6E6E6;
  border-radius:8px;
}
.mtable{
  border-radius: 8px 8px 0px 0px;
  margin-bottom: 0px !important;
}
.br8{
  border-radius: 8px;
}
.mt12{
  margin-top: 12px !important;
}

.crc{
  color: #CCCCCC;
}
.clist{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.citem{
  width:50%;
  margin-bottom: 20px;
}
.ttitle::before{
  width: 3px !important;
  height: 16px !important;
}
.ttitle{
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
}
.countcss{
  margin: 20px 0;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 14px;
  color: #000000;
}
.pr{
  position: relative;
}
.mbtn{
  cursor: pointer;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  position: absolute;
  right: 14px;
  top: 16px;
}
.rowheight{
  margin-top: 12px;
  height: 104px;
}
.rowheight2{
  margin-top: 12px;
  height: 88px;
  margin-bottom: 0px !important;
}
.el-col + .el-col{
  margin-left: 12px;
}
.pd{
  padding: 16px;
}
.br{
  border: 1px solid #E6E6E6;
  border-radius: 8px;
}
.maskcss{
  padding:20px;
  min-height: calc(100vh - 200px);
  background-color: white;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}




	


</style>