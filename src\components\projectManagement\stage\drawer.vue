<template>
  <div class="drawer">
    <el-drawer class="ed" center size="50%" title="新建流程阶段" :visible.sync="drawer_" :direction="direction" @close="handleClose">
      <el-form :rules="rules" class="infoform" ref="form" :model="form" label-width="140px">
          <textBorder>基本信息</textBorder>
          <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
            <el-col :span="12">
              <el-form-item prop="name" label="阶段名称：" class="labeltext">
                <el-input clearable="" class="definput" placeholder="请输入阶段名称" v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">  
              <el-form-item prop="status" label="当前状态：" class="labeltext">
                <el-select class="definput" v-model="form.status" placeholder="请选择状态">
                  <el-option v-for="item in statusType" :key="item.value" :label="item.name"
                                :value="item.value">
                            </el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
          </el-row>
            <textBorder>负责与协作</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                        <el-form-item label="负责人：" prop="chargePerson" class="labeltext">
                            <span  v-if="form.chargePerson" class="mr10">{{form.chargePersonName}}</span>
                             <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择负责人',1)">点击选择负责人</el-button>
                        </el-form-item>
                </el-col>
                <el-col :span="12">  
                  <el-form-item label="协作人：" prop="collaborator" class="labeltext">
                            <el-tag class="tagcss" size="small" v-for="item in xiezuolist" closable @close="handleTagClose(item)" :key="item.id">{{item.name}}</el-tag>
                            <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择协作人',5)">点击选择协作人</el-button>
                        </el-form-item>
                </el-col>
            </el-row>

            <textBorder>特殊</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                  <el-form-item prop="stageType" label="是否为回款阶段：" class="labeltext">
                    <el-select clearable="" class="definput" v-model="form.stageType" placeholder="请选择阶段">
                      <el-option v-for="item in stageTypeArr" :key="item.value" :label="item.name"
                                :value="item.value">
                            </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                
            </el-row>
                    <el-button class="w98 btn_h42 defaultbtn bcenter" type="primary" @click="save">保存</el-button>
    </el-form>
    </el-drawer>
       <systemDialog 
       ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData">
    </systemDialog>
  </div>
</template>

<script>
  import systemDialog from '@/components/common/systemDialog.vue';
  import textBorder from '@/components/common/textBorder.vue'
  import {stageSave,stageInfo,stageUpdate} from '@/api/stage/index'
export default {
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    direction: {
      type: String,
      default: 'rtl',
    },
    xOrder:{
      type:Number,
      default:0,
    },
    yOrder:{
      type:Number,
      default:0,
    },
  },
  components:{
    textBorder,
    systemDialog
  },
  data() {
    return {
      rules: {
          name: [
            { required: true, message: '请输入阶段名称', trigger: 'blur' },
          ],
          chargePerson:[
            { required: true, message: '请选择负责人', trigger: 'change' },
          ],
          stageType: [
            { required: true, message: '请选择是否为回款阶段', trigger: 'change' }
          ],
        },
      statusType:[
        {
            name:'未开始',
            value:1,
        },
        {
          name:'进行中',
          value:2,
        },
        {
          name:'已完成',
          value:3,
        },
    ],
      form:{
        name:'',
        chargePerson:'',
        collaborator:'',
        stageType:'',
        stageTemplateId:'',
        xOrder:1,
        yOrder:1,
        chargePersonName:'',
        collaboratorName:'',
      },
      dialogName:'',
      dialogVisible:false,
      multipleNum:1,
      xiezuolist:[],
      stageTypeArr:[  
        {
            name:'一般阶段',
            value:1,
        },
        {
          name:'回款阶段',
          value:2,
        },
       ],
       stageId:'',
    }
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
      },
    },
  },
  created(){
    
  },
  methods: {
    getStageInfo(stageId){
        this.stageId = stageId
        this.xiezuolist = [];
        stageInfo(stageId).then(res=>{
            if(res.status == 0){
                    this.form  = res.data
                    // 负责人/协作人
                    var ids = res.data.collaborator && res.data.collaborator.split(',');
                    let names = res.data.collaboratorName && res.data.collaboratorName.split(',');
                    ids && ids.forEach((item,index) => {
                        var data = {};
                        data.id = item;
                        data.name = names[index];
                        this.xiezuolist.push(data)
                    });
            }
        })
    },
    save(){
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.form.xOrder = this.xOrder
            this.form.yOrder  = this.yOrder
            this.form.projectId = this.$route.query.id
            if(this.form.chargePerson == ''){
              this.msgError('请选择负责人')
              return
            }
            if(this.stageId){
              this.form.id = this.stageId
              stageUpdate(this.form).then(res=>{
                if(res.status == 0){
                  this.msgSuccess('修改成功')
                  Object.keys(this.form).forEach(key=>{
                    this.form[key] = ''
                  })
                  this.$emit('loadData')
                       this.$emit('changeDrawer', false)
                }else{
                  this.msgError(res.msg)
                }
               })
            }else{
              stageSave(this.form).then(res=>{
                if(res.status == 0){
                  this.msgSuccess('添加成功')
                  this.$emit('loadData')
                  Object.keys(this.form).forEach(key=>{
                    this.form[key] = ''
                  })
                       this.$emit('changeDrawer', false)
                       this.$emit('loadData')
                }else{
                  this.msgError(res.msg)
                }
               })
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
   
   
    },
    submitData(data,type,departmentId){
            if (type == '选择负责人') {
                this.form.chargePerson = data.length>0 ? data[0].id :'';
                this.form.chargePersonName = data.length>0 ? data[0].name :'';
                this.form.customerDepartmentId = departmentId;
                this.$refs.form.validateField(['chargePerson']);
            }else if(type == '选择协作人'){
                this.xiezuoData(data)
                this.xiezuolist = data;
            }
            this.updateSystemVisible(false)
        },
        handleTagClose(tag) {
            this.xiezuolist.splice(this.xiezuolist.indexOf(tag), 1);
            this.xiezuoData(this.xiezuolist)
        },
        xiezuoData(list){
            var ids = [];
            var names = [];
            list.forEach(item => {
                ids.push(item.id);
                names.push(item.name);
            });
            this.form.collaborator = ids.join(',');
            this.form.collaboratorName = names.join(',');
        },
    updateSystemVisible(value){
            this.dialogVisible = value;
        },
       // 选择协作人
       clickXuan(name,multipleNum){
            this.dialogName = name;
            this.multipleNum = multipleNum;
            this.$refs.systemdialog.loadData();
            if (name == '选择负责人') {
                this.form.chargePerson ? this.$refs.systemdialog.updateWorksId([{id:this.form.chargePerson,name:this.form.chargePersonName}]):this.$refs.systemdialog.updateWorksId([]);
            }else if(name == '选择协作人'){
                this.form.collaborator ? this.$refs.systemdialog.updateWorksId(this.xiezuolist):this.$refs.systemdialog.updateWorksId([]);
            }
            this.dialogVisible = true;
        },
        handleClose() {
          this.stageId = ''
          this.xiezuolist = []
          this.$refs['form'].resetFields();
          this.$emit('changeDrawer', false)
        },
  },
}
</script>

<style lang="scss" scoped>
.tagcss /deep/ .el-icon-close {
    width: 12px;
    height: 12px;
    line-height: 12px;
    background-color: #4285F4;
    color: white;
}
.tagcss {
    margin-left: 8px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    background: #DFEAFD;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
}
.bcenter{
  margin: 0 auto;
  display: block;
}
.rcenter{
    position: absolute;
    right: 10px;
    line-height: 34px;
    font-size: 14px;
    color: #c0c4cc;
}
  .drawer{
    /deep/.el-drawer{
      padding: 20px;
      padding-top: 0;
    }
    /deep/ .el-drawer__header{
      padding-left: 0;
      text-align: center;
    }
  }
</style>>
