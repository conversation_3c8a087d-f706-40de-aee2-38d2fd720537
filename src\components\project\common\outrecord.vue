<template>
  <div>
    <div v-if="!isControl" class="fl mb30 mtop20">
        <div class="tcss">产出内容：{{ viewdata.productNum }}个</div>
        <div class="tcss">累计时长：{{viewdata.accumulatedDuration}}</div>
    </div>
    <el-table
    :data="tableData"
    class="mytable"
    height="590px"
    style="width: 100%">
    <el-table-column
      prop="workTime"
      label="日期"
      width="120">
    </el-table-column>
    <el-table-column
      v-if="!isControl"
      prop="taskName"
      label="任务"
      width="180"
      align="center"
      show-overflow-tooltip>
    </el-table-column>
    <el-table-column
      prop="content"
      label="内容"
      min-width="180">
    </el-table-column>
    <el-table-column
      prop="duration"
      label="时长"
      width="120">
    </el-table-column>
    <el-table-column
    v-if="!isControl"
      prop="createByName"
      label="记录人"
      width="120">
    </el-table-column>
    <el-table-column
      v-if="isControl"
      prop="edit"
      label="操作"
      width="80" align="center">
      <template slot-scope="scope">
        <span class="rbtn" @click="showDelete(scope.row)">删除</span>
      </template>
    </el-table-column>
  </el-table>
  <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                @updatePageNum="handleCurrentChange"></page>
  </div>
</template>

<script>
import page from '@/components/common/page.vue';
import { hourWorkList,taskworkhoursDelete,queryWorkListForTask } from '@/api/project'
import Bus from '@/utils/EventBus'
export default {
  props:{
    isControl:{
      type:Boolean,
      default:false,
    }
  },
  // inject: ['taskIdObj'],
  inject: {
    taskIdObj: {
      default: ()=>({})
    }
  },
  data(){
    return{
      tableData:[],
      viewdata:{
        accumulatedDuration:"",
        productNum:""
      },
      pageBean:{
        pageNum:1,
        pageSize:10,
        type:'2',
        taskId:'',
      },
      total:0,
    }
  },
  components:{
    page
  },
  created(){
    
    if (this.isControl) {
      Bus.$on('loadDataOut', (msg) => {
        this.pageBean.taskId = this.taskIdObj.taskId
        this.loadData()
      })
    }else{
      this.pageBean.taskId = this.$route.query.id
    }
  },
  methods:{
    showDelete(row){
      taskworkhoursDelete({ id: row.id }).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '删除成功'
                        })
                        this.loadData();
                    } else {
                        this.$message({
                            type: "error",
                            message: result.msg
                        })
                    }
                }).catch((err) => {
                });
    },
    loadData(){
      hourWorkList(this.pageBean).then(res=>{
        if(res.status == 0){
          this.tableData = res.data;
          this.total = res.page.total;
        }
      })
    },
    handleCurrentChange(page){
      this.pageBean.pageNum = page
      if (this.isControl) {
        this.loadData()
      }else{
        this.loadout()
      }
    },
    updateTaskId(taskId){
      this.pageBean.taskId = taskId
    },
    loadout(taskId){
      if (taskId) {
        this.pageBean.taskId = taskId
      }
      queryWorkListForTask(this.pageBean).then((result) => {
          this.viewdata.accumulatedDuration = result.data.accumulatedDuration
          this.viewdata.productNum = result.data.productNum
          this.tableData = result.data.taskWorkHoursVoList;
          this.total = result.page.total;
      }).catch((err) => {
          
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.fl{
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.mtop20{
  margin-top: 20px;
}
.tcss+.tcss{
  margin-left: 10px;
}
.tcss{
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}
.rbtn{
  color: #F45961;
  cursor: pointer;
}
.mytable{
  border: 1px solid #E6E6E6;
  border-radius: 8px;
}
</style>