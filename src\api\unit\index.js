import service from '@/utils/request.js'
export function unitSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/structure/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function unitUpdate(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/structure/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function queryStructureTreeList(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/structure/list',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function orDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/structure/delete',
    data,
  });
}


export function cusList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/structurecustomer/list',
    params,
  });
}

export function cusListAll(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/structurecustomer/structureCustomerlist',
    params,
  });
}


export function unitCusSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/structurecustomer/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}



export function unitCusDele(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/structurecustomer/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
