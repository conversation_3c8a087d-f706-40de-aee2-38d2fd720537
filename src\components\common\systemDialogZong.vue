<template>
  <el-dialog
    :title="name"
    :visible.sync="dialogVisible"
    width="768px"
    top="5%"
    center
    class="unittree clearfix"
    @close="closeAction"
  >
    <!-- <div class="maindiv"> -->
    <el-form size="mini" inline class="mt right">
      <el-form-item label="">
        <el-input
          size="mini"
          class="inputWid150"
          placeholder="请输入员工姓名"
          v-model="searchName"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          circle=""
          @click="searchAction"
        ></el-button>
      </el-form-item>
    </el-form>
    <div class="condiv">
      <div class="ldiv">
        <span class="spantext">部门</span>
        <el-tree
          :data="data"
          class="ptop"
          node-key="id"
          updateKeyChildren="updateKeyChildren"
          default-expand-all
          :highlight-current="true"
          :expand-on-click-node="false"
          :props="defaultProps"
          @node-click="nodeClick"
        >
        </el-tree>
      </div>
      <div class="line"></div>
      <div class="rdiv">
        <span class="spantext"> 员工 </span>
        <div>
          <el-checkbox
            class="workercheckbox"
            :disabled="getChechkAllDisable()"
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            >全选</el-checkbox
          >
        </div>
        <div v-if="isSearch">
          <el-checkbox-group class="workercheckbox" v-model="checkedWorkers">
            <el-checkbox
              class="lineboxitem"
              v-for="item in workers"
              :label="item.key"
              :key="item.key"
              :disabled="getDisable(item.id)"
              @change="(e) => handleCheckedWorkersChange(e, item)"
              >{{ item.name }} — {{ item.departmentName }}</el-checkbox
            >
          </el-checkbox-group>
        </div>
        <el-checkbox-group
          v-else
          class="workercheckbox"
          v-model="checkedWorkers"
        >
          <el-checkbox
            class="boxitem"
            v-for="item in workers"
            :label="item.key"
            :key="item.key"
            :disabled="getDisable(item.id)"
            @change="(e) => handleCheckedWorkersChange(e, item)"
            >{{ item.name }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="saveAction">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { listDept, listSatff } from '@/api/framework/dept'
export default {
  props: {
    multipleNum: {
      type: Number,
      default: 1,
    },
    name: {
      type: String,
      default: '',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    filtrationList: {
      type: Array,
      default: () => {
        return new Array()
      },
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('updateVisible', value)
      },
    },
  },
  data() {
    return {
      isSearch: false,
      isIndeterminate: false,
      checkAll: false,
      searchName: '',
      workers: [],
      checkedWorkers: [],
      selList: [],
      selDepartmentId: '',
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      pageBean: {
        chargePersonName: '',
      },
    }
  },
  methods: {
    searchButton() {},
    loadData() {
      listDept({})
        .then((result) => {
          this.data = result.data
        })
        .catch((err) => {})
    },
    updateWorksId(data) {
      this.checkedWorkers = []
      this.selList = []
      this.selList = data.map((item) => item)
      this.selList.forEach((element) => {
        this.checkedWorkers.push(`${element.id}_${element.departmentId}`)
      })
    },
    nodeClick(data) {
      if (this.isSearch) {
        this.searchName = ''
      }
      this.checkAll = false
      this.selDepartmentId = data.id
      listSatff({ departmentId: data.id })
        .then((result) => {
          this.isSearch = false
          var count = 0
          result.data.map((item) => {
            item.key = `${item.id}_${item.departmentId}`
            if (this.checkedWorkers.indexOf(item.key) >= 0) {
              count++
            }
          })
          if (count == result.data.length && result.data.length > 0) {
            this.checkAll = true
          }
          this.workers = result.data
        })
        .catch((err) => {})
    },
    updateKeyChildren(key, data) {},
    getDisable(id) {
      if (this.filtrationList.indexOf(id) > -1) {
        return true
      }
      return false
    },
    handleCheckedWorkersChange(checked, data) {
      if (checked) {
        if (this.selList.length < this.multipleNum || this.multipleNum == 0) {
          this.selList.push(data)
        } else if (this.multipleNum == 1) {
          this.selList = [data]
          this.checkedWorkers = [data.key]
        } else {
          this.$message.info(`最多选择${this.multipleNum}个`)
          var index = this.checkedWorkers.indexOf(data.key)
          this.checkedWorkers.splice(index, 1)
        }
        var count = 0
        this.workers.forEach((item) => {
          if (this.checkedWorkers.indexOf(item.key) >= 0) {
            count++
          }
        })

        if (this.workers.length === count) {
          this.checkAll = true
        }
      } else {
        if (this.checkAll) {
          this.checkAll = false
        }
        var index = -1
        this.selList.forEach((item, idx) => {
          if (item.key == data.key) {
            index = idx
            return
          }
        })
        if (index >= 0) {
          this.selList.splice(index, 1)
        }
      }
    },
    closeAction() {
      this.workers = []
      this.isSearch = false
      this.searchName = ''
    },
    saveAction() {
      if (this.multipleNum == 1) {
        var departmentId = ''
        if (this.departmentId) {
          departmentId = ''
        } else {
          departmentId = this.selList[0].departmentId
        }
        this.$emit('submitData', this.selList, this.name, departmentId)
      } else {
        this.$emit('submitData', this.selList, this.name)
      }
    },
    searchAction() {
      if (this.searchName) {
        this.isSearch = true
        listSatff({ userName: this.searchName })
          .then((result) => {
            result.data.map(
              (item) => (item.key = `${item.id}_${item.departmentId}`)
            )
            this.workers = result.data
          })
          .catch((err) => {})
      }
    },
    getChechkAllDisable() {
      if (this.workers.length == 0) {
        return true
      }
      if (this.multipleNum < this.workers.length) {
        return true
      }
      return false
    },
    handleCheckAllChange(val) {
      if (val) {
        if (this.multipleNum != 0 && this.multipleNum < this.workers.length) {
          this.$message.info(`${this.name}最多选择${this.multipleNum}`)
          return
        }
        //将选中的数据加入this.sellist 中 并去重
        this.workers.forEach((item) => {
          if (
            this.selList.indexOf(item) === -1 &&
            this.filtrationList.indexOf(item.id) === -1
          ) {
            this.selList.push(item)
            this.checkedWorkers.push(item.key)
          }
        })
      } else {
        var filteridxs = this.workers.map((item) => item.key)
        this.selList = this.selList.filter(
          (item) => filteridxs.indexOf(item.key) === -1
        )
        this.checkedWorkers = this.checkedWorkers.filter(
          (item) => filteridxs.indexOf(item) === -1
        )
      }
    },
  },
}
</script>

<style scoped>
.sediv {
  padding: 12px;
}
.searchcss {
  width: 200px;
  margin-right: 12px;
}
.mt {
  position: absolute;
  left: 16px;
  top: 15px;
}
.ptop {
  padding-top: 10px;
}
.workercheckbox {
  padding-top: 10px;
}
.workercheckbox /deep/.el-checkbox__label {
  line-height: 32px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  min-width: 65px;
}
.unittree /deep/.el-dialog__body {
  height: 640px;
  padding: 0 !important;
}
.unittree /deep/.el-tree-node__label {
  font-size: 12px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 14px;
}
.unittree /deep/.el-tree-node__expand-icon {
  color: #333333;
}
.unittree /deep/.el-dialog__header {
  border-bottom: none;
}
.unittree /deep/.el-tree-node__expand-icon.is-leaf {
  color: transparent;
}
.condiv {
  display: flex;
  width: 100%;
  height: calc(100%);
  border-top: 1px solid #f0f0f0;
}
.ldiv {
  padding-top: 30px;
  padding-left: 26px;
  width: 35%;
  overflow-y: scroll;
}
.line {
  margin-right: 30px;
  margin-top: 3+0px;
  width: 1px;
  height: 100%;
  background-color: #f0f0f0;
}
.spantext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.rdiv {
  width: 60%;
  padding-top: 30px;
  padding-left: 0px;
  overflow-y: scroll;
}
.lineboxitem {
  width: calc(100% - 30px);
}
</style>