<template>
  <div>
    <textBorder>项目留言板</textBorder>
    <ul class="ulcss logdiv">
      <li class="clearfix lilog" v-for="(item,index) in logList" :key="index" >
        <div class="left times">
            <p>{{ item.createTime.substring(0,10) }}</p>
            <p>{{ item.createTime.substring(10) }}</p>
        </div>
        <div class="left">
                <headuser :url="item.userLogo" width="40" :username="item.createByName"></headuser>
                <div class="left">
                    <span class="pname">{{ item.createByName }}</span>
                    <p class="ptext">{{ item.content }}</p>
                </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import textBorder from '@/components/common/textBorder.vue'
import {projectlogList} from '@/api/project/index'


import headuser from '@/components/common/headuser.vue'
  export default {
    props:{
      proId:{
        type:String,
        default:''
      }
    },
        components:{
          textBorder,
          headuser
        },
        watch:{
          proId:{
            immediate:true,
            handler(newValue){
              if(newValue){
                this.logListData()
              }
            }
          }
        },
        data(){
          return {
            logList:[]
          }
        },
        methods:{
          logListData(){
            projectlogList({projectId:this.proId}).then(res=>{
                    if(res.status == 0){
                          if(res.data){
                            this.logList = res.data
                          }
                    }
                })
          },
        }
        
  }
</script>

<style lang="scss" scoped>
.logdiv{
  padding-bottom: 44px;
  height: calc(100vh - 220px);
  overflow-x:hidden;
}
.lilog{
  margin-bottom: 20px;
}
.pname{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.ptext{
  margin-top: 8px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.times{
  margin-right: 16px;
}
.ulcss{
  padding: 20px 30px;
}
.head{
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 8px;
}
</style>