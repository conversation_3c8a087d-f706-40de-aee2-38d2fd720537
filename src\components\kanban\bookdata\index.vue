<template>
  <div class="bgimg">
    <img class="logo" src="../../../assets/booklogo.png" alt="" />
    <div class="tabs">
      <div
        class="tab"
        v-for="(item, index) in tabList"
        :class="{ activeCss: index == currentIndex }"
        @click="changeTab(index)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="search">
      <div class="searchbox">
        <el-form
          v-show="currentIndex == 0"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          :rules="rules"
          ref="majorRuleForm"
        >
          <el-form-item label="专业类型 :">
            <el-select
              clearable=""
              class="elSelect"
              v-model="formInline.specialtyType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in speaiclTypeData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="专业名称 :"
            label-width="110px"
            prop="specialtyName"
          >
            <el-input
              class="elInput"
              v-model="formInline.specialtyName"
              placeholder="请输入专业名词"
            ></el-input>
          </el-form-item>
        </el-form>

        <el-form
          v-show="currentIndex == 1"
          :inline="true"
          :model="bookFormData"
          class="demo-form-inline"
          :rules="rulesBook"
          ref="ruleFormBook"
        >
          <el-form-item label="教材名称 :" prop="materialName">
            <el-input
              class="elInput"
              v-model="bookFormData.materialName"
              placeholder="请输入教材名称或ISBN"
            ></el-input>
          </el-form-item>

          <el-form-item label="出版社 :" label-width="110px">
            <el-select
              filterable
              class="elSelect"
              v-model="bookFormData.pressId"
              placeholder="请选择"
              clearable=""
            >
              <el-option
                v-for="item in optionsPress"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用书时间：" label-width="110px">
            <el-date-picker
              :editable="false"
              :clearable="true"
              class="yearcss definput"
              value-format="yyyy"
              v-model="useBookYear"
              type="year"
              placeholder="请选择用书时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" v-if="useBookYear">
            <el-select
              class="jijie"
              v-model="useSeason"
              placeholder="请选择"
              clearable
            >
              <el-option label="春季" value="春季"></el-option>
              <el-option label="秋季" value="秋季"></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <el-form
          v-show="currentIndex == 2"
          :inline="true"
          :model="unitFormData"
          class="demo-form-inline"
          :rules="rulesUnitForm"
          ref="ruleFormUnit"
        >
          <el-form-item label="单位名称 :" prop="unitName">
            <el-input
              class="elInput"
              v-model="unitFormData.unitName"
              placeholder="请输入单位名称"
            ></el-input>
          </el-form-item>

          <el-form-item label="类型 :" label-width="110px">
            <el-select
              @change="changeType"
              class="elSelect"
              v-model="unitFormData.unitType"
              placeholder="请选择"
              clearable=""
            >
              <el-option
                v-for="item in unitTypeData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="特点 :"
            label-width="110px"
            v-if="unitFormData.unitType"
          >
            <el-select
              class="elSelect"
              v-model="unitFormData.unitCharacter"
              placeholder="请选择"
              clearable=""
            >
              <el-option
                v-for="item in characterData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="单位地址 :"
            prop="addressData"
            label-width="87px"
            label-position="right"
          >
            <el-cascader
              clearable=""
              v-model="addressData"
              class="definput unitf"
              :options="optionsAddress"
              :props="{
                value: 'id',
                label: 'name',
                children: 'areaList',
                checkStrictly: true,
              }"
            ></el-cascader>
          </el-form-item>
        </el-form>
      </div>
      <p class="tips">{{ tipsMsg[currentIndex] }}</p>
    </div>
    <el-button type="primary" class="searchButton" @click="submitForm"
      >开始查询</el-button
    >
  </div>
</template>

<script>
import { getDict } from '@/utils/tools'
import { queryAreaVoList } from '@/api/index'
import { queryUnitCharacter } from '@/api/clientmanagement/unit'
import { queryVoListByCode } from '@/api/wapi.js'
export default {
  data() {
    return {
      tipsMsg: {
        0: '说明:通过专业查询，能够获得该专业的开设情况与开设院校教材使用情况',
        1: '说明:通过教材查询，能够获得该教材的使用院校、用书专业等数据',
        2: '说明:通过单位查询，能够获得查询范围中单位的教材的使用数据',
      },
      tabList: [
        {
          id: '1',
          name: '通过专业查',
        },
        {
          id: '2',
          name: '通过教材查',
        },
        {
          id: '3',
          name: '通过单位查',
        },
      ],
      currentIndex: 0,
      formInline: {
        specialtyName: '',
        specialtyType: '',
      },
      rules: {
        specialtyName: [
          { required: true, message: '请输入专业名称', trigger: 'blur' },
        ],
      },
      rulesBook: {
        materialName: [
          { required: true, message: '请输入教材名称', trigger: 'blur' },
        ],
      },
      unitTypeData: [],
      bookForm: {},
      addressData: [],
      optionsAddress: [],
      speaiclTypeData: [],
      characterData: [],
      bookFormData: {
        materialName: '',
        pressId: '',
        useBookYear: '',
      },
      typeForm: {
        0: 'majorRuleForm',
        1: 'ruleFormBook',
        2: 'ruleFormUnit',
      },
      useBookYear: '',
      useSeason: '',
      optionsPress: [],
      unitFormData: {
        unitName: '',
        unitType: '',
        unitCharacter: '',
        provinceId: '',
        cityId: '',
      },
      rulesUnitForm: {
        unitName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    getDict('SpecialType').then((result) => {
      this.speaiclTypeData = result
    })
    getDict('UnitType').then((result) => {
      this.unitTypeData = result
    })
    this.queryAreaVoListApi()
  },
  watch: {
    useBookYear(newVal, oldVal) {
      if (!newVal) {
        this.useSeason = ''
      }
    },
  },
  methods: {
    queryPressApi() {
      queryVoListByCode({ code: 'Press' }).then((res) => {
        if (res.status == 0) {
          this.optionsPress = res.data
        }
      })
    },
    changeType(val) {
      if (!val) {
        this.characterData = []
        this.unitFormData.unitCharacter = ''
      } else {
        queryUnitCharacter({ parentId: val, isDeleted: 0 }).then((res) => {
          if (res.status == 0) {
            this.unitFormData.unitCharacter = ''
            this.characterData = res.data
          }
        })
      }
    },
    handleTreeList(list) {
      for (var i = 0; i < list.length; i++) {
        if (list[i].areaList.length < 1) {
          list[i].areaList = undefined
        } else {
          this.handleTreeList(list[i].areaList)
        }
      }
      return list
    },
    queryAreaVoListApi() {
      queryAreaVoList({ level: 0 }).then((res) => {
        if (res.status == 0) {
          this.optionsAddress = this.handleTreeList(res.data)
        }
      })
    },
    submitForm() {
      let formName = this.typeForm[this.currentIndex]
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.currentIndex == 0) {
            this.$router.push({
              path: '/kanban/bookdata/majorresult',
              query: {
                specialtyName: this.formInline.specialtyName,
                specialtyType: this.formInline.specialtyType,
              },
            })
          } else if (this.currentIndex == 1) {
            if (this.useSeason) {
              this.bookFormData.useBookYear =
                this.useBookYear + '年' + this.useSeason
            } else {
              if (this.useBookYear) {
                this.bookFormData.useBookYear = this.useBookYear + '年'
              } else {
                this.bookFormData.useBookYear = ''
              }
            }
            this.$router.push({
              path: '/kanban/bookdata/booksearchresult',
              query: {
                materialName: this.bookFormData.materialName,
                pressId: this.bookFormData.pressId,
                useBookYear: this.bookFormData.useBookYear,
              },
            })
          } else if (this.currentIndex == 2) {
            if (this.addressData) {
              if (this.addressData.length == 1) {
                this.unitFormData.provinceId = this.addressData[0]
                this.unitFormData.cityId = ''
              } else {
                this.unitFormData.provinceId = this.addressData[0]
                this.unitFormData.cityId = this.addressData[1]
              }
            } else {
              this.unitFormData.provinceId = ''
              this.unitFormData.cityId = ''
            }
            this.$router.push({
              path: '/kanban/bookdata/unitsearchdata',
              query: {
                unitName: this.unitFormData.unitName,
                unitType: this.unitFormData.unitType,
                unitCharacter: this.unitFormData.unitCharacter,
                provinceId: this.unitFormData.provinceId,
                cityId: this.unitFormData.cityId,
              },
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    changeTab(index) {
      this.currentIndex = index
      if (index == 1) {
        this.queryPressApi()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.bgimg {
  background: url('../../../assets/bookimg.png') no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .logo {
    margin: 0 auto;
    margin-top: 90px;
    display: block;
  }
  .tabs {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .tab {
    width: 104px;
    height: 54px;
    margin-right: 30px;
    text-align: center;
    line-height: 46px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #4285f4;
    cursor: pointer;
    margin-top: 91px;
  }
  .activeCss {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #ffffff;
    background: url('../../../assets/tabbg.png') no-repeat;
  }
  .search {
    padding: 0 80px;
    margin-top: 4px;
    opacity: 1;
  }
  .tips {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    margin-top: 16px;
  }
  .searchbox {
    padding: 40px 0;
    padding-bottom: 18px;
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0px 20px 40px 0px rgba(157, 177, 255, 0.2);
    border-radius: 12px 12px 12px 12px;
    border: 3px solid #4285f4;
    backdrop-filter: blur(20px);
    padding-left: 48px;
    /deep/.el-form-item__label {
      height: 44px;
      line-height: 44px;
    }
  }
  .elSelect,
  .elInput {
    width: 346px;
    height: 44px;
  }
  .searchButton {
    width: 184px;
    height: 52px;
    background: #4285f4;
    border-radius: 4px 4px 4px 4px;
    display: block;
    margin: 0 auto;
    margin-top: 60px;
  }
  .yearcss {
    width: 202px;
    height: 44px;
  }
  .jijie {
    width: 132px;
  }
  /deep/.el-input__inner {
    height: 44px;
    height: 44px;
    line-height: 44px;
    border-radius: 8px;
  }
  .unitf {
    width: 346px;
  }
}
</style>