<!-- 获取公司列表 -->
<template>
  <div>
    <el-select clearable placeholder="请选择公司" filterable v-model="myValue" @change="change">
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  props: ['value'],
  data () {
    return {
      options: [],
      myValue: ''
    }
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    change (val) {
      let name = ''
      this.options.map(item => {
        if (item.id === val) {
          name = item.name
        }
      })
      let obj = {
        companyId: val,
        companyName: name
      };
      this.$emit('handleGetCompany', obj)
    },
    async getReqData () {
      let data = {};
      data.pageNum = '1';
      data.pageSize = '100000'
      // let res = await this.$axios.get('/sf/business/company/list', {params: data});
      let res = await this.$axios.get('/sf/business/userinfo/company');
      if (res.status === 0) {
        this.options = res.data
      }
    }
  },
  created () {
    this.myValue = this.value || ''
    this.getReqData()
  }
}
</script>
