<template>
  <div>
    <div>
      <back>{{ form.id ? '编辑教材发货' : '新建发货' }}</back>
    </div>
    <div class="mainbg" v-loading="isLoading">
      <el-form
        ref="addform"
        :model="form"
        :rules="rules"
        class="addfcss"
        label-width="118px"
      >
        <textBorder>基础信息</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="10">
              <!-- <el-form-item label="选择教材：" prop="materialId">
                <div class="flex">
                  <div
                    class="unitbtn"
                    :class="{ wid2: form.materialId }"
                    @click="chooseBook"
                  >
                    <span v-if="form.materialId" class="deffont">{{
                      form.materialName
                    }}</span>
                    <span v-else class="pltcss">请选择</span>
                    <i class="rcenter el-icon-arrow-down" />
                  </div>
                  <el-popover
                    v-show="form.materialId"
                    ref="popover"
                    placement="right"
                    class="wid"
                    title="教材信息"
                    width="300"
                    v-model="popoverVisibble"
                    trigger="manual"
                  >
                    <div class="matdetail">
                      <p>教材名称：{{ bookInfo && bookInfo.name }}</p>
                      <p>主编：{{ bookInfo && bookInfo.author }}</p>
                      <p>ISBN：{{ bookInfo && bookInfo.isbn }}</p>
                      <p>出版社：{{ bookInfo && bookInfo.platformName }}</p>
                      <p>价格：{{ bookInfo && bookInfo.price }}元</p>
                      <p>
                        出版时间：{{
                          bookInfo && bookInfo.publicationRevisionTime
                        }}
                      </p>
                      <p>业务经理：{{ bookInfo && bookInfo.operationName }}</p>
                      <i
                        class="el-icon-close tr"
                        @click="popoverVisibble = false"
                      ></i>
                    </div>
                    <span slot="reference" class="bBtn" @click="lookBookInfo"
                      >查看教材</span
                    >
                  </el-popover>
                </div>
              </el-form-item> -->
              <el-form-item label="折扣：" prop="discount">
                <el-input-number
                  class="definput wi"
                  type="number"
                  :controls="false"
                  v-model="form.discount"
                  :min="0"
                  :max="10"
                  :precision="2"
                  placeholder="请输入教材折扣(0~10),保留两位小数"
                ></el-input-number>
              </el-form-item>
              <el-form-item label="物流单：" prop="trackingNumber">
                <el-input
                  class="definput"
                  onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                  v-model="form.trackingNumber"
                  maxlength="20"
                  placeholder="请输入物流单"
                ></el-input>
              </el-form-item>
              <el-form-item label="业务负责人：" prop="chargePerson">
                <div class="unitbtn" @click="clickXuan('请选择业务负责人', 1)">
                  <span v-if="form.chargePerson" class="deffont">{{
                    form.chargeName
                  }}</span>
                  <span v-else class="pltcss">请选择业务负责人</span>
                  <i class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <!-- <el-form-item label="发货量：" prop="sendNumber">
                <el-input
                  class="definput"
                  type="number"
                  v-model="form.sendNumber"
                  placeholder="请输入发货量"
                ></el-input>
              </el-form-item> -->
              <el-form-item prop="useBookYear" label="用书时间：">
                <el-date-picker
                  class="definput wid100"
                  v-model="useBookYear"
                  type="year"
                  format="yyyy"
                  value-format="yyyy年"
                  placeholder="选择年"
                  @change="changeUseBookYear"
                >
                </el-date-picker>
                <span class="pdl">
                  <el-radio class="mr10" v-model="radio" label="春季"
                    >春季</el-radio
                  >
                  <el-radio class="mr10" v-model="radio" label="秋季"
                    >秋季</el-radio
                  >
                </span>
              </el-form-item>
              <el-form-item label="发货时间：" prop="deliveryTime">
                <el-date-picker
                  class="definput width100 datepicker"
                  v-model="form.deliveryTime"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy年MM月dd日"
                  placeholder="选择发货时间"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="仓库负责人：" prop="confirmPerson">
                <div class="unitbtn" @click="clickXuan('请选择仓库负责人', 1)">
                  <span v-if="form.confirmPerson" class="deffont">{{
                    form.confirmName
                  }}</span>
                  <span v-else class="pltcss">请选择仓库负责人</span>
                  <i class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <textBorder class="mt30">教材</textBorder>
        <div class="pt20 bbline">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="10">
              <el-form-item label="收货人：" prop="receivePerson">
                <div class="unitbtn" @click="chooseCustomer">
                  <span v-if="form.receivePerson" class="deffont">{{
                    form.receiveName
                  }}</span>
                  <span v-else class="pltcss">请选择收货人</span>
                  <i class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
              <!-- <el-form-item label="用书专业：" prop="useBookSpecialty">
                <el-select
                  class="definput"
                  popper-class="removescrollbar"
                  v-model="form.useBookSpecialty"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in majorList"
                    :key="item.id"
                    :label="item.specialtyName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item label="关联合同：" prop="contractId">
                <div
                  class="unitbtn sw"
                  :class="{ selectedCss: form.contractName }"
                  @click="chooseContract"
                >
                  {{ form.contractName ? form.contractName : '请选择' }}
                  <i
                    @click.stop="closeCon"
                    v-show="form.contractName"
                    class="rcenter el-icon-close"
                  ></i>
                  <i
                    v-show="!form.contractName"
                    class="rcenter el-icon-arrow-down"
                  />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="用书单位：" prop="receiveUnit">
                <div class="unitbtn">
                  <span v-if="form.receiveUnit" class="deffont">{{
                    form.unitName
                  }}</span>
                  <span v-else class="pltcss">请选择收货人</span>
                  <i class="rcenter el-icon-arrow-down" />
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="pt20 bbline">
            <el-table class="mytable" :data="bookList">
              <el-table-column
                width="200"
                label="教材名称"
                prop="name"
                align="center"
              >
                <template slot-scope="scope">
                  <div class="flex">
                    <div class="unitbtn2">
                      <span v-if="scope.row.id" class="deffont">{{
                        scope.row.name
                      }}</span>
                    </div>
                    <span
                      class="pltcss2"
                      @click="chooseBook(scope.$index, scope.row)"
                      >选择</span
                    >
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                width="170"
                label="用书专业"
                prop=""
                align="center"
              >
                <template slot-scope="scope">
                  <el-select
                    size="mini"
                    popper-class="removescrollbar"
                    v-model="scope.row.bookSpecialty"
                    :placeholder="
                      form.receivePerson ? '请选择' : '请先选择收货人'
                    "
                  >
                    <el-option
                      v-for="item in majorList"
                      :key="item.id"
                      :label="item.specialtyName"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                label="isbn"
                prop="isbn"
                align="center"
                min-width="167px"
              ></el-table-column>
              <el-table-column
                label="主编"
                prop="author"
                align="center"
                min-width="167px"
              ></el-table-column>
              <el-table-column
                label="出版社"
                prop="platformName"
                align="center"
                min-width="167px"
              ></el-table-column>
              <el-table-column
                label="出版时间"
                prop="publicationRevisionTime"
                align="center"
                min-width="167px"
              ></el-table-column>
              <el-table-column
                label="价格"
                fixed="right"
                prop="price"
                align="center"
                min-width="100px"
              ></el-table-column>
              <el-table-column
                width="140"
                label="发货数量"
                fixed="right"
                prop="number"
                align="center"
              >
                <template slot-scope="scope">
                  <el-input-number
                    :controls="false"
                    :precision="0"
                    :step="1"
                    :min="1"
                    size="mini"
                    placeholder="设置发货数量"
                    v-model="scope.row.number"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column 
              label="总码洋" 
              fixed="right" 
              align="center"
              min-width="167px">
                <template slot-scope="scope">
                  <span>{{
                    scope.row.number &&
                    (scope.row.price * scope.row.number).toFixed(2)
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" align="center">
                <template slot-scope="scope">
                  <span class="rbtn"  @click="delBook(scope.$index,scope.row)"
                    >删除</span
                  >
                </template>
              </el-table-column>
            </el-table>
            <div class="bottomView">
              <el-button type="text" icon="el-icon-plus" @click="addBook"
                >添加教材</el-button
              >
            </div>
          </div>
        </div>
        <textBorder class="mt30">补充信息</textBorder>
        <div class="pt20">
          <el-row :gutter="0" type="flex" justify="start">
            <el-col :span="20">
              <el-form-item label="备注：" prop="notes">
                <el-input
                  class="definput"
                  v-model="form.notes"
                  maxlength="300"
                  show-word-limit
                  type="textarea"
                  rows="4"
                  placeholder="请输入备注信息"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="pt20 btncenter">
          <el-form-item>
            <el-button
              class="btn_h42 wid98"
              type="primary"
              @click="submitForm"
              v-dbClick
              >保存</el-button
            >
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 选择组织架构中的人员 -->
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
    <!-- 收货人 -->
    <cuatomerDialog
      ref="customer"
      :visible.sync="customerDialogVisible"
      @updateVisible="updateVisible"
      @updateCustomer="updateCustomer"
    >
    </cuatomerDialog>
    <!-- 选择教材 -->
    <chooseBookDialog
      ref="bookdialog"
      :visible.sync="bookDialogVisible"
      @updateVisible="updateBookVisible"
      @updateData="updateBookData"
      :disableIds="disableIds"
    >
    </chooseBookDialog>
    <!-- 选择单位-->
    <!-- <unitDialog ref="unitdialog" :visible.sync="unitDialogVisible" className="CustomerController" @updateVisible="updateUnitVisible"
            @updateUnit="updateUnit"></unitDialog> -->
    <!-- 合同选择 -->
    <contractDialog
      ref="contractRef"
      :visible.sync="contractVisible"
      @updateVisible="updateContractVisible"
      @updateData="updateContractData"
    ></contractDialog>
  </div>
</template>

<script>
import back from '@/components/common/back.vue'
import { getToken } from '@/utils/auth'
import textBorder from '@/components/common/textBorder.vue'
import systemDialog from '@/components/common/systemDialog.vue'
import cuatomerDialog from '@/components/common/customerDialog.vue'
import chooseBookDialog from '@/components/common/chooseBookDialog.vue'
import { getDict, getFileType, addSnop } from '@/utils/tools'
import {
  adddeliver,
  updatedeliver,
  deliverInfo,
} from '@/api/materialdeliver/index'
import { selectUintSpecialty } from '@/api/clientmanagement/customer'
import { queryBookInfo } from '@/api/product/index'
import contractDialog from '../common/contractDialog.vue'
export default {
  components: {
    back,
    textBorder,
    systemDialog,
    cuatomerDialog,
    chooseBookDialog,
    contractDialog,
  },
  data() {
    return {
      majorList: [],
      bookList: [],
      bookDialogVisible: false,
      customerDialogVisible: false,
      popoverVisibble: false,
      dialogVisible: false,
      isSmall: window.screen.availWidth < 1500 ? true : false,
      isShowChargePerson: this.$route.query.id ? true : false,
      dialogName: '',
      multipleNum: 1,
      isLoading: false,
      rules: {
        sendNumber: [
          { required: true, message: '请输入发货量', trigger: 'blur' },
        ],
        contractId: [
          { required: true, message: '请选择合同', trigger: 'blur' },
        ],
        materialId: [
          { required: true, message: '请选择教材', trigger: 'change' },
        ],
        useBookYear: [
          { required: true, message: '请选择用书时间', trigger: 'change' },
        ],
        chargePerson: [
          { required: true, message: '请选择业务负责人', trigger: 'change' },
        ],
        useBookSpecialty: [
          { required: true, message: '请选择用书专业', trigger: 'change' },
        ],
        receiveUnit: [
          { required: true, message: '请选择用书单位', trigger: 'change' },
        ],
        receivePerson: [
          { required: true, message: '请选择收货人', trigger: 'change' },
        ],
        confirmPerson: [
          { required: true, message: '请选择仓库负责人', trigger: 'change' },
        ],
      },
      form: {
        id: '',
        materialId: '',
        sendNumber: '',
        discount: undefined,
        useBookYear: '',
        useBookSpecialty: '',
        deliveryTime: '',
        trackingNumber: '',
        chargePerson: '',
        receiveUnit: '',
        receivePerson: '',
        confirmPerson: '',
        contractId: '',
        contractName: '',
        chargePersonDepartmentId: '',
      },
      getUrl: `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`,
      headers: { Authorization: getToken() },
      fileData: {
        serviceName: 'crm/opportunityName',
      },
      xiezuolist: [],
      isChooseCustomer: false,
      options: [],
      types: [],
      unitDialogVisible: false,
      fileType: {
        image: 1,
        video: 2,
        redio: 3,
        file: 4,
      },
      useBookYear: '',
      radio: '春季',
      bookInfo: {
        name: '',
        isbn: '',
      },
      contractVisible: false,
      disableIds:[],
    }
  },
  created() {
    if (this.$route.query.customerId) {
      this.isChooseCustomer = true
      this.form.customerId = this.$route.query.customerId
      this.form.customerName = this.$route.query.customerName
      this.form.unitId = this.$route.query.unitId
      this.form.unitName = this.$route.query.unitName
    } else {
      this.isChooseCustomer = false
    }
    this.loadInfo()
  },
  methods: {
    delBook(index, data) {
      this.bookList.splice(index, 1)
    },
    addBook() {
      if (this.bookList.length>=100) {
        this.$message.error('最多选择100种教材')
        return
      }
      this.bookList.push({
          "goodsId":'',
          "name":'',
          "price":'',
          "number":undefined,
          "bookSpecialty":''
      })
    },
    chooseBook(index, row) {
      this.curSelId = row.goodsId || ''
      this.disableIds = []
      this.bookList.forEach(element => {
        if ( element.goodsId && element.goodsId != this.curSelId) {
          this.disableIds.push(element.goodsId)
        }
      });
      this.curBookIndex = index;
      this.curBookRow = row;
      this.bookDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.bookdialog.loadBookData({
          // opportunityId: this.form.id ? this.form.id : "",
          type: 2,
        },this.curSelId);
      });
    },
    updateBookVisible(val) {
      this.bookDialogVisible = val;
    },
    updateBookData(data) {
      if (data) {
        data.goodsId = data.id;
        this.curBookRow = Object.assign(this.curBookRow,data);
        this.bookList.splice(this.curBookIndex, 1, this.curBookRow);
      }else{
        this.curBookRow = {}
        this.bookList.splice(this.curBookIndex, 1, this.curBookRow);
      }
    },
    updateContractData(data) {
      this.form.contractName = data.contractTitle
      this.form.contractId = data.id
    },
    updateContractVisible(val) {
      this.contractVisible = val
    },
    chooseContract() {
      if (this.form.contractId == 0) {
        this.form.contractId = ''
      }
      let params = {
        id: this.form.contractId || '',
        methodName: 'list',
        className: 'ProjectController',
        opportunityId: '',
      }
      this.$refs.contractRef.selectContractData(params)
      this.contractVisible = true
    },
    loadMajor() {
      selectUintSpecialty({
        unitId: this.form.receiveUnit,
      })
        .then((result) => {
          this.majorList = result.data
        })
        .catch((err) => {})
    },
    loadInfo() {
      var id = this.$route.query.id
      if (id) {
        this.form.id = id
        this.isLoading = true
        deliverInfo(id)
          .then((result) => {
            this.form = result.data
            var useBookYear = this.form.useBookYear
            if (useBookYear) {
              var index = useBookYear.indexOf('年')
              this.useBookYear = useBookYear.substring(0, index + 1)
              this.radio = useBookYear.substring(index + 1, useBookYear.length)
            }
            if (this.form.distributionDetailList.length>0) {
              this.bookList = this.form.distributionDetailList 
            }else if(this.form.materialId){
              this.bookList = [{
                goodsId:this.form.materialId,
                id:this.form.materialId,
                name:this.form.materialName,
                price:this.form.price,
                number:this.form.sendNumber,
                platformName:this.form.platformName,
                isbn:this.form.isbn,
                bookSpecialty:this.form.useBookSpecialty
              }]
            }
            
            this.form.useBookSpecialty = this.form.useBookSpecialty || ''
            if (this.form.receiveUnit) {
              this.loadMajor()
            }
            this.isLoading = false
          })
          .catch((err) => {
            this.isLoading = false
          })
      }
    },
    updateCustomer(data) {
      if (data.unitId != this.form.receiveUnit ) {
        this.bookList && this.bookList.forEach(item =>{
          item.bookSpecialty = ''
        })
      }
      this.form.receiveName = data.customerName
      this.form.receivePerson = data.id
      this.form.receiveUnit = data.unitId
      this.form.unitName = data.unitName
      this.$refs.addform.validateField('receivePerson')
      if (this.form.receiveUnit) {
        this.form.useBookSpecialty = ''
        this.loadMajor()
      }
    },
    chooseCustomer() {
      if (this.isChooseCustomer) {
        return
      }
      this.customerDialogVisible = true
      this.$refs.customer.selectCustomerData({
        id: this.form.receivePerson,
        unitId: '',
        className: 'MaterialDeliverController',
      })
    },
    updateVisible(val) {
      this.customerDialogVisible = val
    },
    // 选择协作人
    clickXuan(name, multipleNum) {
      this.dialogName = name
      this.multipleNum = multipleNum
      this.$refs.systemdialog.loadData()
      if (name == '请选择业务负责人') {
        this.form.chargePerson
          ? this.$refs.systemdialog.updateWorksId([
              { id: this.form.chargePerson, name: this.form.chargeName },
            ])
          :  this.$refs.systemdialog.updateWorksId([])
      } else if (name == '请选择仓库负责人') {
        this.form.confirmPerson
          ? this.$refs.systemdialog.updateWorksId([
              { id: this.form.confirmPerson, name: this.form.confirmName },
            ])
          :  this.$refs.systemdialog.updateWorksId([])
      }
      this.dialogVisible = true
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    handleTagClose(tag) {
      this.xiezuolist.splice(this.xiezuolist.indexOf(tag), 1)
      this.ziezuoData(this.xiezuolist)
    },
    submitData(data, type, departmentId) {
      if (type == '请选择业务负责人') {
        this.form.chargePerson = data.length > 0 ? data[0].id : ''
        this.form.chargeName = data.length > 0 ? data[0].name : ''
        this.form.chargePersonDepartmentId = departmentId
        this.$refs.addform.validateField('chargePerson')
      } else if (type == '请选择仓库负责人') {
        this.form.confirmPerson = data.length > 0 ? data[0].id : ''
        this.form.confirmName = data.length > 0 ? data[0].name : ''
        this.$refs.addform.validateField('confirmPerson')
      }
      this.updateSystemVisible(false)
    },
    submitForm() {
      this.$refs['addform'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.form.chargePerson == this.form.confirmPerson) {
          this.$message({
            type: 'error',
            message: '发货人和确认人不能是同一个人!',
          })
          return false
        }
        if (this.radio && this.useBookYear) {
          this.form.useBookYear = `${this.useBookYear}${this.radio}`
        }
        if ( !this.validateBookList()) {
          return 
        }
        this.form.distributionDetailList = this.getBookList()
        if (this.form.id) {
          // 编辑
          updatedeliver(this.form)
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '更新成功！',
                })
                this.$router.back()
              } else {
                this.$message({
                  type: 'error',
                  message: '保存失败！',
                })
              }
            })
            .catch((err) => {})
        } else {
          // 添加
          adddeliver(this.form)
            .then((result) => {
              if (result.data) {
                this.$message({
                  type: 'success',
                  message: '添加成功！',
                })
                this.$router.back()
              } else {
                this.$message({
                  type: 'error',
                  message: '保存失败！',
                })
              }
            })
            .catch((err) => {})
        }
      })
    },
    getBookList(){
      var list = []
      this.bookList.forEach(item => {
        list.push({
          "goodsId":item.goodsId,
          "name":item.name,
          "price":item.price,
          "number":item.number,
          "bookSpecialty":item.bookSpecialty
        })
      });
      return list
    },
    validateBookList(){
      var isValidate = true
      if (this.bookList.length==0) {
        isValidate = false
        this.$message.error(`请完善教材信息`)
      }else{
        for (let index = 0; index < this.bookList.length; index++) {
          const element = this.bookList[index];
          if (!element.id){
            this.$message.error(`请完善第${index+1}行的教材信息`)
            isValidate = false
            return
          }
          if (!element.bookSpecialty) {
            this.$message.error(`${element.name}的用书专业为空，请设置`)
            isValidate = false
            return 
          }
          if (!element.number) {
            this.$message.error(`${element.name}的数量为空，请设置`)
            isValidate = false
            return
          }
          
        }
      }
      
      return isValidate;
    },
    submitSuccess(list) {
      var fileList = Array()
      list.forEach((element) => {
        var fileData = {}
        var fileTypeName = getFileType(element.fileName)
        var type = this.fileType[fileTypeName]
        if (!type) {
          type = 4
        }
        fileData.fileName = element.fileName
        fileData.fileType = type
        fileData.url = element.url
        fileData.fileSize = element.fileSize
        fileList.push(fileData)
      })
      this.form.fileInfoList = fileList
    },

    lookBookInfo() {
      this.popoverVisibble = true
      queryBookInfo(this.form.materialId)
        .then((result) => {
          this.bookInfo = result.data
        })
        .catch((err) => {})
    },
    changeUseBookYear(data) {
      if (this.radio && this.useBookYear) {
        this.form.useBookYear = `${this.useBookYear}${this.radio}`
      }
    },
    updateUnit(data) {
      this.form.unitName = data.unitName
      this.form.receiveUnit = data.id
      this.$refs.addform.validateField('unitId')
      if (this.form.unitId) {
        this.loadMajor()
      }
    },
    chooseunit() {
      this.unitDialogVisible = true
      this.$refs.unitdialog.loadData(this.form.unitId)
    },
    updateUnitVisible(val) {
      this.unitDialogVisible = val
    },
  },
}
</script>
<style>
</style>
<style scoped>
.rbtn{
  cursor: pointer;
}
.pltcss2 {
  color: #4285f4;
  font-size: 13px;
  line-height: 28px;
  cursor: pointer;
}
.unitbtn2 {
  width: calc(100% - 50px);
  background-color: white;
  color: #333;
  position: relative;
  border: 1px solid #dcdfed;
  border-radius: 4px;
  line-height: 26px;
  height: 28px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.bottomView {
  text-align: center;
  padding-bottom: 10px;
}
.wi {
  width: 100% !important;
}
.wi /deep/.el-input__inner {
  text-align: left !important;
}
.matdetail p {
  line-height: 30px;
}
.matdetail {
  position: relative;
}
.tr {
  position: absolute;
  top: -30px;
  right: 0;
}
.wid100 {
  width: calc(100% - 130px);
  margin-right: 10px;
}
.wid2 {
  width: calc(100% - 100px);
}
.wid {
  width: 100px;
}
.tagcss {
  margin-right: 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  background: #dfeafd;
  border-radius: 2px 2px 2px 2px;
  opacity: 1;
}

.studiocss img {
  width: 16px;
  height: 16px;
  margin-right: 3px;
  margin-top: -3px;
}

.uploadtext {
  color: #4285f4;
  cursor: pointer;
}

.pltcss {
  color: #cdcdcd;
}

.unitbtn {
  color: #333333;
  position: relative;
}

.width100 {
  width: 100%;
}

.wid98 {
  width: 98px;
}

.rcenter {
  position: absolute;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}

.btncenter {
  text-align: center;
}

.quanxiancss /deep/.el-form-item__label {
  margin-left: -7px;
  width: 125px !important;
}

.mainbg {
  margin: 16px 0px;
  padding: 20px 16px;
  background-color: white;
  border-radius: 8px;
}

.pd0 {
  padding-right: 0px !important;
}
.flex {
  display: flex;
}
.flex span {
  margin-left: 10px;
}
</style>
<style>
</style>