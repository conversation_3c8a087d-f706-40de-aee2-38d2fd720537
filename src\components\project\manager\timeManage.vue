<template>
  <div class="mainbg fixpb">
    <back>返回</back>
    <el-form
      class="myform mt10 clearfix"
      inline
      @keyup.enter.native="taskListData()"
    >
      <el-form-item label="" v-if="isShowTask">
        <el-input
          clearable
          v-model="pageBean.taskName"
          class="definput"
          placeholder="请输入任务名称"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.createByName"
          class="definput"
          placeholder="请输入提交人"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <el-select
          v-model="pageBean.workType"
          placeholder="请选择工作产出"
          class="definput"
          clearable
        >
          <el-option
            v-for="item in workOutputTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          class="definput1"
          v-model="fftime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="">
        <el-button
          @click="search"
          class="defaultbtn mt"
          icon="el-icon-search"
          type="primary"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item label="" class="fr">
        <el-button
          class="defaultbtn mt"
          icon="el-icon-plus"
          type="primary"
          @click="openWorkRecordDialog"
        >
          工作记录</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="customertable mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        v-if="isShowTask"
        prop="taskName"
        label="任务"
        align="center"
      />
      <el-table-column
        prop="content"
        label="工作内容"
        show-overflow-tooltip
        align="center"
      />
      <el-table-column prop="workHours" label="工时（小时）" align="center" />
      <el-table-column label="工作产出" align="center">
        <template slot-scope="scope">
          {{ getWorkOutputLabel(scope.row.workType) }}
        </template>
      </el-table-column>
      <el-table-column label="产出数量" align="center">
        <template slot-scope="scope">
          {{
            scope.row.duration
              ? scope.row.duration + getWorkOutputUnit(scope.row.workType)
              : '--'
          }}
        </template>
      </el-table-column>
      <el-table-column prop="workTime" label="日期" align="center" />
      <el-table-column prop="createByName" label="提交人" align="center" />
      <el-table-column prop="createTime" label="提交时间" align="center" />
      <el-table-column label="操作" width="100" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            class="rbtn mr10"
            type="text"
            v-isShow="'crm:controller:taskworkhours:delete'"
            v-if="userid === scope.row.createBy || dataScope == '4'"
            @click="deleteRecord(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <!-- 工作记录对话框 -->
    <work-record-dialog
      ref="workRecordDialog"
      :visible.sync="workRecordDialogVisible"
      @submit="submitWorkRecord"
      :work-id="$route.query.workId"
    ></work-record-dialog>
    <!--  -->
    <msg-dialog ref="msgdialog" />
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import {
  workOutputType,
  workOutputTypeMap,
  workOutputUnitMap,
} from '@/utils/status-tool'
import {
  hourWorkList,
  taskworkhoursDelete,
  taskworkhoursSave,
} from '@/api/project/index'
import back from '@/components/common/back.vue'
import dcDialog from '@/components/common/dcDialog.vue'
import WorkRecordDialog from '../common/workRecordDialog.vue'
import DcDialog from '@/components/common/dcDialog.vue'
import MsgDialog from '@/components/common/msgDialog.vue'
export default {
  components: {
    page,
    nolist,
    back,
    WorkRecordDialog,
    MsgDialog,
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        taskName: '',
        createByName: '',
        workType: '',
        projectId: '',
        startTime: '',
        endTime: '',
      },
      workOutputTypeOptions: workOutputType,
      workRecordDialogVisible: false,
      fftime: [],
      userid: sessionStorage.getItem('userid'),
      dataScope: sessionStorage.getItem('dataScope'),
      isShowTask: this.$route.query.workId ? false : true,
    }
  },
  created() {
    this.pageBean.projectId = this.$route.query.id
    if (this.$route.query.workId) {
      this.pageBean.taskId = this.$route.query.workId
      this.pageBean.taskType = 2
    }
    this.taskListData()
  },
  methods: {
    search() {
      if (this.fftime && this.fftime.length > 0) {
        this.pageBean.startTime = this.fftime[0]
        this.pageBean.endTime = this.fftime[1]
      } else {
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
      this.pageBean.pageNum = 1
      this.taskListData()
    },
    taskListData() {
      this.isLoading = true
      hourWorkList(this.pageBean)
        .then((res) => {
          if (res.status == 0) {
            this.tableData = res.data
            this.total = res.page.total
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.taskListData()
    },
    getWorkOutputLabel(type) {
      return workOutputTypeMap[type] || ''
    },
    getWorkOutputUnit(type) {
      return workOutputUnitMap[type] || ''
    },
    openWorkRecordDialog() {
      this.workRecordDialogVisible = true
    },
    submitWorkRecord(formData) {
      taskworkhoursSave(formData).then((res) => {
        if (res.status == 0) {
          this.$nextTick(() => {
            this.$refs.workRecordDialog.resetForm()
            this.workRecordDialogVisible = false
          })
          this.$refs.msgdialog.show({
            type: 'success',
            title: '提交成功',
            msg: '您的工时已成功提交，感谢您的配合！',
          })
          this.taskListData()
        } else {
          this.$refs.msgdialog.show({
            type: 'error',
            title: '提交失败',
            msg: '您的工时提交未成功，请尝试重新提交。',
            onClose:()=>{
              this.$router.go(-1)
            }
          })
        }
      })
    },
    deleteRecord(row) {
      this.$confirm('是否确认删除该工时记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        taskworkhoursDelete({ id: row.id }).then((res) => {
          if (res.status == 0) {
            this.$message.success('删除成功')
            this.taskListData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
  },
}
</script>
<style scoped>
.el-icon--right {
  margin-left: 0px;
}
.overduecss {
  min-width: 50px !important;
  height: 24px;
  line-height: 24px;
  background: #fff5f6;
  border-radius: 12px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #f45961;
  text-align: center;
  margin-left: 10px;
}
.w150 {
  width: 150px;
}
.w110 {
  width: 110px;
}
.wid {
  width: calc(100% - 130px);
}
.mr20 {
  margin-right: 20px;
}
.w300 {
  width: 280px;
}
</style>
<style scoped lang="scss">
.mt10 {
  margin-top: 10px;
}
.mainbg {
  background-color: white;
  padding: 20px;
  min-height: 100%;
}
.fr {
  float: right;
}
.smt {
  margin-top: 19px;
}

.protext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #409eff;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  /* background-color: red; */
  display: inline-block;
  margin-top: 16px;
  vertical-align: top;
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
  color: #4285f4;
  line-height: 24px;
  cursor: pointer;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 5px;
  text-align: center;
}

.c1 {
  color: #4285f4;
}
.c2 {
  color: #ff8d1a;
}
.c3 {
  color: #56c36e;
}
.c4 {
  color: #f56c6c;
}
.c5 {
  color: #f56c6c;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.customertable /deep/.cell {
  padding: 8px 10px;
}

.customertable .el-button {
  padding: 2px;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}
</style>
