<!-- tab切换 -->
<template>
  <div class="base-tab">
    <ul>
      <li v-for="(item,index) in tabs" :key="index" @click="toggle(index)" :class="{ active:active==index }">{{ item.content }}</li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {
      active: 0,
      tabs: [
        { content: '全部' },
        { content: '已结算' },
        { content: '待结算' }
      ]
    }
  },
  methods: {
    toggle (obj) {
      this.active = obj
      obj = obj + 1
      this.$emit('vals', obj)
    }
  }
}
</script>

<style lang="scss" scoped>
  li {
    list-style: none;
  }
  .base-tab::after {
    content: '';
    display: block;
    clear: both;
    visibility: hidden;
  }
  .base-tab {
    border-bottom: 1px solid #E6E6E6;
    margin-bottom: 30px;
    ul li {
      float: left;
      padding: 10px 26px;
      font-size: 16px;
      color: #666;
      cursor: pointer;
    }
    .active {
      color: #EF5496;
      border: 1px solid #E6E6E6;
    }
  }
</style>
