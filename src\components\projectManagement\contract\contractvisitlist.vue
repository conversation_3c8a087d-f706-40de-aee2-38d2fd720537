<template>
  <div>
    <div>
      <back>合同跟进拜访详情</back>
    </div>
    <div class="card">
      <followvisit ref="cusvisit"></followvisit>
    </div>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import followvisit from './followvisit.vue'

export default {
  components: {
    back,
    followvisit,
  },
  data() {
    return {}
  },
  created() {},
  methods: {},
}
</script>
<style  scoped>
.ml5 {
  margin-left: 5px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.zhiding {
  position: absolute;
  top: 20px;
  right: 30px;
  width: 24px;
  height: 24px;
}

.dianbtn {
  position: absolute;
  right: 16px;
  top: 10px;
  width: 24px;
  height: 24px;
  color: #666666;
  letter-spacing: 3px;
  font-size: 18px;
  font-weight: bold;
  line-height: 24px;
  cursor: pointer;
}

.tabscss /deep/.el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 56px;
  line-height: 56px;
}

.tabscss /deep/.el-tabs__header {
  margin-bottom: 30px !important;
}

.cardcss {
  margin-bottom: 10px;
}

.el-col-5 {
  width: 20%;
}

.labeltext label {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
</style>

<style lang="scss" scoped>
.color-0 {
  color: #4a8bf6;
}

.color-1 {
  color: #56c36e;
}

.color-2 {
  color: #4a8bf6;
}

.color-3 {
  color: #4a8bf6;
}

.color-4 {
  color: #e85d5d;
}

.titletext {
  margin-bottom: 30px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.numtext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.numcolor {
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
}

.imgf {
  margin-left: 30px;
  margin-right: 20px;
  width: 48px;
  height: 48px;
}

.piececss {
  position: relative;
  height: 148px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  width: 100%;
  padding: 50px 0;
  box-sizing: border-box;
  box-shadow: 0px 2px 16px 0px rgba(15, 27, 50, 0.08);
}

.savecss {
  display: block;
  margin: 0 auto;
}

.mt20 {
  margin-top: 20px;
  width: 100%;
}

.card {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
  position: relative;
}
</style>