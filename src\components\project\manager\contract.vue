<template>
  <div class="pdfbg">
    <iframe  :style="{'height':`${height+100}px`,'width':'100%'}" v-if="eContractUrl" :src="`https://wthedu.51suiyixue.com/PDF/web/viewer.html?file=${eContractUrl}`" frameborder="0"></iframe>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  computed:{
    ...mapGetters(['eContractUrl'])
  },
  data(){
    return{
      height:0
    }
  },
  created(){
    var width = window.screen.availWidth;
    this.height = (width - 250)/0.75;
  }
}
</script>

<style scoped>
.pdfbg{
  text-align: center;
  /* margin-bottom: 0px !important; */
}
</style>