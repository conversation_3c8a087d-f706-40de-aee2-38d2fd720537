import service from '@/utils/request.js'

export function contractList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/contract/list',
    params,
  });
}


export function visitSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/visit/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function visitUpdate(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/visit/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function visitList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/visit/list',
    params,
  });
}

export function commentList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/visitcomment/list',
    params,
  });
}

export function stageSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/projectstage/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function taskSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/task/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function taskUpdate(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/task/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


export function taskCostSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskcost/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}



export function currencySave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/projectreturnrecord/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function taskHourSave(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskworkhours/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function stageUpdate(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/projectstage/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

export function stageInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/projectstage/info/${id}`,
  });
}


export function taskInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/task/info/${id}`,
  });
}

export function stageReturnInfo(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/projectreturnrecord/info/${id}`,
  });
}


export function taskInfoApi(id) {
  return service.request({
    method: 'get',
    url: `/crm/controller/task/info/${id}`,
  });
}


export function stageDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/projectstage/delete',
    data,
  });
}

export function taskDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/task/delete',
    data,
  });
}


export function returnDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/projectreturnrecord/delete',
    data,
  });
}


export function costList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/taskcost/list',
    params,
  });
}

export function returnList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/projectreturnrecord/list',
    params,
  });
}


export function taskworkhoursList(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/taskworkhours/list',
    params,
  });
}


export function queryTaskCost(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/taskcost/queryTaskCost',
    params,
  });
}


export function queryProjectReturnStatistics(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/projectreturnrecord/queryProjectReturnStatistics',
    params,
  });
}


export function queryTaskWorkHoursStatistics(params) {
  return service.request({
    method: 'get',
    url: '/crm/controller/taskworkhours/queryTaskWorkHoursStatistics',
    params,
  });
}

export function costDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskcost/delete',
    data,
  });
}

export function hourDelete(data) {
  return service.request({
    method: 'post',
    url: '/crm/controller/taskworkhours/delete',
    data,
  });
}

