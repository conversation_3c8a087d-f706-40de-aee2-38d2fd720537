<template>
  <el-dialog title="迁移部门" :close-on-click-modal="false" :visible.sync="visible" width="40%">
    <div>
      <div class="department">
        <div class="topTitle">
          <div class="line"></div>
          <div class="title">部门</div>
        </div>
        <div class="cflex" v-if="hasDepartment">
          <p class="roletitle">选择迁出部门</p>
          <div class="cf1">
            <el-select v-model="selectedDepartment" placeholder="请选择迁出部门" @focus="loadDepartmentList">
              <el-option 
                v-for="item in departmentList" 
                :key="item.id" 
                :label="item.name" 
                :value="item.id">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="cflex" style="margin-top: 15px;">
          <p class="roletitle">选择迁入部门</p>
          <div class="cf1">
            <el-select 
              v-model="migrateForm.departmentNames" 
              placeholder="请选择迁入部门" 
              @focus="chooseMigrateTarget" 
              clearable
              readonly>
              <el-option 
                v-if="migrateForm.departmentNames" 
                :value="migrateForm.departmentNames" 
                :label="migrateForm.departmentNames">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>

    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定
      </el-button>
    </span>

    <MigrateDepartmentDialog 
      ref="migrateDepRef" 
      :visible.sync="migrateDepartmentDialogVisible" 
      :current-department-id="departmentId"
      :current-parent-id="parentId"
      @updateVisible="updateMigrateDialogVisible"
      @submit="handleMigrateTargetSelected"
      @cancel="handleMigrateTargetCancel">
    </MigrateDepartmentDialog>
  </el-dialog>
</template>

<script>
  import { migrateDepartmentEmployees, queryUserDepartmentList } from '@/api/framework/user'
  import MigrateDepartmentDialog from '@/components/common/MigrateDepartmentDialog.vue'
  
  export default {
    components: {
      MigrateDepartmentDialog
    },
    data() {
      return {
        visible: false,
        userId: '',
        departmentList: [],
        selectedDepartment: '',
        selectedDepartmentInfo: null,
        migrateDepartmentDialogVisible: false,
        departmentId: '',
        parentId: '',
        migrateForm: {
          departmentId: '',
          targetDepartmentId: '',
          departmentNames: ''
        },
        selDepartmentData: [],
        hasDepartment: false,
        isNewEmployee: false
      }
    },
    methods: {
      init(id) {
        this.userId = id
        this.visible = true
        this.departmentId = ''
        this.parentId = ''
        this.selectedDepartment = ''
        this.selectedDepartmentInfo = null
        this.migrateForm.departmentNames = ''
        this.migrateForm.targetDepartmentId = ''
        
        queryUserDepartmentList({ userId: id }).then(res => {
          this.hasDepartment = res.data && res.data.length > 0
          this.isNewEmployee = !this.hasDepartment
        })
      },

      async dataFormSubmit() {
        if (this.hasDepartment && !this.selectedDepartment) {
          this.$message.error('请选择部门列表中的部门')
          return
        }

        if (!this.migrateForm.targetDepartmentId) {
          this.$message.error('请选择目标部门')
          return
        }

        let params = {
          id: this.userId,
          departmentId: this.selectedDepartment,
          newDepartmentId: this.migrateForm.targetDepartmentId.split(',')[0],
          isNew: this.isNewEmployee
        }

        let res = await migrateDepartmentEmployees(params)
        if (res.status == 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
          })
          this.visible = false
          this.$emit('refreshTagTypeTree')
        } else {
          this.$message.error(res.msg)
        }
      },
      loadDepartmentList() {
        queryUserDepartmentList({ userId: this.userId }).then(res => {
            this.departmentList = res.data
        })
      },
      watchSelectedDepartment(val) {
        if (val) {
          const dept = this.departmentList.find(item => item.id === val)
          if (dept) {
            this.selectedDepartmentInfo = dept
            this.departmentId = dept.id
            this.parentId = dept.parentId
            
            this.migrateForm.departmentNames = ''
            this.migrateForm.targetDepartmentId = ''
          }
        }
      },
      chooseMigrateTarget() {
        if (this.hasDepartment && !this.selectedDepartment) {
          this.$message.error('请先从选择迁出部门列表中选择一个部门')
          return
        }
        
        this.migrateDepartmentDialogVisible = true
        this.$refs.migrateDepRef.loadData()
        this.$refs.migrateDepRef.reset()
      },
      updateMigrateDialogVisible(value) {
        this.migrateDepartmentDialogVisible = value
      },
      handleMigrateTargetSelected(data) {
        this.migrateDepartmentDialogVisible = false
        this.selDepartmentData = data
        let departmentIds = data.map(item => item.id)
        let departmentNames = data.map(item => item.name)
        this.migrateForm.departmentNames = departmentNames.join(',')
        this.migrateForm.targetDepartmentId = departmentIds.join(',')
      },
      handleMigrateTargetCancel() {
        this.migrateDepartmentDialogVisible = false
      }
    },
    watch: {
      selectedDepartment(val) {
        this.watchSelectedDepartment(val)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .diycss {
    /deep/ .el-checkbox {
      margin-right: 20px;
      margin-bottom: 20px;
    }
  }

  .roletitle {
    text-align: right;
    min-width: 80px;
    margin-right: 10px;
  }
  .topTitle{
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
    background: #f0f3fa;
    border-radius: 4px;
    padding: 0 16px;
    margin-bottom: 20px;
  }
  .line{
    width: 3px;
    height: 14px;
    background-color: #386cfc;
    margin-right: 12px;
  }
  .title{
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2d2f33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .cflex {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  .cf1 {
    flex: 1;
  }
</style>