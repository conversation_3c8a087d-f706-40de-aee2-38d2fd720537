<template>
  <div class="mainbg">
      <modelCom :typeModel="typeModel"></modelCom>
      <!-- <canvas class="can"  width="544" height="544"></canvas> -->
  </div>
</template>
<script>
import modelCom from './modellistcom.vue';
  export default {
      components: {
        modelCom,
      },
      data() {
          return {
            typeModel:'1'
          }
      },
      created() {
        
      },
      mounted() {
    //         // cnavas画块
    //    let oCanvas = document.querySelector("canvas");
    //  // 2.从canvas中拿到绘图工具
    //     let oCtx = oCanvas.getContext("2d");
    //     //绘制个数
    //     let len = 10
    //     //每个扇形占的份额,一个圆的总额是2，看下图
    //     let portion = 0.5
    //     console.log(portion)
    //           //绘制的起点
    //           oCtx.moveTo(272, 272);
    //           //arc的4个参数分别是（x,y,起始角度,结束角度）
    //           oCtx.arc(272, 272, 272,1.5 * Math.PI, 2 * Math.PI);
    //           //关闭路径
    //           oCtx.closePath();
    //           //填充颜色
    //           oCtx.fillStyle = '#c59696'
    //           //绘制
    //           oCtx.fill();

      },
      methods: {
   
      }
  }
</script>
<style scoped>
  .can{
    border-radius: 50%;
    border: 1px solid red;
    background: #fff;
  }
  .mainbg{
    transform: none;
  }
</style>