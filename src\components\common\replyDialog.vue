<template>
    <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="500px"
    append-to-body
    :before-close="closeAction">
        <el-input
        type="textarea"
        :rows="5"
        placeholder="请输入回复内容"
        v-model.trim="textarea"
        show-word-limit
        :maxlength="400">
        </el-input>
        <span slot="footer" class="dialog-footer">
            <el-button @click="closeAction">取 消</el-button>
            <el-button type="primary" @click="submitAction" v-dbClick>确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    props:{
        visible:{
            type:Boolean,
            default:false
        },
        title:{
            type:String,
            default:''
        }
    },
    computed:{
        dialogVisible:{
            get(){
                return this.visible;
            },
            set(val){
                this.$emit('updateVisible',val);
            },
        }
    },
    data(){
        return{
            textarea:''
        }
    },
    methods:{
        closeAction(){
            this.textarea = ''
            this.dialogVisible = false;
        },
        submitAction(){
            this.$emit('replyData',this.textarea)
        },
    }
}
</script>

<style>

</style>