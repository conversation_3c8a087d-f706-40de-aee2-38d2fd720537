<template>
  <div class="drawer">
    <el-drawer class="ed" center size="50%" title="项目详情" :visible.sync="drawer_" :direction="direction"
      @close="handleClose">
      <el-tabs class="edt" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="项目详情" name="first">
          <bacisInfo :form="form" v-on="$listeners" :proId="proId"></bacisInfo>
        </el-tab-pane>
        <el-tab-pane label="项目留言板" name="second">
          <pcommont ref="commontRef" :proId="proId"></pcommont>
        </el-tab-pane>
        <el-tab-pane label="项目日志" name="third">
          <prolog ref="logRef" :proId="proId"></prolog>
        </el-tab-pane>
        <el-tab-pane label="项目合同" name="fourth">
          <contract :form="form"></contract>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
  import bacisInfo from './basicInfo.vue'
  import pcommont from './pcommont.vue'
  import prolog from './prolog.vue'
  import contract from './contract.vue'
  import { proInfo } from '@/api/project/index'
  export default {
    props: {
      drawer: {
        type: Boolean,
        default: false,
      },
      direction: {
        type: String,
        default: 'rtl',
      },
    },
    components: {
      bacisInfo,
      pcommont,
      prolog,
      contract
    },
    data() {
      return {
        activeName: 'first',
        proId: '',
        form: {},
      }
    },
    computed: {
      drawer_: {
        get() {
          return this.drawer
        },
        set(v) {
          //   console.log(v, 'v')
          //   this.$emit('changeDrawer', v)
        },
      },
    },
    methods: {
      proInfoData() {
        proInfo(this.proId).then(res => {
          if (res.status == 0) {
            this.$emit('changeDrawer', true)
            this.form = res.data
          }
        })
      },
      handleClick(tab, event) {
        if (tab.name == 'second') {
          this.$refs.commontRef.commentListData()
        }
        if (tab.name == 'third') {
          this.$refs.logRef.logListData()
        }
      },
      handleClose() {
        this.activeName = 'first'
        this.$emit('changeDrawer', false)
      },
      setProId(proId) {
        this.proId = proId
        this.proInfoData()
      }
    },
  }
</script>

<style lang="scss" scoped>
  .drawer {
    /deep/.el-drawer {
      padding: 20px;
      padding-top: 0;
    }

    /deep/ .el-drawer__header {
      padding-left: 0;
      text-align: center;
    }
  }
</style>>