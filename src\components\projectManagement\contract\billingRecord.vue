<template>
  <div>
    <div class="mb20">
      <span class="ttext deffont"><span class="deffont mr10"></span></span>
      <div class="right flex">
        <el-button
          class="defaultbtn"
          icon="el-icon-my-billing"
          type="primary"
          @click="addAction"
          >开票</el-button
        >
      </div>
    </div>
    <el-table
      class="mytable"
      :height="600"
      :data="dataList"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        property="invoicingAmount"
        label="开票金额（万元）"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="invoicingStatus"
        label="开票状态"
        width="150px"
        align="center"
      >
        <template slot-scope="scope">
          <span
            class="circle"
            :class="{
              'c-five': scope.row.status == 5,
              'c-four': scope.row.status == 4,
              'c-three': scope.row.status == 3,
              'c-two': scope.row.status == 2,
              'c-one': scope.row.status == 1,
            }"
          ></span>
          {{ commonStatusMap[scope.row.status] }}
        </template>
      </el-table-column>

      <el-table-column
        property="createByName"
        label="申请人"
        align="center"
      ></el-table-column>
      <el-table-column
        property="invoicingDepartmentName"
        label="申请人部门"
        align="center"
      ></el-table-column>
      <el-table-column
        property="invoicingDate"
        label="开票日期"
        align="center"
      ></el-table-column>
      <el-table-column
        property="createTime"
        label="申请时间"
        align="center"
      ></el-table-column>
      <el-table-column property="edit" label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            class="deffont bbtn"
            type="text"
            @click="lookAction(scope.row)"
            >详情</el-button
          >
          <el-button
            class="deffont bbtn"
            type="text"
            @click="editAction(scope.row)"
            v-if="
              (scope.row.status == 4 || scope.row.status == 5) &&
              userId == scope.row.createBy
            "
            >编辑</el-button
          >
          <el-button
            class="deffont bbtn"
            type="text"
            v-if="scope.row.status == 1 && userId == scope.row.createBy"
            @click="revokeAction(scope.row)"
            >撤销</el-button
          >
          <el-button
            v-isShow="'crm:controller:contractinvoicing:delete'"
            class="deffont rbtn"
            type="text"
            @click="deleteAction(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <img
          class="nodataimgcss"
          src="../../../assets/img/nodata_icon.png"
          alt=""
          srcset=""
        />
        <p>暂无数据～</p>
      </template>
    </el-table>
    <page
      :currentPage="pageBean.pageNum"
      :pageSize="pageBean.pageSize"
      :total="total"
      @updatePageNum="handleCurrentChange"
    ></page>
    <billingDialog
      ref="billingDialog"
      :visible.sync="dialogVisible"
      :contractId="pageBean.contractId"
      :isEdit="isEdit"
      :editData="currentEditData"
      @updateVisible="updateVisible"
      @submitSuccess="submitSuccess"
    >
    </billingDialog>
    <billingDetailDrawer
      ref="billingDetailDrawer"
      :visible.sync="detailVisible"
      @updateVisible="updateDetailVisible"
    >
    </billingDetailDrawer>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import billingDialog from './components/billingDialog.vue'
import billingDetailDrawer from './components/billingDetailDrawer.vue'
import {
  contractinvoicingList,
  deleteContractinvoicing,
  updateContractinvoicing,
} from '@/api/contract'
import { mapGetters } from 'vuex'
import { commonStatusMap } from '@/utils/status-tool'

export default {
  computed: {
    ...mapGetters(['contractAmount']),
  },
  components: {
    page,
    billingDialog,
    billingDetailDrawer,
  },
  data() {
    return {
      commonStatusMap,
      detailVisible: false,
      dialogVisible: false,
      isLoading: false,
      dataList: [],
      pageBean: {
        contractId: this.$route.query.id,
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      isEdit: false,
      currentEditData: {},
      userId: sessionStorage.getItem('userid'),
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.isLoading = true
      contractinvoicingList(this.pageBean)
        .then((result) => {
          this.dataList = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    lookAction(data) {
      this.detailVisible = true
      this.$refs.billingDetailDrawer.loadData(data.id,data.status)
    },
    updateDetailVisible(data) {
      this.detailVisible = data
    },
    // 编辑
    editAction(data) {
      this.isEdit = true
      this.currentEditData = data
      this.dialogVisible = true
      setTimeout(() => {
        this.$refs.billingDialog.loadDetailData(data.id)
      }, 100)
    },
    // 撤销
    revokeAction(data) {
      this.$confirm('是否撤销开票申请？撤销后该申请可以再次提交', '提示', {
        confirmButtonText: '撤销',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.isLoading = true
        updateContractinvoicing({ id: data.id, status: 5 })
          .then((result) => {
            if (result.data) {
              this.$message({
                type: 'success',
                message: '撤销成功',
              })
              this.loadData()
            } else {
              this.$message({
                type: 'error',
                message: result.msg,
              })
              this.isLoading = false
            }
          })
          .catch((err1) => {
            this.isLoading = false
          })
      })
    },
    deleteAction(data) {
      this.isLoading = true
      deleteContractinvoicing({ id: data.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
            this.isLoading = false
          }
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    addAction() {
      this.isEdit = false
      this.currentEditData = {}
      this.dialogVisible = true
      this.$refs.billingDialog.setContractAmount(this.contractAmount)
    },
    updateVisible(val) {
      this.dialogVisible = val
    },
    submitSuccess() {
      // 注入合同id
      this.updateVisible(false)
      this.pageBean.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>

<style scoped>
.c-one {
  background: rgb(50, 145, 248);
}
.c-two {
  background: rgb(255, 153, 89);
}
.c-three {
  background: rgb(94, 188, 100);
}
.c-four {
  background: rgb(255, 37, 37);
}
.c-five {
  background: rgb(252, 202, 0);
}

.nodataimgcss {
  margin-bottom: 12px;
  width: 180px;
  height: 92px;
}
.mytable /deep/.el-table__empty-text {
  line-height: 20px !important;
  min-height: 20px !important;
}

.submitbtn {
  margin-top: 50px;
}

.flex {
  display: flex;
  align-items: center;
}

.lh {
  width: 100%;
  line-height: 23px;
}
.ttext {
  color: #999999;
  line-height: 34px;
}
.center {
  text-align: center;
}

.ml20 {
  margin-left: 20px;
}
</style>
<style scoped>
</style>
