<template>
    <div class="dataitem">
        <div class="typename">{{item.name}}</div>
        <div class="flex">
        <img class="imgcss" :src="item.icon" alt="">
        <div class="pl12">
            <div >
            <span class="curnumcss" :style="{color:item.color}">{{item.finishGoal}}</span>
            <span class="goalnumcss ml">{{item.unit}}</span></div>
            <div class="goalnumcss">目标：{{item.goal}}{{item.unit}}</div>
        </div>
        </div>
    </div>
</template>

<script>
export default {
    props:{
        item:{
            type:Object,
            default:()=>{}
        }
    }
}
</script>

<style scoped>
.ml{
  margin-left: 3px;
}
.goalnumcss{
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 16px;
}
.curnumcss{
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
  color: #F46D40;
  line-height: 28px;
}
.pl12{
  padding-left: 12px;
}
.flex{
  display: flex;
  align-items: center;
}
.imgcss{
  width: 3em;
  height: 3em;
  margin-left: 1.5vw;
}
.typename{
  padding-top: 20px;
  margin-left: 20px;
  margin-bottom: 1.25em;
  font-size: 1em;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}
.dataitem{
  margin-top: 16px;
  height: 9.375em;
  background: #FFFFFF;
  box-shadow: 0px 2px 16px 0px rgba(15,27,50,0.08);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}
</style>