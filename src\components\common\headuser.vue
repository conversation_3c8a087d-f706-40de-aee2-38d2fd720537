<template>
  <span class="headdiv">
      <img class="imgcss" :style="{width:width+'px',height:width+'px',borderRadius:borderRadius}" v-if="url" :src="url" alt="">
      <div v-else class="ddiv" :style="{width:width+'px',height:width+'px',lineHeight:width+'px',borderRadius:borderRadius}" :class="{'maxSize':width>40}">
          {{ strName() }}
      </div>
  </span>
</template>

<script>
  export default {
        name:'headuser',
        props:{
          width:{
            type:String,
            default:'40'
          },
          url:{
            type:String,
            default:'',
          },
          borderRadius:{
            type:String,
            default:'50%'
          },
          username:{
            type:String,
            default:''
          }
        },
        methods:{
           strName(){
                if(this.username.length>2){
                     let name  = this.username.substring(1,3)
                     return name
                }else{
                  return this.username
                }
           }
        }
  }
</script>

<style lang="scss" scoped>
.ddiv{
  border-radius: 50%;
  display: inline-block;
  vertical-align: middle;
  background-color: #4285F4;
  text-align: center;
  color: #fff;
  font-size: 12px;
}
.headdiv{
  margin-right: 10px;
  float: left;
  display: flex;
}
.imgcss{
  border-radius: 50%;
}
.maxSize{
  font-size: 14px;
}

</style>