<template>
  <div class="login_container">
    <div class="bgcolor">
      <img src="../assets/loginbg.png" alt />
    </div>
    <div class="login_box">
      <div class="avatar_box">
        <img src="../assets/bg.png" alt />
      </div>
      <!-- 登录表单 -->
      <h1>后台管理系统</h1>
      <div class="loginCon">
        <div class="weclom">
          <div class="blueLine"></div>
          <div class="loginText">欢迎登录</div>
        </div>
        <el-form ref="loginFromRef" :model="loginForm" :rules="loginFormRules" class="login_form" @keyup.enter.native="login">
          <div>
            <label slot="label">用户账号</label>
            <div class="clearfix loginAll">
              <div class="leftdiv">
                <img class="leftIcon" src="../assets/account.png" alt="" />
              </div>
              <!-- 用户名 -->
              <el-form-item prop="account">
                <el-input v-model.trim="loginForm.username" placeholder="请输入账号名称" class="loginInput"></el-input>
              </el-form-item>
            </div>
          </div>
          <div>
            <label slot="label">用户密码</label>
            <div class="clearfix loginAll">
              <div class="leftdiv">
                <img class="leftIcon" src="../assets/pass.png" alt="" />
              </div>
              <!-- 密码 -->
              <el-form-item prop="password">
                <el-input v-model.trim="loginForm.password" type="password" placeholder="请输入账号密码"
                  class="loginInput"></el-input>
              </el-form-item>
            </div>
          </div>

          <!-- 按钮区域 -->
          <el-form-item class="btns">
            <el-button class="loginButton" type="primary" @click="login">登录</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import { api } from '../assets/api/axios'
import { log } from 'util'
import { getPermissions, getTitle } from '@/api/user'
export default {
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
      },
      loginFormRules: {
        username: [
          { required: true, message: '请输入登录名称', trigger: 'blur' },
          {
            min: 1,
            max: 10,
            message: '长度在 1 到 10 个字符',
            trigger: 'blur',
          },
        ],
        password: [
          { required: true, message: '请输入登录密码', trigger: 'blur' },
          {
            min: 6,
            max: 15,
            message: '长度在 6 到 15 个字符',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  created(){
    this.$store.commit('permission/SET_LOOKROUTERS', [])
  },
  methods: {
    resetLoginForm() {
      this.$refs.loginFromRef.resetFields()
      this.account = ''
      this.password = ''
    },
    async login() {
      this.$refs.loginFromRef.validate(async (valid) => {
        if (!valid) return
        this.$store
          .dispatch('user/login', this.loginForm)
          .then((res) => {
            // getPermissions().then((res) => {
            //   let path = ''
            //   if (
            //     res.data.menuList[0] &&
            //     res.data.menuList[0].children.length > 0
            //   ) {
            //     path = res.data.menuList[0].children[0].sUrl
            //   } else {
            //     path =
            //       (res.data.menuList[0] && res.data.menuList[0].sUrl) || '/401'
            //   }
            //   localStorage.setItem('firstPath', path.split(',')[0])
            //   console.log('登录后第一个path', path.split(',')[0])
            //   this.$router.push({ path: path.split(',')[0] })
            // })
            // this.loading = false
            window.sessionStorage.setItem('vue_admin_template_token', res.data.token)
            sessionStorage.setItem('applicationName',res.data.applicationName)
            sessionStorage.setItem('applicationId',res.data.applicationId)
            window.sessionStorage.setItem('username', res.data.name)
            sessionStorage.setItem('logo',res.data.logo)
            sessionStorage.setItem('userid',res.data.userId)
            sessionStorage.setItem('dataScope',res.data.dataScope)
            sessionStorage.setItem('departmentIds',res.data.departmentIds.join(','));
            console.log("部门id",res.data.departmentIds.join(','));
            this.$router.push('/home')
          })
          .catch(() => {
            this.loading = false
          })
        // let res = await api.post('sysLogin/doLogin.do', this.loginForm)
        // if (res.code !== 1000) return this.$message.error('登录失败')
        // this.$message.success('登录成功')
        // window.sessionStorage.setItem('pressId', res.data.user.pressId)
        // window.sessionStorage.setItem('typeFlag', res.data.user.typeFlag)
        // window.sessionStorage.setItem('user', JSON.stringify(res.data.user))
      })
    },
  },
}
</script>
<style >
.loginInput .el-input__inner {
  border: none !important;
}
</style>
<style scoped>
.bgcolor {
  width: 1920px;
  height: 620px;
  /* background: #054470; */
}

.loginAll {
  border: 1px solid #e5e5e5;
  margin-top: 18px;
  margin-bottom: 23px;
}

.clearfix::after {
  content: '';
  height: 0;
  line-height: 0;
  display: block;
  visibility: hidden;
  clear: both;
}

.leftIcon {}

.leftdiv {
  float: left;
  width: 43px;
  height: 43px;
  text-align: center;
  background: #f8f8f8;
}

.leftdiv img {
  width: 22px;
  height: 22px;
  margin-top: 10px;
}

.el-button--primary,
.el-button--primary:focus,
.el-button--primary.is-active,
.el-button--primary:active {
  background: #064470;
}

.el-button--primary:hover {
  background: #064470;
  border-color: #064470;
  color: #fff;
}

.loginButton {
  width: 338px;
  height: 44px;
  background: #064470;
  border-radius: 4px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
  margin-top: 40px;
}

.loginText {
  font-size: 24px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #080808;
}

.weclom {
  margin-bottom: 24px;
}

.blueLine {
  width: 7px;
  height: 21px;
  background: #080808;
  border-radius: 2px;
  float: left;
  margin-right: 8px;
  margin-top: 6px;
}

h1 {
  font-size: 55px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #f1f7fb;
  padding: 0;
  margin: 0;
  text-align: center;
  margin-bottom: 27px;
}

.login_container {
  /* background-color: #2b4b6b; */
  background-color: #fff;
  height: 100%;
}

.loginCon {
  width: 417px;
  padding: 0 40px;
  height: 406px;
  padding-top: 43px;
  background: #ffffff;
  box-shadow: 0px 6px 20px 0px rgba(0, 23, 39, 0.07);
}

.login_box {
  border-radius: 3px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  /* background-color: pink; */
}

.avatar_box {
  width: 620px;
  height: 96px;
  position: absolute;
  left: 50%;
  top: 542px;
  z-index: -1;
  transform: translate(-50%, -50%);
}

.avatar_box img {
  width: 620px;
}

/* .avatar_box{
    height: 130px;
    width: 130px;
    border: 1px solid #eee;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    transform: translate(-50%,-50%);
    background-color: #fff;
    padding: 10px;
}
.avatar_box img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #eee;
} */
.el-form-item {
  margin-bottom: 0px;
  float: left;
}

.login_form {
  position: absolute;
  width: 337px;
  box-sizing: border-box;
}

.btns {
  display: flex;
  justify-content: flex-end;
  /* 主轴结尾位置 */
}
</style>
