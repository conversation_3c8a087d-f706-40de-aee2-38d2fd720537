<template>
  <div class="mainbg fixpb" v-loading="isLoading">
    <div class="page-header">
      <el-form
        class="myform clearfix"
        ref="form"
        :model="pageBean"
        :inline="true"
      >
        <el-form-item>
          <el-input
            v-model="pageBean.unitName"
            class="definput inputWid150"
            clearable
            placeholder="单位名称"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="pageBean.chargePersonName"
            class="definput inputWid150"
            clearable
            placeholder="单位负责人"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="pageBean.chargePersonDepartment"
            class="definput inputWid150"
            clearable
            placeholder="负责人部门"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="pageBean.createByName"
            class="definput inputWid150"
            clearable
            placeholder="创建人"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="pageBean.createByDepartment"
            class="definput inputWid150"
            clearable
            placeholder="创建人部门"
          ></el-input>
        </el-form-item>
        <el-form-item label="来源：">
          <el-select
            clearable=""
            class="definput width"
            popper-class="removescrollbar"
            v-model="pageBean.source"
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单位类型：">
          <el-select
            @change="changeType"
            clearable=""
            class="definput width"
            popper-class="removescrollbar"
            v-model="pageBean.unitType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in unitTypeData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="特点：">
          <el-select
            clearable=""
            class="definput w150"
            popper-class="removescrollbar"
            v-model="pageBean.unitCharacter"
            placeholder="请选择单位特点"
          >
            <el-option
              v-for="item in characterData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间：">
          <datepicker @submitAction="submitAction"></datepicker>
        </el-form-item>

        <el-form-item label="单位地址:" prop="addressData">
          <el-cascader
            clearable=""
            v-model="addressData"
            class="definput unitf"
            :options="optionsAddress"
            :props="{
              value: 'id',
              label: 'name',
              children: 'areaList',
              checkStrictly: true,
            }"
          ></el-cascader>
        </el-form-item>

        <el-form-item label="">
          <el-button
            class="defaultbtn"
            type="primary"
            icon="el-icon-search"
            @click="onSearch"
            >查询</el-button
          >
        </el-form-item>
        <el-form-item class="defr" v-if="!isBatchMode">
          <el-button
            v-isShow="'crm:controller:unit:save'"
            class="defaultbtn"
            type="primary"
            icon="el-icon-plus"
            @click="addUnit"
            >新建单位</el-button
          >
        </el-form-item>
        <el-form-item class="defr" v-if="!isBatchMode">
          <el-button
            class="defaultbtn"
            icon="el-icon-my-download"
            type="primary"
            :loading="isDownload"
            @click="downloadAction"
            >下载模版</el-button
          >
        </el-form-item>
        <el-form-item class="defr" v-if="!isBatchMode">
          <el-upload
            class="ml20"
            :action="getUrl"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="handleAvatarSuccess"
            :on-error="handleError"
            :headers="headers"
            :data="fileData"
            accept=".xlsx"
          >
            <el-button
              class="defaultbtn"
              icon="el-icon-my-inport"
              type="primary"
              :loading="isImport"
              >导入单位</el-button
            >
          </el-upload>
        </el-form-item>
        <el-form-item class="defr" v-if="!isBatchMode">
          <el-button
            v-isShow="'crm:controller:unit:exportExcel'"
            class="defaultbtn"
            icon="el-icon-my-download"
            type="primary"
            :loading="isDownload"
            @click="handleUnitExport"
            >单位导出</el-button
          >
        </el-form-item>
        <!-- 批量处理模式下的按钮 -->
        <el-form-item class="defr" v-if="isBatchMode">
          <el-button
            class="defaultbtn"
            @click="cancelBatch"
            >取消批量处理</el-button
          >
        </el-form-item>
        <el-form-item class="defr" v-if="isBatchMode">
          <el-button
            class="defaultbtn"
            type="primary"
            :disabled="selectedUnits.length === 0"
            @click="assignResponsible"
            >分配负责人</el-button
          >
        </el-form-item>
        <!-- 正常模式下的按钮 -->
        <el-form-item class="defr" v-if="!isBatchMode">
          <el-button
            v-isShow="'crm:controller:unit:insertBatchChargePerson'"
            class="defaultbtn"
            type="primary"
            @click="handleBatch"
            >批量处理</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-table
      ref="unitTable"
      :key="tableKey"
      row-key="id"
      @sort-change="sortByColumn"
      @selection-change="handleSelectionChange"
      class="unittable mytable"
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column
        v-if="isBatchMode"
        type="selection"
        width="55"
        align="center">
      </el-table-column>
      <el-table-column
        prop="unitName"
        label="单位名称"
        align="center"
        min-width="200px"
      >
      </el-table-column>
      <el-table-column
        sortable="custom"
        prop="customerNumber"
        label="下属客户数量"
        align="center"
        min-width="140px"
      >
        <template slot-scope="scope">
          <span class="d_color defbtn" @click="showCustomer(scope.row)">{{
            scope.row.customerNumber
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="unitTypeName"
        label="单位类型"
        align="center"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="unitCharacterName"
        label="单位特点"
        align="center"
        min-width="150px"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        prop="customerNumber"
        label="来源"
        align="center"
        min-width="100px"
      >
        <template slot-scope="scope">
          {{ sourceName[scope.row.source] }}
        </template>
      </el-table-column>
      <el-table-column
        prop="provinceName"
        label="省"
        align="center"
        min-width="150px"
      >
      </el-table-column>
      <el-table-column
        prop="cityName"
        label="市"
        align="center"
        min-width="150px"
      >
        <template slot-scope="scope">
          {{ scope.row.cityName ? scope.row.cityName : '--' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="phone"
        align="center"
        min-width="130px"
        label="联系电话"
        :formatter="valueFormatter"
      >
      </el-table-column>
      <el-table-column
        align="center"
        min-width="160px"
        class-name="chargePersoncss"
        label="单位负责人"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.chargePersonNames.length > 0">
            <el-tag
              class="cuscss"
              v-for="(item, index) in scope.row.chargePersonNames"
              :key="index"
              >{{ item }}</el-tag
            >
          </div>
          <div v-else>—</div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        min-width="160px"
        class-name="chargePersoncss"
        label="负责人部门"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.chargePersonDepartments.length > 0">
            <span
              class="cpdepartmentcss"
              v-for="(item, index) in scope.row.chargePersonDepartments"
              :key="index"
              >{{
                index == scope.row.chargePersonDepartments.length - 1
                  ? item
                  : `${item},`
              }}</span
            >
          </div>
          <div v-else>—</div>
        </template>
      </el-table-column>

      <el-table-column
        prop="createByName"
        align="center"
        min-width="130px"
        label="创建人"
      >
      </el-table-column>
      <el-table-column
        prop="createByDepartment"
        align="center"
        min-width="130px"
        label="创建人部门"
      >
        <template slot-scope="scope">
          <span class="cpdepartmentcss">{{
            scope.row.createByDepartment
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!isBatchMode"
        prop="edit"
        width="200"
        align="center"
        fixed="right"
        label="更多操作"
      >
        <template slot-scope="scope">
          <el-dropdown
            placement="bottom-end"
            @command="(e) => handleCommand(e, scope.row)"
            trigger="click"
          >
            <el-button class="mbtn tabbtn" type="text"> 更多 </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-isShow="'crm:controller:unit:info'"
                command="1"
                >详情</el-dropdown-item
              >
              <el-dropdown-item command="0">结构管理</el-dropdown-item>
              <el-dropdown-item v-isShow="'open'" command="2"
                >开设专业</el-dropdown-item
              >
              <el-dropdown-item v-isShow="'zhao'" command="3"
                >招生数据</el-dropdown-item
              >
              <el-dropdown-item
                v-isShow="'crm:controller:customer:save'"
                command="5"
                >新增客户</el-dropdown-item
              >
              <!-- <el-dropdown-item command="4">单位数据</el-dropdown-item> -->
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            :disabled="getDisabled(scope.row)"
            class="bbtn tabbtn"
            type="text"
            @click="onEdit(scope.row)"
          >
            编辑</el-button
          >
          <el-button
            :disabled="getDisabled(scope.row)"
            class="rbtn tabbtn"
            type="text"
            @click="deleteAction(scope.row)"
          >
            删除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <dc-dialog
      :iType="giveType"
      title="提示"
      width="500px"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <template> </template>
      <p class="pcc">{{ giveMessage }}</p>
    </dc-dialog>
    <router-view></router-view>
    <unitcustomer
      ref="unitcustomer"
      :unitName="unitName"
      :visible="dialogCusVisible"
      @updateVisible="updateUnitCusVisible"
    ></unitcustomer>
    <dc-dialog
      iType="2"
      title="导入信息提示"
      width="500px"
      :showCancel="false"
      :dialogVisible.sync="dialogMsgVisible"
      @submit="submitMsgDialog"
      :appendToBody="true"
    >
      <template>
        <p>导入成功：{{ importData.successCount }} 条</p>
        <p>导入失败：{{ importData.errorCount }} 条</p>
        <p
          class="pcc"
          v-for="(item, index) in importData.errorData"
          :key="index"
        >
          {{ item }}
        </p>
      </template>
    </dc-dialog>

    <!-- 分配负责人 -->
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="assignDialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </systemDialog>
    <msg-dialog ref="msgdialog" />
  </div>
</template>

<script>
import page from '../../common/page.vue'
import dcDialog from '../../common/dcDialog.vue'
import nolist from '../../common/nolist.vue'
import systemDialog from '../../common/systemDialog.vue'
import {
  listUnit,
  deleteUnit,
  unitType,
  queryUnitCharacter,
  unitDownLoad,
  unitExport,
  insertBatchChargePerson,
} from '@/api/clientmanagement/unit'
import { getDict } from '@/utils/tools'
import unitcustomer from './components/unitcustomer.vue'
import datepicker from '@/components/common/datepicker'
import {
  getParStr,
  downloadExcelFile,
  downloadExcelFileCommon,
} from '@/utils/tools'
import { getToken } from '@/utils/auth'

import { queryAreaVoList } from '@/api/index'
import { title } from '@/settings'
export default {
  components: {
    page,
    dcDialog,
    nolist,
    unitcustomer,
    datepicker,
    systemDialog,
  },
  data() {
    return {
      optionsAddress: [],
      level: sessionStorage.getItem('dataScope'),
      userId: sessionStorage.getItem('userid'),
      dialogCusVisible: false,
      chooseUnitId: '',
      giveType: 1,
      giveMessage: '',
      isLoading: false,
      dialogVisible: false,
      options: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '公司资源',
          value: '1',
        },
        {
          label: '自主开拓',
          value: '2',
        },
      ],
      sourceName: {
        1: '公司资源',
        2: '自主开拓',
      },
      deleteData: {},
      tableData: [],
      total: 0,
      dateRange: [],
      pageBean: {
        source: '',
        unitName: '',
        chargePersonName: '',
        chargePersonDepartment: '',
        createByName: '',
        createByDepartment: '',
        startTime: '',
        endTime: '',
        time: '',
        pageNum: 1,
        pageSize: 10,
        unitType: '',
        unitCharacter: '',
        isDeleted: 0,
        provinceId: '',
        cityId: '',
        // customerNumberSort: '',
      },
      addressData: [],
      unitName: '',
      unitTypeData: [],
      characterData: [],
      getUrl: `${process.env.VUE_APP_BASE_API}/crm/controller/unit/unitImportExcel`,
      headers: { Authorization: getToken() },
      dialogMsgVisible: false,
      importData: {
        successCount: 0,
        errorCount: 0,
        errorData: [],
      },
      isDownload: false,
      isImport: false,
      fileData: {
        applicationId: sessionStorage.getItem('applicationId'),
        serviceName: 'crm/unit/excel',
      },
      orderToNumber: {
        ascending: 1,
        descending: 2,
      },
      globalPageNum: 1,
      // 批量处理相关状态
      isBatchMode: false,
      selectedUnits: [],
      tableKey: 0,
      assignDialogVisible: false,
      dialogName: '',
      multipleNum: 0,
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
      if (this.$route.query.provinceId) {
        this.addressData[0] = this.$route.query.provinceId
      }
      if (this.$route.query.cityId) {
        this.addressData[1] = this.$route.query.cityId
      }
    }
    this.loadData()
    getDict('UnitType').then((result) => {
      this.unitTypeData = result
    })
    this.queryAreaVoListApi()
  },
  methods: {
    handleTreeList(list) {
      // 删除第三级children
      for (var i = 0; i < list.length; i++) {
        if (list[i].areaList.length < 1) {
          // 判断children的数组长度
          list[i].areaList = undefined
        } else {
          this.handleTreeList(list[i].areaList)
        }
      }
      return list
    },
    queryAreaVoListApi() {
      queryAreaVoList({ level: 0 }).then((res) => {
        if (res.status == 0) {
          this.optionsAddress = this.handleTreeList(res.data)
        }
      })
    },
    sortByColumn(column) {
      this.pageBean.customerNumberSort = this.orderToNumber[column.order]
      this.pageBean.pageNum = 1
      this.loadData()
    },
    showCustomer(data) {
      if (data.customerNumber <= 0) {
        this.$message({
          type: 'info',
          message: '当前单位无下属客户',
        })
        return
      }
      this.dialogCusVisible = true
      this.unitName = data.unitName
      this.$refs.unitcustomer.loadData(data.id)
    },
    updateUnitCusVisible(val) {
      this.dialogCusVisible = val
    },
    valueFormatter(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue
      }
      return '—'
    },
    changeType(val) {
      if (!val) {
        this.characterData = []
        this.pageBean.unitCharacter = ''
      } else {
        queryUnitCharacter({ parentId: val, isDeleted: 0 }).then((res) => {
          if (res.status == 0) {
            this.pageBean.unitCharacter = ''
            this.characterData = res.data
          }
        })
      }
    },
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      listUnit(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    changeDateRange(date) {
      if (date) {
        this.pageBean.startTime = date[0]
        this.pageBean.endTime = date[1]
      } else {
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
    },
    getEditDisabled(data) {
      if (this.level == 4 && data.chargePersonNames) {
        return false
      }
      return true
    },
    getDisabled(data) {
      return false
      // if (this.level == 4 || sessionStorage.getItem('userid') == data.createBy) {
      //   return false
      // }
      // return true;
    },
    onSearch() {
      if (this.addressData) {
        if (this.addressData.length == 1) {
          this.pageBean.provinceId = this.addressData[0]
          this.pageBean.cityId = ''
        } else {
          this.pageBean.provinceId = this.addressData[0]
          this.pageBean.cityId = this.addressData[1]
        }
      } else {
        this.pageBean.provinceId = ''
        this.pageBean.cityId = ''
      }
      this.pageBean.pageNum = 1
      this.loadData()
    },
    addUnit() {
      this.$router.push('/clientManagement/unit/add')
    },
    handleCommand(e, item) {
      var path = ''
      var query = {}
      switch (e) {
        case '0':
          path = '/clientManagement/unit/unitSystem'
          query = { id: item.id, unitName: item.unitName }
          break
        case '1':
          path = '/clientManagement/unit/detail'
          query = { id: item.id, unitName: item.unitName }
          break
        case '2':
          path = '/clientManagement/unit/openmajor'
          query = { id: item.id, unitName: item.unitName }
          break
        case '3':
          path = '/clientManagement/unit/zhaonum'
          query = { id: item.id, unitName: item.unitName }
          break
        case '4':
          path = '/clientManagement/unit/tongdata'
          query = { id: item.id, unitName: item.unitName }
          break
        case '5':
          path = '/clientManagement/customer/add'
          query = {
            unitId: item.id,
            unitName: item.unitName,
          }
          break
        default:
          break
      }
      this.$router.push({
        path: path,
        query: query,
      })
    },
    onEdit(item) {
      this.$router.push({
        path: '/clientManagement/unit/add',
        query: { id: item.id },
      })
    },
    deleteAction(data) {
      this.deleteData = data
      this.dialogVisible = true
      this.giveMessage = '是否确认删除该单位？'
    },
    submitDialog() {
      this.dialogVisible = false
      this.delete()
    },
    delete() {
      deleteUnit({ id: this.deleteData.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    submitAction(type, dateRange) {
      if (type == 11) {
        if (dateRange) {
          this.pageBean.startTime = dateRange[0]
          this.pageBean.endTime = dateRange[1]
        } else {
          this.pageBean.startTime = ''
          this.pageBean.endTime = ''
        }
        this.pageBean.time = ''
      } else {
        this.pageBean.time = type
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
    },
    beforeUpload(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['xlsx']
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error('导入明细仅支持 .xlsx 格式!')
        return false
      }
      this.isImport = true
    },
    handleError(file, res) {
      this.isImport = false
    },
    handleAvatarSuccess(res, file) {
      if (res.status == 0 && res.data.errorCount == 0) {
        this.$message({
          type: 'success',
          message: res.msg,
        })
      } else {
        this.$message({
          type: 'error',
          message: res.msg,
        })
        // 显示错误提示的弹框
      }
      this.dialogMsgVisible = true
      this.importData = res.data
      this.isImport = false
      this.pageBean.pageNum = 1
      // 刷新数据
      this.loadData()
    },
    submitMsgDialog() {
      this.dialogMsgVisible = false
      this.importData = {}
    },
    downloadAction() {
      this.isDownload = true
      unitDownLoad()
        .then((result) => {
          downloadExcelFile(result, `单位信息模版`)
          this.isDownload = false
        })
        .catch((err) => {
          this.isDownload = false
        })
    },
    handleUnitExport() {
      this.isDownload = true
      let params = {
        ...this.pageBean,
        pageNum: this.globalPageNum,
      }
      delete params.pageSize
      unitExport(params)
        .then((result) => {
          if (result.status == 0) {
            downloadExcelFileCommon(result.data, `单位管理`)
            this.isDownload = false
            if (result.data.isExport) {
              this.$confirm('本次导出1千条数据,是否继续导出?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
              })
                .then(() => {
                  this.globalPageNum++
                  this.handleUnitExport()
                })
                .catch(() => {
                  this.globalPageNum = 1
                  this.$message({
                    type: 'info',
                    message: '已取消',
                  })
                })
            } else {
              this.$message({
                type: 'success',
                message: '数据已全部导出',
              })
              this.globalPageNum = 1
            }
          }
        })
        .catch((err) => {
          this.isDownload = false
        })
    },
    handleBatch() {
      this.isBatchMode = true
      this.selectedUnits = []
      this.tableKey++
    },
    cancelBatch() {
      this.isBatchMode = false
      this.selectedUnits = []
      this.tableKey++
    },
    handleSelectionChange(selection) {
      this.selectedUnits = selection
      console.log('选择变化:', selection.length, '个单位被选中')
    },
    assignResponsible() {
      if (this.selectedUnits.length === 0) {
        this.$message.warning('请先选择要分配负责人的单位')
        return
      }
      this.dialogName = '选择负责人'
      this.multipleNum = 5
      this.$refs.systemdialog.loadData()
      this.$refs.systemdialog.updateWorksId([])
      this.assignDialogVisible = true
    },
    updateSystemVisible(value) {
      this.assignDialogVisible = value
    },
    submitData(data, type, departmentId) {
      if (type === '选择负责人') {
        if (data.length === 0) {
          this.$message.warning('请选择负责人')
          return
        }
        if (data.length > 5) {
          this.$message.warning('最多只能选择5个负责人')
          return
        }
        this.batchAssignResponsible(data)
      }
      this.updateSystemVisible(false)
    },
    batchAssignResponsible(responsiblePersons) {
      if (responsiblePersons.length === 0) {
        this.$message.warning('请选择负责人')
        return
      }

      const batchData = []

      this.selectedUnits.forEach(unit => {
        responsiblePersons.forEach(person => {
          batchData.push({
            chargePerson: person.id,
            commonId: unit.id,
            departmentId: person.departmentId
          })
        })
      })

      if (batchData.length === 0) {
        this.$message.warning('没有可分配的数据')
        return
      }

      this.isLoading = true
      insertBatchChargePerson(batchData)
        .then((result) => {
          this.isLoading = false
          if (result.status === 0) {
            this.$refs.msgdialog.show({
              type:'success',
              title:'处理成功',
              msg:"您的批量处理已完成"
            })
            this.cancelBatch()
            this.loadData()
          } else {
            this.$refs.msgdialog.show({
              type:'error',
              title:'处理失败',
              msg:"您的批量处理失败"
            })
            this.loadData()
          }
        }).catch(()=>{
          this.isLoading = false
        })

    },
  },
}
</script>
<style>
.chargePersoncss .cell {
  padding-bottom: 7px !important;
}
</style>
<style lang="scss" scoped>
.cpdepartmentcss {
  font-size: 12px !important;
}
.ml20 {
  margin-right: 16px;
}
.m0 {
  margin-right: 0px !important;
}
.defbtn {
  cursor: pointer;
}
.w150 {
  width: 150px;
}
.width {
  width: 120px !important;
}
.page-header {
  display: flex;
  justify-content: space-between;
}

.unittable .el-button + .el-button {
  margin-left: 20px;
}

.tabbtn {
  font-size: 14px;
  cursor: pointer;
  padding: 2px;
}

.mbtn {
  margin-right: 20px;
}

.mbtn,
.mbtn:hover,
.mbtn:focus {
  color: #ff8d1a !important;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.btn {
  height: 34px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  letter-spacing: 1px;
}

.search {
  width: 96px;
}

.mainbg {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  min-height: calc(100%);
}
.myform .el-form-item:last-child {
  margin-right: 20px !important;
}
</style>
