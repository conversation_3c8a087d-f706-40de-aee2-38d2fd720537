<template>
  <el-drawer
    center
    class="mydrawer"
    title="退款详情"
    size="45%"
    :visible.sync="drawer"
    :before-close="beforeClose"
    direction="rtl"
  >
    <div class="concss">
      <el-form class="infoform" ref="form" label-width="120px">
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="退款金额(万元)：" class="labeltext">
              <span>{{ form.refundAmount }}万元</span>
            </el-form-item>
            <el-form-item label="实际退款日期：" class="labeltext">
              <span>{{ form.actualRefundDate }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="币种：" class="labeltext">
              <span>{{ form.currencyName }}</span>
            </el-form-item>
            <el-form-item label="退款方式：" class="labeltext">
              <span>{{ refundWays[form.refundWay] }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="退款收款账户：" class="labeltext">
              <span>{{ form.refundAccount }}</span>
            </el-form-item>

          </el-col>
          <el-col :span="11">
            <el-form-item label="退款日期：" class="labeltext">
              <span>{{ form.examTime || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="0"
          class="width100 mt10 pb20 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="22">
            <el-form-item label="退款原因：" class="labeltext lh">
              <span>{{ form.refundReason }}</span>
            </el-form-item>
            <el-form-item label="备注：" class="labeltext lh">
              <span>{{ form.notes }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          :gutter="0"
          class="width100 mt10 pb12 mb30 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="11">
            <el-form-item label="申请人：" class="labeltext">
              <span>{{ form.createByName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="申请时间：" class="labeltext">
              <span>{{ form.createTime }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <div class="checkpeople">
              <span class="spanname">审核人：</span>
              <eltimeline
                :businessId="businessId"
                :status="status"
                v-if="drawer"
              ></eltimeline></div
          ></el-col>
        </el-row>
      </el-form>
    </div>
  </el-drawer>
</template>

<script>
import { contractrefundInfo } from '@/api/contract/index'
import eltimeline from '@/components/common/eltimeline.vue'
export default {
  components: {
    eltimeline,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    drawer: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('updateVisible', val)
      },
    },
  },
  data() {
    return {
      refundWays: {
        1: '现金',
        2: '银行转账',
        3: '微信转账',
        4: '支付宝转账',
        5: '其他',
      },
      currencys: {
        1: '人民币',
      },
      form: {
        id: '',
        contractId: '',
        refundAmount: '',
        currency: '',
        actualRefundDate: '',
        refundWay: '',
        refundAccount: '',
        transactionNumber: '',
        refundReason: '',
        notes: '',
      },
      businessId: '',
      status:""
    }
  },
  methods: {
    beforeClose() {
      this.drawer = false
      var keys = Object.keys(this.form)
      keys.forEach((element) => {
        this.form[element] = ''
      })
    },
    loadData(id,status) {
      this.businessId = id
      this.status = status
      contractrefundInfo(id).then((result) => {
        this.form = result.data
      })
    },
  },
}
</script>

<style scoped>
.spanname {
  width: 120px;
  padding-right: 12px;
  text-align: right;
  flex-shrink: 0;
}
.checkpeople {
  display: flex;
}
.pb12 {
  padding-bottom: 12px;
}
.lh /deep/.el-form-item__content {
  line-height: 18px !important;
  padding: 0;
  padding-top: 12px;
}
.infoform /deep/.el-form-item {
  margin-bottom: 0px;
}
.concss {
  padding: 0px 20px;
  padding-top: 30px;
}
.mydrawer /deep/.el-drawer__header {
  text-align: center;
  color: #333333;
}
</style>
