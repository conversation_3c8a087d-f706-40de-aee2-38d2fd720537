<!-- DialogComponent.vue -->
<template>
  <el-dialog
    :visible.sync="visible"
    :title="options.title"
    :width="options.width"
    :before-close="handleBeforeClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <template>
      <div class="dialog-content">
        <div v-if="options.type !== 'default'" class="dialog-icon">
          <i
            :class="[
              'el-icon',
              options.type === 'success'
                ? 'el-icon-success'
                : options.type === 'warning'
                ? 'el-icon-warning'
                : options.type === 'error'
                ? 'el-icon-error'
                : 'el-icon-info',
            ]"
            :style="{ color: getIconColor() }"
          ></i>
        </div>
        <div class="dialog-message" v-html="options.content"></div>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="options.showCancelButton" @click="handleCancel">
          {{ options.cancelButtonText }}
        </el-button>
        <el-button
          v-if="options.showConfirmButton"
          :type="options.type === 'default' ? 'primary' : options.type"
          @click="handleConfirm"
        >
          {{ options.confirmButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      options: {},
    }
  },
  created() {
    console.log(this.options)
  },
  methods: {
    handleBeforeClose(done) {
      if (typeof this.options.beforeClose === 'function') {
        this.options.beforeClose(done)
      } else {
        done()
      }
    },
    handleClose() {
      this.$emit('close')
    },
    handleConfirm() {
      this.$emit('confirm')
    },
    handleCancel() {
      this.$emit('cancel')
    },
    getIconColor() {
      return this.options.type === 'success'
        ? '#67C23A'
        : this.options.type === 'warning'
        ? '#E6A23C'
        : this.options.type === 'error'
        ? '#F56C6C'
        : '#909399'
    },
  },
  beforeDestroy() {
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el)
    }
  },
}
</script>

<style scoped>
.dialog-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.dialog-icon {
  font-size: 28px;
  margin-right: 15px;
}

.dialog-message {
  flex: 1;
  word-break: break-all;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
