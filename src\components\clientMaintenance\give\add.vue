<template>
  <div>
    <div>
        <back>{{form.id ? '编辑发放' :'新建发放'}}</back>
    </div>
    <div class="mainbg" >
        <el-form ref="addform" class="addfcss"  :model="form" :rules="rules" label-width="118px">
            <textBorder>基础信息</textBorder>
            <div class="pt20  bbline">
                <el-row :gutter="0" type="flex" justify="start" >
                    <el-col :span="8">
                        <el-form-item  label="发放事由：" prop="reason">
                            <el-input class="definput" v-model="form.reason" placeholder="请输入发放事由"  maxlength="50" show-word-limit></el-input>
                        </el-form-item>
                        <el-form-item  label="客户单位：">
                            <div class="unitbtn">
                                {{form && form.unitName}} 
                            </div>
                        </el-form-item>
                    </el-col> 
                    <el-col :span="8">
                        <el-form-item  label="发放类型：" prop="type">
                            <el-select class="definput" popper-class="removescrollbar" v-model="form.type" placeholder="请选择">
                                <el-option
                                v-for="item in types"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="发放时间：" prop="distributionTime">
                            <el-date-picker
                            class="definput width100"
                            v-model="form.distributionTime"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="选择发放时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item required label="关联客户：" prop="customerId">
                          <div class="unitbtn" @click="chooseCustomer">
                            <span v-if="form.customerId" class="deffont">{{form.customerName}}</span>
                            <span v-else class="pltcss">请选择</span>
                             <i class=" rcenter el-icon-arrow-down"/></div>
                        </el-form-item>
                        <el-form-item label="发放单号：" prop="orderNumber">
                            <el-input class="definput"  v-model="form.orderNumber" placeholder="请输入发放单号" ></el-input>
                        </el-form-item>
                        
                    </el-col>
                </el-row>
            </div>
            <textBorder class="mt30">补充信息</textBorder>
            <div class="pt20 ">
                <el-row :gutter="0" type="flex" justify="start" >
                    <el-col :span="16">
                        <el-form-item label="发放备注：" prop="notes">
                            <el-input class="definput"  v-model="form.notes" maxlength="300" show-word-limit type="textarea" rows="4" placeholder="请输入发放备注" ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
            <div class="pt20 btncenter">
                <el-form-item >
                    <el-button class="btn_h42 wid98" type="primary" @click="submitForm()" :loading="isSubmit">保存</el-button>
                </el-form-item>
            </div>
        </el-form>
    </div>
    <!-- 关联客户 -->
    <cuatomerDialog 
    ref="customer"
    :visible.sync="customerDialogVisible" 
    @updateVisible="updateVisible" 
    @updateCustomer="updateCustomer">
    </cuatomerDialog>

  </div>
</template>

<script>
import back from '@/components/common/back.vue';
import textBorder from '@/components/common/textBorder.vue';
import cuatomerDialog from '@/components/common/customerDialog.vue';
import { addDistribution,updateDistribution,distributionInfo } from "@/api/clientMaintenance/give";

export default {
    components:{
        back,
        textBorder,
        cuatomerDialog,

    },
    data(){
        return{
            isSubmit:false,
            customerDialogVisible:false,
            dialogVisible:false,
            isSmall:window.screen.availWidth<1500?true:false,
            dialogName:'',
            inputValue:'',
            form:{
                id:'' ,
                reason:"",
                type:"",
                customerId:"",
                distributionTime:"",
                orderNumber:"",
                notes:"",
                distributionDepartmentId:"",
            },
            rules:{
                reason: [
                    { required: true, message: '请输入发放事由', trigger: 'blur' },
                    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
                ],
                type: [
                    { required: true, message: '请选择发放类型', trigger: 'change' }
                ],
                customerId: [
                    { required: true, message: '请选择关联客户', trigger: 'change' }
                ],
            },
            types:[
                {
                    label:"样书",
                    value:1,
                },
                {
                    label:"礼物",
                    value:2,
                },
            ],
        }
    },
    created(){
        this.$route.query.id ? this.loadData() : null
    },
    methods:{
        loadData(){
            this.isLoading = true;
            distributionInfo(this.$route.query.id).then((result) => {
                this.form = result.data;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        updateCustomer(data){
            this.form.customerName = data.customerName;
            this.form.customerId = data.id;
            this.form.unitId = data.unitId;
            this.form.unitName = data.unitName;
            this.$refs.addform.validateField('customerId');
        },
        chooseCustomer(){
          this.customerDialogVisible = true;
          this.$refs.customer.selectCustomerData({id:this.form.customerId,className:"DistributionController"})
        },
        updateVisible(val){
            this.customerDialogVisible = val;
        },
        // 提交表单
        submitForm() {
            this.$refs['addform'].validate((valid) => {
            if (!valid) {
                return false;
            } 
            if (this.form.id) {
                // 编辑
                this.updateData();
            }else{
                // 添加
                this.addData();
            }
            });
        },
        updateData(){
            this.isSubmit = true;
            updateDistribution(this.form).then((result) => {
                    if (result.data) {
                        this.$message({
                        type:"success",
                        message:"更新成功！"
                        })
                        this.$router.back();
                    }else{
                        this.$message({
                        type:"error",
                        message:"保存失败！"
                        })
                    }
                    this.isSubmit = false;
                }).catch((err) => {
                    this.isSubmit = false;
                });
        },
        addData(){
            this.isSubmit = true;
            addDistribution(this.form).then((result) => {
                    if (result.data) {
                        this.$message({
                        type:"success",
                        message:"添加成功！"
                        })
                        this.$router.back();
                    }else{
                        this.$message({
                        type:"error",
                        message:"保存失败！"
                        })
                    }
                    this.isSubmit = false;
                }).catch((err) => {
                    this.isSubmit = false;
                });
        },
    }
}
</script>
<style scoped>
.pltcss{
    color: #cdcdcd;
    font-size: 13px;
}
.unitbtn{
    color: #333;
}
.width100{
    width: 100%;
}
.wid98{
  width: 98px;
}
.uploadtext{
  color: #4285F4;
  cursor: pointer;
}
.uploadcss{
  width: 88px;
  height: 88px;
  line-height: 24px;
  background: #F5F5F5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border:1px dashed #CCCCCC;
}
.uploadcss i{
  margin-top: 20px;
  font-size: 24px;
  color: #4285F4;
}
.rcenter{
    position: absolute;
    right: 10px;
    line-height: 34px;
    font-size: 14px;
    color: #c0c4cc;
}
.btncenter{
    text-align: center;
}
.quanxiancss /deep/.el-form-item__label {
    margin-left: -7px;
    width: 125px !important;
}
.mainbg{
    margin: 16px 0px;
    padding: 20px 16px;
    background-color: white;
    border-radius: 8px;
}
.pd0{
    padding-right: 0px !important;
}
</style>
