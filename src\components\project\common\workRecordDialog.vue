<template>
  <el-dialog
    title="工作记录"
    :visible.sync="visible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      :model="form"
      :rules="dynamicRules"
      ref="form"
      label-width="120px"
    >
      <el-form-item label="关联任务" prop="taskId" v-if="!taskInfo.id && !workId">
        <el-cascader
          v-model="form.taskId"
          placeholder="请选择关联任务"
          style="width: 100%"
          clearable
          filterable
          :show-all-levels="false"
          :options="taskOptions"
          :props="cascaderProps"
          @change="handleTaskChange"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="关联任务" v-if="taskInfo.id && !workId">
        <el-input
          :value="`${taskInfo.taskNum} - ${taskInfo.taskName}`"
          readonly
          style="width: 100%"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="日期" prop="workTime">
        <el-date-picker
          v-model="form.workTime"
          type="date"
          placeholder="请选择日期"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="消耗工时" prop="workHours">
        <el-input
          v-model="form.workHours"
          @input="limitWorkHoursNum"
          type="number"
          :max="9999"
          placeholder="请输入"
        >
          <template slot="append">小时</template>
        </el-input>
      </el-form-item>
      <el-form-item label="工作内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="6"
          placeholder="请输入本次工作内容"
          maxlength="200"
          show-word-limit
        >
        </el-input>
      </el-form-item>
      <el-form-item label="工作产出" prop="workType">
        <el-select
          v-model="form.workType"
          placeholder="请选择工作产出类型"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in workTypeOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.workType === 1" label="时长">
        <el-input
          @input="limitPhoneNum"
          class="minute"
          type="number"
          :max="9999"
          v-model="minuteValue"
          placeholder="请输入分钟"
        ></el-input>
        <span class="linecss">--</span>
        <el-input
          @input="limitToSeconds"
          class="second"
          :max="9999"
          v-model="secondValue"
          placeholder="请输入秒(00)"
        ></el-input>
      </el-form-item>
      <el-form-item v-if="form.workType && form.workType != 1" :label="workTypeOptionMap[form.workType] + '：'">
        <el-input-number
          class="definput widm"
          v-model="form.duration"
          :controls="false"
          :precision="0"
          :step="1"
          :max="9999"
          placeholder="请输入数量"
        ></el-input-number>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { queryTaskList } from '@/api/project/index'
import { workTypeOptions, workTypeOptionMap } from '@/utils/dict'

export default {
  name: 'WorkRecordDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    workId:{
      type: String,
      default:'',
    },
    taskInfo: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      form: {
        taskId: [],
        type: '',
        workHours: '',
        workTime: '',
        content: '',
        projectType: '',
        workType: '',
        duration: '',
        costDate: '',
        costType: '',
        taskType:1
      },
      secondValue: '',
      minuteValue: '',
      rules: {
        workTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入本次工作内容', trigger: 'blur' }
        ],
        workHours: [
          { required: true, message: '请输入消耗工时', trigger: 'blur' }
        ],
        workType: [
          { required: true, message: '请选择工作产出类型', trigger: 'change' }
        ]
      },
      workTypeOptions,
      workTypeOptionMap,
      taskOptions: [],
      cascaderProps: {
        value: 'id',
        label: 'taskName',
        children: 'childrenList',
        checkStrictly: true
      }
    }
  },
  computed: {
    dynamicRules() {
      const rules = {
        workTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入本次工作内容', trigger: 'blur' }
        ],
        workHours: [
          { required: true, message: '请输入消耗工时', trigger: 'blur' }
        ],
        workType: [
          { required: true, message: '请选择工作产出类型', trigger: 'change' }
        ]
      }

      // 如果没有传入任务信息，则需要验证任务选择
      if (!this.taskInfo.id) {
        rules.taskId = [
          { required: true, message: '请选择关联任务', trigger: 'change' }
        ]
      }

      return rules
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && !this.workId) {
        this.loadTasks()
      }
    }
  },

  methods: {
    loadTasks() {
      queryTaskList({projectId: this.$route.query.id}).then((res) => {
        if (res.status == 0) {
          this.taskOptions = this.formatTaskData(res.data)
        }
      })
    },
    formatTaskData(data) {
      return data.map(item => ({
        ...item,
        taskName: `${item.taskNum} - ${item.taskName}`,
        childrenList: item.childrenList && item.childrenList.length > 0
          ? this.formatTaskData(item.childrenList)
          : undefined
      }))
    },
    handleTaskChange(value) {
      // 级联选择器返回数组，取最后一个值作为任务ID
      this.form.taskId = value
    },
    limitPhoneNum(value) {
      if (value.toString().length > 4) {
        this.minuteValue = this.minuteValue.toString().slice(0, 4)
      }
    },
    limitWorkHoursNum(value) {
      if (value.toString().indexOf('.') >= 0) {
        this.form.workHours = value
          .toString()
          .substring(0, value.toString().indexOf('.') + 2)
      }
    },
    limitToSeconds(value) {
      value = value.replace('.', '')
      const regex = /^[0-5][0-9]?$/
      if (!regex.test(value)) {
        this.secondValue = ''
      } else {
        this.secondValue = value
      }
    },
    resetForm() {
      this.form = {
        taskId: [],
        type: '',
        workHours: '',
        workTime: '',
        content: '',
        projectType: '',
        taskType:1,
        workType: '',
        duration: ''
      }
      this.minuteValue = ''
      this.secondValue = ''
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.workType === 1) {
            if (this.minuteValue !== '' || this.secondValue !== '') {
              let minutes = this.minuteValue !== '' ? this.minuteValue : '00'
              let seconds = this.secondValue !== '' ? this.secondValue : '00'

              if (minutes.length === 1) {
                minutes = '0' + minutes
              }
              if (seconds.length === 1) {
                seconds = '0' + seconds
              }

              this.form.duration = `${minutes}:${seconds}`
            } else {
              this.form.duration = ''
            }
          }

          this.form.type = 1 // 工作记录类型

          if (this.workId) {
            this.form.taskId = this.workId
            this.form.taskType = 2
          }else{
            // 如果有传入的任务信息，使用传入的任务ID
            if (this.taskInfo.id) {
              this.form.taskId = this.taskInfo.id
            } else {
              // 级联选择器返回数组，取最后一个值作为任务ID
              this.form.taskId = Array.isArray(this.form.taskId) && this.form.taskId.length > 0
                ? this.form.taskId[this.form.taskId.length - 1]
                : ''
            }
          }



          this.$emit('submit', { ...this.form })
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
.widm {
  width: 100%;
}
.linecss {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  width: 10%;
}
.minute {
  width: 45%;
}
.second {
  width: 45%;
  float: right;
}
</style>
