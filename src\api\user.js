import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/login',
    method: 'get',
    params: data,
  })
}
//侧边栏权限，按钮权限，所有侧边栏
export function getPermissions(params) {
  return request({
    url: '/crm/controller/resource/queryMenuListByUser',
    method: 'get',
    params
  })
}
//项目名称
export function getTitle(params) {
  return request({
    url: '/sf/business/configitem/getConfigItemValue',
    method: 'get',
    params
  })
}
export function logout(params) {
  return request({
    url: '/logout',
    method: 'get',
    params
  })
}
export function passwordUpdate(data) {
  return request({
    url: '/sf/business/userinfo/updatePassword',
    method: 'post',
    data
  })
}