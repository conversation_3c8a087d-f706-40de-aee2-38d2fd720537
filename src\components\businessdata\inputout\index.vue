<template>
  <div class="mainbg fixpb">
    <el-card v-loading="isLoading" class="mt10">
      <el-form label-width="85px" class="myform">
        <el-row :gutter="10" type="flex" justify="start">
          <el-col :span="6">
            <el-form-item label="单位名称：">
              <el-input
                class="definput"
                v-model="pageBean.unitName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="年份：">
              <el-date-picker
                :editable="false"
                :clearable="false"
                class="yearcss"
                value-format="yyyy"
                v-model="pageBean.year"
                type="year"
                placeholder="选择年"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产出比：">
              <div class="numdiv">
                <el-input
                  @input="handleEdit"
                  class="wip"
                  v-model="pageBean.searchMin"
                  type="number"
                >
                </el-input>
                <div class="form-center">--</div>
                <el-input
                  @input="handleEdit1"
                  class="wip"
                  v-model="pageBean.searchMax"
                  type="number"
                >
                </el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button
              class="defaultbtn1 mt"
              icon="el-icon-search"
              type="primary"
              @click="searchAction"
              >搜索</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <el-table
        @sort-change="sortChange"
        class="customertable mytable tootiptable"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="unitName" label="单位名称" align="center">
        </el-table-column>
        <el-table-column
          :formatter="formatter"
          sortable="custom"
          prop="inputPrice"
          label="投入金额"
          align="center"
        >
        </el-table-column>
        <el-table-column
          :formatter="formatter"
          sortable="custom"
          prop="outPrice"
          label="产出金额"
          align="center"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="proportion"
          label="产出投入比"
          align="center"
        >
          <template slot-scope="scope">
            {{ calculate(scope.row.proportion) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="edit"
          width="80"
          align="center"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button class="bbtn" type="text" @click="toBook(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <nolist></nolist>
        </template>
      </el-table>

      <div class="fixpage">
        <page
          :currentPage="pageBean.pageNum"
          :total="total"
          :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"
        ></page>
      </div>
    </el-card>
  </div>
</template>

<script>
import page from '@/components/common/page.vue'
import nolist from '@/components/common/nolist.vue'
import { queryInputOutData } from '@/api/businessdata/index'
import { getParStr } from '@/utils/tools'
export default {
  components: {
    page,
    nolist,
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        unitName: '',
        year: '',
        pageNum: 1,
        pageSize: 10,
        searchMin: '',
        searchMax: '',
        rankType: '',
      },
      updateRankType: {
        'ascending:inputPrice': 1,
        'descending:inputPrice': 2,
        'ascending:outPrice': 3,
        'descending:outPrice': 4,
        'ascending:proportion': 5,
        'descending:proportion': 6,
      },
    }
  },
  created() {
    this.pageBean.year = this.getDate()
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
    }
    this.loadData()
  },
  methods: {
    calculate(proportion) {
      if (proportion == 0) {
        return 0 + '%'
      } else {
        return (proportion * 100).toFixed(2) + '%'
      }
      console.log(proportion)
    },
    formatter(row, column, cellValue) {
      return cellValue + '万元'
    },
    toBook(row) {
      this.$router.push({
        path: '/businessdata/inputout/detail',
        query: {
          year: this.pageBean.year,
          unitId: row.unitId,
        },
      })
    },
    handleEdit(e) {
      let value = e.replace(/[^\d.]/g, '') // 只能输入数字
      value = value.replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
      this.pageBean.searchMin = value
    },
    handleEdit1(e) {
      let value = e.replace(/[^\d.]/g, '') // 只能输入数字
      value = value.replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
      this.pageBean.searchMax = value
    },
    sortChange(column) {
      this.pageBean.pageNum = 1
      let props = column.order + ':' + column.prop
      this.pageBean.rankType = this.updateRankType[props]
      this.loadData()
    },
    getDate() {
      var now = new Date()
      var year = now.getFullYear()
      return year + ''
    },
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      queryInputOutData(this.pageBean)
        .then((result) => {
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    searchAction() {
      if (this.pageBean.searchMax == 0 && this.pageBean.searchMax != '') {
        this.msgError('最大值必须大于0')
        return
      }
      if (
        (+this.pageBean.searchMin >= 0 && this.pageBean.searchMin != '') ||
        (+this.pageBean.searchMax >= 0 && this.pageBean.searchMax != '')
      ) {
        if (
          +this.pageBean.searchMin >= 0 &&
          this.pageBean.searchMin != '' &&
          this.pageBean.searchMax == ''
        ) {
          this.msgError('请输入数量的最大值')
          return
        }
        if (
          +this.pageBean.searchMin >= 0 &&
          this.pageBean.searchMin != '' &&
          this.pageBean.searchMax != '' &&
          +this.pageBean.searchMax <= +this.pageBean.searchMin
        ) {
          this.msgError('最大值得大于最小值')
          return
        }
        if (
          this.pageBean.searchMin == '' &&
          this.pageBean.searchMax != '' &&
          +this.pageBean.searchMax >= 0
        ) {
          this.msgError('请输入数量的最小值')
          return
        }
      }
      this.pageBean.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>
<style scoped lang="scss">
.mainbg {
  background-color: white;
  padding: 20px;
  min-height: 100%;
}
.yearcss /deep/.el-input__inner {
  height: 34px;
}
.wip {
  min-width: 60px;
  width: 40%;
  height: 34px;
}
.wip /deep/.el-input__inner {
  height: 28px;
  position: relative;
  top: -4px;
  line-height: 1px !important;
}
.wip /deep/.el-input__suffix {
  height: 28px;
  line-height: 34px;
}
.numdiv {
  height: 34px;
  border: 1px solid #dcdfe6;
  padding: 0 10px;
  margin-top: 3px;
}

.form-center {
  display: inline-block;
  height: 34px;
  width: 20%;
  text-align: center;
  line-height: 34px;
  vertical-align: top;
}
.el-date-editor.el-input {
  width: 100%;
}
.defaultbtn1 {
  padding: 9px 10px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
}
.mt {
  margin-top: 4px;
}
</style>
