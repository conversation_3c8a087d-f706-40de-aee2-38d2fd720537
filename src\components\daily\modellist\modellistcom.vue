<template>
  <div class="mainbg1 fixpb" v-loading="isLoading">
    <div class="page-header">
      <el-form  class="myform" ref="form" :model="pageBean" :inline="true">
        <el-form-item label="">
          <el-input
          v-model="pageBean.templateName"
          class="definput"
          clearable
          placeholder="请输入模板名称"
        >
        </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-button class=" defaultbtn" type="primary" icon="el-icon-search" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button v-isShow="'crm:business:template:save'" class="defaultbtn" type="primary" icon="el-icon-plus"
          @click="addUnit">创建模板</el-button>
      </div>
    </div>
    <el-table class="mytable" :data="tableData" style="width: 100%" >
      <el-table-column prop="templateName" label="模板名称" align="center">
      </el-table-column>
      <el-table-column prop="edit" width="200" align="center" label="操作">
        <template slot-scope="scope">
            <el-button  :disabled="getDisabled(scope.row)" v-isShow="'crm:business:templatepermission:saveTemplatePermission'"  class="bbtn tabbtn" type="text" @click="toPerSet(scope.row)">
              权限管理</el-button>
          <el-button
          :disabled="getDisabled(scope.row)"
          class="bbtn tabbtn"
          type="text" @click="onEdit(scope.row)">
            编辑</el-button>
          <el-button
          :disabled="getDisabled(scope.row)"
          class="rbtn tabbtn" type="text"
            @click="deleteAction(scope.row)">
            删除</el-button>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>



      <div class="fixpage">
        <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"></page>
    </div>

    <dc-dialog :iType="giveType" title="提示" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
      <template>
      </template>
      <p class="pcc">{{ giveMessage }}</p>
    </dc-dialog>
  </div>
</template>

<script>

  import page from '../../common/page.vue';
  import dcDialog from '../../common/dcDialog.vue';
  import nolist from '../../common/nolist.vue';
  import { listMoban, deleteMoban} from '@/api/moban/index'
  import { getParStr } from '@/utils/tools';
  export default {
    components: {
      page,
      dcDialog,
      nolist,
    },
    props:{
          typeModel:{
            type:String,
            default:''
          },
    },
    data() {
      return {
        giveType: 1,
        giveMessage: '',
        isLoading: false,
        dialogVisible: false,
        deleteData: {},
        tableData: [],
        total: 0,
        pageBean: {
          templateDepartmentIds: '',
          pageNum: 1,
          pageSize: 10,
          templateType: '7',
          type:'3',
          templateName:'',
        },
        level: sessionStorage.getItem('dataScope'),
        userId: sessionStorage.getItem('userid'),
      }
    },
    created() {
      if (Object.keys(this.$route.query).length>0) {
        this.pageBean = Object.assign(this.pageBean,this.$route.query)
        this.pageBean.pageNum = Number(this.pageBean.pageNum)
        this.pageBean.pageSize = Number(this.pageBean.pageSize)
      }
      this.loadData();
    },

    methods: {
      getDisabled(data) {
        if (this.level == 4 || this.userId == data.createBy) {
          return false
        }
        return true;
      },
      toPerSet(data){
              this.$router.push({
                  path:'/daily/modellist/perset',
                  query:{
                    templateId:data.id
                  }
                })
      },
      loadData() {
        history.replaceState(null,null,`#${this.$route.path}?${getParStr(this.pageBean)}`)
        this.isLoading = true;
        listMoban(this.pageBean).then((result) => {
          this.tableData = result.data;
          this.total = result.page.total;
          this.isLoading = false;
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      onSearch() {
        this.pageBean.pageNum = 1;
        this.loadData();
      },
      addUnit() {
                this.$router.push({
                  path:'/daily/modellist/add',
                  query:{
                      modelType:1
                  }
                })
      },
      onEdit(item) {
              this.$router.push({
                path:'/daily/modellist/add',
                query:{
                    modelType:1,
                    id: item.id
                }
              })
      },
      deleteAction(data) {
        this.deleteData = data;
        this.dialogVisible = true;
        this.giveMessage = '是否确认删除该模板？'
      },
      submitDialog() {
        this.dialogVisible = false;
        this.delete();
      },
      delete() {
        deleteMoban({ id: this.deleteData.id }).then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData();
          } else {
            this.$message({
              type: 'error',
              message: result.msg
            })
          }
        }).catch((err) => {

        });
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.loadData();
      }
    }
  }

</script>

<style scoped>
  .definput{
    width: 180px;
  }
  .page-header {
    display: flex;
    justify-content: space-between;
  }
  .tabbtn {
    font-size: 14px;
    cursor: pointer;
    padding: 2px;
  }
  .bbtn,
  .bbtn:hover,
  .bbtn:focus {
    color: #4285F4;
  }
  .rbtn,
  .rbtn:hover,
  .rbtn:focus {
    color: #F45961;
  }
  .mainbg1 {
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    min-height: calc(100vh - 106px);
    position: relative;
    transform: none;
  }
</style>
