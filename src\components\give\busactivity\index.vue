<template>
  <div class="mainbg fixpb">
    <el-form :inline="true" class="myform">
      <el-form-item label="">
        <el-input
          class="definput"
          v-model="pageBean.customerName"
          clearable
          placeholder="请输入客户姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input
          class="definput"
          v-model="pageBean.unitName"
          clearable
          placeholder="请输入单位名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input
          class="definput"
          v-model="pageBean.operationName"
          clearable
          placeholder="请输入业务经理"
        ></el-input>
      </el-form-item>
      <el-form-item label="业务经理部门：" label-width="120px">
          <div class="unitbtn mt definput" @click="chooseDepartment">
          <span v-if="pageBean.distributionDepartmentIds" class="deffont">{{
              searchdepartmentNames
          }}</span>
          <span v-else>请选择</span>
          <i
              v-if="searchdepartmentNames"
              @click.stop="cleardata"
              class="rcenter el-icon-circle-close"
          />
          <i v-else class="rcenter el-icon-arrow-down" />
          </div>
      </el-form-item>
      <el-form-item label="时间：">
        <el-date-picker
          class="definput1"
          v-model="fftime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          class="defaultbtn"
          icon="el-icon-search"
          type="primary"
          @click="searchAction"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item class="fr">
        <el-button
          class="defaultbtn"
          icon="el-icon-download"
          type="primary"
          v-isShow="'crm:controller:distribution:exportBusinessList'"
          :loading="isExport"
          @click="exportData"
          >导出</el-button
        >
        <el-button
          class="defaultbtn"
          icon="el-icon-plus"
          type="primary"
          v-isShow="'crm:controller:distribution:save'"
          @click="addGive"
          >新增</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="jhtable mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        prop="reason"
        label="事由"
        class-name="column_blue"
        align="center"
        min-width="200px"
      >
      </el-table-column>
      <el-table-column
        :formatter="formatter"
        prop="price"
        label="金额"
        align="center"
        min-width="167px"
      >
      </el-table-column>
      <el-table-column
        prop="customerName"
        label="客户"
        align="center"
        min-width="167px"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="unitName"
        label="单位"
        align="center"
        min-width="167px"
      >
      </el-table-column>
      <el-table-column
        prop="operationName"
        label="业务经理"
        align="center"
        min-width="167px"
      >
      </el-table-column>
      <el-table-column
        prop="distributionTime"
        label="时间"
        align="center"
        min-width="167px"
      >
      </el-table-column>
      <el-table-column
        prop="createByName"
        label="创建人"
        align="center"
        min-width="167px"
      >
      </el-table-column>
      <el-table-column
        prop="edit"
        width="250"
        align="center"
        fixed="right"
        label="更多操作"
      >
        <template slot-scope="scope">
          <el-button
            class="bbtn"
            v-isShow="'crm:controller:distribution:info'"
            type="text"
            @click="toDetail(scope.row)"
          >
            详情</el-button
          >
          <el-button
            class="bbtn"
            v-isShow="'crm:controller:distribution:update'"
            type="text"
            @click="toEdit(scope.row)"
            >编辑</el-button
          >
          <el-button
            class="rbtn"
            v-isShow="'crm:controller:distribution:delete'"
            type="text"
            @click="deleteAction(scope.row)"
          >
            删除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>

    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <dc-dialog
      :iType="giveType"
      title="提示"
      width="500px"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <template> </template>
      <p class="pcc">{{ giveMessage }}</p>
    </dc-dialog>
    <revisit
      ref="revisitDialog"
      :type="1"
      :visible.sync="revisitVisible"
      @updateVisible="updateVisible"
    ></revisit>
    <giveDetailsDialog
      :visible.sync="givedialogVisible"
      ref="giveDialog"
      @updateVisible="updateGiveVisible"
    >
    </giveDetailsDialog>
    <!-- 选择部门 -->
    <departmentDialog
    ref="deRef"
    dType="1"
    :visible.sync="dialogDepartmentVisible"
    @updateVisible="updateSystemVisible"
    @submitData="submitData"
    >
    </departmentDialog>
  </div>
</template>

<script>
import page from '@/components/common/page.vue'
import departmentDialog from '@/components/common/departmentDialog.vue';
import revisit from '../components/revisitDialog.vue'
import giveDetailsDialog from '../components/giveDetailsDialog.vue'
import {
  distributionList,
  deleteDistribution,
  exportBusinessList,
} from '@/api/clientMaintenance/give'
import nolist from '@/components/common/nolist.vue'
import { getToken } from '@/utils/auth'
import { downloadFileByUrl } from '@/utils/tools'
import { getParStr } from '@/utils/tools'
import { findPath } from '@/utils/permission';
export default {
  components: {
    page,
    revisit,
    giveDetailsDialog,
    nolist,
    departmentDialog
  },
  data() {
    return {
      dialogDepartmentVisible:false,
      searchdepartmentNames:'',
      selDepartmentData:[],
      giveType: 3,
      fftime: [],
      giveMessage: '',
      givedialogVisible: false,
      dialogVisible: false,
      revisitVisible: false,
      isLoading: false,
      tableData: [],
      deleteData: {},
      total: 0,
      pageBean: {
        type: 3,
        customerName: '',
        distributionDepartmentIds:'',
        goodsName: '',
        unitName: '',
        isRevisit: '',
        operationName: '',
        specialtyName: '',
        startTime: '',
        endTime: '',
        pageNum: 1,
        pageSize: 10,
      },
      errorData: [],
      isDownload: false,
      isImport: false,
      getUrl: `${process.env.VUE_APP_BASE_API}/crm/controller/dicttype/dictItemImportExcel`,
      headers: { Authorization: getToken() },
      fileData: {
        dictTypeCode: 'Press',
      },
      isExport: false,
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
    }
    this.loadData()
  },
  methods: {
    formatter(row, column, cellValue) {
      return cellValue + '元'
    },
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      distributionList(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    searchAction() {
      this.pageBean.pageNum = 1
      if (this.fftime && this.fftime.length > 0) {
        this.pageBean.startTime = this.fftime[0]
        this.pageBean.endTime = this.fftime[1]
      } else {
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
      this.loadData()
    },
    updateGiveVisible(val) {
      this.givedialogVisible = val
    },
    updateVisible(val) {
      this.revisitVisible = val
    },
    submitDialog() {
      this.dialogVisible = false
      if ((this.giveType = 1)) {
        deleteDistribution({ id: this.deleteData.id })
          .then((result) => {
            if (result.data) {
              this.$message({
                type: 'success',
                message: '删除成功',
              })
              this.loadData()
            } else {
              this.giveType = 2
              this.giveMessage = result.msg
              this.dialogVisible = true
            }
          })
          .catch((err) => {})
      }
    },
    addGive() {
      this.$router.push({
        path: '/give/add',
        query: {
          type: this.pageBean.type,
        },
      })
    },
    toEdit(data) {
      this.$router.push({
        path: '/give/add',
        query: {
          id: data.id,
          type: this.pageBean.type,
        },
      })
    },
    deleteAction(data) {
      this.giveType = 1
      this.giveMessage = '是否删除该记录?'
      this.dialogVisible = true
      this.deleteData = data
    },
    exportData() {
      this.isExport = true
      exportBusinessList(this.pageBean)
        .then((result) => {
          if (result.data.url) {
            downloadFileByUrl(result.data.url)
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isExport = false
        })
        .catch((err) => {
          this.isExport = false
        })
    },
    onRevisit(data) {
      this.revisitVisible = true
      this.$nextTick(() => {
        this.$refs.revisitDialog.init(data)
      })
    },
    toDetail(data) {
      const path = '/give/busactivity/detail'
      if (!findPath(path)) {
        return this.$message.error('暂无查看权限')
      }
      this.$router.push({
        path: path,
        query: { id: data.id, type: this.pageBean.type },
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    download() {},
    submitMsgDialog() {
      this.errorData = []
      this.dialogMsgVisible = false
    },
    beforeUpload(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['xlsx']
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error('导入明细仅支持 .xlsx 格式!')
        return false
      }
      this.isImport = true
    },
    handleError(file, res) {
      this.isImport = false
    },
    handleAvatarSuccess(res, file) {
      if (res.status == 0 && res.data.errorCount <= 0) {
        this.$message({
          type: 'success',
          message: res.msg,
        })
      } else {
        this.$message({
          type: 'error',
          message: res.msg,
        })
        // 显示错误提示的弹框
        this.dialogMsgVisible = true
        this.errorData = res.data.errorData
      }
      // 刷新数据
      this.loadData()
      this.isImport = false
    },
    cleardata() {
      this.searchdepartmentNames = ''
      this.pageBean.distributionDepartmentIds = ''
      this.selDepartmentData = []
    },
    chooseDepartment(e) {
        this.dialogDepartmentVisible = true
        this.$refs.deRef.loadData()
        this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
    },
    updateSystemVisible(val) {
        this.dialogDepartmentVisible = val
    },
    submitData(data) {
        this.dialogDepartmentVisible = false
        this.selDepartmentData = data
        let departmentIds = data.map((item) => item.id)
        let departmentNames = data.map((item) => item.name)
        this.pageBean.distributionDepartmentIds = departmentIds.join(',')
        this.searchdepartmentNames = departmentNames.join(',')
    },
  },
}
</script>

<style scoped>
.wi {
  width: 120px;
}
.yesc {
  color: #56c36e;
}

.noc {
  color: #f45961;
}

.mr20 {
  margin-right: 20px;
}

.jhtable .el-button + .el-button {
  margin-left: 20px;
}

.mainbg {
  background-color: white;
  padding: 20px;
  min-height: calc(100vh - 106px);
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  /* background-color: red; */
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 20px;
}

.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}

.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}

.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.jhtable /deep/.cell {
}

.jhtable .el-button {
  padding: 2px;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.bumen /deep/.el-form-item__label {
  width: 130px !important;
  margin-left: -45px;
}
.definput {
  width: 200px;
}
.rcenter {
  position: absolute;
  top: 5px;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}
.definput1 {
  width: 300px;
  height: 34px !important;
  line-height: 34px !important;
  padding: 0px 12px;
}
.right {
  text-align: right;
}
.fr {
}
</style>
