<!-- 月份筛选 -->
<template>
  <div class="MonthScreening">
    <div class="title">最近:</div>
    <ul>
      <li v-for="(item,index) in months" :key="index" @click="change(index)" :class="{ active:active==index }"><button>{{ item.content }}</button></li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {
      months: [
        { content: '一月' },
        { content: '三月' },
        { content: '六月' }
      ],
      active: 3 // 默认不显示
    }
  },
  methods: {
    change (obj) {
      // 点击效果
      this.active = obj;
      // 处理开始时间和结束时间
      let time = new Date()
      let year = time.getFullYear();
      let month = time.getMonth() + 1;
      let day = time.getDate();
      let beginOne = ''
      if (month < 10) {
        month = '0' + month
      }
      if (day < 10) {
        day = '0' + day
      }
      let finish = `${year}-${month}-${day}`
      month = Number(month)
      day = Number(day)
      if (obj === 0) {
        let sum = this.monthScreen(year, month, 1)
        year = parseInt(sum / 12)
        month = parseInt(sum % 12)
        if (month < 10) {
          month = '0' + month
        }
        if (day < 10) {
          day = '0' + day
        }
        beginOne = `${year}-${month}-${day}`
      } else if (obj === 1) {
        let sum = this.monthScreen(year, month, 3)
        year = parseInt(sum / 12)
        month = parseInt(sum % 12)
        if (month < 10) {
          month = '0' + month
        }
        if (day < 10) {
          day = '0' + day
        }
        console.log(year, month)
        beginOne = `${year}-${month}-${day}`
      } else if (obj === 2) {
        let sum = this.monthScreen(year, month, 6)
        year = parseInt(sum / 12)
        month = parseInt(sum % 12)
        if (month < 10) {
          month = '0' + month
        }
        if (day < 10) {
          day = '0' + day
        }
        beginOne = `${year}-${month}-${day}`
      }
      beginOne = `${beginOne} 00:00:00`
      finish = `${finish} 23:59:59`
      console.log(beginOne, finish)
      this.$emit('time', beginOne, finish)
    },
    monthScreen (years, months, number) {
      let sum = (years * 12) + months
      sum = sum - number
      console.log(sum)
      return sum;
    }
  }
}
</script>

<style lang="scss" scoped>
  li {
    list-style: none;
  }
  .MonthScreening::after {
    content: '';
    display: block;
    clear: both;
    visibility: hidden;
  }
  .MonthScreening {
    margin-bottom: 20px;
    .title {
      vertical-align: top;
      font-size: 14px;
      margin-right: 20px;
    }
    .title,ul {
      display: inline-block;
    }
    ul li {
      float: left;
      margin-right: 20px;
      button {
        cursor: pointer;
        border: none;
        background: none;
        outline: none;
      }
    }
    ul .active button {
      color: #EF5496;
    }
  }
</style>
