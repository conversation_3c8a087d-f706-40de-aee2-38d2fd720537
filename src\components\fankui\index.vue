<template>
    <div>
      <el-card>
        <div class="topContent">
            <div class="leftBtn">
                <el-input placeholder="请输入反馈内容关键词" v-model="input" clearable class="input" size="medium"></el-input>
                <el-input placeholder="请输入反馈人姓名" v-model="name" clearable class="input" size="medium"></el-input>
                <el-button type="primary" icon="el-icon-search" size="medium" @click="fetchFeedback">搜索</el-button>
            </div>
        </div>
        <div class="middle">
            <el-table
            :data="tableData"
            style="width: 100%">
            <el-table-column
              prop="content"
              label="问题反馈内容"
              align="center">
            </el-table-column>

            <el-table-column
              label="图片"
              align="center">
              <template slot-scope="scope">
                <div style="display: flex; justify-content: center;">
                  <el-image
                      v-for="(file, index) in scope.row.fileInfoEntityList"
                      :key="index"
                      style="width: 50px; height: 50px; margin-right: 5px;"
                      :src="file.url"
                      :preview-src-list="scope.row.fileInfoEntityList.map(file => file.url)"
                      fit="fill">
                  </el-image>
                </div>
              </template>
            </el-table-column>

          <el-table-column
            prop="createByName"
            label="反馈人"
            align="center">
        </el-table-column>
            <el-table-column
              prop="createTime"
              label="反馈时间"
              align="center">
            </el-table-column>
          </el-table>
        </div>
        <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"></page>
   </el-card>
    </div>
  </template>
  
<script>
import page from '../common/page.vue';
import { fetchFeedBackList } from '@/api/gongao/index.js';

    export default {
    components:{
        page
    },
    data() {
        return {
          input:'',
          name:'',
          tableData: [],
          total: 0,
          pageBean: {
                pageNum: 1,
                pageSize: 10,
                content: '',
                createByName:''
            },
            
        }
    },
    created() {
        this.fetchFeedback();
    },
    methods:{
      handleCurrentChange(page) {
          this.pageBean.pageNum = page;
          this.fetchFeedback();
      },
      fetchFeedback() {
          this.pageBean.content = this.input;
          this.pageBean.createByName = this.name;
          fetchFeedBackList(this.pageBean).then(response => {
              this.tableData = response.data; 
              this.total = response.page.total;
          }).catch(error => {
              console.log('=====');
          });
      }

    }
}
  </script>
  
  <style scoped>
  .topContent{
    display: flex;
  }
  .input{
    width: 200px;
    margin-right: 20px;
  }
  </style>