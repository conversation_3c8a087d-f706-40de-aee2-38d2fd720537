<template>
    <div class="mainbg fixpb">
        <el-form :inline="true" class="myform ">
                    <el-form-item label="">
                        <el-input class="definput" v-model="pageBean.customerName" clearable
                            placeholder="请输入客户姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input class="definput" v-model="pageBean.unitName" clearable
                            placeholder="请输入单位名称"></el-input>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input class="definput" v-model="pageBean.goodsName" clearable
                            placeholder="请输入教材或出版社名称"></el-input>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input class="definput" v-model="pageBean.specialtyName" clearable
                            placeholder="请输入用书专业"></el-input>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input class="definput" v-model="pageBean.operationName" clearable
                            placeholder="请输入业务经理"></el-input>
                    </el-form-item>
                    <el-form-item label="业务经理部门：" label-width="120px">
                        <div class="unitbtn mt definput" @click="chooseDepartment">
                        <span v-if="pageBean.distributionDepartmentIds" class="deffont">{{
                            searchdepartmentNames
                        }}</span>
                        <span v-else>请选择</span>
                        <i
                            v-if="searchdepartmentNames"
                            @click.stop="cleardata"
                            class="rcenter el-icon-circle-close"
                        />
                        <i v-else class="rcenter el-icon-arrow-down" />
                        </div>
                    </el-form-item>
                    <el-form-item label="是否回访：">
                        <el-select class="definput wi" popper-class="removescrollbar" v-model="pageBean.isRevisit" clearable
                            placeholder="请选择">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发放方式：">
                        <el-select class="definput wi" popper-class="removescrollbar" v-model="pageBean.distributionForm" clearable
                            placeholder="请选择">
                            <el-option v-for="item in giveTypes" :key="item.id" :label="item.name" :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发放时间："  >
                        <el-date-picker
                            class="definput1"
                            v-model="fftime"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                           >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item >
                        <el-button class="defaultbtn" icon="el-icon-search" type="primary"
                        @click="searchAction">搜索</el-button>
                    </el-form-item>
                    <el-form-item class="fr">
                        <el-button class="defaultbtn " icon="el-icon-plus" type="primary"
                        v-isShow="'crm:controller:distribution:save'" @click="addGive">我要发放</el-button>
                    </el-form-item>
                    <!-- <el-form-item class="fr">
                        <el-upload class="" :action="getUrl" :show-file-list="false" :before-upload="beforeUpload"
                            :on-success="handleAvatarSuccess" :on-error="handleError" :headers="headers" :data="fileData"
                            accept=".xlsx">
                            <el-button class="defaultbtn" icon="el-icon-my-inport" type="primary"
                                :loading="isImport">导入明细</el-button>
                        </el-upload>
                    </el-form-item>
                    <el-form-item class="fr ml20">
                        <el-button class=" defaultbtn " icon="el-icon-my-download" type="primary" :loading="isDownload"
                            @click="downloadAction">下载模版</el-button>
                    </el-form-item>
                    <el-form-item class="fr ">
                        <el-button class=" defaultbtn " icon="el-icon-my-download" type="primary" :loading="isExport"
                            @click="exportAction">导出数据</el-button>
                    </el-form-item> -->
        </el-form>
        <el-table class="jhtable mytable" :data="tableData" style="width: 100%" border  v-loading="isLoading">
            <el-table-column prop="reason" label="发放事由" class-name="column_blue" align="center" min-width="200px">
            </el-table-column>
            <el-table-column prop="giveType" label="发放方式" align="center" width="100px">
                <template slot-scope="scope">
                    <span>{{ giveTypeMap[scope.row.distributionForm] }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="name"  label="样书" align="center" min-width="167px">
                <template slot-scope="scope" >
                    <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                        <el-tooltip :content="item.name" placement="left" :disabled="item.isShow"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                            <div class="itemlinecss">{{ item.name}}</div>
                        </el-tooltip>
                    </div>
                    <div v-else>{{ scope.row.goodsName }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="platformName" label="出版社" align="center" min-width="167px">
                <template slot-scope="scope">
                    <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                        <el-tooltip :content="item.platformName" placement="left" :disabled="item.isShow2"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                            <div class="itemlinecss">{{item.platformName }}</div>
                        </el-tooltip>
                    </div>
                    <div v-else>
                        {{scope.row.platformName}}
                    </div>

                </template>
            </el-table-column>
            <el-table-column prop="specialtyName" label="用书专业" align="center" min-width="167px">
                <template slot-scope="scope">
                    <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                        <el-tooltip :content="item.specialtyName" placement="left" :disabled="item.isShow3"  v-for="item in scope.row.distributionDetailList" :key="item.id">
                            <div class="itemlinecss">{{item.specialtyName }}</div>
                        </el-tooltip>
                    </div>
                    <div v-else>
                        {{scope.row.specialtyName}}
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="useBookNumber" label="数量" align="center" min-width="167px">
                <template slot-scope="scope">
                    <div v-if="scope.row.distributionDetailList&&scope.row.distributionDetailList.length>0">
                        <div class="itemlinecss" v-for="item in scope.row.distributionDetailList" :key="item.id">
                            {{
                                item.number
                            }}
                        </div>
                    </div>
                    <div v-else>
                        {{scope.row.useBookNumber}}
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="useBookYear" label="用书时间" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="customerName" label="客户" align="center" min-width="167px" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="unitName" label="单位" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="operationName" label="业务经理" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="distributionDepartmentName" label="业务经理部门" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="distributionTime" label="发放时间" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="edit" label="是否回访" align="center" min-width="120px">
                <template slot-scope="scope">
                    <div v-if="scope.row.isRevisit" class="yesc">是</div>
                    <div v-else class="noc">否</div>
                </template>
            </el-table-column>
            <el-table-column prop="createByName" label="创建人" align="center" min-width="167px">
            </el-table-column>
            <el-table-column prop="edit" width="250" align="center" fixed="right" label="更多操作">
                <template slot-scope="scope">
                    <el-button class="bbtn "  type="text"
                    @click="onRevisit(scope.row)"> 回访</el-button>
                    <el-button class="bbtn " v-isShow="'crm:controller:distribution:info'" type="text"
                    @click="toDetail(scope.row)"> 详情</el-button>
                    <el-button class="bbtn" v-isShow="'crm:controller:distribution:update'" type="text" @click="toEdit(scope.row)">编辑</el-button>
                    <el-button class="rbtn " v-isShow="'crm:controller:distribution:delete'" type="text"
                        @click="deleteAction(scope.row)"> 删除</el-button>
                </template>
            </el-table-column>
            <template slot="empty">
                <nolist></nolist>
            </template>
        </el-table>

            <div class="fixpage">
                <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"></page>
            </div>

        <dc-dialog :iType="giveType" title="提示" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog">
            <template>

            </template>
            <p class="pcc">{{ giveMessage }}</p>
        </dc-dialog>

        <revisit ref="revisitDialog" :type="1" :visible.sync="revisitVisible" @updateVisible="updateVisible"></revisit>
        <giveDetailsDialog :visible.sync="givedialogVisible" ref="giveDialog" @updateVisible="updateGiveVisible">
        </giveDetailsDialog>
        <!-- 选择部门 -->
        <departmentDialog
        ref="deRef"
        dType="1"
        :visible.sync="dialogDepartmentVisible"
        @updateVisible="updateSystemVisible"
        @submitData="submitData"
        >
        </departmentDialog>
        <dc-dialog :iType="giveType" title="" width="500px" :dialogVisible.sync="dialogMsgVisible" @submit="submitMsgDialog">
            <template>

            </template>
            <p>导入成功：{{ importData.successCount }} 条</p>
            <p>导入失败：{{ importData.errorCount }} 条</p>
            <p v-for="(item,index) in importData.errorData" :key="index">{{ item }}</p>
        </dc-dialog>
    </div>
</template>

<script>
import page from '@/components/common/page.vue';
import departmentDialog from '@/components/common/departmentDialog.vue';
import revisit from '../components/revisitDialog.vue';
import giveDetailsDialog from '../components/giveDetailsDialog.vue';
import { distributionList, deleteDistribution,downloadTemplate,distributionDownload } from '@/api/clientMaintenance/give';
import nolist from '@/components/common/nolist.vue';
import { getToken } from '@/utils/auth'
import { getParStr,downloadExcelFile,downloadFileByUrl } from "@/utils/tools";
import { giveTypes,giveTypeMap } from '../../../utils/dict';
import { findPath } from '@/utils/permission';
export default {
    components: {
        page,
        revisit,
        giveDetailsDialog,
        nolist,
        departmentDialog
    },
    data() {
        return {
            dialogMsgVisible:false,
            isExport:false,
            importData: {
                successCount: 0,
                errorCount: 0,
                errorData: [],
            },
            dialogDepartmentVisible:false,
            searchdepartmentNames:'',
            selDepartmentData:[],
            giveTypes,
            giveTypeMap,
            giveType: 1,
            giveMessage: "",
            givedialogVisible: false,
            dialogVisible: false,
            revisitVisible: false,
            fftime:[],
            types: {
                1: '样书',
                2: "礼品"
            },
            options: [{
                value: "1",
                label: '是',
            }, {
                value: "0",
                label: '否',
            }],
            isLoading: false,
            tableData: [],
            deleteData: {},
            total: 0,
            pageBean: {
                type: 1,
                distributionForm:"",
                customerName: "",
                distributionDepartmentIds:"",
                goodsName:'',
                unitName: '',
                isRevisit:'',
                specialtyName:"",
                startTime:'',
                endTime:'',
                pageNum: 1,
                pageSize: 10,
            },
            errorData:[],
            isDownload:false,
            isImport:false,
            getUrl: `${process.env.VUE_APP_BASE_API}/crm/controller/dicttype/dictItemImportExcel`,
            headers: { Authorization: getToken() },
            fileData: {
                dictTypeCode: "Material",
            },
        }
    },
    created() {
        if (Object.keys(this.$route.query).length>0) {
            this.pageBean = Object.assign(this.pageBean,this.$route.query)
            this.pageBean.pageNum = Number(this.pageBean.pageNum)
            this.pageBean.pageSize = Number(this.pageBean.pageSize)
            this.pageBean.distributionForm = Number(this.pageBean.distributionForm) || undefined

        }
        this.loadData();
    },
    methods: {
        loadData() {
            history.replaceState(null,null,`#${this.$route.path}?${getParStr(this.pageBean)}`)
            this.isLoading = true;
            distributionList(this.pageBean).then((result) => {
                result.data.map(item =>{
                    item.distributionDetailList && item.distributionDetailList.forEach(element => {
                        element.isShow = element.name.length>10 ? false : true
                        element.isShow2 = element.platformName.length>10 ? false : true
                        element.isShow3 = element.specialtyName.length>10 ? false : true

                    });
                })
                this.tableData = result.data;
                this.total = result.page.total;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        searchAction() {
            console.log("zousearch===>");
            this.pageBean.pageNum = 1;
            if (this.fftime) {
                this.pageBean.startTime = this.fftime[0]
                this.pageBean.endTime = this.fftime[1]
            }else{
                this.pageBean.startTime = ''
                this.pageBean.endTime = ''
            }
            this.loadData()
        },
        updateGiveVisible(val) {
            this.givedialogVisible = val;
        },
        updateVisible(val) {
            this.revisitVisible = val;
        },
        submitDialog() {
            this.dialogVisible = false;
            if (this.giveType = 1) {
                // 删除
                deleteDistribution({ id: this.deleteData.id }).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: "删除成功"
                        })
                        this.loadData();

                    } else {
                        this.giveType = 2;
                        this.giveMessage = result.msg;
                        this.dialogVisible = true;
                    }
                }).catch((err) => {

                });
            }
        },
        addGive() {
            this.$router.push({
                path: '/give/add',
                query:{
                    type:1
                }
            })
        },
        deleteAction(data) {
            this.giveType = 1;
            this.giveMessage = '是否删除该发放记录?';
            this.dialogVisible = true;
            this.deleteData = data;
        },
        toEdit(data){
            this.$router.push({
                path: '/give/add',
                query:{
                    id:data.id,
                    type:1
                }
            })
        },
        onRevisit(data){
            this.revisitVisible = true;
            this.$nextTick(() => {
                this.$refs.revisitDialog.init(data);
            })
        },
        toDetail(data){
          const path = '/give/samplebook/detail'
            if (!findPath(path)) {
              return this.$message.error('暂无查看权限')
            }
            this.$router.push({
                path: path,
                query: { id: data.id,type:1 } // 机会id
            })
        },
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.loadData();
        },
        downloadAction(){
            this.isDownload = true;
            downloadTemplate({ type: 1}).then((result) => {
                downloadExcelFile(result, `样书发放模版`)
                this.isDownload = false;
            }).catch((err) => {
                this.isDownload = false;
            });
        },
        importAction(){

        },
        exportAction(){
            this.isExport = true;
            distributionDownload(this.pageBean).then((result) => {
                // downloadExcelFile(result, `样书发放模版`)
                downloadFileByUrl(result.data.url,'样书发放数据')
                this.isExport = false;
            }).catch((err) => {
                this.isExport = false;
            });
        },
        submitMsgDialog(){
            this.importData = {};
            this.dialogMsgVisible = false;
        },
        beforeUpload(file) {
            const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
            const whiteList = ['xlsx'];
            if (whiteList.indexOf(fileSuffix) === -1) {
                this.$message.error('导入类型仅支持 .xlsx 格式!');
                return false
            }
            this.isImport = true;
        },
        handleError(file, res) {
            this.isImport = false;
        },
        handleAvatarSuccess(res, file) {
            if (res.status == 0 && res.data.errorCount <= 0) {
                this.$message({
                    type: 'success',
                    message: res.msg
                })
            } else {
                this.$message({
                    type: 'error',
                    message: res.msg
                })
                // 显示错误提示的弹框
                this.dialogMsgVisible = true;
                this.importData = res.data;
            }
            // 刷新数据
            this.pageBean.pageNum = 1;
            this.loadData();
            this.isImport = false;
        },
        cleardata() {
            this.searchdepartmentNames = ''
            this.pageBean.distributionDepartmentIds = ''
            this.selDepartmentData = []
        },
        chooseDepartment(e) {
            this.dialogDepartmentVisible = true
            this.$refs.deRef.loadData()
            this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
        },
        updateSystemVisible(val) {
            this.dialogDepartmentVisible = val
        },
        submitData(data) {
            this.dialogDepartmentVisible = false
            this.selDepartmentData = data
            let departmentIds = data.map((item) => item.id)
            let departmentNames = data.map((item) => item.name)
            this.pageBean.distributionDepartmentIds = departmentIds.join(',')
            this.searchdepartmentNames = departmentNames.join(',')
        },
    }
}
</script>

<style scoped>
.ml20{
    margin-left: 20px !important;
}
.itemlinecss{
    margin-left: -10px !important;
    margin-right: -10px !important;
    border-bottom: 1px solid #F0F0F0;
    padding: 14px 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center !important;
}
.itemlinecss:first-child{
    padding-top: 0px;
}
.itemlinecss:last-child{
    border-bottom: none;
    padding-bottom: 0px;
}
.wi{
    width: 120px;
}
.yesc {
    color: #56C36E;
}

.noc {
    color: #F45961;
}

.mr20 {
    margin-right: 20px;
}

.jhtable .el-button+.el-button {
    margin-left: 20px;
}

.mainbg {
    background-color: white;
    padding: 20px;
    min-height:calc(100vh - 106px);
}

.pcc {
    margin: 0 auto;
    text-align: center;
}

.smtext {
    zoom: 0.8;
}

.fxcenter {
    width: 50px;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.zhiding {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    /* background-color: red; */
}

.mt {
    margin-top: 4px;
}

.cusnamecss {
    display: flex;
}

.tagcss {
    font-size: .625em !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 14px;
    min-width: 30px;
    line-height: 11px;
    border-radius: 2px;
    margin-right: 20px;
}

.genjin {
    color: #4285F4;
    background-color: #DFEAFD;
}

.fuze {
    color: #FEF2E7;
    background-color: #FF8D1A;
}

.xiezuo {
    color: #56C36E;
    background-color: #F3FEF6;
}

.tagcss:nth-child(2n+2) {
    margin-top: 4px;
}

.jhtable /deep/.cell {}

.jhtable .el-button {
    padding: 2px;
}

.mr10 {
    margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
    color: #4285F4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
    color: #F45961;
}

.bumen /deep/.el-form-item__label {
    width: 130px !important;
    margin-left: -45px;

}
.definput {
  width: 200px;
}
.rcenter {
  position: absolute;
  top: 5px;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}
.definput1{
    width: 300px;
    height: 34px !important;
    line-height: 34px !important;
    padding: 0px 12px;
}
.right {
    text-align: right;
}
.fr{

}
</style>
