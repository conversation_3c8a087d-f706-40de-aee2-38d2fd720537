<!-- 配置管理 class类名：configtype -->
<template>
  <div class="configtype app-container">
    <el-row>
      <el-col :span="6">
        <div class="eltree-box">
          <el-form :inline="true">
            <el-form-item>
              <el-input
                placeholder="输入配置类型过滤"
                v-model="filterText"
                prefix-icon="fa fa-search"
                style="width: 50%"
              ></el-input>
              <el-tooltip
                effect="dark"
                :content="'添加一级配置类型'"
                placement="right"
              >
                <el-button
                  v-isShow="'sf:business:configtype:save'"
                  type="primary"
                  size="medium"
                  @click="addOrUpdateConfigTypeHandle()"
                  ><i class="fa fa-plus"></i> 添加</el-button
                >
              </el-tooltip>
            </el-form-item>
          </el-form>
          <section class="eltree">
            <el-tree
              class="filter-tree"
              :data="configtypeData"
              :props="defaultProps"
              ref="configTypeTree"
              accordion
              :highlight-current="true"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :render-content="renderContent"
              @node-click="getTreeNode"
            >
            </el-tree>
          </section>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="grid-content bg-purple" ref="configtypetable">
          <el-table
            :data="dataList"
            border
            stripe
            v-loading="dataListLoading"
            style="width: 100%"
            element-loading-text="加载中..."
            element-loading-spinner="el-icon-loading"
          >
            <el-table-column
              prop="name"
              header-align="center"
              align="center"
              label="配置项名称"
            >
            </el-table-column>
            <el-table-column
              prop="code"
              header-align="center"
              align="center"
              label="配置项编码"
            >
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              width="100px"
              label="控件类型"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.editorClass | dict('CONFIG_NEW') }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="value"
              header-align="center"
              align="center"
              show-overflow-tooltip
              label="配置项值"
            >
            </el-table-column>
            <el-table-column
              prop="configtypeName"
              header-align="center"
              align="center"
              label="配置项类型"
            >
            </el-table-column>
            <el-table-column
              prop="sortNo"
              header-align="center"
              align="center"
              width="70"
              label="排序号"
            >
            </el-table-column>
            <el-table-column
              prop="systemic"
              header-align="center"
              align="center"
              label="是否内置"
            >
              <template slot-scope="scope">
                <el-tag
                  type="success"
                  size="mini"
                  v-if="scope.row.systemic == 1"
                  >系统内置</el-tag
                >
                <el-tag type="info" size="mini" v-if="scope.row.systemic == 0"
                  >自定义</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              header-align="center"
              align="center"
              width="130"
              label="操作"
            >
              <template slot-scope="scope">
                <el-button
                  v-isShow="'sf:business:configitem:update'"
                  type="text"
                  size="mini"
                  icon="el-icon-edit"
                  @click="
                    addOrUpdateConfigItemHandle(
                      scope.row.id,
                      scope.row.configtypeId,
                      scope.row.configtypeName
                    )
                  "
                  >修改</el-button
                >
                <el-button
                  v-isShow="'sf:business:configitem:delete'"
                  type="text"
                  size="mini"
                  @click="
                    deleteConfigItem(scope.row, scope.row.code, scope.row.name)
                  "
                  icon="el-icon-delete"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <!-- 弹窗, 新增 / 修改 -->
    <AddOrUpdateConfigType
      v-if="addOrUpdateConfigTypeVisible"
      ref="addOrUpdateConfigType"
      @refreshConfigTypeData="getConfigTypeData"
    ></AddOrUpdateConfigType>
    <AddOrUpdateConfigItem
      v-if="addOrUpdateConfigItemVisible"
      ref="addOrUpdateConfigItem"
      @refreshConfigItemData="getConfigItemData"
    ></AddOrUpdateConfigItem>
  </div>
</template>

<script>
import AddOrUpdateConfigType from './add-or-update-config-type'
import AddOrUpdateConfigItem from './add-or-update-config-item'
export default {
  components: {
    AddOrUpdateConfigType,
    AddOrUpdateConfigItem,
  },
  data() {
    return {
      dataForm: {
        configtypeId: '',
      },
      configtypeData: [],
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateConfigTypeVisible: false,
      addOrUpdateConfigItemVisible: false,
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'name',
      },
    }
  },
  watch: {
    filterText(val) {
      this.$refs.configTypeTree.filter(val)
    },
  },
  methods: {
    // 配置类型的方法
    // 获取配置类型数据
    async getConfigTypeData() {
      let res = await this.$axios.get('/sf/business/configtype/configTypeTree')
      if (res.status === 0) {
        this.configtypeData = res.data
        this.dataListLoading = false
      }
    },
    // 获取点击的树节点
    getTreeNode(data) {
      this.dataForm.configtypeId = data.id
      this.getConfigItemData()
    },
    // 新增 / 修改
    addOrUpdateConfigTypeHandle(id, parentId, parentName) {
      this.addOrUpdateConfigTypeVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdateConfigType.init(id, parentId, parentName)
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 删除
    deleteConfigType(node, data) {
      let id = data.id
      this.$confirm(`确定要对[${data.name}]进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        let res = await this.$axios.post('/sf/business/configtype/delete', {
          id: id,
        })
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getConfigTypeData()
            },
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    renderContent(h, { node, data, store }) {
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
          <span>
            <el-popover placement="bottom" width="60" trigger="click">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() =>
                    this.addOrUpdateConfigItemHandle('', data.id, data.name)
                  }
                >
                  添加配置项
                </el-button>
              </div>
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() =>
                    this.addOrUpdateConfigTypeHandle(
                      data.id,
                      data.parentId,
                      node.parent.data.name
                    )
                  }
                >
                  编辑
                </el-button>
              </div>
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() => this.deleteConfigType(node, data)}
                >
                  删除
                </el-button>
              </div>
              <div>
                <el-button
                  size="mini"
                  type="text"
                  on-click={() =>
                    this.addOrUpdateConfigTypeHandle('', data.id, data.name)
                  }
                >
                  添加下级
                </el-button>
              </div>
              <el-button slot="reference" type="text" size="mini">
                <div class="tree-icon">
                  <i class="fa fa-cog"></i>
                </div>
              </el-button>
            </el-popover>
          </span>
        </span>
      )
    },
    // 配置类型对应的配置项
    // 获取数据列表
    async getConfigItemData() {
      this.dataListLoading = true
      let data = {
        configtypeId: this.dataForm.configtypeId,
      }
      let res = await this.$axios.get('/sf/business/configitem/listAll', {
        params: data,
      })
      if (res.status === 0) {
        this.dataList = res.data
      } else {
        this.dataList = []
      }
      this.dataListLoading = false
    },
    // 新增 / 修改
    addOrUpdateConfigItemHandle(id, configtypeId, configtypeName) {
      this.addOrUpdateConfigItemVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdateConfigItem.init(id, configtypeId, configtypeName)
      })
    },
    // 删除
    deleteConfigItem(scope, code, name) {
      if (scope.systemic === 1) {
        this.$message.success('系统内置不允许删除')
        return
      }
      this.$confirm(`确定对[${name}]进行[删除]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        let res = await this.$axios.get('/sf/business/configitem/delete', {
          params: { code: code },
        })
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getConfigItemData()
            },
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  },
  mounted() {
    this.$refs.configtypetable.style.height =
      Number(window.innerHeight) - 200 + 'px'
  },
  created() {
    this.getConfigTypeData()
  },
}
</script>
<style>
.el-tooltip__popper.is-dark {
  max-width: 700px;
}
</style>
<style lang="scss" scoped>
.filter-tree {
  font-size: 14px;
}
.eltree-box {
  padding: 0 20px 0 0;
}
.grid-content {
  overflow-y: scroll;
}
</style>
