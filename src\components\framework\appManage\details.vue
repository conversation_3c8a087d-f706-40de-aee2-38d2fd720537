<template>
  <div>
    <BaseDialog :title="subTitle" :isshow.sync="visible" width="800px" height="auto">
      <el-form label-width="140px" class="baseDialogClass">
        <el-form-item label="应用：" prop="appCode">{{ruleForm.appCode | dict('APPLICATION') }}</el-form-item>
        <div class="clearfix">
          <el-form-item label="版本名称：" class="pull-left">
            <span>V{{ruleForm.versionFirst}}.{{ruleForm.versionSecond}}.{{ruleForm.versionThird}}</span>
          </el-form-item>
        </div>
        <el-form-item label="最小更新版本号：">{{ruleForm.miniCode}}</el-form-item>
        <el-form-item label="版本号：">{{ruleForm.versionCode}}</el-form-item>
        <el-form-item label="升级包地址：">{{ruleForm.upgradeUrl}}</el-form-item>
        <el-form-item label="升级描述：">
          <span class="descriptionClass" v-html="ruleForm.description"></span>
        </el-form-item>
        <!-- <el-form-item label="强制升级：">
          <span v-if="ruleForm.forceUpgrade===1">是</span>
          <span v-else>否</span>
        </el-form-item> -->
        <el-form-item label="状态：">
          <span v-if="ruleForm.isEnabled===1">启用</span>
          <span v-else>禁用</span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </BaseDialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/base/BaseDialog.vue";
export default {
  data() {
    return {
      isDisabled: false,
      subTitle: "详情",
      visible: false,
      ruleForm: {},
      appId: ""
    };
  },
  methods: {
    init(obj) {
      this.visible = true;
      // this.ruleForm = obj
      this.ruleForm = Object.assign({}, obj);
    }
  },
  components: {
    BaseDialog
  }
};
</script>

<style lang="scss" scoped>
.baseDialogClass {
  overflow-y: scroll;
  height: 540px;
  padding: 20px;
}
.dialog-footer {
  display: block;
  text-align: right;
  padding: 20px;
}
.descriptionClass {
  line-height: 1.5;
  display: inline-block;
}
</style>
