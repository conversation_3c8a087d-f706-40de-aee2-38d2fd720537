<template>
  <div>
    <div>
        <back>返回</back>
    </div>
    <el-card class="mt10">
        <div v-loading="isLoading">
          <el-tabs v-model="activeName" @tab-click="handleClick"  class="tabscss">
            <el-tab-pane label="投入明细" name="first" >
            </el-tab-pane>
            <el-tab-pane   label="产出明细" name="second" >
            </el-tab-pane>
          </el-tabs>
            <el-form :inline="true" label-width="85px" class="myform ">
                        <el-form-item v-if="dataScope==3 || dataScope ==4" label="部门：" label-width="60px" >
                            <el-input class="definput iw" v-model="pageBean.departmentName" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                        <el-form-item v-if="dataScope==3 || dataScope ==4 || dataScope ==2" label="业务经理：">
                            <el-input class="definput wfi" v-model="pageBean.operationName" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                        <el-form-item v-if="activeName == 'first'" label="类型：" label-width="60px">
                          <el-select class="definput wi" popper-class="removescrollbar" v-model="pageBean.type" clearable
                          placeholder="请选择">
                          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                          </el-option>
                      </el-select>
                        </el-form-item>
                        <el-form-item label="时间：" label-width="60px">
                          <el-date-picker
                            popper-class="elpicker"
                            :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd"
                            @focus="isShow($event)"
                            class="elpicker"
                            v-model="defaultYear"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                          </el-date-picker>
                      </el-form-item>
                        <el-button class="defaultbtn mb10 "  type="primary" @click="searchAction">搜索
                        </el-button>
            </el-form>
            <el-table key="table1" v-if="activeName == 'first'" class="customertable mytable tootiptable" :data="tableData" >
                <el-table-column :formatter="formatter" prop="price" label="金额" align="center"  >
                </el-table-column>
                <el-table-column prop="reason" label="事由" align="center" >
                </el-table-column>
                <el-table-column  label="类型" align="center" >
                  <template slot-scope="scope">
                    {{typeObj[scope.row.type]}}
                  </template>
                </el-table-column>
                <el-table-column prop="operationName" label="业务经理" align="center"  >
                </el-table-column>
                <el-table-column prop="distributionDepartmentName" label="部门" align="center"  >
                </el-table-column>
                <el-table-column prop="distributionTime" label="时间" align="center"  >
                </el-table-column>
                <template slot="empty">
                    <nolist></nolist>
                </template>
            </el-table>
            <el-table key="table2" v-if="activeName == 'second'" class="customertable mytable tootiptable" :data="tableDataOut" >
              <el-table-column :formatter="formatter1" prop="contractAmount" label="产出金额" align="center"  >
              </el-table-column>
              <el-table-column prop="contractTitle" label="合同" align="center" >
              </el-table-column>
              <el-table-column prop="chargePersonName" label="业务经理" align="center"  >
              </el-table-column>
              <el-table-column prop="contractDepartmentName" label="部门" align="center" >
              </el-table-column>
              <el-table-column prop="beginTime" label="时间" align="center"  >
              </el-table-column>
              <template slot="empty">
                  <nolist></nolist>
              </template>
          </el-table>
            <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
                @updatePageNum="handleCurrentChange"></page>
        </div>
  </el-card>
</div>
</template>
<script>
  import page from '../../common/page.vue';
  import nolist from '../../common/nolist.vue';
  import back from '../../common/back.vue';
  import { queryDistributionDate, queryContractOutDate } from "@/api/businessdata/index";
  export default {
      components: {
          page,
          nolist,
          back
      },
      watch: {
        'defaultYear'(newVal) {
          if (newVal == null) {
            this.defaultYear = this.copyYear
          }
        }
      },

      data() {
          return {
              activeName: 'first',
              isLoading: false,
              tableData: [],
              tableDataOut:[],
              total: 0,
              pageBean: {
                  pageNum: 1,
                  pageSize: 10,
                  type:'',
                  departmentName:'',
                  operationName:'',
                  startTime:'',
                  endTime:'',
                  customerId:'',
              },
              dataScope:window.sessionStorage.getItem('dataScope'),
              defaultYear:[this.$route.query.year + '-01-01',this.$route.query.year + '-12-31'],
              copyYear:[this.$route.query.year + '-01-01',this.$route.query.year + '-12-31'],
              options: [{
                  value: "1",
                  label: '样书',
              }, {
                  value: "2",
                  label: '礼品',
              },
              {
                  value: "3",
                  label: '商务活动',
              }],
              typeObj:{
                  1:'样书',
                  2:'礼品',
                  3:'商务活动',
              },
              isAdd:false,
              pickerOptions: {
                disabledDate: (time) => {
                    const [start, end] = this.copyYear
                    const timer = new Date(time).getTime()+ 8.64e7
                    const timer1 = new Date(time).getTime()
                    return timer < new Date(start) || timer1 > new Date(end)
                },
            },
          }
      },
      created() {
          this.pageBean.customerId = this.$route.query.customerId
          this.pageBean.startTime = this.defaultYear[0]
          this.pageBean.endTime = this.defaultYear[1]
          this.loadData();
      },
      mounted(){
      },
      methods: {
        formatter(row, column, cellValue) {
          return cellValue + "元";
        },
        formatter1(row, column, cellValue){
          return cellValue + "万元";
        },
        isShow (e) {
           this.$nextTick(()=>{
            let dsubNum = document.getElementsByClassName("is-left")[0].firstChild.lastChild.innerHTML.substring(7,9)
            if(dsubNum<=1){
              console.log(document.getElementsByClassName("is-left")[0].firstChild.lastChild.previousElementSibling)
              document.getElementsByClassName("is-left")[0].firstChild.lastChild.previousElementSibling.setAttribute('style','cursor:not-allowed')
              document.getElementsByClassName("is-left")[0].firstChild.lastChild.previousElementSibling.disabled = 'disabled';
            }
            var leftArrow = document.getElementsByClassName("el-icon-arrow-left");
            var rightArrow = document.getElementsByClassName("el-icon-arrow-right");
            if(!this.isAdd){
              this.isAdd = true
              leftArrow[1].addEventListener('click', (event) => {
                document.getElementsByClassName("is-right")[0].firstChild.lastChild.previousElementSibling.setAttribute('style','cursor:pointer')
              document.getElementsByClassName("is-right")[0].firstChild.lastChild.previousElementSibling.disabled = false
                let dateNum = event.srcElement.nextElementSibling.innerHTML
                let subNum = dateNum.substring(7,9)
                if(subNum<=1){
                  event.srcElement.setAttribute('style','cursor:not-allowed')
                  event.srcElement.disabled = 'disabled';
                }
              })
              rightArrow[1].addEventListener('click', (event) => {
                document.getElementsByClassName("is-left")[0].firstChild.lastChild.previousElementSibling.setAttribute('style','cursor:pointer')
              document.getElementsByClassName("is-left")[0].firstChild.lastChild.previousElementSibling.disabled = false;
                let dateNum = event.srcElement.nextElementSibling.innerHTML
                let subNum = dateNum.substring(7,9)
                if(subNum>=12){
                  event.srcElement.setAttribute('style','cursor:not-allowed')
                  event.srcElement.disabled = 'disabled';
                }
              })
            }
           })
        },
          handleClick(tab, event) {
            var tabName = tab.name;
            this.pageBean.departmentName = ''
            this.pageBean.operationName = ''
            this.pageBean.type = ''
            this.defaultYear = this.copyYear
            this.pageBean.startTime = this.defaultYear[0]
            this.pageBean.endTime = this.defaultYear[1]
            if(tabName=='first'){
              this.pageBean.pageNum = 1;
              this.loadData();
            }else{
              this.pageBean.pageNum = 1;
              this.loadDataOutput();
            }
          },
          loadData() {
              this.isLoading = true;
              queryDistributionDate(this.pageBean).then((result) => {
                  this.tableData = result.data ? result.data : [];
                  this.total = result.data ? result.page.total : 0
                  this.isLoading = false;
              }).catch((err) => {
                  this.isLoading = false;
              });
          },
          loadDataOutput() {
              this.isLoading = true;
              queryContractOutDate(this.pageBean).then((result) => {
                  this.tableDataOut = result.data ? result.data : [];
                  this.total = result.data ? result.page.total : 0
                  this.isLoading = false;
              }).catch((err) => {
                  this.isLoading = false;
              });
          },
          searchAction() {
              this.pageBean.startTime = this.defaultYear[0]
              this.pageBean.endTime = this.defaultYear[1]
              this.pageBean.pageNum = 1;
              if(this.activeName=='first'){
                this.loadData();
              }else{
                this.loadDataOutput();
              }
          },
          handleCurrentChange(page) {
              this.pageBean.pageNum = page;
              if(this.activeName=='first'){
                this.loadData();
              }else{
                this.loadDataOutput();
              }
          },
      }
  }
</script>
<style>
  .elpicker .el-icon-d-arrow-left {
    display: none;
  }
  .elpicker  .el-icon-d-arrow-right:before {
    content: none;
  }
</style>
<style scoped>

  .mt10{
    margin-top: 10px;
  }
  .tabscss /deep/ .el-tabs__item{
    width: 120px;
    text-align: center;
  }
  .elpicker{
    height: 34px;
  }
  .elpicker /deep/.el-range-separator{
      line-height: 26px;
  }
  .elpicker /deep/.el-range__icon{
    position: relative;
    top: -2px;
  }
  .elpicker{
      width: 300px;
  }
  .mb10{
    margin-bottom: 10px;
  }
  .pscss{
    position: absolute;
    right: 9px;
    top: 10px;
    color: #C0C4CC;
  }
  .guang /deep/.el-input__inner{
    line-height: 1px !important;
  }
  .wi{
    width: 120px;
  }
  .wcss{
      width: 280px;
  }
  .cDialog /deep/.el-dialog__body{
    display: flex;
    justify-content: center;
    align-content: center;
  }
  .cDialog /deep/.el-dialog{
    width: 580px !important;
  }
  .iw{
      width: 180px;
      height: 34px;
  }
  .iw /deep/.el-input__inner{
      padding-right: 6px;
  }
  .w100{
    width: 100px;
  }
  .wfi{
    width: 180px;
  }
  .mt {
      margin-top: 4px;
  }
  .mr10 {
      margin-right: 10px;
  }
  .right {
      text-align: right;
  }
   .defaultbtn + .defaultbtn{
    margin-right: 10px;
   }
   .ml{
    margin-left: 10px;
   }
</style>