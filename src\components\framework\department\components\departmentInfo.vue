<template>
    <div>
        <div class="topTitle">
            <div class="line"></div>
            <div class="title">基本信息</div>
        </div>
        <div class="content">
            <div class="item">部门名称：{{ departmentDetail.name || '-' }}</div>
            <div class="item">上级部门：{{ departmentDetail.parentDepartmentName || '-' }}</div>
            <div class="item">备注：{{ departmentDetail.remark || '-' }}</div>
        </div>
    </div>
</template>
<script>
export default {
    components: {
    },
    data() {
        return {};
    },
    props: {
        departmentDetail: {
            type: Object,
            default: () => ({})
        }
    },
    methods: {}
};
</script>

<style lang="scss" scoped>
.topTitle{
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
    background: #f0f3fa;
    border-radius: 4px;
    padding: 0 16px;
    margin-bottom: 20px;
}
.line{
    width: 3px;
    height: 14px;
    background-color: #386cfc;
    margin-right: 12px;

}
.title{
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2d2f33;
    text-align: left;
    font-style: normal;
    text-transform: none;
}
.item{
    height: 21px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #2e2f33;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 12px;
}
</style> 