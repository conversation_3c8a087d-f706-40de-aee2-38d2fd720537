<template>
  <div class="maskbg" v-if="isShow">
    <div class="maincss" v-loading="isLoading">
      <div class="pd20">
        {{ name }}
      </div>
      <iframe class="filecss" @load="loaded" :src="url"></iframe>
      <img class="closebtn" src="@/assets/white_close.png" @click="close" />
    </div>
  </div>
</template>

<script>
import { fileInfo } from '@/api/index'
export default {
  data() {
    return {
      name: '',
      url: '',
      isShow: false,
      isLoading: false,
    }
  },
  methods: {
    show(item) {
      this.name = item.fileName
      this.isLoading = true
      var par = {}
      if (item.id) {
        par = {
          id: item.id,
        }
      } else {
        par = { url: item.url }
      }
      fileInfo(par)
        .then((result) => {
          this.url = result.data.url
          this.isShow = true
        })
        .catch((err) => {})
    },
    loaded() {
      this.isLoading = false
    },
    close() {
      this.isShow = false
    },
  },
}
</script>

<style lang="scss" scoped>
.pd20 {
  padding-bottom: 16px;
}
.maskbg {
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  background-color: rgba($color: #000000, $alpha: 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}
.maincss {
  width: 60vw;
  height: 90vh;
  background-color: white;
  padding: 16px;
  position: relative;
  border-radius: 8px;
}
.filecss {
  width: 100%;
  height: calc(100% - 40px);
}
.closebtn {
  width: 24px;
  height: 24px;
  position: absolute;
  top: -10px;
  right: -10px;
}
</style>