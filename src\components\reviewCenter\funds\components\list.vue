<template>
  <div class="mainbg fixpb">
    <div class="headerdiv">
      <el-form :inline="true" class="myform clearfix">
        <el-form-item label="">
          <el-input
            v-model="pageBean.contractKeywords"
            class="definput inputWid150"
            clearable
            placeholder="合同名称或编号"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="pageBean.unitName"
            class="definput inputWid150"
            s
            clearable
            placeholder="请输入客户单位"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="pageBean.applicantName"
            class="definput inputWid150"
            clearable
            placeholder="申请人"
          >
          </el-input>
        </el-form-item>

        <el-form-item label-width="85px" label="合同类型：">
          <el-select
            @change="changeValue"
            clearable
            class="definput w1"
            popper-class="removescrollbar"
            v-model="pageBean.contractType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in optionsContract"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            clearable
            class="definput inputWid150 ml ellipsis"
            popper-class="removescrollbar"
            v-model="pageBean.contractTypeSub"
            placeholder="请选择"
          >
            <el-option
              v-for="item in contractSubTypes"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="状态" v-if="handleType === 2">
          <el-select
            v-model="pageBean.status"
            class="definput inputWid150"
            popper-class="removescrollbar"
            clearable
            placeholder="请选择状态"
          >
            <el-option
              v-for="item in reviewStatus"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            @click="search"
            class="defaultbtn mt"
            icon="el-icon-search"
            type="primary"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-table class="mytable" :data="tableData" style="width: 100%">
      <el-table-column prop="contractName" label="合同名称" align="center" />
      <el-table-column prop="contractNumber" label="编号" align="center" />
      <el-table-column prop="contractTypeName" label="类型" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.contractTypeName }}</span>
          <span v-if="scope.row.contractTypeItem">{{
            '-' + scope.row.contractTypeItem
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="unitName" label="单位" align="center" />
      <el-table-column
        prop="contractAmount"
        label="合同金额(万元)"
        align="center"
      />
      <el-table-column
        prop="amount"
        label="回款金额(万元)"
        align="center"
        width="167"
      />
      <el-table-column
        prop="status"
        label="状态"
        v-if="handleType == 2"
        align="center"
        width="167"
      >
        <template slot-scope="scope">
          <span
            class="circle"
            :class="{
              'c-four': scope.row.status == 4,
              'c-three': scope.row.status == 3,
              'c-two': scope.row.status == 2,
              'c-one': scope.row.status == 0,
            }"
          ></span>
          {{ reviewStatusMap[scope.row.status] || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="applicantName"
        label="申请人"
        align="center"
        width="167"
      />
      <el-table-column
        prop="taskDepartmentName"
        label="申请人部门"
        align="center"
        width="167"
      />
      <el-table-column
        prop="applyTime"
        label="申请时间"
        align="center"
        width="167"
      />
      <el-table-column
        prop="edit"
        width="200"
        align="center"
        fixed="right"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            class="bbtn tabbtn"
            type="text"
            v-if="handleType == 2"
            @click="reviewAction(scope.row)"
          >
            详情</el-button
          >
          <el-button
            class="bbtn tabbtn"
            type="text"
            v-else
            @click="reviewAction(scope.row)"
          >
            审核</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>
    <review-drawer
      ref="reviewDrawer"
      v-model="isShow"
      :data="reviewData"
      @reload="loadData"
      :handleType="handleType"
    ></review-drawer>
  </div>
</template>

<script>
import page from '@/components/common/page'
import { reviewStatus, reviewStatusMap } from '@/utils/dict'
import { queryVoListByCode } from '@/api/wapi.js'
import { queryContractReturnList } from '@/api/reviewCenter'
import reviewDrawer from './reviewDrawer.vue'
export default {
  components: {
    page,
    reviewDrawer,
  },
  props: {
    handleType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      total: 0,
      reviewStatus,
      reviewStatusMap,
      isShow: false,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        applicantName: '',
        contractKeywords: '',
        unitName: '',
        status: '',
        contractType: '',
        contractTypeSub: '',
        ruleType: 2,
      },
      optionsContract: [],
      contractSubTypes: [],
      reviewData: {},
      tableData: [],
    }
  },

  created() {
    this.pageBean.handleType = this.handleType
    this.getDictApi()
  },
  methods: {
    setValue(val) {
      let item = this.optionsContract.filter((item) => item.id === val)
      this.contractSubTypes = item[0].children
    },
    getDictApi() {
      queryVoListByCode({ code: 'ContractType' }).then((res) => {
        if (res.status == 0 && res.data.length > 0) {
          this.optionsContract = res.data
          if (this.pageBean.contractType) {
            this.setValue(this.pageBean.contractType)
          }
        }
      })
    },

    changeValue(val) {
      if (val == '') {
        this.pageBean.contractTypeSub = ''
        this.contractSubTypes = []
      } else {
        this.pageBean.contractTypeSub = ''
        let item = this.optionsContract.filter((item) => item.id === val)
        this.contractSubTypes = item[0].children
      }
    },
    loadData() {
      queryContractReturnList(this.pageBean)
        .then((res) => {
          this.tableData = res.data || []
          this.total = res.page.total
        })
        .catch((err) => {})
    },
    search() {
      console.log('search')
      this.pageBean.pageNum = 1
      this.loadData()
    },
    reload() {
      this.pageBean.handleType = this.handleType
      Object.keys(this.pageBean).forEach((item) => {
        if (item == 'pageNum') {
          this.pageBean.pageNum = 1
        } else if (
          item != 'pageSize' &&
          item != 'ruleType' &&
          item != 'handleType'
        ) {
          this.pageBean[item] = ''
        }
      })
      this.loadData()
    },
    handleCurrentChange(pageNum) {
      console.log(pageNum)
      this.pageBean.pageNum = pageNum
      this.loadData()
    },
    reviewAction(data) {
      this.isShow = true
      this.$nextTick(() => {
        this.$refs.reviewDrawer.loadinfo()
      })
      this.reviewData = data
    },
  },
}
</script>
<style lang="scss" scoped>
.bbtn {
  cursor: pointer;
}

.w1 {
  width: 120px;
}

.ml {
  margin-left: 10px;
}
</style>
