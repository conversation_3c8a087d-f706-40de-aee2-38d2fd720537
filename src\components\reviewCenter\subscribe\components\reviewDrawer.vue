<template>
  <el-drawer
    class="mydrawer"
    :visible.sync="visible"
    direction="rtl"
    size="50%"
    title="审核"
    @close="handleClose"
    center
  >
    <div class="drawer-content">
      <el-table :data="[data]" style="width: 100%" class="mytable">
        <el-table-column
          prop="materialSubscriptionVo.materialName"
          label="教材名称"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.isbn"
          label="ISBN"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.platformName"
          label="出版社"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.unitName"
          label="用书单位"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.specialtyName"
          label="用书专业"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.customerName"
          label="客户"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.useBookYear"
          label="用书时间"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.price"
          label="教材定价(元)"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.reserveNum"
          label="预定数量"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.actualNum"
          label="实报数量"
          align="center"
          width="167"
        />
        <el-table-column
          prop="materialSubscriptionVo.discount"
          label="折扣"
          align="center"
          width="167"
        />
      </el-table>
      <el-form class="infoform" ref="form" label-width="120px">
        <el-row
          :gutter="0"
          class="width100 pb12 mb20 bbline"
          type="flex"
          justify="center"
        >
          <el-col :span="12">
            <el-form-item label="报订人：" class="labeltext">
              <span>{{ data.applicantName || '--' }}</span>
            </el-form-item>
            <el-form-item label="报批时间：" class="labeltext">
              <span>{{ data.applyTime || '--' }}</span>
            </el-form-item>
            <el-form-item label="中标公司：" class="labeltext">
              <span>{{
                data.materialSubscriptionVo?.bidwinnerName || '--'
              }}</span>
            </el-form-item>
            <el-form-item label="货源：" class="labeltext">
              <span>{{
                data.materialSubscriptionVo?.sourceGoods || '--'
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完成率：" class="labeltext">
              <span>{{
                data.materialSubscriptionVo?.completionRate || '--'
              }}</span>
            </el-form-item>
            <el-form-item label="报订码洋(万元)" class="labeltext">
              <span
                >{{
                  data.materialSubscriptionVo?.reserveRetailPrice || '--'
                }}
                万元</span
              >
            </el-form-item>
            <el-form-item label="实际码洋(万元)" class="labeltext">
              <span
                >{{
                  data.materialSubscriptionVo?.actualRetailPrice || '--'
                }}万元</span
              >
            </el-form-item>
            <el-form-item label="备注：" class="labeltext">
              <span>{{ data.materialSubscriptionVo?.notes || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="checkpeople">
          <span class="spanname">审核意见：</span>
          <eltimeline
            :businessId="data.businessId"
            :status="data.status"
            v-if="visible"
          ></eltimeline>
        </div>
      </el-form>
      <div class="btns" v-if="handleType === 1">
        <el-button @click="handleReject">驳回</el-button>
        <el-button type="primary" @click="handleApprove">通过</el-button>
      </div>
    </div>

    <!-- 通过对话框 -->
    <approve-dialog
      :visible.sync="approveDialogVisible"
      @submit="onApproveSubmit"
    ></approve-dialog>

    <!-- 驳回对话框 -->
    <reject-dialog
      :visible.sync="rejectDialogVisible"
      @submit="onRejectSubmit"
    ></reject-dialog>
  </el-drawer>
</template>

<script>
import ApproveDialog from '../../common/approveDialog.vue'
import RejectDialog from '../../common/rejectDialog.vue'
import { approve } from '@/api/reviewCenter'
import eltimeline from '@/components/common/eltimeline.vue'
export default {
  name: 'ReviewDrawer',
  components: {
    ApproveDialog,
    RejectDialog,
    eltimeline,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    handleType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: () => {
        return new Object()
      },
    },
  },
  data() {
    return {
      businessId: '',
      approveDialogVisible: false,
      rejectDialogVisible: false,
      tableData: [],
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  methods: {
    handleClose() {
      this.visible = false
    },
    handleApprove() {
      this.approveDialogVisible = true
    },
    handleReject() {
      this.rejectDialogVisible = true
    },
    onApproveSubmit() {
      this.submit({
        id: this.data.id,
        status: 3,
      })
    },
    onRejectSubmit(data) {
      console.log('驳回提交的数据', data)
      this.submit({
        id: this.data.id,
        status: 4,
        remark: data,
      })
    },

    submit(par) {
      approve(par)
        .then((result) => {
          if (result.data) {
            this.visible = false
            if (par.status == 3) {
              this.msgSuccess('审核通过')
            } else {
              this.msgSuccess('已驳回')
            }
            this.$emit('reload')
          } else {
            this.$message.error(result.msg)
          }
        })
        .catch((err) => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
}
.boxItem {
  display: flex;
  align-items: center;
  line-height: 18px;
}
.title {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  margin-bottom: 20px;
}
/deep/ .el-drawer {
  .el-drawer__header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }
}
.mydrawer /deep/.el-drawer__header {
  text-align: center;
  color: #333333;
}
.btns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.mb20 {
  margin-bottom: 20px;
}
.spanname {
  width: 110px;
  padding-right: 8px;
  text-align: right;
  flex-shrink: 0;
}
.checkpeople {
  display: flex;
  margin-left: 6px;
}
</style>
