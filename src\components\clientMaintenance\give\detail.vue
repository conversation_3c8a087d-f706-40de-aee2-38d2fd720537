<template>

  <div>
    <back>发放详情</back>
    <div class="mainbg" v-loading="isLoading">
        <el-form class="infoform" ref="form" :model="form" label-width="120px">
          <textBorder>基本信息</textBorder>
          <div class="mb30 bbline">
            <el-row :gutter="20" class="width100 mt10 pb5 " >
                <el-col :span="8">
                    <el-form-item label="发放事由:" class="labeltext">
                        <span>{{form.reason}}</span>
                    </el-form-item>
                    <el-form-item label="客户单位:" class="labeltext">
                    <span>{{form.unitName}}</span>
                    </el-form-item>
                    <el-form-item label="是否回访:" class="labeltext">
                    <span>{{form.isRevisit ? '是' : '否'}}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">  
                    <el-form-item label="发放类型:" class="labeltext">
                        <span>{{types[form.type]}}</span>
                    </el-form-item>
                    <el-form-item label="发放单号:" class="labeltext">
                        <span >{{form.orderNumber}}</span> 
                    </el-form-item>
                </el-col>
                <el-col :span="8">   
                    <el-form-item label="发放客户:" class="labeltext">
                        <span>{{form.customerName}}</span>
                    </el-form-item>
                    <el-form-item label="发放时间:" class="labeltext">
                        <span>{{form.distributionTime}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
          </div>

          <textBorder>补充信息</textBorder>
          <div class="mb30 bbline">
            <el-row :gutter="20" class="width100 mt10  pb15" >
                <el-col :span="24">
                    <el-form-item label="备注:" class="labeltext">
                        <span>{{form.notes}}</span>
                    </el-form-item>
                </el-col>
            </el-row>
          </div>

          <textBorder>系统信息</textBorder>
          <el-row :gutter="20" class="width100 mt10 pb15">
              <el-col :span="8">
                  <el-form-item label="创建人:" class="labeltext">
                      <span>{{form.createByName}}</span>
                  </el-form-item>
                  <el-form-item label="最后修改时间:" class="labeltext">
                      <span>{{form.modifyTime}}</span>
                  </el-form-item>
              </el-col>
              <el-col :span="8">
                  <el-form-item label="最后修改人:" class="labeltext">
                      <span>{{form.modifyByName}}</span>
                  </el-form-item>
              </el-col>
              <el-col :span="8">
                  <el-form-item label="创建时间:" class="labeltext">
                      <span>{{form.createTime}}</span>
                  </el-form-item>
              </el-col>
          </el-row>
      </el-form>
    </div>

  </div>
</template>

<script>
 import textBorder from '@/components/common/textBorder.vue'
 import back from '@/components/common/back.vue';
 import { distributionInfo } from '@/api/clientMaintenance/give';
  export default {
    components:{
      back,
      textBorder,
    },
    data(){
     return {
        colors:[],
        isLoading:false,
        form: {
            id:"",
            reason:"",
            type:"",
            customerName:"",
            unitName:"",
            orderNumber:"",
            distributionTime:"",
            isRevisit:false,
            notes:"",
            createByName:"",
            modifyByName:"",
            createTime:"",
            modifyTime:""
        },
        types:{
                1:'样书',
                2:"礼品"
            },
     }
    },
    created(){
        this.loadData();
    },
    methods:{
        loadData(){
            this.isLoading = true;
            distributionInfo(this.$route.query.id).then((result) => {
                this.form = result.data;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        }
    }

  }
</script>

<style lang="scss" scoped>
.pb15{
    padding-bottom: 15px;
}
.filebg{
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  padding: 3px 10px;
  color: #4285F4;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #4285F4;
  cursor: pointer;

}
.mainbg{
    min-height: calc(100vh - 150px);
    margin: 16px 0px;
    padding: 20px 16px;
    padding-bottom: 0px;
    background-color: white;
    border-radius: 8px;
}
.colorcss{
    color:#F45961;
}
.tagitemcss{
    font-size: 12px;
    padding:4px 8px;
    margin: 0px 4px;
    background-color: #DFEAFD;
    border-radius: 2px;
    color: #4285F4;
}
.mtop20{
  margin-top: 20px;
}
.ml40{
    margin-left: 40px;
}
</style>
<style scoped>
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}
.mulitline /deep/.el-form-item__content{
    padding-top: 10px;
    line-height: 20px;
}

</style>