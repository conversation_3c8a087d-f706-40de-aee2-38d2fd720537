<template>
    <div class="rankcss">
        <div class="flex">
            <img class="img24" v-if="sort<4" :src="srtImgs[sort]" alt="">
            <span class="text24" v-else>{{sort}}</span>
            <headuser class="rankhead" :url="itemData.userLogo" :username="itemData && itemData.createByName" width="40"></headuser>
            <span class="deffont">{{itemData && itemData.createByName}}</span>
        </div>
        <div class="right tcss">{{ rankType==1?(itemData.totalTime+'分钟'):(itemData && itemData.totalScore+'积分')}}</div>
    </div>
</template>

<script>
import headuser from '../../../common/headuser.vue';
export default {
    props:{
        itemData:{
            type:Object,
            default:()=>{},
        },
        sort:{
            type:Number,
            default:0
        },
        rankType:{
            type:Number,
            default:1
        }
    },
    components:{
        headuser
    },
    data(){
        return{
            defImg:require('../../../../assets/touxiang.png'),
            srtImgs:{
                1:require('../../../../assets/img/rank_1.png'),
                2:require('../../../../assets/img/rank_2.png'),
                3:require('../../../../assets/img/rank_3.png'),
            }
        }
    },
}
</script>

<style scoped>
.tcss{
    /* margin-top: -0px; */
    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #999999;


}
.text24{
    width: 24px;
    padding-top: 3px;
    text-align: center;
    font-size: 16px;
    font-family: Roboto-Medium, Roboto;
    font-weight: 500;
    color: #666666;
    margin-right: 12px;

}
.flex{
  display: flex;
  align-items: center;
}
.img24{
  width: 24px;
  height: 24px;
  margin-right: 12px;
}
.rankhead{
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-left: 8px;
  margin-right: 12px;
}
.rankcss{
  height: 56px;
  padding: 0px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

</style>