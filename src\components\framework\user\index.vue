<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-tree
            :data="deptOptionsLeft"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
            class="lefttree"
            :highlight-current="true"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="用户账号" prop="username">
            <el-input
              v-model="queryParams.loginAccount"
              placeholder="请输入用户账号"
              clearable
              size="small"
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item
            label="状态"
            prop="status"
            v-isShow="'sf:business:userinfo:updateEnable'"
          >
            <el-select
              v-model="queryParams.status"
              placeholder="用户状态"
              clearable
              size="small"
              style="width: 240px"
              v-isShow="'sf:business:userinfo:updateEnable'"
            >
              <el-option
                v-for="dict in statusOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              size="small"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-isShow="'sf:business:userinfo:save'"
              >新增</el-button
            >
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-isShow="'sf:business:userinfo:delete'"
              >删除</el-button
            >
          </el-col> -->
          <!-- <el-col :span="1.5">
            <el-button
              type="info"
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-hasPermi="['system:user:import']"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:user:export']"
            >导出</el-button>
          </el-col> -->
        </el-row>

        <el-table
          v-loading="loading"
          :data="userList"
          @selection-change="handleSelectionChange"
          style="margin-top: 10px"
        >
          <el-table-column type="selection" width="40" align="center" />
          <el-table-column
            label="用户帐号"
            align="center"
            prop="loginAccount"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="用户类型"
            align="center"
            prop="userType"
            :formatter="formatUserType"
          />
          <el-table-column
            label="用户归属"
            align="center"
            prop="departmentName"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="状态" align="center">
            <template slot-scope="scope">
              <el-switch
                v-isShow="'sf:business:userinfo:updateEnable'"
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="5"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            width="160"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="180"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-isShow="'sf:business:userinfo:update'"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-isShow="'sf:business:userinfo:delete'"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户帐号">
              <el-input
                v-model="form.loginAccount"
                placeholder="请输入用户帐号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="form.id == undefined"
              label="用户密码"
              prop="password"
            >
              <el-input
                v-model="form.password"
                placeholder="请输入用户密码"
                type="password"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="用户类型" prop="userType">
              <el-select v-model="form.userType" placeholder="请选择">
                <el-option
                  v-for="dict in userTypeArr"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户应用" prop="applyType">
              <el-select
                @change="changeuser"
                v-model="form.applyType"
                placeholder="请选择应用"
              >
                <el-option
                  v-for="dict in applyArr"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户归属" prop="department_id">
              <el-select
                v-model="form.departmentId"
                placeholder="请选择用户归属"
              >
                <el-option
                  v-for="dict in useridentifyArr"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox
            v-model="upload.updateSupport"
          />是否更新已经存在的用户数据
          <el-link type="info" style="font-size: 12px" @click="importTemplate"
            >下载模板</el-link
          >
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  exportUser,
  resetUserPwd,
  changeUserStatus,
  importTemplate,
  listApply,
  listAllUser,
} from '@/api/framework/user'
import { applicationList } from '@/api/framework/application'
import { getToken } from '@/utils/auth'
import { listDept } from '@/api/framework/dept'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import md5 from 'md5'
import { isPasswordValid } from '@/utils/coding-utils'
export default {
  name: 'User',
  components: { Treeselect },
  data() {
    const pwdValidator = (rule, value, callback) => {
      console.log('value', value)
      if (value != undefined || isPasswordValid(value)) {
        callback()
      } else {
        callback(new Error('请输入6-18位字符'))
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      deptOptionsLeft: [],
      checkedtree: [],
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [
        {
          value: 1,
          label: '正常',
        },
        {
          value: 5,
          label: '停用',
        },
      ],
      // 性别状态字典
      sexOptions: [
        {
          value: 0,
          label: '女',
        },
        {
          value: 1,
          label: '男',
        },
        {
          value: 2,
          label: '未知',
        },
      ],
      applyArr: [],
      useridentifyArr: [],
      formerUserObj: {
        1: '企业管理员',
        2: '出版社管理员',
        3: '省厅管理员',
        4: '省厅高职',
        5: '省厅本科',
        6: '省厅专家',
        7: '其他',
        8: '高校管理员',
        9: '省厅义务教育管理员',
      },
      userTypeArr: [
        {
          value: 1,
          label: '企业管理员',
        },
        {
          value: 2,
          label: '出版社管理员',
        },
        {
          value: 3,
          label: '省厅管理员',
        },
        {
          value: 4,
          label: '省厅高职',
        },
        {
          value: 5,
          label: '省厅本科',
        },
        {
          value: 6,
          label: '省厅专家',
        },
        {
          value: 8,
          label: '高校管理员',
        },
        {
          value: 7,
          label: '其他',
        },
        {
          value: 9,
          label: '省厅义务教育管理员',
        },
      ],
      // 表单参数
      form: {},
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/system/user/importData',
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        departmentId: undefined,
        createStartTime: null,
        createEndTime: null,
      },
      // 表单校验
      rules: {
        status: [
          { required: true, message: '请选择部门状态', trigger: 'blur' },
        ],
        username: [
          { required: true, message: '用户名称不能为空', trigger: 'blur' },
        ],
        departmentId: [
          { required: true, message: '归属部门不能为空', trigger: 'blur' },
        ],
        password: [{ validator: pwdValidator, trigger: 'change' }],
      },
    }
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    },
  },
  created() {
    this.getDataList()
    this.getlistApply()
  },
  methods: {
    /**
     * @description: 格式化性別
     */
    formatUserType(row) {
      return this.formerUserObj[row.userType]
    },
    changeuser(id) {
      this.selectUserGui(id)
    },
    selectUserGui(id) {
      listAllUser({ applicationId: id }).then((res) => {
        this.useridentifyArr = res.data
      })
    },
    //查询应用列表
    getlistApply() {
      applicationList().then((res) => {
        this.applyArr = res.data
      })
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true
      this.queryParams.createStartTime =
        (this.dateRange && this.dateRange[0]) || ''
      this.queryParams.createEndTime =
        (this.dateRange && this.dateRange[1]) || ''
      listUser(this.queryParams).then((res) => {
        if (res.status == 0) {
          this.userList = res.data
          this.total = res.page.total
          this.loading = false
        }
      })
    },
    getDataList() {
      applicationList().then((res) => {
        if (res.status === 0) {
          this.deptOptionsLeft = res.data
          this.queryParams.applicationId = this.deptOptionsLeft[0].id
          this.$nextTick().then(() => {
            const firstNode = document.querySelector('.el-tree-node')
            firstNode.click()
          })
          this.getList()
        }
      })
    },

    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      }
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      console.log('data', data)
      this.queryParams.applicationId = data.id
      this.getList()
    },
    // 用户状态修改
    handleStatusChange(row) {
      console.log(row)
      let text = row.status == 1 ? '启用' : '停用'
      this.$confirm(
        '确认要"' + text + '""' + row.loginAccount + '"用户吗?',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(function () {
          const tempData = {
            id: row.id,
            status: row.status,
          }
          return changeUserStatus(tempData)
        })
        .then(() => {
          this.msgSuccess(text + '成功')
        })
        .catch(function () {
          row.status = row.status == 1 ? 5 : 1
        })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        departmentId: undefined,
        username: undefined,
        password: undefined,
        status: 1,
        roleIds: [],
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        departmentId: undefined,
        createStartTime: null,
        createEndTime: null,
      }
      // this.resetForm("queryForm");
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      console.log('ids', this.ids)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    //归属部门选择
    selectDept(node) {
      this.form.departmentName = node.name
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加用户'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const userId = row.id || this.ids
      getUser(userId).then((res) => {
        if (res.status == 0) {
          this.form.loginAccount = res.data.loginAccount
          this.form.departmentId = res.data.departmentId
          this.form.id = res.data.id
          this.form.applyType = res.data.applicationId
          this.selectUserGui(this.form.applyType)
          this.form.userType = res.data.userType
          this.open = true
          this.title = '修改用户'
          this.form.password = ''
        }
      })
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$confirm('是否重置该用户密码?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(function () {
          return resetUserPwd(row.id)
        })
        .then((res) => {
          this.getList()
          this.msgSuccess(res.msg)
        })
        .catch(function () {})
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          var param
          if (this.form.password) {
            param = Object.assign({}, this.form)
            param.password = md5(md5(this.form.password))
            param.showPassword = this.form.password
          } else {
            param = Object.assign({}, this.form)
          }
          if (param.id != undefined) {
            updateUser(param).then((response) => {
              if (response.status == 0) {
                this.msgSuccess('修改成功')
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addUser(param).then((response) => {
              if (response.status == 0) {
                this.msgSuccess('新增成功')
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.id || this.ids
      console.log('userIds', userIds)
      this.$confirm('是否确认删除该用户?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(function () {
          return delUser(userIds)
        })
        .then(() => {
          this.getList()
          this.msgSuccess('删除成功')
        })
        .catch(function () {})
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有用户数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(function () {
          return exportUser(queryParams)
        })
        .then((response) => {
          this.download(response.msg)
        })
        .catch(function () {})
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then((response) => {
        this.download(response.msg)
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
  },
}
</script>
<style>
.vue-treeselect .vue-treeselect__placeholder,
.vue-treeselect .vue-treeselect__single-value {
  line-height: 40px;
}
</style>