<template>
  <div>
    <div class="tcen">
      <back class="backc">返回</back>
      <span class="namec">{{ $route.query.name }}</span>
    </div>

    <div class="mainbg">
      <el-tabs
        type="border-card"
        class="tabcss"
        v-model="activeName"
        @tab-click="clickTab"
      >
        <el-tab-pane label="详情" name="basicinfo">
          <basicinfo ref="basicinfo"></basicinfo>
        </el-tab-pane>
        <el-tab-pane label="看板" name="stadata">
          <stadata ref="stadata"></stadata>
        </el-tab-pane>
        <el-tab-pane label="合同" name="contract">
          <contract></contract>
        </el-tab-pane>
        <el-tab-pane label="日志" name="logs">
          <logs ref="logs"></logs>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import back from '@/components/common/back.vue'
import basicinfo from './basicinfo.vue'
import stadata from './stadata.vue'
import contract from './contract.vue'
import logs from './logs.vue'
export default {
  components: {
    back,
    basicinfo,
    stadata,
    contract,
    logs,
  },
  data() {
    return {
      // id:$route.query.id,
      activeName: 'basicinfo',
    }
  },
  created() {},
  methods: {
    clickTab(data) {
      console.log('data=====', data.name)
      if (data.name == 'logs') {
        this.$refs.logs.loadData()
      } else if (data.name == 'basicinfo') {
        this.$refs.basicinfo.load()
      } else if (data.name == 'stadata') {
        this.$refs.stadata.loadData()
      }
    },
  },
}
</script>

<style scoped>
.tcen {
  position: relative;
  text-align: center;
}

.backc {
  display: inline-block;
  position: absolute;
  left: 0;
}
.tabcss /deep/.el-tabs__header {
  background-color: #4285f4;
  border-radius: 8px 8px 0px 0px;
  overflow: hidden !important;
  height: 62px !important;
}
.tabcss /deep/ .is-active {
  background: rgba(255, 255, 255, 0.2) !important;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  color: #ffffff !important;
  border-right-color: none;
  border-left-color: none;
}
.tabcss /deep/.el-tabs__content {
  padding: 0px !important;
}
.tabcss /deep/.el-tabs__item:hover {
  color: white !important;
}
.tabcss /deep/.el-tabs__item {
  height: 62px !important;
  line-height: 62px !important;
  color: white;
  font-family: Microsoft YaHei, Microsoft YaHei;
  text-align: center !important;
  padding: 0 !important;
  width: 200px !important;
  border: none;
}
.tabcss {
  border: none !important;
  box-shadow: none !important;
  background: rgba(0, 0, 0, 0) !important;
}
.mainbg {
  margin-top: 20px;
  /* background-color: white !important; */
  min-height: calc(100vh - 140px);
  width: 100%;
}
</style>