<template lang="html">
  <div class="mobandiv">
    <div class="headertitle">
      <back>{{editId ? '编辑模板' : '新增模板'}}</back>
    </div>
    <div class="formcontainer">
      <div class="left-board">
        <p class="blueline">基础组件</p>
        <div @click="addComponent(item)" class="components-item" v-for="(item, listIndex) in inputComponents"
          :key="listIndex">
          <img class="iconcss" :src="item.__config__.iconUrl" /> {{ item.__config__.label }}
        </div>
      </div>
      <div class="field-box-container">
        <right-panel v-if="activeData" :active-data="activeData" :form-conf="formConf"
          :show-field="!!drawingList.length" />
      </div>
      <div class="center-board">
        <p class="blueline">模板标题</p>
        <div class="paddinginner">
          <el-input class="mobantitlecss" v-model="formdata.templateName" placeholder="请输入模板标题"></el-input>
          <el-form class="formcss" label-width="120px">
            <FormItem :drawing-list="drawingList" :current-item="item" :index="index" :active-id="activeId"
              v-for="(item, index) in drawingList" :key="item.renderKey" :form-conf="formConf"
              @activeItem="activeFormItem" @deleteItem="drawingItemDelete"></FormItem>
          </el-form>
        </div>
      </div>
    </div>
    <el-card class='settime' v-if='formdata.templateType==3 || formdata.templateType==6'>
      <p>设置提交时间</p>
      <div class='timed'>
        <el-select clearable class='timecss' v-model="formdata.timeNode" placeholder="请选择">
          <el-option
            v-for="item in optionsTime"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
        <el-time-picker
          format="HH:mm"
          value-format="HH:mm"
          class='timeel'
          v-model="formdata.offTime"
          placeholder="请选择时间点">
        </el-time-picker>
      </div>
      
    </el-card>
    <el-card class="box-card" v-if="modelType ==2 || modelType ==3">
      <div>
        <p>{{viewObj.text}}</p>
        <div class="dflex">
          <div class="center">
            <img v-if="firstObj.logo" class="imglogo" :src="firstObj.logo" alt="" @click="addLook(1)">
            <div class="dimgdiv" v-if="firstObj.id && firstObj.logo==''" @click="addLook(1)">
                  {{firstObj.name}}
            </div>
            <i v-if="!firstObj.id" class="el-icon-circle-plus-outline addicon" @click="addLook(1)"></i>
            <p class="mtp">{{firstObj.name?firstObj.name:viewObj.people[0]}}</p>
          </div>
          <div class="mpx">
            <i class="el-icon-arrow-right jian"></i>
            <i class="el-icon-arrow-right jian"></i>
          </div>
          <div class="center">
            <i   v-if="secondObj.id" class="el-icon-close closeimg" @click="closeB(2)"></i>
            <img v-if="secondObj.logo" class="imglogo" :src="secondObj.logo" alt="" @click="addLook(2)">
            <div class="dimgdiv" v-if="secondObj.id && secondObj.logo==''" @click="addLook(2)">
                  {{secondObj.name}}
            </div>
            <i v-if="!secondObj.id" class="el-icon-circle-plus-outline addicon" @click="addLook(2)"></i>
            <p class="mtp">{{secondObj.name?secondObj.name:viewObj.people[1]}}</p>
          </div>
          <div class="mpx">
            <i class="el-icon-arrow-right jian"></i>
            <i class="el-icon-arrow-right jian"></i>
          </div>
          <div class="center">
            <i v-if="threeObj.id" class="el-icon-close closeimg" @click="closeB(3)"></i>
            <img v-if="threeObj.logo" class="imglogo" :src="threeObj.logo" alt="" @click="addLook(3)">
            <div class="dimgdiv" v-if="threeObj.id && threeObj.logo==''" @click="addLook(3)">
                  {{threeObj.name}}
            </div>
            <i v-if="!threeObj.id" class="el-icon-circle-plus-outline addicon" @click="addLook(3)"></i>
            <p class="mtp">{{threeObj.name?threeObj.name:viewObj.people[2]}}</p>
          </div>
        </div>
      </div>
    </el-card>
    <el-button type="primary" class="saveTemplate" @click="submitdata">保存模板</el-button>
    <departmentDialog ref="deRef" :dType="dType" :visible.sync="dialogVisibleDe" @updateVisible="updateSystemVisible"
      @submitData="submitData">
    </departmentDialog>
  </div>
</template>
<script>
import back from '../../common/back.vue'
import departmentDialog from '@/components/common/departmentDialog.vue'
import {
  saveTemplate,
  updateTemplate,
  TemplateDetail,
  detailId,
} from '../../../api/index'
import { deletePeople } from '@/api/moban/index'
import { inputComponents, formConf } from '@/components/generator/config'
import { deepClone } from '@/utils/index'
import FormItem from './FormItem'
import { getIdGlobal, saveIdGlobal } from '@/utils/db'
import RightPanel from './RightPanel'
const idGlobal = getIdGlobal()
export default {
  data() {
    return {
      optionsTime: [
        {
          id: 1,
          name: '星期一',
        },
        {
          id: 2,
          name: '星期二',
        },
        {
          id: 3,
          name: '星期三',
        },
        {
          id: 4,
          name: '星期四',
        },
        {
          id: 5,
          name: '星期五',
        },
        {
          id: 6,
          name: '星期六',
        },
        {
          id: 7,
          name: '星期日',
        },
      ],
      dialogVisibleDe: false,
      dType: '2',
      formdata: {
        templateName: '',
        templateType: sessionStorage.getItem('templateType'),
        templateDepartmentIds: '',
        disposePersons: [],
        offTime: '',
        timeNode: '',
      },
      formConf,
      idGlobal,
      inputComponents: inputComponents,
      drawingList: [],
      activeId: null,
      activeData: null,
      editId: '',
      InfoList: [],
      firstObj: {},
      secondObj: {},
      threeObj: {},
      headNameType: '',
      jiPeople: {
        text: '审核人',
        people: ['第一审核人', '第二审核人', '第三审核人'],
      },
      zongPeople: {
        text: '查阅人',
        people: ['第一查阅人', '第二查阅人', '第三查阅人'],
      },
      viewObj: {},
      modelType: '',
    }
  },
  components: { FormItem, RightPanel, back, departmentDialog },
  created() {
    this.editId = this.$route.query.id
    if (this.editId) {
      this.getData()
    } else {
      if (this.$route.query.modelType != 1) {
        this.formdata.templateDepartmentIds = sessionStorage
          .getItem('departmentIds')
          .split(',')
      }
    }
    if (this.$route.query.modelType == 3) {
      this.viewObj = this.zongPeople
      this.modelType = this.$route.query.modelType
    } else if (this.$route.query.modelType == 2) {
      this.viewObj = this.jiPeople
      this.modelType = this.$route.query.modelType
    } else if (this.$route.query.modelType == 1) {
      this.formdata.templateType = '7'
    }
  },
  watch: {
    idGlobal: {
      handler(val) {
        saveIdGlobal(val)
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    closeB(type) {
      if (type == 2) {
        let uid = this.secondObj.dingUserId
          ? this.secondObj.id
          : this.secondObj.userId
        this.deletePeopleApi({ id: uid }, () => {
          this.secondObj = this.threeObj
          this.threeObj = {}
        })
      } else if (type == 3) {
        let uid = this.threeObj.dingUserId
          ? this.threeObj.id
          : this.threeObj.userId
        this.deletePeopleApi({ id: this.threeObj.id }, () => {
          this.threeObj = {}
        })
      }
    },
    deletePeopleApi(params, callback) {
      deletePeople(params).then((res) => {
        if (res.status == 0) {
          callback()
        }
      })
    },
    addLook(type) {
      if (type == 3) {
        if (Object.keys(this.firstObj).length <= 0) {
          this.$message.error(`请先添加${this.viewObj.people[0]}`)
          return
        }
        if (Object.keys(this.secondObj).length <= 0) {
          this.$message.error(`请先添加${this.viewObj.people[1]}`)
          return
        }
      }
      if (type == 2) {
        if (Object.keys(this.firstObj).length <= 0) {
          this.$message.error(`请先添加${this.viewObj.people[0]}`)
          return
        }
      }
      this.headNameType = type
      this.dialogVisibleDe = true
      this.$refs.deRef.loadData()
    },
    submitData(data) {
      this.dialogVisibleDe = false
      if (this.headNameType == 1) {
        this.firstObj = data[0]
      } else if (this.headNameType == 2) {
        let firstId = this.firstObj.dingUserId
          ? this.firstObj.id
          : this.firstObj.userId
        if (data[0].id == firstId) {
          this.$message.error('审核人重复添加')
          return
        }
        this.secondObj = data[0]
      } else {
        let firstId = this.firstObj.dingUserId
          ? this.firstObj.id
          : this.firstObj.userId
        let seconedId = this.secondObj.dingUserId
          ? this.secondObj.id
          : this.secondObj.userId
        if (data[0].id == firstId || data[0].id == seconedId) {
          this.$message.error('审核人重复添加')
          return
        }
        this.threeObj = data[0]
      }
    },
    updateSystemVisible(value) {
      this.dialogVisibleDe = value
    },
    backAction() {
      this.$router.go(-1)
    },
    async submitdata() {
      if (this.formdata.templateName == '') {
        this.$message.error('请输入模板标题')
        return
      }
      if (
        (this.formdata.timeNode && !this.formdata.offTime) ||
        (!this.formdata.timeNode && this.formdata.offTime)
      ) {
        this.$message.error('请设置提交时间')
        return
      }
      if (this.$route.query.modelType != 1) {
        if (Object.keys(this.firstObj).length == 0) {
          let strText = ''
          if (this.$route.query.modelType == 3) {
            strText = '查阅人'
          } else if (this.$route.query.modelType == 2) {
            strText = '审核人'
          }
          this.$message.error('请添加' + strText)
          return
        }
      }
      if (this.drawingList.length == 0) {
        this.$message.error('请添加模板项')
        return
      }
      let cloneList = this.drawingList
      let submitDataArr = []
      cloneList.forEach((item, index) => {
        submitDataArr.push({
          itemName: item.__config__.label,
          itemType: item.__config__.itemType,
          itemSort: index,
          isRequired: item.__config__.required ? 1 : 0,
          id: item.__config__.id ? item.__config__.id : '',
          itemLimitNumber: item.__config__.itemLimitNumber
            ? item.__config__.itemLimitNumber
            : '',
          itemDescribe: item.__config__.itemDescribe,
        })
      })
      console.log(submitDataArr)
      this.formdata.templateItemEntities = submitDataArr

      if (this.$route.query.modelType != 1) {
        if (Object.keys(this.firstObj).length > 0) {
          let uid = this.firstObj.dingUserId
            ? this.firstObj.id
            : this.firstObj.userId
          this.formdata.disposePersons.push({
            userId: uid,
            logsSort: 1,
          })
        }
        if (Object.keys(this.secondObj).length > 0) {
          let uid = this.secondObj.dingUserId
            ? this.secondObj.id
            : this.secondObj.userId
          this.formdata.disposePersons.push({
            userId: uid,
            logsSort: 2,
          })
        }
        if (Object.keys(this.threeObj).length > 0) {
          let uid = this.threeObj.dingUserId
            ? this.threeObj.id
            : this.threeObj.userId
          this.formdata.disposePersons.push({
            userId: uid,
            logsSort: 3,
          })
        }
      }

      if (this.editId) {
        this.formdata.id = this.editId
        let res = await updateTemplate(this.formdata)
        if (res.status !== 0) return this.$message.error(res.msg)
        this.$message.success('修改成功')
      } else {
        let res = await saveTemplate(this.formdata)
        if (res.status !== 0) return this.$message.error(res.msg)
        this.$message.success('新增成功')
      }
      this.$router.go(-1)
    },
    async drawingItemDelete(index, list) {
      if (list[index].__config__.id) {
        let res = await detailId({
          id: list[index].__config__.id,
          templateId: this.$route.query.id,
        })
        if (res.status !== 0) return this.$message.error(res.msg)
      }
      list.splice(index, 1)
      this.$nextTick(() => {
        const len = this.drawingList.length
        if (len) {
          this.activeFormItem(this.drawingList[len - 1])
        }
      })
    },
    activeFormItem(currentItem) {
      this.activeData = currentItem
      this.activeId = currentItem.__config__.formId
    },
    addComponent(item) {
      const clone = this.cloneComponent(item)
      this.drawingList.push(clone)
      this.activeFormItem(clone)
    },
    cloneComponent(origin) {
      const clone = deepClone(origin)
      this.createIdAndKey(clone)
      return clone
    },
    createIdAndKey(item) {
      const config = item.__config__
      config.formId = ++this.idGlobal
      config.renderKey = `${config.formId}${+new Date()}` // 改变renderKey后可以实现强制更新组件
      if (config.layout === 'colFormItem') {
        item.__vModel__ = `field${this.idGlobal}`
      }
      return item
    },
    async getData() {
      let res = await TemplateDetail(this.editId)
      if (res.status != 0) return this.$message.error(res.msg)
      this.formdata = res.data
      this.formdata.disposePersons = []

      if (this.formdata.timeNode == 0) {
        this.formdata.timeNode = ''
      }
      let renderList = []
      let originalData = []
      if (
        res.data.templateItemVoList &&
        res.data.templateItemVoList.length > 0
      ) {
        originalData = res.data.templateItemVoList
      }
      if (
        res.data.templateDisposeVoList &&
        res.data.templateDisposeVoList.length > 0
      ) {
        if (res.data.templateDisposeVoList[0]) {
          this.firstObj = res.data.templateDisposeVoList[0]
        }
        if (res.data.templateDisposeVoList[1]) {
          this.secondObj = res.data.templateDisposeVoList[1]
        }
        if (res.data.templateDisposeVoList[2]) {
          this.threeObj = res.data.templateDisposeVoList[2]
        }
      }
      originalData.forEach((item, index) => {
        if (item.itemType == 1) {
          renderList.push({
            __config__: {
              label: item.itemName,
              tag: 'el-input',
              required: item.isRequired == 1 ? true : false,
              layout: 'colFormItem',
              itemType: item.itemType,
              formId: ++this.idGlobal,
              id: item.id,
              itemDescribe: item.itemDescribe,
              itemLimitNumber: item.itemLimitNumber,
            },
          })
        } else if (item.itemType == 2) {
          renderList.push({
            __config__: {
              label: item.itemName,
              tag: 'el-input',
              required: item.isRequired == 1 ? true : false,
              layout: 'colFormItem',
              itemType: item.itemType,
              formId: ++this.idGlobal,
              id: item.id,
              itemDescribe: item.itemDescribe,
              itemLimitNumber: item.itemLimitNumber,
            },
            type: 'textarea',
          })
        } else if (item.itemType == 3 || item.itemType == 4) {
          renderList.push({
            __config__: {
              label: item.itemName,
              tag: 'el-upload',
              required: item.isRequired == 1 ? true : false,
              layout: 'colFormItem',
              itemType: item.itemType,
              formId: ++this.idGlobal,
              id: item.id,
              buttonText: '点击上传',
              itemDescribe: item.itemDescribe,
              itemLimitNumber: item.itemLimitNumber,
            },
            __slot__: {
              'list-type': true,
            },
            action: '',
          })
        }
      })
      this.activeFormItem(renderList[0])
      this.drawingList = renderList
    },
  },
}
</script>

<style scoped>
.timeel {
  width: 160px;
  margin-left: 20px;
}
.timed {
  margin-top: 14px;
}
.timecss {
  width: 200px;
}
.settime {
  margin-top: 20px;
}
.closeimg {
  position: absolute;
  right: -20px;
  top: -14px;
  cursor: pointer;
  font-size: 24px;
}
.imglogo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  cursor: pointer;
}
.dimgdiv {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #2490ff;
  text-align: center;
  line-height: 60px;
  color: #fff;
}
.mtp {
  margin-top: 10px;
}
.mpx {
  margin: 0 60px;
}
.center {
  text-align: center;
  position: relative;
}
.dflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.addicon {
  font-size: 60px;
  color: #2490ff;
  cursor: pointer;
}
.jian {
  font-size: 20px;
}
.box-card {
  margin-top: 20px;
}
.paddinginner {
  padding: 0 20px;
}

.formcss::after {
  content: '';
  display: block;
  clear: both;
}

.mobantitlecss {
  height: 40px;
  background: #ffffff;
  border-radius: 10px;
  margin-bottom: 20px;
  margin-right: 100px;
}

.field-box-container {
  float: right;
  position: relative;
  width: 370px;
}

.iconcss {
  position: relative;
  top: 0px;
  margin-right: 4px;
  margin-left: 40px;
}

.blueline {
  border-left: 4px solid #2490ff;
  padding-left: 20px;
  margin-top: 0;
  margin-bottom: 20px;
}

.saveTemplate {
  height: 30px;
  line-height: 0px;
  margin: 0 auto;
  margin-top: 20px;
  display: block;
}

.weizhi {
  margin-top: -3px;
  display: block;
  float: left;
  margin-left: 30px;
}

.zuopintext {
  padding: 0;
  margin: 0;
  font-size: 12px;
  font-weight: 400;
  margin-top: -3px;
  margin-left: 5px;
}

.ptext {
  padding: 0;
  margin: 0;
  margin-top: -5px;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 700;
  color: #2490ff;
  margin-left: 10px;
}

.headertitle {
  background: #f7f9fc;
}

.lf {
  float: left;
}

.circle {
  width: 8px;
  height: 8px;
  background: #2490ff;
  border-radius: 50%;
}

.textflex {
  flex: 1;
}

.backicon {
  width: 25px;
  height: 25px;
  margin-top: -4px;
  margin-right: 10px;
  cursor: pointer;
}

.headertitle {
  font-size: 20px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  text-align: left;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}
.headertitle >>> .backimg {
  top: 6px;
}

.center-board >>> .active-from-item > .el-form-item {
  background: #f6f7ff;
  border-radius: 6px;
}

.center-board >>> .drawing-item .el-form-item {
  padding: 12px 10px;
}

.center-board >>> .active-from-item > .drawing-item-delete {
  display: initial;
}

.center-board >>> .drawing-item-delete {
  display: none;
  position: absolute;
  top: 0px;
  line-height: 22px;
  text-align: center;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  z-index: 1;
  width: 60px;
  height: 100%;
  background: #e9523f;
  border-radius: 0px 10px 10px 0px;
}

.center-board >>> .el-form-item {
  margin-bottom: 0px !important;
}

.center-board >>> .drawing-item {
  position: relative;
  margin-bottom: 20px;
}

.center-board >>> .drawing-item-delete {
  right: 0px;
  color: #fff;
  font-size: 20px;
}

.center-board >>> .el-form-item__content {
  margin-right: 70px;
}

.center-board >>> .el-icon-delete {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  display: block;
  width: 20px;
  height: 20px;
}

.center-board {
  width: auto;
  margin: 0 390px 0 222px;
  background: #fff;
  padding-top: 20px;
  height: calc(100vh - 160px);
  overflow-y: auto;
  overflow-x: hidden;
}

.components-item {
  cursor: pointer;
  border: 1px dashed #f6f7ff;
  width: 160px;
  height: 40px;
  background: #daecff;
  border: 1px solid #98cbff;
  border-radius: 20px;
  margin: 0 auto;
  line-height: 40px;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #666666;
  margin-bottom: 20px;
}

.left-board {
  padding-top: 20px;
  float: left;
  width: 200px;
  background: #fff;
  height: calc(100vh - 160px);
}

.fr {
  float: right;
}

.fl {
  float: left;
}
</style>
