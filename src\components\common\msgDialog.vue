<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="400px"
    top="30vh"
    class="msgdialog"
    @close="close"
  >
    <img class="msgicon" :src="imgUrl[messageType]" alt="" />
    <div class="titlename">{{ title }}</div>
    <div class="msgcss">{{ msg }}</div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      messageType: 'success',
      title: '',
      msg: '',
      imgUrl: {
        success: require('@/assets/successicon.png'),
        error: require('@/assets/erroricon.png'),
      },
      closeAction: null,
    }
  },
  methods: {
    show({ type, title, msg, onClose }) {
      this.messageType = type
      this.title = title
      this.msg = msg
      this.closeAction = onClose
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
      if (this.closeAction) {
        this.closeAction()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.msgdialog {
  /deep/.el-dialog {
    border-radius: 8px !important;
  }
  /deep/.el-dialog__header {
    border-bottom: none !important;
    padding: 15px 20px;
  }
  /deep/.el-dialog__body {
    padding-top: 0px;
    text-align: center !important;
  }
}
.msgicon {
  width: 64px;
  height: 64px;
  margin: 0 auto !important;
}
.titlename {
  margin-top: 16px;
  height: 30px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #333333;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.msgcss {
  margin-top: 29px;
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
