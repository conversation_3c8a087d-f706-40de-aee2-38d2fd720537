<template>
    <el-dialog @close="handleClose" title="新建提醒" center width="600px" :visible.sync="dialogFormVisible">
        <el-form  ref="refForm" :rules="rules" class="mainform" :model="form">
            <el-form-item prop="title" label="提醒事项：" :label-width="formLabelWidth">
                <el-input class="definput" v-model="form.title" placeholder="请输入提醒事项" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item prop="content" label="内容描述：" :label-width="formLabelWidth">
                <el-input 
                    maxlength="300"
                    show-word-limit 
                    type="textarea" 
                    v-model="form.content" autocomplete="off" placeholder="请输入内容描述" rows="5"
                    ></el-input>
            </el-form-item>
            <el-form-item prop="remindTime" label="提醒时间：" :label-width="formLabelWidth">
                <el-date-picker   popper-class="datepickerPopperClass" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" v-model="form.remindTime" type="datetime" class=" definput wid100" placeholder="选择日期时间"
                :picker-options="{ disabledDate: disabledDate }"
                    >
                </el-date-picker>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitAction">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
      import { remindSave } from '@/api/index'
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        }
    },
    computed: {
        dialogFormVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('updateVisible', value)
            }
        }
    },
    data() {
          return {
            rules: {
                title: [
                    { required: true, message: '请输入提醒事项', trigger: 'blur' },
                ],
                content: [
                    { required: true, message: '请输入内容描述', trigger: 'blur' }
                ],
                remindTime: [
                    {  required: true, message: '请选择日期时间', trigger: 'change' }
                ],
            },
            formLabelWidth: '120px',
            form: {
                title: "",
                content: "",
                remindTime: '',
                taskType:1
            }
        }
    },
    methods:{
        disabledDate(time) {
            var date = new Date()
            return time < new Date(date.getFullYear(),date.getMonth(),date.getDate(),0,0,0);
        },
        handleClose() {
            this.form.title = ''
            this.form.content = ''
            this.form.remindTime = ''
            this.$refs.refForm.resetFields()
            this.$emit('updateVisible', false)
        },
        remindSaveApi(){
            remindSave(this.form).then(res=>{
               if(res.status == 0){
                this.dialogFormVisible = false
                this.$message({
                                    type: "success",
                                    message: "操作成功！"
                                })
               }
               this.$emit('submitData')
            })
        },
        clearTime(){
        },
        submitAction(){
            this.$refs['refForm'].validate((valid) => {
                if (valid) {
                    this.remindSaveApi()
                } else {
                    console.log('error submit!!');
                    return false;
                }
                });
        },
    }
}
</script>



<style lang="scss" scoped>
.wid100{
    width: 100% !important;
}
@import "@/styles/mixin.scss";
.datepickerPopperClass{
  .el-button--text{
    display: none;  
  }
  .el-time-spinner__wrapper{
    width:100%;
  }
  .el-scrollbar:nth-child(2){
    display: none;
  }
}

 .mainform {
    width: 80%;
    margin: 0 auto;
}

.timediv {
    display: flex;
    justify-content: flex-start;
}

.datepicker /deep/.el-input__prefix {
    left: calc(100% - 30px);
}

.datepicker /deep/.el-input__inner {
    @include formatFont13;
    padding-left: 15px !important;
}

.datepicker /deep/.el-input__icon {
    line-height: 34px;
}

.mainform /deep/.el-form-item {
    margin-bottom: 30px;
}

.mainform /deep/.el-form-item__label {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400 !important;
    color: #333333;
    line-height: 34px;
}

.mainform /deep/.el-form-item__content {
    line-height: 34px;
}

.mainform /deep/.el-textarea__inner {
    @include formatFont13;
    line-height: 1.5;
}

.mainform /deep/.el-input__inner {
    @include formatFont13;
}

.canclebtn {
    margin-top: 7px;
    margin-left: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}
</style>
