<template>
  <div>
    <el-upload
      :action="getUrl"
      class="upload-demo"
      :limit="limit"
      :file-list="fileLists"
      :on-exceed="formHandleExceed"
      :before-upload="beforeUploadForm"
      :on-remove="HandleRemove"
      :accept="accept"
      :on-success="handleSuccess"
      :multiple="multiple"
      :on-progress="onProgress"
      :on-preview="onPreview"
    >
      <slot></slot>
      <div slot="tip" class="el-upload__tip">
        <slot name="ptip"></slot>
        <!-- 只能上传mp3文件，且不超过500kb -->
      </div>
      <!-- <el-button type="primary" :disabled="disabled">点击上传</el-button> -->
      <!-- <i class="tips" >只能上传文档，且大小不能超过{{formMaxSize}}M</i> -->
    </el-upload>
    <fileview ref="fileview"></fileview>
  </div>
</template>
<script>
import { deepClone } from '@/utils/tools.js'
import fileview from './fileview.vue'
export default {
  props: {
    fileList: {
      default: () => [],
      type: Array,
    },
    accept: {
      default: '.pdf',
      type: String,
    },
    limit: {
      // 限制文件个数
      default: 10,
      type: Number,
    },
    fileMaxSize: {
      // 文件大小限制
      default: 50,
      type: Number,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    onpreview: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    fileview,
  },
  data() {
    return {
      fileLists: this.fileList, // 显示上传文件
      disabled: false,
      data: null,
      getUrl: `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`,
      fileListClone: [],
      isUploading: false,
    }
  },
  methods: {
    getUploading() {
      return this.isUploading
    },
    onProgress() {
      this.isUploading = true
      console.log('uploading')
    },
    changeFileList(fileList) {
      fileList.forEach((file) => {
        file.fileName ? (file.name = file.fileName) : null
      })
      this.fileLists = fileList
    },
    setFileList(fileList) {
      this.fileLists = fileList
    },
    submitFile() {
      this.isUploading = false
      this.$emit('submitImg', this.fileListClone)
    },
    updateFileList(fileList) {
      let fileListClone = deepClone(fileList)
      this.fileListClone = []
      fileListClone.forEach((item) => {
        if (item.response) {
          this.fileListClone.push({
            url: item.response.data.url,
            fileName: item.response.data.fileName,
            fileSize: item.response.data.size,
            name: item.response.data.fileName,
            fileDruation: item.response.data.duration,
          })
        } else {
          this.fileListClone.push(item)
        }
      })
    },
    handleSuccess(response, file, fileList) {
      console.log(fileList, '-----------')
      if (fileList.every((item) => item.status == 'success')) {
        this.setFileList(fileList)
        this.updateFileList(fileList)
        this.submitFile()
      }
    },
    beforeUploadForm(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.'))
      if (!this.accept.includes(fileSuffix)) {
        this.$message.error('请上传正确的文件格式')
        return false
      }
      if (file.size / 1024 / 1024 > this.fileMaxSize) {
        this.$message({
          message: `上传文件大小不能超过${this.fileMaxSize}M!`,
          type: 'warning',
        })
        return false
      }
    },
    HandleRemove(file, fileList) {
      this.isUploading = false
      this.setFileList(fileList)
      this.updateFileList(fileList)
      this.submitFile()
    },
    formHandleExceed(files, formFileList) {
      this.$message.warning(
        `当前限制选择 ${this.limit} 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + formFileList.length} 个文件`
      )
    },
    onPreview(file) {
      if (this.onpreview) {
        this.$refs.fileview.show(file.response.data)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.upload-demo /deep/.el-upload {
  text-align: left !important;
}
.upload-demo /deep/.el-upload-list__item {
  width: 400px;
}
</style>