<template>
<div>
    <back class="mb20">重要日期</back>
    <el-card>
        <el-form >
            <textBorder>重要日期</textBorder>
            <el-row class="minheight pd30">
                <el-col :span="12">
                    <el-form-item label="" prop="hobby">
                        <el-input class="definput width45"  placeholder="请输入"
                            v-model="inputValue3" 
                        ></el-input>
                        
                        <el-date-picker
                        value-format="yyyy-MM-dd"
                        class="definput width45"
                        v-model="timeValue"
                        type="date"
                        placeholder="选择日期">
                        </el-date-picker>
                        <i class=" addbtn el-icon-circle-plus" @click="addTime"></i>
                    </el-form-item>
                    <el-form-item label="" prop="hobby" v-for=" (item,index) in inputTags2" :key="index">
                        <el-input class="definput width45"  placeholder="请输入"
                            v-model="item.careContent" 
                        ></el-input>
                        <el-date-picker
                        class="definput width45"
                        v-model="item.careContentText"
                        type="date"
                        placeholder="选择日期">
                        </el-date-picker>
                        <i class="el-icon-delete frcenter" @click="deleteItem2(index)"></i>
                    </el-form-item>
                </el-col>
            </el-row>
            <div class="divcenter">
                <el-button class="w98 btn_h42" type="primary" @click="submitForm" v-loading="isLoading">保存</el-button>
            </div>
            
        </el-form>

        
    </el-card>
</div>
 
</template>

<script>
import textBorder from '../../common/textBorder.vue';
import back from '../../common/back.vue'
import { customercareQueryVoList,customercareSave,customercareUpdate } from "@/api/clientmanagement/customer";
export default {
    components:{
        textBorder,
        back
    },
    data(){
        return{
            inputValue3:'',
            timeValue:'',
            inputTags2:[],
            isLoading:false,
            isEdit:false,
        }
    },
    created(){
        this.loaddata();
    },
    methods:{
        loaddata(){
            customercareQueryVoList({
                customerId:this.$route.query.customerId,
            }).then((result) => {
                this.inputTags2 = result.data;
                if (result.data.length>0) {
                    this.isEdit = true;
                }
            }).catch((err) => {
                
            });
        },
         addTime(){
            if (this.inputValue3 && this.timeValue) {
                this.inputTags2.push({
                    careContent:this.inputValue3,
                    careContentText:this.timeValue
                })
                this.inputValue3 = '';
                this.timeValue = '';
            }else{
                this.$message({
                    type:'info',
                    message:'请填写完整内容及时间，才能进行添加'
                })
            }
        },
        deleteItem2(index){
            this.inputTags2.splice(index,1);
        },
        submitForm(){
            var par = {
                customerId:this.$route.query.customerId,
                customerCareEntityList:this.inputTags2
            }
            if (this.inputValue3 && this.timeValue) {
                par.customerCareEntityList.push({
                    careContent:this.inputValue3,
                    careContentText:this.timeValue
                });
            }
            if (this.isEdit) {
                this.isLoading = true;
                customercareUpdate(par).then((result) => {
                    if (result.data) {
                        this.$message({
                            type:'success',
                            message:'保存成功'
                        })
                        this.inputValue3 = '';
                        this.timeValue = '';
                        this.loaddata();
                        
                    }
                    this.isLoading = false;
                }).catch((err) => {
                    this.isLoading = false;
                });
            }else{
                this.isLoading = true;
                customercareSave(par).then((result) => {
                    if (result.data) {
                        this.$message({
                            type:'success',
                            message:'保存成功'
                        })
                        this.inputValue3 = '';
                        this.timeValue = '';
                        this.loaddata();
                    }
                    this.isLoading = false;
                }).catch((err) => {
                    this.isLoading = false;
                });
            }
            
        },
    }
}
</script>
<style scoped>
.minheight{
    height: calc(100vh - 250px) !important;
    overflow-y: auto;
}
.divcenter{
    text-align: center;
}
.pd30{
    padding: 30px 0px;
}
.width45{
    width: 45%;
}
.width45 +.width45{
    margin-left: 10px;
}
.addbtn{
    margin-left: 5px;
    font-size: 20px;
    position: absolute;
    font-size: 20px;
    right: 0px;
    top: 10px;
    color: #4285F4 !important;
    cursor: pointer;
}
.frcenter{
    position: absolute;
    font-size: 20px;
    right: 0px;
    top: 10px;
    color: #f44242 !important;
    cursor: pointer;
}
.w98 {
    width: 98px;
}

</style>