<template>
  <div>
    <!-- :disabled="fileLists.length >= limit || uploadBtn" -->
    <el-upload
    :limit="limit"
    class="iupload"
    list-type="picture-card"
    :action="getUrl"
    :on-success="handleSuccess"
    :file-list="fileLists"
    :on-progress="handleProgress"
    :before-upload="beforeUpload"
    :data="data"
    :accept="accept"
    :multiple="multiple">
      <div class="uploadcss" v-if="limit != fileLists.length">
        <i class="el-icon-plus"></i>
        <div class="uploadtext deffont">点击上传</div>
      </div>
      <div slot="file" slot-scope="{file}" class="pr">
        <template v-if="file.status == 'success'">
          <img class="urlimg" :src="file._url" alt="">
          <span class="el-upload-list__item-actions">
            <span v-if="file.fileType == 'video'" class="el-upload-list__item-preview" @click="handleShowVideo(file)">
              <i class="el-icon-video-play"></i>
            </span>
            <span class="el-upload-list__item-delete" @click="handleRemove(file)">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </template>
        <template v-else>
          <img class="urlimg" :src="file.url" alt="">
          <el-progress :width="88" :height="88" type="circle" class="progressModule" :color="colors"
            :percentage="uploadPercentage" v-if="showProgress"></el-progress>
        </template>
      </div>
    </el-upload>

    <el-dialog :visible.sync="dialogVisible" append-to-body width="40%" style="text-align:center">
      <video :src="dialogImageUrl" alt="" autoplay class="video" controls="controls"></video>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone } from '@/utils/tools.js'
import { getFileType, addSnop } from '@/utils/tools.js'
import { deleteFile } from "@/api/index";
export default {
  props: {
    limit: {
      type: Number,
      default: 12,
    },
    accept: {
      default: '.png',
      type: String
    },
    fileList: {
      type: Array,
      default: () => {
        return new Array()
      }
    },
    multiple: {
      type: Boolean,
      default: true
    },
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      fileLists: this.fileList,
      editForm: {
        url: '',
        uid: null
      },
      editView: false,
      uploadPercentage: 0,
      showProgress: false,
      colors: [
        { color: '#ADD8E6', percentage: 20 },
        { color: '#87CEEB', percentage: 40 },
        { color: '#87CEFA', percentage: 60 },
        { color: '#00BFFF', percentage: 80 },
        { color: '#1296DB', percentage: 100 }
      ],
      uploadBtn: false,
      getUrl: `${process.env.VUE_APP_BASE_API}/aliyun/oss/uploadFiles`,
      fileListClone: []
    }
  },
  mounted() {
  },
  methods: {
    beforeUpload(file){
      var whiteList = this.accept.split(',')
      if (whiteList.length>0) {
        console.log('dddddd',this.whiteList)
        const fileSuffix = file.name.substring(file.name.lastIndexOf('.'))
        if (whiteList.indexOf(fileSuffix) === -1) {
            this.$message.error(`支持${this.accept}格式`);
            return false
        }
        return true
      }else{
        return true
      }

    },
    setFileList(fileList) {
      this.fileLists = fileList
    },
    handleRemove(file) {
      let fileList = this.fileLists
      for (let i in fileList) {
        if (fileList[i].uid === file.uid) {
          fileList.splice(i, 1);
          break;
        }
      }
      this.setFileList(fileList)
      this.handleClone(fileList)
      this.submitFile()
    },
    handleClone(fileListClone) {
      this.fileListClone = []
      fileListClone.forEach(item => {
        if (item.response) {
          this.fileListClone.push({
            url: item.response.data.url,
            fileName: item.response.data.fileName,
            fileSize: item.response.data.size,
            name: item.response.data.fileName,
            status: 'success',
            _url: item._url
          })
        } else {
          this.fileListClone.push({
            url: item.url,
            fileName: item.fileName,
            fileSize: item.fileSize,
            name: item.fileName,
            status: 'success',
            _url: item._url
          })
        }

      })
    },
    handleShowVideo(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleSuccess(response, file, fileList) {
      if (response.status == 0) {
        if (getFileType(file.response.data.url) == 'video') {
          file._url = addSnop(file.response.data.url)
          file.fileType = 'video'
        } else {
          file._url = file.response.data.url
        }
        this.showProgress = false
        this.uploadBtn = false
        if (fileList.every(item => item.status == 'success')) {
          this.setFileList(fileList)
          let fileListClone = deepClone(fileList)
          this.handleClone(fileListClone)
          this.submitFile()
        }
      }

    },
    // 播放视频
    handleEditVideo(file) {
      this.editForm.url = file.url
      this.editForm.uid = file.uid
      this.editView = true
    },
    submitFile() {
      this.$emit('submitImg', this.fileListClone)
    },
    handleProgress(event, file, fileList) {
      this.uploadBtn = true
      this.showProgress = true
      this.uploadPercentage = +event.percent.toFixed(0)
    },
  }
}
</script>

<style>
.el-upload-list__item-actions {
  position: relative;
}

.urlimg {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>

<style scoped lang="scss">
.iupload /deep/.el-upload--picture-card {
  border: none !important;
  width: 88px;
  height: 88px;
  line-height: 88px;
}

.iupload /deep/.el-upload-list__item-delete {
  position: absolute !important;
  right: 0;
  top: -11px;
}

.iupload /deep/ .el-upload-list__item {
  width: 88px !important;
  height: 88px !important;
  border-radius: 8px 8px 8px 8px !important;
  cursor: pointer !important;
}

.pr {
  position: relative;
  width: 88px;
  height: 88px;
}

.uploadtext {
  color: #4285F4;
  cursor: pointer;
}

.uploadtext {
  color: #4285F4;
  cursor: pointer;
}

.uploadcss {
  width: 88px;
  height: 88px;
  line-height: 24px;
  background: #F5F5F5;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px dashed #CCCCCC;
  display: inline-block;
}

.uploadcss i {
  margin-top: 20px;
  font-size: 24px;
  color: #4285F4;
}

.el-icon-plus {
  font-size: 30px !important;
}

.el-icon-edit {
  font-size: 18px !important;
}

.el-icon-video-play {
  font-size: 18px !important;

}

.el-icon-delete {
  font-size: 18px !important;
  color: rgb(243, 143, 130);
}

.el-input>>>.el-textarea__inner {
  font-size: 18px !important;
}

.video {
  min-height: 200px;
  max-height: 600px;
  min-width: 200px;
  max-width: 100%;
}

.progressModule {
  width: 88px;
  height: 88px;
}

.el-progress-circle {
  width: 80px !important;
  height: 80px !important;
}

.progressModule>>>.el-progress__text {
  color: #1296DB;
  font-size: 15px !important;
}

.el-upload {
  display: inline-block;
}
</style>
