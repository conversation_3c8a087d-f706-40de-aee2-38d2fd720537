<!--角色管理页面-->
<template>
  <div class="app-container">
    <el-button
      type="primary"
      v-isShow="'sf:business:application:save'"
      @click="handleAdd()"
      >新增</el-button
    >
    <el-button
      type="danger"
      v-isShow="'sf:business:application:delete'"
      @click="deleteHandle()"
      :disabled="dataListSelections.length <= 0"
      >删除</el-button
    >
    <!-- 表格 -->
    <div class="pt20">
      <el-table
        :data="dataList"
        stripe
        :empty-text="$emptyFont"
        border
        @selection-change="selectionChangeHandle"
        style="width: 100%"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        v-loading="dataListLoading"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="标志ID" width="180"></el-table-column>
        <el-table-column
          prop="name"
          label="应用名称"
          width="180"
        ></el-table-column>
        <el-table-column prop="description" label="描述信息"></el-table-column>
        <el-table-column label="是否启用" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.isEnabled"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
              v-isShow="'sf:business:application:updateEnable'"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="创建时间">
          <template slot-scope="scope"
            ><span v-if="scope.row.createTime">{{
              ruleTime(scope.row.createTime)
            }}</span></template
          >
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template slot-scope="scope">
            <el-button
              v-isShow="'sf:business:application:update'"
              @click="handleUpdate(scope.row)"
              size="small"
              icon="el-icon-edit"
              type="text"
              >修改</el-button
            >
            <el-button
              v-isShow="'sf:business:application:delete'"
              @click="deleteHandle(scope.row.id, scope.row.name)"
              size="small"
              icon="el-icon-delete"
              type="text"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="openDataScope"
      width="500px"
      append-to-body
    >
      <el-form :model="form" ref="form" label-width="120px" :rules="rule">
        <el-form-item label="应用平台名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.description" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancelDataScope">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  applicationList,
  changeAppStatus,
  getApplication,
  addApplication,
  updateApplication,
} from '@/api/framework/application'
import pagination from '@/mixin/pagination'
export default {
  mixins: [pagination],
  data() {
    return {
      dataList: [],
      dataListLoading: false,
      centerDialogVisible: false,
      search: {},
      dataListSelections: [],
      // 是否显示弹出层
      openDataScope: false,
      // 弹出层标题
      title: '',
      // 表单参数
      form: {},
      rule: {
        name: [
          { required: true, message: '应用平台名称不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 是否启用
    handleStatusChange(row) {
      let text = row.isEnabled == 1 ? '启用' : '停用'
      this.$confirm('确认要"' + text + '"吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(function () {
          const tempData = {
            id: row.id,
            isEnabled: row.isEnabled,
          }
          return changeAppStatus(tempData)
        })
        .then(() => {
          this.msgSuccess(text + '成功')
        })
        .catch(function () {
          row.isEnabled = row.isEnabled == 1 ? 0 : 1
        })
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        description: undefined,
        isEnabled: 1,
      }
      this.resetForm('form')
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.openDataScope = true
      this.title = '添加应用平台'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      getApplication(row.id).then((res) => {
        if (res.status == 0) {
          this.form = res.data
          this.openDataScope = true
          this.title = '修改应用平台'
        }
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            const tempData = {
              id: this.form.id,
              name: this.form.name,
              description: this.form.description,
              isEnabled: this.form.isEnabled,
            }
            updateApplication(tempData).then((response) => {
              if (response.status == 0) {
                this.msgSuccess('修改成功')
                this.openDataScope = false
                this.getDataList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            console.log('this.form', this.form)
            addApplication(this.form).then((response) => {
              if (response.status == 0) {
                this.msgSuccess('新增成功')
                this.openDataScope = false
                this.getDataList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    // 删除
    deleteHandle(id, name) {
      var ids = id
        ? [id]
        : this.dataListSelections.map((k) => {
            return k.id
          })
      var idNames = name
        ? [name]
        : this.dataListSelections.map((item) => {
            return item.name
          })
      let data = {}
      data.ids = ids.join(',')
      this.$confirm(
        `确定对[${idNames.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        let res = await this.$axios.post(
          '/sf/business/application/delete',
          data
        )
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            },
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //
    getDataList() {
      this.dataListLoading = true
      applicationList().then((res) => {
        if (res.status === 0) {
          this.dataList = res.data
          this.dataListLoading = false
        }
      })
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false
      this.reset()
    },
  },
}
</script>

<style lang="scss" scoped>
.rolepotion {
  height: 500px;
  overflow-y: scroll;
  position: relative;
  // .dialog-foot{
  //   position: absolute;
  //   bottom: 0;
  // }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.query-add .el-button {
  margin-left: 20px;
}
</style>
