<template>
  <div class="commentdiv" :class="{secondcss:item.parentId != '0'}">
      <ul>
        <li class="commentli">
          <div>
                <!-- <img class="head left" src="@/assets/touxiang.png" alt=""> -->
                <p class="">
                  <span class="pname"><span>{{parentName}}</span> <span class="rcss"> {{parentName ? "回复" :''  }} </span>{{ item.createByName }}</span>
                  <span class="spantext">{{ item.createTime }}</span>
                  <span class="reply" v-if="item.parentId == '0'" @click="replayAction">回复</span>
                  <span class="reply dBtn" v-if="(userId == item.createBy) || (dataScope == 4)" @click="deleteAction" v-dbClick>删除</span>
                </p>
                <p class="ptext">{{item.comment}}</p>
            </div>
            <template v-if="item.childrenList.length>0" >
              <commentitem :item="citem" v-for="(citem,index) in item.childrenList" :parentName="item.createByName" :key="index" v-on="$listeners"></commentitem>
            </template>
        </li>
      </ul>
  </div>
</template>

<script>
  export default {
    name:'commentitem',
    props:{
      item:{
        type:Object,
        default:()=>{}
      },
      parentName:{
        type:String,
        default:''
      }
    },
    data(){
      return{
        userId:window.sessionStorage.getItem('userid'),
        dataScope:window.sessionStorage.getItem('dataScope')
      }
    },
    created(){

    },
    methods:{
      replayAction(){
        this.$emit('reply',{id:this.item.id,name:this.item.createByName})
      },
      deleteAction(){
        console.log("dddddddddd",this.item.id);
        this.$emit('deleteAction',this.item.id)
      },
    }
    
  }
</script>

<style lang="scss" scoped>

.rcss{
  color: #999999;
}
.pname{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-right: 12px;
}
.secondcss{
  margin-left: 30px !important;
  margin-top: 12px !important;
}
.commentli{
  margin-bottom: 20px;
}
.ptext{
  margin-top: 8px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-left: 0px;
  padding-right: 12px;
  word-wrap: break-word;
}
.reply{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  margin-left: 16px;
  cursor: pointer;
}
.dBtn{
  color: #F45961 !important;
}
.spantext{
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.head{
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 8px;
}
.commentdiv{
  margin-left: 16px;
  margin-top: 10px;
}

</style>