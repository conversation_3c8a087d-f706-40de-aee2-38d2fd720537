<template lang="html">
  <el-card class="box-card">
    <div  class="tools-bar">
      <el-button v-isShow="'ear:company:expertlecturerinfo:save'" type="success" icon="el-icon-plus" size="small" @click="addUser">新增</el-button>
    </div>
    <div>
      <el-table
        ref="singleTable"
        :data="tableData"
        border
        highlight-current-row
        style="width: 100%">
        <el-table-column
          prop="name"
          label="标准名称"
          min-width="120">
        </el-table-column>
        <el-table-column
          prop="createTime"
          min-width="210"
          label="创建时间">
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          width="150">
          <template slot-scope="scope">
                  <div>
                    <el-button
                      v-isShow="'ear:company:expertlecturerinfo:info'"
                      type="primary"
                      size="small"
                      @click="handleEdit(scope.$index, scope.row)"
                    >编辑</el-button>
                    <el-button
                    v-isShow="'ear:company:expertlecturerinfo:delete'"
                      type="danger"
                      size="small"
                      @click="handleResetPwd(scope.$index, scope.row)"
                    >删除</el-button>
                  </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-bar">
        <el-pagination
          @size-change="handleSizeChange"
          :current-page="1"
          @current-change="handleCurrentChange"
          :page-size="pageBean.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="totalRecord">
        </el-pagination>
      </div>
    </div>
    <el-dialog width="60%" :title="dialogTitle" :visible.sync="dialogVisible" @close="onDialogClose()">
      <el-form ref="dataForm"  :model="dataForm" label-width="100px">
         <el-form-item class="expertList" label="标准名称" prop="name">
          <el-input v-model="dataForm.name" placeholder="请输入标准名称"></el-input>
        </el-form-item>
          <el-form-item label="标准详情">
                          <template>
  <vue-ueditor-wrap
    v-model="dataForm.content"
    :config="myConfig"
  ></vue-ueditor-wrap>
</template>
          </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onDialogSubmit()" >确定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>
<script>
import VueUeditorWrap from 'vue-ueditor-wrap'
import {
  getList,
  policySave,
  deletePolicy,
  policyDetail,
  updatePolicy,
} from '@/api/basesystem/index.js'
export default {
  data() {
    return {
      search: '',
      stashList: [],
      totalRecord: 0,
      tableLoading: false,
      dialogVisible: false,
      dialogTitle: '新增',
      rules: {
        schoolName: [
          { required: true, message: '请输入学校名称', trigger: 'blur' },
        ],
      },
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        afficheType: '2',
      },
      dataForm: {
        name: '',
        afficheType: '2',
        content: '',
      },
      tableData: [],
      options: [
        { label: '专家', id: 2 },
        { label: '讲师', id: 1 },
      ],
      getUrl: `${process.env.VUE_APP_BASE_API}aliyun/oss/uploadFiles`,
      fileListDet: [],
      headerUrl: { Authorization: window.sessionStorage.getItem('token') },
      imgDetails: { commonName: 'company' },
      myConfig: {
        // 编辑器不自动被内容撑高
        autoHeightEnabled: false,
        // 初始容器高度
        initialFrameHeight: 240,
        // 初始容器宽度
        initialFrameWidth: '100%',
        // 上传文件接口（这个地址是我为了方便各位体验文件上传功能搭建的临时接口，请勿在生产环境使用！！！）
        // serverUrl: 'http://35.201.165.105:8000/controller.php',
        // UEditor 资源文件的存放路径，如果你使用的是 vue-cli 生成的项目，通常不需要设置该选项，vue-ueditor-wrap 会自动处理常见的情况，如果需要特殊配置，参考下方的常见问题2
        UEDITOR_HOME_URL: '/UEditor/',
      },
    }
  },
  created() {
    this.initList()
  },
  watch: {},
  mounted() {},
  methods: {
    // 上传详情图
    handleRemoveDet(file, fileList) {},
    handlePreviewDet(file) {},
    handleAvatarSuccessDet(res, file, fileList) {
      this.fileListDet = fileList
      this.dataForm.headPortraitUrl = res.data.url
      this.dataForm.headPortraitName = res.data.fileName
      this.dataForm.headPortraitSize = res.data.size
    },
    // 禁用状态格式化
    statusFormatter(row, column) {
      let status = row.identityType
      if (status === 1) {
        return '讲师'
      } else {
        return '专家'
      }
    },
    async initList() {
      let res = await getList(this.pageBean)
      if (res.status != 0) return this.$message.error(res.msg)
      this.tableData = res.data
      this.totalRecord = res.page.total
    },
    addUser() {
      this.dialogVisible = true
      for (let key in this.dataForm) {
        this.dataForm[key] = ''
      }
      this.dataForm.afficheType = '2'
    },
    handleStatus(row) {},
    onDialogClose() {
      this.$refs.dataForm.resetFields()
    },
    handleSizeChange(val) {
      this.pageBean.pageNum = val
      this.initList()
    },
    handleCurrentChange(val) {
      this.pageBean.pageNum = val
      this.initList()
    },
    handleChangeStatus(index, row) {},
    handleResetPwd(index, row) {
      this.$confirm('确认删除该数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let res = await deletePolicy({ ids: [row.id].join(',') })
          if (res.status !== 0) return this.$message.error(res.msg)
          this.initList()
          this.$message({ message: '删除成功', type: 'success' })
        })
        .catch(() => {})
    },
    async handleEdit(index, row) {
      this.dialogVisible = true
      this.dialogTitle = '修改'
      this.dataForm.id = row.id
      this.fileListDet = []
      this.editExpert(row.id)
    },
    async editExpert(id) {
      let res = await policyDetail(id)
      this.dataForm = res.data
    },
    onDialogSubmit() {
      this.$refs.dataForm.validate(async (valid) => {
        if (valid) {
          if (this.dialogTitle == '修改') {
            delete this.dataForm.modifyTime
            delete this.dataForm.createTime
            let res = await updatePolicy(this.dataForm)
            if (res.status !== 0) return this.$message.error(res.msg)
            this.$message.success('修改成功')
          } else {
            let res = await policySave(this.dataForm)
            if (res.status !== 0) return this.$message.error(res.msg)
            this.$message({ message: '添加成功', type: 'success' })
          }
          this.dialogVisible = false
          this.initList()
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style scoped>
.expertList {
  width: 400px;
}
.fr {
  float: right;
}
.fl {
  float: left;
}
.search-bar {
  overflow: hidden;
}
.tools-bar {
  margin-bottom: 20px;
}
</style>
