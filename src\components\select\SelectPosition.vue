<!-- 所属岗位 暂时不用-->
<template>
  <el-form-item label="所属岗位" prop="departmentId">
    <el-select clearable placeholder="请选择" filterable v-model="myValue" @change="change" :disabled="disabled">
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      ></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
export default {
  props: ['value', 'disabled', 'companyid'],
  data () {
    return {
      options: [],
      myValue: ''
    }
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    change (val) {
      let name = ''
      this.options.map(item => {
        if (item.id === val) {
          name = item.name
        }
      })
      let obj = {
        companyId: val,
        companyName: name
      };
      this.$emit('handleGetCompany', obj)
    },
    async getReqData () {
      let data = {
        companyId: '51587762725335425'
      }
      if (this.companyid) {
        let res = await this.$axios.get('/sf/business/companyposition/findByCompanyId', {params: data});
        if (res.status === 0) {
          this.options = res.data
        }
      }
    }
  },
  created () {
    this.getReqData()
  }
}
</script>
