<template>
  <div class="mainbg fixpb">
    <el-form class="myform clearfix" inline @keyup.enter.native="search">
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.userName"
          class="definput"
          placeholder="请输入姓名"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="unitbtn mt definput w1" @click="chooseDepartment">
          <span v-if="pageBean.departmentId" class="deffont">{{
            searchDepartmentName || '请选择部门'
          }}</span>
          <span v-else class="deffont">请选择部门</span>
          <i
            v-if="searchDepartmentName"
            @click.stop="clearDepartment"
            class="rcenter el-icon-circle-close"
          />
          <i v-else class="rcenter el-icon-arrow-down pscss" />
        </div>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          v-model="pageBean.year"
          type="year"
          placeholder="请选择年份"
          class="definput"
          clearable
          value-format="yyyy"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="">
        <el-button
          @click="search"
          class="defaultbtn mt"
          icon="el-icon-search"
          type="primary"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item label="" class="fr">
        <el-button
          class="defaultbtn mt"
          icon="el-icon-plus"
          type="primary"
          v-isShow="'crm:controller:personalgoals:save'"
          @click="openCreateDialog"
        >
          创建目标</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="customertable mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column prop="year" label="年份" align="center" />
      <el-table-column prop="userName" label="姓名" align="center" />
      <el-table-column prop="departmentName" label="部门" align="center" />
      <el-table-column prop="createByName" label="创建人" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" />
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            class="bbtn mr10"
            type="text"
            v-isShow="'crm:controller:personalgoals:info'"
            @click="viewDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            class="bbtn mr10"
            type="text"
            v-isShow="'crm:controller:personalgoals:update'"
            :disabled="scope.row.isDisabled"
            @click="editGoal(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            class="rbtn mr10"
            type="text"
            v-isShow="'crm:controller:personalgoals:delete'"
            :disabled="scope.row.isDisabled"
            @click="deleteGoal(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <el-dialog
      :title="isEditMode ? '编辑目标' : '创建目标'"
      :visible.sync="createDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="resetCreateForm"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="createForm"
        label-width="120px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="人员选择"
              prop="userId"
              required
              v-if="!isEditMode"
            >
              <span v-if="form.userId" class="mr10">{{ form.userName }}</span>
              <el-button
                class="bBtn"
                icon="el-icon-plus"
                type="text"
                @click="clickSelectPerson('选择人员', 1)"
                >点击选择人员</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年份" prop="year" v-if="!isEditMode">
              <el-date-picker
                v-model="form.year"
                type="year"
                placeholder="请选择年份"
                class="definput"
                value-format="yyyy"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="跟进拜访（次）" prop="newVisit">
              <el-input
                v-model="form.newVisit"
                class="definput"
                placeholder="请输入跟进拜访次数"
              ></el-input>
            </el-form-item>

            <el-form-item label="业绩（万元）" prop="contractAmount">
              <el-input
                v-model="form.contractAmount"
                class="definput"
                placeholder="请输入业绩"
              ></el-input>
            </el-form-item>
            <el-form-item label="合同新增（个）" prop="newContract">
              <el-input
                v-model="form.newContract"
                class="definput"
                placeholder="请输入合同新增个数"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户新增（人）" prop="newCustomers">
              <el-input
                v-model="form.newCustomers"
                class="definput"
                placeholder="请输入客户新增人数"
              ></el-input>
            </el-form-item>

            <el-form-item label="回款（万元）" prop="contractReturnAmount">
              <el-input
                v-model="form.contractReturnAmount"
                class="definput"
                placeholder="请输入回款金额"
              ></el-input>
            </el-form-item>

            <el-form-item label="样书发放（次）" prop="materialDeliverNum">
              <el-input
                v-model="form.materialDeliverNum"
                class="definput"
                placeholder="请输入样书发放次数"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="isSubmitting"
          >确定</el-button
        >
      </span>
    </el-dialog>

    <!-- 选择部门 -->
    <departmentDialog
      ref="deRef"
      dType="1"
      :visible.sync="dialogDepartmentVisible"
      @updateVisible="updateDepartmentVisible"
      @submitData="submitDepartmentData"
    >
    </departmentDialog>

    <!-- 选择人员 -->
    <systemDialog
      ref="systemdialog"
      :name="dialogName"
      :multipleNum="multipleNum"
      :visible.sync="dialogVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitPersonData"
    >
    </systemDialog>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import departmentDialog from '../../common/departmentDialog.vue'
import systemDialog from '../../common/systemDialog.vue'
import {
  personalgoalsList,
  addpersonalgoals,
  personalgoalsdelete,
  updatepersonalgoals,
  jiHuaInfo,
} from '@/api/goal/index'

export default {
  components: {
    page,
    nolist,
    departmentDialog,
    systemDialog,
  },
  data() {
    return {
      isLoading: false,
      isSubmitting: false,
      tableData: [],
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        goalsType: 9,
        userName: '',
        departmentId: '',
        year: '',
      },
      searchDepartmentName: '',
      createDialogVisible: false,
      dialogDepartmentVisible: false,
      dialogVisible: false,
      dialogName: '',
      multipleNum: 1,
      isEditMode: false,
      editingId: null,
      form: {
        userId: '',
        userName: '',
        departmentId: '',
        year: '',
        newVisit: '',
        newCustomers: '',
        contractAmount: '',
        contractReturnAmount: '',
        newContract: '',
        materialDeliverNum: '',
        goalsType: 9,
        goalStatus: 1,
      },
      rules: {
        userId: [{ required: true, message: '请选择人员', trigger: 'change' }],
        year: [{ required: true, message: '请选择年份', trigger: 'change' }],
        newVisit: [
          {
            pattern: /^[0-9]*$/,
            message: '跟进拜访次数必须是数字',
            trigger: 'blur',
          },
        ],
        newCustomers: [
          {
            pattern: /^[0-9]*$/,
            message: '客户新增人数必须是数字',
            trigger: 'blur',
          },
        ],
        contractAmount: [
          {
            pattern: /^[0-9]*\.?[0-9]*$/,
            message: '业绩必须是数字',
            trigger: 'blur',
          },
        ],
        contractReturnAmount: [
          {
            pattern: /^[0-9]*\.?[0-9]*$/,
            message: '回款金额必须是数字',
            trigger: 'blur',
          },
        ],
        newContract: [
          {
            pattern: /^[0-9]*$/,
            message: '合同新增个数必须是数字',
            trigger: 'blur',
          },
        ],
        materialDeliverNum: [
          {
            pattern: /^[0-9]*$/,
            message: '样书发放次数必须是数字',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    search() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    isDisabled( data){
      const userid = sessionStorage.getItem('userid')
      const dataScope = sessionStorage.getItem('dataScope')
      if (userid == data.createBy || dataScope == 4) {
        return false
      } else {
        return true
      }
    },

    loadData() {
      this.isLoading = true
      personalgoalsList(this.pageBean)
        .then((res) => {
          if (res.status == 0) {
            res.data.forEach(element => {
              element.isDisabled = this.isDisabled(element)
            });
            this.tableData = res.data
            this.total = res.page.total
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    chooseDepartment() {
      this.dialogDepartmentVisible = true
      this.$refs.deRef.loadData()
    },
    updateDepartmentVisible(value) {
      this.dialogDepartmentVisible = value
    },
    submitDepartmentData(data) {
      if (data && data.length > 0) {
        this.pageBean.departmentId = data[0].id
        this.searchDepartmentName = data[0].name
      } else {
        this.pageBean.departmentId = ''
        this.searchDepartmentName = ''
      }
      this.dialogDepartmentVisible = false
    },
    clearDepartment() {
      this.pageBean.departmentId = ''
      this.searchDepartmentName = ''
    },
    openCreateDialog() {
      this.createDialogVisible = true
    },
    resetCreateForm() {
      if (this.$refs.createForm) {
        this.$refs.createForm.resetFields()
      }
      // 重置表单数据
      Object.keys(this.form).forEach((key) => {
        if (
          key === 'userId' ||
          key === 'userName' ||
          key === 'year' ||
          key === 'departmentId'
        ) {
          this.form[key] = ''
        } else if (key === 'goalsType') {
          this.form[key] = 9
        } else if (key === 'goalStatus') {
          this.form[key] = 1
        } else {
          this.form[key] = null
        }
      })
      // 重置编辑状态
      this.isEditMode = false
      this.editingId = null
    },
    fillFormData(data) {
      Object.keys(this.form).forEach((key) => {
        if (data.hasOwnProperty(key)) {
          if (key === 'year') {
            this.form[key] = data[key] ? String(data[key]) : ''
          } else {
            this.form[key] =
              data[key] || (typeof this.form[key] === 'string' ? '' : null)
          }
        }
      })
    },
    clickSelectPerson(name, multipleNum) {
      this.dialogName = name
      this.multipleNum = multipleNum
      this.$refs.systemdialog.loadData()
      if (this.form.userId) {
        this.$refs.systemdialog.updateWorksId([
          {
            id: this.form.userId,
            name: this.form.userName,
            departmentId: this.form.departmentId,
          },
        ])
      } else {
        this.$refs.systemdialog.updateWorksId([])
      }
      this.dialogVisible = true
    },
    updateSystemVisible(value) {
      this.dialogVisible = value
    },
    submitPersonData(data, type, departmentId) {
      if (type == '选择人员') {
        this.form.userId = data.length > 0 ? data[0].id : ''
        this.form.userName = data.length > 0 ? data[0].name : ''
        this.form.departmentId = departmentId || ''
      }
      this.dialogVisible = false
    },
    submitForm() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          this.isSubmitting = true
          const apiCall = this.isEditMode
            ? updatepersonalgoals
            : addpersonalgoals
          const formData = this.isEditMode
            ? { ...this.form, id: this.editingId }
            : this.form

          apiCall(formData)
            .then((res) => {
              if (res.status == 0) {
                this.$message.success(this.isEditMode ? '更新成功' : '创建成功')
                this.createDialogVisible = false
                this.resetCreateForm()
                this.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
            .finally(() => {
              this.isSubmitting = false
            })
        }
      })
    },
    viewDetail(row) {
      this.$router.push({
        path: '/managementCenter/goal/detail',
        query: {
          id: row.id,
          year: row.year,
          userName: row.userName,
        },
      })
    },
    editGoal(row) {
      this.isEditMode = true
      this.editingId = row.id

      jiHuaInfo({ id: row.id }).then((res) => {
        if (res.status == 0) {
          const data = res.data
          this.fillFormData(data)
          this.createDialogVisible = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    deleteGoal(row) {
      this.$confirm(
        '是否删除该用户的年目标？删除后在目标排行中将默认排在最后，个人目标中将无目标展示',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        personalgoalsdelete({ id: row.id }).then((res) => {
          if (res.status == 0) {
            this.$message.success('删除成功')
            this.loadData()
          } else {
            this.$message.error(res.message)
          }
        })
      })
    },
  },
}
</script>
<style scoped>
.el-icon--right {
  margin-left: 0px;
}
.w1 {
  width: 200px;
}
.unitbtn {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  height: 34px;
  line-height: 34px;
  padding: 0 15px;
  cursor: pointer;
  position: relative;
  background-color: #fff;
}
.unitbtn:hover {
  border-color: #c0c4cc;
}
.deffont {
  color: #606266;
}
.pscss {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
.rcenter {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
.mr10 {
  margin-right: 10px;
}
.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}
.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.bBtn {
  color: #4285f4;
}
</style>

<style scoped lang="scss">
.mainbg {
  background-color: white;
  padding: 20px;
  min-height: 100%;
  border-radius: 10px;
}
.fr {
  float: right;
}
.mt {
  margin-top: 4px;
}
.customertable /deep/.cell {
  padding: 8px 10px;
}
.customertable .el-button {
  padding: 2px;
}
</style>
