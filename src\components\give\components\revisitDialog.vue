<template>
    <el-dialog class="revisitdialog" title="回访" top="80px" :visible.sync="dialogTableVisible" width="300px" center>
        <div class="topcss">
            <el-button class="defaultbtn" icon="el-icon-plus" type="primary" @click="addAction">添加回访</el-button>
        </div>
        <div class="listh">
            <el-timeline v-if="dataList.length>0" :reverse="false"
            >
                <el-timeline-item class="timeline" v-for="item in dataList" :key="item.id" hide-timestamp :timestamp="item.createTime" color="#4285F4" placement="top">
                <div class="bline">
                    <el-button class="pa" size="mini" type="danger" @click="deleteAction(item)">移除</el-button>
                    <div class="itemcss">
                        <div class="mb10">{{item.createByName}}</div>
                        <div class="mb10 timecss">{{item.createTime}}</div>
                        <p class="mb10 ttextcss">回访概况：</p>
                        <div class="mb10 cardcss">
                            <p>{{item.revisitResult}}</p>
                        </div>
                        <div class="mb10">
                            <el-row>
                                <el-col :span="8">
                                    <span class="lh30">选用意向：{{optionNames[item.selectIntention]}}</span> 
                                </el-col>
                                <el-col :span="8">
                                    <span class="lh30">选用数量：{{item.selectNumber}}册</span> 
                                </el-col>
                            </el-row>
                            
                        </div>
                        <p class="mb10 ttextcss">原因说明：</p>
                        <div class="mb cardcss">
                            <p>{{item.note}}</p>
                        </div>
                    </div>
                </div>
                    
                    
                </el-timeline-item>
            </el-timeline>
            <nolist v-else></nolist>
        </div>
        
        <el-dialog title="添加回访" class="addrevisitcss" :visible.sync="addVisible" append-to-body center>
            <el-form ref="addform" class="revisitcss" :model="form" :rules="rules" label-width="100px">
                <el-row :gutter="0">
                    <el-col :span="24">
                        <el-form-item  label='回访概况：' prop="revisitResult">
                            <el-input class="definput" type="textarea" :maxlength="200" show-word-limit :rows="5"
                                v-model="form.revisitResult" placeholder="请输入回访概况"></el-input>
                        </el-form-item>
                        <el-form-item v-show="type == 1"  label='选用意向：' prop="selectIntention">
                            <el-select class="definput" v-model="form.selectIntention" placeholder="请选择">
                                <el-option
                                v-for="item in options"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-show="type == 1"  label='选用数量：' prop="selectNumber">
                            <el-input class="definput" type="number"  v-model="form.selectNumber" placeholder="请输入选用数量"></el-input>
                        </el-form-item>
                        <el-form-item v-show="type == 1"  label='原因说明：' prop="note">
                            <el-input class="definput" type="textarea" :maxlength="200" show-word-limit :rows="5"
                                v-model="form.note" placeholder="请输入原因说明"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
            <div class="center">
                <el-button class="submitbtn defaultbtn" type="primary" :loading="isSubmit"
                    @click="submitForm()">提交</el-button>
            </div>

        </el-dialog>
        <dc-dialog iType="1" title="确定删除吗？" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog"
            :appendToBody="true">
            <template>

            </template>
            <p class="pcc">是否删除该回访？</p>
        </dc-dialog>
    </el-dialog>
</template>

<script>

import { distributionRevisitList, queryDetailOverview, deleteDistributionRevisit, addDistributionRevisit } from "@/api/clientMaintenance/give";
import nolist from '@/components/common/nolist.vue';
export default {
    components:{
        nolist
    },
    data() {
        return {
            dialogVisible: false,
            addVisible: false,
            activeName: "first",
            dataList: [],
            form: {
                revisitResult: "",
                selectIntention: "",
                selectNumber:'',
                note:''
            },
            rules: {
                selectIntention: [
                    { required: true, message: '请选择选用意向', trigger: 'change' },
                ],
                selectNumber: [
                    { required: true, message: '请选择选用数量', trigger: 'blur' },
                ],
                note: [
                    { required: true, message: '请选择原因说明', trigger: 'blur' },
                ],
                revisitResult: [
                    { required: true, message: '请输入回访概况', trigger: 'blur' },
                    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
                ],
            },
            options:[
                {id:1,name:'高'},
                {id:2,name:'较高'},
                {id:3,name:'一般'},
                {id:4,name:'较低'},
                {id:5,name:'低'},
            ],
            optionNames:{
                1:'高',
                2:'较高',
                3:'一般',
                4:'较低',
                5:'低'
            },
            currentData: {},//当前发放数据 
            deleteData: {},
            isSubmit: false,
            isLoading: false,
            totaldata: {
                distributionNumber: 0,
                distributionTotalAmount: 0
            },
            pageBean: {
            },
            total: 13,
        }
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        type:{
            type: Number,
            default:1
        },
    },
    computed: {
        dialogTableVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('updateVisible', val);
            },

        }
    },
    methods: {
        init(data) {
            this.currentData = data;
            this.form.distributionId = data.id;
            this.pageBean.distributionId = data.id;
            this.loadList();

        },
        loadList() {
            this.isLoading = true;
            distributionRevisitList(this.pageBean).then((result) => {
                this.dataList = result.data;
                this.total = result.page.total;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        addAction() {
            this.addVisible = true;
        },
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.loadList();
        },
        deleteAction(val) {
            this.dialogVisible = true;
            this.deleteData = val;
        },
        beforeClose() {
            this.addVisible = false;
            var keys = Object.keys(this.form);
            this.$refs['addform'].resetFields();
            keys.forEach(element => {
                if (element != 'distributionId') {
                    this.form[element] = ''
                }
            });
        },
        // 提交表单
        submitForm() {
            this.$refs['addform'].validate((valid) => {
                if (!valid) {
                    return false;
                }
                if (this.form.id) {
                    // 编辑
                    this.updateData();
                } else {
                    // 添加
                    this.addData();
                }
            });
        },
        addData() {
            this.isSubmit = true;
            addDistributionRevisit(this.form).then((result) => {
                if (result.data) {
                    this.$message({
                        type: "success",
                        message: "添加成功！"
                    })
                    this.loadList();
                    this.$parent.loadData();
                    this.beforeClose();
                } else {
                    this.$message({
                        type: "error",
                        message: "保存失败！"
                    })
                }
                this.isSubmit = false;
            }).catch((err) => {
                this.isSubmit = false;
            });
        },
        updateData() {

        },
        submitDialog() {
            deleteDistributionRevisit({ id: this.deleteData.id }).then((result) => {
                if (result.data) {
                    this.$message({
                        type: "success",
                        message: "移除成功"
                    });
                    this.loadList();
                    this.$parent.loadData();
                    this.dialogVisible = false;
                } else {
                    this.$message({
                        type: "error",
                        message: result.msg
                    });
                }
            }).catch((err) => {

            });

        },
    }
}
</script>
<style scoped>
.pa{
    position: absolute;
    top: 5px;
    right: 5px;
}
.cardcss{
    padding:10px 16px;
    border-radius: 5px;
    border: 1px solid #e7e7e7 !important;
    line-height: 24px;
}
.itemcss{
    width: calc(100% - 100px);
}
.bline{
    position: relative;
    padding-bottom: 5px;
    /* border-bottom: 1px solid #e7e7e7 !important; */
}
.lh30{
    line-height: 30px;
}
.ttextcss{
    color: #4285F4;
    font-weight: 500;
}
.timeline /deep/.el-timeline-item__tail{
    border-left:2px solid #4285F4;
    left: 5px;
}
.timecss{
    color: #999999;
    font-size: 12px;
}
.timeline /deep/.el-timeline-item__node--normal{
    left: 0px;
}
.listh{
    height: calc(100% - 100px) !important;
    overflow-y: auto;
}
.topcss{
    height: 40px;
    text-align: right;
    margin-bottom: 20px;
}
.mb{
    margin-bottom: 20px;
}

.width100 {
    width: 100%;
}

.submitbtn {
    margin-top: 100px;
}

.lh {
    width: 100%;
    line-height: 23px;
}

.ttext {
    color: #999999;
    line-height: 34px;
}

.center {
    margin-top: 50px;
    text-align: center;
}

.bbtn {
    color: #4285F4;
    cursor: pointer;
}

.rbtn {
    color: #F45961;
    cursor: pointer;
}

.right{
    text-align: right;
}

.revisitcss {
    line-height: 34px;
}

.revisitcss /deep/.el-form-item__label {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
}

.revisitcss /deep/.el-form-item {
    margin-bottom: 20px;
    /* padding-right: 60px; */
}

.ml20 {
    margin-left: 20px;
}

.revisitdialog /deep/.el-dialog {
    min-width: 700px !important;
    height: calc(100% - 200px) !important;
    
}

.revisitdialog /deep/.el-dialog__body {
    padding: 20px;
    padding-top: 0px;
    height: 100% !important;
}

.revisitdialog /deep/.el-dialog__header {
    border: none;
}
</style>
<style scoped>
.tabscss /deep/.el-tabs__item {
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 40px;
    width: 240px !important;
    text-align: center;
    line-height: 40px;
    /* padding: 0 60px; */
}
</style>