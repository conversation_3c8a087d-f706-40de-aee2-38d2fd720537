import service from '@/utils/request.js'
export function getParentModule() {
	return service.request({
		method: 'get',
		url: '/sf/business/resource/menuListByUser',
	});
}
export function addModules(data) {
	return service.request({
		method: 'post',
		url: '/goldcourse/manager/resource/save',
		data
	});
}
export function selectModulesByFid(data) {
	return service.request({
		method: 'get',
		url: `/goldcourse/manager/resource/info/${data}`,
	});
}
export function updateModules(data) {
	return service.request({
		method: 'post',
		url: '/goldcourse/manager/resource/update',
		data
	});
}


export function deleteByIds(data) {
	return service.request({
		method: 'post',
		url: `/goldcourse/manager/resource/delete`,
		data
	});
}
