<template>
  <div class="mainbg" ref="box">
    <div v-if="dataList.length == 0 && loadBoo " class="nolist">
      <img class="noimg" src="../../../assets/no.png" alt="">
      <p>暂无记录~</p>
    </div>
    <listitem @loadData="loadData" @audioPlay="audioPlay" :key="index" v-for="(item,index) in dataList" :item="item"></listitem>
    <div class="loading"  v-loading="loading">
    </div>
  </div>
</template>

<script>
import listitem from '../../common/listitem.vue';
import {visitList} from '@/api/visit/index'
export default {
  components:{
    listitem,
  },
  data(){
    return{
      loading:true,
      dataList:[],
      pageBean:{
        unitId:this.$route.query.id,
        pageNum:1,
        pageSize:2,
      },
      total:0,
      types:[
                {
                    label:"电话拜访",
                    value:"1",
                },
                {
                    label:"线上拜访",
                    value:"2",
                },
                {
                    label:"实地拜访",
                    value:"3",
                },
            ],
            onOff:false,
            loadBoo:false
    }
  },
  created(){
    this.getData()
  },
  mounted () {
    document.getElementById('section').addEventListener('scroll', this.lazyLoading) // 滚动到底部，再加载的处理事件
},
beforeDestroy(){
  document.getElementById('section').removeEventListener('scroll', this.lazyLoading) // 滚动到底部，再加载的处理事件
},
  methods:{
    // 滚动加载
	lazyLoading (e) {
    if(!this.onOff){
      const scrollTop = e.target.scrollTop // 滚动条滚动时，距离顶部的距离
	    const windowHeight = e.target.clientHeight // 可视区的高度
	    const scrollHeight = e.target.scrollHeight // 滚动条的总高度
	    // 滚动条到底部
	    if (scrollTop + windowHeight === scrollHeight) {
          this.onOff = true
          this.loading = true
	        this.pageBean.pageNum++
	        if (this.pageBean.pageNum > Math.ceil(this.total / this.pageBean.pageSize)) {
            this.loading = false
            return
          } 
	        this.getData()
	    }
    }
	},
  search(){
        this.pageBean.pageNum = 1
        this.getData(1)
  },
  getData(type){
    visitList(this.pageBean).then(res=>{
      if(res.status == 0){
        if(type){
          this.dataList = []
        }
        this.dataList = [...this.dataList,...res.data]
        this.total = res.page.total
        this.dataList.forEach(item=>{
          if(item.soundList.length>0){
            this.$set(item,'isPlay',false)
          }
        })
        this.onOff = false
        this.loading = false
        this.loadBoo = true
      }
    })
  },
  loadData(){
    this.getData(1)
  },
    audioPlay(item,boolean){
        this.dataList.forEach(item1=>{
          if(item1.soundList.length>0){
            item1.isPlay = false
          }
        })
        console.log(boolean)
       item.isPlay =boolean
    },
    addVisit(){
      this.$router.push('/clientMaintenance/followVisit/add')
    }
  }

}
</script>
<style scoped>
.nolist{
  height: calc(100vh - 283px);
  text-align: center;
  overflow: hidden;
}
.noimg{
  margin-top: 180px;
}
.loading{
  width: 100px;
  height: 60px;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.f16{
  font-size: 16px;
  color: #333333;
}

.pb15{
  padding-bottom: 15px;
}
.mt3{
  margin-top: 3px;
}
.ml{
  margin-left: -80px;
}
.mainbg{
  background-color: white;
  padding: 20px;
}

.myform /deep/.el-form-item{
    /* min-width: 250px;
    max-width: 300px; */
    margin-bottom: 15px;
    padding-right: 3vw;
}
.right{
    text-align: right;
}
</style>