import router from "@/router";
import AppIndex from "@/pages/AppIndex.vue";
export default {
  methods: {
    async getSession() {
      // let res = await this.$axios.get('/sf/business/resource/menuList')
      // let res = await this.$axios.get('/sf/business/resource/menuListByUser')
      let res = await this.$axios.get('/sf/business/resource/menuListByUser')
      if (res.status === 0) {
        debugger
        let menuTotal = res.data.menuList;
        let permissionmenu = res.data.resourceList;
        let buttonPermissionKey = res.data.permissionKeys;
        let appRouters = [{
          path: "/AppIndex",
          name: "AppIndex",
          component: AppIndex
        }];
        let childrenData = [];
        let initRrouters = function (routers, menulist) {
          for (let i = 0; i < menulist.length; i++) {
            if (menulist[i].children && menulist[i].children.length > 0) {
              menulist[i].children.forEach(item => {
                recursionFun(routers, item)
              })
            } else {
              let url = ""
              let component = ""
              if (menulist[i].sUrl) {
                url = `${menulist[i].sUrl}`
                component = `${menulist[i].sUrl}`
              }

              let menu = {
                path: url,
                name: menulist[i].name,
                component: () => import(`@/pages${component}`)
              };
              routers.push(menu);
            }
          }
        }
        let recursionFun = function (routers, item) {
          if (item.children && item.children.length > 0) {
            item.children.forEach(k => {
              recursionFun(routers, k)
            })
          } else {
            let url = ""
            let component = ""
            if (item.sUrl) {
              url = `${item.sUrl}`
              component = `${item.sUrl}`
            }
            let menu = {
              path: url,
              name: item.name,
              component: () => import(`@/pages${component}`)
            };
            routers.push(menu);
          }
        }
        initRrouters(childrenData, menuTotal);
        appRouters[0].children = childrenData;
        router.addRoutes(appRouters);
        this.$store.commit("setMenu", { menuList: menuTotal });
        this.$store.commit("setPermissionMenu", { menuList: permissionmenu });
        this.$store.commit('setButtonPermissionKey', { menuList: buttonPermissionKey })
      }
    }
  }
}
