<template>
  <div>
    <div v-if="!isCreateBy" class="fl mb30 mtop20">
      <div class="tcss">累计成本：{{ totalCost }}</div>
    </div>
    <el-table
      :data="tableData"
      class="mytable"
      height="590px"
      style="width: 100%"
    >
      <el-table-column prop="costDate" label="日期" width="120">
      </el-table-column>
      <el-table-column
        v-if="!isCreateBy"
        prop="taskName"
        label="任务"
        min-width="180"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column prop="reason" label="成本事项" min-width="180">
      </el-table-column>
      <el-table-column prop="costAmount" label="金额(元)" min-width="180">
      </el-table-column>
      <el-table-column prop="costTypeName" label="费用类型" min-width="180">
      </el-table-column>
      <el-table-column
        v-if="!isCreateBy"
        prop="createByName"
        label="创建人"
        min-width="120"
      >
      </el-table-column>
      <el-table-column
        v-if="isControl"
        prop="edit"
        label="操作"
        width="80"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <span class="rbtn" @click="showDelete(scope.row)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <page
      :currentPage="pageBean.pageNum"
      :total="total"
      :pageSize="pageBean.pageSize"
      @updatePageNum="handleCurrentChange"
    ></page>
  </div>
</template>

<script>
import page from '@/components/common/page.vue'

import { constList, constDelete, queryCostListForTask } from '@/api/project'
import Bus from '@/utils/EventBus'

export default {
  props: {
    isControl: {
      type: Boolean,
      default: false,
    },
    isCreateBy: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '1',
    },
  },
  // inject: ['taskIdObj'],
  inject: {
    taskIdObj: {
      default: () => ({}),
    },
  },
  data() {
    return {
      tableData: [],
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        taskId: '',
      },
      totalCost: '',
      total: 0,
    }
  },
  created() {
    Bus.$on('loadDataConst', (msg) => {
      this.pageBean.taskId = this.taskIdObj.taskId
      this.loadData()
    })
  },
  components: {
    page,
  },
  methods: {
    showDelete(row) {
      constDelete({ id: row.id })
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '删除成功',
            })
            this.loadData()
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
        })
        .catch((err) => {})
    },
    loadData() {
      this.pageBean.taskType = this.type
      constList(this.pageBean).then((res) => {
        if (res.status == 0) {
          this.tableData = res.data
          this.total = res.page.total
        }
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      if (this.isControl) {
        this.loadData()
      } else {
        this.loadcost()
      }
    },
    updateTaskId(taskId) {
      this.pageBean.taskId = taskId
    },
    loadcost(taskId) {
      if (taskId) {
        this.pageBean.taskId = taskId
      }
      queryCostListForTask(this.pageBean)
        .then((result) => {
          this.totalCost = result.data.totalCost
          this.tableData = result.data.taskCostVoList
          this.total = result.page.total
        })
        .catch((err) => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.fl {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.mtop20 {
  margin-top: 20px;
}
.tcss + .tcss {
  margin-left: 10px;
}
.tcss {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}
.rbtn {
  color: #f45961;
  cursor: pointer;
}
.mytable {
  border: 1px solid #e6e6e6;
  border-radius: 8px;
}
</style>