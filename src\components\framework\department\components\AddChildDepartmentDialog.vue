<template>
  <el-dialog
    title="添加子部门"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form :model="form" :rules="rules" ref="childDepartmentForm" label-width="100px">
      <el-form-item label="子部门名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入子部门名称" maxlength="15" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="上级部门" prop="parentId">
        <el-input v-model="department.name" disabled></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sortno">
        <el-input-number v-model="form.sortno" :min="0" 
        :max="999" :controls="false"></el-input-number>
      </el-form-item>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddChildDepartmentDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    department: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        name: '',
        parentId: '',
        sortno: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入子部门名称', trigger: 'blur' },
        ],

      }
    };
  },
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },

  methods: {
    handleClosed() {
      this.$refs.childDepartmentForm.resetFields();
    },
    handleSubmit() {
      this.$refs.childDepartmentForm.validate(valid => {
        if (valid) {
          const parentId = this.department.id;
          this.$emit('submit', { 
            ...this.form,
            parentId
          });
        }
      });
    }
  }
};
</script> 
<style scoped lang="scss">
.dialog-footer{
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
        