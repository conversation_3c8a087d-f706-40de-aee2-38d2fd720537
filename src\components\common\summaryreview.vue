<template>
  <div class="bgdiv" v-loading="loading">
    <img
      class="closebtn"
      @click="close"
      src="../../assets/img/<EMAIL>"
      alt=""
    />
    <div v-if="viewtype == 'review'">
      <div class="headbg clearfix">
        <el-button
          v-if="!pageData.isFirstPage"
          type="text"
          class="bbtn w80 prevb"
          icon="el-icon-arrow-left"
          @click="lastData"
        >
          上一个
        </el-button>
        <div class="centertitle">
          {{ getTimestr(reviewData) }}{{ types[plantype].name }}
        </div>
        <el-button
          v-if="!pageData.isLastPage"
          type="text"
          class="bbtn w80 nextb"
          @click="handleCurrentChange"
        >
          下一个
          <i class="el-icon-arrow-right el-icon--right"></i>
        </el-button>
      </div>
      <div class="ctimecss bbtn">
        <div class="timecss">
          {{ searchTime }}
          <i class="el-icon-arrow-down"></i>
        </div>
        <el-date-picker
          class="mydatecss"
          v-model="planTime"
          @change="changeDatePicker"
          :type="types[plantype].type"
          :format="types[plantype].format"
          placeholder="请选择时间"
          prefix-icon="none"
          clear-icon="none"
        >
        </el-date-picker>
      </div>
    </div>
    <div v-else></div>
    <textBorder>个人{{ types[plantype].name }}</textBorder>
    <addzongcom
      ref="addzongcom"
      layouttype="2"
      :zjtype="plantype"
      :zjid="reviewData.id"
    ></addzongcom>
    <textBorder>总结内容</textBorder>
    <div class="modelcss pt20">
      <showmodel :viewDataList="reviewData.templateItemList"></showmodel>
    </div>
    <textBorder>抄送人</textBorder>
    <div class="modelcss pt20">
      <ul class="uflex" v-if="reviewData.copyPersons.length > 0">
        <li
          v-for="(item, index) in reviewData.copyPersons"
          :key="index"
          class="pli"
        >
          <img v-if="item.logo" class="pimg" :src="item.logo" alt="" />
          <div class="noimg" v-else>
            {{ item.name }}
          </div>
          <p class="pname">{{ item.name }}</p>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import textBorder from '@/components/common/textBorder.vue'
import showmodel from '@/components/zongjie/temmodel/showmodel.vue'
import addzongcom from '@/components/zongjie/components/addzongcom.vue'
import { getPlanTime, getWeek } from '../../utils/index'
import { queryReviewGoals } from '@/api/goal'
export default {
  components: {
    textBorder,
    addzongcom,
    showmodel,
  },
  props: {
    plantype: {
      type: String,
      default: '4',
    },
    viewtype: {
      type: String,
      default: 'review',
    },
    viewData: {
      type: Array,
      default: () => {
        return new Array()
      },
    },
    date: {
      type: Date,
      default: () => {
        return new Date()
      },
    },
    page: {
      type: Object,
      default: () => {
        return new Object()
      },
    },
  },
  data() {
    return {
      loading: false,
      types: {
        4: {
          name: '年度总结',
          name2: '年度',
          type: 'year',
          format: 'yyyy',
        },
        5: {
          name: '月度总结',
          name2: '月度',
          type: 'month',
          format: 'yyyy-M',
        },
        6: {
          name: '总结',
          name2: '',
          type: 'week',
          format: 'yyyy 第 W 周',
        },
      },
      planTime: '',
      sForm: {
        goalsType: this.plantype + '',
        createBy: '',
        year: '',
        month: '',
        week: '',
        pageNum: 1,
        pageSize: 1,
      },
      searchTime: '',
      reviewData: {},
      pageData: {},
    }
  },
  created() {
    this.reviewData = this.viewData[0]
    this.sForm.createBy = this.reviewData.createBy
    this.sForm.year = this.reviewData.year != '0' ? this.reviewData.year : ''
    this.sForm.month = this.reviewData.month != '0' ? this.reviewData.month : ''
    this.sForm.week = this.reviewData.week != '0' ? this.reviewData.week : ''
    this.pageData = this.page
    this.planTime = this.date
    this.searchTime =
      this.getTimestr(this.reviewData, this.plantype) +
      this.types[this.plantype].name2
  },
  methods: {
    close() {
      this.$emit('closereview')
    },
    loadData() {
      queryReviewGoals(this.sForm)
        .then((result) => {
          if (result.data && result.data.length > 0) {
            this.loading = false
            this.reviewData = result.data[0]
            this.$nextTick(() => {
              this.$refs.addzongcom.loadData()
            })
            this.pageData = result.page
            this.searchTime =
              this.getTimestr(this.reviewData, this.plantype) +
              this.types[this.plantype].name2
          } else {
            this.loading = false
            this.$message({
              type: 'error',
              message: '暂无总结回顾可查看',
            })
          }
        })
        .catch((err) => {})
    },
    changeDatePicker(date) {
      if (!date) {
        this.sForm.year = ''
        this.sForm.month = ''
        this.sForm.week = ''
        return
      }
      var newDate = new Date(date)
      this.sForm.year = newDate.getFullYear()
      if (this.plantype == 5) {
        this.sForm.month = newDate.getMonth() + 1
      } else if (this.plantype == 6) {
        var time = getWeek(date)
        var times = time.split('-')
        this.sForm.year = times[0]
        this.sForm.week = times[1]
      }
      this.sForm.pageNum = 1
      this.loading = true
      this.loadData()
    },
    lastData() {
      this.sForm.pageNum--
      this.loadData()
    },
    handleCurrentChange() {
      this.sForm.pageNum++
      this.loadData()
    },
    getTimestr(data) {
      return getPlanTime(data, this.plantype)
    },
  },
}
</script>
<style scoped>
.pname {
  text-align: center;
}
.noimg {
  width: 48px;
  height: 48px;
  line-height: 48px;
  border-radius: 8px;
  background-color: #4285f4;
  text-align: center;
  color: #fff;
  font-size: 12px;
}
.uflex {
  display: flex;
  flex-wrap: wrap;
}
.closeimg {
  position: absolute;
  right: -8px;
  top: -8px;
  cursor: pointer;
}
.pli {
  margin-right: 16px;
  margin-bottom: 16px;
  position: relative;
}
.pli:last-child {
  margin-right: 0px;
}
.pimg {
  width: 48px;
  height: 48px;
  border-radius: 8px 8px 8px 8px;
}
.pflex {
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
}
.adddi {
  width: 44px;
  height: 44px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d6d6d6;
  text-align: center;
  line-height: 44px;
  font-size: 18px;
  color: #d6d6d6;
  cursor: pointer;
}
.nextb {
  position: absolute;
  top: -12px;
  right: 0;
}
.prevb {
  position: absolute;
  top: -12px;
  left: 0;
}
.centertitle {
  height: 26px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
  line-height: 23px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.mydatecss /deep/.el-input__inner {
  border: none;
  text-align: center;
  color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
}
.mydatecss {
  position: absolute;
  top: 0;
  left: calc(50% - 50px);
  width: 100px;
  background-color: rgba(0, 0, 0, 0);
}
.ctimecss {
  position: relative;
  cursor: pointer;
  text-align: center;
  margin-bottom: 20px;
}

.tablebox {
  height: 337px;
  overflow-x: hidden;
  padding-right: 10px;
  margin: 16px 0;
}
.myform /deep/.el-form-item {
  margin-bottom: 0px !important;
}
.myform {
  margin: 16px 0;
}
.headbg {
  height: 30px;
  position: relative;
  margin-bottom: 12px;
}
.bgdiv {
  background-color: white;
  border-radius: 10px;
  min-height: calc(100vh - 140px);
  width: 100%;
  padding: 20px 14px;
  position: relative;
}
.closebtn {
  position: absolute;
  right: 8px;
  top: 10px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 100;
}
.imgcss {
  width: 3em;
  height: 3em;
}
.goalitem {
  height: 3em;
  margin-bottom: 20px;
  display: flex;
}

.textcss {
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 19px;
  margin-bottom: 10px;
}
.width100 {
  width: 100%;
}
.pl12 {
  padding-left: 8px;
}
</style>