<template>
    <el-dialog class="revisitdialog" title="回访" top="80px" :visible.sync="dialogTableVisible" width="70%" center>
        <div>
            <el-button class="right defaultbtn mb20" icon="el-icon-plus" type="primary" @click="addAction">添加回访</el-button>
        </div>
        <el-table :height="477" class="mytable" :data="dataList" style="width:100%" v-loading="isLoading">
            <el-table-column property="revisitTime" label="回访时间" align="center" width="200px"></el-table-column>
            <el-table-column property="revisitResult" label="回访结果" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column property="createByName" label="回访人" align="center" width="150px"></el-table-column>
            <el-table-column property="edit" width="150px" label="操作" align="center">
                <template slot-scope="scope">
                    <div class="rbtn deffont" @click="deleteAction(scope.row)">移除</div>
                </template>
            </el-table-column>
        </el-table>
        <page :currentPage="pageBean.pageNum" :pageSize="pageBean.pageSize" :total="total"
            @updatePageNum="handleCurrentChange"></page>
        <el-dialog title="添加回访" class="addrevisitcss" :visible.sync="addVisible" append-to-body center>
            <el-form ref="addform" class="revisitcss" :model="form" :rules="rules" label-width="100px">
                <el-row :gutter="0">
                    <el-col :span="24">
                        <el-form-item  label='回访时间：' prop="revisitTime">
                            <el-date-picker class="definput width100" v-model="form.revisitTime" type="datetime"
                                format="yyyy-MM-dd HH:mm" placeholder="选择回访时间">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item  label='回访结果：' prop="revisitResult">
                            <el-input class="definput" type="textarea" :maxlength="200" show-word-limit :rows="5"
                                v-model="form.revisitResult" placeholder="请输入回访结果"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
            <div class="center">
                <el-button class="submitbtn defaultbtn" type="primary" :loading="isSubmit"
                    @click="submitForm()">提交</el-button>
            </div>

        </el-dialog>
        <dc-dialog iType="1" title="确定删除吗？" width="500px" :dialogVisible.sync="dialogVisible" @submit="submitDialog"
            :appendToBody="true">
            <template>

            </template>
            <p class="pcc">是否删除该回访？</p>
        </dc-dialog>
    </el-dialog>
</template>

<script>

import page from '@/components/common/page.vue';
import { distributionRevisitList, queryDetailOverview, deleteDistributionRevisit, addDistributionRevisit } from "@/api/clientMaintenance/give";

export default {
    components: {
        page
    },
    data() {
        return {
            dialogVisible: false,
            addVisible: false,
            activeName: "first",
            dataList: [],
            form: {
                revisitResult: "",
                revisitTime: ""
            },
            rules: {
                revisitTime: [
                    { required: true, message: '请选择回访时间', trigger: 'chang e' },
                ],
                revisitResult: [
                    { required: true, message: '请输入回访结果', trigger: 'blur' },
                    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
                ],
            },
            currentData: {},//当前发放数据 
            deleteData: {},
            isSubmit: false,
            isLoading: false,
            totaldata: {
                distributionNumber: 0,
                distributionTotalAmount: 0
            },
            pageBean: {
                pageNum: 1,
                pageSize: 8,
            },
            total: 13,
        }
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
    },
    computed: {
        dialogTableVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('updateVisible', val);
            },

        }
    },
    methods: {
        init(data) {
            this.currentData = data;
            this.form.distributionId = data.id;
            this.pageBean.distributionId = data.id;
            this.loadList();

        },
        loadList() {
            this.isLoading = true;
            distributionRevisitList(this.pageBean).then((result) => {
                this.dataList = result.data;
                this.total = result.page.total;
                this.isLoading = false;
            }).catch((err) => {
                this.isLoading = false;
            });
        },
        addAction() {
            this.addVisible = true;
        },
        handleCurrentChange(page) {
            this.pageBean.pageNum = page;
            this.loadList();
        },
        deleteAction(val) {
            this.dialogVisible = true;
            this.deleteData = val;
        },
        beforeClose() {
            this.addVisible = false;
            var keys = Object.keys(this.form);
            this.$refs['addform'].resetFields();
            keys.forEach(element => {
                if (element != 'distributionId') {
                    this.form[element] = ''
                }
            });
        },
        // 提交表单
        submitForm() {
            this.$refs['addform'].validate((valid) => {
                if (!valid) {
                    return false;
                }
                if (this.form.id) {
                    // 编辑
                    this.updateData();
                } else {
                    // 添加
                    this.addData();
                }
            });
        },
        addData() {
            this.isSubmit = true;
            addDistributionRevisit(this.form).then((result) => {
                if (result.data) {
                    this.$message({
                        type: "success",
                        message: "添加成功！"
                    })
                    this.loadList();
                    this.$parent.loadData();
                    this.beforeClose();
                } else {
                    this.$message({
                        type: "error",
                        message: "保存失败！"
                    })
                }
                this.isSubmit = false;
            }).catch((err) => {
                this.isSubmit = false;
            });
        },
        updateData() {

        },
        submitDialog() {
            deleteDistributionRevisit({ id: this.deleteData.id }).then((result) => {
                if (result.data) {
                    this.$message({
                        type: "success",
                        message: "移除成功"
                    });
                    this.loadList();
                    this.$parent.loadData();
                    this.dialogVisible = false;
                } else {
                    this.$message({
                        type: "error",
                        message: result.msg
                    });
                }
            }).catch((err) => {

            });

        },
    }
}
</script>
<style scoped>
.width100 {
    width: 100%;
}

.submitbtn {
    margin-top: 100px;
}

.lh {
    width: 100%;
    line-height: 23px;
}

.ttext {
    color: #999999;
    line-height: 34px;
}

.center {
    margin-top: 50px;
    text-align: center;
}

.bbtn {
    color: #4285F4;
    cursor: pointer;
}

.rbtn {
    color: #F45961;
    cursor: pointer;
}

.revisitcss {
    line-height: 34px;
}

.revisitcss /deep/.el-form-item__label {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
}

.revisitcss /deep/.el-form-item {
    margin-bottom: 20px;
    /* padding-right: 60px; */
}

.ml20 {
    margin-left: 20px;
}

.revisitdialog /deep/.el-dialog {
    min-width: 700px !important;
}

.revisitdialog /deep/.el-dialog__body {
    padding: 20px;
    padding-top: 0px;
}

.revisitdialog /deep/.el-dialog__header {
    border: none;
}
</style>
<style scoped>
.tabscss /deep/.el-tabs__item {
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    height: 40px;
    width: 240px !important;
    text-align: center;
    line-height: 40px;
    /* padding: 0 60px; */
}
</style>