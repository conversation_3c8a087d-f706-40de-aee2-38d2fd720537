<template>
  <div>
    <el-table class="detailList mytable"  :data="tableData" style="width: 100%" v-loading="isLoading">
      <el-table-column
        prop="contractTitle"
        label="合同标题"
        align="center"
        class-name="column_blue"
        show-overflow-tooltip
        min-width="200px"
        >
      </el-table-column>
      <el-table-column
        prop="contractNumber"
        label="合同编号"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="customerName"
        label="客户姓名"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="unitName"
        label="客户单位"
        show-overflow-tooltip
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="contractAmount"
        label="合同金额（万元）"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="discount"
        label="整单折扣"
        align="center"
        width="167px"
        >
      </el-table-column>
      <el-table-column
        prop="returnStatus"
        align="center"
        label="回款状态"
        width="167px">
        <template slot-scope="scope">
          <div :style="{color:status1[scope.row.returnStatus].color}">{{status1[scope.row.returnStatus].name}}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="invoicingStatus"
        align="center"
        label="开票状态"
        width="167px">
        <template slot-scope="scope">
          <div :style="{color:status1[scope.row.invoicingStatus].color}">{{status2[scope.row.invoicingStatus].name}}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="167px"
        >
      </el-table-column>
      <!-- <el-table-column
        prop="edit"
        width="100"
        align="center"
        fixed="right"
        label="更多操作">
        <template slot-scope="scope">
          <el-button class="bbtn tabbtn" size="mini"  type="text" @click="onDetail(scope.row)"> 详情</el-button>
        </template>
         
      </el-table-column> -->
    </el-table>
    <page 
    :currentPage="pageBean.pageNum" 
    :total="total" 
    :pageSize="pageBean.pageSize" 
    @updatePageNum="handleCurrentChange"
    ></page>
  </div>
</template>

<script>

import page from '../../common/page.vue';
import { contractList } from "@/api/contract";
  export default {
    components:{
      page
    },
    data(){
      return{
        isLoading:false,
        tableData:[],
        status1:{
          1:{
            name:"未回款",
            color:"#FF8D1A"
          },
          2:{
            name:"部分回款",
            color:"#4285F4"
          },
          3:{
            name:"全部回款",
            color:"#56C36E"
          },
          4:{
            name:"超额回款",
            color:"#F45961"
          },
        },
        status2:{
          1:{
            name:"未开票",
            color:"#FF8D1A"
          },
          2:{
            name:"部分开票",
            color:"#4285F4"
          },
          3:{
            name:"全部开票",
            color:"#56C36E"
          },
          4:{
            name:"超额开票",
            color:"#F45961"
          },
        },
        total:0,
        pageBean:{
          unitId:this.$route.query.id,
          pageNum:1,
          pageSize:10,
        }
      }
    },
    created(){
      
      console.log("sssss",this.$route.query.id);
    },
    methods:{
      loadData(){
        this.isLoading = true;
        contractList(this.pageBean).then((result) => {
          this.tableData = result.data;
          this.total = result.page.total;
          this.isLoading = false;
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      onDetail(data){
        console.log(data)
        this.$router.push({
          path:"projectManagement/contract/detail",
          query:{
            id:data.name
          }
        })
      },
      // 项目名称列
      // columnFormatter(row,column,cellValue,index){

      // },
      handleCurrentChange(page){
        this.pageBean.pageNum = page;
        this.loadData();
      }
    }
  }
</script>

<style lang="scss" scoped>
.tabbtn{
  font-size: 14px;
}
.bbtn,.bbtn:hover,.bbtn:focus{
  color: #4285F4;
}
.c1{
  color:#F45961;
}
</style>