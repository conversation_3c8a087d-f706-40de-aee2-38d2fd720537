<template>
  <div class="bg">
    <div class="header">
      <el-form inline class="myform" label-width="72px">
        <el-form-item label="案例名称:">
          <el-input class="definput" v-model="pageBean.caseName" clearable placeholder="请输入案例名称"></el-input>
        </el-form-item>
        <el-form-item label=" 分享人:">
          <el-input class="definput" v-model="pageBean.createByName" clearable placeholder="请输入分享人"></el-input>
        </el-form-item>
        <el-form-item label="分享部门:">
          <el-input class="definput" v-model="pageBean.departmentName" clearable placeholder="请输入分享人部门"></el-input>
        </el-form-item>
        <el-form-item label="分享单位:">
          <el-input class="definput" v-model="pageBean.unitName" clearable placeholder="请输入单位"></el-input>
        </el-form-item>
        <el-form-item label="案例类型:">
          <el-select class="definput" popper-class="removescrollbar" v-model="pageBean.caseClassify" clearable
            placeholder="请选择案例类型">
            <el-option key="" label="全部" value=""></el-option>
            <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分享类型:">
          <el-select class="definput" popper-class="removescrollbar" v-model="pageBean.caseType" clearable
            placeholder="请选择分享类型">
            <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间:">
          <datepicker class="inputwidth" @submitAction="submitAction"></datepicker>
        </el-form-item>
        <el-form-item>
          <el-button class="defaultbtn " icon="el-icon-search" type="primary" @click="searchAction">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-dropdown
        placement="bottom-end"
        @command="handleCommand"
        trigger="click"
        class="dropright">
          <el-button v-isShow="'crm:controller:case:save'" class="defaultbtn sharebtn" icon="el-icon-share"
            type="primary">我要分享</el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1">视频类</el-dropdown-item>
            <el-dropdown-item :command="2">文章类</el-dropdown-item>
            <el-dropdown-item :command="3">文档类</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
    </div>
    <div class="concss">
      <div class="leftlist" v-loading="isLoading" :class="{width100:!checkPermission('/caseLibrary/rank')}">
        <el-row :gutter="12">
          <el-col class="mb12 w25 itemcss" :span="spanNum" :key="index" v-for="(item,index) in dataList">
            <div class="card" @click="detailAction(item)">
              <div class="posi">
                <span class="rtext">{{item.caseClassifyName}}</span>
                <img class="fengmian" :src="item.caseCoverUrl" alt="">
                <div class="lookdiv">
                  <span class="sptext"><img class="iconimg" src="../../../assets/look.png" alt="">
                    {{item.caseViewNum}}</span>
                  <span class="sptext mw67"><img class="iconimg" src="../../../assets/time.png" alt="">
                    {{item.createTime}}</span>
                </div>
              </div>
              <div class="condiv">
                <div class="jianjiecss">
                  {{item.caseName}}
                </div>
                <div class="lhcss" >
                  <div class="usercss">
                    <headuser
                    class="headimg"
                    :url="item.userLogo"
                    width="24"
                    :username="item.createByName"
                    alt="">
                    </headuser>
                    <span
                    class="namecss">
                      {{item.createByName}}
                    </span>
                  </div>
                  <span class="zantext" :class="{'zan_sel':item.isLike}" @click.stop="zanAction(item)"><img class="zanimg"
                      :src="item.isLike ? selZan : defZan" alt="">{{item.caseLikeNum}}</span>
                </div>
                <div class="datacss1">
                  <span class="tagitem" :key="index" v-for="(tag,index) in item.newTags "> {{tag}}</span>
                  <span class="tagitem mr0 el-icon-more" v-if="item.newTags.length != item.tags.length"></span>
                </div>
              </div>
            </div>
          </el-col>
          <noList v-if="dataList.length == 0 && loadBoo "></noList>
        </el-row>
        <page :currentPage="pageBean.pageNum" :pageSize="pageBean.pageSize" :total="total"
          @updatePageNum="handleCurrentChange"></page>
      </div>
      <div
      class="personal"
      @click="pushPersonal"
      v-isShow="'studyShare:caseLibrary:personal'">
          <img src="@/assets/personalicon.png" alt="">
          <div class="personcss">个人中心</div>
      </div>
      <msg-dialog ref="msgdialog"/>
    </div>
  </div>
</template>

<script>
  import page from '../../common/page.vue';
  import headuser from "../../common/headuser.vue";
  import datepicker from '@/components/common/datepicker.vue'
  import { studyshareList, queryCaseLearningRanking, deletestudyshare } from "@/api/studyshare/index";
  import { getDict,getParStr } from "@/utils/tools";
  import noList from '@/components/common/nolist.vue'

  import { checkPermission } from '@/utils/permission'
  export default {
    components: {
      page,
      headuser,
      noList,
      datepicker
    },
    data() {
      return {
        activeName:'first',
        loadBoo: false,
        types: [
          {
            id: '',
            name: '全部'
          },
          {
            id: '1',
            name: '视频类'
          },
          {
            id: '2',
            name: '文章类'
          },
          {
            id: '3',
            name: '文档类'
          }
        ],
        defZan: require('../../../assets/img/zan_def.png'),
        selZan: require('../../../assets/img/zan_sel.png'),
        tagMaxWid: 0,
        spanNum: 6,
        dataList: [],
        sortList: [],
        deleteVisible: false,
        deleteData: {},
        level: sessionStorage.getItem('dataScope'),
        userId: sessionStorage.getItem('userid'),
        isLoading: false,
        total: 0,
        pageBean: {
          caseName: "",
          caseType: "",
          caseClassify: "",
          createByName: "",
          departmentName:'',
          pageNum: 1,
          pageSize: 8,
          unitName:'',
          time:'',
          startTime:'',
          endTime:'',
          status:1,
        },
        options: [],
        rankPage: {
          pageNum: 1,
          pageSize: 10,
          rankType:1,
        },
        rankType:1,
        rankObj:{
          'first':1,
          'second':2,
        }
      }
    },
    created() {
      if (Object.keys(this.$route.query).length>0) {
          this.pageBean = Object.assign(this.pageBean,this.$route.query)
          this.pageBean.pageNum = Number(this.pageBean.pageNum)
          this.pageBean.pageSize = Number(this.pageBean.pageSize)
      }
      this.getTypeList();
      var wid = window.screen.availWidth;
      var conwidth = wid - 170 - 40;
      var listviewWidth = conwidth;
      var num = 0;
      if (listviewWidth < 1000) {
        this.spanNum = 6;
        num = 4;
        this.pageBean.pageSize = 8;
      } else {
        this.spanNum = 4;
        num = 6;
        this.pageBean.pageSize = 12;
      }
      var itemWidth = listviewWidth / num ;
      this.tagMaxWid = itemWidth - 36;
      console.log("sssssssddddddd",listviewWidth,this.tagMaxWid);
      this.loadData();
      this.loadRank();

    },
    mounted(){
       if (sessionStorage.getItem('isAddStudy')) {
          this.$refs.msgdialog.show({
            type:'success',
            title:'提交成功',
            msg:"您的学习分享已成功提交，感谢您的分享！",
            onClose:()=>{
              sessionStorage.removeItem('isAddStudy')
            }
          })
        }
    },
    methods: {
      handleClick(tab, event) {
                this.activeName = tab.name;
                this.rankType = this.rankObj[tab.name]
                this.rankPage.rankType = this.rankType
                this.loadRank()
      },
      getTypeList() {
        getDict('CaseType').then((result) => {
          this.options = result;
        }).catch((err) => {

        });
      },
      loadData() {
        history.replaceState(null,null,`#${this.$route.path}?${getParStr(this.pageBean)}`)
        this.isLoading = true;
        studyshareList(this.pageBean).then((result) => {
          result.data.forEach(item => {
            item.tags = item.label.split(',')
            item.newTags = this.processData(item.tags)
            item.isDelShow = this.level == 4 || this.userId == result.data.createBy;
          });
          this.dataList = result.data;
          this.total = result.page.total;
          this.isLoading = false;
          this.loadBoo = true
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      loadRank() {
        queryCaseLearningRanking(this.rankPage).then((result) => {
          this.sortList = result.data;
        }).catch((err) => {

        });
      },
      phDetail() {
        this.$router.push('/studyShare/caseLibrary/rank')
      },
      handleCommand(data) {
        this.$router.push({
          path: '/studyShare/caseLibrary/add',
          query: { type: data }
        })
      },
      searchAction() {
        this.pageBean.pageNum = 1;
        this.loadData();
      },
      zanAction(data) {

      },
      detailAction(data) {

        if (!checkPermission('caseLibrary/detail')) {
          this.$message({
            type: "error",
            message: '您没有操作权限'
          })
          return
        }
        this.$router.push({
          path: '/studyShare/caseLibrary/detail',
          query: { id: data.id }
        })
      },
      processData(tags) {
        var tagWidth = 0;
        var list = [];

        tags.forEach(item => {
          var itemWid = item.length * 16 + 16;
          tagWidth = tagWidth + itemWid;
          if (tagWidth > this.tagMaxWid) {
            return
          } else {
            list.push(item);
          }
        })
        return list;
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.loadData();
      },
      showDelete(data) {
        this.deleteData = data;
        this.deleteVisible = true;
      },
      submitDialog() {
        // 删除数据
        deletestudyshare({ id: this.deleteData.id }).then((result) => {
          if (result.data) {
            this.$message({
              type: "success",
              message: result.msg
            })
            this.deleteVisible = false;
            this.loadData();
          } else {
            this.$message({
              type: "error",
              message: result.msg
            })
          }
        }).catch((err) => {

        });
      },
      submitAction(type, dateRange) {
        if (type == 11) {
          if (dateRange) {
            this.pageBean.startTime = dateRange[0]
            this.pageBean.endTime = dateRange[1]
          } else {
            this.pageBean.startTime = ''
            this.pageBean.endTime = ''
          }
          this.pageBean.time = ''
        } else {
          this.pageBean.time = type
          this.pageBean.startTime = ''
          this.pageBean.endTime = ''
        }
      },
      pushPersonal(){
        this.$router.push('/studyShare/caseLibrary/personal')
      },
    }
  }
</script>
<style scoped>
.dropright{
  position: absolute;
  right: 20px;
  top: 20px;
}
.lhcss{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
}
.personcss{
  position: absolute;
  bottom: 0px;
  width: 56px;
  height: 18px;
  line-height: 18px;
  background: linear-gradient( 90deg, #0079FB 0%, #00B7F6 100%);
  border-radius: 99px 99px 99px 99px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
  text-align: center;
  font-style: normal;
  text-transform: none;
  z-index: 111 !important;
}
.personal{
  border-radius: 30px;
  position: fixed;
  right: 20px;
  bottom: 200px;
  width: 56px;
  height: 56px;
  text-align: center;

}
.personal img{
  width:48px;
  height:48px;
  z-index: index 10 !important;
  margin: 0 auto;
}
  .lookdiv{
    position: absolute;
    left: 0;
    bottom: 0;
    height: 40px;
    background: linear-gradient( 180deg, rgba(51,51,51,0) 0%, rgba(51,51,51,0.6) 100%);
    border-radius: 0px 0px 0px 0px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    line-height: 40px;
    text-align: right;
    color: #FFFFFF;
    width: 100%;
    padding-right: 5px;
  }
  .posi{
    position: relative;
  }
  .tabscss /deep/.el-tabs__item{
     width: 50%;
     text-align: center;
  }
  .tabscss /deep/.el-tabs__nav{
     width: 100%;
  }

  .mtrow{
    margin-top: 10px;
  }
  .deletebtn {
    position: absolute;
    width: 24px;
    height: 24px;
    top: 10px;
    right: 12px;
    cursor: pointer;
  }
  .mw67 {
    max-width: 67px;
  }

  .ml10 {
    margin-left: 5px;
  }

  .itemcss {
    position: relative;
    cursor: pointer;
  }

  .text24 {
    width: 24px;
    padding-top: 3px;
    text-align: center;
  }

  .flex {
    display: flex;
    align-items: center;
  }

  .img24 {
    width: 24px;
    height: 24px;
  }

  .rankhead {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-left: 8px;
    margin-right: 12px;
  }

  .rankcss {
    height: 56px;
    padding: 0px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .tc {

    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    line-height: 15px;
    cursor: pointer;
  }

  .phtitle {

    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 19px;

  }

  .paihang_t {
    background: url(../../../assets/liebg.png) no-repeat;
    background-size: 100% 100%;
    height: 20px;
    padding: 0px 16px;
  }

  .paihang_t img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }

  .mr0 {
    margin-right: 0px !important;
  }

  .datacss1 {
    margin-top: 8px;
    padding-left: 12px;
    padding-right: 4px;
    padding-bottom: 16px;
    height: 35px;
    overflow: hidden;
    white-space: nowrap;
    overflow: hidden;
  }

  .tagitem {
    margin-right: 8px;
    padding: 4px 7px;
    padding-bottom: 4px;
    background: #DFEAFD;
    background: #F6F7FB;
    border-radius: 2px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }

  .datacss {
    margin-top: 7px;
    height: 24px;
    background: rgba(255,164,76,0.12);
    border-radius: 2px 2px 2px 2px;
    max-width: calc(100% - 24px);
    width: auto;
    display: inline-block;
    margin: 0 12px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #FFA44C;
    box-sizing: border-box;
    padding: 0 8px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .rtext {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 14px;
    margin-top: 2px;
    display: block;
    width: 64px;
    height: 24px;
    background: rgba(0,0,0,0.4);
    border-radius: 2px 2px 2px 2px;
    position: absolute;
    left: 10px;
    top: 10px;
    text-align: center;
    line-height: 24px;
  }

  .sptext {
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    line-height: 14px;
    padding-right: 8px;
  }

  .jianjiecss {
    margin: 0px 12px;
    margin-top: 7px;
    margin-bottom:12px;
    height: 23px;
    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #444444;
    line-height: 23px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .zantext {
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    line-height: 16px;
    color: #333333;
  }

  .zan_sel {
    color: #FFA44C;
  }

  .iconimg {
    width: 16px;
    height: 16px;
    opacity: 1;
    margin-top: -3px;
  }

  .zanimg {
    width: 16px;
    height: 16px;
    opacity: 1;
    margin-top: -3px;
    margin-right: 4px;
  }

  .namecss {
    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    line-height: 24px;
  }

  .condiv {
    position: relative;
  }

  .headimg {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    opacity: 1;
  }

  .mb12 {
    margin-bottom: 12px;
  }

  .fengmian {
    width: 100%;
    border-radius: 8px 8px 0px 0px;
    aspect-ratio: 4 / 3;
  }

  .card {
    width: 100%;
    background-color: white;
    border-radius: 8px;
  }

  .rlist {
    width: 20%;
    min-width: 260px;
    background-color: white;
    border-radius: 8px;
  }

  .leftlist {
    margin-right: 12px;
    width: 100%;
    height: 100%;
    min-height: calc(100vh - 300px);
  }

  .concss {
    display: flex;
    padding: 20px;
    min-height: calc(100% - 74px);
  }

  .mt {
    margin-top: 0px;
  }

  .ml10 {
    margin-left: 20px;
  }

  .rheadcss {
    height: 38px;
    line-height: 38px;
    width: 15vw;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .header {
    width: 100%;
    padding: 0px 20px;
    padding-top: 17px;
    background-color: white;
    display: flex;
  }
  .myform{
    width: calc(100% - 120px);
  }
  .myform /deep/.el-form-item {
    margin-bottom: 10px !important;
    padding-right: 1vw;
  }

  .myform /deep/.el-form-item .el-input__inner {
    width: 100% !important;
  }

  .bg {

    min-height: calc(100% + 40px);
    margin: -20px;
  }

  .llist {
    /* height:  */
  }
  .definput{
    width: 220px !important ;
  }
</style>
<style>

</style>
