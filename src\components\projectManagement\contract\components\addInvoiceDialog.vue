<template>
    <el-dialog 
    title="添加发货单" 
    class="adddetailcss"
    width="45%" 
    :visible.sync="addVisible" 
    :before-close="beforeClose"
    append-to-body 
    center>
        <el-form ref="addform" class="revisitcss myform" :model="form" :rules="rules" label-width="95px">
            <el-row type="flex" justify="center" :gutter="0">
                <el-col :span="16">
                    <el-form-item   label='物流单号：' prop="trackingNumber">
                        <el-input  class="definput" v-model="form.trackingNumber" placeholder="请输入物流单号"></el-input>
                    </el-form-item>
                    <el-form-item   label='教材名称：' prop="teachingMaterial">
                        <el-input  class="definput" v-model="form.teachingMaterial" placeholder="请输入教材名称"></el-input>
                    </el-form-item>
                    <el-form-item  label='出版社：' prop="publish">
                        <el-input  class="definput" v-model="form.publish" placeholder="请输入出版社名称"></el-input>
                    </el-form-item>
                    <el-form-item   label='书号：' prop="isbn">
                        <el-input  class="definput" v-model="form.isbn" placeholder="请输入书号"></el-input>
                    </el-form-item>
                    <el-form-item  label='价格：' prop="price">
                        <el-input class="definput"  type="number"  v-model="form.price" placeholder="请输入价格" ></el-input>
                    </el-form-item>
                    <el-form-item  label='数量：' prop="number">
                        <el-input class="definput"  type="number"  v-model="form.number" placeholder="请输入数量" ></el-input>
                    </el-form-item>
                    <el-form-item  label='折扣：' prop="discount">
                        <el-input class="definput"  type="number" :min="0" :max="1" v-model="form.discount" placeholder="请输入折扣" ></el-input>
                    </el-form-item>
                    <el-form-item  label='发货时间：' prop="deliveryTime">
                        <el-date-picker
                        class="definput w100"
                        v-model="form.deliveryTime"
                        type="datetime"
                        placeholder="选择发货时间">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="center">
            <el-button class="submitbtn defaultbtn" type="primary" @click="submitAction" :loading="isSubmit">提交</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { addContractinvoice  } from "@/api/contract/index";
export default {
    props:{
        visible:{
            type:Boolean,
            default:false
        },
        contractId:{
            type:String,
            default:"",
        },
    },
    data(){
        return{
            isSubmit:false,
            rules: {
                trackingNumber: [
                    { required: true, message: '请输入物流单号', trigger: 'blur' },
                ],
                teachingMaterial: [
                    { required: true, message: '请输入教材名称', trigger: 'blur' }
                ],
                },
            form:{
                contractId:this.contractId,
                trackingNumber:"",
                teachingMaterial:"",
                publish:"",
                isbn:'',
                price:"",
                number:"",
                discount:"",
                deliveryTime:''
            }
        }
    },
    computed:{
        addVisible:{
            get(){
                return this.visible
            },
            set(val){
                this.$emit('updateVisible',val)
            }
        }
    },
    methods:{
        beforeClose(){
            console.log("sssssssssssssss")
            this.addVisible = false;
            var keys = Object.keys(this.form);
            this.$refs['addform'].resetFields();
            keys.forEach(element => {
                if (element != 'contractId') {
                    this.form[element] = ''
                }
            });
        },
        submitAction(){
            console.log("tijiao==",this.form);
            this.$refs['addform'].validate((valid) => {
                if (!valid) {
                    return false;
                } 
                if (this.form.id) {
                    
                }else{
                    this.addData()
                }
            });
            
        },
        addData(){
            this.isSubmit = true;
            addContractinvoice(this.form).then((result) => {
                if (result.data) {
                    this.$message({
                        type:'success',
                        message:"添加成功"
                    })
                    this.beforeClose()
                    this.$emit('submitSuccess')
                }else{
                    this.$message({
                        type:'error',
                        message:result.msg
                    })
                }
                this.isSubmit = false;
            }).catch((err) => {
                this.isSubmit = false;
            });
        },
    }
}
</script>

<style scoped>
.w100{
    width: 100% !important;
}
.center{
    text-align: center;
}
.submitbtn{
    margin-top: 30px;
}
.revisitcss /deep/.el-form-item{
    margin-bottom: 18px;
}

</style>