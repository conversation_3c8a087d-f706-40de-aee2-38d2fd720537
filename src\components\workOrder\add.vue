<template>
    <div>
        <div>
            <back>{{form.id ? '编辑工单' : '新建工单'}}</back>
        </div>
        <div class="mainbg">
            <el-form ref="addform" class="addfcss" :rules="rules" :model="form" label-width="118px">
                <textBorder>基础信息</textBorder>
                <div class="pt20  bbline">

                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="16">
                            <el-form-item label="工单内容：" prop="content">
                                <el-input maxlength="300" show-word-limit class="definput" type="textarea" rows="5"
                                    placeholder="请输入工单内容" v-model="form.content"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item label="优先级：" prop="priority">
                                <el-select class="definput" placeholder="请选择优先级" v-model="form.priority">
                                    <el-option :key="item.value" v-for="item in options2" :value="item.value"
                                        :label="item.name"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <textBorder class="mt30">负责与协作</textBorder>
                <div class="pt20  bbline">
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8" v-if="isShowChargePerson">
                            <el-form-item label="负责人：" prop="chargePerson">
                                <span v-if="form.chargePerson" class="mr10">{{form.chargePersonName}}</span>
                                <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择负责人',1)">
                                    点击选择负责人</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="受理人：" prop="acceptedPerson">
                                <span v-if="form.acceptedPerson" class="mr10">{{form.acceptedPersonName}}</span>
                                <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择受理人',1)">
                                    点击选择受理人</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="0">
                        <el-col :span="16">
                            <el-form-item label="协作人：" >
                            <el-tag
                            class="tagcss"
                            size="small"
                            v-for="item in xiezuolist"
                            closable
                            @close="handleTagClose(item)"
                            :key="item.id"
                            >{{ item.name }}</el-tag>
                                <el-button class="bBtn" icon="el-icon-plus" type="text" @click="clickXuan('选择协作人',30)">
                                    点击选择协作人</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </div>
                <textBorder class="mt30">关联</textBorder>
                <div class="pt20  bbline">
                    <el-row :gutter="0" type="flex" justify="start">
                        <el-col :span="8">
                            <el-form-item label="关联合同：">
                                <div class="unitbtn sw" :class="{selectedCss:form.contractName}"
                                    @click="chooseContract">{{form.contractName ? form.contractName :'请选择'}}
                                    <i @click.stop="closeCon" v-show="form.contractName"
                                        class="rcenter el-icon-close"></i>
                                    <i v-show="!form.contractName" class=" rcenter el-icon-arrow-down" />
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="关联项目：">
                                <div class="unitbtn sw" :class="{selectedCss:form.projectName}" @click="chooseProject">
                                    {{form.projectName!='' ? form.projectName :'请选择'}} <i @click.stop="closePro"
                                        v-show="form.projectName" class="rcenter el-icon-close"></i> <i
                                        v-show="!form.projectName" class=" rcenter el-icon-arrow-down" /></div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>

                <div class="pt20 btncenter">
                    <el-form-item>
                        <el-button class="btn_h42 wid98" type="primary" @click="saveAction" :loading="isSubmit">保存
                        </el-button>
                    </el-form-item>
                </div>
            </el-form>
        </div>
        <!-- 负责人/协作人 -->
        <systemDialog  ref="systemdialog" :name="dialogName" :multipleNum="multipleNum" :visible.sync="dialogVisible"
            @updateVisible="updateSystemVisible" @submitData="submitData">
        </systemDialog>
        <!-- 合同选择 -->

        <contractDialog ref="contractRef" :visible.sync="contractVisible" @updateVisible="updateContractVisible"
            @updateData="updateContractData"></contractDialog>

        <!-- 项目选择 -->
        <projectDialog ref="projectRef" :visible.sync="projectVisible" @updateVisible="updateProjectVisible"
            @updateData="updateProjectData"></projectDialog>

        <!-- 部门验证组件 -->
        <verifyDeparment
          ref="verifyDeparment"
          @submit="addData"
        ></verifyDeparment>
    </div>
</template>

<script>
    import back from '../common/back.vue';
    import textBorder from '../common/textBorder.vue';
    import systemDialog from '../common/systemDialog.vue';
    import projectDialog from '../common/projectDialog.vue';
    import contractDialog from '../common/contractDialog.vue';
    import { addWorkOrder, workorderInfo, updateWorkorder } from "@/api/workorder";
import VerifyDeparment from '../common/verifyDeparment.vue';

    export default {
        components: {
            back,
            textBorder,
            systemDialog,
            projectDialog,
            contractDialog,
            VerifyDeparment
        },
        data() {
            return {
                idAndName:'',
                dialogRecordVisible:'',
                isSubmit: false,
                isShowChargePerson: this.$route.query.id ? true : false,
                multipleNum: 1,
                xiezuolist: [],
                acceptedPersonList:[],
                projectVisible: false,
                contractVisible: false,
                dialogVisible: false,
                isSmall: window.screen.availWidth < 1500 ? true : false,
                dialogName: '',
                inputValue: '',
                isDeleteTag: false,
                rules: {
                    content: [
                        { required: true, message: '请输入工单内容', trigger: 'blur' },
                        { min: 3, max: 400, message: '长度在 3 到 400 个字符', trigger: 'blur' }
                    ],
                    acceptedPerson: [
                        { required: true, message: '请选择受理人', trigger: 'change' }
                    ],
                    chargePerson: [
                        { required: true, message: '请选择负责人', trigger: 'change' }
                    ],
                    priority: [
                        { required: true, message: '请选择优先级', trigger: 'change' }
                    ],
                },
                form: {
                    content: "",
                    status: '',
                    priority: "",
                    chargePerson: "",
                    chargePersonName: "",
                    acceptedPerson: "",
                    acceptedPersonName: '',
                    collaborator:'',
                    collaboratorName:'',
                    collaboratorDepartmentId:"",
                    contractId: "",
                    projectId: "",
                    chargePersonDepartmentId: "",
                    acceptedPersonDepartmentId: "",
                    projectName: '',
                    contractName: '',
                },
                options: [{
                    value: 1,
                    name: '未分配',
                }, {
                    value: 2,
                    name: '待接单',
                }, {
                    value: 3,
                    name: '处理中',
                }, {
                    value: 4,
                    name: '已完成',
                }],
                options2: [
                {
                    value: 1,
                    name: '低',
                }, {
                    value: 2,
                    name: '一般',
                }, {
                    value: 3,
                    name: '紧急',
                }, {
                    value: 4,
                    name: '非常紧急',
                }],
                unitDialogVisible: false,
            }
        },
        created() {
            if (this.$route.query.id) {
                this.loadInfo();
            }
        },
        methods: {
            loadInfo() {
                workorderInfo(this.$route.query.id).then((result) => {
                    this.form = result.data;
                    this.form.projectId = this.form.projectId == 0 ? '' : this.form.projectId;
                    this.form.contractId = this.form.contractId == 0 ? '' : this.form.contractId;
                    var ids = result.data.collaborator && result.data.collaborator.split(',')
                    var names = result.data.collaboratorName && result.data.collaboratorName.split(',')
                    var departmentIds = result.data.collaboratorDepartmentId && result.data.collaboratorDepartmentId.split(',')
                    ids &&
                    ids.forEach((item, index) => {
                        this.xiezuolist.push({ id: item, name: names[index],departmentId:departmentIds[index] })
                    })
                }).catch((err) => {

                });
            },
            closeCon() {
                this.form.contractName = ''
                this.form.contractId = ''
            },
            // 保存
            saveAction() {
                this.$refs['addform'].validate((valid) => {
                    if (!valid) {
                        return false;
                    }
                    if (this.form.id) {
                        this.uploadData();
                    } else {
                        this.$refs.verifyDeparment.verify()
                    }
                });
            },
            uploadData() {
                this.isSubmit = true;
                updateWorkorder(this.form).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '更新成功'
                        })
                        this.$router.back();
                    } else {
                        this.$message({
                            type: "error",
                            message: result.msg
                        })
                    }
                    this.isSubmit = false;
                }).catch((err) => {
                    this.isSubmit = false;
                });
            },
            addData(departmentId) {
                this.isSubmit = true;
                this.form.chargePersonDepartmentId = departmentId;
                this.form.chargePerson = sessionStorage.getItem('userid')
                addWorkOrder(this.form).then((result) => {
                    if (result.data) {
                        this.$message({
                            type: "success",
                            message: '添加成功'
                        })
                        this.$router.back();
                    } else {
                        this.$message({
                            type: "error",
                            message: result.msg
                        })
                    }
                    this.isSubmit = false;
                }).catch((err) => {
                    this.isSubmit = false;
                });
            },
            // 项目
            chooseProject() {
                let params = {
                    methodName: 'list',
                    className: 'ProjectController',
                    contractId: '',
                    id: this.form.projectId || '',
                }
                this.$refs.projectRef.selectProjectData(params)
                this.projectVisible = true;
            },
            updateProjectVisible(val) {
                this.projectVisible = val;
            },
            updateProjectData(data) {
                this.form.projectId = data.id;
                this.form.projectName = data.projectName;
            },
            // 合同
            chooseContract() {
                if (this.$route.query.contractId) {
                    return
                }
                let params = {
                    id: this.form.contractId || '',
                    methodName: 'list',
                    className: 'VisitController',

                }
                this.$refs.contractRef.selectContractData(params)
                this.contractVisible = true;
            },
            updateContractData(data) {
                this.form.contractName = data.contractTitle;
                this.form.contractId = data.id;
            },
            updateContractVisible(val) {
                this.contractVisible = val;
            },
            closePro() {
                this.form.projectId = '';
                this.form.projectName = '';
            },
            handleTagClose(tag) {
                this.xiezuolist.splice(this.xiezuolist.indexOf(tag), 1);
                this.xiezuoData(this.xiezuolist)
            },
            xiezuoData(list) {
                var ids = [];
                var names = [];
                var departmentIds = [];
                list.forEach(item => {
                    ids.push(item.id);
                    names.push(item.name);
                    departmentIds.push(item.departmentId)
                });
                this.form.collaborator = ids.join(',');
                this.form.collaboratorName = names.join(',');
                this.form.collaboratorDepartmentId = departmentIds.join(',')
            },
            // 选择协作人
            clickXuan(name, multipleNum) {
                this.dialogName = name;
                this.multipleNum = multipleNum;
                this.$refs.systemdialog.loadData();
                if (name == '选择负责人') {
                    this.form.chargePerson ? this.$refs.systemdialog.updateWorksId([{ id: this.form.chargePerson, name: this.form.chargePersonName,departmentId:this.form.customerDepartmentId }]) : this.$refs.systemdialog.updateWorksId([]);
                } else if (name == '选择受理人') {
                    this.form.acceptedPerson ? this.$refs.systemdialog.updateWorksId(this.acceptedPersonList) : this.$refs.systemdialog.updateWorksId([]);
                } else if (name == '选择协作人') {
                    this.filtrationList = this.form.chargePerson ? [this.form.chargePerson] :[]
                    this.$refs.systemdialog.updateWorksId(this.xiezuolist)
                }
                this.dialogVisible = true;
            },
            updateSystemVisible(value) {
                this.dialogVisible = value;
            },
            //  负责人/协作人
            submitData(data, type, departmentId) {
                if (type == '选择负责人') {
                    this.form.chargePerson = data.length > 0 ? data[0].id : '';
                    this.form.chargePersonName = data.length > 0 ? data[0].name : '';
                    this.form.customerDepartmentId = departmentId;
                    this.$refs.addform.validateField('chargePerson')
                } else if (type == '选择受理人') {
                    var ids = [];
                    var names = [];
                    data.forEach(item => {
                        ids.push(item.id);
                        names.push(item.name);
                    });
                    this.form.acceptedPerson = ids.join(',');
                    this.form.acceptedPersonName = names.join(',');
                    this.form.acceptedPersonDepartmentId = departmentId;
                    this.$refs.addform.validateField('acceptedPerson')
                    this.acceptedPersonList = data;
                }else if (type === '选择协作人') {
                    this.xiezuoData(data);
                    this.xiezuolist = data;
                }
                this.updateSystemVisible(false)
            },
        }
    }
</script>
<style scoped>
    .unitbtn {
        position: relative;
    }

    .sw {
        width: 224px !important;
    }

    .studiocss img {
        width: 16px;
        height: 16px;
        margin-right: 3px;
        margin-top: -3px;
    }

    .width100 {
        width: 100%;
    }

    .wid98 {
        width: 98px;
    }

    .uploadtext {
        color: #4285F4;
        cursor: pointer;
    }

    .uploadcss {
        width: 88px;
        height: 88px;
        line-height: 24px;
        background: #F5F5F5;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px dashed #CCCCCC;
    }

    .uploadcss i {
        margin-top: 20px;
        font-size: 24px;
        color: #4285F4;
    }

    .rcenter {
        position: absolute;
        right: 10px;
        line-height: 34px;
        font-size: 14px;
        color: #c0c4cc;
        z-index: 10;
        top: 0;
    }

    .btncenter {
        text-align: center;
    }

    .mainbg {
        margin: 16px 0px;
        padding: 20px 16px;
        background-color: white;
        border-radius: 8px;
    }

    .pd0 {
        padding-right: 0px !important;
    }
    .tagcss /deep/ .el-icon-close {
        width: 12px;
        height: 12px;
        line-height: 12px;
        background-color: #4285f4;
        color: white;
    }

    .tagcss {
        margin-left: 8px;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #4285f4;
        background: #dfeafd;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
    }
</style>
<style>

</style>
