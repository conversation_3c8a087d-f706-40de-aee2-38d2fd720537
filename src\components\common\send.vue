<template>
  <div class="savediv">
                  <el-input show-word-limit maxlength="400" ref="input"  class="inputcommont" v-model="value" placeholder="说点什么，鼓励一下你的队友吧~"></el-input>
                  <el-button @click="send" class="savebutton" type="primary">提交</el-button>
  </div>
</template>

<script>
  export default {
        data(){
          return{
            value:''
          }
        },
        methods:{
          autoFocus(){
            this.$nextTick(() => {
              this.$refs.input.focus()
            })
          },
          send(){
              if(this.value ==''){
                  this.msgError('请输入评论内容')
                  return
              }
              this.$emit('save',this.value)
          },
          clearValue(){
            this.value =''
          }
        },
        mounted(){
        }
  }
</script>

<style lang="scss" scoped>
.savediv{
  margin-top: 20px;
}
.inputcommont{
  width: calc(100% - 88px);
  margin-right: 16px;

  /deep/input{
    height: 34px !important;
    line-height: 34px !important;
  }
}
.savebutton{
  width: 72px;
  padding: 9px 5px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #FFFFFF;
}
</style>