import service from '@/utils/request.js'

export function baodingsave(data) {
  return service.request({
    method: 'post',
    url: `/crm/controller/materialsubscription/save`,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
}

export function baodingupdate(data) {
  return service.request({
    method: 'post',
    url: `/crm/controller/materialsubscription/update`,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
}

export function baodinglist(params) {
  return service.request({
    method: 'get',
    url: `/crm/controller/materialsubscription/list`,
    params
  });
}

export function baodinginfo(id){
  return service.request({
    method: 'get',
    url: `/crm/controller/materialsubscription/info/${id}`,
  });
}

// 报批
export function subscriptionUpdate(data) {
  return service.request({
    method: 'post',
    url: `/crm/controller/materialsubscription/subscriptionUpdate`,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  });
}

export function baodingdelete(data){
  return service.request({
    method: 'post',
    url: `/crm/controller/materialsubscription/delete`,
    data
  });
}
