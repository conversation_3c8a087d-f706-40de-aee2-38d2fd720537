<template>
  <div>
                    <ul class="clearfix guancontent">
                      <li :key="index" v-for="(item,index) in pdfArr" @click="viewPdf(item)" class="liitem left">{{ item.fileName }}</li>
                    </ul>
                    <fileview ref="fileview"></fileview>
  </div>
</template>

<script>
import fileview from '../../../common/fileview.vue';
  export default {
    components:{
      fileview
    },
    props:{
        pdfArr:
        {
          type:Array,
          default:()=>[]
        },
        type:{
          type:String,
          default:''
        }
    },
      methods:{
        viewPdf(item){
          this.$refs.fileview.show(item)
        }
      }
  }
</script>
<style>
.guancontent{
  margin: 10px 0;
  margin-left: 18px;
}
.liitem{
  border-radius: 4px 4px 4px 4px;
  border: 1px dashed #4285F4;
  padding: 5px 8px ;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
  box-sizing: border-box;
  cursor: pointer;
  margin-right: 20px;
  line-height: 14px;
  box-sizing: border-box;
  margin-bottom: 10px;
}
</style>