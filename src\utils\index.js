
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    return value.toString().padStart(2, '0')
  })
  return time_str
}


export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}


export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
    decodeURIComponent(search)
      .replace(/"/g, '\\"')
      .replace(/&/g, '","')
      .replace(/=/g, '":"')
      .replace(/\+/g, ' ') +
    '"}'
  )
}

export function findName(arr, id) {
  let obj = arr.find(function (obj) {
    if (obj.id == id) {
      return obj
    }
  })
  return obj.name;
}


export function timenow() {
  var d = new Date(), str = '';
  str += d.getFullYear() + '-';
  str += d.getMonth() + 1 + '-';
  str += d.getDate();
  return str;
}

// 深拷贝对象
export function deepClone(obj) {
  const _toString = Object.prototype.toString

  // null, undefined, non-object, function
  if (!obj || typeof obj !== 'object') {
    return obj
  }

  // DOM Node
  if (obj.nodeType && 'cloneNode' in obj) {
    return obj.cloneNode(true)
  }

  // Date
  if (_toString.call(obj) === '[object Date]') {
    return new Date(obj.getTime())
  }

  // RegExp
  if (_toString.call(obj) === '[object RegExp]') {
    const flags = []
    if (obj.global) { flags.push('g') }
    if (obj.multiline) { flags.push('m') }
    if (obj.ignoreCase) { flags.push('i') }

    return new RegExp(obj.source, flags.join(''))
  }

  const result = Array.isArray(obj) ? [] : obj.constructor ? new obj.constructor() : {}

  for (const key in obj) {
    result[key] = deepClone(obj[key])
  }

  return result
}
export function getWeek(date) {
	date = new Date(date);
	var date2 = new Date(date.getFullYear(), 0, 1);
	var day1 = date.getDay();
	if (day1 == 0) day1 = 7;
	var day2 = date2.getDay();
	if (day2 == 0) day2 = 7;
	let d = Math.round((date.getTime() - date2.getTime() + (day2 - day1) * (24 * 60 * 60 * 1000)) / 86400000);
	//当周数大于52则为下一年的第一周
	if((Math.ceil(d / 7) + 1)>52){
		return (date.getFullYear()+1)+"-1"
  }
  console.log(date)
	return date.getFullYear()+"-"+(Math.ceil(d / 7) + 1);
};

export function getPlanTime(data,type) {

  if (type == '1' || type == '4') {
    return data.year;
  }
  if (type == '2' || type == '5') {
    return  `${data.year}-${data.month}月`
  }

  if (type == '3' || type == '6') {
    return  `${data.year} 第 ${data.week} 周`
  }
  
}


export  function firstToUpper1(str) {
  return str.trim().replace(str[0], str[0].toUpperCase());
}


/**
 * Base64字符串转File文件
 * @param {String} base64 Base64字符串(字符串不包含Data URI scheme)
 * @param {String} filename 文件名称
 * @param {String} type 文件类型(例如：image/png)
 */
export function base64toFile(dataurl, filename) {
	let arr = dataurl.split(',');
	let mime = arr[0].match(/:(.*?);/)[1];
	let bstr = atob(arr[1]);
	let n = bstr.length;
	let u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	return new File([u8arr], filename, {
		type: mime
	});
}

/**
 * 获取目标类型标题
 * @param {Number} goalsType 目标类型
 * @returns {String} 对应的标题
 */
export function getGoalsTypeTitle(goalsType) {
  const typeMap = {
    1: '年计划',
    2: '月计划',
    3: '周计划',
    4: '年总结',
    5: '月总结',
    6: '周总结',
    7: '日志',
    9: '个人目标'
  }
  return typeMap[goalsType]
}
