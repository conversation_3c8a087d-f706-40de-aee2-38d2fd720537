<template>
  <div class="mod-config">
    <el-row :gutter="20" class="el-rowone">
      <el-col :span="5" class="wcolor clrp letree">
        <div class="cflex cfd leall">
          <div class="clearfix tdiv ">
            <span class="zutext">组织架构</span>
            <el-button v-isShow="'crm:controller:companydepartment:synchronizeUpdateDepartments'"
              class="right defaultbtn1" type="primary" @click="updateDepartmant()">
              同步客户端架构</el-button>
          </div>
          <el-tree :default-expand-all="true" class="filter-tree  cfd cf1" :data="tagTypeData" :props="defaultProps"
            ref="tagTypeTree" :highlight-current="true" :expand-on-click-node="false" @node-click="getTreeNode">
          </el-tree>
        </div>
      </el-col>
      <el-col :span="19" class="cpding">
        <div class="tablecss wcolor" ref="configtypetable">
          <div class="page-header">
            <el-form size="small" ref="form" :model="pageBean" :inline="true">
              <el-form-item label="筛选角色：">
                <el-select clearable="" v-model="pageBean.roleId" placeholder="请选择">
                  <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="员工姓名：">
                <el-input v-model="pageBean.name" clearable placeholder="请输入员工姓名"></el-input>
              </el-form-item>
              <el-form-item label="">
                <el-button class="search btn defaultbtn" type="primary" icon="el-icon-search" @click="onSearch">搜索
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="全部" name="one"></el-tab-pane>
            </el-tabs>
            <el-table :height="tableHeight" class="mytable1" :data="dataList" v-loading="isLoading">
              <el-table-column prop="name" label="员工信息">
                <template slot-scope="scope">
                  <headuser :url="scope.row.logo" width="48" :username="scope.row.name"></headuser>
                  <span class="username">{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="departmentName" label="部门">
              </el-table-column>
              <el-table-column prop="roleName" label="角色">
              </el-table-column>
              <el-table-column prop="edit" width="120" label="操作">
                <template slot-scope="scope">
                  <el-button class="bBtn" type="text" @click="addOrUpdateTagTypeHandle(scope.row)"> 编辑角色</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <page :currentPage="pageBean.pageNum" :total="total" :pageSize="pageBean.pageSize"
            @updatePageNum="handleCurrentChange"></page>
        </div>
      </el-col>
    </el-row>
    <AddOrUpdateTagType v-if="addOrUpdateTagTypeVisible" ref="addOrUpdateTagType" @refreshTagTypeTree="userListData">
    </AddOrUpdateTagType>
  </div>
</template>

<script>
  import AddOrUpdateTagType from './components/add-or-update-tag-type'
  import page from '../../common/page.vue';
  import { getRoleList } from '@/api/framework/roleposition'
  import { treeDepartmentList, userList, synchronizeUpdateDepartments } from '@/api/framework/user'
  import headuser from '@/components/common/headuser.vue'
  import { Loading } from 'element-ui';
  export default {
    data() {
      return {
        activeName: 'one',
        isLoading: false,
        pageBean: {
          roleId: '',
          name: '',
          pageNum: 1,
          pageSize: 15,
          departmentId: ''
        },
        dataList: [],
        total: 0,
        addOrUpdateTagTypeVisible: false,
        defaultProps: {
          children: 'children',
          label: 'name',
        },
        tableHeight: window.innerHeight - 312,
        roleList: [],
        dimg: require('@/assets/touxiang.png'),
        tagTypeData: [],
      }
    },
    components: {
      AddOrUpdateTagType,
      page,
      headuser
    },
    methods: {
      updateDepartmant() {
        let loadingInstance = Loading.service({
          lock: true,
          text: '同步信息中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        synchronizeUpdateDepartments().then(res => {
          if (res.status == 0) {
            this.$nextTick(() => {
              loadingInstance.close();
            });
            this.msgSuccess('同步成功')
            this.getRoleListData()
            this.treeDepartmentListData()
            this.userListData()
          } else {
            this.$nextTick(() => {
              loadingInstance.close();
            });
          }
        }).catch(() => {
          this.$nextTick(() => {
            loadingInstance.close();
          });
        })
      },
      userListData() {
        userList(this.pageBean).then(res => {
          if (res.status == 0) {
            this.dataList = res.data
            this.total = res.page.total
          }
        })
      },
      treeDepartmentListData() {
        treeDepartmentList().then(res => {
          if (res.status == 0) {
            this.tagTypeData = res.data
          }
        })
      },
      getRoleListData() {
        getRoleList().then(res => {
          if (res.status === 0) {
            this.roleList = res.data
          }
        })
      },
      handleClick(tab, event) {
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.userListData()
      },
      onEdit(item) {
        this.addOrUpdateTagItemVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdateTagItem.init(
          )
        })
      },
      onSearch() {
        this.pageBean.pageNum = 1;
        this.userListData()
      },
      getTreeNode(data) {
        this.pageBean.departmentId = data.id
        this.userListData()
      },
      addOrUpdateTagTypeHandle(row) {
        this.addOrUpdateTagTypeVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdateTagType.init(row.id)
        })
      },
    },

    mounted() {
    },
    created() {
      this.getRoleListData()
      this.treeDepartmentListData()
      this.userListData()
    },
  }
</script>

<style scoped lang="scss">
  .defaultbtn1 {
    padding: 9px 10px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
  }

  .username {
    clear: both;
    position: relative;
    top: 10px;
  }


  .avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .leall {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
  }

  .letree {
    display: flex;
  }

  .mytable1 {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    margin-bottom: 0px !important;
  }

  .mod-config {
    height: 100%;
  }

  .search {
    margin-left: 10px;
  }

  .tablecss {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  .clrp {
    padding-left: 0 !important;
    padding-right: 0 !important;
    min-width: 240px;
  }

  .cpding {
    padding-right: 0 !important;
    padding-left: 16px !important;
    display: flex;
    flex-direction: column;
  }

  .el-rowone {
    margin-left: 0 !important;
    margin-right: 0 !important;
    display: flex;
    height: 100%;
  }

  .wcolor {
    background: #fff;
  }

  .tdiv {
    line-height: 40px;
    height: 56px;
    box-sizing: border-box;
    border-bottom: 1px solid #F0F0F0;
  }

  .zutext {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #999999;
  }

  .pcc {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    text-align: center;
    line-height: 24px;
  }

  .ml16 {
    margin-left: 16px;
  }

  .mr16 {
    margin-right: 16px;
  }

  .filter-tree {
    font-size: 14px;
    overflow-y: auto;
    overflow-x: auto;
    width: 100%;
    /deep/.el-tree-node__children {
      overflow: visible;
    }
  }

  .filter-tree,
  .grid-contenttable {
    overflow-y: scroll;
  }
</style>