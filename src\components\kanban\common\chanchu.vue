<template>
    <div>
        <el-card class="niandu1">
            <div class="title">
                <textBorder class="biaoti">产出投入</textBorder>
            </div>
            <el-row :gutter="12">
                <el-col :span="25" v-for="(item, index) in chartData" :key="index">
                    <div class="kuai">
                        <div class="toptitle">
                            <textBorder class="wenzi">{{item.title}}</textBorder>
                        </div>
                        <div v-if="!item.chartId" class="shuzi">
                            <div class="number1"><span class="number-style5">{{ item.number }}</span>{{item.title != '产出投入比'?"万元":""}}</div>
                            <div class="lastyearss">
                                <span class="lastyear">较上一年：</span><p class="number-style3">{{ item.lastYear }}</p><span v-if="item.title != '产出投入比'" class="lastyear">万元</span>
                            </div>
                            <div class="zeng" v-if="item.number || item.lastYear" :class="typeClass[item.changeType]">
                                <div class="zengzhang">{{item.changeType}}：</div>
                                <div class="baifenbi">{{ item.growth }}</div>
                                <img :src="item.icon" alt="" class="jiantou">
                            </div>
                        </div>

                        <el-col>
                            <div v-if="item.chartId" class="tubiao">
                                <div :id="item.chartId" class="biao"></div>
                            </div>
                        </el-col>

                    </div>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
import '../detail.css';
import * as echarts from 'echarts';
import textBorder from '@/components/common/textBorder.vue';
export default {
    components:
    {
        textBorder
    },
    data() {
        return {
            chartData: [],
            yearOutputData:{},
            materialData:'',
            infomationData:'',
            upicon: require('../../../assets/Group4189.png'),
            downicon:require('../../../assets/Group4190.png'),
            chipingicon:require('../../../assets/chiping.png'),
            typeClass:{
                "增长":'upcss',
                "下降":'downcss',
                '持平':'chipingcss'
            },
        }
    },
    mounted() {
        
    },
    beforeDestroy() {
        if (this.$_resizeEventHandler) {
            window.removeEventListener('resize', this.$_resizeEventHandler);
        }
    },
    methods: {
        loadData(data){
            this.chartData = this.getChartData(data)
            console.log('323333',this.chartData,this.yearOutputData)
            this.$nextTick(() => {
                this.chartData.forEach((item) => {
                    if (item.chartId && item.options) {
                        var chart = echarts.init(document.getElementById(item.chartId));
                        chart.setOption(item.options);
                        this.$_resizeEventHandler = () => chart.resize();
                        window.addEventListener('resize', this.$_resizeEventHandler);
                    }
                });
            });
        },
        getImg(type){
            if (type === '增长') {
                return this.upicon
            }else if (type === '下降') {
                return this.downicon
            }else{
                return this.chipingicon
            }
        },
        getYearData(data){
            this.yearOutputData = {
                title: '年度产出',
                number: data.thisYearPerformance,
                lastYear: data.lastYearPerformance,
                changeType:data.performanceFluctuations,
                growth: data.performanceRange || '0',
                icon: this.getImg(data.performanceFluctuations)
            }

            this.chartData[0] = this.yearOutputData;
            this.materialData = data.thisYearMaterialPerformance;
            this.infomationData = data.thisYearInformationPerformance;
            
        },
        getChartData(data){
            return [
                this.yearOutputData,
                {
                    title: '年度投入',
                    number: data.thisYearInvestment,
                    lastYear: data.lastYearInvestment,
                    changeType:data.investmentFluctuations,
                    growth: data.investmentRange || '0',
                    icon: this.getImg(data.investmentFluctuations)
                },
                {
                    title: '产出投入比',
                    number: data.outputInvestmentProportion,
                    lastYear: data.lastYearInvestmentProportion,
                    changeType: data.outputFluctuations,
                    growth: data.outputRange || '0',
                    icon: this.getImg(data.outputFluctuations)
                },
                {
                    title: '成本投入分布',
                    chartId: 'pie-chart-1',
                    options: {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{b} : {c} ({d}%)'
                        },
                        legend: {
                            top: '0', 
                            right: '0', 
                            icon: 'circle', 
                            itemWidth: 10, 
                            itemHeight: 10, 
                        },
                    series: [
                        // 画外部描述的饼图
                        {
                        type: "pie",
                        radius: "60%",
                        center: ["50%", "50%"],
                        data: [

                            {
                            name: "商务",
                            value: data.businessCostAmount,
                            itemStyle: {
                                color: '#56c36e'
                            }
                            },
                            {
                            name: "样书",
                            value: data.sampleBookAmount,
                            itemStyle: {
                                color: '#ff9024'
                            }
                            },
                            {
                            name: "礼品",
                            value: data.giftOutputAmount,
                            itemStyle: {
                                color: '#4285f4'
                            }
                            },
                        ],
                        label: {
                            show: true,
                            position: "outside",
                            color: "#7F8FA4",
                            fontSize: 12,
                        },
                        labelLine: {
                            show: true,
                            length: 9, // 可以调整标签线的长度
                            length2: 9 // 标签线的二段长度
                        },
                        },
                        // // 画内部百分比的饼图
                        // {
                        // type: "pie",
                        // data: [
                        // {
                        //     name: "商务",
                        //     value: 43766,
                        //     itemStyle: {
                        //         color: '#56c36e'
                        //     }
                        //     },
                        //     {
                        //     name: "样书",
                        //     value: 56173,
                        //     itemStyle: {
                        //         color: '#ff9024'
                        //     }
                        //     },
                        //     {
                        //     name: "礼品",
                        //     value: 46173,
                        //     itemStyle: {
                        //         color: '#4285f4'
                        //     }
                        //     },
                        // ],
                        // label: {
                        //     show: true,
                        //     position: "inside",
                        //     formatter: `{d}%`,
                        // },
                        // },
                    ],

                    }
                },
                {
                    title: '业绩产出分布',
                    chartId: 'pie-chart-2',
                    options: {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{b} : {c} ({d}%)'
                        },
                        legend: {
                            top: '0', 
                            right: '0', 
                            icon: 'circle', 
                            itemWidth: 10, 
                            itemHeight: 10, 
                        },
                    series: [
                        // 画外部描述的饼图
                        {
                        type: "pie",
                        radius: "60%",
                        center: ["50%", "50%"],
                        data: [

                            {
                            name: "信息化",
                            value: this.infomationData,
                            itemStyle: {
                                color: '#ff9931'
                            }
                            },
                            {
                            name: "教材",
                            value: this.materialData,
                            itemStyle: {
                                color: '#eb6067'
                            }
                            },
                        ],
                        label: {
                            show: true,
                            position: "outside",
                            color: "#7F8FA4",
                            fontSize: 12,
                        },
                        labelLine: {
                            show: true,
                            length: 9, // 可以调整标签线的长度
                            length2: 9 // 标签线的二段长度
                        },
                        },
                    ],

                    }
                },
            ]
        },
    }
}
</script>
<style lang="scss" scoped>
.upcss{
    color: #F46D40 !important;
}
.downcss{
    color: #56C36E !important;
}
.chipingcss{
    color: #4285F4 !important;
}
::v-deep .el-col-25 {
    width: 20%;
}

.shuzi {
    margin-top: 16px;
    text-align: center;
}

.biao {
    width: calc(100% + 32px);
    margin-left: -16px;
    margin-right: -16px;
    height: 180px;  
}
.number-style5 {
    font-family: Roboto, Roboto;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
}
</style>