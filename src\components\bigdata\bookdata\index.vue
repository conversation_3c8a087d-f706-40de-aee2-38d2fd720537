<template>
  <div class="mainbg fixpb">
    <el-card v-loading="isLoading" class="mt10">
      <div class="datalogo">
        <img src="../../../assets/wutonglogo.png" alt="" />
        <span class="logoti">梧桐智企教材大数据</span>
      </div>
      <el-form label-width="85px" class="myform">
        <el-row :gutter="10" type="flex" justify="start" class="flexs">
          <el-col :span="4">
            <el-form-item label="教材名称：">
              <el-input
                class="definput"
                v-model="pageBean.materialName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="ISBN:">
              <el-input
                class="definput"
                v-model="pageBean.isbn"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="出版社：">
              <el-input
                class="definput"
                v-model="pageBean.platformName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="单位名称：">
              <el-input
                class="definput"
                v-model="pageBean.unitName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="用书季节：">
              <el-select
                v-model="pageBean.useSeason"
                placeholder="请选择"
                clearable
              >
                <el-option label="春季" value="春季"></el-option>
                <el-option label="秋季" value="秋季"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="4">
            <el-form-item label="用书时间：">
              <el-date-picker
                :editable="false"
                :clearable="false"
                class="yearcss definput"
                value-format="yyyy"
                v-model="pageBean.useBookYear"
                type="year"
                placeholder="选择年"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" type="flex" justify="start" class="flexs">
          <el-col :span="4">
            <el-form-item label="是否合作：">
              <el-select
                v-model="pageBean.isCooperate"
                placeholder="请选择"
                clearable
              >
                <el-option label="合作" :value="1"></el-option>
                <el-option label="非合作" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="用书专业：">
              <el-input
                class="definput"
                v-model="pageBean.bookSpecialtyName"
                clearable
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="业务经理：">
              <el-input
                class="definput"
                v-model="pageBean.operationName"
                clearable
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-form-item label="业务经理部门：" label-width="120px">
            <div class="unitbtn mt definput w1" @click="chooseDepartment">
              <span
                v-if="pageBean.operationDepartmentId.length > 0"
                class="deffont"
                >{{ searchdepartmentNames || '请选择' }}</span
              >
              <span v-else>请选择</span>
              <i
                v-if="searchdepartmentNames"
                @click.stop="cleardata"
                class="rcenter el-icon-circle-close"
              />
              <i v-else class="rcenter el-icon-arrow-down" />
            </div>
          </el-form-item>
          <el-form-item label="用书量：" class="numcss">
            <div class="numdiv">
              <el-input
                @input="handleEdit"
                class="wip"
                v-model="pageBean.searchMin"
                type="number"
              >
                <span slot="suffix">册</span>
              </el-input>
              <div class="form-center">--</div>
              <el-input
                @input="handleEdit1"
                class="wip"
                v-model="pageBean.searchMax"
                type="number"
              >
                <span slot="suffix">册</span>
              </el-input>
            </div>
          </el-form-item>
          <el-button
            class="defaultbtn1 mt"
            icon="el-icon-search"
            type="primary"
            @click="searchAction"
            >搜索</el-button
          >
          <el-button
            class="defaultbtn1 mt"
            type="primary"
            @click="exportFilteredData"
            :loading="isExporting"
            >导出筛选数据</el-button
          >
          <el-button
            class="defaultbtn1 mt"
            type="primary"
            @click="exportFilteredDataDetail"
            :loading="isExportingD"
            >导出详细数据</el-button
          >
        </el-row>
      </el-form>
      <el-table
        :cell-style="cell"
        @sort-change="sortChange"
        class="customertable mytable tootiptable"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="unitName" label="单位名称" align="center">
          <template slot-scope="scope">
            <div @click="openDetails(scope.row)">{{ scope.row.unitName }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="operationNames" label="业务经理" align="center">
        </el-table-column>
        <el-table-column
          prop="operationPersonDepartments"
          label="业务经理部门"
          align="center"
        >
        </el-table-column> -->
        <el-table-column
          :show-overflow-tooltip="true"
          prop="specialtyNames"
          label="用书专业"
          align="center"
        >
        </el-table-column>

        <el-table-column prop="useBookYear" label="用书时间" align="center">
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="yongliangNum"
          label="用书量"
          align="center"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="joinNum"
          label="合作"
          align="center"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="notJoinNum"
          label="非合作"
          align="center"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="planRecruitNumber"
          label="计划招生"
          align="center"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="realityRecruitNumber"
          align="center"
          label="实际招生"
        >
        </el-table-column>
        <template slot="empty">
          <nolist></nolist>
        </template>
      </el-table>

      <div class="fixpage">
        <page
          :currentPage="pageBean.pageNum"
          :total="total"
          :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"
        ></page>
      </div>
    </el-card>
    <!-- 选择部门 -->
    <departmentDialog
      ref="deRef"
      dType="1"
      :visible.sync="dialogDepartmentVisible"
      @updateVisible="updateSystemVisible"
      @submitData="submitData"
    >
    </departmentDialog>
  </div>
</template>

<script>
import page from '@/components/common/page.vue'
import departmentDialog from '@/components/common/departmentDialog.vue'
import nolist from '@/components/common/nolist.vue'
import {
  queryData,
  downloadFilteredData,
  downloadDetailedData,
} from '@/api/bigdata/index'
import { mapGetters } from 'vuex'
import { getParStr, downloadExcelFile } from '@/utils/tools'

export default {
  components: {
    page,
    nolist,
    departmentDialog,
  },
  data() {
    return {
      isExportingD: false,
      dialogDepartmentVisible: false,
      searchdepartmentNames: '',
      selDepartmentData: [],
      isExporting: false,
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        materialName: '',
        platformName: '',
        isCooperate: '',
        isbn: '',
        pageNum: 1,
        pageSize: 10,
        useBookYear: '',
        searchMin: '',
        searchMax: '',
        bookSpecialtyName: '',
        rankType: '',
        searchType: '1',
        useSeason: '',
        operationName: '',
        operationDepartmentId: [],
      },
      updateRankType: {
        'ascending:yongliangNum': 1,
        'descending:yongliangNum': 2,
        'ascending:joinNum': 3,
        'descending:joinNum': 4,
        'ascending:notJoinNum': 5,
        'descending:notJoinNum': 6,
        'ascending:planRecruitNumber': 7,
        'descending:planRecruitNumber': 8,
        'ascending:realityRecruitNumber': 9,
        'descending:realityRecruitNumber': 10,
      },
    }
  },
  // 点击后拿到数据返回给下单地址
  beforeRouteLeave(to, from, next) {
    if (to.path === '/bigdata/bookdata/datadetail') {
      if (this.pageBean.materialName) {
        this.$store.commit('bookusage/CHANGEBOOKNUM', {
          key: 'materialName',
          value: this.pageBean.materialName,
        })
      } else {
        this.$store.commit('bookusage/CHANGEBOOKNUM', {
          key: 'materialName',
          value: '',
        })
      }
    } else {
      this.$store.commit('bookusage/CHANGEBOOKNUM', {
        key: 'materialName',
        value: '',
      })
    }
    next()
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
      this.pageBean.operationDepartmentId = this.pageBean.operationDepartmentId
        ? this.$route.query.operationDepartmentId.split(',')
        : []
      this.searchdepartmentNames = this.pageBean.operationDepartmentNames || ''
    }
    if (this.materialName) {
      this.pageBean.materialName = this.materialName
    }
    this.pageBean.useBookYear = this.getDate()
    this.loadData()
  },
  computed: {
    ...mapGetters(['materialName']),
  },
  methods: {
    exportFilteredData() {
      this.isExporting = true
      let params = this.copy(this.pageBean)
      if (params.useSeason) {
        params.useBookYear = params.useBookYear + '年' + params.useSeason
      }
      downloadFilteredData(params)
        .then((result) => {
          downloadExcelFile(result, `教材用量数据`)
          this.isExporting = false
        })
        .catch((err) => {
          this.isExporting = false
          this.$message.error('导出文件时出错')
        })
    },

    exportFilteredDataDetail() {
      this.isExportingD = true
      let params = this.copy(this.pageBean)
      if (params.useSeason) {
        params.useBookYear = params.useBookYear + '年' + params.useSeason
      }
      downloadDetailedData(params)
        .then((result) => {
          downloadExcelFile(result, `教材用量详细数据`)
          this.isExportingD = false
        })
        .catch((err) => {
          this.isExportingD = false
          this.$message.error('导出文件时出错')
        })
    },
    handleEdit(e) {
      let value = e.replace(/[^\d]/g, '') // 只能输入数字
      value = value.replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
      this.pageBean.searchMin = value
    },
    handleEdit1(e) {
      let value = e.replace(/[^\d]/g, '') // 只能输入数字
      value = value.replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
      value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
      this.pageBean.searchMax = value
    },
    openDetails(row) {
      if (row.specialtys) {
        const ids = row.specialtys.split(',')
        const names = row.specialtyNames.split('/')
        let majorList = ids.map((id, index) => ({
          id,
          name: names[index],
        }))
        sessionStorage.setItem('majorList', JSON.stringify(majorList))
        sessionStorage.setItem('specialtys', row.specialtys)
      }
      this.$router.push({
        path: '/bigdata/bookdata/datadetail',
        query: {
          materialName: this.pageBean.materialName
            ? this.pageBean.materialName
            : '',
          platformName: this.pageBean.platformName
            ? this.pageBean.platformName
            : '',
          useBookYear: this.pageBean.useBookYear,
          unitId: row.unitId,
        },
      })
    },
    cell({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        return 'color: #4285F4;cursor:pointer'
      }
    },
    sortChange(column) {
      let props = column.order + ':' + column.prop
      this.pageBean.pageNum = 1
      this.pageBean.rankType = this.updateRankType[props]
      this.loadData()
    },
    getDate() {
      var now = new Date()
      var year = now.getFullYear()
      return year + ''
    },
    copy(obj1) {
      var obj2 = {}
      for (var i in obj1) {
        obj2[i] = obj1[i]
      }
      return obj2
    },
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      let params = {}

      if (this.pageBean.useSeason) {
        params = this.copy(this.pageBean)
        params.useBookYear =
          this.pageBean.useBookYear + '年' + this.pageBean.useSeason
      } else {
        params = this.copy(this.pageBean)
      }
      queryData(params)
        .then((result) => {
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    searchAction() {
      if (this.pageBean.searchMax == 0 && this.pageBean.searchMax != '') {
        this.msgError('最大值必须大于0')
        return
      }
      if (
        (+this.pageBean.searchMin >= 0 && this.pageBean.searchMin != '') ||
        (+this.pageBean.searchMax >= 0 && this.pageBean.searchMax != '')
      ) {
        if (
          +this.pageBean.searchMin >= 0 &&
          this.pageBean.searchMin != '' &&
          this.pageBean.searchMax == ''
        ) {
          this.msgError('请输入数量的最大值')
          return
        }
        if (
          +this.pageBean.searchMin >= 0 &&
          this.pageBean.searchMin != '' &&
          this.pageBean.searchMax != '' &&
          +this.pageBean.searchMax <= +this.pageBean.searchMin
        ) {
          this.msgError('最大值得大于最小值')
          return
        }
        if (
          this.pageBean.searchMin == '' &&
          this.pageBean.searchMax != '' &&
          +this.pageBean.searchMax >= 0
        ) {
          this.msgError('请输入数量的最小值')
          return
        }
      }

      this.pageBean.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    cleardata() {
      this.searchdepartmentNames = ''
      this.pageBean.operationDepartmentId = []
      this.selDepartmentData = []
    },
    chooseDepartment(e) {
      this.dialogDepartmentVisible = true
      this.$refs.deRef.loadData()
      this.$refs.deRef.updateWorksIdTree(this.selDepartmentData)
    },
    updateSystemVisible(val) {
      this.dialogDepartmentVisible = val
    },
    submitData(data) {
      this.dialogDepartmentVisible = false
      this.selDepartmentData = data
      let departmentIds = data.map((item) => item.id)
      let departmentNames = data.map((item) => item.name)
      this.pageBean.operationDepartmentId = departmentIds
      this.pageBean.operationDepartmentNames = departmentNames.join(',')
      // this.pageBean.operationDepartmentId = departmentIds.join(',')
      this.searchdepartmentNames = departmentNames.join(',')
    },
  },
}
</script>
<style scoped lang="scss">
.bumen {
  width: 160px;
}
.flexs {
  flex-wrap: wrap;
}
.numcss {
  width: 274px;
  flex-shrink: 0;
}
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: calc(100vh - 106px);
}

.yearcss /deep/.el-input__inner {
  height: 34px;
}

.wip {
  min-width: 60px;
  width: 40%;
  height: 34px;
}

.wip /deep/.el-input__inner {
  height: 28px;
  position: relative;
  top: -4px;
  line-height: 1px !important;
}

.wip /deep/.el-input__suffix {
  height: 28px;
  line-height: 34px;
}

.numdiv {
  height: 34px;
  border: 1px solid #dcdfe6;
  padding: 0 10px;
  margin-top: 3px;
}

.form-center {
  display: inline-block;
  height: 34px;
  width: 20%;
  text-align: center;
  line-height: 34px;
  vertical-align: top;
}

.el-date-editor.el-input {
  width: 100%;
}

.myform {
  padding: 20px 0;
}

.logoti {
  height: 37px;
  font-size: 28px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  display: inline-block;
  vertical-align: middle;
  margin-left: 12px;
}

.datalogo {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.defaultbtn1 {
  padding: 9px 10px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 36px;
  padding: 0 12px;
}

.mt {
  margin-top: 4px;
}

.customertable .el-button {
  padding: 2px;
}
.w1 {
  width: 200px;
}
.rcenter {
  position: absolute;
  top: 5px;
  right: 10px;
  line-height: 34px;
  font-size: 14px;
  color: #c0c4cc;
}
</style>