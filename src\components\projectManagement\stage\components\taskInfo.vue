<template>
  <div>
    <el-form class="infoform" ref="form" :model="form" label-width="130px">
          <textBorder>任务信息</textBorder>
          <div class="bp">
            <el-button :disabled="!form.isOperate" v-if="!(form.taskStatus ==3 || form.taskStatus == 4)" class="defaultbtn" icon="el-icon-edit" type="primary" @click="goToEdit">编辑</el-button>
            <el-button :disabled="!form.isOperate" @click="deleteHandle" class="defaultbtn" icon="el-icon-delete" type="danger">删除</el-button>
          </div>
          <el-row  :gutter="0" class="width100 mt10 pb12 mb30 bbline  " >
            <el-col :span="12">
              <el-form-item label="任务名称：" class="labeltext markitem textwarp">
                 <span>{{ form.taskName }}</span>
              </el-form-item>
              <el-form-item label="开始时间：" class="labeltext">
                <span>{{ form.beginTime }}</span>
              </el-form-item>
              <el-form-item label="当前状态：" class="labeltext">
                <span>{{ form.taskStatusName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">  
              <el-form-item label="预估工时(人/天)：" class="labeltext">
                 <span>{{ form.workHours==0?'': form.workHours}}</span>
              </el-form-item>
              <el-form-item label="结束时间：" class="labeltext">
                 <span class="column_blue">{{ form.endTime }}</span>
              </el-form-item>
              <el-form-item label="记录工时：" class="labeltext">
                <span>{{ form.totalWorkHours }}</span>
              </el-form-item>
            </el-col>
            <!-- <div class="clear"></div> -->
            <div class="clear">
              <el-form-item label="任务内容：" class="labeltext markitem textwarp">
                <span>{{ form.taskContent }}</span>
              </el-form-item>
            </div>
          </el-row>
          <textBorder>负责与协作</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                <el-form-item label="负责人：" class="labeltext">
                    <span>{{ form.chargePersonName }}</span>
                </el-form-item>
                </el-col>
                <el-col :span="12">  
                <el-form-item label="协作人：" class="labeltext">
                    <span>{{ form.collaboratorName }}</span>
                </el-form-item>
                </el-col>
            </el-row>

          <textBorder>补充信息</textBorder>
          <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline" >
            <el-col :span="24">
              <el-form-item label="附件：" class="labeltext">
                 <pdfview type="1" :pdfArr="form.fileInfoList" ></pdfview>
              </el-form-item>
               <el-form-item label="备注：" class="labeltext markitem">
                <span class="mark">{{ form.notes }}</span>
              </el-form-item>
            </el-col>
          </el-row>

       

            <textBorder>系统信息</textBorder>
            <el-row :gutter="0" class="width100 mt10 pb12 mb30 bbline">
                <el-col :span="12">
                <el-form-item label="创建人：" class="labeltext">
                    <span>{{ form.createByName }}</span>
                </el-form-item>
                <el-form-item label="创建时间：" class="labeltext">
                    <span>{{ form.createTime }}</span>
                </el-form-item>
                </el-col>
                <el-col :span="12">  
                <el-form-item label="最后修改人：" class="labeltext">
                    <span>{{ form.modifyByName }}</span>
                </el-form-item>
                <el-form-item label="最后修改时间：" class="labeltext">
                    <span>{{ form.modifyTime }}</span>
                </el-form-item>
                </el-col>
            </el-row>
    </el-form>
  </div>
</template>

<script>
import pdfview from '@/components/common/pdfview.vue'
  import textBorder from '@/components/common/textBorder.vue'
  import {taskDelete} from '@/api/stage/index'
  export default {
    props:{
      form:{
        type:Object,
        default:()=>({})
      }
    },
    data(){
     return {
     }
    },
    components:{
      textBorder,
      pdfview,
    },
    methods:{
      goToEdit(){
        this.$router.push({
          path:'/projectManagement/stage/taskadd',
          query:{
            taskId:this.form.id
          }
        })
      },
      deleteHandle(){
        taskDelete({id:this.form.id}).then(res=>{
            if(res.status == 0){
                this.msgSuccess('删除成功')
                this.$emit('changeDrawer',false)
                this.$emit('loadData')
            }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.markitem /deep/.el-form-item__content{
  line-height: 24px !important;
  margin-top: 10px;
}
.mark{
  line-height: 24px !important;
}
.clear{
        clear: both;
    }
.pdfspan{
  margin-left: -18px !important;
}
/deep/ .guancontent {
    margin-left: -18px !important;
    margin: 0;
    margin-top: 10px;
}
.pb12{
    padding-bottom: 12px;
}

</style>
<style scoped>
.ptext{
  margin-bottom: 16px;
}
.mulh{
  line-height: 20px;
}
.pdfdiv{
  cursor: pointer;
  display: inline-block;
  height: 24px;
  border-radius: 4px 4px 4px 4px;
  border: 1px dashed #4285F4;
  line-height: 4px;
  padding: 10px;
  box-sizing: border-box;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285F4;
}
.lh /deep/.el-form-item__content{
    line-height: 18px !important;
    padding: 0;
    padding-top: 12px;
}
.infoform /deep/.el-form-item{
  margin-bottom: 0px;
}
.infoform{
  position: relative;
}
.bp{
  position: absolute;
  right: 0;
  top: 0;
}
.textwarp{
  word-wrap:break-word;
}

</style>