<template>
  <div>
    <div>
      <el-card class="box-card">
        <!-- button -->
        <el-row type="flex" class="row-bg" justify="end">
          <el-col class="col-bg" :span="24">
            <div class="grid-content bg-purple operation-but">
              <el-button
                size="small"
                type="primary"
                plain
                @click="addBannerClick"
                icon="el-icon-plus"
                >添加</el-button
              >
              <el-button
                size="small"
                type="danger"
                plain
                @click="deleteInfo()"
                icon="el-icon-delete"
                >删除</el-button
              >
            </div>
          </el-col>
        </el-row>
        <el-table
          ref="multipleTable"
          :data="bannerList"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="广告图片" width="180">
            <template slot-scope="scope">
              <img :src="scope.row.imagsUrl" class="itemimg" />
            </template>
          </el-table-column>
          <el-table-column
            prop="skipUrlPortal"
            label="广告数据链接"
            show-overflow-tooltip
            :formatter="linkFormatter"
          ></el-table-column>
          <el-table-column
            prop="issueType"
            label="轮播类型"
            :formatter="sexFormatter"
          ></el-table-column>
          <el-table-column prop="number" label="排序"></el-table-column>
          <el-table-column prop="createTime" label="添加时间"></el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="primary"
                plain
                @click="editBannerId(scope.row.id)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    <div class="block">
      <el-pagination
        layout="prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        :total="total"
      ></el-pagination>
    </div>
    <!-- add -->
    <el-dialog
      title="新增轮播图"
      :visible.sync="addBannerdialog"
      width="40%"
      :before-close="handleClose"
    >
      <el-form
        ref="addSchoolListref"
        :rules="addbannerlListRules"
        :model="addbannerlList"
        label-width="130px"
      >
        <el-form-item label="轮播类型" prop="typeFlag">
          <el-select
            v-model="addbannerlList.issueType"
            placeholder="请选择轮播类型"
            @change="selectType"
          >
            <el-option
              :label="item.name"
              :value="item.id"
              v-for="(item, index) in bannerType"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传图片">
          <el-upload
            class="upload-demo"
            :action="getUrl"
            name="file"
            :limit="1"
            accept=".jpg, .png, .JPG, .jpeg, .JPEG, .PNG, .gif"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :file-list="fileList"
            :headers="headerUrl"
            :data="imgUploadData"
            
            :auto-upload="false"
            :on-change='changeUpload'
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item
          v-show="showApp"
          label="广告跳转app链接"
          prop="skipUrlApp"
        >
          <el-input
            v-model="addbannerlList.skipUrlApp"
            placeholder="请输入广告跳转app链接"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-show="showPc"
          label="广告跳转pc链接"
          prop="skipUrlPortal"
        >
          <el-input
            v-model="addbannerlList.skipUrlPortal"
            placeholder="请输入广告跳转pc链接"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="number">
          <el-input
            v-model="addbannerlList.number"
            placeholder="请选择"
            type="number"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="clearRorm()">取 消</el-button>
        <el-button
          v-isShow="'ear:company:advertisingspace:update'"
          type="primary"
          @click="addBanner()"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <!-- /add -->
    <!-- edit -->
    <el-dialog
      title="编辑轮播图"
      :visible.sync="editBannerdialog"
      width="40%"
      :before-close="handleClose"
    >
      <el-form
        ref="addSchoolListref"
        :rules="addbannerlListRules"
        :model="addbannerlList"
        label-width="130px"
      >
        <el-form-item label="轮播类型" prop="typeFlag">
          <el-select
            v-model="addbannerlList.issueType"
            placeholder="请选择轮播类型"
          >
            <el-option
              :label="item.name"
              :value="item.id"
              v-for="(item, index) in bannerType"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传图片">
          <el-upload
            class="upload-demo"
            :action="getUrl"
            name="file"
            :limit="1"
            accept=".jpg, .png, .JPG, .jpeg, .JPEG, .PNG, .gif"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :file-list="fileListedit"
            :headers="headerUrl"
            :auto-upload="false"
            :on-change='changeUpload'
            :data="imgUploadData"
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item
          v-show="showApp"
          label="广告跳转app链接"
          prop="skipUrlApp"
        >
          <el-input
            v-model="addbannerlList.skipUrlApp"
            placeholder="请输入广告跳转app链接"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-show="showPc"
          label="广告跳转pc链接"
          prop="skipUrlPortal"
        >
          <el-input
            v-model="addbannerlList.skipUrlPortal"
            placeholder="请输入广告跳转pc链接"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="number">
          <el-input
            v-model="addbannerlList.number"
            placeholder="请选择"
            type="number"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="clearRorm()">取 消</el-button>
        <el-button type="primary" @click="editBanner()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- /edit -->
         <!-- vueCropper 剪裁图片实现-->
    <el-dialog title="图片剪裁" :visible.sync="dialogVisible" append-to-body>
      <div class="cropper-content">
        <div class="cropper" style="text-align:center">
        <vueCropper
            ref="cropper"
            :img="option.img"
            :outputSize="option.size"
            :outputType="option.outputType"
            :info="true"
            :full="option.full"
            :canMove="option.canMove"
            :canMoveBox="option.canMoveBox"
            :original="option.original"
            :autoCrop="option.autoCrop"
            :fixed="option.fixed"
            :fixedNumber="option.fixedNumber"
            :centerBox="option.centerBox"
            :infoTrue="option.infoTrue"
            :fixedBox="option.fixedBox"
          ></vueCropper>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="finish" :loading="loading">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getadvertisList,
  addeBanner,
  deleteBanner,
  bannerDetail,
  updateBanner,
} from '@/api/basesystem/index.js'
import { uploadFile } from '../../api/wapi';
export default {
  data() {
    return {
      showApp: false,
      showPc: true,
      bannerList: [],
      multipleSelection: [],
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        type: '1',
      },
      dialogVisible: false,
      // 裁剪组件的基础配置option
      option: {
        img: '', // 裁剪图片的地址
        info: true, // 裁剪框的大小信息
        outputSize: 0.8, // 裁剪生成图片的质量
        outputType: 'jpeg', // 裁剪生成图片的格式
        canScale: false, // 图片是否允许滚轮缩放
        autoCrop: true, // 是否默认生成截图框
        // autoCropWidth: 300, // 默认生成截图框宽度
        // autoCropHeight: 200, // 默认生成截图框高度
        fixedBox: true, // 固定截图框大小 不允许改变
        fixed: true, // 是否开启截图框宽高固定比例
        fixedNumber: [1920,400], // 截图框的宽高比例
        full: true, // 是否输出原图比例的截图
        canMoveBox: false, // 截图框能否拖动
        original: false, // 上传图片按照原始比例渲染
        centerBox: false, // 截图框是否被限制在图片里面
        infoTrue: true // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
      },
      loading:false,
      total: 0,
      addBannerdialog: false,
      addbannerlList: {
        issueType: 2,
        imagsUrl: '',
        coverFileName: '',
        coverFileSize: '',
        skipUrlPortal: '',
        skipUrlApp: '',
        number: '',
        type: 1,
      },
      bannerType: [
        { id: 2, name: 'pc' },
        { id: 1, name: '小程序' },
      ],
      addbannerlListRules: {
      },
      getUrl: `${process.env.VUE_APP_BASE_API}aliyun/oss/uploadFiles`,
      headerUrl: { Authorization: window.sessionStorage.getItem('token') },
      imgUploadData: { serviceName: 'company' },
      fileList: [],
      fileListedit: [],
      editBannerdialog: false,
    }
  },
  created() {
    this.getBanner()
  },
  methods: {
    addBannerClick() {
      for (let key in this.addbannerlList) {
        this.addbannerlList[key] = ''
      }
      this.addbannerlList.type = 1
      this.addBannerdialog = true
    },
    linkFormatter(row) {
      if (row.skipUrlPortal == '') {
        return row.skipUrlApp
      } else {
        return row.skipUrlPortal
      }
    },
    sexFormatter(row, column) {
      if (row.issueType == 2) {
        return 'pc'
      } else {
        return '小程序'
      }
    },
    selectType(msg) {
      if (msg == 2) {
        this.showApp = false
        this.showPc = true
      } else {
        this.showApp = true
        this.showPc = false
      }
    },
    async getBanner() {
      let res = await getadvertisList(this.pageBean)
      this.bannerList = res.data
      this.total = res.page.total
    },
    async addBanner() {
      let res = await addeBanner(this.addbannerlList)
      this.addBannerdialog = false
      this.fileList = []
      this.$message({
        type: 'success',
        message: '添加成功!',
      })
      this.getBanner()
    },
    async editBannerId(id) {
      this.fileListedit = []
      this.editBannerdialog = true
      let res = await bannerDetail(id)
      this.addbannerlList = res.data
      if (res.data.issueType == 2) {
        this.showApp = false
        this.showPc = true
      } else {
        this.showApp = true
        this.showPc = false
      }
      this.fileListedit.push({ url: res.data.imagsUrl,"name":res.data.coverFileName })
    },
    async editBanner() {
      this.addbannerlList.type = 1
      delete this.addbannerlList.createTime
      delete this.addbannerlList.modifyTime
      let res = await updateBanner(this.addbannerlList)
      this.getBanner()
      this.editBannerdialog = false
      this.addbannerlList = {}
    },
    deleteInfo(id) {
      if (this.multipleSelection.length == 0) {
        return this.$message.error('请选择删除项')
      }
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          this.newmultipleSelection = this.multipleSelection.map((item) => {
            return item.id
          })
          let res = await deleteBanner({
            ids: this.newmultipleSelection.join(','),
          })
          this.getBanner()
          this.$message({
            type: 'success',
            message: '删除成功!',
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    clearRorm() {
      this.editBannerdialog = false
      this.addBannerdialog = false
      this.addbannerlList = {}
      this.fileList = []
      this.fileListedit = []
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 上传图片
    handleRemove(file, fileList) {
      this.addbannerlList.imagsUrl = ''
      this.addbannerlList.coverFileName = ''
      this.addbannerlList.size = ''
    },
    handlePreview(file) {},
            // 上传按钮   限制图片大小
    changeUpload(file, fileList) {
      console.log("file=====>",file);
      this.fileList = [];
      if (!this.addbannerlList.issueType) {
        return this.$message.error("请先选择轮博类型")
      }
      switch (this.addbannerlList.issueType) {
        case 1:
           this.option.fixedNumber = [750,260];
          break;
        case 2:
           this.option.fixedNumber = [1920,400];
          break;
        default:
          break;
      }
      const jpegOrpng = (file.raw.type == 'image/jpeg' || file.raw.type == 'image/png');
      if (!jpegOrpng) {
        this.$message.info('上传封面文件只能是JPG 或 PNG');
        return
      }
      const isLt5M = file.size / 1024 / 1024 < 2
      if (!isLt5M) {
        this.$message.info('上传文件大小不能超过 2MB!');
        return
      }
      this.fileinfo = file;
      let reader = new FileReader();
      reader.readAsDataURL(new Blob([file.raw],{type :file.raw.type}));
      reader.onload = () =>{
        console.log("======>reader.result",reader.result);
        this.$nextTick(() => {
          this.dialogVisible = true
          this.option.img = reader.result;
        });
      }
    },
    // 点击裁剪，这一步是可以拿到处理后的地址
     finish() {
      this.$refs.cropper.getCropBlob((data) => {
        this.loading = true
       let formData = new FormData();
       formData.append("file",data,this.fileinfo.name);
       uploadFile( formData).then((res) => {
         this.loading = false;
         this.dialogVisible = false;
          if (res && res.data.url) {
            this.addbannerlList.imagsUrl = res.data.url
            this.addbannerlList.coverFileName = res.data.fileName
            this.addbannerlList.coverFileSize = res.data.size
            this.fileList = [{"url":this.addbannerlList.imagsUrl,"name":this.addbannerlList.coverFileName}]
          } else {
            this.$message.error(res.msg+"上传失败请稍后再试..");
            this.fileList = [];
          }
       }).catch((err) => {
         
       });

      })
    },
    // 监听pagesize的变化
    handleSizeChange(newSize) {},
    handleCurrentChange(newPage) {
      this.pageBean.pageNum = newPage
      this.getBanner()
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          this.fileListedit = []
          done()
        })
        .catch((_) => {})
    },
  },
}
</script>
<style  scoped>
.block {
  margin-top: 40px;
}
.seachBox {
  width: 300px;
  float: left;
  margin-right: 20px;
}
.seachInput {
  overflow: hidden;
}
.itemimg {
  width: 100px;
  height: 50px;
  object-fit: contain;
  margin-right: 5px;
}
/* // 截图 */
.cropper-content .cropper {
    width: auto;
    height: 300px;
}
</style>
