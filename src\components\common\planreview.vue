<template>
  <div class="bgdiv" v-loading="loading">
    <div v-if="viewtype == 'review'">
      <img
        class="closebtn"
        @click="close"
        src="../../assets/img/<EMAIL>"
        alt=""
      />
      <div class="headbg">
        <el-button
          v-if="!pageData.isFirstPage"
          type="text"
          class="bbtn w80"
          icon="el-icon-arrow-left"
          @click="lastData"
        >
          上一个
        </el-button>
        <el-button type="text" class="w80" v-else></el-button>
        <div class="centertitle">
          {{ getTimestr(reviewData) }}{{ types[plantype].name }}
        </div>
        <el-button
          v-if="!pageData.isLastPage"
          type="text"
          class="bbtn w80"
          @click="handleCurrentChange"
        >
          下一个
          <i class="el-icon-arrow-right el-icon--right"></i>
        </el-button>
        <el-button type="text" class="w80" v-else></el-button>
      </div>
      <div class="ctimecss bbtn">
        <div class="timecss">
          {{ searchTime }}
          <i class="el-icon-arrow-down"></i>
        </div>
        <el-date-picker
          class="mydatecss"
          v-model="planTime"
          @change="changeDatePicker"
          :type="types[plantype].type"
          :format="types[plantype].format"
          placeholder="请选择时间"
          prefix-icon="none"
          clear-icon="none"
        >
        </el-date-picker>
      </div>
    </div>
    <div v-else-if="viewtype == 'change'">
      <div class="ctitle">原计划</div>
    </div>
    <div v-else></div>

    <textBorder>个人{{ types[plantype].label }}</textBorder>
    <el-form class="myform" inline>
      <el-row :gutter="10">
        <el-col :span="8">
          <div class="carditem">
            <div class="flex">
              <img class="picon" src="../../assets/img2.png" alt="" />
              <div class="ptitle">新增拜访与跟进（次）</div>
            </div>
            <div>
              <div class="pconcss">
                {{ (reviewData.newVisit && reviewData.newVisit) || '--' }}
              </div>
              <div class="pmbcss">目标</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="carditem">
            <div class="flex">
              <img class="picon" src="../../assets/img5.png" alt="" />
              <div class="ptitle">新增客户(个)</div>
            </div>
            <div>
              <div class="pconcss">
                {{
                  (reviewData.newCustomers && reviewData.newCustomers) || '--'
                }}
              </div>
              <div class="pmbcss">目标</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="carditem">
            <div class="flex">
              <img class="picon" src="../../assets/img3.png" alt="" />
              <span class="ptitle">业绩(万元)</span>
            </div>
            <div>
              <div class="pconcss">
                {{
                  (reviewData.contractAmount && reviewData.contractAmount) ||
                  '--'
                }}
              </div>
              <div class="pmbcss">目标</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="carditem">
            <div class="flex">
              <img class="picon" src="../../assets/img4.png" alt="" />
              <div class="ptitle">回款金额(万元)</div>
            </div>
            <div class="pconcss">
              {{
                (reviewData.contractReturnAmount &&
                  reviewData.contractReturnAmount) ||
                '--'
              }}
            </div>
            <div class="pmbcss">目标</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="carditem">
            <div class="flex">
              <img class="picon" src="../../assets/hetongicon.png" alt="" />
              <div class="ptitle">新增合同(个)</div>
            </div>
            <div>
              <div class="pconcss">
                {{ (reviewData.newContract && reviewData.newContract) || '--' }}
              </div>
              <div class="pmbcss">目标</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="carditem">
            <div class="flex">
              <img class="picon" src="../../assets/yangshuicon.png" alt="" />
              <div class="ptitle">样书发放(次)</div>
            </div>
            <div>
              <div class="pconcss">
                {{
                  (reviewData.materialDeliverNum &&
                    reviewData.materialDeliverNum) ||
                  '--'
                }}
              </div>
              <div class="pmbcss">目标</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <textBorder v-if="plantype == '1'"
      >单位{{ types[plantype].label }}</textBorder
    >
    <div v-if="plantype == '1'">
      <el-row :gutter="8">
        <el-col :span="6">
          <unititem
            name="信息化业绩"
            unit="万元"
            :icon="require('../../assets/img3.png')"
            :num="unitData.informationAmount"
          ></unititem>
        </el-col>
        <el-col :span="6">
          <unititem
            name="教材业绩"
            unit="万元"
            :icon="require('../../assets/img4.png')"
            :num="unitData.teachingMaterialAmount"
          ></unititem>
        </el-col>
        <el-col :span="6">
          <unititem
            name="合同数量"
            unit="个"
            :icon="require('../../assets/hetongicon.png')"
            :num="unitData.totalContract"
          ></unititem>
        </el-col>
        <el-col :span="6">
          <unititem
            name="新增跟进与拜访"
            unit="次"
            :icon="require('../../assets/img2.png')"
            :num="unitData.totalVisit"
          ></unititem>
        </el-col>
      </el-row>
    </div>
    <div class="tablebox" v-if="plantype == '1'">
      <el-table
        class="mytable"
        v-for="(item, index) in getUnitGoalList(
          reviewData.personalGoalsUnitList
        )"
        :key="index"
        :data="item"
        :span-method="objectSpanMethod"
        border
      >
        <el-table-column prop="unitName" label="学校">
          <template slot-scope="scope">
            <span>{{ scope.row.unitName }} </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="unitgoalTypeName"
          label="目标维度"
          width="180"
        ></el-table-column>
        <el-table-column prop="goalNum" label="目标" width="100">
          <template v-slot="{ row }">
            <span>{{ row.goalNum }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <textBorder>计划内容</textBorder>
    <div class="modelcss pt20">
      <showmodel :viewDataList="reviewData.templateItemList"></showmodel>
    </div>
    <textBorder>抄送人</textBorder>
    <div class="modelcss pt20">
      <ul class="uflex" v-if="reviewData.copyPersons.length > 0">
        <li
          v-for="(item, index) in reviewData.copyPersons"
          :key="index"
          class="pli"
        >
          <img v-if="item.logo" class="pimg" :src="item.logo" alt="" />
          <div class="noimg" v-else>
            {{ item.name }}
          </div>
          <p class="pname">{{ item.name }}</p>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import textBorder from '@/components/common/textBorder.vue'
import showmodel from '@/components/zongjie/temmodel/showmodel.vue'
import { getPlanTime, getWeek } from '@/utils/index'
import { queryReviewGoals } from '@/api/goal'
import unititem from '../plan/common/unititem.vue'
export default {
  components: {
    textBorder,
    showmodel,
    unititem,
  },
  props: {
    plantype: {
      type: String,
      default: '1',
    },
    viewtype: {
      type: String,
      default: 'review',
    },
    viewData: {
      type: Array,
      default: () => {
        return new Array()
      },
    },
    date: {
      type: Date,
      default: () => {
        return new Date()
      },
    },
    page: {
      type: Object,
      default: () => {
        return new Object()
      },
    },
  },
  data() {
    return {
      loading: false,
      keyNames: {
        informationAmount: '信息化业绩（万元）',
        teachingMaterialAmount: '教材业绩金额（万元）',
        totalContract: '合同数量',
        totalVisit: '拜访次数',
      },
      types: {
        1: {
          name: '年度计划',
          label: '年度目标',
          name2: '年度',
          type: 'year',
          format: 'yyyy',
        },
        2: {
          name: '月度计划',
          label: '月度目标',
          name2: '月度',
          type: 'month',
          format: 'yyyy-M',
        },
        3: {
          name: '计划',
          label: '周目标',
          name2: '',
          type: 'week',
          format: 'yyyy 第 W 周',
        },
      },
      planTime: '',
      sForm: {
        goalsType: this.plantype + '',
        createBy: '',
        year: '',
        month: '',
        week: '',
        pageNum: 1,
        pageSize: 1,
      },
      searchTime: '',
      reviewData: {},
      pageData: {},
      unitData: {
        informationAmount: 0,
        teachingMaterialAmount: 0,
        totalContract: 0,
        totalVisit: 0,
      },
      defunitList: [
        [
          {
            unitName: '暂未选择学校',
            unitgoalType: 'informationAmount',
            unitgoalTypeName: '信息化业绩（万元）',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'teachingMaterialAmount',
            unitgoalTypeName: '教材业绩金额（万元）',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'totalContract',
            unitgoalTypeName: '合同数量',
            goalNum: '--',
          },
          {
            unitName: '暂未选择学校',
            unitgoalType: 'totalVisit',
            unitgoalTypeName: '拜访次数',
            goalNum: '--',
          },
        ],
      ],
    }
  },
  created() {
    this.reviewData = this.viewData[0]
    this.pageData = this.page
    this.planTime = this.date
    this.sForm.year = this.reviewData.year != '0' ? this.reviewData.year : ''
    this.sForm.month = this.reviewData.month != '0' ? this.reviewData.month : ''
    this.sForm.week = this.reviewData.week != '0' ? this.reviewData.week : ''
    this.sForm.createBy = this.reviewData.createBy
    this.searchTime =
      this.getTimestr(this.reviewData, this.plantype) +
      this.types[this.plantype].name2
    this.getUnitTotal()
  },
  methods: {
    loadData() {
      queryReviewGoals(this.sForm)
        .then((result) => {
          if (result.data && result.data.length > 0) {
            this.loading = false
            this.reviewData = result.data[0]
            this.pageData = result.page
            this.searchTime =
              this.getTimestr(this.reviewData, this.plantype) +
              this.types[this.plantype].name2
            this.getUnitTotal()
          } else {
            this.loading = false
            this.$message({
              type: 'error',
              message: '暂无计划回顾可查看',
            })
          }
        })
        .catch((err) => {})
    },
    getUnitTotal() {
      Object.keys(this.unitData).forEach((item) => {
        this.unitData[item] = 0
      })
      this.reviewData.personalGoalsUnitList.forEach((item) => {
        this.unitData.informationAmount =
          this.unitData.informationAmount + item.informationAmount
        this.unitData.teachingMaterialAmount =
          this.unitData.teachingMaterialAmount + item.teachingMaterialAmount
        this.unitData.totalContract =
          this.unitData.totalContract + item.totalContract
        this.unitData.totalVisit = this.unitData.totalVisit + item.totalVisit
        console.log('this.unitData', this.unitData)
      })
    },
    changeDatePicker(date) {
      if (!date) {
        this.sForm.year = ''
        this.sForm.month = ''
        this.sForm.week = ''
        return
      }
      var newDate = new Date(date)
      this.sForm.year = newDate.getFullYear()
      if (this.plantype == 2) {
        this.sForm.month = newDate.getMonth() + 1
      } else if (this.plantype == 3) {
        var time = getWeek(newDate)
        var times = time.split('-')
        this.sForm.year = times[0]
        this.sForm.week = times[1]
      }
      this.sForm.pageNum = 1
      this.loading = true
      this.loadData()
    },
    lastData() {
      this.sForm.pageNum--
      this.loadData()
    },
    handleCurrentChange() {
      this.sForm.pageNum++
      this.loadData()
    },
    getTimestr(data) {
      return getPlanTime(data, this.plantype)
    },
    close() {
      this.$emit('closereview')
    },
    getUnitGoalList(array) {
      if (array.length > 0) {
        var unitGoalList = []
        var keys = [
          'informationAmount',
          'teachingMaterialAmount',
          'totalContract',
          'totalVisit',
        ]
        array.forEach((element, idx) => {
          var list = []
          keys.forEach((key) => {
            var item = {
              id: element.id,
              index: idx,
              unitId: element.unitId,
              unitName: element.unitName,
              year: element.year,
              goalNum: element[key],
              unitgoalType: key,
              unitgoalTypeName: this.keyNames[key],
            }
            list.push(item)
          })
          unitGoalList.push(list)
        })
        /**
                
                 */
        return unitGoalList
      } else {
        return this.defunitList
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (columnIndex === 0 && rowIndex === 0) {
          return {
            rowspan: 4,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
  },
}
</script>
<style scoped>
.flex {
  display: flex;
  align-items: center;
}
.carditem {
  width: 100%;
  height: 190px;
  background: #ffffff;
  box-shadow: 0px 2px 16px 0px rgba(15, 27, 50, 0.08);
  border-radius: 8px 8px 8px 8px;
  padding: 16px 20px;
  margin-bottom: 10px;
}
.pmbcss {
  width: 100%;
  height: 21px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  line-height: 19px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 20px;
}
.pconcss {
  margin-top: 36px;
  width: 100%;
  height: 28px;
  font-family: Roboto, Roboto;
  font-weight: bold;
  font-size: 1.5em;
  color: #333333;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.picon {
  min-width: 40px;
  height: 40px;
}
.ptitle {
  padding-left: 10px;
  max-height: 40px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 1em;
  color: #999999;
  font-style: normal;
  text-transform: none;
}
.pname {
  text-align: center;
}
.noimg {
  width: 48px;
  height: 48px;
  line-height: 48px;
  border-radius: 8px;
  background-color: #4285f4;
  text-align: center;
  color: #fff;
  font-size: 12px;
}
.uflex {
  display: flex;
  flex-wrap: wrap;
}
.closeimg {
  position: absolute;
  right: -8px;
  top: -8px;
  cursor: pointer;
}
.pli {
  margin-right: 16px;
  margin-bottom: 16px;
  position: relative;
}
.pli:last-child {
  margin-right: 0px;
}
.pimg {
  width: 48px;
  height: 48px;
  border-radius: 8px 8px 8px 8px;
}
.pflex {
  display: flex;
  margin-top: 10px;
  flex-wrap: wrap;
}
.adddi {
  width: 44px;
  height: 44px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d6d6d6;
  text-align: center;
  line-height: 44px;
  font-size: 18px;
  color: #d6d6d6;
  cursor: pointer;
}
.w80 {
  width: 70px !important;
}
.centertitle {
  height: 26px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 20px;
  color: #333333;
  line-height: 23px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 12px;
}
.mydatecss /deep/.el-input__inner {
  border: none;
  text-align: center;
  color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
}
.mydatecss {
  position: absolute;
  top: 0;
  left: calc(50% - 50px);
  width: 100px;
  background-color: rgba(0, 0, 0, 0);
}
.ctimecss {
  position: relative;
  cursor: pointer;
  text-align: center;
  margin-bottom: 20px;
}
.tablebox {
  height: 290px;
  overflow-x: hidden;
  padding-right: 10px;
  margin-top: 20px;
}
.myform /deep/.el-form-item {
  margin-bottom: 0px !important;
}
.myform {
  margin: 16px 0;
}
.headbg {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.bgdiv {
  background-color: white;
  border-radius: 10px;
  min-height: calc(100vh - 140px);
  width: 100%;
  padding: 20px;
  position: relative;
}
.closebtn {
  position: absolute;
  right: 8px;
  top: 10px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 100;
}
.ctitle {
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 20px;
  font-weight: 500;
}
</style>