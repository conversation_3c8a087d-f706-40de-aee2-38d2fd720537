<!--基本资料-->
<template>
    <div>
        <div class="message">
            <div><img  :src="this.firstData.avatar" alt=""></div>
            <div>
                <ul>
                    <li v-for="item in message1" :key="item.id">
                        <span>{{ item.label }}</span>
                        <span>{{ item.value }}</span>
                    </li>
                </ul>
            </div>
            <div>
                <ul>
                    <li v-for="item in message2" :key="item.id">
                        <span>{{ item.label }}</span>
                        <span>{{ item.value }}</span>
                    </li>
                </ul>
            </div>
            <div class="check-phone-number">
              <el-button type="text" @click="enquiryNumber">查看会员手机号</el-button>
              <!-- <span>{{ this.memberNumber }}</span> -->
            </div>
        </div>
        <h2 class="album-title"><span>相册{{ this.album.length }}张</span></h2>
        <div class="photo-album">
            <ul>
                <li v-for="picture in album" :key="picture.id">
                    <img :src="picture.url" alt="">
                </li>
            </ul>
        </div>
        <!-- 手机号查询弹框 -->
        <el-dialog
            title="查看会员手机号"
            :visible.sync="dialog"
            width="30%"
            append-to-body>
            <el-form label-width="100px">
                <el-form-item label="查看原因：">
                    <el-select clearable v-model="area">
                        <el-option
                            v-for="item in cause"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialog = false">取 消</el-button>
                <el-button type="primary" @click="confirmSubmit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
  props: [ 'first' ],
  data() {
    return {
      message1: [
        { id: 1, label: '昵称：', value: '' },
        { id: 2, label: '账号：', value: '' },
        { id: 3, label: '姓名：', value: '' },
        { id: 4, label: '年龄：', value: '' },
        { id: 5, label: '身高：', value: '' },
        { id: 6, label: '婚姻状况：', value: '' }
      ],
      message2: [
        { id: 1, label: '体型：', value: '' },
        { id: 2, label: '学历：', value: '' },
        { id: 3, label: '居住地：', value: '' },
        { id: 4, label: '户籍地：', value: '' },
        { id: 5, label: '职业：', value: '' },
        { id: 6, label: '民族：', value: '' }
      ],
      dialog: false,
      album: [],
      imageList: {},
      area: '',
      firstData: {},
      cause: [],
      // memberNumber: '',
      Submit: {}
    }
  },
  watch: {
    firstSelectValue(val) {
      this.first = val
      this.detailData()
      this.getImgList()
    }
  },
  computed: {
    firstSelectValue() {
      return this.first
    }
  },
  // 详情数据
  methods: {
    async detailData () {
      let res = await this.$axios.get(`/sf/member/ocs/info/${this.first.id}`);
      if (res.status === 0) {
        this.firstData = res.data;
        this.message1[0].value = this.firstData.nickname
        this.message1[1].value = this.firstData.businessId
        this.message1[2].value = ''
        this.message1[3].value = ''
        this.message1[4].value = this.firstData.height
        this.message1[5].value = this.firstData.maritalStatusDesc
        this.message2[0].value = this.firstData.bodilyFormName
        this.message2[1].value = this.firstData.occupationName
        this.message2[2].value = ''
        this.message2[3].value = `${this.firstData.householdProvinceName} ${this.firstData.householdCityName} ${this.firstData.householdCountyName}`
        this.message2[4].value = ''
        this.message2[5].value = this.firstData.nationalityName
      }
    },
    // 会员照片列表
    async getImgList() {
      this.imageList.pageNum = '1'
      this.imageList.pageSize = '5'
      this.imageList.userId = this.first.id
      let res = await this.$axios.get('sf/member/ocs/userPhoto', { params: this.imageList })
      if (res.status === 0) {
        this.album = res.data;
      } else {
        this.error(`${res.msg}`)
      }
    },
    // 查看会员手机号
    async enquiryNumber() {
      this.dialog = true
      let res = await this.$axios.get('sf/business/tagitem/listAll', {params: {tagtypeCode: 'VIEW_USER_MOBILEPHONE_REEASON'}})
      if (res.status === 0) {
        this.cause = res.data
      } else {
        this.error(`${res.msg}`)
      }
    },
    // 弹出框确认提交
    async confirmSubmit() {
      this.cause.map(item => {
        if (item.id === this.area) {
          this.Submit.reasonContent = item.name
        }
      })
      this.Submit.toUserId = this.first.id
      this.Submit.toUserNickname = this.firstData.nickname
      this.Submit.reasonId = this.area
      let res = await this.$axios.post('sf/userinfo/ocsviewusermobilephonerecord/save', this.Submit)
      if (res.status === 0) {
        // this.memberNumber = res.msg
        this.$message.success(res.msg)
      } else {
        this.error(`${res.msg}`)
      }
      this.dialog = false
    }
  },
  created() {
    // this.memberNumber = ''
    this.detailData()
    this.getImgList()
  }
}
</script>

<style lang="scss" scoped>
    ul li {
        list-style: none;
        margin: 0px;
    }
    .message::after {
        content: '';
        display: block;
        clear: both;
        visibility: hidden;
    }
    .message {
      font-size: 14px;
        .check-phone-number {
          margin-left: 20px;
          .el-button {
            padding: 0;
          }
        }
        div {
            float: left;
            ul li {
              margin-bottom: 20px;
                span {
                    display: inline-block;
                }
                span:first-child {
                    width: 80px;
                    text-align: right;
                }
            }
        }
        div:first-child {
            width: 80px;
            height: 80px;
            img {
                width: 80px;
                height: 80px;
            }
        }
        div:nth-child(2) {
            margin: 0 56px 0 14px;
        }
    }
    // 相册（8张）标题
    .album-title {
        padding-bottom: 20px;
        border-bottom: 1px solid #E6E6E6;
    }
    // 相册（8张）
    .photo-album {
        margin-top: 15px;
        ul li {
            float: left;
            img {
                width: 90px;
                height: 90px;
            }
        }
    }
</style>
