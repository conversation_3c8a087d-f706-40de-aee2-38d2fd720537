<template>
  <div class="mainbg">
      <zongAddmodelCom :typeModel="typeModel"></zongAddmodelCom>
  </div>
</template>
<script>
import zongAddmodelCom from '../../zongjie/temmodel/addmodel.vue';
  export default {
      components: {
        zongAddmodelCom,
      },
      data() {
          return {
            typeModel:'1'
          }
      },
      created() {
      },
      mounted() {
         
      },
      methods: {
   
      }
  }
</script>
<style scoped>
</style>