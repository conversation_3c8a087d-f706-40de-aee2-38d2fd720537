<template>
  <el-dialog
  title=""
  :visible.sync="dialogVisible"
  class="viewdialog"
  width="540px"
  @close="close">
  <template #title>

  </template>
   <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="`已读(${data.first.length})`" name="first"></el-tab-pane>
      <el-tab-pane :label="`未读(${data.second.length})`" name="second"></el-tab-pane>
      <el-tab-pane :label="`点赞(${data.third.length})`" name="third"></el-tab-pane>
    </el-tabs>
  <div class="listdiv" v-if="data[activeName] && data[activeName].length>0">
    <div class="uitemcss" v-for="item in data[activeName]" :key="item.id">
      <div class="head">
        <headuser width="32" :url="item.logo" :username="item.name"></headuser>
      </div>
      <div>
        <span class="username">{{item.name}}</span>
      </div>
    </div>
  </div>
  <div class="listdiv" v-else>
    <nolist class="nodatapd"></nolist>
  </div>



</el-dialog>
</template>

<script>
import {
  queryViewCount,
  queryLikeList
 } from "@/api/studyshare/index";
import Headuser from "@/components/common/headuser.vue";
import nolist from "@/components/common/nolist.vue";
export default {
  components:{
    Headuser,
    nolist
  },
  data(){
    return{
      dialogVisible:false,
      activeName:'first',
      data:{
        "first":[],
        "second":[],
        "third":[]
      },
    }
  },
  methods:{
    close(){
      this.dialogVisible = false;
    },
    show(id){
      this.activeName = 'first'
      this.loaddata(id)
      this.loadLikeData(id)
    },
    loaddata(id){
      queryViewCount({
        caseId:id
      }).then((result) => {
        this.data.first = result.data.viewList;
        this.data.second = result.data.notViewList;
        this.dialogVisible = true;
      }).catch((err) => {

      });
    },
    loadLikeData(id){
      queryLikeList({
        id:id,
      }).then((result) => {
        this.data.third = result.data || []
      }).catch((err) => {

      });
    },
    handleClick(){

    },
  },
}
</script>

<style>

</style>

<style lang="scss" scoped>
.nodatapd{
  margin: 0 auto;
  margin-top: 50px;
}
.listdiv{
  padding: 16px 0px;
  width: 100% !important;
  max-height: calc(100% - 50px);
  overflow-y: auto !important;
  display: flex;
  flex-wrap: wrap;
}
.uitemcss{
  width: 52px;
  margin-right: 10px;
  margin-bottom: 16px;
  text-align: center;
}
.head{
  width:32px;
  height: 32px;
  margin: 0 auto;
  margin-bottom: 6px;
}
.username{
  width: 52px;
  height: 20px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #4D4D4D;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.uitemcss:nth-child(8n){
  margin-right: 0px !important;
}
.viewdialog{
  /deep/.el-dialog__header{
    padding: 0 20px !important;
  }
  /deep/.el-tabs{
    width: calc(100% - 50px) !important;
  }
  /deep/.el-tabs__header{
    margin-bottom: 8px !important;
  }
  /deep/.el-tabs__nav-wrap::after{
    height: 0 !important;
  }
}
.viewdialog{
  /deep/.el-dialog__body{
    width: 100% !important;
    height: 580px !important;
    padding: 20px !important;
  }
}
</style>
