<template>
  <div class="bg">
    <div class="header">
      <back>返回</back>
    </div>
    <div class="concss">
      <div class="leftlist" v-loading="isLoading" :class="{width100:!checkPermission('/caseLibrary/rank')}">
        <div class="tdivcss">
          <div class="title">我的分享</div>
          <el-dropdown
          placement="bottom-end"
          @command="handleCommand"
          trigger="click"
          class="right">
            <el-button v-isShow="'crm:controller:case:save'" class="defaultbtn sharebtn" icon="el-icon-share"
              type="primary">我要分享</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">视频类</el-dropdown-item>
              <el-dropdown-item :command="2">文章类</el-dropdown-item>
              <el-dropdown-item :command="3">文档类</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>

        <el-row :gutter="20">
          <el-col class="mb12 w25 itemcss" :span="spanNum" :key="index" v-for="(item,index) in dataList">
            <div class="card" @click="detailAction(item)">
              <div class="posi">
                <span class="rtext">{{item.caseClassifyName}}</span>
                <img class="fengmian" :src="item.caseCoverUrl" alt="">
                <div class="lookdiv">
                  <span class="sptext"><img class="iconimg" src="@/assets/look.png" alt="">
                    {{item.caseViewNum}}</span>
                </div>
              </div>
              <div class="condiv">
                <div class="jianjiecss">
                  {{item.caseName}}
                </div>
                <div class="bottomcss">
                  <span class="timecss mw67">
                   <img class="iconimg" src="@/assets/img/time.png" alt=""> {{item.createTime}}
                  </span>
                  <div class="fl">
                    <img class="btncss" @click.stop="toEdit(item)" src="@/assets/editbtn.png" alt="">
                    <img class="btncss" @click.stop="showDelete(item)" src="@/assets/deletebtn.png" alt="">
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <noList v-if="dataList.length == 0 && loadBoo "></noList>
        </el-row>
        <page :currentPage="pageBean.pageNum" :pageSize="pageBean.pageSize" :total="total"
          @updatePageNum="handleCurrentChange"></page>
      </div>
      <div class="rlist" >
        <div class="personalcss">
          <div class="topcss">
            <headuser class="headcss" width="72" :url="personalData.userLogo" :username="personalData.userName"></headuser>
            <div class="unamecss">{{personalData.userName}}</div>
          </div>
          <div class="personmessage">
              <div class="msgitem">
                <div class="count">{{personalData.learningTime}}</div>
                <div class="tname">学习(分钟)</div>
              </div>
              <div class="line"></div>
              <div class="msgitem">
                <div class="count">{{personalData.caseNum}}</div>
                <div class="tname">我的分享</div>
              </div>
              <div class="line"></div>
              <div class="msgitem">
                <div class="count">{{personalData.totalScore}}</div>
                <div class="tname">获得积分</div>
              </div>
          </div>
        </div>
        <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
        :stretch="false"
        v-if="checkPermission('/caseLibrary/rank')"
        class="tabscss">
          <el-tab-pane label="时长排行" name="first"></el-tab-pane>
          <el-tab-pane label="积分排行" name="second"></el-tab-pane>
          <rankItem
          :rankType="rankType"
          :key="index"
          v-for="(item,index) in sortList"
          :itemData="item"
          :sort="index+1"></rankItem>
        </el-tabs>
      </div>
    </div>
    <dc-dialog iType="1" title="温馨提示" width="500px" :showCancel="true" :dialogVisible.sync="deleteVisible"
      @submit="submitDialog">
      <template>

      </template>
      <p class="pcc">是否删除该案例？删除后该案例的相关评论信息也将删除。</p>
    </dc-dialog>
    <msg-dialog ref="msgdialog" />
  </div>
</template>

<script>
  import page from '@/components/common/page.vue';
  import rankItem from './components/rankItem.vue';
  import headuser from "@/components/common/headuser.vue";
  import back from "@/components/common/back.vue";
  import datepicker from '@/components/common/datepicker.vue'
  import dcDialog from '../../common/dcDialog.vue';
  import { studyshareList, queryCaseLearningRanking, deletestudyshare,queryMyCaseInfo } from "@/api/studyshare/index";
  import { getDict,getParStr } from "@/utils/tools";
  import noList from '@/components/common/nolist.vue'

  import { checkPermission } from '@/utils/permission'
  export default {
    components: {
      page,
      rankItem,
      headuser,
      back,
      noList,
      datepicker
    },
    data() {
      return {
        activeName:'first',
        loadBoo: false,
        types: [
          {
            id: '',
            name: '全部'
          },
          {
            id: '1',
            name: '视频类'
          },
          {
            id: '2',
            name: '文章类'
          },
          {
            id: '3',
            name: '文档类'
          }
        ],
        defZan: require('../../../assets/img/zan_def.png'),
        selZan: require('../../../assets/img/zan_sel.png'),
        tagMaxWid: 0,
        spanNum: 6,
        dataList: [],
        sortList: [],
        deleteVisible: false,
        deleteData: {},
        level: sessionStorage.getItem('dataScope'),
        userId: sessionStorage.getItem('userid'),
        isLoading: false,
        total: 0,
        pageBean: {
          caseName: "",
          caseType: "",
          caseClassify: "",
          createByName: "",
          departmentName:'',
          pageNum: 1,
          pageSize: 8,
          personalFlag:1,
          unitName:'',
          time:'',
          startTime:'',
          endTime:''
        },
        options: [],
        rankPage: {
          pageNum: 1,
          pageSize: 10,
          rankType:1,
        },
        rankType:1,
        rankObj:{
          'first':1,
          'second':2,
        },
        personalData:{},
      }
    },
    created() {
      if (Object.keys(this.$route.query).length>0) {
          this.pageBean = Object.assign(this.pageBean,this.$route.query)
          this.pageBean.pageNum = Number(this.pageBean.pageNum)
          this.pageBean.pageSize = Number(this.pageBean.pageSize)
      }
      this.getTypeList();
      var wid = window.screen.availWidth;
      var conwidth = wid - 170 - 40;
      var listviewWidth = conwidth * 0.8;
      var num = 0;
      if (listviewWidth < 1000) {
        this.spanNum = 8;
        num = 3;
        this.pageBean.pageSize = 6;
      } else {
        this.spanNum = 6;
        num = 4;
        this.pageBean.pageSize = 8;
      }
      var itemWidth = listviewWidth / num - 12;
      this.tagMaxWid = itemWidth - 36;
      this.loadData();
      this.loadRank();
      this.laodPersonal()
    },
    mounted(){
       if (sessionStorage.getItem('isAddStudy')) {
          this.$refs.msgdialog.show({
            type:'success',
            title:'提交成功',
            msg:"您的学习分享已成功提交，感谢您的分享！",
            onClose:()=>{
              sessionStorage.removeItem('isAddStudy')
            }
          })
        }
    },
    methods: {
      handleClick(tab, event) {
        this.activeName = tab.name;
        this.rankType = this.rankObj[tab.name]
        this.rankPage.rankType = this.rankType
        this.loadRank()
      },
      laodPersonal(){
        queryMyCaseInfo({}).then((result) => {
          console.log('dddds',result.data);
          this.personalData = result.data
        }).catch((err) => {

        });
      },
      getTypeList() {
        getDict('CaseType').then((result) => {
          this.options = result;
        }).catch((err) => {

        });
      },
      loadData() {
        history.replaceState(null,null,`#${this.$route.path}?${getParStr(this.pageBean)}`)
        this.isLoading = true;
        studyshareList(this.pageBean).then((result) => {
          result.data.forEach(item => {
            item.tags = item.label.split(',')
            item.newTags = this.processData(item.tags)
            item.isDelShow = this.level == 4 || this.userId == result.data.createBy;
          });
          this.dataList = result.data;
          this.total = result.page.total;
          this.isLoading = false;
          this.loadBoo = true
        }).catch((err) => {
          this.isLoading = false;
        });
      },
      loadRank() {
        queryCaseLearningRanking(this.rankPage).then((result) => {
          this.sortList = result.data;
        }).catch((err) => {

        });
      },
      phDetail() {
        this.$router.push('/studyShare/caseLibrary/rank')
      },
      handleCommand(data) {
        this.$router.push({
          path: '/studyShare/caseLibrary/add',
          query: { type: data }
        })
      },
      searchAction() {
        this.pageBean.pageNum = 1;
        this.loadData();
      },
      zanAction(data) {
      },
      detailAction(data) {

        if (!checkPermission('caseLibrary/detail')) {
          this.$message({
            type: "error",
            message: '您没有操作权限'
          })
          return
        }
        this.$router.push({
          path: '/studyShare/caseLibrary/detail',
          query: { id: data.id }
        })
      },
      processData(tags) {
        var tagWidth = 0;
        var list = [];

        tags.forEach(item => {
          var itemWid = item.length * 16 + 16;
          tagWidth = tagWidth + itemWid;
          if (tagWidth > this.tagMaxWid) {
            return
          } else {
            list.push(item);
          }
        })
        return list;
      },
      handleCurrentChange(page) {
        this.pageBean.pageNum = page;
        this.loadData();
      },
      toEdit(data){
        this.$router.push({
          path:'/studyShare/caseLibrary/add',
          query:{
            id:data.id,
            type:data.caseType
          },
        })

      },
      showDelete(data) {
        this.deleteData = data;
        this.deleteVisible = true;
      },
      submitDialog() {
        // 删除数据
        deletestudyshare({ id: this.deleteData.id }).then((result) => {
          if (result.data) {
            this.$message({
              type: "success",
              message: result.msg
            })
            this.deleteVisible = false;
            this.loadData();
          } else {
            this.$message({
              type: "error",
              message: result.msg
            })
          }
        }).catch((err) => {

        });
      },
      submitAction(type, dateRange) {
        if (type == 11) {
          if (dateRange) {
            this.pageBean.startTime = dateRange[0]
            this.pageBean.endTime = dateRange[1]
          } else {
            this.pageBean.startTime = ''
            this.pageBean.endTime = ''
          }
          this.pageBean.time = ''
        } else {
          this.pageBean.time = type
          this.pageBean.startTime = ''
          this.pageBean.endTime = ''
        }
      },
    }
  }
</script>
<style scoped>
.msgitem{
  width: calc(100% - 2px)
}
.line{
  margin-top: 10px;
  width: 1px;
  height: 52px;
  background-color: rgba(255,255,255,0.2);
}
.msgitem .count{
  font-family: DIN, DIN;
  font-weight: bold;
  font-size: 32px;
  color: #FFFFFF;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.msgitem .tname{
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: rgba(255,255,255,0.8);
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.timecss{
  height: 20px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #999999;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.btncss{
  width: 16px;
  height: 16px;
}
.btncss{
  margin-left: 14px;
}
.topcss{
  text-align: center;
  display: flex;
  justify-content: start;
  align-items: center;
}
.topcss .unamecss{
  height: 30px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #FFFFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.topcss .headcss{
  width: 72px;
  height: 72px;
  border-radius: 50%;
  overflow: hidden !important;
  border: 2px solid rgba(255,255,255,0.6);

}
.headcss .headdiv{
  margin-right: 0 !important;
}
.personmessage{
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 16px;

}
.bottomcss{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
}
.personalcss{
  padding: 20px;
  text-align: center;
  border: 1px solid #f5f5f5;
  background: url('../../../assets/personbg.png') no-repeat;
  background-size: 100% 100%;

}
  .tdivcss{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

  }
  .tdivcss .title{
    font-size: 16px;
    color: #4285F4;
    font-weight: 500;
  }
  .lookdiv{
    position: absolute;
    left: 0;
    bottom: 0;
    height: 40px;
    background: linear-gradient( 180deg, rgba(51,51,51,0) 0%, rgba(51,51,51,0.6) 100%);
    border-radius: 0px 0px 0px 0px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    line-height: 40px;
    text-align: right;
    color: #FFFFFF;
    width: 100%;
    padding-right: 5px;
  }
  .posi{
    position: relative;
  }
  .tabscss{
    margin-top: 20px;
    background: #FFFFFF;
    height: calc(100% - 220px);
    border-radius: 8px;
  }
  .tabscss /deep/.el-tabs__content{
    height:calc(100% - 50px);
    overflow-y: auto;
  }
  .tabscss /deep/.el-tabs__item{
     width: 50%;
     text-align: center;
  }
  .tabscss /deep/.el-tabs__nav{
     width: 100%;
    background: url(../../../assets/liebg.png) no-repeat;
    background-size: 100% 100%;
  }

  .mtrow{
    margin-top: 10px;
  }
  .deletebtn {
    position: absolute;
    width: 24px;
    height: 24px;
    top: 10px;
    right: 12px;
    cursor: pointer;
  }
  .sharebtn {
    margin-left: 10px;
  }
  .mw67 {
    max-width: 167px;
  }

  .ml10 {
    margin-left: 5px;
  }

  .itemcss {
    position: relative;
    cursor: pointer;
  }

  .text24 {
    width: 24px;
    padding-top: 3px;
    text-align: center;
  }

  .flex {
    display: flex;
    align-items: center;
  }

  .img24 {
    width: 24px;
    height: 24px;
  }

  .rankhead {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-left: 8px;
    margin-right: 12px;
  }

  .rankcss {
    height: 56px;
    padding: 0px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .tc {

    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    line-height: 15px;
    cursor: pointer;
  }

  .phtitle {

    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 19px;

  }

  .paihang_t {
    background: url(../../../assets/liebg.png) no-repeat;
    background-size: 100% 100%;
    height: 20px;
    padding: 0px 16px;
  }

  .paihang_t img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }

  .mr0 {
    margin-right: 0px !important;
  }

  .datacss1 {
    margin-top: 5px;
    padding-left: 12px;
    padding-right: 4px;
    padding-bottom: 20px;
    height: 42px;
    overflow: hidden;
    white-space: nowrap;
    overflow: hidden;
  }

  .tagitem {
    margin-right: 8px;
    padding: 4px 7px;
    padding-bottom: 4px;
    background: #DFEAFD;
    border-radius: 2px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4285F4;
    line-height: 14px;
  }

  .datacss {
    margin-top: 7px;
    height: 24px;
    background: rgba(255,164,76,0.12);
    border-radius: 2px 2px 2px 2px;
    max-width: calc(100% - 24px);
    width: auto;
    display: inline-block;
    margin: 0 12px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #FFA44C;
    box-sizing: border-box;
    padding: 0 8px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .rtext {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 14px;
    margin-top: 2px;
    display: block;
    width: 64px;
    height: 24px;
    background: rgba(0,0,0,0.4);
    border-radius: 4px;
    position: absolute;
    left: 10px;
    top: 10px;
    text-align: center;
    line-height: 24px;
  }

  .sptext {
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    line-height: 14px;
    padding-right: 8px;
  }

  .jianjiecss {
    margin-top: 7px;
    height: 23px;
    font-size: 13px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #444444;
    line-height: 23px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .zantext {
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    line-height: 16px;
    color: #333333;
    position: absolute;
    top: 10px;
    right: 12px
  }

  .zan_sel {
    color: #FFA44C;
  }

  .iconimg {
    width: 16px;
    height: 16px;
    opacity: 1;
    margin-top: -3px;
  }

  .zanimg {
    width: 16px;
    height: 16px;
    opacity: 1;
    margin-top: -3px;
    margin-right: 4px;
  }

  .namecss {
    padding-left: 66px;
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 32px;
  }

  .condiv {
    position: relative;
    padding-bottom: 6px;
  }

  .headimg {
    position: absolute;
    top: -18px;
    left: 10px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    opacity: 1;
    border: 2px solid #FFFFFF;
  }

  .mb12 {
    margin-bottom: 20px;
  }

  .fengmian {
    width: 100%;
    border-radius: 8px 8px 0px 0px;
    aspect-ratio: 4 / 3;
  }

  .card {
    width: 100%;
    background-color: white;
    border-radius: 8px;
  }

  .rlist {
    width: 30%;
    height: 100%;
    min-width: 300px;
    border-radius: 8px;
  }

  .leftlist {
    margin-right: 12px;
    width:70%;
    height: 100%;
    background: #FFFFFF;
    padding: 20px;
    border-radius: 8px;
  }

  .concss {
    display: flex;
    height: 100%;
    border-radius: 8px 8px 8px 8px;
  }

  .mt {
    margin-top: 0px;
  }

  .ml10 {
    margin-left: 20px;
  }

  .rheadcss {
    height: 38px;
    line-height: 38px;
    width: 15vw;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .header {
    width: 100%;
    padding-bottom:16px;
    display: flex;
  }

  .myform /deep/.el-form-item {
    margin-bottom: 10px !important;
    padding-right: 1vw;
  }

  .myform /deep/.el-form-item .el-input__inner {
    width: 100% !important;
  }

  .bg {
    height: calc(100% - 40px);
  }

  .llist {
    /* height:  */
  }
</style>
<style>

</style>
