import { login, logout, getTitle } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'
import permission from './permission'
import md5 from 'md5'
import { mapActions, mapMutations } from 'vuex'
const getDefaultState = () => {
  return {
    token: getToken(),
    avatar: '',
    username: '',
    userId: '',
    title: ''
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_USER: (state, { username, userId }) => {
    state.username = username
    state.userId = userId
    localStorage.setItem('userId', userId)
    localStorage.setItem('username', username)
  },
  SET_TITLE: (state, title) => {
    state.title = title
  }
}

const actions = {
  // user login

  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: md5(md5(password)) }).then(response => {
        const { data } = response
        commit('SET_TOKEN', data.token)
        commit('SET_USER', {
          username: data.accountName,
          userId: data.id
        })
        setToken(data.token)
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getTitle({ commit }) {
    return new Promise((resolve, reject) => {
      getTitle({ code: 'DEFAULT_PLANTFORM_NAME' }).then(res => {
        commit('SET_TITLE', res.data)
        sessionStorage.setItem('title', res.data)
        resolve(res.data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  // user logout
  logout({ commit }) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        removeToken() // must remove  token  first
        resetRouter()
        commit('RESET_STATE')
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

