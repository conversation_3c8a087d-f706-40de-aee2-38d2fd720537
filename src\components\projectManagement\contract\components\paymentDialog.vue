<template>
  <el-dialog
    :title="isEdit ? '编辑回款' : '回款'"
    class="adddetailcss"
    width="60%"
    :visible.sync="addVisible"
    :before-close="beforeClose"
    append-to-body
    center
  >
    <el-form
      ref="addform"
      class="refundcss myform"
      :model="form"
      :rules="rules"
      label-width="130px"
    >
      <el-row
        class="mb30 bbline"
        type="flex"
        justify="space-around"
        :gutter="0"
      >
        <el-col :span="12">
          <el-form-item label="回款金额：" prop="returnAmount">
            <el-input
              class="definput"
              type="number"
              v-model="form.returnAmount"
              placeholder="请输入回款金额(单位:万元)"
            ></el-input>
          </el-form-item>
          <el-form-item label="实际回款日期：" prop="actualReturnDay">
            <el-date-picker
              class="definput w100"
              v-model="form.actualReturnDay"
              type="datetime"
              placeholder="请选择实际回款日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="是否预收：" prop="isAdvance">
            <el-select
              class="definput"
              popper-class="removescrollbar"
              v-model="form.isAdvance"
              placeholder="请选择是否预收"
            >
              <el-option
                v-for="item in types"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="币种：" prop="currency">
            <el-select
              class="definput"
              popper-class="removescrollbar"
              v-model="form.currency"
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="付款方式：" prop="paymentWay">
            <el-select
              class="definput"
              popper-class="removescrollbar"
              v-model="form.paymentWay"
              placeholder="请选择"
            >
              <el-option
                v-for="item in paymentWays"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="0">
        <el-col :span="12">
          <el-form-item label="收款账户：" prop="collectionAccount">
            <el-input
              class="definput"
              v-model="form.collectionAccount"
              placeholder="请输入收款账户"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="交易流水号：" prop="transactionNumber">
            <el-input
              class="definput"
              v-model="form.transactionNumber"
              placeholder="请输入交易流水号"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="center">
      <el-button
        class="submitbtn defaultbtn"
        type="primary"
        @click="submitAction"
        :v-loading="isSubmit"
        >提交</el-button
      >
    </div>
    <verifyDeparment ref="verifyDeparment" @submit="addData"></verifyDeparment>
  </el-dialog>
</template>
<script>
import {
  addContractreturn,
  contractreturnInfo,
  updateContractreturn,
} from '@/api/contract/index'
import { getDict } from '@/utils/tools'
import verifyDeparment from '@/components/common/verifyDeparment.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    contractId: {
      type: String,
      default: '',
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    verifyDeparment,
  },
  data() {
    return {
      rules: {
        returnAmount: [
          {
            required: true,
            message: '请输入回款金额（万元）',
            trigger: 'blur',
          },
        ],
        actualReturnDay: [
          { required: true, message: '请输入实际回款日期', trigger: 'blur' },
        ],
        // transactionNumber: [
        //     { required: true, message: '请输入交易流水号', trigger: 'blur' },
        // ],
        isAdvance: [
          { required: true, message: '请选择是否预收', trigger: 'change' },
        ],
      },
      form: {
        contractId: this.contractId,
        returnAmount: '',
        currency: '',
        actualReturnDay: '',
        paymentWay: '',
        isAdvance: '',
        collectionAccount: '',
        transactionNumber: '',
        returnDepartmentId: '',
      },
      isSubmit: false,
      options: [
        {
          id: '1',
          name: '人民币',
        },
      ],
      types: [
        {
          id: 1,
          name: '是',
        },
        {
          id: 2,
          name: '否',
        },
      ],
      paymentWays: [
        {
          id: 1,
          name: '现金',
        },
        {
          id: 2,
          name: '银行转账',
        },
        {
          id: 3,
          name: '微信转账',
        },
        {
          id: 4,
          name: '支付宝转账',
        },
        {
          id: 5,
          name: '其他',
        },
      ],
    }
  },
  computed: {
    addVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('updateVisible', val)
      },
    },
  },
  created() {
    getDict('Currency')
      .then((result) => {
        this.options = result
      })
      .catch((err) => {})
  },
  methods: {
    loadDetailData(id) {
      contractreturnInfo(id).then((res) => {
        if (res.status == 0) {
          this.form = { ...res.data }
          this.form.currency = res.data.currency === 0 ? undefined : res.data.currency
          this.form.paymentWay = res.data.paymentWay === 0 ? undefined : res.data.paymentWay;
        }
      })
    },
    submitAction() {
      this.$refs['addform'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.isEdit) {
          this.updateData()
        } else {
          this.$refs.verifyDeparment.verify()
        }
      })
    },
    beforeClose() {
      this.addVisible = false
      var keys = Object.keys(this.form)
      this.$refs['addform'].resetFields()
      keys.forEach((element) => {
        if (element != 'contractId') {
          this.form[element] = ''
        }
      })
    },
    addData(departmentId) {
      this.form.returnDepartmentId = departmentId
      this.isSubmit = true
      addContractreturn(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '添加成功',
            })
            this.beforeClose()
            this.$emit('submitSuccess')
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
    updateData() {
      this.isSubmit = true
      updateContractreturn(this.form)
        .then((result) => {
          if (result.data) {
            this.$message({
              type: 'success',
              message: '修改成功',
            })
            this.beforeClose()
            this.$emit('submitSuccess')
          } else {
            this.$message({
              type: 'error',
              message: result.msg,
            })
          }
          this.isSubmit = false
        })
        .catch((err) => {
          this.isSubmit = false
        })
    },
  },
}
</script>

<style scoped>
.w100 {
  width: 100% !important;
}
.center {
  text-align: center;
}
.submitbtn {
  margin-top: 100px;
}
.refundcss /deep/.el-form-item {
  margin-bottom: 18px;
}
.refundcss {
  padding-right: 4vw;
  padding-left: 2vw;
}
</style>
