<template>
  <div class="p20">
    <back :type="1" @goback="goback">日报详情</back>
    <div class="maindiv">
      <div class="owntop " :class="{ w60: isShow }" >
        <div class="w800" :class="{ wshow: isShow }">
            <listitem @showCustomer="showCustomer" @showRead="showRead" :isShowButton="false"  ref="ListRef" @loadData="loadData"    :item="itemObj">
              <p class="linemar">当日工作回顾:</p>
              <el-row :gutter="20">
                <el-col :span="8"  v-for="(item,index) in list" :key="index">
                  <dataitem :item="item"></dataitem>
                </el-col>
              </el-row>
            </listitem>
        </div>
      </div>
      <div class="rightdiv " :class="{ w40: isShow }">
        <div v-if="isShow" class="reviewmask">
          <planreview ref="planreview" v-if="reviewType == 'planreview'" @closereview="closereview"
            :plantype="form.goalsType" :viewData="viewData" :page="pageData" :date="planTime">
          </planreview>
          <summaryreview v-if="reviewType == 'summaryreview'" @closereview="closereview"
            :plantype="form.goalsType" :viewData="viewData" :page="pageData" :date="planTime">
          </summaryreview>
        </div>
        <div v-else class="tright">
          <div class="bbtn" id="planreview" @click="showView" >
            <i class="el-icon-notebook-2" alt="" />
            计划回顾
          </div>
          <div class="bbtn" id="summaryreview" @click="showView">
            <i class="el-icon-notebook-2" alt="" />
            总结回顾
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="选择回顾时间" :visible.sync="dialogPlanVisible" append-to-body width="340px" center :before-close="handlePlanClose">
      <span>
        <el-form class="mt30" label-width="85px">
          <el-form-item :label="
          `${reviewType == 'summaryreview' ?'总结':'计划'}类型：`">
            <el-select class="definput width200"  v-model="form.goalsType" placeholder="请选择" @change="changeType">
            <el-option
              v-for="item in options[reviewType]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          </el-form-item>
          <el-form-item v-if="form.goalsType" :label="
              `${
                types[form.goalsType].label
              }：`
            ">
            <el-date-picker class="definput width200" :picker-options="pickerOptions" @change="changeDatePicker"
              v-model="planTime" :type="types[form.goalsType].type" :format="types[form.goalsType].format"
              placeholder="请选择">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="nextAction">下一步</el-button>
      </span>
    </el-dialog>
    <customerMsg ref="customerMsg" :visible.sync="drawerVisible" @closeCus="closeCus"></customerMsg>
    <readRecord :notReadLogs="notReadLogs" :readLogs="readLogs" ref="customerMsg" :visible.sync="drawerVisibleRead" @closeRead="closeRead"></readRecord>
  </div>
</template>

<script>
  import back from "@/components/common/back.vue";
  import planreview from "@/components/common/planreview.vue";
  import summaryreview from "@/components/common/summaryreview.vue";
  import { getWeek } from "@/utils/index";
  import listitem from './listitem.vue';
  import dataitem from './dataitem.vue';
  import {
    querPersonalGoalsVoInfo,
    queryDailyReview
  } from "@/api/daily";
  import customerMsg  from "../common/customerMsg.vue";
  import readRecord  from "../common/readRecord.vue";
  import {
    queryReviewGoals,
  } from "@/api/goal";
  export default {
    components: {
      back,
      planreview,
      summaryreview,
      listitem,
      dataitem,
      customerMsg,
      readRecord
    },
    data() {
      return {
        drawerVisibleRead:false,
        drawerVisible:false,
        list:[
              {id:1,name:"拜访客户",finishGoal:0,icon:require('../../../assets/img2.png'),unit:"次",color:"#4A8BF6",  finishRate:0},
              {id:2,name:"新增客户",finishGoal:0,icon:require('../../../assets/img5.png'),unit:"个",color:"#F46D40",finishRate:0},
              {id:3,name:"样书发放",finishGoal:0,icon:require('../../../assets/yang.png'),unit:"次",color:"#56C36E",finishRate:0},
              {id:4,name:"新增合同",finishGoal:0,icon:require('../../../assets/img/hetong_icon.png'),unit:"个",color:"#FF8D1A",finishRate:0},
              {id:5,name:"回款金额",finishGoal:0,icon:require('../../../assets/img4.png'),unit:"万元",color:"#4A8BF6",finishRate:0},
              {id:6,name:"业绩",finishGoal:0,icon:require('../../../assets/img3.png'),unit:"万元",color:"#E85D5D",finishRate:0},
            ],
        itemObj:{},
        pickerOptions: {
          disabledDate: this.endPickerTime
        },
        isShow: false,
        isLoading: false,
        dialogPlanVisible: false,
        year: new Date(this.$route.query.time).getFullYear(),
        planTime: "",
        options:{
          'planreview':[
            {label:'年计划',value:'1'},
            {label:'月计划',value:'2'},
            {label:'周计划',value:'3'}
          ],
          'summaryreview':[
            {label:'年总结',value:'4'},
            {label:'月总结',value:'5'},
            {label:'周总结',value:'6'}
          ]
        },
        types: {
          "1": {
            name: "年计划",
            label: "年度计划",
            type: "year",
            format: "yyyy"
          },
          "2": {
            name: "计划",
            label: "度计划",
            type: "month",
            format: "yyyy-M"
          },
          "3": {
            name: "计划",
            label: "计划",
            type: "week",
            format: "yyyy 第 W 周"
          },
          "4": {
            name: "年总结",
            label: "年度总结",
            type: "year",
            format: "yyyy"
          },
          "5": {
            name: "总结",
            label: "度总结",
            type: "month",
            format: "yyyy-M"
          },
          "6": {
            name: "总结",
            label: "总结",
            type: "week",
            format: "yyyy 第 W 周"
          }
        },
        form: {
          goalsType: '',
          year: "",
          month: "",
          week: "",
          pageSize:1,
          pageNum:1
        },
        reviewType: "",
        labelPosition: "right",
        viewData: {},
        pageData:{},
        readLogs:[],
        notReadLogs:[],
        toObj:{
          0:{
            finishGoal:'visit'
          },
          1:{
            finishGoal:'customer'
          },
          2:{
            finishGoal:'deliverNum'
          },
          3:{
            finishGoal:'contract'
          },
          4:{
            finishGoal:'returnAmount'
          },
          5:{
            finishGoal:'contractAmount'
          },
       },
      };
    },
    created() {
    },
    methods: {
      toReview(){
       
    },
      showCustomer(customerId){
        this.drawerVisible = true;
        this.$nextTick(()=>{
          this.$refs.customerMsg.loadDataInfo(customerId);
        })
      },
      showRead(item){
        this.readLogs = item.readLogs
        this.notReadLogs = item.notReadLogs
        this.drawerVisibleRead = true
      },
      closeRead(){
          this.drawerVisibleRead = false
      },
      closeCus(){
        this.drawerVisible = false;
      },
      loadData(){
        this.getData(1)
      },
      goback() {
        this.$emit('closeBack')
        this.isShow = false;
      },
      loadReviewData() {
        this.form.createBy = this.itemObj.createBy
        queryReviewGoals(this.form).then((result) => {
          if (result.data && result.data.length>0) {
            this.viewData = result.data;
            this.pageData = result.page;
            this.isShow = true;
            this.dialogPlanVisible = false;
          } else {
            this.$message({
              type: "error",
              message: '暂无相关回顾可查看'
            });
          }
        }).catch((err) => {

        });
      },
      endPickerTime(time) {
        if (this.reviewType == "planreview") {
          return false;
        }
        const today = new Date();
        return time > today;
      },
      changeDatePicker(date) {
        var newDate = new Date(date);
        this.form.year = newDate.getFullYear();
        if (this.form.goalsType == 2 || this.form.goalsType == 5) {
          this.form.month = newDate.getMonth() + 1;
        } else if (this.form.goalsType == 3 || this.form.goalsType == 6) {
          var time = getWeek(newDate);
          var times = time.split("-");
          this.form.year = times[0];
          this.form.week = times[1];
        }
      },
      changeType(){
        if (this.planTime) {
          this.planTime = ''
        }
      },
      handlePlanClose() {
        this.dialogPlanVisible = false;
        this.planTime = "";
        this.form.goalsType = ''
      },
      nextAction() {
        if (!this.form.goalsType) {
            this.$message({
              type: "info",
              message: `请选择${this.reviewType == 'planreview' ? '计划类型' : '总结类型'}`
            });
            return 
          }
        if (this.planTime) {
          
          if (this.reviewType == 'planreview') {
              var fdata = Object.assign({},this.form)
              this.loadReviewData(fdata);
            }else{
              this.loadReviewData(this.form);
            }
        } else {
          this.$message({
            type: "info",
            message: `请选择${this.types[this.form.goalsType].label}`
          });
        }
      },
      closereview() {
        this.isShow = false;
        this.reviewType = "";
        this.planTime = "";
        this.form.goalsType = ''
      },
      loadInfo(item) {
        querPersonalGoalsVoInfo({ id:item.id })
          .then(result => {
            this.itemObj = result.data;
          })
          .catch(err => { });
          queryDailyReview({
            userId:item.createBy,
            time:item.createTime,
          }).then(res=>{
            if(res.status == 0){
                  let resultData = res.data
                  this.list.forEach((item,index) => {
                    item.finishGoal = resultData.dailyReviewVo[this.toObj[index]['finishGoal']];
                  });
            }else{
                this.$message({
                  type: "error",
                  message: result.msg
                })
            }
          })
      },
      showView(e) {
        this.planTime = "";
        this.dialogPlanVisible = true;
        this.reviewType = e.target.id;
      },
    }
  };
</script>

<style lang="scss" scoped>
    .reviewmask{
      overflow-y: auto;
      height: 100%;
    }
    .w800{
      width: 740px;
      margin: 0 auto;
    }
    .wshow{
      width: 100%;
    }
    .linemar{
      margin: 16px 0 8px 0;
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
    }
  .ptitle{
    font-weight: 400;
    font-size: 20px;
    color: #333333;
    text-align: center;
    margin-bottom: 10px;
  }
  .closediv{
      height: 32px;
      padding-top: 10px;
      margin-right: 10px;
  }
  .closebtn{
    width: 24px;
    height: 24px;
    cursor: pointer;
    float: right;
}
  .dailylist {
    padding-left: 10px;
  }

  .eltab /deep/.el-tabs__item {
    width: 120px;
    text-align: center;
  }

  .rili {
    height: 36px;
    line-height: 36px;
    margin: 10px 0;
    position: relative;
    padding: 0 4px;
    cursor: pointer;
  }

  .p20 {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .adddi {
    width: 44px;
    height: 44px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #D6D6D6;
    text-align: center;
    line-height: 44px;
    font-size: 18px;
    color: #D6D6D6;
    cursor: pointer;
  }

  .zhu {
    font-family: SourceHanSansSC;
    font-weight: 300;
    font-size: 14px;
    color: rgb(173, 173, 173);
  }

  .bposi {
    position: relative;
    height: 34px;
    margin-bottom: 10px;
  }

  .borderdi {
    border: 1px solid #DCDFE6;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
  }

  .mr {
    margin-left: 20px;
  }

  .upload-demo {
    margin-top: 20px;
  }

  .pd20 {
    padding: 20px 0;
  }

  .tright {
    text-align: center;
  }

  .maindiv {
    display: flex;
    padding-top: 20px;
    flex: 1;
  }

  .rightdiv {
    width: 20%;
    margin-left: 20px;
    border-radius: 10px;
    height: calc(100vh - 136px);
  }

  .w40 {
    width: 50%;
    background-color: white;
    flex: 1;
  }

  .submitmask {
    width: 100%;
    text-align: center;
    margin-top: 20px;
  }

  .textAlign /deep/.el-input__inner {
    text-align: center !important;
  }

  .flexcss {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .textAlignRight {
    text-align: right;
  }

  .width200 {
    width: 200px;
  }

  .definput /deep/.el-input-group__append {
    background-color: #f6f7fb !important;
    color: #333333 !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
  }

  .rtextcss {
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    color: #999999;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
  }

  .owntable /deep/ td,
  th {
    padding: 8px 0 !important;
  }

  .owntable /deep/ th {
    background-color: #f6f7fb !important;
  }

  .owntable /deep/ .cell {
    font-size: 14px !important;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400 !important;
    color: #333333 !important;
  }

  .mubiao {
    background: #fff;
    margin-top: 10px;
    padding: 24px 20px;
    min-height: 500px;
    box-sizing: border-box;
  }

  .inputcss {
    height: 36px !important;
  }

  .elform /deep/ .el-input__inner {
    line-height: 36px !important;
    height: 36px;
  }

  .deleteimg {
    position: absolute;
    right: 12px;
    bottom: 12px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .tablebox {
    height: 337px;
    overflow-x: hidden;
  }

  .asbu {
    position: absolute;
    right: 0;
    top: 0;
  }

  .rela {
    position: relative;
    margin-bottom: 24px;
  }

  .elform /deep/.el-input-group__append {
    padding: 0 12px !important;
  }

  .mt {
    margin-top: 20px;
  }

  .elform {
    width: 100%;
  }

  .elform .el-form-item {
    margin-bottom: 20px !important;
  }

  .bbtn {
    height: 48px;
    line-height: 48px;
    background-color: #dfeafd;
    font-size: 16px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #4285f4;
    letter-spacing: 1px;
    border-radius: 4px;
    cursor: pointer;
  }

  .bbtn:hover {
    background-color: #4285f4;
    color: white !important;
  }

  .bbtn+.bbtn {
    margin-top: 10px;
  }

  .owntop {
    width: 80%;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    overflow-y: auto;
    height: calc(100vh - 136px);
  }

  .w60 {
    width: 50%;
  }

  .personalplan {
    background: #fff;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    width: 800px;
    margin: 0 auto;
  }

  .unitplan {
    flex: 1;
    background: #fff;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
  }

  .myform /deep/.el-form-item__label {
    padding: 0 !important;
    line-height: 36px;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
  }
</style>