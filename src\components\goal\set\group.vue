<template>
  <div class="bg">
    <div class="orgcss">
      <el-tree node-key="id" :default-expand-all="true" class="filter-tree  cfd cf1" :data="tagTypeData" :props="defaultProps" ref="tagTypeTree"
            :highlight-current="true" :expand-on-click-node="false" 
            @node-click="getTreeNode">
          </el-tree>
    </div>
    <div class="comcss">
      <div>
        <span class="tagcss" :class="{'tagsel':activeName == 'bumen'}" @click="clickAction('bumen')">部门目标</span>
        <span class="tagcss" :class="{'tagsel':activeName == 'kehu'}" @click="clickAction('kehu')">客户目标</span>
      </div>
       <bumenGoal ref="bumen" :bumenObj="bumenObj" v-show="activeName == 'bumen'"></bumenGoal>
       <div v-show="activeName == 'kehu'" class="kehudiv">
        <kehuGoal ref="kehu" :kehuObj="kehuObj"></kehuGoal>
       </div>
       
    </div>
  </div>
</template>

<script>
import bumenGoal from './bumenGoal.vue';
import kehuGoal from './kehuGoal.vue';
import {treeDepartmentList} from '@/api/framework/user'
import {goalSetView,departmentGoals} from '@/api/goal/index'
export default {
  components:{
    bumenGoal,
    kehuGoal
  },
    data(){
        return{
          tagTypeData:[],
            activeName:'bumen',
            defaultProps: {
              children: 'children',
              label: 'name',
            },
            params:{
              departmentId:''
            },
            kehuObj:{},
            bumenObj:{},
        }
    },
    created(){
      this.treeDepartmentListData()
    },
    methods:{
      treeDepartmentListData(){
        treeDepartmentList().then(res=>{
          if(res.status ==0){
              this.tagTypeData = res.data
              if (this.tagTypeData.length > 0) {
                this.currentNodekey = this.tagTypeData[0].id;
                this.$nextTick(() => {
                  this.$refs.tagTypeTree.setCurrentKey(this.currentNodekey); //一定要加这个选中了否则样式没有出来
                });
                this.params.departmentId =  this.currentNodekey
                this.goalSetViewData()
              }
          }
        })
      },
      clickAction(name){
        this.activeName = name;
      },
      getTreeNode(data) {
        this.params.departmentId = data.id
        this.goalSetViewData()
      },
      goalSetViewData(){
        goalSetView(this.params).then(res=>{
            this.kehuObj = res.data
            this.$nextTick(()=>{
              this.$refs.kehu.handleData()
            })
        })
        departmentGoals(this.params).then(res=>{
            this.bumenObj = res.data
            this.$nextTick(()=>{
              this.$refs.bumen.handleData()
            })
        })
      }
    }
}
</script>

<style scoped lang="scss">
.kehudiv{
  height: calc(100% - 20px);
  overflow-y: scroll;
}
/deep/ .el-tree{
  display: inline-block;
  min-width: 100%;
  padding-right: 10px;
}
.tagcss{  
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #666666;
  margin-right: 52px;
  cursor: pointer;
}
.tagsel{
  color: #4285F4;
}
.ptop{
  padding-top: 10px;
}
.workercheckbox{
  padding-top: 10px;
}
.workercheckbox /deep/.el-checkbox__label{
  line-height: 32px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  min-width: 65px;
}
.unittree /deep/.el-dialog__body{
  padding: 0 !important;
}
.unittree /deep/.el-tree-node__label{
  font-size: 12px !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 14px;
}
.unittree /deep/.el-tree-node__expand-icon{
  color: #333333;
}
.unittree /deep/.el-tree-node__expand-icon.is-leaf{
  color: transparent;
}
.comcss{
    position: absolute;
    left: 282px;
    top: 0px;
    right: 0px;
    bottom: 0px;
    background-color: white;
    border-radius: 8px;
    padding: 20px;
}

.orgcss{
    position: absolute;
    left: 0px;
    top: 0px;
    width: 272px;
    bottom: 0px;
    background-color: white;
    border-radius: 8px;
    overflow: auto;
    padding: 10px 15px;
    overflow-x: scroll;
}
.titlecss{
    font-size: 20px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    margin-bottom: 20px;
}
.bg{
    position: relative;
    height: 100%;
    /* background-color: red; */
}
</style>