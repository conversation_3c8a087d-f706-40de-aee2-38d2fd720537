<template>
  <div>
    <div>
      <back>客户详情</back>
    </div>
    <div class="card">
      <p class="titletext">数据概览</p>

      <span class="zhiding">
        <img
          class="zhiimg"
          :src="isTop == 1 ? zdimg_sel : zdimg"
          @click="updateTopStatus"
        />
        置顶
      </span>

      <el-row :gutter="20" class="cardcss">
        <el-col :span="5" v-for="(item, index) in dataViewList" :key="index">
          <div class="piececss">
            <img class="imgf left" :src="item.imgUrl" alt="" />
            <div>
              <p
                class="numcolor"
                :class="[`color-${index}`]"
                v-if="item.text === '合同总额'"
              >
                {{ item.numText }}<span class="ml5">万元</span>
              </p>
              <p class="numcolor" :class="[`color-${index}`]" v-else>
                {{ item.numText }}
              </p>
              <p class="numtext">{{ item.text }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
        stretch
        class="tabscss"
      >
        <el-tab-pane label="基本信息" name="first">
          <bacisInfo ref="cusinfo"></bacisInfo>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:visit:list')"
          label="跟进与拜访"
          name="second"
        >
          <followvisit ref="cusvisit"></followvisit>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:opportunity:list')"
          label="销售机会"
          name="third"
        >
          <opportunity ref="cusopportunity"></opportunity>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:contract:list')"
          label="合同"
          name="fourth"
        >
          <contract ref="cuscontract"></contract>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:project:list')"
          label="项目"
          name="five"
        >
          <projectView ref="cusproject"></projectView>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:distribution:list')"
          label="样书发放"
          name="seven"
        >
          <records ref="cusdistribution"></records>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkPermission('crm:controller:distribution:list')"
          label="礼品发放"
          name="six"
        >
          <recordsli ref="cusdistributionli"></recordsli>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import back from '../../common/back.vue'
import bacisInfo from './basicInfo.vue'
import opportunity from './opportunity.vue'
import followvisit from './followvisit.vue'
import contract from './contract.vue'
import projectView from './project.vue'
import records from './records.vue'
import recordsli from './recordsli.vue'
import {
  updateCustomer,
  queryCustomerOverview,
} from '@/api/clientmanagement/customer'

export default {
  computed: {
    ...mapGetters(['isTop']),
  },
  components: {
    back,
    followvisit,
    opportunity,
    contract,
    projectView,
    records,
    bacisInfo,
    recordsli,
  },
  data() {
    return {
      activeName: 'first',
      refData: {
        first: 'cusinfo',
        second: 'cusvisit',
        third: 'cusopportunity',
        fourth: 'cuscontract',
        five: 'cusproject',
        seven: 'cusdistribution',
        six: 'cusdistributionli',
      },
      unitId: this.$route.query.id,
      zdimg: require('../../../assets/img/zhidingd.png'),
      zdimg_sel: require('../../../assets/img/zhiding_icon.png'),
      dataViewList: [
        {
          imgUrl: require('../../../assets/img2.png'),
          isGo: this.checkPermission('crm:controller:visit:list'),
          numText: '0',
          text: '跟进与拜访',
        },
        {
          imgUrl: require('../../../assets/xiaoshou.png'),
          isGo: this.checkPermission('crm:controller:opportunity:list'),
          numText: '0',
          text: '销售机会',
        },
        {
          imgUrl: require('../../../assets/img/project_icon.png'),
          isGo: this.checkPermission('crm:controller:project:list'),
          numText: '0',
          text: '项目',
        },
        {
          imgUrl: require('../../../assets/img/hetong_icon.png'),
          isGo: this.checkPermission('crm:business:contract:list'),
          numText: '0',
          text: '合同',
        },
        {
          imgUrl: require('../../../assets/img3.png'),
          numText: '0',
          isGo: false,
          text: '合同总额',
        },
      ],
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData() {
      queryCustomerOverview({ id: this.$route.query.id })
        .then((result) => {
          console.log('')
          this.splitData(result.data)
        })
        .catch((err) => {})
    },
    splitData(data) {
      this.dataViewList.forEach((element) => {
        switch (element.text) {
          case '跟进与拜访':
            element.numText = data.visitNumber
            break
          case '销售机会':
            element.numText = data.opportunityNum
            break
          case '项目':
            element.numText = data.projectNumber
            break
          case '合同':
            element.numText = data.contractNumber
            break
          case '合同总额':
            element.numText = data.contractTotalAmount
            break

          default:
            break
        }
      })
    },
    updateTopStatus() {
      var par = {
        id: this.$route.query.id,
        top: this.isTop == 0 ? 1 : 0,
      }
      updateCustomer(par)
        .then((result) => {
          if (result.data) {
            this.$store.commit('cus/CHANGE_KEY', {
              key: 'isTop',
              value: par.top,
            })
          }
        })
        .catch((err) => {})
    },
    handleClick(tab, event) {
      var tabName = tab.name
      var refName = this.refData[tabName]
      this.$refs[refName].loadData()
    },
    goPage(item) {
      var path = ''
      switch (item.text) {
        case '跟进与拜访':
          path = '/clientMaintenance/followVisit/index'
          break
        case '销售机会':
          path = '/clientMaintenance/salesLead/index'
          break
        case '项目':
          path = '/clientManagement/project/index'
          break
        case '合同':
          path = '/projectManagement/contract/index'
          break

        default:
          break
      }
      if (path.length > 0) {
        this.$router.push({
          path: path,
          query: { customerId: this.unitId }, // 单位id
        })
      }
    },
    onSubmit() {
      console.log('submit!')
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
  },
}
</script>
<style  scoped>
.ml5 {
  margin-left: 5px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.zhiding {
  position: absolute;
  top: 20px;
  right: 30px;
  line-height: 24px;
}
.zhiimg {
  width: 24px;
  height: 24px;
  position: relative;
  top: -3px;
  cursor: pointer;
}

.dianbtn {
  position: absolute;
  right: 16px;
  top: 10px;
  width: 24px;
  height: 24px;
  color: #666666;
  letter-spacing: 3px;
  font-size: 18px;
  font-weight: bold;
  line-height: 24px;
  cursor: pointer;
}

.tabscss /deep/.el-tabs__item {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 56px;
  line-height: 56px;
  /* padding: 0 60px; */
}

.tabscss /deep/.el-tabs__header {
  margin-bottom: 30px !important;
}

.cardcss {
  margin-bottom: 10px;
}

.el-col-5 {
  width: 20%;
}

.labeltext label {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
</style>

<style lang="scss" scoped>
.color-0 {
  color: #4a8bf6;
}

.color-1 {
  color: #56c36e;
}

.color-2 {
  color: #4a8bf6;
}

.color-3 {
  color: #4a8bf6;
}

.color-4 {
  color: #e85d5d;
}

.titletext {
  margin-bottom: 30px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.numtext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}

.numcolor {
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
}

.imgf {
  margin-left: 30px;
  margin-right: 20px;
  width: 48px;
  height: 48px;
}

.piececss {
  position: relative;
  height: 148px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  width: 100%;
  padding: 50px 0;
  box-sizing: border-box;
  box-shadow: 0px 2px 16px 0px rgba(15, 27, 50, 0.08);
}

.savecss {
  display: block;
  margin: 0 auto;
}

.mt20 {
  margin-top: 20px;
  width: 100%;
}

.card {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
  position: relative;
}
</style>
