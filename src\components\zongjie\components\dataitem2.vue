<template>
    <div class="dataitem">
        <div class="typename">{{item.name}}({{item.unit}})</div>
        <div class="flex">
        <img class="imgcss" :src="item.icon" alt="">
        <div class="pl12">
            <div ><span class="goalnumcss">目标：</span><span class="curnumcss">{{item.goal}}</span></div>
            <div ><span class="goalnumcss">达成：</span><span class="curnumcss">{{item.finishGoal}}</span></div>
        </div>
        </div>
    </div>
</template>

<script>
export default {
    props:{
        item:{
            type:Object,
            default:()=>{}
        }
    }
}
</script>

<style scoped>
.ml{
  margin-left: 3px;
}
.goalnumcss{
  height: 21px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 0.75em;
  color: #999999;
  line-height: 21px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.curnumcss{
  height: 23px;
  font-family: <PERSON><PERSON>, Roboto;
  font-weight: bold;
  font-size: 1em;
  color: #333333;
  line-height: 23px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.pl12{
  padding-left: 8px;
}
.flex{
  margin-top: 20px;
  display: flex;
  align-items: center;
}
.imgcss{
  width: 2.5em;
  height: 2.5em;
}
.typename{
  height: 21px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 1em;
  color: #999999;
  line-height: 19px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.dataitem{
  padding: 16px;
  margin-top: 16px;
  height: 9.375em;
  background: #FFFFFF;
  box-shadow: 0px 2px 16px 0px rgba(15,27,50,0.08);
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}
</style>