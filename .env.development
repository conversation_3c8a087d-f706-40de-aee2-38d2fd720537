# just a flag
ENV = 'development'
# VUE_APP_BASE_API = 'http://192.168.0.85:9052/api'
#VUE_APP_BASE_API = 'http://api.platform.subanyun.com/'
# VUE_APP_BASE_API = 'http://192.168.0.85:9052/api'
 VUE_APP_BASE_API = 'http://192.168.10.109:9051'
# VUE_APP_BASE_API = 'http://192.168.10.187:9052/api'
#   VUE_APP_BASE_API = 'http://118.190.88.122:9052/api'
# VUE_APP_BASE_API = 'http://118.190.88.169:9052/api'
#VUE_APP_BASE_API = 'http://platform.maisuiedu.com/api/'
#VUE_APP_BASE_API = 'http://192.168.1.6:9014/'
#VUE_APP_BASE_API = 'http://192.168.3.199:9000/'
# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true 