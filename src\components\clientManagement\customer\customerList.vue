<template>
  <div class="mainbg fixpb" v-loading="isLoading">
    <el-form class="myform pr clearfix" inline>
      <el-form-item>
        <el-input
          class="definput inputWid150"
          v-model="pageBean.customerName"
          clearable
          placeholder="客户名称"
        >
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          class="definput inputWid150"
          v-model="pageBean.unitName"
          clearable
          placeholder="客户单位"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          class="definput inputWid150"
          v-model="pageBean.phone"
          clearable
          placeholder="联系方式"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          class="definput inputWid150"
          v-model="pageBean.majorNames"
          clearable
          placeholder="负责专业"
        >
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          class="definput inputWid150"
          v-model="pageBean.createByName"
          clearable
          placeholder="创建人"
        >
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          class="definput inputWid150"
          v-model="pageBean.createByDepartment"
          clearable
          placeholder="创建人部门"
        >
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          class="definput inputWid150"
          v-model="pageBean.chargePersonName"
          clearable
          placeholder="负责人"
        >
        </el-input> </el-form-item
      ><el-form-item>
        <el-input
          class="definput inputWid150"
          v-model="pageBean.chargePersonDepartment"
          clearable
          placeholder="负责人部门"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="客户级别：">
        <el-select
          class="definput inputWid150"
          popper-class="removescrollbar"
          clearable
          v-model="pageBean.customerLevel"
          placeholder="请选择"
        >
          <el-option key="" label="全部" value=""> </el-option>
          <el-option
            v-for="item in levels"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="近期跟进：">
        <el-select
          class="definput width"
          popper-class="removescrollbar"
          clearable
          v-model="pageBean.isFollow"
          placeholder="请选择"
        >
          <el-option key="" label="全部" value=""> </el-option>
          <el-option key="1" label="近期曾跟进" value="1"> </el-option>
          <el-option key="0" label="近期未跟进" value="0"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间：">
        <datepicker @submitAction="submitAction"></datepicker>
      </el-form-item>
      <el-form-item>
        <el-button
          class="defaultbtn mt"
          icon="el-icon-search"
          type="primary"
          @click="searchAction"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item class="defr m0">
        <el-button
          v-isShow="'crm:controller:customer:save'"
          class="defaultbtn"
          icon="el-icon-plus"
          type="primary"
          @click="addCustomer"
          >新建客户</el-button
        >
      </el-form-item>
      <el-form-item class="defr">
        <el-button
          class="defaultbtn"
          icon="el-icon-my-download"
          type="primary"
          :loading="isDownload"
          @click="downloadAction"
          >下载模版</el-button
        >
      </el-form-item>
      <el-form-item class="defr">
        <el-upload
          class="ml20"
          :action="getUrl"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :on-success="handleAvatarSuccess"
          :on-error="handleError"
          :headers="headers"
          :data="fileData"
          accept=".xlsx"
        >
          <el-button
            class="defaultbtn"
            icon="el-icon-my-inport"
            type="primary"
            :loading="isImport"
            >导入客户</el-button
          >
        </el-upload>
      </el-form-item>
      <el-form-item class="defr">
        <el-button
          v-isShow="'crm:controller:customer:exportExcel'"
          class="defaultbtn"
          icon="el-icon-my-download"
          type="primary"
          :loading="isDownloadCustomer"
          @click="handleCustomerExport"
          >客户导出</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="customertable mytable tootiptable"
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column
        prop="customerName"
        label="客户名称"
        align="center"
        width="150px"
      >
        <template slot-scope="scope">
          <div class="cusnamecss">
            <div class="fxcenter" v-if="scope.row.isFollow">
              <div class="tags">
                <div v-if="scope.row.isFollow" class="tagcss genjin">
                  <span class="smtext">跟进</span>
                </div>
              </div>
            </div>
            <div class="namecss">
              <img
                v-if="scope.row.top"
                class="zhiding"
                src="../../../assets/img/zhiding_icon.png"
                alt=""
              />
              <span
                class="column_blue"
                v-if="scope.row.customerName.length <= 13"
                >{{ scope.row.customerName }}</span
              >
              <el-tooltip
                v-else
                class="item"
                effect="dark"
                :content="scope.row.customerName"
                placement="top-start"
              >
                <span :style="computedCss(scope.row)" class="column_blue">{{
                  scope.row.customerName
                }}</span>
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="unitName"
        label="客户单位"
        align="center"
        min-width="167px"
      >
      </el-table-column>
      <el-table-column
        prop="unitDepartment"
        label="部门"
        align="center"
        width="150px"
      >
      </el-table-column>
      <el-table-column
        prop="duties"
        label="职务"
        align="center"
        min-width="120px"
      >
      </el-table-column>
      <el-table-column
        prop="customerLevelName"
        label="客户级别"
        align="center"
        width="120px"
      >
        <template slot-scope="scope">
          <span :class="levelColor[scope.row.customerLevelName]">{{
            scope.row.customerLevelName
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="phone"
        align="center"
        label="联系电话"
        width="120px"
      >
      </el-table-column>

      <el-table-column
        align="center"
        class-name="chargePersoncss"
        min-width="200px"
        label="负责人"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.chargePersonNames.length > 0">
            <el-tag
              class="cuscss"
              v-for="(item, index) in scope.row.chargePersonNames"
              :key="index"
              >{{ item }}</el-tag
            >
          </div>
          <div v-else>—</div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        min-width="160px"
        class-name="chargePersoncss"
        label="负责人部门"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.chargePersonDepartments.length > 0">
            <span
              class="cpdepartmentcss"
              v-for="(item, index) in scope.row.chargePersonDepartments"
              :key="index"
              >{{
                index == scope.row.chargePersonDepartments.length - 1
                  ? item
                  : `${item},`
              }}</span
            >
          </div>
          <div v-else>—</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createByName"
        label="创建人"
        align="center"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="chargePersonDepartment"
        label="创建人部门"
        align="center"
        width="200px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="departcss cpdepartmentcss">
            {{ scope.row.createByDepartment }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="160"
      >
      </el-table-column>
      <el-table-column
        prop="majorNames"
        label="负责专业"
        align="center"
        width="200px"
      >
        <template slot-scope="scope">
          {{
            scope.row.specialtyName.length > 0
              ? scope.row.specialtyName.join(',')
              : '—'
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="edit"
        width="250"
        align="center"
        fixed="right"
        label="更多操作"
      >
        <template slot-scope="scope">
          <el-dropdown
            placement="bottom-end"
            @command="(e) => handleCommand(e, scope.row)"
            trigger="click"
          >
            <el-button class="bbtn mr10" type="text"> 更多 </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-isShow="'crm:controller:visit:save'"
                :command="0"
                >跟进与拜访
              </el-dropdown-item>
              <el-dropdown-item
                v-isShow="'crm:controller:customer:info'"
                :command="1"
                >查看详情
              </el-dropdown-item>
              <el-dropdown-item
                v-isShow="'crm:business:customercare:queryVoList'"
                :command="2"
                >重要日期
              </el-dropdown-item>
              <el-dropdown-item
                v-isShow="'crm:controller:distribution:save'"
                :command="3"
                >样书发放
              </el-dropdown-item>
              <el-dropdown-item
                v-isShow="'crm:controller:distribution:save'"
                :command="4"
                >礼品发放
              </el-dropdown-item>
              <el-dropdown-item
                v-isShow="'crm:controller:materialsubscription:save'"
                :command="5"
                >教材报订
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            class="bbtn"
            v-isShow="'crm:business:customerbook:list'"
            type="text"
            @click="toBook(scope.row)"
          >
            用书信息
          </el-button>
          <el-button
            v-isShow="'crm:controller:customer:update'"
            class="bbtn"
            type="text"
            @click="getIsEnable(scope.row, 'edit')"
          >
            编辑</el-button
          >
          <el-button
            v-isShow="'crm:controller:customer:delete'"
            class="rbtn"
            type="text"
            @click="getIsEnable(scope.row, 'delete')"
          >
            删除</el-button
          >
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>
    <dc-dialog
      :iType="dialogType"
      title="温馨提示"
      width="500px"
      :showCancel="isShowCancel"
      :dialogVisible.sync="dialogVisible"
      @submit="submitDialog"
    >
      <template> </template>
      <p class="pcc">{{ deletemsg }}</p>
      <!-- <p class="pcc">除，请先接触客户与单位的关系。</p> -->
    </dc-dialog>
    <dc-dialog
      iType="2"
      title="导入信息提示"
      width="500px"
      :showCancel="false"
      :dialogVisible.sync="dialogMsgVisible"
      @submit="submitMsgDialog"
      :appendToBody="true"
    >
      <template>
        <p>导入成功：{{ importData.successCount }} 条</p>
        <p>导入失败：{{ importData.errorCount }} 条</p>
        <p
          class="pcc"
          v-for="(item, index) in importData.errorData"
          :key="index"
        >
          {{ item }}
        </p>
      </template>
    </dc-dialog>
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import {
  listCustomer,
  deleteCustomer,
  customerDownLoad,
  customerExport,
} from '@/api/clientmanagement/customer'
import { getDict } from '@/utils/tools'
import { customerLevelColors } from '@/utils/dict.js'
import datepicker from '@/components/common/datepicker.vue'
import {
  getParStr,
  downloadExcelFile,
  downloadExcelFileCommon,
} from '@/utils/tools'
import { getToken } from '@/utils/auth'
export default {
  components: {
    page,
    nolist,
    datepicker,
  },
  data() {
    return {
      dialogVisible: false,
      dialogType: '1',
      deleteCustomerId: '',
      isShowCancel: true,
      typename: '',
      levels: [],
      isLoading: false,
      tableData: [],
      total: 0,
      deletemsg: '',
      dateRange: [],
      levelColor: customerLevelColors,
      pageBean: {
        isDeleted: 0,
        customerLevel: '',
        isFollow: '',
        customerName: '',
        unitName: '',
        phone: '',
        chargePerson: '', // 我负责
        collaborator: '', // 我协作
        startTime: '',
        endTime: '',
        time: '',
        chargePersonName: '',
        chargePersonDepartment: '',
        createByName: '',
        createByDepartment: '',
        pageNum: 1,
        pageSize: 10,
      },
      getUrl: `${process.env.VUE_APP_BASE_API}/crm/controller/customer/customerImportExcel`,
      headers: { Authorization: getToken() },
      dialogMsgVisible: false,
      importData: {
        successCount: 0,
        errorCount: 0,
        errorData: [],
      },
      isDownload: false,
      isImport: false,
      fileData: {
        applicationId: sessionStorage.getItem('applicationId'),
        serviceName: 'crm/customer/excel',
      },
      globalPageNum: 1,
      isDownloadCustomer: false,
    }
  },
  computed: {
    computedCss() {
      return function () {
        let width = 200 - 50
        return {
          width: width + 'px',
        }
      }
    },
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
    }
    getDict('CustomerLevel')
      .then((result) => {
        this.levels = result
      })
      .catch((err) => {})
    this.loadData()
  },
  methods: {
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      listCustomer(this.pageBean)
        .then((result) => {
          result.data &&
            result.data.forEach((element) => {
              var userid = window.sessionStorage.getItem('userid')
              if (
                element.collaborator.includes(userid) &&
                element.chargePerson != userid
              ) {
                element.isCollaborator = true
              } else {
                element.isCollaborator = false
              }
              element.isChargePerson =
                element.chargePerson == userid ? true : false
            })
          this.tableData = result.data ? result.data : []
          this.total = result.data ? result.page.total : 0
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    changeDateRange(date) {
      if (date) {
        this.pageBean.startTime = date[0]
        this.pageBean.endTime = date[1]
      } else {
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
    },
    getIsEnable(data, typeStr) {
      if (typeStr == 'edit') {
        this.onEdit(data)
      } else {
        var dataScope = sessionStorage.getItem('dataScope')
        if (dataScope == 4) {
          this.deleteAction(data)
        } else {
          this.$message({
            type: 'error',
            message: '暂无权限，无法删除',
          })
        }
      }
    },
    searchAction() {
      this.pageBean.pageNum = 1
      this.loadData()
    },
    //  删除弹框删除客户
    submitDialog() {
      this.dialogVisible = false
      if (this.dialogType == 1) {
        this.deleteData()
      }
    },
    deleteData() {
      deleteCustomer({ id: this.deleteCustomerId })
        .then((result) => {
          if (result.data) {
            this.loadData()
            this.$message({
              type: 'success',
              message: '删除成功,删除后该客户将进入回收站!',
            })
          } else {
            this.dialogType = 2
            this.isShowCancel = false
            this.deletemsg = result.msg
            this.dialogVisible = true
          }
        })
        .catch((err) => {})
    },

    addCustomer() {
      this.$router.push({
        path: '/clientManagement/customer/add',
      })
    },
    deleteAction(data) {
      this.dialogType = 1
      this.isShowCancel = true
      this.dialogVisible = true
      this.deletemsg = '是否删除该客户？删除后将在回收站保留15天。'
      this.deleteCustomerId = data.id
    },
    toDetail(data) {
      this.$router.push({
        path: '/clientManagement/customer/detail',
        query: { id: data.id },
      })
    },
    toBook(data) {
      this.$router.push({
        path: '/clientManagement/customer/bookinfo',
        query: { id: data.id },
      })
    },
    onEdit(data) {
      this.$router.push({
        path: '/clientManagement/customer/add',
        query: {
          id: data.id,
        },
      })
    },
    handleCommand(index, data) {
      var rpath = ''
      switch (index) {
        case 0: // 跟进与拜访
          rpath = '/clientMaintenance/followVisit/add'
          this.$router.push({
            path: rpath,
            query: {
              customerId: data.id,
              customerName: data.customerName,
              unitId: data.unitId,
              unitName: data.unitName,
            },
          })
          break
        case 1:
          this.toDetail(data)
          break
        case 2:
          {
            rpath = '/clientManagement/customer/Importantdate'
            this.$router.push({
              path: rpath,
              query: {
                customerId: data.id,
              },
            })
          }
          break
        case 3:
          this.toGive(data, 1)
          break
        case 4:
          this.toGive(data, 2)
          break
        case 5:
          rpath = '/subscribe/add'
          this.$router.push({
            path: rpath,
            query: {
              customerId: data.id,
              customerName: data.customerName,
              unitId: data.unitId,
              unitName: data.unitName,
            },
          })
          break
        default:
          break
      }
    },
    toGive(data, type) {
      this.$router.push({
        path: '/give/add',
        query: {
          type: type,
          customerId: data.id,
          customerName: data.customerName,
          unitId: data.unitId,
          unitName: data.unitName,
        },
      })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
    getColor(name) {
      return
    },
    submitAction(type, dateRange) {
      if (type == 11) {
        if (dateRange) {
          this.pageBean.startTime = dateRange[0]
          this.pageBean.endTime = dateRange[1]
        } else {
          this.pageBean.startTime = ''
          this.pageBean.endTime = ''
        }
        this.pageBean.time = ''
      } else {
        this.pageBean.time = type
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
    },
    beforeUpload(file) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      const whiteList = ['xlsx']
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.error('导入明细仅支持 .xlsx 格式!')
        return false
      }
      this.isImport = true
    },
    handleError(file, res) {
      this.isImport = false
    },
    handleAvatarSuccess(res, file) {
      if (res.status == 0 && res.data.errorCount == 0) {
        this.$message({
          type: 'success',
          message: res.msg,
        })
      } else {
        this.$message({
          type: 'error',
          message: res.msg,
        })
      }
      this.dialogMsgVisible = true
      this.importData = res.data
      this.isImport = false
      this.pageBean.pageNum = 1
      // 刷新数据
      this.loadData()
    },
    submitMsgDialog() {
      this.dialogMsgVisible = false
      this.importData = {}
    },
    downloadAction() {
      this.isDownload = true
      customerDownLoad()
        .then((result) => {
          downloadExcelFile(result, `客户信息模版`)
          this.isDownload = false
        })
        .catch((err) => {
          this.isDownload = false
        })
    },
    handleCustomerExport() {
      this.isDownloadCustomer = true
      let params = {
        ...this.pageBean,
        pageNum: this.globalPageNum,
        exportType: 1,
      }
      delete params.pageSize
      customerExport(params)
        .then((result) => {
          this.isDownloadCustomer = false
          downloadExcelFileCommon(result.data, `客户管理`)
          if (result.data.isExport) {
            this.$confirm('本次导出1千条数据,是否继续导出?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                this.globalPageNum++
                this.handleCustomerExport()
              })
              .catch(() => {
                this.globalPageNum = 1
                this.$message({
                  type: 'info',
                  message: '已取消',
                })
              })
          } else {
            this.$message({
              type: 'success',
              message: '数据已全部导出',
            })
            this.globalPageNum = 1
          }
        })
        .catch((err) => {
          this.isDownloadCustomer = false
        })
    },
  },
}
</script>
<style>
.tootiptable .el-tooltip {
  text-align: left;
}
.chargePersoncss .cell {
  padding-bottom: 7px !important;
}
</style>
<style scoped lang="scss">
.mainbg {
  padding: 20px;
  background-color: white;
  min-height: 100%;
}
.cpdepartmentcss {
  font-size: 12px !important;
}
.ml20 {
  margin-right: 16px;
}
.m0 {
  margin-right: 0px !important;
}
.mf {
  display: flex !important;
}
.m20 {
  margin-right: 20px !important;
}
.pr {
  position: relative;
}
.defr {
  float: right !important;
}
.width {
  width: 120px;
}
.width160 {
  width: 160px;
}
.departcss {
  width: 100%;
  text-align: center;
}
.namecss {
  display: flex;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  min-width: 30px !important;
  /* height: 52px; */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  margin-top: -4px;
  vertical-align: middle;
  position: relative;
  top: 8px;
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
}

.tagcss {
  font-size: 0.625em !important;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
}

.column_blue {
  /* padding-top: 14px; */
  line-height: 24px;
  color: #4285f4;
  display: inline-block;
  text-align: left;
  /* overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis; */
}

.genjin {
  color: #4285f4;
  background-color: #dfeafd;
}

.fuze {
  color: #fef2e7;
  background-color: #ff8d1a;
}

.xiezuo {
  color: #56c36e;
  background-color: #f3fef6;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.tagcss:nth-child(3n) {
  margin-top: 4px;
}

.customertable /deep/.cell {
}

.customertable .el-button {
  padding: 2px;
}
.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}
.right {
  text-align: right;
}
.myform .el-form-item:last-child {
  margin-right: 20px !important;
}
</style>
