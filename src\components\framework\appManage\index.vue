<!--app管理-->
<template>
  <div class="app-container">
    <el-form :inline="true">
      <el-form-item>
        <el-input
          placeholder="请输入版本号"
          clearable
          v-model="search.versionCode"
          class="input-230"
          @keyup.native="stock"
          @blur="stock"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          @clear="cleatInput"
          v-model="search.appCode"
          clearable
          placeholder="请选择应用"
          class="input-230"
        >
          <el-option
            v-for="item in Dict.APPLICATION"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <SelectDatePicker
          v-model="time"
          :startplaceholder="'开始时间'"
          :endplaceholder="'结束时间'"
        ></SelectDatePicker>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" size="medium" @click="handleSearch">
          <i class="fa fa-search"></i>查询
        </el-button>
        <el-button
          type="primary"
          size="medium"
          v-isShow="'sf:business:appversion:save'"
          @click="addOrUpdateHandle()"
          ><i class="fa fa-plus"></i> 添加</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table
      :data="tableData"
      border
      :empty-text="$emptyFont"
      v-loading="dataListLoading"
      style="width: 100%"
      element-loading-text="拼命加载中..."
      element-loading-spinner="el-icon-loading"
    >
      <el-table-column label="应用编码">
        <template slot-scope="scope">
          <span>
            {{ scope.row.appCode | dict('APPLICATION') }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="版本名称" prop="">
        <template slot-scope="scope">
          <span v-if="scope.row.versionFirst">
            V{{ scope.row.versionFirst }}.{{ scope.row.versionSecond }}.{{
              scope.row.versionThird
            }}
          </span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="最小更新版本号" prop="miniCode"></el-table-column>
      <el-table-column label="版本号" prop="versionCode"></el-table-column>
      <!-- <el-table-column label="强制升级" >
        <template slot-scope="scope">
          <span v-if="scope.row.forceUpgrade===1">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column> -->
      <el-table-column label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isEnabled === 1" size="small" type="success"
            >启用</el-tag
          >
          <el-tag v-if="scope.row.isEnabled === 0" size="small" type="danger"
            >禁用</el-tag
          >
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="180">
        <template slot-scope="scope">{{
          ruleTime(scope.row.createTime)
        }}</template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="250">
        <template slot-scope="scope">
          <el-button
            @click="handleviewDetail(scope.row)"
            type="text"
            size="small"
            v-isShow="'sf:business:appversion:info'"
            icon="el-icon-view"
            >查看</el-button
          >
          <el-button
            @click="addOrUpdateHandle(scope.row)"
            type="text"
            size="small"
            v-isShow="'sf:business:appversion:update'"
            icon="el-icon-edit"
            >修改</el-button
          >
          <el-button
            @click="handleSwitchStaus(scope.row, 0)"
            type="text"
            size="small"
            v-if="scope.row.isEnabled === 1"
            v-isShow="'sf:business:appversion:update'"
            icon="el-icon-switch-button"
            >禁用</el-button
          >
          <el-button
            @click="handleSwitchStaus(scope.row, 1)"
            type="text"
            size="small"
            v-if="scope.row.isEnabled === 0"
            v-isShow="'sf:business:appversion:update'"
            icon="el-icon-aim"
            >启用</el-button
          >
          <el-button
            v-if="scope.row.isEnabled !== 1"
            @click="handleDelete(scope.row)"
            type="text"
            size="small"
            v-isShow="'sf:business:appversion:delete'"
            icon="el-icon-delete"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div style="padding: 32px 16px 12px; background: #fff">
      <el-pagination
        background
        v-if="pagingObj.totalSum"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagingObj.currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagingObj.totalSum"
      ></el-pagination>
    </div>

    <!-- 弹窗, 新增 / 修改 -->
    <AddOrUpdate
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList(search)"
    ></AddOrUpdate>
    <!-- 详情 -->
    <app-details v-if="detailVisible" ref="detailRef"></app-details>
  </div>
</template>

<script>
import SelectDatePicker from '@/components/select/SelectDatePicker.vue'
import pagination from '@/mixin/pagination'
import AddOrUpdate from './add-or-update'
import AppDetails from './details'
export default {
  mixins: [pagination],
  data() {
    return {
      dataListLoading: false,
      detailVisible: false,
      addOrUpdateVisible: false,
      time: [],
      search: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      totalPages: {},
    }
  },
  components: {
    SelectDatePicker,
    AddOrUpdate,
    AppDetails,
  },
  created() {
    // 默认先查询数据
    this.handleSearch()
  },
  methods: {
    cleatInput() {
      console.log(222)
      delete this.search.appCode
    },
    stock() {
      this.search.versionCode = this.search.versionCode.replace(/[^\d]/g, '')
    },
    // 新增 / 修改
    addOrUpdateHandle(obj) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(obj)
      })
    },
    // 查看
    handleviewDetail(obj) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.detailRef.init(obj)
      })
    },

    // 状态
    handleSwitchStaus(row, status) {
      let str = ''
      if (status === 0) {
        str = '禁用'
      } else {
        str = '启用'
      }
      this.$confirm(
        `您确认要${str}[${row.appCode}, V${row.versionFirst}.${row.versionSecond}.${row.versionThird}]此条记录吗?`,
        '操作确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          // 发送 axios
          this._updateAppversion(row, status)
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消"
          // });
        })
    },
    async _updateAppversion(row, status) {
      let params = {
        id: row.id,
        isEnabled: status,
      }
      let res = await this.$axios.post('/sf/business/appversion/update', params)
      if (res.status === 0) {
        this.$message({
          message: '操作成功',
          type: 'success',
        })
        this.getDataList(this.search)
      } else {
        this.$message.error(res.msg)
      }
    },

    // 删除
    handleDelete(row) {
      let str = `${row.appCode}, V${row.versionFirst}.${row.versionSecond}.${row.versionThird}`
      this.$confirm(`您确认要删除“${str}”此条记录吗？?`, '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // 发送删除 axios
          this._deleteAppversion(row.id)
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消"
          // });
        })
    },
    async _deleteAppversion(id) {
      let res = await this.$axios.post('/sf/business/appversion/delete', {
        ids: id,
      })
      if (res.status === 0) {
        this.$message({
          message: '操作成功',
          type: 'success',
        })
        this.getDataList(this.search)
      } else {
        this.$message.error(res.msg)
      }
    },

    // 查询
    handleSearch() {
      this.search.pageNum = 1
      // this.search.pageSize = 10;
      this.pagingObj.currentPage = 1
      this.handleTimeParams(this.time, 'startTime', 'endTime')
      this.getDataList(this.search)
    },
    // 列表请求参数
    async getDataList(obj) {
      this.dataListLoading = true
      let res = await this.$axios.get('/sf/business/appversion/list', {
        params: obj,
      })
      this.dataListLoading = false
      if (res.status === 0) {
        this.tableData = res.data
        this.pagingObj.totalSum = res.page.total
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.dialog-footer {
  padding: 16px 0;
  text-align: center;
  display: block;
}
.executeformClass {
  width: 500px;
  padding-top: 20px;
  margin: 0 auto;
}
.userAvatar {
  width: 45px;
  height: 45px;
  text-align: center;
  display: inline-block;
  img {
    display: inline-block;
    max-width: 45px;
    max-height: 45px;
  }
}
</style>
