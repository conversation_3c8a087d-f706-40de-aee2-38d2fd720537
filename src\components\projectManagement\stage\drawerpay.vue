<template>
  <div class="drawer">
    <el-drawer class="diydra ed" center size="50%" title="回款流程详情" :visible.sync="drawer_" :direction="direction" @close="handleClose">
      <el-tabs class="diytab edt" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="阶段信息" name="first">
          <bacisInfo v-on="$listeners" :form="form"></bacisInfo>
        </el-tab-pane>
        <el-tab-pane label="回款记录" name="third">
            <payrecord  :form="form" :stageId="stageId" ref="returnRef"></payrecord>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>



  </div>
</template>

<script>


import {stageSave,stageInfo,stageUpdate} from '@/api/stage/index'
import bacisInfo from './basicInfo.vue'
import payrecord from './payrecord'
export default {
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    direction: {
      type: String,
      default: 'rtl',
    },
  },
  components:{
    bacisInfo,
    payrecord,
    
  },
  data() {
    return {
      activeName: 'first',
      detailVisible:false,
      stageId:'',
      form:{}
    }
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
       
      },
    },
  },
  methods: {
    getStageInfo(stageId){
        this.stageId = stageId
        this.$refs.returnRef.rturnListData(this.stageId)
        stageInfo(this.stageId).then(res=>{
            if(res.status == 0){
                this.form  = res.data
            }
        })
    },
    updateDetailVisible(visible){
      this.detailVisible = visible;
    },
    handleClick(tab, event) {
      },
    handleClose() {
      this.$emit('changeDrawer', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.diytab /deep/.el-tabs__header{
  margin-bottom: 30px;
}
.diydra /deep/.el-drawer{
    min-width: 755px;
    overflow-y: auto;
}
.color-0{
  color: #56C36E;
}
.color-1{
  color: #4A8BF6;
}
.color-2{
  color: #E85D5D;
}
.color-3{
  color: #EC9037;
}
.color-4{
  color: #F46D40;
}
.el-col-lg-4-8 {
		width: 20%;
	}
.numtext{
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.numcolor{
  font-size: 24px;
  font-family: Roboto-Bold, Roboto;
  font-weight: bold;
}
.imgf{
  margin-right: 5px;
  width: 40px;
}
.piececss{
  position: relative;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  box-shadow: 0px 2px 16px 0px rgba(15,27,50,0.08);

}
.cardcss{
  margin-bottom: 10px;
}
  .drawer{
    /deep/.el-drawer{
      padding: 20px;
      padding-top: 0;
    }
    /deep/ .el-drawer__header{
      padding-left: 0;
      text-align: center;
    }
  }
</style>>
