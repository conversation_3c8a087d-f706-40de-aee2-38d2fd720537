<template>
  <el-dialog title="编辑角色" :close-on-click-modal="false" :visible.sync="visible" width="40%">
    <div>
      <div class="cflex">
        <p class="roletitle">授权</p>
        <div class="cf1">
          <el-checkbox v-model="item.isSelected" :key="index" :label="item.id" v-for="(item,index) in checkArr">
            {{ item.name }}</el-checkbox>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button v-isShow="'crm:controller:userinfodetail:updateUserRole'" type="primary" @click="dataFormSubmit()">确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>

  import { queryRoleList, updateUserRole } from '@/api/framework/user'
  export default {
    data() {
      return {
        visible: false,
        checkList: [],
        checkArr: [],
        userId: ''
      }
    },
    methods: {
      init(id) {
        this.userId = id
        this.visible = true
        queryRoleList({ userId: id }).then(res => {
          if (res.status === 0) {
            this.checkArr = res.data
          }
        })
      },
      getArrId(arr) {
        let newArr = []
        for (let i = 0; i < arr.length; i++) {
          console.log(arr[i].isSelected)
          if (arr[i].isSelected) {
            newArr.push(arr[i].id)
          }
        }
        return newArr
      },
      // 表单提交
      async dataFormSubmit() {
        let roleIds = this.getArrId(this.checkArr)
        let params = {
          roleIds,
          userId: this.userId
        }
        let res = await updateUserRole(params)
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
          })
          this.visible = false
          this.$emit('refreshTagTypeTree')
        } else {
          this.$message.error(res.msg)
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .diycss {
    /deep/ .el-checkbox {
      margin-right: 20px;
      margin-bottom: 20px;
    }
  }

  .roletitle {
    text-align: right;
    width: 80px;
    margin-right: 10px;
  }
</style>>