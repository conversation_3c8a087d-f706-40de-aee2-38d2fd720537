<!-- 登录日志 -->
<template>
  <div class="mod-config app-container">
    <el-form :inline="true">
      <el-form-item>
        <el-input v-model="search.uAccountName" clearable placeholder="用户账号" class="input-230"></el-input>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          class="input-230"
          v-model="time"
          clearable
          type="datetimerange"
          value-format="yyyy-MM-dd"
          start-placeholder="起始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="search.platform" clearable placeholder="请选择平台">
          <el-option
            v-for="item in Dict.FLATFORM_TYPE"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" v-isShow="'sf:business:loginlog:list'" @click="handleSearch"><i class="fa fa-search"></i>查询</el-button>
      </el-form-item>
    </el-form>
    <!-- table -->
    <el-table
      :data="dataList"
      border
      stripe
      v-loading="dataListLoading"
      style="width: 100%;"
      element-loading-text="加载中..." element-loading-spinner="el-icon-loading">
      <el-table-column
        prop="uAccountName"
        header-align="center"
        align="center"
        label="姓名">
      </el-table-column>
      <el-table-column
        prop="uAccountName"
        header-align="center"
        align="center"
        label="创建时间">
        <template slot-scope="scope">
          {{ruleTime(scope.row.createTime)}}
        </template>
      </el-table-column>
      <el-table-column
        prop="province"
        header-align="center"
        align="center"
        label="省">
      </el-table-column>
      <el-table-column
        prop="city"
        header-align="center"
        align="center"
        label="市">
      </el-table-column>
      <el-table-column
        prop="ip"
        header-align="center"
        align="center"
        label="IP">
      </el-table-column>
      <el-table-column
        prop="region"
        header-align="center"
        align="center"
        label="登录区域">
      </el-table-column>
      <el-table-column
        prop="platform"
        header-align="center"
        align="center"
        label="平台">
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      style="padding: 32px 16px 12px;background:#fff"
      background
      v-if="pagingObj.totalSum"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagingObj.currentPage"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagingObj.totalSum">
    </el-pagination>
  </div>
</template>

<script>
import pagination from '@/mixin/pagination'
export default {
  mixins: [pagination],
  data () {
    return {
      time: [],
      search: {},
      dataList: [],
      dataListLoading: false
    }
  },
  methods: {
    handleSearch () {
      this.search.pageNum = 1;
      this.pagingObj.currentPage = 1;
      this.handleTimeParams(this.time, 'startTime', 'endTime')
      this.getDataList(this.search)
    },
    async getDataList (obj) {
      let res = await this.$axios.get('/sf/business/loginlog/list', { params: obj });
      if (res.status === 0) {
        this.dataList = res.data;
        this.pagingObj.totalSum = res.page.total;
      }
    }
  },
  created () {
    this.search.pageNum = '1';
    this.search.pageSize = '10';
    this.getDataList(this.search);
  }
}
</script>
