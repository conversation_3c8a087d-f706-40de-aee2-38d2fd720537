<template>
  <el-dialog title="编辑角色" :close-on-click-modal="false" :visible.sync="visible" width="40%">
    <div>
      <div class="department">
        <div class="topTitle">
          <div class="line"></div>
          <div class="title">部门</div>
        </div>
        <div class="cflex">
          <p class="roletitle">所属部门</p>
          <div class="cf1">
            <el-select
              v-model="selectedDepartmentNames"
              placeholder="请选择部门"
              @focus="chooseDepartment"
              clearable
              readonly>
              <el-option
                v-if="selectedDepartmentNames"
                :value="selectedDepartmentNames"
                :label="selectedDepartmentNames">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="role">
        <div class="topTitle">
          <div class="line"></div>
          <div class="title">角色</div>
        </div>
        <div class="cflex">
          <p class="roletitle">授权</p>
          <div class="cf1">
            <el-checkbox v-model="item.isSelected" :key="index" :label="item.id" v-for="(item,index) in checkArr">
              {{ item.name }}</el-checkbox>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button v-isShow="'crm:controller:userinfodetail:updateUserRole'" type="primary" @click="dataFormSubmit()">确定
      </el-button>
    </span>

    <DepartmentSelectDialog
      ref="departmentSelectRef"
      :visible.sync="departmentSelectDialogVisible"
      :user-id="userId"
      :selected-departments="selectedDepartments"
      @updateVisible="updateDepartmentDialogVisible"
      @submit="handleDepartmentSelected"
      @cancel="handleDepartmentCancel">
    </DepartmentSelectDialog>
  </el-dialog>
</template>

<script>
  import { queryRoleList, userRelateMove, queryUserDepartmentList } from '@/api/framework/user'
  import DepartmentSelectDialog from '@/components/common/DepartmentSelectDialog.vue'

  export default {
    components: {
      DepartmentSelectDialog
    },
    data() {
      return {
        visible: false,
        checkList: [],
        checkArr: [],
        userId: '',
        departmentSelectDialogVisible: false,
        selectedDepartmentNames: '',
        selectedDepartments: []
      }
    },
    methods: {
      init(id) {
        this.userId = id
        this.visible = true
        this.selectedDepartmentNames = ''
        this.selectedDepartments = []

        // 获取用户角色信息
        queryRoleList({ userId: id }).then(res => {
          if (res.status == 0) {
            this.checkArr = res.data
          }
        })

        // 获取用户部门信息并回显
        queryUserDepartmentList({ userId: id }).then(res => {
          if (res.status == 0 ) {
            this.selectedDepartments = res.data
            let departmentNames = res.data.map(item => item.name)
            this.selectedDepartmentNames = departmentNames.join(',')
          }
        })
      },
      getArrId(arr) {
        let newArr = []
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].isSelected) {
            newArr.push(arr[i].id)
          }
        }
        return newArr
      },
      async dataFormSubmit() {
        this.$confirm('请先确定原部门中是否有历史数据，如有历史数据请先迁出，否则将导致历史数据不可见', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let roleIds = this.getArrId(this.checkArr)
          let departmentUserList = this.selectedDepartments.map(dept => ({
            departmentId: dept.id,
            userId: this.userId
          }))

          let params = {
            roleIds,
            id: this.userId,
            departmentUserList
          }

          let res = await userRelateMove(params)
          if (res.status == 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
            })
            this.visible = false
            this.$emit('refreshTagTypeTree')
          } else {
            this.$message.error(res.msg)
          }
        }).catch(() => {
          // 用户取消操作
        })
      },
      chooseDepartment() {
        this.departmentSelectDialogVisible = true
        this.$nextTick(() => {
          this.$refs.departmentSelectRef.loadData()
        })
      },
      updateDepartmentDialogVisible(value) {
        this.departmentSelectDialogVisible = value
      },
      handleDepartmentSelected(data) {
        this.departmentSelectDialogVisible = false
        this.selectedDepartments = data
        let departmentNames = data.map(item => item.name)
        this.selectedDepartmentNames = departmentNames.join(',')
      },
      handleDepartmentCancel() {
        this.departmentSelectDialogVisible = false
      }
    }
  }
</script>
<style lang="scss" scoped>
  .diycss {
    /deep/ .el-checkbox {
      margin-right: 20px;
      margin-bottom: 20px;
    }
  }

  .roletitle {
    text-align: right;
    width: 80px;
    margin-right: 10px;
  }
  .topTitle{
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
    background: #f0f3fa;
    border-radius: 4px;
    padding: 0 16px;
    margin-bottom: 20px;
  }
  .line{
    width: 3px;
    height: 14px;
    background-color: #386cfc;
    margin-right: 12px;
  }
  .title{
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 16px;
    color: #2d2f33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
  .cflex {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  .cf1 {
    flex: 1;
  }
</style>
