<template>
  <el-dialog
    :title="isEdit ? '编辑成本' : '成本申报'"
    :visible.sync="visible"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="form" label-width="120px">
      <div class="theadcss" v-if="taskObj.taskNum">
        ID：
        {{ taskObj.taskNum }}
        <span class="sml">{{ taskObj.taskName }}</span>
      </div>
      <el-form-item label="关联任务" prop="taskId" v-else>
        <el-cascader
          v-model="form.taskId"
          placeholder="请选择关联任务"
          style="width: 100%"
          clearable
          filterable
          :show-all-levels="false"
          :options="taskOptions"
          :props="cascaderProps"
          @change="handleTaskChange"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="日期" prop="costDate">
        <el-date-picker
          v-model="form.costDate"
          type="date"
          placeholder="请选择日期"
          value-format="yyyy-MM-dd"
          style="width: 100%"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="事项" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="6"
          placeholder="请输入成本事项"
          maxlength="200"
          show-word-limit
        >
        </el-input>
      </el-form-item>
      <el-form-item label="费用类型" prop="costType">
        <el-select
          v-model="form.costType"
          placeholder="请选择费用类型"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in costTypeOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="金额" prop="costAmount">
        <el-input-number
          v-model="form.costAmount"
          :controls="false"
          :min="0"
          :max="99999999"
          :precision="2"
          placeholder="请输入金额（单位：元）"
          class="definput widm textleft"
        >
        </el-input-number>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </div>

    <!-- 部门验证组件 -->
    <verifyDeparment
      ref="verifyDeparment"
      @submit="submitWithDepartment"
    ></verifyDeparment>
  </el-dialog>
</template>

<script>
import { queryVoListByCode } from '@/api/wapi.js'
import { queryTaskList } from '@/api/project/index'
import verifyDeparment from '@/components/common/verifyDeparment.vue'

export default {
  name: 'CostRecordDialog',
  components: {
    verifyDeparment,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: () => ({}),
    },
    taskObj: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      form: {
        taskId: [],
        costDate: '',
        reason: '',
        projectType: '',
        costType: '',
        taskType: 1,
        costAmount: undefined,
      },
      rules: {
        taskId: [
          { required: true, message: '请选择关联任务', trigger: 'change' },
        ],
        costDate: [
          { required: true, message: '请选择日期', trigger: 'change' },
        ],
        reason: [
          { required: true, message: '请输入成本事项', trigger: 'blur' },
        ],
        costType: [
          { required: true, message: '请选择费用类型', trigger: 'change' },
        ],
        costAmount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
        ],
      },
      costTypeOptions: [],
      taskOptions: [],
      cascaderProps: {
        value: 'id',
        label: 'taskName',
        children: 'childrenList',
        checkStrictly: true,
      },
    }
  },
  created() {
    this.loadCostTypes()
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadTasks().then(() => {
          // 任务数据加载完成后再填充编辑数据
          if (this.isEdit && this.editData.id) {
            this.fillEditData()
          }
        })
      }
    },
    editData: {
      handler(newVal) {
        if (
          this.isEdit &&
          newVal.id &&
          this.visible &&
          this.taskOptions.length > 0
        ) {
          this.fillEditData()
        }
      },
      deep: true,
    },
  },
  methods: {
    loadCostTypes() {
      queryVoListByCode({ code: 'CostType' }).then((res) => {
        if (res.status == 0) {
          this.costTypeOptions = res.data
        }
      })
    },
    loadTasks() {
      return queryTaskList({ projectId: this.$route.query.id }).then((res) => {
        if (res.status == 0) {
          this.taskOptions = this.formatTaskData(res.data)
        }
        return res
      })
    },
    formatTaskData(data) {
      return data.map((item) => ({
        ...item,
        taskName: `${item.taskNum} - ${item.taskName}`,
        childrenList:
          item.childrenList && item.childrenList.length > 0
            ? this.formatTaskData(item.childrenList)
            : undefined,
      }))
    },
    handleTaskChange(value) {
      this.form.taskId = value
    },
    fillEditData() {
      if (this.editData && this.editData.id) {
        const taskPath = this.findTaskPath(
          this.editData.taskId,
          this.taskOptions
        )

        this.form = {
          id: this.editData.id,
          status: this.editData.status,
          taskId:
            taskPath.length > 0
              ? taskPath
              : this.editData.taskId
              ? [this.editData.taskId]
              : [],
          costDate: this.editData.costDate || '',
          reason: this.editData.reason || '',
          projectType: this.editData.projectType || '',
          costType: this.editData.costType || '',
          taskType: this.editData.taskType || 1,
          costAmount: this.editData.costAmount || undefined,
        }
      }
    },
    findTaskPath(targetTaskId, taskList, currentPath = []) {
      for (const task of taskList) {
        const newPath = [...currentPath, task.id]

        // 如果找到目标任务，返回路径
        if (task.id === targetTaskId) {
          return newPath
        }

        // 如果有子任务，递归查找
        if (task.childrenList && task.childrenList.length > 0) {
          const foundPath = this.findTaskPath(
            targetTaskId,
            task.childrenList,
            newPath
          )
          if (foundPath.length > 0) {
            return foundPath
          }
        }
      }

      return []
    },
    resetForm() {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 新增时验证部门 - 编辑无需验证
          if (!this.isEdit) {
            this.$refs.verifyDeparment.verify()
          } else {
            this.submitWithDepartment(this.editData.departmentId)
          }
        }
      })
    },
    submitWithDepartment(departmentId) {
      const formData = { ...this.form }
      formData.taskId =
        Array.isArray(this.form.taskId) && this.form.taskId.length > 0
          ? this.form.taskId[this.form.taskId.length - 1]
          : ''
      formData.departmentId = departmentId

      if (this.isEdit && this.editData.id) {
        formData.id = this.editData.id
      }

      this.$emit('submit', formData)
      this.handleClose()
    },
  },
}
</script>

<style scoped>
.theadcss {
  padding: 0 30px;
  height: 45px;
  line-height: 45px;
  background: #4285f4;
  border-radius: 8px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 16px;
  color: #ffffff;
  margin-bottom: 16px;
}
.dialog-footer {
  text-align: right;
}
.widm {
  width: 100%;
}
.textleft /deep/.el-input__inner {
  text-align: left !important;
}
</style>
