<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :direction="direction"
    :before-close="handleClose"
    size="50%"
  >
    <div class="drawer-container">
      <el-table :data="tableData" style="width: 100%"  class="unittable mytable">
        <el-table-column prop="materialName" label="教材名称" align="center" />
        <el-table-column prop="isbn" label="ISBN" align="center" />
        <el-table-column prop="platformName" label="出版社" align="center" />
        <el-table-column prop="unitName" label="用书单位" align="center" />
        <el-table-column prop="specialtyName" label="用书专业" align="center" />
        <el-table-column prop="customerName" label="客户" align="center" />
        <el-table-column prop="useBookYear" label="用书时间" align="center" />
        <el-table-column prop="price" label="教材定价(元)" align="center" />
        <el-table-column prop="reserveNum" label="预定数量" align="center" />
        <el-table-column prop="actualNum" label="实报数量" align="center">
          <template slot-scope="scope">
            {{ scope.row.actualNum || '---' }}
          </template>
        </el-table-column>
        <el-table-column prop="discount" label="折扣" align="center">
          <template slot-scope="scope">
            {{ scope.row.discount || '---' }}
          </template>
        </el-table-column>
      </el-table>

      <div class="bdInfo">
            <div class="infoItem">
              <div class="title">报订人：<span>{{ info.createName || '---' }}</span></div>
              <div class="title">报订时间：<span>{{ info.createTime || '---' }}</span></div>
              <div class="title">中标公司：<span>{{ info.bidwinnerName || '---' }}</span></div>
              <div class="title">货源：<span>{{ info.sourceGoods || '---' }}</span></div>
              <div class="title">备注：<span>{{ info.notes || '---' }}</span></div>
            </div>
            <div class="infoItem">
              <div class="title">完成率：<span>{{ info.completionRate || '---' }}</span></div>
              <div class="title">报订码洋(万元)：<span>{{ info.reserveRetailPrice || '---' }}</span></div>
              <div class="title">实际码洋(万元)：<span>{{ info.actualRetailPrice || '---' }}</span></div>
              <div class="title">状态：<span>{{ statusMap[info.status] || '---' }}</span></div>
            </div>
      </div>
      <div class="checkpeople">
          <div class="title">审核人：</div>
          <eltimeline class="mt" :businessId="baodingId" :status="info.status" v-if="value && baodingId && info.status"></eltimeline>
        </div>
    </div>
  </el-drawer>
</template>

<script>
import { baodinginfo } from '@/api/baoding'
import eltimeline from '@/components/common/eltimeline.vue'
export default {
  components:{
    eltimeline
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '详情'
    },
  },
  data() {
    return {
      direction: 'rtl',
      tableData:[],
      baodingId:'',
      info: {
        orderPerson: '123',
        orderTime: '',
        winningCompany: '',
        source: '',
        completionRate: '',
        orderAmount: '',
        actualAmount: '',
        status: ''
      },
      statusMap:{
        1:'未报批',
        2:'审核中',
        3:'已通过',
        4:'已驳回'
      },
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
  },
  methods: {
    handleClose() {
      this.visible = false
    },
    loadinfo(id){
      this.baodingId = id;
      baodinginfo(id).then(res =>{
        this.info = res.data;
        this.tableData = [this.info]
      }).catch(err=>{

      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  padding: 20px;
}
.bdInfo{
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  .infoItem{
    width: 50%;
  }
}
.block{
  margin-top: 20px;
}
.title{
  line-height: 34px;
  margin-bottom: 16px;
}
.spanname {
  padding-right: 8px;
  text-align: right;
  flex-shrink: 0;
}
.checkpeople {
  display: flex;
  align-items: start;
  .mt{
    margin-top: 6px;
    margin-left: 10px;
  }
}
</style>
