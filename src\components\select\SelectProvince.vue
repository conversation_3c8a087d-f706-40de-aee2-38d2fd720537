<!-- 地区县 -->
<template>
  <div style="display: inline-block">
    <el-select v-model="provinceId" placeholder="全国" prop="provinceId" clearable class="input-230">
      <el-option
        v-for="item in provinary.provinceData"
        :key="item.id"
        :label="item.name"
        :value="item.id">
      </el-option>
    </el-select>
    <el-select v-model="cityId" placeholder="选择市" prop="cityId" clearable class="input-230">
      <el-option
        v-for="item in provinary.cityData"
        :key="item.id"
        :label="item.name"
        :value="item.id">
      </el-option>
    </el-select>
    <el-select v-model="countyId" placeholder="选择区" prop="countyId" clearable class="input-230">
      <el-option
        v-for="item in provinary.countyData"
        :key="item.id"
        :label="item.name"
        :value="item.id">
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  props: ['value'],
  data () {
    return {
      str: '000000000000',
      provinceId: '',
      cityId: '',
      countyId: '',
      paramsAry: [
        {
          provinceName: '',
          provinceId: ''
        },
        {
          cityName: '',
          cityId: ''
        },
        {
          countyName: '',
          countyId: ''
        }
      ],
      provinary: {
        provinceData: [], // 省
        cityData: [], // 市
        countyData: [] // 区
      }
    }
  },
  watch: {
    value (val) {
      // this.provinceId = val[0] || '';
      // this.cityId = val[1] || '';
      // this.countyId = val[2] || '';
    },
    provinceId (val) {
      this.cityId = ''
      this.countyId = ''
      this.getCityName(val)
    },
    cityId (val) {
      console.log(val, 9)
      this.countyId = ''
      this.getCountyName(val)
    },
    countyId (val) {
      this.getCountId(val)
    }
  },
  methods: {
    // 获取市
    async getCityName (obj) {
      if (obj) {
        this.provinary.provinceData.map(item => {
          if (item.id === obj) {
            this.paramsAry[0].provinceName = item.name
            this.paramsAry[0].provinceId = obj
          }
        })
      } else {
        this.paramsAry[0].provinceName = '';
        this.paramsAry[0].provinceId = ''
      }
      this.handleParams()
      let data = {}
      data.parentId = obj
      let res = await this.$axios.get('/sf/business/area/query', { params: data })
      if (res.status === 0) {
        this.provinary.cityData = res.data
      }
    },
    // 获取区
    async getCountyName (obj) {
      if (obj) {
        this.provinary.cityData.map(item => {
          if (item.id === obj) {
            this.paramsAry[1].cityName = item.name
            this.paramsAry[1].cityId = obj
          }
        })
      } else {
        this.paramsAry[1].cityName = '';
        this.paramsAry[1].cityId = ''
      }
      this.handleParams()
      let data = {}
      data.parentId = obj
      let res = await this.$axios.get('/sf/business/area/query', { params: data })
      if (res.status === 0) {
        this.provinary.countyData = res.data
      }
    },
    getCountId (obj) {
      if (obj) {
        this.provinary.countyData.map(item => {
          if (item.id === obj) {
            this.paramsAry[2].countyName = item.name
            this.paramsAry[2].countyId = obj
          }
        })
      } else {
        this.paramsAry[2].countyName = '';
        this.paramsAry[2].countyId = ''
      }
      this.handleParams()
    },
    // 获取省
    async getProvinceData (obj) {
      let data = {}
      data.parentId = obj
      let res = await this.$axios.get('/sf/business/area/query', { params: data })
      if (res.status === 0) {
        this.provinary.provinceData = res.data
      }
    },
    handleParams () {
      this.$emit('handleFatherParams', this.paramsAry)
    }
  },
  created () {
    this.getProvinceData(this.str)
  }
}
</script>
