<!--会员收入 class:ordersReceivedStatistics -->
<template>
    <div>
        <BaseOrderNumber :moneyList="moenyData"></BaseOrderNumber>
        <el-form :inline="true">
            <el-form-item>
                <el-select clearable placeholder="选择收入类型" v-model="dataForm.businessType">
                    <el-option
                        v-for="item in IncomeType"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
              <SelectDatePicker v-model="time"></SelectDatePicker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" @click="handleSearch">查询</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
            prop="orderId"
            label="订单号"
            width="140">
            </el-table-column>
            <el-table-column
            prop="description"
            label="收入类型"
            width="200">
            </el-table-column>
            <el-table-column
            prop=""
            label="支付账号"
            width="140">
            </el-table-column>
            <el-table-column
            prop="payUserName"
            label="支付姓名/单位"
            width="180">
            </el-table-column>
            <el-table-column
            label="支付方式"
            width="140">
                <template slot-scope="scope">
                  <span>{{paymentType(scope.row.paymentChannelYype)}}</span>
                </template>
            </el-table-column>
            <el-table-column
            prop="transactionAmount"
            label="交易金额（元）"
            width="160">
            </el-table-column>
            <el-table-column
            prop="percentage"
            label="收益比例"
            width="120">
            </el-table-column>
            <el-table-column
            prop="amount"
            label="收入金额（元）"
            width="160">
            </el-table-column>
            <el-table-column
            prop="closeTime"
            label="结算时间">
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div>
          <el-pagination
            v-if="pagingObj.totalSum"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagingObj.currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagingObj.totalSum">
          </el-pagination>
        </div>
    </div>
</template>

<script>
import BaseOrderNumber from '@/components/base/BaseOrderNumber.vue'
import SelectDatePicker from '@/components/select/SelectDatePicker.vue'

export default {
  props: [ 'fourth' ],
  data() {
    return {
      dataValue: '',
      tableData: [],
      IncomeType: [],
      time: [],
      moenyData: [],
      pagingObj: {
        totalSum: '',
        currentPage: 1
      },
      dataForm: {}
    }
  },
  components: {
    BaseOrderNumber,
    SelectDatePicker
  },
  watch: {
    fourthSelectValue(val) {
      this.fourth = val
      this.handleTimeParams(this.time);
      this.dataForm.userId = this.fourth.id
      this.getDataList(this.dataForm);
      this.getIncomeStatistics(this.dataForm)
    }
  },
  computed: {
    fourthSelectValue() {
      return this.fourth
    }
  },
  methods: {
    // 支付方式
    paymentType(types) {
      let str = '';
      switch (types) {
        case 0:
          str = "余额"
          break;
        case 1:
          str = "微信"
          break;
        case 2:
          str = "支付宝"
          break;
        case 3:
          str = "云闪付"
          break;
      }
      return str
    },
    // 获取支付类型下拉框
    async detailData() {
      let typeSelect = {}
      typeSelect.fundType = '1'
      let res = await this.$axios.get('sf/member/fundAccountFlow/typeFundList', { params: typeSelect })
      if (res.status === 0) {
        this.IncomeType = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取时间
    handleTimeParams (obj) {
      if (obj && obj instanceof Array) {
        this.dataForm.startTime = obj[0];
        this.dataForm.endTime = obj[1]
      } else {
        this.dataForm.startTime = '';
        this.dataForm.endTime = ''
      }
    },
    // 分页
    handleSizeChange (val) {
      this.dataForm.pageSize = val
      this.getDataList(this.dataForm)
    },
    handleCurrentChange (obj) {
      this.dataForm.pageNum = obj
      this.getDataList(this.dataForm)
    },
    // 查询
    handleSearch () {
      this.handleTimeParams(this.time);
      this.getDataList(this.dataForm);
      this.getIncomeStatistics(this.dataForm)
    },
    // 会员收入列表
    async getDataList(obj) {
      obj.pageNum = '1';
      obj.pageSize = '10';
      let res = await this.$axios.get('sf/member/fundAccountFlow/fundList', { params: obj })
      if (res.status === 0) {
        console.log(res)
        this.tableData = res.data
        this.pagingObj.totalSum = res.page.total
      } else {
        this.$message.error(res.msg)
      }
    },
    // 会员收入统计
    async getIncomeStatistics(obj) {
      let res = await this.$axios.get('sf/member/fundAccountFlow/totalFund', { params: obj })
      if (res.status === 0) {
        if (res.data === null) {
          this.moenyData = [
            {
              label: '收入总额',
              value: 0
            }
          ]
        } else {
          this.moenyData = [
            {
              label: '收入总额',
              value: `${res.data.totalIncome}`
            }
          ]
        }
      } else {
        this.$message.error(res.msg)
      }
    }
  },
  created() {
    this.detailData()
    this.dataForm.userId = this.fourth.id
    this.dataForm.fundType = '1'
    this.getIncomeStatistics(this.dataForm)
    this.getDataList(this.dataForm);
  }
}
</script>

<style lang="scss" scoped>
.enveloright {
  top: 0px !important;
  right: 0px !important;
  position: relative !important;
}
</style>
