<template>
  <div class="mainbg fixpb">
    <back>返回</back>
    <el-form class="myform clearfix mt" inline @keyup.enter.native="search">
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.taskName"
          class="definput"
          placeholder="请输入任务名称"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <el-input
          clearable
          v-model="pageBean.createByName"
          class="definput"
          placeholder="请输入申请人"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <el-select
          v-model="pageBean.costType"
          placeholder="请选择费用类型"
          class="definput"
          clearable
        >
          <el-option
            v-for="item in costTypeOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-select
          v-model="pageBean.status"
          placeholder="请选择审批状态"
          class="definput"
          clearable
        >
          <el-option
            v-for="item in approvalStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker
          class="definput1"
          v-model="fftime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="">
        <el-date-picker
          v-model="pageBean.queryTime"
          type="datetime"
          placeholder="请选择申请时间"
          class="definput"
          clearable
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="">
        <el-button
          @click="search"
          class="defaultbtn"
          icon="el-icon-search"
          type="primary"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item label="" class="fr">
        <el-button
          class="defaultbtn"
          icon="el-icon-plus"
          type="primary"
          @click="openCostRecordDialog"
        >
          成本申报</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      class="customertable mytable"
      :data="tableData"
      style="width: 100%"
      v-loading="isLoading"
    >
      <el-table-column
        prop="taskName"
        label="任务"
        align="center"
        min-width="167"
      />
      <el-table-column
        prop="costAmount"
        label="成本金额（元)"
        align="center"
        width="167"
      />
      <el-table-column label="成本类型" align="center" width="167">
        <template slot-scope="scope">
          {{ getCostTypeLabel(scope.row.costType) }}
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center" width="167">
        <template slot-scope="scope">
          <span :class="getApprovalStatusClass(scope.row.status)">
            {{ getApprovalStatusLabel(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="costDate"
        label="成本日期"
        align="center"
        width="167"
      />
      <el-table-column
        prop="createByName"
        label="申请人"
        align="center"
        width="167"
      />
      <el-table-column
        prop="taskDepartmentName"
        label="申请人部门"
        align="center"
        width="167"
      />
      <el-table-column
        prop="createTime"
        label="申请时间"
        align="center"
        width="167"
      />
      <el-table-column label="操作" width="250" fixed="right" align="center">
        <template slot-scope="scope">
          <!-- 详情按钮 - 任何状态都可查看 -->
          <el-button
            class="bbtn mr10"
            type="text"
            @click="viewDetail(scope.row)"
          >
            详情
          </el-button>
          <!-- 编辑按钮 - 只有被驳回(4)和已撤销(5)状态可编辑 -->
          <el-button
            v-if="canEdit(scope.row)"
            class="bbtn mr10"
            type="text"
            @click="editRecord(scope.row)"
          >
            编辑
          </el-button>
          <!-- 撤销按钮 - 只有待审核(1)状态可撤销 -->
          <el-button
            v-if="canWithdraw(scope.row)"
            class="rbtn mr10"
            type="text"
            @click="withdrawRecord(scope.row)"
          >
            撤销
          </el-button>
          <!-- 删除按钮 - 任何状态都可删除，但需要权限 -->
          <el-button
            class="rbtn mr10"
            type="text"
            v-isShow="'crm:controller:taskcost:delete'"
            v-if="currentUserId == scope.row.createBy || dataScope == 4"
            @click="deleteRecord(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      <template slot="empty">
        <nolist></nolist>
      </template>
    </el-table>
    <div class="fixpage">
      <page
        :currentPage="pageBean.pageNum"
        :total="total"
        :pageSize="pageBean.pageSize"
        @updatePageNum="handleCurrentChange"
      ></page>
    </div>

    <!-- 成本申报 -->
    <cost-record-dialog
      :visible.sync="costRecordDialogVisible"
      :isEdit="isEditMode"
      :editData="editData"
      @submit="submitCostRecord"
    ></cost-record-dialog>

    <!-- 成本详情 -->
    <review-drawer
      ref="reviewDrawer"
      v-model="isShow"
      :data="reviewData"
      :handleType="2"
    ></review-drawer>
    <msg-dialog ref="msgdialog" />
  </div>
</template>

<script>
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import {
  approvalStatus,
  approvalStatusMap,
  approvalStatusColorMap,
} from '@/utils/status-tool'
import {
  constList,
  constDelete,
  taskcostSave,
  taskcostInfo,
  taskCostUpdate,
} from '@/api/project/index'
import { queryVoListByCode } from '@/api/wapi.js'
import back from '@/components/common/back.vue'
import CostRecordDialog from '../common/costRecordDialog.vue'
import ReviewDrawer from '../../reviewCenter/projectcost/components/reviewDrawer.vue'
export default {
  components: {
    page,
    nolist,
    back,
    CostRecordDialog,
    ReviewDrawer,
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        taskName: '',
        costType: '',
        status: '',
        projectId: '',
        createByName: '',
        startTime: '',
        endTime: '',
      },
      fftime: [],
      approvalStatusOptions: approvalStatus,
      costTypeOptions: [],
      costRecordDialogVisible: false,
      isEditMode: false,
      editData: {},
      isShow: false,
      reviewData: {},
      // 当前用户信息
      currentUserId: sessionStorage.getItem('userid'),
      dataScope: sessionStorage.getItem('dataScope'),
    }
  },

  created() {
    this.pageBean.projectId = this.$route.query.id
    this.loadCostTypes()
    this.costListData()
  },
  methods: {
    loadCostTypes() {
      queryVoListByCode({ code: 'CostType' }).then((res) => {
        if (res.status == 0) {
          this.costTypeOptions = res.data
        }
      })
    },
    search() {
      if (this.fftime && this.fftime.length > 0) {
        this.pageBean.startTime = this.fftime[0]
        this.pageBean.endTime = this.fftime[1]
      } else {
        this.pageBean.startTime = ''
        this.pageBean.endTime = ''
      }
      this.pageBean.pageNum = 1
      this.costListData()
    },
    costListData() {
      this.isLoading = true
      constList(this.pageBean)
        .then((res) => {
          if (res.status == 0) {
            this.tableData = res.data
            this.total = res.page.total
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.costListData()
    },
    getCostTypeLabel(costType) {
      const costTypeItem = this.costTypeOptions.find(
        (item) => item.id === costType
      )
      return costTypeItem ? costTypeItem.name : ''
    },
    getApprovalStatusLabel(status) {
      return approvalStatusMap[status] || ''
    },
    getApprovalStatusClass(status) {
      return `tagcss ${approvalStatusColorMap[status] || ''}`
    },
    openCostRecordDialog() {
      this.isEditMode = false
      this.editData = {}
      this.costRecordDialogVisible = true
    },
    submitCostRecord(formData) {
      const apiCall = this.isEditMode
        ? taskCostUpdate(formData)
        : taskcostSave(formData)
      const successMessage = this.isEditMode ? '更新成功' : '申报成功'

      apiCall.then((res) => {
        if (res.status == 0) {
          this.$refs.msgdialog.show({
            type: 'success',
            title: successMessage,
            msg: '您的申报已成功提交，感谢您的配合！',
          })
          this.costListData()
        } else {
          this.$refs.msgdialog.show({
            type: 'error',
            title: '申报失败',
            msg: '您的申报未成功，请尝试重新提交。',
          })
        }
      })
    },
    viewDetail(row) {
      this.isShow = true
      this.reviewData = { ...row, businessId: row.id }
      this.$nextTick(() => {
        this.$refs.reviewDrawer.loadinfo()
      })
    },
    editRecord(row) {
      taskcostInfo(row.id).then((res) => {
        if (res.status == 0) {
          this.isEditMode = true
          this.editData = res.data
          this.costRecordDialogVisible = true
        } else {
          this.$message.error(res.message)
        }
      })
    },
    withdrawRecord(row) {
      this.$confirm('是否确认撤销该成本记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 调用taskCostUpdate接口，传递status: 5进行撤销
        const withdrawData = {
          id: row.id,
          taskType: row.taskType,
          status: 5,
        }
        taskCostUpdate(withdrawData).then((res) => {
          if (res.status == 0) {
            this.$message.success('撤销成功')
            this.costListData()
          } else {
            this.$message.error(res.msg)
            this.costListData()
          }
        })
      })
    },
    deleteRecord(row) {
      this.$confirm('是否确认删除该成本记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        constDelete({ id: row.id }).then((res) => {
          if (res.status == 0) {
            this.$message.success('删除成功')
            this.costListData()
          } else {
            this.$message.error(res.msg)
          }
        })
      })
    },
    // 权限判断
    canEdit(row) {
      // 只有被驳回(4)和已撤销(5)状态可编辑
      return row.status === 4 || row.status === 5
    },
    canWithdraw(row) {
      // 只有待审核(1)状态可撤销
      return row.status === 1
    },
  },
}
</script>
<style scoped>
.el-icon--right {
  margin-left: 0px;
}
.overduecss {
  min-width: 50px !important;
  height: 24px;
  line-height: 24px;
  background: #fff5f6;
  border-radius: 12px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #f45961;
  text-align: center;
  margin-left: 10px;
}
.w150 {
  width: 150px;
}
.w110 {
  width: 110px;
}
.wid {
  width: calc(100% - 130px);
}
.mr20 {
  margin-right: 20px;
}
.w300 {
  width: 280px;
}
</style>
<style scoped lang="scss">
.mt {
  margin-top: 10px !important;
}
.mainbg {
  background-color: white;
  padding: 20px;
  min-height: 100%;
}
.fr {
  float: right;
}
.smt {
  margin-top: 19px;
}

.protext {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4285f4;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #409eff;
}

.pcc {
  margin: 0 auto;
  text-align: center;
}

.smtext {
  zoom: 0.8;
}

.fxcenter {
  width: 50px;
  height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.zhiding {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  /* background-color: red; */
  display: inline-block;
  margin-top: 16px;
  vertical-align: top;
}

.mt {
  margin-top: 4px;
}

.cusnamecss {
  display: flex;
  color: #4285f4;
  line-height: 24px;
  cursor: pointer;
}

.tagcss {
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  height: 14px;
  min-width: 30px;
  line-height: 11px;
  border-radius: 2px;
  margin-right: 5px;
  text-align: center;
}

.c1 {
  color: #4285f4;
}
.c2 {
  color: #ff8d1a;
}
.c3 {
  color: #56c36e;
}
.c4 {
  color: #f56c6c;
}
.c5 {
  color: #f56c6c;
}

.tagcss:nth-child(2n + 2) {
  margin-top: 4px;
}

.customertable /deep/.cell {
  padding: 8px 10px;
}

.customertable .el-button {
  padding: 2px;
}

.mr10 {
  margin-right: 10px;
}

.bbtn,
.bbtn:hover,
.bbtn:focus {
  color: #4285f4;
}

.rbtn,
.rbtn:hover,
.rbtn:focus {
  color: #f45961;
}

.right {
  text-align: right;
}
</style>
