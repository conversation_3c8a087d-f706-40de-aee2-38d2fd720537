<!-- 订单类型 -->
<template>
  <div>
    <el-select v-model="myValue" clearable placeholder="订单类型" class="input-230" @change="handleChange">
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.name"
        :value="item.name">
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  props: ['value'],
  data () {
    return {
      myValue: '',
      options: []
    }
  },
  watch: {
    value (val) {
      this.myValue = val
    }
  },
  methods: {
    handleChange (val) {
      this.$emit('input', val)
    },
    async init () {
      let res = await this.$axios.get('/sf/member/fundAccountFlow/typeFundList', {params: {fundType: 0}});
      if (res.status === 0) {
        this.options = res.data
      }
    }
  },
  created () {
    this.init()
  }
}
</script>
