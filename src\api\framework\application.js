import request from '@/utils/request'
// import { praseStrEmpty } from "@/utils/ruoyi";

// 查询应用列表
export function applicationList(query) {
  return request({
    url: '/sf/business/application/list',
    method: 'get',
    params: query
  })
}
// 查询已启用应用列表
export function applicationOpenList(query) {
  return request({
    url: '/sf/business/application/enabledList',
    method: 'get',
    params: query
  })
}
// 查询应用详细
export function getApplication(id) {
  return request({
    url: '/sf/business/application/info/' + id,
    method: 'get'
  })
}

// 新增应用
export function addApplication(data) {
  return request({
    url: '/sf/business/application/save',
    method: 'post',
    data,
  })
}

// 修改应用
export function updateApplication(data) {
  return request({
    url: '/sf/business/application/update',
    method: 'post',
    data,
  })
}

// 应用状态修改
export function changeAppStatus(data) {
  return request({
    url: '/sf/business/application/update',
    method: 'post',
    data
  })
}





