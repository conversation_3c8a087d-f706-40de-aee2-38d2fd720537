<!-- 复制 部门管理列表+弹框 class: mod-config -->
<template>
  <div class="mod-config">
    <BaseDialog title="部门管理" :isshow.sync="dialogShow" width="1000px" height="700px">
      <section class="mod-wrap">
      <el-button type="primary" @click="addDetament(0)" style="margin: 10px 0;">添加部门</el-button>
      <el-table
        :data="dataList"
        border
        :empty-text="$emptyFont"
        stripe
        row-key="id"
        v-loading="dataListLoading"
        style="width: 100%;"
        element-loading-text="加载中..." element-loading-spinner="el-icon-loading">
        <el-table-column
        prop="name"
        label="部门名称"
        fixed="left"
        width="300">
        </el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          label="创建时间">
          <template slot-scope="scope"><span>{{ruleTime(scope.row.createTime)}}</span></template>
        </el-table-column>
        <el-table-column
          prop="sortNo"
          header-align="center"
          align="center"
          label="排序号">
        </el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="left"
          width="200"
          label="操作">
          <template slot-scope="scope">
            <el-button type="text" v-isShow="'sf:business:companydepartment:save'" size="mini" @click="addDetament(1, scope.row.id, scope.row)">添加下级</el-button>
            <el-button type="text" size="mini" v-isShow="'sf:business:companydepartment:info'" @click="addDetament(2, scope.row.id, scope.row)">编辑</el-button>
            <el-button type="text" size="mini" v-isShow="'sf:business:companydepartment:delete'" @click="deleteHandle(scope.row.id,scope.row.name, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      </section>
    </BaseDialog>
    <!-- 弹窗, 新增 / 修改 -->
    <el-dialog :title="topTitle" :visible.sync="isShowTab" width="400px" :close-on-click-modal="false">
      <el-form :model="dataForm" :rules="Rule.ADDDEPARTMENT" ref="dataForm" label-width="100px">
        <el-form-item label="所属部门" v-if="diffState!==0">
          <el-input v-model="dataForm.parentName" placeholder="所属部门" disabled class="input-230"></el-input>
        </el-form-item>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="dataForm.name" placeholder="部门名称" class="input-230"></el-input>
        </el-form-item>
        <el-form-item label="排序号" prop="sortNo">
          <el-input-number type="number" v-model="dataForm.sortNo" placeholder="排序号" class="input-230"></el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer" v-isShow="'sf:business:companydepartment:update'">
        <el-button @click="isShowTab = false">取消</el-button>
        <el-button type="primary"  @click="dataFormSubmit('dataForm')">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import pagination from '@/mixin/pagination'
import BaseDialog from '@/components/base/BaseDialog.vue'
export default {
  mixins: [pagination],
  data () {
    return {
      dataList: [],
      pageNum: 1,
      total: 0,
      dataListLoading: false,
      addOrUpdateVisible: false,
      getStrId: '',
      search: {},
      dialogShow: false,
      isShowTab: false,
      topTitle: '',
      diffState: '',
      dataForm: {
        parentName: '',
        name: '',
        sortNo: ''
      }
    }
  },
  components: {
    BaseDialog
  },
  methods: {
    // 表单提交
    dataFormSubmit (dataForm) {
      this.$refs[dataForm].validate(async (valid) => {
        if (valid) {
          let params = {
            'id': this.dataForm.id,
            'parentId': this.dataForm.parentId,
            'companyId': this.dataForm.companyId,
            'level': this.dataForm.level,
            'name': this.dataForm.name, // 部门名称
            'sortNo': this.dataForm.sortNo // 排序位置
          }
          let url = this.diffState === 0 || this.diffState === 1 ? '/sf/business/companydepartment/save' : '/sf/business/companydepartment/update'
          let res = await this.$axios.post(`${url}`, params)
          if (res.status === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.isShowTab = false
                this.search.pageNum = '1';
                this.getDepatmentData(this.search)
              }
            })
          } else {
            this.$message.error(res.msg)
          }
        }
      })
    },
    init (val) {
      this.dialogShow = true
      this.search.pageNum = '1';
      this.search.pageSize = '10';
      this.search.companyId = val;
      this.dataForm.companyId = val;
      this.getDepatmentData(this.search)
    },
    clearFormData () {
      this.dataForm.name = ''
    },
    // 添加部门
    addDetament (val, id, obj) {
      this.clearFormData()
      this.isShowTab = true
      this.diffState = val
      if (val === 0) {
        this.topTitle = '添加部门'
        this.dataForm.parentName = ''
        this.dataForm.sortNo = 0
        this.dataForm.parentId = this.dataForm.companyId
        this.dataForm.level = 1
      } else if (val === 1) {
        this.topTitle = '添加下级部门'
        this.dataForm.parentName = obj.name
        this.dataForm.sortNo = 0
        this.dataForm.parentId = obj.id
        this.dataForm.level = Number(obj.level) + 1
      } else {
        this.dataForm.parentName = obj.name
        this.dataForm.sortNo = obj.sortNo
        this.dataForm.name = obj.name
        this.dataForm.level = obj.level
        this.dataForm.parentId = obj.parentId
        this.dataForm.id = obj.id
        this.topTitle = '修改部门'
      }
    },
    // 获取部门下所有子部门id
    handleGetObjId (obj) {
      this.getStrId = `${this.getStrId},${obj.id}`
      if (obj.children && obj.children.length > 0) {
        obj.children.map(item => {
          this.handleGetObjId(item, this.getStrId)
        })
      }
    },
    // 删除
    deleteHandle (id, name, obj) {
      this.getStrId = ''
      this.handleGetObjId(obj)
      let ids = this.getStrId.substr(1);
      let data = {};
      data.ids = ids;
      this.$confirm(`确定对"${name}"进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await this.$axios.post('/sf/business/companydepartment/delete', data);
        if (res.status === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDepatmentData(this.search)
            }
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 根据公司查部门
    async getDepatmentData (obj) {
      this.dataListLoading = true
      let res = await this.$axios.get('/sf/business/companydepartment/findByCompanyId', {params: obj})
      if (res.status === 0) {
        this.dataListLoading = false
        this.dataList = res.data
      }
    }
  }
}
</script>

<style>
.mod-config .el-table .cell{
  text-align: left !important;
}
</style>

<style lang="scss" scoped>
.mod-wrap{
  padding: 10px;
  height: 700px;
  overflow-y: scroll;
}
</style>
