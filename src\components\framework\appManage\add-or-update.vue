<template>
  <div>
    <!-- <BaseDialog :title="subTitle" :isshow.sync="visible" width="800px" height="auto"> -->
    <el-dialog
      :title="subTitle"
      :visible.sync="visible"
      width="900px"
      @close="close"
    >
      <!--  -->
      <el-form
        :model="ruleForm"
        ref="ADD_APPRef"
        :rules="Rule.ADD_APPversion"
        label-width="140px"
        class="baseDialogClass"
      >
        <el-form-item label="应用：" prop="appCode">
          <el-select
            v-model="ruleForm.appCode"
            clearable
            placeholder="请选择应用"
            :disabled="isDisabled"
            class="input-300"
          >
            <el-option
              v-for="item in Dict.APPLICATION"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div class="clearfix">
          <el-form-item label="版本名称：" prop="versionFirst" class="pull-left">
            <el-input-number
              :disabled="isDisabled"
              class="inputNumberClass"
              v-model="ruleForm.versionFirst"
              controls-position="right"
              :min="1"
            ></el-input-number>
          </el-form-item>
          <el-form-item prop="versionSecond" class="pull-left lineitem">
            <el-input-number
              :disabled="isDisabled"
              class="inputNumberClass"
              v-model="ruleForm.versionSecond"
              controls-position="right"
              :min="0"
            ></el-input-number>
          </el-form-item>
          <el-form-item prop="versionThird" class="pull-left lineitem">
            <el-input-number
              :disabled="isDisabled"
              class="inputNumberClass"
              v-model="ruleForm.versionThird"
              controls-position="right"
              :min="0"
            ></el-input-number>

            <span>V{{ruleForm.versionFirst}}.{{ruleForm.versionSecond}}.{{ruleForm.versionThird}}</span>
          </el-form-item>
        </div>
        <el-form-item label="最小更新版本号：" prop="miniCode">
          <el-input-number
            :disabled="isDisabled"
            v-model="ruleForm.miniCode"
            controls-position="right"
            :min="0"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="版本号：" prop="versionCode">
          <el-input-number
            :disabled="isDisabled"
            v-model="ruleForm.versionCode"
            controls-position="right"
            :min="0"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="升级包地址：" prop="upgradeUrl">
          <el-input
            :disabled="isDisabled"
            v-model="ruleForm.upgradeUrl"
            placeholder="请输入升级包地址"
            class="input-300"
          ></el-input>
        </el-form-item>
        <el-form-item label="升级描述：" prop="description">
          <quill-editor ref="myTextEditor" class="editor-example" v-model="ruleForm.description" :options="editorOption"></quill-editor>
          <!-- <span class="editor-tips">
            描述填写示例：<br/>
            1、修复问题；<br>
            2、优化应用；
          </span> -->
        </el-form-item>
        <!-- <el-form-item label="强制升级：">
          <el-switch
            @change="changeForceUpgrade"
            v-model="ruleForm.forceUpgrade"
            active-color="#13ce66"
            active-value="1"
            inactive-value="0"
          ></el-switch>
          <span> {{forceUpgradeText}}</span>
        </el-form-item> -->
        <el-form-item label="状态：" prop>
          <el-switch
            @change="changeEnabled"
            v-model="ruleForm.isEnabled"
            active-color="#13ce66"
            active-value="1"
            inactive-value="0"
          ></el-switch>
          <span> {{enabledText}}</span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()" :loading="submitloading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/base/BaseDialog.vue";

import { quillEditor } from 'vue-quill-editor';
// import 'quill/dist/quill.core.css';
// import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';
export default {
  data() {
    return {
      enabledText: '禁用',
      forceUpgradeText: '否',
      submitloading: false,
      isDisabled: false,
      subTitle: "",
      visible: false,
      editorOption: {
        theme: 'bubble',
        placeholder: `升级描述填写示例：
1、修复问题；
2、优化应用；`,
        modules: {
          toolbar: [
            // ['bold', 'italic', 'underline', 'strike'],
            // [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            // [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            // ['link', 'image'],
            // [{ 'color': [] }, { 'background': [] }],
            // [{ 'font': [] }],
            [{ 'align': [] }],
            ['clean']
          ]
        }
      },
      ruleForm: {
        versionFirst: 1,
        versionSecond: 0,
        versionThird: 0,
        miniCode: 0,
        versionCode: 0,
        forceUpgrade: 0,
        isEnabled: 0
      },
      
      appId: ""
    };
  },
  methods: {
    close() {
      console.log('close方法')
      this.$refs['ADD_APPRef'].clearValidate()
      this.temp = this.$options.data().temp;
      this.visible = false;
    },
    init(obj) {
      this.appId = '';
      this.visible = true;
      this.ruleForm = {
        versionFirst: 1,
        versionSecond: 0,
        versionThird: 0,
        miniCode: 0,
        versionCode: 0,
        forceUpgrade: 0,
        isEnabled: 0
      }
      if (obj) {
        // 编辑
        this.isDisabled = true;
        this.subTitle = "修改版本";
        this.appId = obj.id;
        this.ruleForm = Object.assign({}, obj);
        this.ruleForm.forceUpgrade = obj.forceUpgrade + ''
        this.ruleForm.isEnabled = obj.isEnabled + ''

        if (this.ruleForm.isEnabled === '0') {
          this.enabledText = '禁用'
        } else {
          this.enabledText = '启用'
        }
        if (this.ruleForm.forceUpgrade === '0') {
          this.forceUpgradeText = '否'
        } else {
          this.forceUpgradeText = '是'
        }
      } else {
        // 添加
        this.isDisabled = false;
        this.subTitle = "添加版本";
      }
    },
    changeEnabled (v) {
      if (v === '0') {
        this.enabledText = '禁用'
      } else {
        this.enabledText = '启用'
      }
    },
    changeForceUpgrade (v) {
      if (v === '0') {
        this.forceUpgradeText = '否'
      } else {
        this.forceUpgradeText = '是'
      }
    },
    dataFormSubmit () {
      this.$refs.ADD_APPRef.validate((valid) => {
        if (valid) {
          this._postappversion(this.ruleForm)
          console.log(this.ruleForm.versionFirst)
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    async _postappversion (data) {
      let url = '';
      if (!this.appId) {
        url = '/sf/business/appversion/save'
      } else {
        data.id = this.appId;
        url = '/sf/business/appversion/update'
        delete data.createTime
        delete data.modifyTime
        delete data.isDeleted
        delete data.modifyBy
        delete data.modifyByName
      }
      this.submitloading = true
      let res = await this.$axios.post(`${url}`, data)
      if (res.status === 0) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.submitloading = false
            this.visible = false
            this.$emit('refreshDataList')
          }
        })
      } else {
        this.submitloading = false
        this.$message.error(res.msg)
      }
    }
  },
  components: {
    BaseDialog,
    quillEditor
  }
};
</script>

<style lang="scss" scoped>
.baseDialogClass {
  padding: 20px;
}
.dialog-footer {
  display: block;
  text-align: right;
}
.inputNumberClass {
  width: 98px;
}
.pull-left {
  float: left;
  margin-right: 3px;
}
.lineitem /deep/ .el-form-item__content {
  margin-left: 0 !important;
}

.editor-example {
  width: 300px;
  height: 8rem;
  display: inline-block;
}
.quill-editor {
  border-bottom: none;
  /deep/ .ql-editor {
    border-radius: 4px;
    border: 1px solid #DCDFE6;
  }
}
.editor-tips {
  display: inline-block;
  vertical-align: top;
  line-height: 1.5;
}
</style>
