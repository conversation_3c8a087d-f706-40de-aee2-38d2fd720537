<template>
  <div>
    <div>
      <back>返回</back>
    </div>
    <div class="mainbg fixpb" v-loading="isLoading">
      <div class="page-header">
        <el-form
          class="myform clearfix"
          ref="form"
          :model="pageBean"
          :inline="true"
        >
          <el-form-item label="用书专业：">
            <el-input
              v-model="pageBean.specialtyName"
              class="definput"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="用书时间：" label-width="110px">
            <el-date-picker
              :editable="false"
              :clearable="true"
              class="yearcss definput"
              value-format="yyyy"
              v-model="useBookYear"
              type="year"
              placeholder="请选择用书时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" v-if="useBookYear">
            <el-select
              class="jijie1"
              v-model="pageBean.useSeason"
              placeholder="请选择"
              clearable
            >
              <el-option label="春季" value="春季"></el-option>
              <el-option label="秋季" value="秋季"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="合作属性: ">
            <el-select
              class="jijie"
              v-model="pageBean.isCooperation"
              placeholder="请选择"
              clearable
            >
              <el-option label="合作" value="2"></el-option>
              <el-option label="非合作" value="1"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="">
            <el-button
              class="defaultbtn"
              type="primary"
              icon="el-icon-search"
              @click="onSearch"
              >查询</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <el-table
        ref="unitTable"
        row-key="id"
        class="unittable mytable"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="materialName" label="教材名称" align="center">
        </el-table-column>
        <el-table-column prop="useBookYear" label="用书时间" align="center">
        </el-table-column>
        <el-table-column prop="provinceName" label="合作属性" align="center">
          <template slot-scope="scope">
            {{ scope.row.isCooperation == 2 ? '合作' : '非合作' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalBookNumber" label="用量" align="center">
        </el-table-column>
        <el-table-column prop="specialtyName" label="用书专业" align="center">
        </el-table-column>
        <el-table-column prop="planRecruitNumber" label="计划招生(人)">
        </el-table-column>
        <el-table-column prop="realityRecruitNumber" label="实际招生(人)">
        </el-table-column>
        <template slot="empty">
          <nolist></nolist>
        </template>
      </el-table>
      <div class="fixpage">
        <page
          :currentPage="pageBean.pageNum"
          :total="total"
          :pageSize="pageBean.pageSize"
          @updatePageNum="handleCurrentChange"
        ></page>
      </div>
    </div>
  </div>
</template>

<script>
import back from '../../common/back.vue'
import page from '../../common/page.vue'
import nolist from '../../common/nolist.vue'
import { getParStr } from '@/utils/tools'
import { queryUnitBookStatisticsDetail } from '@/api/kanban/index'
export default {
  components: {
    page,
    nolist,
    back,
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      total: 0,
      pageBean: {
        pageNum: 1,
        pageSize: 10,
        useBookYear: '',
        materialId: '',
        unitId: '',
        isCooperation: '',
        useSeason: '',
        specialtyName: '',
      },
      useBookYear: '',
    }
  },
  created() {
    if (Object.keys(this.$route.query).length > 0) {
      this.pageBean = Object.assign(this.pageBean, this.$route.query)
      this.pageBean.pageNum = Number(this.pageBean.pageNum)
      this.pageBean.pageSize = Number(this.pageBean.pageSize)
      if (this.$route.query.unitId) {
        this.pageBean.unitId = this.$route.query.unitId
      }
      if (this.$route.query.materialId) {
        this.pageBean.materialId = this.$route.query.materialId
      }
    }
    this.loadData()
  },
  watch: {
    useBookYear(newVal, oldVal) {
      if (!newVal) {
        this.pageBean.useSeason = ''
      }
    },
  },
  methods: {
    valueFormatter(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue
      }
      return '—'
    },
    loadData() {
      history.replaceState(
        null,
        null,
        `#${this.$route.path}?${getParStr(this.pageBean)}`
      )
      this.isLoading = true
      queryUnitBookStatisticsDetail(this.pageBean)
        .then((result) => {
          this.tableData = result.data
          this.total = result.page.total
          this.isLoading = false
        })
        .catch((err) => {
          this.isLoading = false
        })
    },
    onSearch() {
      if (this.pageBean.useSeason) {
        this.pageBean.useBookYear =
          this.useBookYear + '年' + this.pageBean.useSeason
      } else {
        if (this.useBookYear) {
          this.pageBean.useBookYear = this.useBookYear + '年'
        } else {
          this.pageBean.useBookYear = ''
        }
      }
      this.pageBean.pageNum = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pageBean.pageNum = page
      this.loadData()
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/.el-input__inner {
  height: 34px;
  line-height: 34px;
}
.jijie1 {
  width: 132px;
}

.page-header {
  display: flex;
  justify-content: space-between;
}

.unittable .el-button + .el-button {
  margin-left: 20px;
}

.search {
  width: 96px;
}

.mainbg {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  min-height: 100%;
  margin-top: 20px;
}
.myform .el-form-item:last-child {
  margin-right: 20px !important;
}
</style>
