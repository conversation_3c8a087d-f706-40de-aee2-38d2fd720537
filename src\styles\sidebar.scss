#app {

  .main-container {
    height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth  !important;
    background-color: $menuBg;
    height: calc(100% - 56px);
    position: fixed;
    font-size: 0px;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
      background: #fff;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100%);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 5px;
      vertical-align: middle;
      width: 1.7em;
      height: 1.7em;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background-color: $menuHover  !important;

        span {
          color: #4285F4;
        }
        .svg-icon {
          fill: #4285F4;
          color: #4285F4;
        }

      }
    }

    .el-menu-item.is-active {
      background-color: #DFEAFD !important;
      color: #4285F4;

      span {
        color: #4285F4 !important;
      }
    }

    .is-active>.el-submenu__title {
      color: $subMenuActiveText  !important;
    }

    .el-submenu__title span {
      font-size: 16px;
    }

    .submenu-title-noDropdown span {
      font-size: 16px;
    }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      span {
        font-family: Microsoft YaHei-Regular,
          Microsoft YaHei;
        font-weight: 400;
        color: #333333;
      }

      text-align: center;
      min-width: $sideBarWidth  !important;
      // background-color: $subMenuBg  !important;

      background: #fff;


      &:hover {
        background-color: $subMenuHover  !important;
        color: #4285F4;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth  !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth  !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      background: #fff;
    }
  }



  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}